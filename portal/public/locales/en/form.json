{"account_form_subscription_not_existed": "Subscription {{subscriptions}} of {{form}} is not existed in {{account}}", "action": {"extracted": "Data extracted", "extracting": "Extracting data...", "processing": "Data Submitted, Transaction Processing...", "purple_trac_completed": "PurpleTRAC integration completed.", "purple_trac_processing": "Processing PurpleTRAC integration...", "saved": "All Changes Saved", "scoring_system_processing": "Processing Scoring System...", "submitting": " Submitting.."}, "add_fields": "Add fields", "add_form": "Add form", "add_node": "Add node", "add_related_forms": "Add related forms", "add_widgets": "Add widgets", "add_zone": "Add zone", "added": "Added", "allow_upload": "Allow upload", "auto_populate_settings": {"add_context": "Add context", "add_extra_configuration": "Add extra configuration", "add_fixed_value": "Add fixed value", "current_register_field": "{{registerName}} fields", "data_source": "Data Source", "data_source_context": "Context", "data_source_field": "Data source field", "data_source_fixed_value": "Fixed value", "data_source_required": "Please select data source", "data_source_type": "Data Source Type", "data_source_type_configuration": "Data Source Type Configuration", "data_source_type_options": {"ACCOUNT": "ACCOUNT", "LLI": "LLI", "MDS": "MDS Catalogue", "collection": "Collection", "data_register": "Data register", "external_data": "External data", "ocimf": "OCIMF", "ofac": "OFAC", "paris_mou": "Paris Mou", "pdf": "PDF", "purple_trac": "PurpleTRAC", "q88": "Q88", "related_form": "Related Form", "us_coast_guard": "US Coast Guard"}, "data_source_type_required": "Please select data source type", "entity_field": {"berth": "MDS Berth Id", "company": "MDS Company Id", "country": "MDS Country Id", "port": "MDS Port Id", "terminal": "MDS Terminal Id", "vessel": "MDS Vessel Id"}, "extra_configuration": "Extra Configuration", "field": "Field", "field_configuration": "Field Configuration", "include_validation_value": "Include validation value", "itself": "Itself", "lookup_field_options": "{{lookupName}} Fields", "mapping": "Mapping", "none": "None", "only_one_account_data_source": "Only one Account datasource is allowed", "related_form_extra_config_message": "Only get 1st transaction in above related form", "target_field": "Target Field", "target_field_required": "Please select a target field"}, "border": {"color": "Border color", "width": {"label": "Border width", "none": "None", "thick": "<PERSON><PERSON><PERSON>", "thin": "Thin"}}, "break_line": "Break line", "can_not_create_new_version_for_entity_workspace": "Cannot create new version more than one for this entity workspace. Multiple releases for the same workspace type are not allowed.", "can_not_release_for_entity_workspace": "Only one workspace can be released for each workspace type. Multiple releases for the same workspace type are not allowed.", "change_active_transaction_version": {"action": "Action", "change_active_version": "Change Active Version", "change_active_version_failed": "Change Active to {{version}} Version failed", "change_active_version_success": "Change Active to {{version}} Version success", "confirm_active_version": "Do you want to change active to version {{version}} ?"}, "change_content": "Change Content", "change_log": {"add": "Add", "change": "Changes", "change_by": "Change by", "change_date": "Change date", "collection": "Collection", "collection_added": "Collection: {{added}}", "collection_deleted": "Collection: {{deleted}}", "collection_item_added": "Collection Value: {{added}}", "collection_item_deleted": "Collection Value: {{deleted}}", "collection_item_updated": "Collection Value: {{updated}}", "collection_updated": "Collection: {{updated}}", "content": {"change": "Change", "configuration.calculationFormula.dataType": "Data Type", "configuration.calculationFormula.expression": "Expression", "configuration.currency": "<PERSON><PERSON><PERSON><PERSON>", "configuration.decimalPlace": "Decimal place", "configuration.decimalSeparator": "Decimal separator", "configuration.defaultValue": "Default value", "configuration.filterCondition": "Filter condition", "configuration.format": "Format", "configuration.leadingZeros": "Leading zeros", "configuration.mode": "Mode", "configuration.numberType": "Number type", "configuration.pickerType": "Picker type", "configuration.placeholder": "Placeholder", "configuration.readOnly": "<PERSON><PERSON><PERSON>", "configuration.rollup.dataType": "Data Type", "configuration.rollup.operator": "Operator", "configuration.ruleConfigs.greaterThanDate.pickerType": "Greater than/Type", "configuration.ruleConfigs.greaterThanDate.value": "Greater than/Value", "configuration.ruleConfigs.lessThanDate.pickerType": "Less than/Type", "configuration.ruleConfigs.lessThanDate.value": "Less than/Value", "configuration.ruleConfigs.maxLength": "Max length", "configuration.ruleConfigs.minLength": "Min length", "configuration.ruleConfigs.required": "Required", "configuration.style.backgroundColor": "Background color", "configuration.style.color": "Color", "configuration.thousandSeparator": "Thousand separator", "configuration.uom": "UOM", "configuration.visibility": "Visibility", "configuration.visibilityConditions": "Visibility condition", "from": "From", "label": "Label", "to": "To"}, "delete": "Delete", "field_added": "Field: {{added}}", "field_deleted": "Field: {{deleted}}", "field_updated": "Field: {{updated}}", "kanban_views": {"column": {"added": "View: {{viewName}} - Column: {{added}}", "deleted": "View: {{viewName}} - Column: {{deleted}}", "updated": "View: {{viewName}} - Column: {{updated}}"}, "layout": {"added": "View: {{viewName}} - Card Layout: Column added", "deleted": "View: {{viewName}} - Card Layout: Column deleted", "updated": "View: {{viewName}} - Card Layout: {{updated}}"}, "swimlane": {"added": "View: {{viewName}} - Swimlane: {{added}}", "deleted": "View: {{viewName}} - Swimlane: {{deleted}}", "updated": "View: {{viewName}} - Swimlane: {{updated}}"}}, "layout": "Layout", "layout_added": "Layout: {{added}}", "layout_deleted": "Layout: {{deleted}}", "layout_updated": "Layout: {{updated}}", "layout_zone": "Layout zone", "layout_zone_added": "Layout: {{layoutName}} - Layout Zone: {{added}}", "layout_zone_deleted": "Layout: {{layoutName}} - Layout Zone: {{deleted}}", "layout_zone_field": "Layout Field ", "layout_zone_field_added": "Layout: {{layoutName}} - Layout Zone: {{layoutZoneName}} - Layout Zone Field: {{added}}", "layout_zone_field_deleted": "Layout: {{layoutName}} -  Layout Zone: {{layoutZoneName}} - Layout Zone Field: {{deleted}}", "layout_zone_field_updated": " Layout: {{layoutName}} - Layout Zone: {{layoutZoneName}} - Layout Zone Field: {{updated}}", "layout_zone_updated": "Layout: {{layoutName}} - Layout Zone: {{updated}}", "relation_added": "Relation Form: {{added}}", "relation_deleted": "Relation Form: {{deleted}}", "relation_updated": "Relation Form: {{updated}}", "role": "Role Access Control", "role_stage_added": "Access Control: Role {{roleAdded}} - Stage {{stageAdded}}", "role_stage_deleted": "Access Control: Role {{roleDeleted}} - Stage {{stageDeleted}}", "role_stage_updated": "Access Control: Role {{roleUpdated}} - Stage {{stageUpdated}}}", "setting": "Setting", "setting_added": "Setting: {{added}}", "setting_deleted": "Setting: {{deleted}}", "setting_updated": "Setting: {{updated}}", "stage_acl_collection": "Stage Access Control: Stage {{stageName}} - Collection {{collectionName}} - Collection Item {{collectionItemName}} ", "stage_acl_field": "Stage Access Control: Stage {{stageName}} - Field {{fieldName}} ", "stage_acl_form": "Stage Access Control: Stage {{stageName}} - Form {{formName}} ", "stage_added": "Stage: {{added}}", "stage_decision_added": "Stage Decision: {{added}}", "stage_decision_deleted": "Stage Decision: {{deleted}}", "stage_decision_updated": "Stage Decision: {{deleted}}", "stage_deleted": "Stage: {{deleted}}", "stage_role_acl_collection": "ACL: Role {{roleName}} - Stage {{stageName}} - Layout {{layoutName}} - Collection {{collectionName}} - Collection Item {{collectionItemName}}", "stage_role_acl_decision": "ACL: Role {{roleName}} - Stage {{stageName}} - Layout {{layoutName}} - Decision {{decisionName}}", "stage_role_acl_field": "ACL: Role {{roleName}} - Stage {{stageName}} - Layout {{layoutName}} - Field {{fieldName}}", "stage_role_acl_relation": "ACL: Role {{roleName}} - Stage {{stageName}} - Layout {{layoutName}} - Form {{formName}}", "stage_transition_added": "Stage Transitions: {{sourceAdded}} {{targetAdded}}", "stage_transition_deleted": "Stage Transitions: {{sourceDeleted}} {{targetDeleted}}", "stage_transition_updated": "Stage Transitions: {{sourceUpdated}} {{targetUpdated}}", "stage_updated": "Stage: {{updated}}", "type": "Type", "update": "Update", "views": "Views", "views_added": "View: {{added}}", "views_deleted": "View: {{deleted}}", "views_updated": "View: {{updated}}", "views_updated_column": "View: {{updated}} - Column constraint: {{columnName}}", "views_updated_swimlane": "View: {{updated}} - Swimlane constraint: {{swimlaneName}}"}, "change_role": "Change role", "collection": {"action_name": "Action name", "answer_label": "Answer value", "auto_populate": "Auto Populate", "automation": "Automation", "automation_action_mapping": "Automation action mapping", "automation_mapping": "Automation mappings", "button": {"add_activity": "Add collection values", "add_collection": "Add collection", "add_criteria": "Add criteria values", "add_field": "Add field", "add_fields": "Add fields", "add_group": "Add group", "add_rollup_field": "Add rollup field", "add_summary": "Add summary", "bulk_auto_populate_setting": "Auto Populate Settings", "bulk_delete": "Delete Items", "delete": "Delete", "preview": "Preview"}, "can_add_value": "Can add collection values", "change_name": "Change name", "collection_filters": "Collection filters", "collection_groups": "Collection groups", "comparison_label": "Comparison value", "config": {"actions": "Actions", "additional_fields": "Additional fields", "auto_populate_setting": "Auto populate settings", "calculation": "Calculation", "configuration": "Configuration", "context_mapping": "Context mapping", "mapping_fields": "Mapping fields", "validation": "Validation"}, "context_mapping": "Context mapping", "display_settings": "Display settings", "features": {"alias": "<PERSON><PERSON>", "can_add_multiples": "Can add multiples", "done_extra_config": "Has extra config", "has_attachment": "Has attachment", "has_comment": "Has comment", "help_text": "Help text", "mapping_fields": "Mapping fields", "name": "Name"}, "field": {"group_name": "Group name", "group_name_placeholder": "Input group name"}, "form_action": "Form Action", "hide_default_collection_values_when_empty": "Hide default collection values when empty", "message": {"bulk_delete": "Delete selected items?", "bulk_delete_description": "Are you sure to delete all selected items?", "delete_collection": "Delete collection?", "delete_collection_description": "Are you sure to delete this collection?", "delete_collection_group": "Delete group?", "delete_collection_group_description": "Are you sure to delete this group?", "delete_confirm": "Are you sure to delete this field?", "delete_mapping": "Delete mapping?", "delete_mapping_description": "Are you sure to delete this mapping?", "delete_title": "Delete field"}, "settings": "Settings", "summary": "Summary", "summary_settings": "Summary settings", "tab": {"default": "<PERSON><PERSON><PERSON>", "optional": "Optional"}, "title": "Collection", "validation_result": "Validation result", "validation_statement": "Validation Statement", "zone_name": "Zone Name"}, "column": {"change_logs": "Change Logs", "comment": "Comment", "context": "Context(s)", "created": "Created", "created_at": "Created At", "created_by": "Created By", "default": "<PERSON><PERSON><PERSON>", "description": "Description", "information": "Information", "last_edited": "Last Edited", "latest_version": "Latest Version", "name": "Name", "no_trans": "Number of transactions", "no_use": "Number of use", "published": "Published", "published_at": "Published At", "published_by": "Published By", "stage": "Stage", "status": "Status", "type": "Type", "updated": "Updated", "updated_by": "Updated By", "version": "Version", "stage_kpi": "Stage KPI"}, "comment": {"comment_required": "Please input comment", "create_new_version_failed": "Create new version failed", "create_new_version_success": "Create new version successfully", "form_version_comment": "Form Version Comment", "title": "Comment"}, "configurations": "Configurations", "confirm_publish_form": "Confirm Publish", "confirm_publish_form_msg": "Please confirm to to publish:", "create_form_builder": "Create Form Builder", "cvf": {"title": "Conditional Value Field"}, "data_register": "Register", "decision": {"run_failed": "Run decision failed", "triggered": "Decision triggered"}, "decision_types": {"action": "Action", "decision_type_label": "Decision type", "transition": "Transition"}, "decisions": "Decisions", "delete_confirm": "Are you sure to delete this field?", "delete_field": "Delete field", "delete_form": "Delete form", "delete_form_confirm": "Are you sure to delete this form?", "delete_related_form": "Delete related form", "delete_related_form_desc": "Are you sure to delete this related form?", "delete_title": "Delete field", "discard_changes": "Discard changes", "display_drop_zone": {"always_show_drop_zone": "Always show drop zone", "hide_drop_zone": "Hide drop zone", "show_drop_zone_until_upload": "Show drop zone until upload", "title": "Display drop zone"}, "display_options": "Display options", "document_field": "Document field", "document_field_required": "Please select document field", "edit_form": "Edit form", "errors": {"additional_field_not_found": "Additional field not found", "auto_populate_setting_not_found": "Auto populate setting not found", "automation_mapping_not_found": "Automation mapping not found", "automation_version_not_found": "Automation version not found", "datasource_not_found": "Datasource not found", "invalid_open_form_transaction_configuration": "Invalid open form transaction configuration", "invalid_route": "Invalid Route", "no_active_layout": "No active layout", "no_permission": "You have no permission on this form", "open_record_instead_of_workspace": "No workspace found. Open record instead of workspace", "transaction_field_not_found": "Transaction field not found"}, "external": {"LLI__COMPANY": "LLI - Company", "LLI__LLI_CASUALTY": "LLI - Casualty", "LLI__LLI_PSC": "LLI - PSC", "LLI__LLI_VESSEL": "LLI - <PERSON><PERSON><PERSON>", "LLI__VESSEL": "LLI - <PERSON><PERSON><PERSON>", "MDS__COMPANY": "MDS - Company", "MDS__VESSEL": "MDS - <PERSON><PERSON><PERSON>", "OCIMF__OCIMF_OVID_CREW": "OCIMF - Ovid <PERSON> ", "OCIMF__OCIMF_OVID_OVMSA": "OCIMF - <PERSON>vid <PERSON>", "OCIMF__OCIMF_OVID_PSC": "OCIMF - Ovid <PERSON>", "OCIMF__OCIMF_OVID_VIQ": "OCIMF - <PERSON><PERSON>", "OCIMF__OCIMF_OVID_VPQ": "OCIMF - <PERSON><PERSON>", "OCIMF__OCIMF_SIRE2_VIQ": "OCIMF - Sire 2 VIQ", "OCIMF__OCIMF_SIRE_CREW": "OCIMF - Sire Crew", "OCIMF__OCIMF_SIRE_INCIDENT": "OCIMF - Sire Incident", "OCIMF__OCIMF_SIRE_PSC": "OCIMF - Sire PSC", "OCIMF__OCIMF_SIRE_TMSA": "OCIMF - Sire TMSA", "OCIMF__OCIMF_SIRE_VIQ": "OCIMF - Sire <PERSON>", "OCIMF__OCIMF_SIRE_VPQ": "OCIMF - Sire <PERSON>", "OFAC__OFAC_COMPANY_SANCTION": "OFAC - Company Sanction", "OFAC__OFAC_VESSEL_SANCTION": "OFAC - Vessel Sanction", "PARIS_MOU__PARIS_MOU_PSC": "PARIS MOU - Paris Mou PSC", "PDF__PDF_OVID_VIQ": "PDF - OVID VIQ", "PDF__PDF_Q88": "PDF - Q88", "PDF__PDF_SIRE2_VIQ": "PDF - Sire 2 VIQ", "PDF__PDF_SIRE_CREW": "PDF - <PERSON><PERSON>", "Q88__Q88_HVPQ": "Q88 - VPQ", "USCG__USCG_PSC": "US Coast Guard - PSC"}, "field_name": "Field name", "field_not_found": "Field not found", "field_types": {"answer": "Answer", "calculation": "Calculation", "checkbox": "Checkbox", "comparison": "Comparison", "datePicker": "Date Picker", "datetimePicker": "Datetime Picker", "document": "Document", "duration": "Duration", "lookup": "Lookup", "number": "Number", "roleLookup": "Role Lookup", "rollup": "Rollup", "select": "Select", "separator": "Separator", "sire2Answer": "Sire2 Answer", "text": "Text", "textArea": "Text Area", "timePicker": "Time Picker", "uiGroup": "Ui Group", "userLookup": "User Lookup"}, "fields_files": "Field/File(s)", "file": {"limit_size": "The file size must be smaller than {{fileSize}}!", "not_allow_file_type": "The file type is not allowed", "not_available": "The file is not available"}, "form": "Form", "form_builder_steps": "Workflow Steps", "form_field": {"description": "Description", "description_placeholder": "Input Description", "name": "Name", "name_message": "Please input name", "name_placeholder": "Input Name", "subscription": "Subscription", "subscription_message": "Please select subscription", "subscription_placeholder": "Select subscription"}, "form_has_no_version": "{{form}} has no active version", "form_name": "Form name", "form_publish": {"account_roles": "{{accountName}} roles", "form_roles": "Form roles"}, "form_updated": "Form updated", "form_was_published_to_account": "{{form}} was published to {{account}}", "headers": {"versions": "Versions"}, "layout": {"copy_layout_from": "Copy layout setting from", "create_new_layout": "Create new layout", "create_new_layout_failed": "Create new layout failed", "delete_confirm": "Are you sure to delete this layout?", "delete_failed": "Delete layout failed", "delete_layout": "Delete layout", "delete_tab_item": "Delete tab item", "delete_tab_item_confirm": "Are you sure to delete this tab item?", "layout_type": "Layout type", "layout_type_placeholder": "Layout type", "name": "Name", "name_required": "Please enter layout name", "select_doc_field": "Please select Document field", "select_field": "Select field", "show_zone_name_as_legend": "Show zone name as legend", "type_required": "Please select type", "types": {"detail": "Detail layout", "summary": "Summary layout"}, "update_layout": "Update layout", "update_layout_failed": "Update layout failed"}, "lookups": "Lookups", "manual_events": {"action": "Action", "confirm": "Do you really want to trigger this action?", "event": "Event"}, "mds_id": "MDS ID", "message": {"bulk_auto_populate_setting_successfully": "Auto Populate Settings have been updated", "delete_confirm": "Would you like to delete {{record}}?", "delete_title": "Delete form builder", "delete_transaction": "Delete transaction", "formula_invalid": "The formula is invalid", "get_data_register_failed": "Can not get data registers", "increase_version_failed": "Increase version failed", "publish_form_failed": "Publish form failed", "publish_form_successfully": "Publish form successfully", "publish_related_data_register_required": "Please publish all related data registers to account", "publish_related_form_and_data_register_required": "Please publish all related forms & data registers to account", "publish_related_form_required": "Please publish all related forms to account"}, "migrate_transaction": {"action": "Action", "confirm_migrate": "Are you sure to migrate transactions?", "migrate_to_version": "Migrate to version {{version}}", "migrate_transaction": "Migrate Transaction", "migrate_transaction_warning_1": "We are migrating transactions to a new version. Please note that some data from the old version may be lost during this process.", "migrate_transaction_warning_2": "We recommend you review your records and save any important information before proceeding.", "select_version": "Select version", "total_transaction": "Total transaction", "transaction_migrate_failed": "Transaction migrate failed", "transaction_migrated": "Transaction migrated", "version": "Version"}, "missing_criteria_config_warning": "Missing configuration for criteria fields: {{name}}", "next_stages": "Next stages", "notifications": {"no_form_value": "Form value is not set", "purchase_ocimf_viq_fail": "Purchase VIQ report fail", "purchase_ocimf_viq_success": "Purchase VIQ report success", "save_selection_failed": "Save selection failed", "save_selection_success": "Save selection success", "transaction_created": "Created transaction successfully", "transaction_update_fail": "Fail to update transaction", "transaction_update_processing": "Transaction update processing", "transaction_updated": "Transaction update submitted", "trigger_automation": "Trigger automation successfully"}, "operator": {"equal": "Equal", "greater": "Greater", "greater_or_equal": "Greater or equal", "is_empty": "Is empty", "is_fields_required": "Is required field/file(s)", "is_not_empty": "Is not empty", "is_transaction_required": "Is required transaction", "less": "Less", "less_or_equal": "Less or equal", "multiselect_equals": "Multiselect equals", "multiselect_not_equals": "Multiselect not equals", "not_equal": "Not equal", "required_upload_files": "Required upload file(s)", "select_any_in": "Select any in", "select_not_any_in": "Select not any in", "title": "Operator"}, "override": {"comment": "Comment", "createdAt": "Created At", "from_value": "From value", "override_by": "Override by", "override_history": "Override history", "to_value": "To value", "type": "Type", "types": {"system": "System", "user": "User"}}, "populate_related_mapping": {"add_mapping": "Add mapping", "field": "Fields", "field_is_required": "Context field is required", "form": "Form", "on_change": {"label": "On change", "pull": "<PERSON><PERSON>", "push": "<PERSON><PERSON>"}, "on_creation": "On creation", "remove_stage_confirm": "Do you really want to remove this stage settings?", "select_field": "Select field", "select_form": "Select form", "select_stage": "Select stage"}, "publish_form": "Publish {{formName}}", "related_data_registers": "Related data registers", "related_forms": "Related forms", "remove_widget_confirm": "Are you sure to delete this widget?", "report_id": "REPORT ID", "role_acl": {"add_role": "Add role", "add_role_settings": "Add role settings", "add_stage": "Add stage", "add_stage_settings": "Add stage settings", "available_decisions": "Available decisions", "choose_layout": "Choose layout", "copy_from": "Copy from", "decision_actions": "Decision actions", "delete_role": "Delete role?", "delete_stage": "Delete stage?", "duplicate_stage_role": "Stage already exists", "layout": "Layout", "layout_required": "Layout is required", "manual_actions": "Manual Automations", "role_name": "Role name", "role_name_exists": "Role exists", "role_name_required": "Please enter role name", "stage": "Stage", "stage_name": "Stage name", "stage_required": "Stage is required", "stage_transition": "Stage transitions", "title": "Role Access Control"}, "role_mapping": "Roles Mapping", "rollup": {"context": {"collection": "Form Collection", "collection_field": "Collection Field", "collection_group": "Collection Group", "collection_value": "Collection Value", "form": "Form", "related_form": "Related Form", "related_form_collection": "Related Form Collection"}, "context_label": "Context", "context_placeholder": "Select context", "field_label": "Field", "field_placeholder": "Select field", "operator": {"average": "Average", "count": "Count", "earliest": "Earliest", "latest": "Latest", "max": "Max", "min": "Min", "sum": "Sum"}, "operator_label": "Operator", "operator_placeholder": "Select operator", "type_label": "Type", "type_placeholder": "Select type"}, "save_warning_msg": "You have unsaved changes.", "select_account": "Select account", "select_field": "Select field to determine number of relations", "select_fields": "Select fields", "select_role": "Select role", "select_role_description": "Please select the role you want to access this form", "select_wf": "Select workflow stage when relation should be created", "selected_forms": "Selected forms", "selected_widgets": "Selected widgets", "setting": "Setting", "settings": {"additional_fields": "Additional fields", "background_color": "Background color", "color": "Color", "color_conditions": "Color conditions", "condition": "Condition", "data_validation_conditions": "Data validation conditions", "disable_create_button": "Disable Create button", "field_background_color": "Field background color", "icon": "Icon", "icon_label": "Icon label", "override": "Override", "required_upload_file": "Required upload file(s)", "show_display_text_in_drop_zone": "Show display text in drop zone", "text_color": "Text color", "title": "Settings"}, "stage": {"change_failed": "Change stage failed", "triggered_change": "Stage changed"}, "stage_acl": {"add_condition": "Add condition", "confirm_delete_condition": "Are you sure you want to delete this condition?", "data_register_condition_section": "Data register condition section", "download": "Download", "editable": "Editable", "fields": "Form Fields", "form_field_condition_section": "Form field condition section", "hidden": "Hidden", "locked": "Locked", "optional_if_conditions_are_true": "If not checked, the field is required if any condition below is NOT met", "read_only": "Read Only", "relations": "Related Forms", "required": "Required", "required_conditions": "Required conditions", "required_field_condition": "Conditions to make this field required", "required_fields": "Required fields", "required_if_conditions_are_true": "Make field required if all condition are met", "required_relation_condition": "Conditions to make this relation required", "stage_name": "Stage name", "target_stages": "Apply requirement in these workflow stages", "target_stages_duplicate": "Target stages must be unique", "target_stages_required": "Target stages is required", "tip_1": "Set the rules that determine when this field must be filled in", "tip_2": "When 'Make field required if all condition are met' is ON, the field is required if every condition below is true.", "tip_3": "When OFF, the field is required if any condition below is false", "title": "Stage Access Control", "visible": "Visible", "widget_name": "Widget name", "widgets": "Form Widgets"}, "stage_role_acl": {"can_create": "Can Create", "can_delete": "Can Delete", "title": "Stage Role Access Control"}, "status": {"draft": "Draft", "editing": "Editing", "published": "Published", "ready_to_published": "Published", "released": "Released", "unpublished": "Unpublished"}, "stay_here": "Stay here", "step": {"access_control": "Access Controls", "automation": "Automation", "collections": "Collections", "configuration": "Configuration", "fields": "Fields", "information": "Information", "layouts": "Layouts", "relations": "Relations", "summary": "Summary", "test": "Test/Sandbox simulator", "version": "Version", "view": "View", "wfs": "Workflow Stages", "widgets": "Widgets", "api_builder": "API Builder"}, "target_open_options": "Target open options", "target_related_form_actions": {"drawer": "Drawer", "popup": "Popup", "tab": "Tab"}, "toolbar_actions": "Toolbar actions", "transaction": "Transaction", "transaction_id_fields": "Select form field", "transaction_id_format": "Transaction ID format", "transaction_id_prefix": "Prefix", "transaction_leading_zeros": "Leading zeros", "transaction_msg": {"required_upload_files": "Please upload file to form(s): {{names}}", "start_stage_not_found": "Form's start stage is not exist", "user_has_no_permission_on_start_stage": "You do not have permission on form's start stage", "user_has_not_roles": "You has no roles to create transaction"}, "transition_condition_error_message": "In order to transition to the next stage, you need to satisfy the following conditions:", "unavailable_field": "Unavailable field", "update_failed": "Update failed", "upload": {"not_upload_related_form": "Transaction is created but does not allow to upload file(s): {{formName}}", "reset_upload_files": "Reset uploaded files"}, "validations": {"constraint_type_duplicate": "Constraint type must be unique", "delete_collection_used": "This collection is being used by layout. Please delete the collection from layout first.", "invalid_form_acl": "Invalid stage role access control, please check again. (ex: select layout, ...)", "invalid_mapping_context": "Existing context has not been mapped in collections: {{names}}", "missing_stage_role": "You forgot to add roles to form, please back to Access control step", "required": "This field is required", "role_required": "Role is required", "save_selection_failed": "Save selection failed", "save_selection_success": "Save selection success", "unique_decision_name": "Decision name must be unique", "unique_decision_target": "Decision target must be unique", "unique_transition_decision": "Transition decision must be unique", "validation_errors": "Validation errors", "version_required": "Please select version"}, "view_all_relations": "View all relations", "view_change": "View change", "views": {"add_column": "Add column", "add_lane": "Add lane", "add_swimlane": "Add swimlane", "calendar": "Calendar", "card_layout": "Card layout", "clear": "Clear", "columns": "Columns", "constraint": "Constraint", "count_trans_one": "{{count}} transaction", "count_trans_other": "{{count}} transactions", "create_column": "Create column", "create_new_view": "Create new view", "create_new_view_failed": "Create new view failed", "default": "<PERSON><PERSON><PERSON>", "delete_confirm": "Are you sure to delete this view?", "delete_failed": "Delete view failed", "delete_view": "Delete view", "error": {"form_version_stage_not_exist": "This stage is not exist transaction form version", "stage_role_not_in_form_version": "Stage role is not in form version", "stage_transition_not_allowed": "Stage transition is not allowed", "user_not_in_role": "User does not have required role to perform this movement"}, "false": "False", "field": {"filterable_label": "<PERSON><PERSON>", "lock_label": "Freeze Column", "resizable_label": "Resize Col<PERSON>n", "searchable_label": "<PERSON><PERSON>", "sortable_label": "Enable sort", "width_fixed_error": "Please enter valid number 1-999", "width_label": "<PERSON><PERSON><PERSON>", "width_percent_error": "Please enter valid number 1-100", "width_placeholder": "Enter width of field using unit px or %"}, "fields": "Fields", "filter": "Filters", "fit_all_transactions": "Fit all transactions", "fit_column_on_screen": "Fit column on screen", "from": "From", "kanban": "Ka<PERSON><PERSON>", "kanban_input_search_title": "Search", "less_than_message": "This column will be highlighted when the number of transactions is less than minimum", "list": "List", "mapping_column": "Mapping column", "mapping_swimlane": "Mapping swimlane", "max": "Max", "menu_entry_point": "Menu entry point", "message": {"delete_confirm": "Are you sure to delete this column?", "delete_title": "Delete column"}, "min": "Min", "name": "Name", "name_required": "Please enter view name", "other_position": "Unassigned/Other position", "other_position_place": {"bottom": "Bottom", "top": "Top"}, "over_limit_message": "This column will be highlighted when the number of transactions exceeds this limit", "schedule": "Schedule", "select_field": "Select field", "select_field_required": "Please select fields", "select_swimlanes": "Select swimlane", "select_view": "Select view", "stages": "Stages", "swimlane_type": {"dynamic": "Dynamic", "manual": "Manual"}, "swimlanes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to": "To", "true": "True", "type": "Type", "type_required": "Please select view type", "unmapped_value": "UnMapped values", "update_column": "Update column", "update_lane": "Update lane", "update_swimlane": "Update swimlane", "update_view": "Update view", "update_view_failed": "Update view failed"}, "visibility_criteria": "Visibility Criteria", "visibility_criteria_options": {"hide": "<PERSON>de", "show": "Show"}, "warning": "Warning", "wfs": {"action": "Action", "actions": "Actions", "add": "Add", "background_color": "Background color", "can_transition_back": "Can transition back", "decision": "Decision", "decision_executed_failed": "Decision execution failed", "decision_executed_success": "Decision executed successfully", "decisions": "Decisions", "delete": "Delete", "delete_transaction": "Delete transaction", "delete_transaction_confirm": "Are you sure to delete this transaction?", "no_permission": "No permission", "previous_stage": "Previous stage", "target_stage": "Target stage", "text_color": "Text color", "transition_to": "Transition to", "validation_statement": "Validation statement", "visible_in_toolbar": "Visible in toolbar", "visible_stage": "Show Stage in Toolbar When Active", "wfs_visualizer": "Workflow Stages Visualizer"}, "widget": {"all_used": "All widgets are being used", "collection": "Collection", "collectionField": "Collection Field", "field": "Field", "fileViewer": "File Viewer", "relation": "Relation", "summary": "Summary", "tab": "Tab", "toolbar": "<PERSON><PERSON><PERSON>", "widget": "Widget", "instruction": "Instruction"}, "widget_field": {"context": "Context", "context_value_field": "Context value", "data_sources": "Data sources", "field_name": "Field name", "font_size": "Font size", "font_weight": "Font weight", "highlight_color": "Highlight color", "information": "Information", "open_form_by": "Open form by", "select_widget": "Select widget", "separator": "Separator", "show_field_label": "Show field label", "show_field_value": "Show field value", "text_color": "Text color", "type": "Type"}, "width_option_placeholders": {"fixed": "input the pixel ie: 200", "percent": "input the percent ie: 20"}, "width_options": {"auto": "Auto", "fixed": "Fixed (px)", "percent": "Percent (%)"}, "form_lock": {"acquire_form_lock_failed": "Failed to acquire form lock", "acquire_form_lock_success": "Form lock acquired successfully", "release_form_lock_failed": "Failed to release form lock", "release_form_lock_success": "Form lock released successfully", "take_over_editing_modal_description": "This form is not locked. Do you want to take over editing permission on this form?", "release_edit_control": "Release Edit Control", "take_over_edit_control": "Take Over Edit Control"}}