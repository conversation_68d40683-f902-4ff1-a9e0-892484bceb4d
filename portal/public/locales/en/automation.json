{"automation_has_no_version": "{{name}} has no active version", "automation_publish": {"account_roles": "{{accountName}} roles"}, "automation_type": {"data_register": "Data Register", "external": "External", "form_transaction": "Form", "ocimf": "OCIMF"}, "automation_updated": "Automation updated successfully", "automation_updated_failed": "Automation updated failed", "automation_was_published_to_account": "{{name}} was published to {{account}}", "border": {"color": "Border color", "width": {"label": "Border width", "none": "None", "thick": "<PERSON><PERSON><PERSON>", "thin": "Thin"}}, "builder": {"action": {"add_action": "Add action", "add_event": "Add event node", "add_rule": "Add rule", "delete_rule": "Delete rule", "function_type": {"ACTION.CALCULATE_SYSTEM_SCORE": "Calculate system score", "ACTION.change_transaction_workflow": "Change workflow stage", "ACTION.CHANGE_TRANSACTION_WORKFLOW_STAGE": "Change workflow stage", "ACTION.CREATE_REGISTER": "Create register record", "ACTION.create_register_record": "Create register record", "ACTION.CREATE_RELATED_TRANSACTION": "Create related transaction", "ACTION.CREATE_TRANSACTION": "Create transaction", "ACTION.create_transaction": "Create transaction", "ACTION.CREATE_TRANSACTION_BY_DATASOURCE": "Create transaction by datasource", "ACTION.CREATE_TRANSACTION_BY_EXTERNAL": "Create or update transaction", "ACTION.CREATE_UPDATE_REGISTER": "Create/Update register record", "ACTION.DATA_PIPELINE_POPULATE_Q88": "Populate Q88", "ACTION.EMAIL": "Send email", "ACTION.email": "Send email", "ACTION.PDF": "Export PDF", "ACTION.POPULATE_PURPLETRAC": "Populate from PurpleTRAC", "ACTION.ROLLUP_FORM_TRANSACTIONS": "Rollup form transactions", "ACTION.ROLLUP_REGISTER_RECORDS": "Rollup register records", "ACTION.RUN_LLM": "Run LLM", "ACTION.UPDATE_COLLECTION_FIELD": "Update collection field", "ACTION.UPDATE_REGISTER_FIELD": "Update register field", "ACTION.update_register_field": "Update register field", "ACTION.UPDATE_TRANSACTION_FIELD": "Update transaction field", "ACTION.update_transaction_field": "Update transaction field", "print_pdf": "Export PDF", "send_email": "Send email", "ACTION.WEBHOOK": "Send webhook"}, "title": "Action", "type": "Action type"}, "event": {"COLLECTION_TRANSACTION.UPDATED": "Collection transaction updated", "DATA_REGISTER.CREATED": "Register created", "DATA_REGISTER.DELETED": "Register deleted", "DATA_REGISTER.PUBLISHED": "Register published", "DATA_REGISTER.RECORD_CREATED": "Register record added", "DATA_REGISTER.RECORD_DELETED": "Register record deleted", "DATA_REGISTER.RECORD_FIELD_UPDATED": "Register record field changed", "DATA_REGISTER.RECORD_STATUS_UPDATED": "Status changed", "DATA_REGISTER.RECORD_UPDATED": "Register record updated", "DATA_REGISTER.RELEASED": "Register released", "DATA_REGISTER.SCHEDULED": "Scheduled", "EXTERNAL_NEW_DOCUMENT.CREATED": "New external document created", "FORM_TRANSACTION.CREATED": "Transaction created", "FORM_TRANSACTION.DELETED": "Transaction deleted", "FORM_TRANSACTION.FIELD_UPDATED": "Transaction field changed", "FORM_TRANSACTION.MANUAL": "Manual", "FORM_TRANSACTION.SCHEDULED": "Scheduled", "FORM_TRANSACTION.STAGE_UPDATED": "Transaction stage changed", "FORM_TRANSACTION.STATUS_UPDATED": "Status changed", "FORM_TRANSACTION.UPDATED": "Transaction updated", "FORM_TRANSACTION.STAGE_KPI_STATUS_CHANGED": "Stage KPI Event", "scheduler": "Scheduled event", "scheduler_note": "The system operates using the UTC time zone for scheduling configuration.", "type": "Type"}, "event_type": {"field_changed": "Field Changed", "register_created": "Register created", "register_deleted": "Register deleted", "register_published": "Register published", "register_record_added": "Register record added", "register_record_deleted": "Register record deleted", "register_record_field_changed": "Register record field changed", "register_record_updated": "Register record updated", "register_released": "Register released", "scheduled": "Register scheduled", "stage_changed": "Stage changed", "status_changed": "Register status changed", "transaction_created": "Transaction created", "transaction_updated": "Transaction updated"}, "header": {"action": "New action", "event": "Event configuration", "root_event": "When: {{0}}", "rule": "New rule"}, "rule": {"add_condition": "Add Condition", "collection_fields": "Collection fields", "collection_fields_required": "Please select at least one collection field", "collections": "Collections", "collections_required": "Please select at least one collection", "conditions": "Conditions", "enable": "Enable", "from_stage": "From stage", "name": "name", "ordering": "By ordering", "parallel": "<PERSON><PERSON><PERSON>", "run_mode": "Run action mode", "stage": "Current stage", "to_stage": "To stage", "wfs_kpi_status": "Work Flow Stage KPI Status", "trigger_fields": "Trigger fields", "trigger_work_flow_stage": "Trigger Work Flow Stage"}, "rule_action": {"add_receiver": "Receivers", "by_role": "By role", "by_user": "By user", "collection": "Collection", "data_register": "Data Register", "document_field": "Select document field", "email_field": "Select email field", "external_user_role": "External User Role", "imo_required": "IMO is required", "name": "name", "select_email_template": "Select email template", "select_imo_field": "Select IMO field", "select_role": "Select role", "select_user": "Select user", "select_vessel_imo": "Select vessel IMO", "template": "Template", "template_is_required": "Email template is required"}}, "button": {"delete": "Delete", "delete_all": "Delete Automation", "pause": "Pause", "pause_all": "Pause all versions", "resume": "Resume", "resume_all": "Resume all versions", "view_log": "View log"}, "change_log": {"add": "Add", "change": "Changes", "change_by": "Change by", "change_date": "Change date", "configuration": "Configuration", "configuration_action_added": "Configuration Action: {{added}}", "configuration_action_deleted": "Configuration Action: {{deleted}}", "configuration_action_updated": "Configuration Action: {{updated}}", "configuration_added": "Configuration: {{added}}", "configuration_deleted": "Configuration: {{deleted}}", "configuration_event_added": "Configuration Event: {{added}}", "configuration_event_deleted": "Configuration Event: {{deleted}}", "configuration_event_updated": "Configuration Event: {{updated}}", "configuration_rule_added": "Configuration Rule: {{added}}", "configuration_rule_deleted": "Configuration Rule: {{deleted}}", "configuration_rule_updated": "Configuration Rule: {{updated}}", "configuration_updated": "Configuration: {{updated}}", "content": {"change": "Change", "configuration.calculationFormula.dataType": "Data Type", "configuration.calculationFormula.expression": "Expression", "configuration.currency": "<PERSON><PERSON><PERSON><PERSON>", "configuration.decimalPlace": "Decimal place", "configuration.decimalSeparator": "Decimal separator", "configuration.defaultValue": "Default value", "configuration.filterCondition": "Filter condition", "configuration.format": "Format", "configuration.leadingZeros": "Leading zeros", "configuration.mode": "Mode", "configuration.numberType": "Number type", "configuration.pickerType": "Picker type", "configuration.placeholder": "Placeholder", "configuration.readOnly": "<PERSON><PERSON><PERSON>", "configuration.rollup.dataType": "Data Type", "configuration.rollup.operator": "Operator", "configuration.ruleConfigs.greaterThanDate.pickerType": "Greater than/Type", "configuration.ruleConfigs.greaterThanDate.value": "Greater than/Value", "configuration.ruleConfigs.lessThanDate.pickerType": "Less than/Type", "configuration.ruleConfigs.lessThanDate.value": "Less than/Value", "configuration.ruleConfigs.maxLength": "Max length", "configuration.ruleConfigs.minLength": "Min length", "configuration.ruleConfigs.required": "Required", "configuration.style.backgroundColor": "Background color", "configuration.style.color": "Color", "configuration.thousandSeparator": "Thousand separator", "configuration.uom": "UOM", "configuration.visibility": "Visibility", "configuration.visibilityConditions": "Visibility condition", "from": "From", "label": "Label", "to": "To"}, "delete": "Delete", "field_added": "Field: {{added}}", "field_deleted": "Field: {{deleted}}", "field_updated": "Field: {{updated}}", "layout": "Layout", "layout_added": "Layout: {{added}}", "layout_deleted": "Layout: {{deleted}}", "layout_updated": "Layout: {{updated}}", "layout_zone": "Layout zone", "layout_zone_added": "Layout: {{layoutName}} - Layout Zone: {{added}}", "layout_zone_deleted": "Layout: {{layoutName}} - Layout Zone: {{deleted}}", "layout_zone_field": "Layout Field ", "layout_zone_field_added": "Layout: {{layoutName}} - Layout Zone: {{layoutZoneName}} - Layout Zone Field: {{added}}", "layout_zone_field_deleted": "Layout: {{layoutName}} -  Layout Zone: {{layoutZoneName}} - Layout Zone Field: {{deleted}}", "layout_zone_field_updated": " Layout: {{layoutName}} - Layout Zone: {{layoutZoneName}} - Layout Zone Field: {{updated}}", "layout_zone_updated": "Layout: {{layoutName}} - Layout Zone: {{updated}}", "relation_added": "Relation Form: {{added}}", "relation_deleted": "Relation Form: {{deleted}}", "relation_updated": "Relation Form: {{updated}}", "role_acl_added": "Access Control: Role {{roleAdded}} - Layout {{layoutAdded}} - Widget {{widgetAdded}}", "role_acl_deleted": "Access Control: Role {{roleDeleted}} - Layout {{layoutDeleted}} - Widget {{widgetDeleted}}", "role_acl_updated": "Access Control: Role {{roleUpdated}} - Layout {{layoutUpdated}} - Widget {{widgetUpdated}}", "type": "Type", "update": "Update", "widget": "Configuration", "widget_added": "Widget: {{added}}", "widget_deleted": "Widget: {{deleted}}", "widget_updated": "Widget: {{updated}}"}, "column": {"actions": "Actions", "active_version": "Active version", "automation_action_name": "Action Name", "automation_action_type": "Action Type", "automation_execution": "Passed Rules / Total / Actions", "automation_name": "Automation Name", "automation_version": "Version", "change_logs": "Change Logs", "comment": "Comment", "context": "Context", "context_name": "Context Name", "context_placeholder": "Select context", "context_transaction": "Record", "context_version": "Context Version", "created": "Created", "created_at": "Created At", "created_by": "Created By", "description": "Description", "description_placeholder": "Input description", "event_type": "Event Type", "icon": "Icon", "information": "Information", "last_edited": "Last Edited", "latest_version": "Latest Version", "name": "Name", "name_placeholder": "Input name", "no_use": "Number of use", "published": "Published", "published_at": "Published At", "published_by": "Published By", "select_dr": "Select Data Register", "select_dr_placeholder": "Please select data register", "select_form": "Select Form", "select_form_placeholder": "Please select form", "state": "State", "status": "Status", "subscription": "Subscription", "subscription_placeholder": "Select subscription", "triggered_at": "Triggered At", "triggered_by": "Triggered By", "type": "Type", "updated": "Updated", "updated_at": "Updated At", "updated_by": "Updated By", "version": "Version"}, "comment": {"auto_version_comment": "Automation version comment"}, "confirm_publish": "Confirm Publish", "confirmation": {"delete_desc": "Would you like to delete selected automation?", "delete_title": "Delete Automation", "pause_desc": "Would you like to pause selected automation version?", "pause_title": "Pause Automation Version", "resume_desc": "Would you like to resume selected automation version?", "resume_title": "Resume Automation Version"}, "create_action": {"action_create_register": {"add_mapping_fields": "Add Mapping Fields", "datasourceField": {"label": "Source Field", "placeholder": "Select Datasoure Field"}, "default_value": {"label": "Default Value", "placeholder": "Input default Field"}, "delete": {"confirm": "Are you sure to delete this mapping field ?", "title": "Confirm Delete Mapping Field"}, "registerId": {"label": "Register", "placeholder": "Select Register"}, "targetField": {"label": "Target Field", "placeholder": "Select Target Field"}}, "action_create_transaction": {"add_mapping_fields": "Add Mapping Fields", "datasource": {"datasource_require_msg": "Please select the datasource", "document": "Document type", "document_placeholder": "Select Document Type", "document_require_msg": "Please select the document type", "label": "Datasource", "placeholder": "Select Datasource"}, "datasourceField": {"label": "Source Field", "placeholder": "Select Datasoure Field"}, "default_value": {"label": "Default Value", "placeholder": "Input default Field"}, "delete": {"confirm": "Are you sure to delete this mapping field ?", "title": "Confirm Delete Mapping Field"}, "formId": {"label": "Form", "placeholder": "Select Form", "require_msg": "Please select the form"}, "select_field": {"label": "Select field to determine number of relations", "require_msg": "Please select the field"}, "targetField": {"label": "Target Field", "placeholder": "Select Target Field"}}, "action_name": {"label": "Action Name", "placeholder": "Enter Action Name", "require_msg": "Please enter action name to create action"}, "action_send_email": {"cc": {"label": "CC Emails", "placeholder": "Enter CC Emails", "require_msg": "Please enter cc emails"}, "documentId": {"label": "Document", "placeholder": "Select Document", "require_msg": "Please select document"}, "to": {"label": "To Email", "placeholder": "Enter To Email", "require_msg": "Please enter target email"}}, "action_send_notification": {"documentId": {"label": "Document", "placeholder": "Select Document", "require_msg": "Please select document"}}, "action_type": {"label": "Action Type", "placeholder": "Select Action Type", "require_msg": "Please select action type to create action"}, "action_wfs_change": {"from_label": "From Stage", "from_placeholder": "Select From Work Flow Stage", "require_msg": "Please select destination stage", "to_label": "To Stage", "to_placeholder": "Select To Work Flow Stage"}, "condition": "Condition", "delete_popup": "Are you sure to delete this action ?", "drawer_header": "Create Action", "drawer_header_updated": "Update Action", "parameters_mapping": "Parameters mapping", "q88": {"vessel_context": "<PERSON><PERSON><PERSON>"}, "update_collection_field_on_updating_transaction": "Update collection fields on updating transaction", "update_field_on_updating_transaction": "Update fields on updating transaction", "update_field_values": "Update Field Values", "update_form_fields": {"field_default_value": "Field default Value", "field_name": "Field Name", "field_name_required": "Field Name is required", "field_value": "Field Value", "field_value_required": "Field Value is required", "include_validation_value": "Include validation value", "select_value_source": "Select value source"}, "update_register_fields": {"field_default_value": "Field default Value", "field_name": "Field Name", "field_name_required": "Field Name is required", "field_value": "Field Value", "field_value_required": "Field Value is required"}, "webhook_action": {"method": "Method", "url": "URL", "headers": "Headers", "header_key": "Header Key", "header_value": "Header Value", "body_example_title": "Body example", "body_template_title": "Body template", "body_placeholder": "Enter body", "body_example": "{\"aggregateId\":\"b358b3da-e0a5-4712-9a6a-b4594ecdf145\",\"metadata\":{\"correlationId\":\"8EQ_RxKXAl8uytxobz223\",\"causationId\":\"72d610b7-ba63-47ba-808f-e530305e6d2a\",\"timestamp\":1749450479924,\"userId\":\"02f6461b-a184-4cee-846e-4de612566a39\",\"tenantId\":\"7fde9180-e43f-458d-bc6d-9eaeb4f92253\",\"version\":1,\"source\":\"system\",\"type\":\"FORM_TRANSACTION.UPDATED\",\"name\":\"FORM_TRANSACTION.UPDATED\"},\"id\":\"1b88e5a6-82c8-44af-adfc-f7051f0dbd19\"}", "body_template": "{\"aggregateId\":\"Form Transaction ID\",\"metadata\":{\"correlationId\":\"ID for trace only\",\"causationId\":\"ID for trace only\",\"timestamp\":\"Timestamp\",\"userId\":\"User ID\",\"tenantId\":\"Tenant ID\",\"version\":\"Version\",\"source\":\"Source\",\"type\":\"Type\",\"name\":\"Name\"},\"id\":\"Event ID\"}", "add_header": "Add header", "bulk_edit": "Bulk Edit", "bulk_edit_headers": "Bulk Edit Headers", "bulk_edit_description": "Enter headers in key:value format, with each header on a new line.", "show_example": "Show Example", "show_template": "Show Template"}}, "create_automation": "Create Automation", "data_register": "Data Register", "data_source": "Data Source", "delete_confirm": "Are you sure to delete this field?", "delete_field": "Delete field", "errors": {"not_found": "Automation not found"}, "form": "Form", "form_field": {"description": "Description", "description_placeholder": "Input Description", "header": "Header", "header_message": "Please select field", "header_placeholder": "Select field", "name": "Name", "name_message": "Please input name", "name_placeholder": "Input Name", "role": "Role", "select_data_register": "Select data register", "select_record": "Select record", "subscription": "Subscription", "subscription_message": "Please select subscription", "subscription_placeholder": "Select subscription"}, "headers": {"versions": "Versions"}, "message": {"add_my_workspaces_failed": "Add my workspaces failed", "add_my_workspaces_successfully": "Add my workspaces successfully", "choose_your_widgets": "Choose your widgets before adding a role.", "delete_confirm": "Are you sure to delete this workspace?", "delete_title": "Delete workspace", "empty_widget": "No widget found. Please add widget first.", "increase_version_failed": "Increase version failed", "publish_related_widgets_required": "Please publish all widgets to account", "publish_workspace_failed": "Publish workspace failed", "publish_workspace_successfully": "Publish workspace successfully", "select_context": "Select context value field"}, "notifications": {"automation_updated": "Automation updated successfully", "workspace_updated": "Automation updated successfully", "workspace_updated_failed": "Automation updated failed"}, "publish_workspace": "Publish {{name}}", "related_widgets": "Related widgets", "select_role_description": "Please select the role you want to access this workspace", "select_workspaces": "Select workspaces", "setting": "Setting", "settings": {"background_color": "Background color", "color": "Color", "color_conditions": "Color conditions", "condition": "Condition", "field_background_color": "Field background color", "icon": "Icon", "icon_label": "Icon label", "title": "Settings"}, "state": {"paused": "Paused", "resumed": "Resumed"}, "status": {"draft": "Draft", "editing": "Editing", "published": "Published", "released": "Released"}, "step": {"configuration": "Configuration", "information": "Information", "test": "Test/Sandbox simulator", "version": "Version"}, "unavailable_field": "Unavailable field", "validation": {"context_required": "Please select automation context", "context_value_required": "Please select context value", "icon_required": "Please select automation icon", "name_required": "Please enter automation name", "required": "This field is required", "subscription_required": "Please select subscription", "url_required": "Please enter a valid URL"}, "versions": "Versions"}