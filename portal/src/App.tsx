import { Authenticated, AuthProvider, CanAccess, Refine, useGetIdentity } from '@refinedev/core';

import { ErrorComponent } from '@refinedev/antd';

import { useAuth0 } from '@auth0/auth0-react';
import routerBindings, { CatchAllNavigate, NavigateToResource, UnsavedChangesNotifier } from '@refinedev/react-router-v6';
import { App as AntdApp, ConfigProvider, notification } from 'antd';
import { BrowserRouter, Navigate, Outlet, Route, Routes } from 'react-router-dom';

import { Loading } from '@/components/loading';
import { INITIAL_ROUTE } from '@/constants/layout';

import { ANONYMOUS_TOKEN_KEY, CLAIM_KEY, IMPERSONATE_TOKEN_KEY, TOKEN_KEY } from '@/constants/storage-key';

import '@refinedev/antd/dist/reset.css';
import { DataProviders } from '@refinedev/core/dist/contexts/data/types';
import { jwtDecode } from 'jwt-decode';

import { accessControlProvider } from '@/accessControlProvider';
import { DataProvider, DefaultResource, FeatureEnum } from '@/common/enums';
import { DynamicRouteComponent } from '@/components/dynamic-subscription';
import { MDSAuthenticatedLayout } from '@/components/layout';
import { PublicLayout } from '@/components/layout/public';
import { dataProvider } from '@/dataProvider';
import { NUser } from '@/interfaces';

import { useResourceStore } from '@/stores';
import colors from '@/styles/theme/colors';
import { getEnvConfig } from '@/utils';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import { useAppNotificationProvider } from './common/hooks';
import {
    AccountDetailPage,
    AccountPage,
    AccountSubscriptionDetailPage,
    AccountSubscriptionsPage,
    AggregationBuilderDetailsPage,
    AggregationBuilderPage,
    AnonymousTransactionDetailPage,
    ApiBuilderDetailPage,
    ApiBuilderListPage,
    AutoMationBuilderDetailPage,
    AutoMationBuilderListPage,
    AutoMationLogListPage,
    CollectionBuilderDetailPage,
    CredentialManagerPage,
    DatalakeDownloadPage,
    DataPropertyBuilderCreatePage,
    DataPropertyBuilderEditPage,
    DataPropertyBuilderListPage,
    DataRegisterBuilderView,
    DataRegisterDetailPage,
    DataRegisterListPage,
    DataRegisterTransactionList,
    DocumentBuilderDetailPage,
    DocumentBuilderListPage,
    EditDataRegisterTransactionPage,
    FormBuilderDetailPage,
    FormBuilderListPage,
    Home,
    Login,
    MyWorkspacesPage,
    RoleDetailPage,
    RoleListPage,
    RoleUserWorkspaceDetailPage,
    SearchDetailsPage,
    SubscriptionDetailPage,
    SubscriptionPage,
    UserEditPage,
    UsersPage,
    UserWorkspaceDetailPage,
    UserWorkspacesListPage,
    WidgetDetailsPage,
    WidgetManagerListPage,
    WorkspaceDetailPage,
    WorkspaceListPage,
} from './component';

import { Page403 } from './pages/error/403';
import { FormDetailPage } from './pages/form-transaction/FormDetailPage';
import { FormTransactionListPage } from './pages/form-transaction/FormTransactionListPage';
import { FormListPage } from './pages/form/FormListPage';
import { PermissionType } from './types/permission-type';
import Connector from './utils/mqtt/Connector';

pdfjs.GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.min.mjs', import.meta.url).toString();

const AuthenticatedOrAnonymousRoute = ({
    children,
    allowAnonymous = false,
    isLoginPage = false,
}: {
    children: React.ReactNode;
    allowAnonymous?: boolean;
    isLoginPage?: boolean;
}) => {
    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();

    const isAnonymous = identity?.isAnonymous;

    // only allow anonymous to access login page
    if (isLoginPage && isAnonymous) {
        return <>{children}</>;
    }

    // block anonymous to access other routes (except routes with allowAnonymous=true)
    if (isAnonymous && !allowAnonymous) {
        return <Navigate to="/login" />;
    }

    return <>{children}</>;
};

function App() {
    const { isLoading, logout } = useAuth0();
    const { t, i18n } = useTranslation();
    const i18nProvider = {
        translate: (key: string, params: object) => t(key, params),
        changeLocale: (lang: string) => i18n.changeLanguage(lang),
        getLocale: () => i18n.language,
    };

    notification.config({
        placement: 'bottomRight',
        bottom: 40,
    });

    const handleLogout = async () => {
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(IMPERSONATE_TOKEN_KEY);
        localStorage.removeItem(CLAIM_KEY);
        await logout({ logoutParams: { returnTo: window.location.origin } });
        return {
            success: true,
        };
    };

    const authProvider: AuthProvider = {
        login: async (token: string) => {
            if (token) {
                const user: NUser.IUserIdentity = jwtDecode(token);
                if (user.isAnonymous) {
                    sessionStorage.setItem(ANONYMOUS_TOKEN_KEY, token);
                } else {
                    localStorage.setItem(TOKEN_KEY, token);
                    sessionStorage.removeItem(`${ANONYMOUS_TOKEN_KEY}`);
                }

                return {
                    success: true,
                };
            }
            return {
                success: false,
            };
        },
        logout: async () => {
            return await handleLogout();
        },
        onError: async (error) => {
            if (error.statusCode === 401) {
                notification.error({
                    message: 'Your login session is expired. Please login again.',
                    type: 'error',
                });
                await handleLogout();
            }
            return { error };
        },
        check: async () => {
            const token = localStorage.getItem(TOKEN_KEY);
            if (!token) {
                return {
                    authenticated: false,
                    error: {
                        message: 'Check failed',
                        name: 'Token not found',
                    },
                };
            }

            return { authenticated: true };
        },
        getPermissions: async () => {
            const anonymous = sessionStorage.getItem(ANONYMOUS_TOKEN_KEY);
            const impersonate = localStorage.getItem(IMPERSONATE_TOKEN_KEY);
            const token = localStorage.getItem(TOKEN_KEY);
            if (!anonymous && !impersonate && !token) {
                return Promise.reject();
            }

            if (anonymous) {
                try {
                    const user: NUser.IUserIdentity = jwtDecode(anonymous);
                    return Promise.resolve(user?.rights || []);
                } catch (err) {
                    return Promise.reject();
                }
            }

            if (!token) {
                return Promise.reject();
            }

            if (impersonate) {
                try {
                    const user: NUser.IUserIdentity = jwtDecode(impersonate);
                    return Promise.resolve(user?.rights || []);
                } catch (err) {
                    return Promise.reject();
                }
            }

            try {
                const user: NUser.IUserIdentity = jwtDecode(token);
                return Promise.resolve(user?.rights || []);
            } catch (err) {
                return Promise.reject();
            }
        },
        getIdentity: async () => {
            const anonymous = sessionStorage.getItem(ANONYMOUS_TOKEN_KEY);
            const impersonate = localStorage.getItem(IMPERSONATE_TOKEN_KEY);
            const token = localStorage.getItem(TOKEN_KEY);
            if (!anonymous && !impersonate && !token) {
                return Promise.reject();
            }

            if (anonymous) {
                try {
                    const user: NUser.IUserIdentity = jwtDecode(anonymous);
                    return Promise.resolve(user);
                } catch (err) {
                    return Promise.reject();
                }
            }

            if (!token) {
                return Promise.reject();
            }

            if (impersonate) {
                try {
                    const user: NUser.IUserIdentity = jwtDecode(impersonate);
                    const impersonating: NUser.IUserIdentity = jwtDecode(token);
                    user.impersonating = impersonating;
                    return Promise.resolve(user);
                } catch (err) {
                    return Promise.reject();
                }
            }

            try {
                const user: NUser.IUserIdentity = jwtDecode(token);
                return Promise.resolve(user);
            } catch (err) {
                return Promise.reject();
            }
        },
    };

    const resources = useResourceStore.use.data().resources;
    const dynamicResources = resources.filter((r: any) => !Object.values(DefaultResource).includes(r.name as DefaultResource));

    const dataProviders: DataProviders = {
        [DataProvider.Default]: dataProvider(getEnvConfig.BASE_API_URL),
        [DataProvider.Configuration]: dataProvider(getEnvConfig.CONFIGURATION_API_URL),
        [DataProvider.Definition]: dataProvider(getEnvConfig.DEFINITION_API_URL),
        [DataProvider.Application]: dataProvider(getEnvConfig.APPLICATION_API_URL),
        [DataProvider.DataLake]: dataProvider(getEnvConfig.DATA_LAKE_API_URL),
    };

    if (isLoading) {
        return (
            <div className="w-full h-screen">
                <Loading />
            </div>
        );
    }

    return (
        <BrowserRouter>
            <AntdApp>
                <Suspense
                    fallback={
                        <div className="w-full h-screen">
                            <Loading />
                        </div>
                    }
                >
                    <ConfigProvider
                        theme={{
                            token: {
                                fontFamily: 'Nunito',
                                fontSize: 12,
                                lineHeight: 1.5,
                                colorPrimary: colors.primary,
                            },
                            components: {
                                Button: {
                                    primaryShadow: 'none',
                                },
                                Modal: {
                                    titleFontSize: 24,
                                },
                            },
                        }}
                    >
                        <Refine
                            i18nProvider={i18nProvider}
                            dataProvider={dataProviders}
                            notificationProvider={useAppNotificationProvider}
                            routerProvider={routerBindings}
                            authProvider={authProvider}
                            resources={resources}
                            accessControlProvider={accessControlProvider}
                            options={{
                                syncWithLocation: true,
                                warnWhenUnsavedChanges: true,
                                useNewQueryKeys: true,
                                reactQuery: {
                                    clientConfig: {
                                        defaultOptions: {
                                            queries: {
                                                retry: false,
                                                refetchOnWindowFocus: false,
                                            },
                                        },
                                    },
                                },
                            }}
                        >
                            <Connector brokerUrl={getEnvConfig.MQTT_URL} options={{ keepalive: 60 }}>
                                <Routes>
                                    <Route
                                        element={
                                            <AuthenticatedOrAnonymousRoute>
                                                <Authenticated key="authenticated-inner" fallback={<CatchAllNavigate to="/login" />}>
                                                    <MDSAuthenticatedLayout>
                                                        <Outlet />
                                                    </MDSAuthenticatedLayout>
                                                </Authenticated>
                                            </AuthenticatedOrAnonymousRoute>
                                        }
                                    >
                                        <Route path="" index element={<Navigate to={INITIAL_ROUTE} />} />
                                        <Route path={DefaultResource.Dashboard} element={<MyWorkspacesPage />} />
                                        <Route path={DefaultResource.Configuration} element={<Home />} />
                                        <Route
                                            path={DefaultResource.Subscriptions}
                                            element={
                                                <>
                                                    <CanAccess action="" resource={DefaultResource.Subscriptions} fallback={<Page403 />}>
                                                        <Outlet />
                                                    </CanAccess>
                                                </>
                                            }
                                        >
                                            <Route index element={<SubscriptionPage />} />
                                            <Route path="create" element={<SubscriptionDetailPage />} />
                                            <Route path="edit/:id" element={<SubscriptionDetailPage />} />
                                        </Route>

                                        <Route
                                            path={DefaultResource.Account}
                                            element={
                                                <>
                                                    <CanAccess action="" resource={DefaultResource.Account} fallback={<Page403 />}>
                                                        <Outlet />
                                                    </CanAccess>
                                                </>
                                            }
                                        >
                                            <Route index element={<AccountPage />} />
                                            <Route path="create" element={<AccountDetailPage />} />
                                            <Route path="edit/:id" element={<AccountDetailPage />} />

                                            <Route path="subscriptions/:accountId" element={<AccountSubscriptionsPage />} />
                                            <Route path="subscriptions/:accountId/create" element={<AccountSubscriptionDetailPage />} />
                                            <Route
                                                path="subscriptions/:accountId/edit/:accountSubscriptionId"
                                                element={<AccountSubscriptionDetailPage />}
                                            />
                                        </Route>

                                        <Route
                                            path={DefaultResource.Roles}
                                            element={
                                                <>
                                                    <Outlet />
                                                </>
                                            }
                                        >
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.UserManagement}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <RoleListPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="create"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.UserManagement}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <RoleDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.UserManagement}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <RoleDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route
                                            path={DefaultResource.Users}
                                            element={
                                                <>
                                                    <Outlet />
                                                </>
                                            }
                                        >
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.UserManagement}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <UsersPage />
                                                    </CanAccess>
                                                }
                                            ></Route>
                                            <Route
                                                path="create"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.UserManagement}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <UserEditPage />
                                                    </CanAccess>
                                                }
                                            ></Route>
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.UserManagement}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <UserEditPage />
                                                    </CanAccess>
                                                }
                                            ></Route>
                                        </Route>

                                        <Route
                                            path={DefaultResource.DatalakeFile}
                                            element={
                                                <>
                                                    <Outlet />
                                                </>
                                            }
                                        >
                                            <Route
                                                path="download/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <DatalakeDownloadPage />
                                                    </CanAccess>
                                                }
                                            ></Route>
                                        </Route>

                                        <Route path={DefaultResource.DataRegisterBuilders} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.DataRegister}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <DataRegisterListPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.DataRegister}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <DataRegisterDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.WidgetBuilder} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Widget}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <WidgetManagerListPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Widget}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <WidgetDetailsPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>
                                        <Route path={DefaultResource.AggregationBuilder} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Widget}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <AggregationBuilderPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Widget}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <AggregationBuilderDetailsPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>
                                        <Route path={DefaultResource.DataRegisters} element={<Outlet />}>
                                            <Route index element={<DataRegisterBuilderView />} />
                                        </Route>

                                        <Route path={DefaultResource.DataRegister} element={<Outlet />}>
                                            <Route index path=":id" element={<DataRegisterTransactionList />} />
                                            <Route path=":id/create" element={<EditDataRegisterTransactionPage />} />
                                            <Route path=":id/edit/:dataId" element={<EditDataRegisterTransactionPage />} />
                                        </Route>

                                        <Route path={DefaultResource.FormTemplate} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.FormBuilder}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormBuilderListPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.FormBuilder}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormBuilderDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.Forms} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormListPage />
                                                    </CanAccess>
                                                }
                                            />

                                            <Route
                                                path=":id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormTransactionListPage />
                                                    </CanAccess>
                                                }
                                            />

                                            <Route
                                                path=":id/create"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path=":id/create/:originalTransactionId"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path=":id/edit/:dataId"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path=":id/edit/:dataId/:originalFormId/:originalTransactionId"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Forms}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <FormDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.WorkspaceBuilders} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.WorkspaceBuilders}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <WorkspaceListPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.WorkspaceBuilders}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <WorkspaceDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.Workspaces} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Workspaces}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <UserWorkspacesListPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path=":id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Workspaces}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <RoleUserWorkspaceDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path=":entityType/:contextValue"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Workspaces}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <UserWorkspaceDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path=":entityType/:contextValue/register/:registerId"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.Workspaces}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <UserWorkspaceDetailPage />
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.SearchDetails} element={<Outlet />}>
                                            <Route index element={<SearchDetailsPage />} />
                                        </Route>

                                        {/* Automation Builder */}
                                        <Route path={DefaultResource.AutomationBuilder} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.AutomationBuilder}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <AutoMationBuilderListPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="log"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.AutomationBuilder}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <AutoMationLogListPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.AutomationBuilder}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <AutoMationBuilderDetailPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        {/* API Builder */}
                                        <Route path={DefaultResource.ApiBuilder} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.ApiBuilder}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <ApiBuilderListPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.ApiBuilder}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <ApiBuilderDetailPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        {/* Document Builder */}
                                        <Route path={DefaultResource.DocumentBuilders} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.DocumentBuilder}
                                                        action={PermissionType.ReadOnly}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <DocumentBuilderListPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                            <Route
                                                path="edit/:id"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.DocumentBuilder}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <DocumentBuilderDetailPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.DataPropertyBuilder} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.DataPropertyBuilder}
                                                        action={PermissionType.FullAccess}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <DataPropertyBuilderListPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                            <Route path="create" element={<DataPropertyBuilderCreatePage />} />
                                            <Route path="edit/:id" element={<DataPropertyBuilderEditPage />} />
                                        </Route>

                                        {/* Collection Builder */}
                                        <Route path={DefaultResource.CollectionBuilder} element={<Outlet />}>
                                            <Route
                                                path=":registerTransactionId"
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.DataRegister}
                                                        action={PermissionType.Edit}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <CollectionBuilderDetailPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <Route path={DefaultResource.CredentialManager} element={<Outlet />}>
                                            <Route
                                                index
                                                element={
                                                    <CanAccess
                                                        resource={FeatureEnum.CredentialManager}
                                                        action={PermissionType.FullAccess}
                                                        fallback={<Page403 />}
                                                    >
                                                        <>
                                                            <CredentialManagerPage />
                                                        </>
                                                    </CanAccess>
                                                }
                                            />
                                        </Route>

                                        <>
                                            {dynamicResources.length &&
                                                dynamicResources.map((rs: any) => {
                                                    const meta = rs.meta;
                                                    const resource = meta as { [key: string]: any };
                                                    const features = resource['features'];
                                                    return (
                                                        <Route
                                                            key={rs.name}
                                                            path={rs.list as string}
                                                            element={
                                                                <CanAccess
                                                                    resource={features}
                                                                    action={PermissionType.ReadOnly}
                                                                    fallback={<Page403 />}
                                                                >
                                                                    <DynamicRouteComponent resourceName={rs.name} />
                                                                </CanAccess>
                                                            }
                                                        ></Route>
                                                    );
                                                })}
                                        </>
                                        <Route path="*" element={<ErrorComponent />} />
                                    </Route>
                                    <Route
                                        element={
                                            <AuthenticatedOrAnonymousRoute allowAnonymous={true}>
                                                <PublicLayout>
                                                    <Outlet />
                                                </PublicLayout>
                                            </AuthenticatedOrAnonymousRoute>
                                        }
                                    >
                                        <Route path={DefaultResource.Transactions} element={<Outlet />}>
                                            <Route
                                                path=":id/edit/:dataId"
                                                element={
                                                    <AuthenticatedOrAnonymousRoute allowAnonymous={true}>
                                                        <AnonymousTransactionDetailPage />
                                                    </AuthenticatedOrAnonymousRoute>
                                                }
                                            />
                                        </Route>
                                    </Route>
                                    <Route
                                        element={
                                            <AuthenticatedOrAnonymousRoute isLoginPage={true}>
                                                <Authenticated key="authenticated-outer" fallback={<Outlet />}>
                                                    <NavigateToResource />
                                                </Authenticated>
                                            </AuthenticatedOrAnonymousRoute>
                                        }
                                    >
                                        <Route path="/login" element={<Login />} />
                                    </Route>
                                </Routes>
                                <UnsavedChangesNotifier />
                            </Connector>
                        </Refine>
                    </ConfigProvider>
                </Suspense>
            </AntdApp>
        </BrowserRouter>
    );
}

export default App;
