import { DATA_PROVIDER } from '@/constants';
import { ENDPOINT_RESOURCES } from '@/constants/app-resources';
import { useApiUrl, useCustomMutation, useTranslation } from '@refinedev/core';
import { notification } from 'antd';

export const useDeleteCollectionRow = () => {
    const { mutate, isLoading: deleting } = useCustomMutation();

    const { translate } = useTranslation();

    const apiUrl = useApiUrl(DATA_PROVIDER.APPLICATION);

    const deleteCollectionRow = (params: {
        transactionId: string;
        rowKey: string;
        collectionIdentityId: string;
        activeRoleId: string;
        isTest: boolean;
        onSuccess?: () => void;
        onError?: () => void;
    }) => {
        const { transactionId, rowKey, collectionIdentityId, onSuccess, onError, activeRoleId, isTest } = params;
        mutate(
            {
                url: `${apiUrl}/${isTest ? ENDPOINT_RESOURCES.TEST_FORM_TRANSACTION_FIELD : ENDPOINT_RESOURCES.FORM_TRANSACTION_FIELD}/collection-row`,
                method: 'delete',
                values: {
                    transactionId,
                    rowKey,
                    collectionIdentityId,
                    isTest,
                    activeRoleId,
                },
                successNotification: false,
                errorNotification: false,
            },
            {
                onSuccess: () => {
                    notification.success({
                        message: translate('message.delete_successfully', { ns: 'account' }),
                    });
                    onSuccess?.();
                },
                onError: () => {
                    notification.error({
                        message: translate('message.delete_failed', { ns: 'account' }),
                    });
                    onError?.();
                },
            },
        );
    };

    return {
        deleteCollectionRow,
        deleting,
    };
};
