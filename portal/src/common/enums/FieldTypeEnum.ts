export enum FieldTypeEnum {
    Text = 'text',
    Number = 'number',
    TextArea = 'textArea',
    Select = 'select',
    DatePicker = 'datePicker',
    TimePicker = 'timePicker',
    DatetimePicker = 'datetimePicker',
    Calculation = 'calculation',
    Lookup = 'lookup',
    Duration = 'duration',
    Rollup = 'rollup',
    Separator = 'separator',
    UiGroup = 'uiGroup',
    Checkbox = 'checkbox',
    UserLookup = 'userLookup',
    RoleLookup = 'roleLookup',
    Sire2Answer = 'sire2Answer',

    Definable = 'definable',

    //Criteria fields
    Answer = 'answer',
    Comparison = 'comparison',

    //Document
    Document = 'document',
}

export enum ExtraFieldTypeEnum {
    Widget = 'widget',
}

export enum FieldTypeStaticEnum {
    TransactionId = 'transactionId',
}

export enum UiComponentTypeEnum {
    Separator = 'separator',
    UiGroup = 'uiGroup',
}

export enum ComponentTypeEnum {
    FormField = 'fieldComponent',
    UiComponent = 'uiComponent',
}

export enum AdditionalFieldTypeEnum {
    Answer = 'answer',
    Comparison = 'comparison',
    Normal = 'normal',
    Definable = 'definable',
}

export enum ValidationTypeEnum {
    ValidationResult = 'validationResult',
}
