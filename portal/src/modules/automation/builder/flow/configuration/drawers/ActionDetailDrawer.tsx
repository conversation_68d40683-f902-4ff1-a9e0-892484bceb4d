import { AutomationActionFunctionType, DataProvider } from '@/common/enums';
import { useIsAccount } from '@/common/hooks';
import { DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE, ENDPOINT_RESOURCES, NAMESPACES } from '@/constants';
import { ACTION_TYPES } from '@/constants/automation';
import { NAutomation, NDataRegisterBuilder } from '@/interfaces';
import { SelectFormField, TextFormField } from '@/modules/form-fields';
import { IOption } from '@/modules/widget/widget-types/list-widget/list-data-config-step/selectors/DataRegisterSelector';
import { useAutomationStore } from '@/stores/useAutomationStore';
import { useOne, useTable, useTranslation } from '@refinedev/core';
import { Button, Drawer, DrawerProps, Form, Popconfirm, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Node } from 'reactflow';
import { CreateTransactionAction, EmailAction, UpdateFormFieldsAction, WebhookAction, WorkflowStageChange } from './actions';
import { ACTION_CONFIG } from './actions/action-field-contanst';
import { CalculateSystemScoreAction } from './actions/CalculateSystemScoreAction';
import { CreateRegisterAction } from './actions/CreateRegisterAction';
import { CreateRelatedTransactionAction } from './actions/CreateTransactionAction/CreateRelatedTransactionAction';
import { CreateTransactionByExternal } from './actions/CreateTransactionAction/CreateTransactionActionByExternal';
import { CreateTransactionByDatasourceAction } from './actions/CreateTransactionAction/CreateTransactionByDatasourceAction';
import { CreateUpdateRegisterAction } from './actions/CreateUpdateRegisterAction';
import { ExportAction } from './actions/ExportAction';
import { PopulatePurpleTracAction } from './actions/PopulatePurpleTracAction';
import { RollupDataRegisterTransactionAction } from './actions/TransactionRollupAction/RollupDataRegisterTransactionAction';
import { RollupFormTransactionAction } from './actions/TransactionRollupAction/RollupFormTransactionAction';
import { UpdateCollectionFieldAction } from './actions/UpdateCollectionField';
import { UpdateRegisterFieldsAction } from './actions/UpdateRegisterFieldsAction/UpdateRegisterFieldsAction';
import { PopulateQ88Action } from './actions/PopulateQ88TracAction';
import { RunLLMAction } from './actions/RunLLM';

type Props = {
    onClose?: () => void;
    isOpen: boolean;
    actionNode?: Node;
};

const ACTION_COMPONENTS: Record<string, any> = {
    [AutomationActionFunctionType.CREATE_TRANSACTION]: CreateTransactionAction,
    [AutomationActionFunctionType.CREATE_RELATED_TRANSACTION]: CreateRelatedTransactionAction,
    [AutomationActionFunctionType.CREATE_REGISTER]: CreateRegisterAction,
    [AutomationActionFunctionType.UPDATE_TRANSACTION_FIELD]: UpdateFormFieldsAction,
    [AutomationActionFunctionType.UPDATE_REGISTER_FIELD]: UpdateRegisterFieldsAction,
    [AutomationActionFunctionType.CHANGE_TRANSACTION_WORKFLOW_STAGE]: WorkflowStageChange,
    [AutomationActionFunctionType.EMAIL]: EmailAction,
    [AutomationActionFunctionType.PDF]: ExportAction,
    [AutomationActionFunctionType.CREATE_TRANSACTION_BY_EXTERNAL]: CreateTransactionByExternal,
    [AutomationActionFunctionType.CREATE_UPDATE_REGISTER]: CreateUpdateRegisterAction,
    [AutomationActionFunctionType.UPDATE_COLLECTION_FIELD]: UpdateCollectionFieldAction,
    [AutomationActionFunctionType.POPULATE_PURPLETRAC]: PopulatePurpleTracAction,
    [AutomationActionFunctionType.ROLlUP_FORM_TRANSACTIONS]: RollupFormTransactionAction,
    [AutomationActionFunctionType.ROLLUP_REGISTER_RECORDS]: RollupDataRegisterTransactionAction,
    [AutomationActionFunctionType.CALCULATE_SYSTEM_SCORE]: CalculateSystemScoreAction,
    [AutomationActionFunctionType.CREATE_TRANSACTION_BY_DATASOURCE]: CreateTransactionByDatasourceAction,
    [AutomationActionFunctionType.DATA_PIPELINE_POPULATE_Q88]: PopulateQ88Action,
    [AutomationActionFunctionType.POPULATE_Q88]: PopulateQ88Action,
    [AutomationActionFunctionType.WEBHOOK]: WebhookAction,
    [AutomationActionFunctionType.RUN_LLM]: RunLLMAction,
};

export const ActionDetailDrawer: React.FC<Props & DrawerProps> = (props: Props & DrawerProps) => {
    const { onClose, isOpen, actionNode, ...restProps } = props;

    const { translate } = useTranslation();

    const methods = useForm();

    const { automation, selectedActionId, setNode } = useAutomationStore();

    const action: NAutomation.AutomationAction = actionNode?.data?.actions?.find(
        (a: NAutomation.AutomationAction) => a.id === selectedActionId,
    );
    const contextType = automation?.contextType;

    const { watch } = methods;

    const isAccount = useIsAccount();

    const watchFunctionType = watch(ACTION_CONFIG.FUNCTION_TYPE);

    const functionType = useMemo(() => {
        return watchFunctionType;
    }, [watchFunctionType]);

    const { setValue, handleSubmit } = methods;

    const [registerBuilders, setRegisterBuilders] = useState<any[]>([]);
    const [registerBuilderId, setRegisterBuilderId] = useState<string | null>();

    const [createTxDrFields, setCreateTxDrFields] = useState<{ value: string; label: string }[]>([]);

    const {
        tableQueryResult: registerBuildersQueryResult,
        setCurrent: setCurrentRegisterBuilders,
        current: currentRegisterBuilders,
    } = useTable<NDataRegisterBuilder.IDataRegisterBuilder>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER,
        initialPageSize: DEFAULT_PAGE_SIZE,
        initialCurrent: DEFAULT_PAGE_INDEX,
        sorters: {
            initial: [
                {
                    field: 'createdAt',
                    order: 'desc',
                },
            ],
        },
        syncWithLocation: false,
        hasPagination: true,
        queryOptions: {
            // enabled: functionType === AutomationActionFunctionType.CreateRegister,
            enabled: false,
        },
    });

    const { data: registerBuilderResponse, refetch: registerBuilderRefetch } = useOne<NDataRegisterBuilder.IDataRegisterBuilder>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER,
        id: registerBuilderId + '',
        queryOptions: {
            enabled: !!registerBuilderId,
        },
    });
    const onScrollRegisterBuilderLoadMore = (event: any) => {
        const target = event.target;
        if (target?.scrollTop + target?.offsetHeight === target?.scrollHeight) {
            setCurrentRegisterBuilders(currentRegisterBuilders + 1);
        }
    };

    useEffect(() => {
        let data: IOption[] =
            registerBuildersQueryResult?.data?.data?.map((value) => {
                return {
                    label: value.name,
                    value: value?.id || '',
                };
            }) || [];

        if (currentRegisterBuilders > 1) {
            data = [...registerBuilders, ...data];
        }

        setRegisterBuilders([...data]);
    }, [currentRegisterBuilders, registerBuildersQueryResult?.data?.data]);

    useEffect(() => {
        setCurrentRegisterBuilders(1);
    }, [functionType]);

    useEffect(() => {
        if (!action) return;

        methods.reset({ ...action });

        // form.setFieldValue('name', action?.name);
        // form.setFieldValue('functionType', action?.functionType);
        // for (const key in action?.configuration || {}) {
        //     form.setFieldValue(`configuration.${key}`, action?.configuration[key]);
        // }

        // setFunctionType(action?.functionType);

        // switch (action?.functionType) {
        //     case AutomationActionFunctionType.CreateTransaction: {
        //         setCurrentForms(1);
        //         setCreateTxDrMappings(action?.configuration?.mappingFields || []);
        //         setFormId(action?.configuration?.formId || null);
        //         form.setFieldValue('configuration.formId', action?.configuration?.formId || null);
        //         break;
        //     }
        //     case AutomationActionFunctionType.CreateRegister: {
        //         setCurrentRegisterBuilders(1);
        //         setCreateTxDrMappings(action?.configuration?.mappingFields || []);
        //         setRegisterBuilderId(action?.configuration?.registerBuilderId || null);
        //         form.setFieldValue('configuration.registerBuilderId', action?.configuration?.registerBuilderId || null);
        //         break;
        //     }
        //     case AutomationActionFunctionType.WFSChange: {
        //         form.setFieldValue('configuration.destinationStageIdentityId', action?.configuration?.destinationStageIdentityId || null);
        //         break;
        //     }
        //     case AutomationActionFunctionType.Email: {
        //         form.setFieldValue('configuration.documentId', action?.configuration?.documentId || null);
        //         form.setFieldValue('configuration.to', action?.configuration?.to || null);
        //         form.setFieldValue('configuration.cc', action?.configuration?.cc || null);
        //         break;
        //     }
        //     case AutomationActionFunctionType.Notification: {
        //         form.setFieldValue('configuration.documentId', action?.configuration?.documentId || null);
        //         break;
        //     }

        //     default:
        //         break;
        // }
    }, [action]);

    useEffect(() => {
        if (!registerBuilderId) return;

        registerBuilderRefetch();
    }, [registerBuilderId]);

    useEffect(() => {
        const fields =
            registerBuilderResponse?.data?.latestDataRegisterVersion?.fields
                ?.filter((item) => item.label !== 'Transaction Id')
                ?.map((item) => ({
                    value: item.fieldId,
                    label: item.label,
                })) || [];

        setCreateTxDrFields(fields);
    }, [registerBuilderResponse?.data]);

    const actionOptions = contextType
        ? ACTION_TYPES[contextType].map((item) => {
              return {
                  value: item,
                  label: translate(`builder.action.function_type.${item}`, { ns: NAMESPACES.AUTOMATION }),
              } as DefaultOptionType;
          })
        : [];

    const onSubmit = async (values: Record<string, any>) => {
        if (!actionNode?.data?.actions) return;

        const { name, functionType, configuration } = values;
        const actionData = {
            name: name,
            functionType: functionType,
            configuration: { ...(action?.configuration ?? {}), ...configuration },
        } as NAutomation.AutomationAction;

        const newAction: NAutomation.AutomationAction = { ...action, ...actionData };

        const actionIndex = actionNode?.data.actions?.findIndex((item: NAutomation.AutomationAction) => item.id === action?.id) ?? -1;
        if (actionIndex >= 0) {
            actionNode.data.actions[actionIndex] = newAction;
        } else {
            actionNode.data.actions.push(newAction);
        }

        setNode(actionNode);

        // props?.setAction(newAction);
        onClose?.();
    };

    const handleClose = async () => {
        const isValid = await methods.trigger();
        if (!isValid) return;
        onSubmit(methods.getValues());
    };

    const ActionComponent = ACTION_COMPONENTS[functionType as any] || null;

    const handleDeleteAction = (id: string | undefined) => {
        if (!actionNode?.data?.actions) return;
        actionNode.data.actions = actionNode?.data?.actions?.filter((item: NAutomation.AutomationAction) => item.id !== id);
        setNode(actionNode);
    };

    return (
        <Drawer
            onClose={onClose}
            open={isOpen}
            size={'large'}
            closeIcon={null}
            title={
                <div className="flex gap-2 items-center justify-between">
                    <h4>
                        {translate(action?.name ? 'create_action.drawer_header_updated' : 'create_action.drawer_header', {
                            ns: NAMESPACES.AUTOMATION,
                        })}
                    </h4>
                    <Space>
                        <Popconfirm
                            title={translate('create_action.delete_popup', { ns: NAMESPACES.AUTOMATION })}
                            onConfirm={() => {
                                handleDeleteAction(action?.id);
                                onClose?.();
                            }}
                        >
                            <Button type="default" danger>
                                {translate('button.delete')}
                            </Button>
                        </Popconfirm>
                        <Button loading={false} type="default" onClick={handleClose}>
                            {translate('button.close')}
                        </Button>
                    </Space>
                </div>
            }
            {...restProps}
        >
            <FormProvider {...methods}>
                <Form layout="horizontal">
                    <TextFormField
                        required
                        name={ACTION_CONFIG.NAME}
                        label={translate('create_action.action_name.label', { ns: NAMESPACES.AUTOMATION })}
                        placeholder={translate('create_action.action_name.placeholder', { ns: NAMESPACES.AUTOMATION })}
                        rules={{
                            required: {
                                value: true,
                                message: translate('create_action.action_name.require_msg', { ns: NAMESPACES.AUTOMATION }),
                            },
                        }}
                    />

                    <SelectFormField
                        name={ACTION_CONFIG.FUNCTION_TYPE}
                        rules={{
                            required: {
                                value: true,
                                message: translate('create_action.action_type.require_msg', { ns: NAMESPACES.AUTOMATION }),
                            },
                        }}
                        showSearch={true}
                        label={translate('create_action.action_type.label', { ns: NAMESPACES.AUTOMATION })}
                        placeholder={translate('create_action.action_type.placeholder', { ns: NAMESPACES.AUTOMATION })}
                        options={actionOptions}
                        onChange={() => {
                            setRegisterBuilderId(null);

                            setValue('configuration.registerBuilderId', null);
                            setValue('configuration.formId', null);
                        }}
                    />

                    <h2 className="pb-2">{translate('create_action.parameters_mapping', { ns: NAMESPACES.AUTOMATION })}</h2>
                    {ActionComponent && <ActionComponent functionType={functionType} />}
                </Form>
            </FormProvider>
        </Drawer>
    );
};
