import { FieldTypeEnum, LookupDataset } from '@/common/enums';
import { Icon } from '@/components/icon';
import {
    DATA_REGISTER_RECORD_ID,
    DeleteOutlined,
    MoreOutlined,
    NAMESPACES,
    NON_LOGIC_FIELD_TYPES,
    TRANSACTION_FIELD_ID,
} from '@/constants';
import { DateFormSettingField, RadioGroupFormField, SelectFormField } from '@/modules/form-fields';
import { DatetimeFormSettingField } from '@/modules/form-fields/DatetimeFormSettingField';
import { TimeFormSettingField } from '@/modules/form-fields/TimeFormSettingField';
import { widgetRendererDefinitions } from '@/modules/shared/form-builder/definition';
import { FormFieldType } from '@/modules/shared/form-builder/types';
import { useTranslation } from '@refinedev/core';
import { Button, Tooltip } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useEffect, useState } from 'react';
import { useWatch } from 'react-hook-form';

type Props = {
    index: number;
    actionType: 'form' | 'register';
    sourceFields: { [key: string]: Partial<FormFieldType> };
    targetFields: { [key: string]: Partial<FormFieldType> };
    controlName: string;
    onRemove: (index: number) => void;
};

const fieldWithoutPickers = [FieldTypeEnum.DatePicker, FieldTypeEnum.DatetimePicker, FieldTypeEnum.TimePicker];

export const ConditionSettingItem: React.FC<Props> = (props: Props) => {
    const { index, onRemove, sourceFields, targetFields, actionType, controlName } = props;
    const { translate } = useTranslation();
    const [sourceFieldOptions, setSourceFieldOptions] = useState<DefaultOptionType[]>([]);
    const [targetFieldOptions, setTargetFieldOptions] = useState<DefaultOptionType[]>([]);

    const fromFieldControlName = `${controlName}.${index}.fromFieldId`;
    const toFieldControlName = `${controlName}.${index}.toFieldId`;
    const comparatorTypeControlName = `${controlName}.${index}.comparatorType`;

    const watchTargetFieldId = useWatch({
        name: toFieldControlName,
    });
    const watchComparatorType = useWatch({
        name: comparatorTypeControlName,
        defaultValue: 'value',
    });
    const watchSourceFieldId = useWatch({
        name: fromFieldControlName,
    });

    const targetField = targetFields?.[watchTargetFieldId ?? ''];
    const fieldType = targetField?.calculationFormula?.dataType ?? targetField?.rollup?.dataType ?? targetField?.type ?? 'field';
    const sourceField = sourceFields?.[watchSourceFieldId ?? ''];

    const FieldValue = widgetRendererDefinitions[fieldType as string]?.Field;

    useEffect(() => {
        const _fieldOptions = Object.values(sourceFields ?? {})
            .filter((field) => {
                if (field.fieldId === TRANSACTION_FIELD_ID || NON_LOGIC_FIELD_TYPES.includes(field.type as FieldTypeEnum)) {
                    return false;
                }

                switch (targetField?.type) {
                    case FieldTypeEnum.Calculation:
                        return targetField?.configuration?.calculationFormula?.dataType === field.type;
                    default:
                        return targetField?.type === field.type;
                }
            })
            .map((field) => ({
                value: field.fieldId,
                label: field.label,
            }));

        setSourceFieldOptions(_fieldOptions as DefaultOptionType[]);
    }, [watchTargetFieldId, sourceFields]);

    useEffect(() => {
        const _fieldOptions = Object.values(targetFields ?? {})
            .filter((field) => {
                if (field.fieldId === TRANSACTION_FIELD_ID || NON_LOGIC_FIELD_TYPES.includes(field.type as FieldTypeEnum)) {
                    return false;
                }
                return true;
            })
            .map((field) => ({
                value: field.fieldId,
                label: field.label,
            }));

        if (actionType === 'register') {
            _fieldOptions.push({ value: DATA_REGISTER_RECORD_ID, label: 'Record Id' });
        }
        setTargetFieldOptions(_fieldOptions as DefaultOptionType[]);
    }, [watchTargetFieldId, targetFields]);

    useEffect(() => {
        const _fieldOptions = Object.values(sourceFields ?? {})
            .filter((field) => {
                if (field.fieldId === TRANSACTION_FIELD_ID || NON_LOGIC_FIELD_TYPES.includes(field.type as FieldTypeEnum)) {
                    return false;
                }
                if (watchTargetFieldId === DATA_REGISTER_RECORD_ID) {
                    return field.type === FieldTypeEnum.Lookup;
                }

                switch (targetField?.type) {
                    case FieldTypeEnum.Calculation:
                        return targetField?.configuration?.calculationFormula?.dataType === field.type;
                    default:
                        return targetField?.type === field.type;
                }
            })
            .map((field) => ({
                value: field.fieldId,
                label: field.label,
            }));

        setSourceFieldOptions(_fieldOptions as DefaultOptionType[]);
    }, [watchTargetFieldId, sourceFields]);

    return (
        <div className="flex gap-2">
            <div className="w-full">
                <SelectFormField
                    name={toFieldControlName}
                    label={translate('create_action.action_create_transaction.targetField.label', {
                        ns: NAMESPACES.AUTOMATION,
                    })}
                    placeholder={translate('create_action.action_create_transaction.targetField.placeholder', {
                        ns: NAMESPACES.AUTOMATION,
                    })}
                    options={targetFieldOptions}
                />
            </div>
            <div className="flex justify-center">
                <Tooltip
                    placement="top"
                    title={
                        <article className="p-2">
                            <p className="text-sm font-medium text-black mb-2">
                                {translate('create_action.update_form_fields.select_value_source', {
                                    ns: NAMESPACES.AUTOMATION,
                                })}
                            </p>
                            <RadioGroupFormField
                                name={comparatorTypeControlName}
                                options={[
                                    { label: 'Value', value: 'value' },
                                    { label: 'Field', value: 'field' },
                                ]}
                                defaultValue="value"
                                optionType="button"
                            />
                        </article>
                    }
                    color={'white'}
                >
                    <Button danger className="border-none bg-transparent shadow-none p-0">
                        <Icon icon={MoreOutlined} />
                    </Button>
                </Tooltip>
            </div>
            {watchComparatorType === 'field' && sourceFieldOptions?.length > 0 && (
                <div className="w-full">
                    <SelectFormField
                        name={fromFieldControlName}
                        label={translate('create_action.action_create_transaction.datasourceField.label', {
                            ns: NAMESPACES.AUTOMATION,
                        })}
                        placeholder={translate('create_action.action_create_transaction.datasourceField.placeholder', {
                            ns: NAMESPACES.AUTOMATION,
                        })}
                        options={sourceFieldOptions}
                    />
                </div>
            )}
            {watchComparatorType === 'value' && (
                <span className="w-full">
                    {fieldType && FieldValue && !fieldWithoutPickers.includes(fieldType as FieldTypeEnum) && (
                        <FieldValue
                            targetId={targetField?.configuration?.targetId}
                            dataset={LookupDataset.DataRegister}
                            name={`${controlName}.${index}.fieldDefaultValue`}
                            label={translate('create_action.update_form_fields.field_default_value', { ns: NAMESPACES.AUTOMATION })}
                            options={sourceField?.options}
                            rules={{
                                required: {
                                    value: true,
                                    message: translate('create_action.update_form_fields.field_value_required', {
                                        ns: NAMESPACES.AUTOMATION,
                                    }),
                                },
                            }}
                        />
                    )}
                    {fieldType && fieldWithoutPickers.includes(fieldType as FieldTypeEnum) && (
                        <>
                            {fieldType === FieldTypeEnum.DatePicker && (
                                <DateFormSettingField
                                    name={`${controlName}.${index}.defaultValue`}
                                    pickerTypeName={`${controlName}.${index}.pickerType`}
                                />
                            )}

                            {fieldType === FieldTypeEnum.TimePicker && (
                                <TimeFormSettingField
                                    name={`${controlName}.${index}.defaultValue`}
                                    pickerTypeName={`${controlName}.${index}.pickerType`}
                                />
                            )}

                            {fieldType === FieldTypeEnum.DatetimePicker && (
                                <DatetimeFormSettingField
                                    name={`${controlName}.${index}.defaultValue`}
                                    pickerTypeName={`${controlName}.${index}.pickerType`}
                                />
                            )}
                        </>
                    )}
                </span>
            )}

            <div className="col-span-1 w-full col-end-13">
                <Button danger onClick={() => onRemove(index)}>
                    <Icon icon={DeleteOutlined} />
                </Button>
            </div>
        </div>
    );
};
