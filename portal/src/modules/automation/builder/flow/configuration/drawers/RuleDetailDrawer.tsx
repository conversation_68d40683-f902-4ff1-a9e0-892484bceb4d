import {
    ActionRunModeEnum,
    AutomationContextType,
    AutomationNodeType,
    DataRegisterEventEnum,
    FieldTypeEnum,
    TransactionEventEnum,
} from '@/common/enums';
import { handleExternalProperties, MappingType, useRegisterFieldOptions } from '@/common/hooks';
import {
    EXTERNAL_DATA_SOURCE__SEPARATE_MARK,
    EXTERNAL_DATA_SOURCE_MAPPING,
    FROM_STAGE_FIELD,
    NAMESPACES,
    NON_LOGIC_FIELD_TYPES,
    PlusOutlined,
    STAGE_KPI_FIELD_ID,
    TO_STAGE_FIELD,
} from '@/constants';
import { Automation } from '@/constants/automation';
import { NAutomation, NDataRegisterBuilder, NForm } from '@/interfaces';
import { ConditionFormField, SelectFormField, SwitchFormField, TextFormField } from '@/modules/form-fields';
import { PopulateConditions } from '@/modules/shared/form-builder/populate-conditions/PopulateConditions';
import { parseToConditionFieldTypes } from '@/modules/shared/form-builder/utils';
import { useFormStore } from '@/stores';
import { useAutomationStore } from '@/stores/useAutomationStore';
import { ConditionFieldType } from '@/types/ConditionFieldType';
import { IFormCollection } from '@/types/FormCollectionType';
import { useTranslation } from '@refinedev/core';
import { Button, Drawer, DrawerProps, Popconfirm } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { get, keyBy } from 'lodash';
import { useEffect, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { Node } from 'reactflow';
import { useAutomationContext } from '../../../../hooks/useAutomationContext';

type Props = {
    isOpen: boolean;
    rule: Node;
    automationTemplate?: NAutomation.Automation;
    onClose?: () => void;
};

export const RuleDetailDrawer: React.FC<Props & DrawerProps> = (props: Props & DrawerProps) => {
    const { onClose, isOpen, rule, automationTemplate, ...restProps } = props;
    const { contextId, contextType } = automationTemplate ?? {};
    const [conditionFields, setConditionFields] = useState<ConditionFieldType[]>([]);
    const [triggerFields, setTriggerFields] = useState<DefaultOptionType[]>([]);
    const [triggerWfs, setTriggerWfs] = useState<DefaultOptionType[]>([]);
    const [activeKey, setActiveKey] = useState<string[]>([]);

    const { contextData, isFetching: isFetchingContext } = useAutomationContext(
        contextId ?? '',
        contextType ?? AutomationContextType.FormTransaction,
    );

    const { translate } = useTranslation();
    const { setNode, deleteNode, deleteEdge, nodes, edges, selectedNodeId } = useAutomationStore();
    const eventType = nodes?.find((node) => node.type === AutomationNodeType.Event)?.data?.configuration?.type;

    const method = useForm({
        defaultValues: {
            name: rule.data?.label ?? '',
            isEnable: rule.data?.isEnable,
            configuration: rule.data?.configuration ?? {},
        },
    });

    const { handleSubmit } = method;
    const wfsIdentityId = (method as any).watch(Automation.rule.triggerWorkFlowStage);

    const runModes: DefaultOptionType[] = [
        {
            value: ActionRunModeEnum.PARALLEL,
            label: translate('builder.rule.parallel', { ns: NAMESPACES.AUTOMATION }),
        },
        {
            value: ActionRunModeEnum.ORDERING,
            label: translate('builder.rule.ordering', { ns: NAMESPACES.AUTOMATION }),
        },
    ];
    const dsFormName = Automation.rule.dataSource;

    const handleClose = (values: Record<string, any>) => {
        const { name, isEnable, configuration } = values;
        const ruleData = {
            label: name,
            isEnable: isEnable,
            configuration: { ...(rule.data?.configuration ?? {}), ...configuration },
        };
        rule.data = { ...rule.data, ...ruleData };
        setNode(rule);
        onClose?.();
    };

    useEffect(() => {
        if (!contextData) {
            return;
        }

        let cFields: ConditionFieldType[] = [];
        let trgFields: DefaultOptionType[] = [];
        let trgWfs: DefaultOptionType[] = [];

        switch (contextType) {
            case AutomationContextType.FormTransaction:
                {
                    const form = contextData as NForm.FormType;

                    cFields = getConditionFieldsForForm(form);
                    trgFields = getTriggerFieldsFromForm(form) ?? [];
                    trgWfs = getTriggerWfsForm(form) ?? [];
                }
                break;

            case AutomationContextType.DataRegister:
                {
                    const dr = contextData as NDataRegisterBuilder.IDataRegisterBuilder;
                    cFields = getConditionFieldsForDr(dr);
                    trgFields = getTriggerFieldsFromDr(dr) ?? [];
                }
                break;

            case AutomationContextType.EXTERNAL:
                {
                    // const externalFields =  handleExternalProperties({ properties: contextData, dataSourceType: contextType, dataSource: contextType });
                    const reversedMapping: Record<string, string> = Object.fromEntries(
                        Object.entries(EXTERNAL_DATA_SOURCE_MAPPING).map(([key, value]) => [value, key]),
                    );

                    const combineDataSourceKey = reversedMapping[contextId as string];
                    if (combineDataSourceKey) {
                        const [dataSourceType, dataSource] = combineDataSourceKey.split(EXTERNAL_DATA_SOURCE__SEPARATE_MARK);
                        const dataSourceProperties = handleExternalProperties({
                            properties: contextData,
                            dataSourceType,
                            dataSource,
                        });

                        cFields = dataSourceProperties.map((item) => {
                            return {
                                ...item,
                                label: item.name,
                                name: item.id,
                                type: MappingType[item.dataType]?.[0],
                            };
                        });
                    }
                }
                break;
        }

        setTriggerWfs(trgWfs);
        setConditionFields(cFields);
        setTriggerFields(trgFields);
    }, [contextData]);

    const getConditionFieldsForForm = (form: NForm.FormType) => {
        let conFields: ConditionFieldType[] = [];
        const formFields = form.latestFormVersion?.fields ?? [];

        const map = keyBy(formFields as any, 'fieldId');

        conFields = parseToConditionFieldTypes(map)?.filter((item) => !NON_LOGIC_FIELD_TYPES.includes(item.type));
        const stages = form.latestFormVersion?.stages ?? [];
        const stageOptions = stages.map((stage) => {
            return {
                value: stage.identityId ?? '',
                label: <span>{stage.name}</span>,
            };
        });

        switch (eventType) {
            case TransactionEventEnum.FORM_TRANSACTION_STAGE_UPDATED:
                conFields.push({
                    id: FROM_STAGE_FIELD,
                    label: translate('builder.rule.from_stage', { ns: NAMESPACES.AUTOMATION }),
                    name: FROM_STAGE_FIELD,
                    type: FieldTypeEnum.Select,
                    options: stageOptions,
                });
                conFields.push({
                    id: TO_STAGE_FIELD,
                    label: translate('builder.rule.to_stage', { ns: NAMESPACES.AUTOMATION }),
                    name: TO_STAGE_FIELD,
                    type: FieldTypeEnum.Select,
                    options: stageOptions,
                });
                break;
            case TransactionEventEnum.FORM_TRANSACTION_STAGE_KPI_STATUS_CHANGED: {
                const options = (
                    (contextData as NForm.FormType)?.latestFormVersion?.stages?.find((item) => item.identityId === wfsIdentityId)?.config
                        ?.kpiSettings || []
                ).map((item) => ({
                    label: item.kpiStatusLabel,
                    value: item.id,
                }));

                console.log(':options', options);
                if (!conFields.find((item) => item.id === STAGE_KPI_FIELD_ID)) {
                    conFields.push({
                        id: STAGE_KPI_FIELD_ID,
                        label: translate('builder.rule.wfs_kpi_status', { ns: NAMESPACES.AUTOMATION }),
                        name: STAGE_KPI_FIELD_ID,
                        type: FieldTypeEnum.Select,
                        options,
                    });
                }
                break;
            }
            default:
                conFields.push({
                    id: TO_STAGE_FIELD,
                    label: translate('builder.rule.stage', { ns: NAMESPACES.AUTOMATION }),
                    name: TO_STAGE_FIELD,
                    type: FieldTypeEnum.Select,
                    options: stageOptions,
                });
        }
        return conFields;
    };

    const getConditionFieldsForDr = (dr: NDataRegisterBuilder.IDataRegisterBuilder) => {
        let conFields: ConditionFieldType[] = [];
        const formFields = dr.latestDataRegisterVersion?.fields ?? [];
        const map = keyBy(formFields as any, 'fieldId');
        conFields = parseToConditionFieldTypes(map)?.filter((item) => !NON_LOGIC_FIELD_TYPES.includes(item.type));
        return conFields;
    };

    const getTriggerWfsForm = (form: NForm.FormType) => {
        return form.latestFormVersion?.stages?.map((item) => {
            return {
                value: item.identityId,
                label: item.name,
            } as DefaultOptionType;
        });
    };
    const getTriggerFieldsFromForm = (form: NForm.FormType) => {
        return form.latestFormVersion?.fields
            ?.filter((item) => !NON_LOGIC_FIELD_TYPES.includes(item.type))
            .map((item) => {
                return {
                    value: item.fieldId,
                    label: item.label,
                } as DefaultOptionType;
            });
    };
    const getTriggerFieldsFromDr = (dr: NDataRegisterBuilder.IDataRegisterBuilder) => {
        return dr?.latestDataRegisterVersion?.fields?.map((item) => {
            return {
                value: item.fieldId,
                label: item.label,
            } as DefaultOptionType;
        });
    };

    const handleRemove = () => {
        if (rule.id) {
            deleteNode(rule.id);
            // delete action edge
            const actionEdge = edges.find((item) => item.source == rule.id);
            if (actionEdge) {
                deleteEdge(actionEdge.id);
                deleteNode(actionEdge.target);
            }
            onClose?.();
        }
    };

    return (
        <Drawer
            onClose={onClose}
            open={isOpen}
            title={
                <div className="flex gap-2 items-center justify-between">
                    <span className="text-lg">{rule?.data?.name ?? translate('builder.header.rule', { ns: NAMESPACES.AUTOMATION })}</span>
                    <span className="flex gap-2">
                        <Popconfirm
                            title={translate('builder.action.delete_rule', { ns: NAMESPACES.AUTOMATION })}
                            description={translate('button.delete_confirm', { ns: NAMESPACES.COMMON })}
                            onConfirm={handleRemove}
                            okText={translate('yes')}
                            cancelText={translate('no')}
                        >
                            <Button danger>{translate('button.remove')}</Button>
                        </Popconfirm>
                        <Button type="default" onClick={handleSubmit(handleClose)}>
                            {translate('button.close')}
                        </Button>
                    </span>
                </div>
            }
            {...restProps}
        >
            <FormProvider {...method}>
                <div className="flex flex-col gap-2">
                    <TextFormField
                        required
                        name={Automation.rule.name}
                        label={translate('builder.rule.name', { ns: NAMESPACES.AUTOMATION })}
                    />
                    <SwitchFormField
                        name={Automation.rule.isEnable}
                        label={translate('builder.rule.enable', { ns: NAMESPACES.AUTOMATION })}
                    />
                    <SelectFormField
                        name={Automation.rule.runMode}
                        options={runModes}
                        loading={isFetchingContext}
                        defaultValue={ActionRunModeEnum.PARALLEL}
                        label={translate('builder.rule.run_mode', { ns: NAMESPACES.AUTOMATION })}
                        rules={{
                            required: {
                                value: true,
                                message: 'Run mode field is required',
                            },
                        }}
                    />
                    {eventType === TransactionEventEnum.FORM_TRANSACTION_STAGE_KPI_STATUS_CHANGED && (
                        <SelectFormField
                            name={Automation.rule.triggerWorkFlowStage}
                            options={triggerWfs}
                            loading={isFetchingContext}
                            onChange={(value) => {
                                console.log(value);
                            }}
                            label={translate('builder.rule.trigger_work_flow_stage', { ns: NAMESPACES.AUTOMATION })}
                            // rules={{
                            //     required: {
                            //         value: true,
                            //         message: 'Work Flow Stage is required',
                            //     },
                            // }}
                        />
                    )}

                    {(eventType === TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED ||
                        eventType === DataRegisterEventEnum.DATA_REGISTER_RECORD_FIELD_UPDATED) && (
                        <SelectFormField
                            name={Automation.rule.triggerFields}
                            mode="multiple"
                            options={triggerFields}
                            loading={isFetchingContext}
                            label={translate('builder.rule.trigger_fields', { ns: NAMESPACES.AUTOMATION })}
                        />
                    )}

                    {eventType === TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED && <CollectionTransactionFieldChangedRule />}

                    <ConditionFormField
                        containerTitle={translate('builder.rule.conditions', { ns: NAMESPACES.AUTOMATION })}
                        name={Automation.rule.conditions}
                        data={{ fields: conditionFields, rules: rule?.data?.configuration?.conditions ?? {} }}
                    />

                    <PopulateConditions fields={contextData?.latestFormVersion?.fields} dsFormName={dsFormName} />
                </div>
            </FormProvider>
        </Drawer>
    );
};

export const CollectionTransactionFieldChangedRule = () => {
    const selectedCollectionIds: string[] =
        useWatch({
            name: Automation.rule.selectedCollectionIds,
        }) ?? [];

    const { formTemplate } = useFormStore();

    const collectionOptions =
        formTemplate?.latestFormVersion?.formCollections?.map((item) => ({
            label: item.name,
            value: item.identityId,
        })) ?? [];

    const { translate } = useTranslation();
    return (
        <>
            <section>
                <SelectFormField
                    name={`${Automation.rule.selectedCollectionIds}`}
                    mode="multiple"
                    options={collectionOptions}
                    label={translate('builder.rule.collections', { ns: NAMESPACES.AUTOMATION })}
                />

                {selectedCollectionIds.map((collectionIdentityId) => {
                    const collection = formTemplate?.latestFormVersion?.formCollections?.find(
                        (item) => item.identityId === collectionIdentityId,
                    );

                    return (
                        <div>
                            <h4>{collection?.name}</h4>
                            <CollectionFieldSelector collection={collection as IFormCollection} />
                        </div>
                    );
                })}
            </section>
        </>
    );
};

export const CollectionFieldSelector: React.FC<{ collection: IFormCollection }> = ({ collection }) => {
    const { isLoading, options } = useRegisterFieldOptions({
        registerId: collection.dataRegisterId,
    });

    const { translate } = useTranslation();

    const populatedFieldOptions = options?.filter((f) => f.type === FieldTypeEnum.Definable || f.isSupportField?.toString() === 'true');

    return (
        <SelectFormField
            loading={isLoading}
            name={`${Automation.rule.collections}.${collection.identityId}.fieldIds`}
            options={populatedFieldOptions}
            mode="multiple"
            label={translate('builder.rule.collection_fields', { ns: NAMESPACES.AUTOMATION })}
            rules={{
                required: {
                    value: true,
                    message: translate('builder.rule.collection_fields_required', { ns: NAMESPACES.AUTOMATION }),
                },
            }}
        />
    );
};
