import { AutomationActionFunctionType, AutomationContextType, DataProvider, FieldTypeEnum } from '@/common/enums';
import { useIsAccount } from '@/common/hooks';
import {
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
    ENDPOINT_RESOURCES,
    NAMESPACES,
    NON_LOGIC_FIELD_TYPES,
    TRANSACTION_FIELD_ID,
} from '@/constants';
import { NDataRegisterBuilder, NForm } from '@/interfaces';
import { IOption } from '@/modules/widget/widget-types/list-widget/list-data-config-step/selectors/DataRegisterSelector';
import { CrudFilters, useOne, useTable, useTranslation } from '@refinedev/core';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { useAutomationStore } from '../../../../../../../../stores/useAutomationStore';
import { ACTION_CONFIG } from '../action-field-contanst';
import { SelectFormField } from '../../../../../../../form-fields';

export const PopulateQ88Action: React.FC = () => {
    const { translate } = useTranslation();
    const { setValue } = useFormContext();
    const isAccount = useIsAccount();

    const { automation } = useAutomationStore();
    const contextType = automation?.contextType;
    const contextId = automation?.contextId;

    const { data: registerTemplateResponse } = useOne<NDataRegisterBuilder.IDataRegisterBuilder>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER,
        id: contextId,
        queryOptions: {
            enabled: contextType === AutomationContextType.DataRegister && !!contextId,
        },
    });
    const registerCtx = registerTemplateResponse?.data?.latestDataRegisterVersion;
    const registerFields = registerCtx?.fields || [];

    const { data: formTemplateResponse } = useOne<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE,
        id: contextId + '',
        queryOptions: {
            enabled: contextType === AutomationContextType.FormTransaction && !!contextId,
        },
    });
    const formCtx = formTemplateResponse?.data?.latestFormVersion;
    const formFields = formCtx?.fields || [];

    const targetFields = useMemo(() => {
        switch (contextType) {
            case AutomationContextType.DataRegister: {
                return registerFields
                    .filter((field) => field.type === FieldTypeEnum.Lookup)
                    .map((field) => ({
                        label: field.label,
                        value: field.fieldId,
                        type: field.type,
                    }));
            }
            case AutomationContextType.FormTransaction: {
                return formFields
                    .filter((field) => field.type === FieldTypeEnum.Lookup)
                    .map((field) => ({
                        label: field.label,
                        value: field.fieldId,
                        type: field.type,
                    }));
            }
            default:
                return [];
        }
    }, [contextType, registerFields, formFields]);

    return (
        <>
            <div className="w-full col-span-3">
                <SelectFormField
                    name={'vesselContextFieldId'}
                    label={translate('create_action.q88.vessel_context', {
                        ns: NAMESPACES.AUTOMATION,
                    })}
                    options={targetFields}
                />
            </div>
        </>
    );
};
