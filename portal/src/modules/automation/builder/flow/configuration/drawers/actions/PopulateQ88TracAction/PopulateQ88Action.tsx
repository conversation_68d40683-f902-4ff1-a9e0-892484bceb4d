import { useRegisterFieldOptions } from '@/common/hooks';
import { NAMESPACES } from '@/constants';
import { SelectFormField } from '@/modules/form-fields';
import { useFormStore } from '@/stores';
import { useTranslation } from '@refinedev/core';
import { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

export const PopulateQ88Action: React.FC = () => {
    const { translate } = useTranslation();
    const { fields } = useFormStore();

    const { setValue } = useFormContext();

    const VesselIMOFieldName = 'configuration.vesselIMO';
    const IMOFieldName = 'configuration.IMO';

    const fieldOptions = useMemo(() => {
        return Object.values(fields ?? {}).map((field) => ({
            label: field.label,
            value: field.fieldId,
            type: field.type,
        }));
    }, [fields]);

    const selectedFieldWatch = useWatch({
        name: VesselIMOFieldName,
    });

    const selectedField = fields?.[selectedFieldWatch];

    const { options, isLoading } = useRegisterFieldOptions({ registerId: selectedField?.targetId });

    return (
        <section>
            <SelectFormField
                name={VesselIMOFieldName}
                label={translate('builder.rule_action.select_vessel_imo', { ns: NAMESPACES.AUTOMATION })}
                rules={{
                    required: {
                        value: true,
                        message: translate('builder.rule_action.imo_required', { ns: NAMESPACES.AUTOMATION }),
                    },
                }}
                onChange={() => {
                    setValue(IMOFieldName, '');
                }}
                options={fieldOptions}
                allowClear
            />

            {selectedField?.targetId && (
                <SelectFormField
                    loading={isLoading}
                    name={IMOFieldName}
                    label={translate('builder.rule_action.select_imo_field', { ns: NAMESPACES.AUTOMATION })}
                    rules={{
                        required: {
                            value: true,
                            message: translate('builder.rule_action.imo_required', { ns: NAMESPACES.AUTOMATION }),
                        },
                    }}
                    options={options}
                    allowClear
                />
            )}
        </section>
    );
};
