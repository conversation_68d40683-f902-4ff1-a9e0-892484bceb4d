import { AutomationActionFunctionType, DataProvider } from '@/common/enums';
import { useIsAccount } from '@/common/hooks';
import { DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE, ENDPOINT_RESOURCES, NAMESPACES, NON_LOGIC_FIELD_TYPES } from '@/constants';
import { NForm } from '@/interfaces';
import { ConditionFormField, SelectFormField } from '@/modules/form-fields';
import { CrudFilters, useOne, useTable, useTranslation } from '@refinedev/core';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { ACTION_CONFIG } from '../action-field-contanst';
import { useEffect, useMemo, useState } from 'react';
import { IOption } from '@/modules/widget/widget-types/list-widget/list-data-config-step/selectors/DataRegisterSelector';
import { But<PERSON>, Collapse, CollapseProps } from 'antd';
import { debounce, keyBy } from 'lodash';
import { ConditionSettingItem } from './ConditionSetttingItem';
import { FormFieldType } from '@/modules/shared/form-builder/types';
import { ActionSettingItem } from './ActionSettingItem';
import { ConditionFieldType } from '@/types/ConditionFieldType';
import { parseToConditionFieldTypes } from '@/modules/shared/form-builder/utils';
import { useFormStore } from '@/stores';

const SourceType = {
    FORM: 'form',
    REGISTER: 'register',
};

const SourceTypeOptions: IOption[] = [
    {
        label: 'Form',
        value: SourceType.FORM,
    },
    {
        label: 'Register',
        value: SourceType.REGISTER,
    },
];

export const RollupFormTransactionAction: React.FC = () => {
    const { translate } = useTranslation();

    const { fields } = useFormStore();

    const [forms, setForms] = useState<any[]>([]);
    const [targetForms, setTargetForms] = useState<any[]>([]);

    const [sourceForm, setSourceForm] = useState<NForm.FormVersionType>();
    const [targetForm, setTargetForm] = useState<NForm.FormVersionType>();
    const [activeKey, setActiveKey] = useState<string[]>([]);

    const [sourceFields, setSourceFields] = useState<{ [key: string]: Partial<FormFieldType> }>();
    const [targetFields, setTargetFields] = useState<{ [key: string]: Partial<FormFieldType> }>();

    const { setValue } = useFormContext();
    const isAccount = useIsAccount();

    const conditionsControlName = 'configuration.conditions';

    const watchFunctionType = useWatch({
        name: ACTION_CONFIG.FUNCTION_TYPE,
    });

    const watchFormId = useWatch({
        name: 'configuration.formId',
    });
    const watchTargetFormId = useWatch({
        name: 'configuration.targetFormId',
    });

    const watchSourceType = useWatch({
        name: 'configuration.sourceType',
    });

    //register scope

    const [register, setRegisters] = useState<any[]>([]);
    const [sourceRegisterFields, setSourceRegisterFields] = useState<{ [key: string]: Partial<FormFieldType> }>();
    const [sourceRegister, setSourceRegister] = useState() as any;

    const {
        tableQueryResult: formsQueryResult,
        setCurrent: setCurrentForms,
        setFilters,
        current: currentForms,
    } = useTable<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE,
        initialPageSize: DEFAULT_PAGE_SIZE,
        initialCurrent: DEFAULT_PAGE_INDEX,
        sorters: {
            initial: [
                {
                    field: 'createdAt',
                    order: 'desc',
                },
            ],
        },
        filters: {
            initial: watchFormId
                ? [
                      {
                          field: 'defaultValue',
                          operator: 'eq',
                          value: [watchFormId],
                      },
                  ]
                : [],
        },
        syncWithLocation: false,
        hasPagination: true,
        queryOptions: {
            enabled: watchFunctionType === AutomationActionFunctionType.ROLlUP_FORM_TRANSACTIONS,
        },
        meta: {
            searchQuery: {
                isPublishedForm: true,
            },
        },
    });

    const {
        tableQueryResult: targetFormsQueryResult,
        setCurrent: setCurrentTargetForms,
        setFilters: setFiltersTargetForms,
        current: currentTargetForms,
    } = useTable<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE,
        initialPageSize: DEFAULT_PAGE_SIZE,
        initialCurrent: DEFAULT_PAGE_INDEX,
        sorters: {
            initial: [
                {
                    field: 'createdAt',
                    order: 'desc',
                },
            ],
        },
        filters: {
            initial: watchTargetFormId
                ? [
                      {
                          field: 'defaultValue',
                          operator: 'eq',
                          value: [watchTargetFormId],
                      },
                  ]
                : [],
        },
        syncWithLocation: false,
        hasPagination: true,
        queryOptions: {
            enabled: watchFunctionType === AutomationActionFunctionType.ROLlUP_FORM_TRANSACTIONS,
        },
        meta: {
            searchQuery: {
                isPublishedForm: true,
            },
        },
    });

    const onScrollFormLoadMore = (event: any) => {
        const target = event.target;
        if (target?.scrollTop + target?.offsetHeight === target?.scrollHeight) {
            setCurrentForms(currentForms + 1);
        }
    };

    const onScrollTargetFormLoadMore = (event: any) => {
        const target = event.target;
        if (target?.scrollTop + target?.offsetHeight === target?.scrollHeight) {
            setCurrentForms(currentForms + 1);
        }
    };

    useEffect(() => {
        let data: IOption[] =
            formsQueryResult?.data?.data?.map((value) => {
                return {
                    label: value.name,
                    value: value?.id || '',
                };
            }) || [];

        if (currentForms > 1) {
            data = [...forms, ...data];
        }
        setForms([...data]);
    }, [currentForms, formsQueryResult?.data?.data]);

    useEffect(() => {
        let data: IOption[] =
            targetFormsQueryResult?.data?.data?.map((value) => {
                return {
                    label: value.name,
                    value: value?.id || '',
                };
            }) || [];

        if (currentForms > 1) {
            data = [...forms, ...data];
        }
        setTargetForms([...data]);
    }, [currentTargetForms, targetFormsQueryResult?.data?.data]);

    useEffect(() => {
        setCurrentForms(1);
    }, [watchFunctionType]);

    const debouncedSearching = useMemo(() => {
        const onSearch = (value: string) => {
            setForms([]);

            const crudFilters: CrudFilters = [
                {
                    field: 'name',
                    operator: 'contains',
                    value: value,
                },
                {
                    field: 'activeVersionId',
                    operator: 'ne',
                    value: null,
                },
            ];

            setCurrentForms(1);
            setFilters(crudFilters);
        };

        return debounce(onSearch, 300);
    }, [setCurrentForms, setFilters]);

    const debouncedSearchingTargetForm = useMemo(() => {
        const onSearch = (value: string) => {
            setTargetForms([]);

            const crudFilters: CrudFilters = [
                {
                    field: 'name',
                    operator: 'contains',
                    value: value,
                },
                {
                    field: 'activeVersionId',
                    operator: 'ne',
                    value: null,
                },
            ];

            setCurrentTargetForms(1);
            setFiltersTargetForms(crudFilters);
        };

        return debounce(onSearch, 300);
    }, [setCurrentTargetForms, setFiltersTargetForms]);

    const {
        fields: contextMappingFields,
        append: appendContextMappingField,
        remove: removeContextMappingField,
    } = useFieldArray({
        name: 'configuration.contextMappingFields',
    });

    const {
        fields: mappingFields,
        append,
        remove,
    } = useFieldArray({
        name: 'configuration.mappingFields',
    });

    const {
        fields: actionFields,
        append: appendActionField,
        remove: removeActionField,
    } = useFieldArray({
        name: 'configuration.actionFields',
    });

    const { data: sourceFormRespose } = useOne<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE,
        id: watchFormId + '',
        queryOptions: {
            enabled: !!watchFormId,
        },
    });

    const { data: targetFormRespose } = useOne<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE,
        id: watchTargetFormId + '',
        queryOptions: {
            enabled: !!watchTargetFormId,
        },
    });

    useEffect(() => {
        if (sourceFormRespose?.data?.latestFormVersion) setSourceForm(sourceFormRespose?.data?.latestFormVersion);
    }, [sourceFormRespose?.data?.latestFormVersion]);

    useEffect(() => {
        if (targetFormRespose?.data?.latestFormVersion) setTargetForm(targetFormRespose?.data?.latestFormVersion);
    }, [targetFormRespose?.data?.latestFormVersion]);

    useEffect(() => {
        const _currentFields = sourceForm?.fields?.reduce(
            (acc, f) => {
                acc[f.fieldId] = f;
                return acc;
            },
            {} as { [key: string]: Partial<FormFieldType> },
        );

        if (_currentFields) setSourceFields(_currentFields);
    }, [sourceForm]);

    useEffect(() => {
        const _currentFields = targetForm?.fields?.reduce(
            (acc, f) => {
                acc[f.fieldId] = f;
                return acc;
            },
            {} as { [key: string]: Partial<FormFieldType> },
        );

        if (_currentFields) setTargetFields(_currentFields);
    }, [targetForm]);

    const getConditionFields = () => {
        let conFields: ConditionFieldType[] = [];
        const formFields = sourceFields || sourceRegisterFields || [];

        const map = keyBy(formFields as any, 'fieldId');

        conFields = parseToConditionFieldTypes(map)?.filter((item) => !NON_LOGIC_FIELD_TYPES.includes(item.type));

        return conFields;
    };

    // register scope

    const watchRegisterId = useWatch({
        name: 'configuration.registerId',
    });

    const { data: sourceRegisterResponse } = useOne<any>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER,
        id: watchRegisterId + '',
        queryOptions: {
            enabled: !!watchRegisterId,
        },
    });

    const {
        tableQueryResult: registerQueryResult,
        setCurrent: setCurrentRegisters,
        setFilters: setRegisterFilter,
        current: currentRegister,
    } = useTable<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER,
        initialPageSize: DEFAULT_PAGE_SIZE,
        initialCurrent: DEFAULT_PAGE_INDEX,
        sorters: {
            initial: [
                {
                    field: 'createdAt',
                    order: 'desc',
                },
            ],
        },
        filters: {
            initial: watchRegisterId
                ? [
                      {
                          field: 'defaultValue',
                          operator: 'eq',
                          value: [watchRegisterId],
                      },
                  ]
                : [],
        },
        syncWithLocation: false,
        hasPagination: true,
        queryOptions: {
            enabled: watchFunctionType === AutomationActionFunctionType.ROLlUP_FORM_TRANSACTIONS,
        },
        meta: {
            searchQuery: {
                isPublishedForm: true,
            },
        },
    });

    const onScrollRegisterLoadMore = (event: any) => {
        const target = event.target;
        if (target?.scrollTop + target?.offsetHeight === target?.scrollHeight) {
            setCurrentRegisters(currentRegister + 1);
        }
    };

    const debouncedRegisterSearching = useMemo(() => {
        const onSearch = (value: string) => {
            setForms([]);

            const crudFilters: CrudFilters = [
                {
                    field: 'name',
                    operator: 'contains',
                    value: value,
                },
                {
                    field: 'activeVersionId',
                    operator: 'ne',
                    value: null,
                },
            ];

            setCurrentRegisters(1);
            setRegisterFilter(crudFilters);
        };

        return debounce(onSearch, 300);
    }, [setCurrentRegisters, setFilters]);

    useEffect(() => {
        if (sourceRegisterResponse?.data?.latestDataRegisterVersion)
            setSourceRegister(sourceRegisterResponse?.data?.latestDataRegisterVersion);
    }, [sourceRegisterResponse?.data?.latestDataRegisterVersion]);

    useEffect(() => {
        let data: IOption[] =
            registerQueryResult?.data?.data?.map((value) => {
                return {
                    label: value.name,
                    value: value?.id || '',
                };
            }) || [];

        if (currentForms > 1) {
            data = [...forms, ...data];
        }
        setRegisters([...data]);
    }, [currentRegister, registerQueryResult?.data?.data]);

    useEffect(() => {
        const _currentFields = sourceRegister?.fields?.reduce(
            (acc: any, f: any) => {
                acc[f.fieldId] = f;
                return acc;
            },
            {} as { [key: string]: Partial<FormFieldType> },
        );

        if (_currentFields) setSourceRegisterFields(_currentFields);
    }, [sourceRegister]);

    const items: CollapseProps['items'] = [
        {
            key: 'dataConfiguration',
            label: 'Trigger settings',
            children: (
                <>
                    <SelectFormField
                        name={'configuration.sourceType'}
                        label={'Source type'}
                        placeholder={'Select source type'}
                        options={SourceTypeOptions}
                        rules={{
                            required: {
                                value: true,
                                message: translate('create_action.action_create_transaction.formId.require_msg', {
                                    ns: NAMESPACES.AUTOMATION,
                                }),
                            },
                        }}
                    />

                    {watchSourceType === SourceType.FORM && (
                        <SelectFormField
                            name={'configuration.formId'}
                            label={translate('create_action.action_create_transaction.formId.label', { ns: NAMESPACES.AUTOMATION })}
                            placeholder={translate('create_action.action_create_transaction.formId.placeholder', {
                                ns: NAMESPACES.AUTOMATION,
                            })}
                            onPopupScroll={onScrollFormLoadMore}
                            options={forms}
                            loading={formsQueryResult.isLoading}
                            onSearch={debouncedSearching}
                            onChange={() => {
                                setValue('configuration.mappingFields', []);
                            }}
                            rules={{
                                required: {
                                    value: true,
                                    message: translate('create_action.action_create_transaction.formId.require_msg', {
                                        ns: NAMESPACES.AUTOMATION,
                                    }),
                                },
                            }}
                        />
                    )}

                    {watchSourceType === SourceType.FORM && watchFormId && (
                        <>
                            {contextMappingFields.map((item, index) => (
                                <ConditionSettingItem
                                    key={item.id}
                                    index={index}
                                    onRemove={removeContextMappingField}
                                    actionType={'form'}
                                    sourceFields={fields ?? {}}
                                    targetFields={sourceFields ?? {}}
                                    controlName={'configuration.contextMappingFields'}
                                />
                            ))}
                            <Button
                                type="primary"
                                className="block ml-auto"
                                onClick={() => {
                                    appendContextMappingField({});
                                }}
                            >
                                {translate('button.add', { ns: NAMESPACES.COMMON })}
                            </Button>
                            <ConditionFormField
                                containerTitle={translate('builder.rule.conditions', { ns: NAMESPACES.AUTOMATION })}
                                name={conditionsControlName}
                                data={{ fields: getConditionFields() }}
                            />
                        </>
                    )}

                    {watchSourceType === SourceType.REGISTER && (
                        <SelectFormField
                            name={'configuration.registerId'}
                            label={'Select register'}
                            placeholder={'Select register'}
                            onPopupScroll={onScrollRegisterLoadMore}
                            options={register}
                            loading={registerQueryResult.isLoading}
                            onSearch={debouncedRegisterSearching}
                            onChange={() => {
                                setValue('configuration.mappingFields', []);
                            }}
                            rules={{
                                required: {
                                    value: true,
                                    message: translate('create_action.action_create_transaction.formId.require_msg', {
                                        ns: NAMESPACES.AUTOMATION,
                                    }),
                                },
                            }}
                        />
                    )}

                    {watchSourceType === SourceType.REGISTER && watchRegisterId && (
                        <>
                            {contextMappingFields.map((item, index) => (
                                <ConditionSettingItem
                                    key={item.id}
                                    index={index}
                                    onRemove={removeContextMappingField}
                                    actionType={'register'}
                                    sourceFields={fields ?? {}}
                                    targetFields={sourceRegisterFields ?? {}}
                                    controlName={'configuration.contextMappingFields'}
                                />
                            ))}
                            <Button
                                type="primary"
                                className="block ml-auto"
                                onClick={() => {
                                    appendContextMappingField({});
                                }}
                            >
                                {translate('button.add', { ns: NAMESPACES.COMMON })}
                            </Button>
                            <ConditionFormField
                                containerTitle={translate('builder.rule.conditions', { ns: NAMESPACES.AUTOMATION })}
                                name={conditionsControlName}
                                data={{ fields: getConditionFields() }}
                            />
                        </>
                    )}
                </>
            ),
        },
        {
            key: 'conditionConfiguration',
            label: 'Condition settings',
            children: (
                <>
                    {watchSourceType === SourceType.FORM && watchFormId ? (
                        <div className="flex flex-col gap-2">
                            <SelectFormField
                                name={'configuration.targetFormId'}
                                label={translate('create_action.action_create_transaction.formId.label', { ns: NAMESPACES.AUTOMATION })}
                                placeholder={translate('create_action.action_create_transaction.formId.placeholder', {
                                    ns: NAMESPACES.AUTOMATION,
                                })}
                                onPopupScroll={onScrollTargetFormLoadMore}
                                options={targetForms}
                                loading={targetFormsQueryResult.isLoading}
                                onSearch={debouncedSearchingTargetForm}
                                onChange={() => {
                                    setValue('configuration.mappingFields', []);
                                }}
                                rules={{
                                    required: {
                                        value: true,
                                        message: translate('create_action.action_create_transaction.formId.require_msg', {
                                            ns: NAMESPACES.AUTOMATION,
                                        }),
                                    },
                                }}
                            />
                            {mappingFields.map((item, index) => (
                                <ConditionSettingItem
                                    key={item.id}
                                    index={index}
                                    onRemove={remove}
                                    actionType={'form'}
                                    sourceFields={sourceFields ?? {}}
                                    targetFields={targetFields ?? {}}
                                    controlName={'configuration.mappingFields'}
                                />
                            ))}
                            <Button
                                type="primary"
                                className="block ml-auto"
                                onClick={() => {
                                    append({});
                                }}
                            >
                                {translate('button.add', { ns: NAMESPACES.COMMON })}
                            </Button>
                        </div>
                    ) : null}

                    {watchSourceType === SourceType.REGISTER && watchRegisterId ? (
                        <div className="flex flex-col gap-2">
                            <SelectFormField
                                name={'configuration.targetFormId'}
                                label={translate('create_action.action_create_transaction.formId.label', { ns: NAMESPACES.AUTOMATION })}
                                placeholder={translate('create_action.action_create_transaction.formId.placeholder', {
                                    ns: NAMESPACES.AUTOMATION,
                                })}
                                onPopupScroll={onScrollTargetFormLoadMore}
                                options={targetForms}
                                loading={targetFormsQueryResult.isLoading}
                                onSearch={debouncedSearchingTargetForm}
                                onChange={() => {
                                    setValue('configuration.mappingFields', []);
                                }}
                                rules={{
                                    required: {
                                        value: true,
                                        message: translate('create_action.action_create_transaction.formId.require_msg', {
                                            ns: NAMESPACES.AUTOMATION,
                                        }),
                                    },
                                }}
                            />
                            {mappingFields.map((item, index) => (
                                <ConditionSettingItem
                                    key={item.id}
                                    index={index}
                                    onRemove={remove}
                                    actionType={'form'}
                                    sourceFields={sourceRegisterFields ?? {}}
                                    targetFields={targetFields ?? {}}
                                    controlName={'configuration.mappingFields'}
                                />
                            ))}
                            <Button
                                type="primary"
                                className="block ml-auto"
                                onClick={() => {
                                    append({});
                                }}
                            >
                                {translate('button.add', { ns: NAMESPACES.COMMON })}
                            </Button>
                        </div>
                    ) : null}
                </>
            ),
        },
        {
            key: 'actionConfiguration',
            label: 'Action settings',
            children: (
                <>
                    {watchSourceType === SourceType.FORM && watchFormId ? (
                        <>
                            {actionFields.map((item, index) => (
                                <ActionSettingItem
                                    key={item.id}
                                    index={index}
                                    onRemove={removeActionField}
                                    sourceFields={sourceFields ?? {}}
                                    targetFields={targetFields ?? {}}
                                />
                            ))}
                            <Button
                                type="primary"
                                className="block ml-auto"
                                onClick={() => {
                                    appendActionField({});
                                }}
                            >
                                {translate('button.add', { ns: NAMESPACES.COMMON })}
                            </Button>
                        </>
                    ) : null}

                    {watchSourceType === SourceType.REGISTER && watchRegisterId ? (
                        <>
                            {actionFields.map((item, index) => (
                                <ActionSettingItem
                                    key={item.id}
                                    index={index}
                                    onRemove={removeActionField}
                                    sourceFields={sourceRegisterFields ?? {}}
                                    targetFields={sourceFields ?? {}}
                                />
                            ))}
                            <Button
                                type="primary"
                                className="block ml-auto"
                                onClick={() => {
                                    appendActionField({});
                                }}
                            >
                                {translate('button.add', { ns: NAMESPACES.COMMON })}
                            </Button>
                        </>
                    ) : null}
                </>
            ),
        },
    ];

    return <Collapse onChange={(keys) => setActiveKey(typeof keys === 'string' ? [keys] : keys)} activeKey={activeKey} items={items} />;
};
