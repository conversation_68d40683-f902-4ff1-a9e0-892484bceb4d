import { DataProvider, DataSourceTypeEnum } from '@/common/enums';
import { Loading } from '@/components/loading';
import { SaveChangesWarningModal } from '@/components/save-changes-warning-modal/SaveChangesWarningModal';
import { ENDPOINT_RESOURCES, NAMESPACES } from '@/constants';
import { EXTERNAL_DATA_SOURCES } from '@/constants/external';
import { FloatInput, FloatSelect } from '@/form-components';
import { getEnvConfig } from '@/utils';
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useApiUrl, useCustomMutation, useNavigation, useTranslation } from '@refinedev/core';
import dataProvider from '@refinedev/simple-rest';
import { Button, Card, Col, Collapse, Flex, InputNumber, message, Row, Select, Space, Tag, Tree, TreeDataNode } from 'antd';
import { cloneDeep, get, isArray, isBoolean, isEmpty, partition, toNumber } from 'lodash';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import { DocLliPscTreeType, PropertyType, VpqDocumentPathParamType } from './types';
import { PropertyMapping } from './components/PropertyMapping';
import { PropertyTag } from './components/PropertyTag';
import { getPurpleTRACProperties } from './utils/fetchPurpleTRACTree';

const dataLakeProvider = dataProvider(getEnvConfig.DATA_LAKE_API_URL);

export const DataPropertyBuilder: React.FC<{
    mode: string;
}> = ({ mode }) => {
    const contexts = [
        {
            label: 'Vessel',
            value: 'VESSEL',
        },
        {
            label: 'Company',
            value: 'COMPANY',
        },
    ];
    const dataTypes = [
        {
            label: 'Text',
            value: 'STRING',
        },
        {
            label: 'Number',
            value: 'NUMBER',
        },
        {
            label: 'Boolean',
            value: 'BOOLEAN',
        },
        {
            label: 'Date',
            value: 'DATE',
        },
    ];
    const datasourceTypes = [
        {
            value: 'PDF',
            label: 'PDF',
            documentTypes: {
                VESSEL: [
                    { value: EXTERNAL_DATA_SOURCES.PDF_SIRE_CREW, label: 'SIRE Crew Matrix' },
                    { value: EXTERNAL_DATA_SOURCES.PDF_SIRE2_VIQ, label: 'SIRE 2 VIQ' },
                    { value: EXTERNAL_DATA_SOURCES.PDF_OVID_VIQ, label: 'OVID VIQ' },
                    { value: EXTERNAL_DATA_SOURCES.PDF_Q88, label: 'Q88' },
                    {
                        value: EXTERNAL_DATA_SOURCES.PDF_CMID,
                        label: 'CMID',
                    },
                ],
            },
        },
        {
            value: 'OCIMF',
            label: 'OCIMF',
            documentTypes: {
                VESSEL: [
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VPQ, label: 'SIRE VPQ', documentPaths: [] },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_OVID_VPQ, label: 'OVID VPQ', documentPaths: [] },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VIQ, label: 'SIRE VIQ' },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_OVID_VIQ, label: 'OVID VIQ' },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE2_VIQ, label: 'SIRE 2 VIQ' },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_CREW, label: 'SIRE Crew Matrix' },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_OVID_CREW, label: 'OVID Crew Matrix' },
                    {
                        value: EXTERNAL_DATA_SOURCES.OCIMF_OVID_PSC,
                        label: 'OVID PSC',
                    },
                    {
                        value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_PSC,
                        label: 'SIRE PSC',
                    },
                    {
                        value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_INCIDENT,
                        label: 'SIRE Incident',
                    },
                ],
                COMPANY: [
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_OVID_OVMSA, label: 'OVID OVMSA' },
                    { value: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_TMSA, label: 'SIRE TMSA' },
                ],
            },
        },
        {
            value: 'PARIS_MOU',
            label: 'Paris MoU',
            documentTypes: {
                VESSEL: [
                    {
                        value: EXTERNAL_DATA_SOURCES.PARIS_MOU_PSC,
                        label: 'PSC',
                    },
                ],
            },
        },
        {
            value: 'LLI',
            label: 'Lloyd’s List Intelligence',
            documentTypes: {
                VESSEL: [
                    {
                        value: EXTERNAL_DATA_SOURCES.LLI_CASUALTY,
                        label: 'Casualty',
                    },
                    {
                        value: EXTERNAL_DATA_SOURCES.LLI_VESSEL,
                        label: 'Vessel Characteristics',
                    },
                    {
                        value: EXTERNAL_DATA_SOURCES.LLI_PSC,
                        label: 'PSC',
                    },
                ],
            },
        },
        {
            value: 'USCG',
            label: 'US Coast Guard',
            documentTypes: {
                VESSEL: [
                    {
                        value: EXTERNAL_DATA_SOURCES.USCG_PSC,
                        label: 'PSC',
                    },
                ],
            },
        },
        {
            value: 'OFAC',
            label: 'Office of Foreign Assets Control',
            documentTypes: {
                COMPANY: [
                    {
                        value: EXTERNAL_DATA_SOURCES.OFAC_COMPANY_SANCTION,
                        label: 'Sanction',
                    },
                ],
                VESSEL: [
                    {
                        value: EXTERNAL_DATA_SOURCES.OFAC_VESSEL_SANCTION,
                        label: 'Sanction',
                    },
                ],
            },
        },
        {
            value: 'PURPLETRAC',
            label: 'PurpleTRAC',
            documentTypes: {
                VESSEL: [{ value: EXTERNAL_DATA_SOURCES.PURPLETRAC_VESSEL_SCREENING, label: 'PurpleTRAC Vessel Screening' }],
            },
        },
        {
            value: 'Q88',
            label: 'Q88',
            documentTypes: {
                VESSEL: [{ value: EXTERNAL_DATA_SOURCES.Q88_VPQ, label: 'Q88 VPQ' }],
            },
        },
    ];
    const apiUrl = useApiUrl(DataProvider.DataLake);
    const { id } = useParams();

    const navigator = useNavigation();
    const { mutateAsync } = useCustomMutation();

    const [name, setName] = useState<string>();
    const [description, setDescription] = useState<string>();
    const [context, setContext] = useState<string>();
    const [dataType, setDataType] = useState<string>();

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [ocimfVpqTemplate, setOcimfVpqTemplate] = useState<any>({});
    const [docSireCrewTree, setDocSireCrewTree] = useState<any>([]);
    const [docPdfQ88Tree, setDocPdfQ88Tree] = useState<any>([]);
    const [docPdfCmidTree, setDocPdfCmidTree] = useState<any>([]);
    const [docOvidCrewTree, setDocOvidCrewTree] = useState<any>([]);
    const [docSireVIQTree, setDocSireVIQTree] = useState<any>([]);
    const [docVIQ2Tree, setDocVIQ2Tree] = useState<any>([]);
    const [docOvidVIQTree, setDocOvidVIQTree] = useState<any>([]);
    const [docPSCTree, setDocPSCTree] = useState<any>([]);
    const [docParisMouPscTree, setDocParisMouPscTree] = useState<any>([]);
    const [q88Tree, setQ88Tree] = useState<any>([]);

    const [docUscgPscTree, setDocUscgPscTree] = useState<any>([]);
    const [docOfacCompanySanctionTree, setDocOfacCompanySanctionTree] = useState<any>([]);
    const [docOfacVesselSanctionTree, setDocOfacVesselSanctionTree] = useState<any>([]);

    // tree structure for Lli datasource
    const [docLliVesselTree, setDocLliVesselTree] = useState<any>([]);
    const [docLliPscTree, setDocLliPscTree] = useState<DocLliPscTreeType[]>([]);
    const [docLliCasualtyTree, setDocLliCasualtyTree] = useState<any>([]);

    const [docOvidOVMSATree, setDocOvidOVMSATree] = useState<any>([]);
    const [docSireTMSATree, setDocSireTMSATree] = useState<any>([]);
    const [docSireIncidentTree, setDocSireIncidentTree] = useState<any>([]);
    const [purpleTRACTree, setPurpleTRACTree] = useState<any>([]);
    const [isOpenWarning, setIsOpenWarning] = useState<boolean>(false);

    const [parentProp, setParentProp] = useState<PropertyType>({
        id: uuid(),
        name: '',
        description: '',
        type: '',
        calculateType: '',
        dataType: '',
        datasource: '',
        documentType: '',
        documentPath: '',
        documentPathParams: {},
        isParent: true,
        childIds: [],
        dateFormat: null,
        isHeader: null,
        isColumn: null,
        extra: {},
    });

    const [props, setProps] = useState<PropertyType[]>([]);
    const [originData, setOriginData] = useState<any>([]);
    const [messageApi, contextHolder] = message.useMessage();

    const { translate } = useTranslation();

    const fetchOcimfVpqTemplate = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_VPQ_TEMPLATE,
            })
            .then((result: any) => {
                setOcimfVpqTemplate(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch VPQ Template',
                });
            });
    };

    const fetchDocSireCrewTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_SIRE_CREW_TREE,
            })
            .then((result: any) => {
                setDocSireCrewTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Sire Crew Header Tree',
                });
            });
    };

    const fetchDocPdfSireCrewTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_PDF_SIRE_CREW_TREE,
            })
            .then((result: any) => {
                setDocSireCrewTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch PDF Sire Crew Header Tree',
                });
            });
    };

    const fetchDocPdfQ88Tree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_PDF_Q88_TREE,
            })
            .then((result: any) => {
                setDocPdfQ88Tree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch PDF Q88 Tree',
                });
            });
    };

    const fetchDocPdfCmidTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_PDF_CMID_TREE,
            })
            .then((result: any) => {
                setDocPdfCmidTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch PDF CMID Tree',
                });
            });
    };

    const fetchDocOvidCrewTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OVID_CREW_TREE,
            })
            .then((result: any) => {
                setDocOvidCrewTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Ovid Crew Header Tree',
                });
            });
    };

    const fetchDocSireVIQTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_SIRE_VIQ_TREE,
            })
            .then((result: any) => {
                setDocSireVIQTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch VIQ Header Tree',
                });
            });
    };

    const fetchDocOvidVIQTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_OVID_VIQ_TREE,
            })
            .then((result: any) => {
                setDocOvidVIQTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch OVID VIQ Tree',
                });
            });
    };

    const fetchDocPdfOvidVIQTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_OVID_VIQ_TREE,
            })
            .then((result: any) => {
                setDocOvidVIQTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch PDF OVID VIQ Tree',
                });
            });
    };

    const fetchDocVIQ2Tree = () => {
        dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_VIQ2_TREE,
            })
            .then((result: any) => {
                setDocVIQ2Tree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch VIQ2 Header Tree',
                });
            });
    };

    const fetchDocOvidOVMSATree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_OVID_OVMSA_TREE,
            })
            .then((result: any) => {
                setDocOvidOVMSATree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch OVID OVMSA Tree',
                });
            });
    };

    const fetchDocSireTMSATree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_SIRE_TMSA_TREE,
            })
            .then((result: any) => {
                setDocSireTMSATree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch SIRE TMSA Tree',
                });
            });
    };

    const fetchDocLliVesselTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_LLI_VESSEL_TREE,
            })
            .then((result: any) => {
                setDocLliVesselTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch LLI Vessel Tree',
                });
            });
    };

    const fetchDocLliCasualtyTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_LLI_CASUALTY_TREE,
            })
            .then((result: any) => {
                setDocLliCasualtyTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch LLI Casualty Tree',
                });
            });
    };

    const fetchDocUscgPscTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_USCG_PSC_TREE,
            })
            .then((result: any) => {
                setDocUscgPscTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch USCG PSC Tree',
                });
            });
    };

    const fetchDocOfacCompanySanctionTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OFAC_COMPANY_SANCTION_TREE,
            })
            .then((result: any) => {
                setDocOfacCompanySanctionTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Ofac Company Sanction Tree',
                });
            });
    };

    const fetchDocOfacVesselSanctionTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OFAC_VESSEL_SANCTION_TREE,
            })
            .then((result: any) => {
                setDocOfacVesselSanctionTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Ofac Company Sanction Tree',
                });
            });
    };

    const fetchQ88Tree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_Q88_TREE,
            })
            .then((result: any) => {
                setQ88Tree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Q88 Tree',
                });
            });
    };

    const fetchPropertyById = () => {
        return dataLakeProvider
            .getList({
                resource: `${ENDPOINT_RESOURCES.DATA_LAKE_GET_PARENT_PROPERTY}/${id}`,
            })
            .then((result: any) => {
                setParentProp({ ...result.data, childs: undefined });
                setName(result.data.name);
                setContext(result.data.type);
                setProps(result.data.childs);
                setDataType(result.data.dataType);
                setDescription(result.data.description);
                setOriginData(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Property',
                });
            });
    };

    const fetchDocOvidPSCTree = () => {
        dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_PSC_TREE,
            })
            .then((result: any) => {
                setDocPSCTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch OVID PSC Header Tree',
                });
            });
    };

    const fetchDocParisMouPscTree = () => {
        dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_PARIS_MOU_PSC_TREE,
            })
            .then((result: any) => {
                setDocParisMouPscTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Paris Mou PSC Tree',
                });
            });
    };

    const fetchDocSireIncidentTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_OCIMF_SIRE_INCIDENT_TREE,
            })
            .then((result: any) => {
                setDocSireIncidentTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Sire Incident Tree',
                });
            });
    };

    const fetchDocLliPscTree = () => {
        return dataLakeProvider
            .getList({
                resource: ENDPOINT_RESOURCES.DATA_LAKE_GET_LLI_PSC_TREE,
            })
            .then((result: any) => {
                setDocLliPscTree(result.data);
            })
            .catch(() => {
                messageApi.open({
                    type: 'error',
                    content: 'Cannot Fetch Sire Incident Tree',
                });
            });
    };

    const fetchPurpleTRACTree = () => {
        const purpleTRACProperties = getPurpleTRACProperties();
        setPurpleTRACTree(purpleTRACProperties);
    };

    useEffect(() => {
        setIsLoading(true);
        const promisers = [
            fetchOcimfVpqTemplate(),
            fetchDocSireCrewTree(),
            fetchDocPdfSireCrewTree(),
            fetchDocPdfQ88Tree(),
            fetchDocPdfCmidTree(),
            fetchDocOvidCrewTree(),
            fetchDocSireVIQTree(),
            fetchDocOvidVIQTree(),
            fetchDocPdfOvidVIQTree(),
            fetchDocVIQ2Tree(),
            fetchDocOvidPSCTree(),
            fetchDocOvidOVMSATree(),
            fetchDocSireTMSATree(),
            fetchDocSireIncidentTree(),
            fetchDocParisMouPscTree(),
            fetchDocLliVesselTree(),
            fetchDocLliCasualtyTree(),
            fetchDocUscgPscTree(),
            fetchDocOfacCompanySanctionTree(),
            fetchDocLliPscTree(),
            Promise.resolve(fetchPurpleTRACTree()),
            fetchDocOfacVesselSanctionTree(),
            fetchQ88Tree(),
        ];
        if (mode === 'Update' && id) {
            promisers.push(fetchPropertyById());
        }

        Promise.all(promisers)
            .catch((error) => {
                console.error(error);
            })
            .finally(() => {
                setIsLoading(false);
            });
    }, [mode, id]);

    const getVpqTree = (propIndex: number, documentPathParamIndex: number, documentType: string) => {
        if (!isArray(props[propIndex].documentPathParams)) return [];

        const param: VpqDocumentPathParamType = props[propIndex].documentPathParams[documentPathParamIndex];
        const documentPath = param.documentPath;
        if (!documentPath) return [];
        if (!ocimfVpqTemplate?.[documentType]?.[documentPath]) return [];

        const treeData: TreeDataNode[] = [];
        const template = ocimfVpqTemplate[documentType][documentPath];
        const chapters = template.Template.Chapter;

        for (const chapter of chapters) {
            const chapterPath = {
                chapterPath: chapter.num + '',
            };
            const treeItem: any = {
                title: <b>{`${chapter.num}. ${chapter.title}`}</b>,
                key: JSON.stringify(chapterPath),
                children: [],
                selectable: false,
            };
            let sections = chapter.Section;
            if (!isArray(sections)) {
                sections = [sections];
            }
            for (const section of sections) {
                const sectionPath = {
                    ...chapterPath,
                    sectionPath: section.num + '',
                };
                const treeItemSection: any = {
                    title: <b>{`${section.num}. ${section.title}`}</b>,
                    key: JSON.stringify(sectionPath),
                    children: [],
                    selectable: false,
                };
                treeItem.children.push(treeItemSection);

                let questions = section.Question;
                if (!isArray(questions)) {
                    questions = [questions];
                }
                for (const question of questions) {
                    const questionPath = {
                        ...sectionPath,
                        questionPath: question.num + '',
                    };
                    const treeItemQuestion: any = {
                        title: <b>{`${question.num}. ${question.Title}`}</b>,
                        key: JSON.stringify(questionPath),
                        children: [],
                        selectable: false,
                    };
                    treeItemSection.children.push(treeItemQuestion);

                    let controls = question.Control;

                    if (!isArray(controls) && !['FixedGrid', 'VariableGrid'].includes(controls.dataType)) {
                        treeItemQuestion.key = JSON.stringify({
                            ...questionPath,
                            answerPath: controls.num + '',
                        });
                        treeItemQuestion.selectable = true;
                        continue;
                    } else if (!isArray(controls)) {
                        controls = [controls];
                    }
                    for (const control of controls) {
                        const controlPath: any = {
                            ...questionPath,
                            answerPath: control.num + '',
                        };
                        const treeItemAnswer: any = {
                            title: `${['FixedGrid', 'VariableGrid'].includes(control.dataType) ? 'Table' : control.Label && control.Label !== '' ? control.Label : control.num}`,
                            key: JSON.stringify(controlPath),
                            children: [],
                            selectable: !['FixedGrid', 'VariableGrid'].includes(control.dataType),
                        };
                        treeItemQuestion.children.push(treeItemAnswer);

                        if (control.dataType === 'FixedGrid') {
                            // console.log('FixedGrid', controlPath);
                            const [columns, rows] = partition(control.GridItem, (item: any) => item.type === 'Col');
                            for (const column of columns) {
                                const columnPath = {
                                    ...controlPath,
                                    colId: column.id,
                                };
                                const treeItemColumn: any = {
                                    title: `${column.id}. ${column.label}`,
                                    key: JSON.stringify(columnPath),
                                    children: [],
                                    selectable: false,
                                };
                                treeItemAnswer.children.push(treeItemColumn);
                                for (const row of rows) {
                                    const key = JSON.stringify({ ...columnPath, rowId: row.id });
                                    treeItemColumn.children.push({
                                        title: `${row.id}. ${row.label}`,
                                        key,
                                        selectable: true,
                                    });
                                }
                            }
                        } else if (control.dataType === 'VariableGrid') {
                            // console.log('VariableGrid', controlPath);
                            for (const column of control.GridItem) {
                                const key = JSON.stringify({ ...controlPath, colId: column.id });
                                treeItemAnswer.children.push({
                                    title: `${column.id}. ${column.label}`,
                                    key,
                                    selectable: true,
                                });
                            }
                        }
                    }
                }
            }
            treeData.push(treeItem);
        }

        return treeData;
    };

    function formatDataBeforeCompare(data1: any): any[] {
        const formattedData: any[] = [];

        const { childs, ...parent } = data1;
        formattedData.push(parent);

        if (childs && Array.isArray(childs)) {
            formattedData.push(...childs);
        }

        return formattedData;
    }

    function compareData(data1: any, data2: any): boolean {
        const formattedData1 = formatDataBeforeCompare(data1);

        const sortedData1 = formattedData1.sort((a, b) => a.id.localeCompare(b.id));
        const sortedData2 = data2.sort((a: any, b: any) => a.id.localeCompare(b.id));

        return JSON.stringify(sortedData1) === JSON.stringify(sortedData2);
    }

    const submit = async () => {
        if (!name) {
            messageApi.open({
                type: 'error',
                content: 'Please enter data property name',
            });
            return;
        }
        if (!context) {
            messageApi.open({
                type: 'error',
                content: 'Please select context',
            });
            return;
        }
        if (!dataType) {
            messageApi.open({
                type: 'error',
                content: 'Please select data type',
            });
            return;
        }
        const cloneProps = cloneDeep(props);
        for (const cloneProp of cloneProps) {
            if ([EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VPQ, EXTERNAL_DATA_SOURCES.OCIMF_OVID_VPQ].includes(cloneProp.documentType as string)) {
                for (const item of cloneProp.documentPathParams) {
                    if (
                        (item.colId && !item.rowId) ||
                        (!item.colId && item.rowId) ||
                        !item.answerPath ||
                        !item.questionPath ||
                        !item.sectionPath ||
                        !item.chapterPath
                    ) {
                        messageApi.open({
                            type: 'error',
                            content: `Please check incorrect VPQ Template config`,
                        });
                        return;
                    }
                }
            } else if (!cloneProp.documentPath) {
                messageApi.open({
                    type: 'error',
                    content: `Please check incorrect config`,
                });
                return;
            }

            cloneProp.calculateType = 'MAPPING_PATH';
            cloneProp.dataType = dataType;
            cloneProp.description = description || '';
            cloneProp.name = name;
            cloneProp.type = context;
        }

        const cloneParentProp = cloneDeep(parentProp);
        cloneParentProp.calculateType = null;
        cloneParentProp.childIds = props.map((item) => item.id);
        cloneParentProp.dataType = dataType;
        cloneParentProp.datasource = null;
        cloneParentProp.dateFormat = null;
        cloneParentProp.description = description || '';
        cloneParentProp.documentPath = null;
        cloneParentProp.documentPathParams = {};
        cloneParentProp.documentType = null;
        cloneParentProp.isParent = true;
        cloneParentProp.name = name;
        cloneParentProp.type = context;

        try {
            await mutateAsync({
                url: `${apiUrl}/ctx-properties/v1/upsert`,
                method: 'post',
                values: { payload: [cloneParentProp, ...cloneProps] },
                errorNotification: false,
                successNotification: false,
            });
            navigator.goBack();
        } catch (error) {
            messageApi.open({
                type: 'error',
                content: 'Datalake Service Error',
            });
            return;
        }
    };

    const getSelectedFormat = (documentPath: string | null, treeData: any[]): any => {
        if (!documentPath) return {};

        for (const node of treeData) {
            if (node.selectable && node.key === documentPath) {
                return {
                    dateFormat: node?.format?.dateFormat || null,
                };
            }
            if (isArray(node.children)) {
                const result = getSelectedFormat(documentPath, node.children);
                if (!isEmpty(result)) return result;
            }
        }
        return {};
    };
    const getSelectedExtra = (documentPath: string | null, treeData: any[], label: string): any => {
        if (!documentPath) return {};
        for (const node of treeData) {
            if (node.selectable && node.key === documentPath) {
                let prefix = documentPath.split('.')[documentPath.split('.').length - 2];

                // Check if prefix matches an object or array-like pattern
                if (/\{.*\}|\[\]$/.test(prefix)) {
                    prefix = '';
                }

                return { ...node?.extra, mappedFieldTitle: prefix ? `${label}.${prefix}.${node.title}` : `${label}.${node.title}` };
            }
            if (isArray(node.children)) {
                const result = getSelectedExtra(documentPath, node.children, label);
                if (!isEmpty(result)) return result;
            }
        }
        return {};
    };

    const getSelectedIsColumn = (documentPath: string | null, treeData: any[]): any => {
        if (!documentPath) return null;
        for (const node of treeData) {
            if (node.selectable && node.key === documentPath && isBoolean(node?.isColumn)) {
                return node.isColumn;
            }
            if (isArray(node.children)) {
                const result = getSelectedIsColumn(documentPath, node.children);
                if (isBoolean(result)) return result;
            }
        }
        return null;
    };

    const getSelectedIsHeader = (documentPath: string | null, treeData: any[]): any => {
        if (!documentPath) return null;
        for (const node of treeData) {
            if (node.selectable && node.key === documentPath && isBoolean(node?.isHeader)) {
                return node.isHeader;
            }
            if (isArray(node.children)) {
                const result = getSelectedIsHeader(documentPath, node.children);
                if (isBoolean(result)) return result;
            }
        }
        return null;
    };

    const getTitleFromParams = (rawData: any, params: any): string | undefined => {
        const chapter = rawData?.Chapter?.[+params.chapterPath - 1];
        const section = chapter?.Section?.[+params.sectionPath - 1];
        const question = section?.Question?.[+params.questionPath - 1];
        if (!question) return undefined;

        const answer = question?.Control;

        if (params.rowId && params.colId && answer?.dataType === 'FixedGrid') {
            const row = answer.GridItem.find((item: any) => item.type === 'Row' && item.id === params.rowId);
            const col = answer.GridItem.find((item: any) => item.type === 'Col' && item.id === params.colId);

            if (row && col) {
                return `${section?.title}.${question?.Title}.${row.label} `;
            }
        }

        if (Array.isArray(answer)) {
            return `${section?.title}.${question?.Title}.${answer[+params.answerPath - 1]?.Label}`;
        } else if (typeof answer === 'object') {
            return `${section?.title}.${question?.Title}`;
        }

        return '';
    };

    const propAction = {
        addNewProp: () => {
            setProps([
                ...props,
                {
                    id: uuid(),
                    name: '',
                    description: '',
                    type: '',
                    calculateType: '',
                    dataType: '',
                    datasource: '',
                    documentType: null,
                    documentPath: null,
                    documentPathParams: {},
                    isParent: null,
                    childIds: null,
                    dateFormat: null,
                    isHeader: null,
                    isColumn: null,
                    extra: {},
                },
            ]);
        },
        deleteProp: (index: number) => {
            const cloneProps = cloneDeep(props);
            cloneProps?.splice(index, 1);
            setProps(cloneProps);
        },
        setPropDatasource: (index: number, datasource: string) => {
            const cloneProps = cloneDeep(props);
            cloneProps[index].datasource = datasource;
            cloneProps[index].documentType = null;
            cloneProps[index].documentPathParams = {};
            setProps(cloneProps);
        },
        setPropDocumentType: (index: number, documentType: string) => {
            const cloneProps = cloneDeep(props);
            cloneProps[index].documentType = documentType;
            cloneProps[index].documentPath = null;
            cloneProps[index].dateFormat = null;
            cloneProps[index].isHeader = null;
            cloneProps[index].isColumn = null;
            cloneProps[index].extra = {};

            if (
                cloneProps[index].datasource === DataSourceTypeEnum.OCIMF &&
                [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VPQ, EXTERNAL_DATA_SOURCES.OCIMF_OVID_VPQ].includes(documentType)
            ) {
                cloneProps[index].documentPathParams = [];
            } else {
                cloneProps[index].documentPathParams = {};
            }
            setProps(cloneProps);
        },
        setDocumentPath: (
            index: number,
            documentPath: string | null,
            format: { dateFormat?: string },
            extra: any = {},
            isHeader: boolean | null = null,
            isColumn: boolean | null = null,
        ) => {
            const cloneProps = cloneDeep(props);
            cloneProps[index].documentPath = documentPath;
            cloneProps[index].isHeader = isHeader;
            cloneProps[index].isColumn = isColumn;
            cloneProps[index].extra = extra;
            if (format.dateFormat) {
                cloneProps[index].dateFormat = format.dateFormat;
            } else {
                cloneProps[index].dateFormat = null;
            }
            setProps(cloneProps);
        },
        documentPathParam: {
            setDocumentPath: (index: number, documentPathParamIndex: number, documentPath: string) => {
                const cloneProps = cloneDeep(props);
                if (!isArray(cloneProps[index].documentPathParams)) return;
                cloneProps[index].documentPathParams[documentPathParamIndex] = {
                    documentPath,
                };
                setProps(cloneProps);
            },
            deleteDocumentPath: (index: number, documentPathParamIndex: number) => {
                const cloneProps = cloneDeep(props);
                if (!isArray(cloneProps[index].documentPathParams)) return;
                cloneProps[index].documentPathParams?.splice(documentPathParamIndex, 1);
                setProps(cloneProps);
            },
            addDocumentPath: (index: number) => {
                const cloneProps = cloneDeep(props);
                if (!isArray(cloneProps[index].documentPathParams)) return;
                cloneProps[index].documentPathParams.push({
                    documentPath: null,
                    chapterPath: null,
                    sectionPath: null,
                    questionPath: null,
                    answerPath: null,
                    colId: null,
                    rowId: null,
                });
                setProps(cloneProps);
            },
            getSelectedKey: (param: any) => {
                if (!param) return '';
                const key = {
                    chapterPath: param.chapterPath,
                    sectionPath: param.sectionPath,
                    questionPath: param.questionPath,
                    answerPath: param.answerPath,
                };
                if (param?.colId && !param?.rowId?.includes('-')) {
                    return JSON.stringify({
                        ...key,
                        colId: param.colId,
                    });
                } else if (!param?.colId) {
                    return JSON.stringify(key);
                }
                return JSON.stringify({
                    ...key,
                    colId: param.colId,
                    rowId: param.rowId,
                });
            },
            vpqTreeSelect: (index: number, documentPathParamIndex: number, param: VpqDocumentPathParamType, rawData: any) => {
                const mappedFieldTitle = getTitleFromParams(rawData, param);
                const cloneProps = cloneDeep(props);
                if (!isArray(cloneProps[index].documentPathParams)) return;
                cloneProps[index].documentPathParams[documentPathParamIndex] = {
                    ...param,
                    documentPath: cloneProps[index].documentPathParams[documentPathParamIndex].documentPath,
                    mappedFieldTitle: mappedFieldTitle,
                };
                console.log('mappedFieldTitle: ', mappedFieldTitle);

                setProps(cloneProps);
            },
            setVpqRowId: (index: number, documentPathParamIndex: number, rowId: number) => {
                const cloneProps = cloneDeep(props);
                if (!isArray(cloneProps[index].documentPathParams)) return;
                cloneProps[index].documentPathParams[documentPathParamIndex].rowId = rowId + '';
                setProps(cloneProps);
            },
        },
    };

    const renderMappingConfig = ({
        prop,
        propIndex,
        label,
        docTree,
    }: {
        prop: PropertyType;
        propIndex: number;
        label: string;
        docTree: any;
    }) => {
        if (!prop.documentType) return;

        return (
            <Flex vertical={false} className="w-full flex" justify="start" align="center" style={{ marginTop: 10 }}>
                <Card className="w-full" size="small">
                    <PropertyTag propData={prop} style={{ marginLeft: 15, fontSize: '14px', marginBottom: 10 }} />
                    <br></br>
                    <hr className="py-1 px-0" />
                    <Collapse
                        items={[
                            {
                                key: prop.documentType,
                                label: label,
                                children: (
                                    <Tree
                                        defaultExpandedKeys={[prop.documentPath || '']}
                                        defaultSelectedKeys={[prop.documentPath || '']}
                                        showLine={true}
                                        treeData={docTree}
                                        onSelect={([value]) => {
                                            if (typeof value !== 'string') {
                                                // propAction.setDocumentPath(propIndex, null, {});
                                                return;
                                            }
                                            const isColumn = getSelectedIsColumn(value, docTree);
                                            const isHeader = getSelectedIsHeader(value, docTree);
                                            const extra = getSelectedExtra(value, docTree, label);
                                            console.log('extra: ', extra);

                                            propAction.setDocumentPath(
                                                propIndex,
                                                value as string | null,
                                                getSelectedFormat(value, docTree),
                                                extra,
                                                isHeader,
                                                isColumn,
                                            );
                                        }}
                                    />
                                ),
                            },
                        ]}
                        defaultActiveKey={[prop.documentType]}
                    />
                </Card>
            </Flex>
        );
    };

    const onClose = () => {
        const cloneParentProp = cloneDeep(parentProp) as any;
        cloneParentProp.calculateType = null;
        cloneParentProp.childIds = props.map((item) => item.id);
        cloneParentProp.dataType = dataType;
        cloneParentProp.datasource = null;
        cloneParentProp.dateFormat = null;
        cloneParentProp.description = description || '';
        cloneParentProp.documentPath = null;
        cloneParentProp.documentPathParams = {};
        cloneParentProp.documentType = null;
        cloneParentProp.isParent = true;
        cloneParentProp.name = name;
        cloneParentProp.type = context;

        const cloneProps = cloneDeep(props) as any;

        for (const cloneProp of cloneProps) {
            cloneProp.calculateType = 'MAPPING_PATH';
            cloneProp.dataType = dataType;
            cloneProp.description = description || '';
            cloneProp.name = name;
            cloneProp.type = context;
        }

        const payload = [cloneParentProp, ...cloneProps];
        const isCompare = compareData(originData, payload);

        if (!isCompare) {
            setIsOpenWarning(true);
        } else {
            window.history.back();
        }
    };

    const handleBack = () => {
        setIsOpenWarning(false);
        window.history.back();
    };

    const onCancel = () => {
        setIsOpenWarning(false);
    };

    const onSaveAndExit = () => {
        submit();
    };

    return isLoading ? (
        <div className="w-full h-screen">
            <Loading />
        </div>
    ) : (
        <>
            {contextHolder}

            <div className="w-full flex flex-col gap-2">
                <Flex vertical={false} className="w-full flex gap-2" justify="space-between" align="flex-end">
                    <h2 className="font-semibold">{mode} Data Property</h2>
                    <div className="flex w-1/5 flex-end space-x-1 justify-end">
                        <Button onClick={onClose} size="middle" type="default">
                            {translate('button.close')}
                        </Button>
                        <Button onClick={submit} size="middle" type="primary" htmlType="submit">
                            {translate('button.save')}
                        </Button>
                    </div>
                </Flex>
                <hr className="py-1 px-0" />

                <Row gutter={16}>
                    <Col className="gutter-row" span={6}>
                        <FloatInput
                            label={translate('field.data_property_name', { ns: NAMESPACES.DATA_PROPERTY })}
                            placeholder={translate('field.data_property_name_placeholder', { ns: NAMESPACES.DATA_PROPERTY })}
                            onChange={(event) => {
                                setName(event.target.value);
                            }}
                            value={name}
                        />
                    </Col>
                    <Col className="gutter-row" span={6}>
                        <FloatSelect
                            onSelect={setContext}
                            label={translate('field.context', { ns: NAMESPACES.DATA_PROPERTY })}
                            placeholder={translate('field.context_place_holder', { ns: NAMESPACES.DATA_PROPERTY })}
                            options={contexts}
                            disabled={!!props?.length}
                            value={context}
                        />
                    </Col>
                    <Col className="gutter-row" span={6}>
                        <FloatSelect
                            label={translate('field.data_type', { ns: NAMESPACES.DATA_PROPERTY })}
                            placeholder={translate('field.data_type_place_holder', { ns: NAMESPACES.DATA_PROPERTY })}
                            options={dataTypes}
                            onSelect={setDataType}
                            disabled={!!props?.length}
                            value={dataType}
                        />
                    </Col>
                    <Col className="gutter-row" span={6}>
                        <FloatInput
                            label={translate('field.description', { ns: NAMESPACES.DATA_PROPERTY })}
                            placeholder={translate('field.description_placeholder', { ns: NAMESPACES.DATA_PROPERTY })}
                            onChange={(event) => {
                                setDescription(event.target.value);
                            }}
                            value={description}
                        />
                    </Col>
                </Row>
            </div>
            <br />
            <h2 className="font-semibold">Mapping </h2>

            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                {context &&
                    dataType &&
                    props.map((prop: PropertyType, propIndex: number) => (
                        <>
                            <Flex className="w-full flex gap-2">
                                <Card className="w-full" size="small">
                                    <Flex>
                                        <Select
                                            placeholder="Datasource Type"
                                            style={{ width: 150 }}
                                            onChange={(value) => {
                                                propAction.setPropDatasource(propIndex, value);
                                            }}
                                            options={datasourceTypes}
                                            value={prop.datasource}
                                            showSearch
                                            filterOption={(input, option: any) =>
                                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                            }
                                        />

                                        {prop.datasource && (
                                            <Select
                                                placeholder="Document"
                                                value={prop.documentType}
                                                onChange={(value) => {
                                                    propAction.setPropDocumentType(propIndex, value);
                                                }}
                                                style={{ width: 250, marginLeft: 15 }}
                                                options={get(
                                                    datasourceTypes.find((item) => item.value === prop.datasource),
                                                    `documentTypes.${context}`,
                                                    [],
                                                )}
                                                showSearch
                                                filterOption={(input, option: any) =>
                                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                }
                                            />
                                        )}
                                    </Flex>
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VPQ, EXTERNAL_DATA_SOURCES.OCIMF_OVID_VPQ].includes(
                                            prop.documentType,
                                        ) &&
                                        isArray(prop.documentPathParams) &&
                                        prop.documentPathParams.map((param: VpqDocumentPathParamType, documentPathParamIndex: number) => (
                                            <Flex
                                                vertical={false}
                                                className="w-full flex"
                                                justify="start"
                                                align="center"
                                                style={{ marginTop: 10 }}
                                            >
                                                <Card className="w-full" size="small">
                                                    <Flex vertical={false} className="w-full flex" justify="space-between" align="flex-end">
                                                        <div>
                                                            <Select
                                                                placeholder="Document Template"
                                                                value={param.documentPath}
                                                                onChange={(value) => {
                                                                    propAction.documentPathParam.setDocumentPath(
                                                                        propIndex,
                                                                        documentPathParamIndex,
                                                                        value,
                                                                    );
                                                                }}
                                                                style={{ width: 300 }}
                                                                options={Object.keys(ocimfVpqTemplate?.[prop.documentType as string])?.map(
                                                                    (code: any) => ({
                                                                        label: ocimfVpqTemplate?.[prop.documentType as string]?.[code]
                                                                            ?.Template?.name,
                                                                        value: code,
                                                                    }),
                                                                )}
                                                            />

                                                            {param.chapterPath && (
                                                                <>
                                                                    <Tag
                                                                        style={{ marginLeft: 15, fontSize: '14px' }}
                                                                        color={
                                                                            param.chapterPath &&
                                                                            param.sectionPath &&
                                                                            param.questionPath &&
                                                                            param.answerPath &&
                                                                            ((param.rowId && param.colId) || (!param.rowId && !param.colId)
                                                                                ? true
                                                                                : false)
                                                                                ? 'success'
                                                                                : 'error'
                                                                        }
                                                                    >
                                                                        {`${param.chapterPath}.${param.sectionPath || ''}.${param.questionPath || ''}.${param.answerPath || ''}`}{' '}
                                                                        {param.rowId || param.colId
                                                                            ? `- Column: ${param.colId || ''} - Row: ${param.rowId || ''}`
                                                                            : ''}
                                                                    </Tag>
                                                                    {param.colId && !param.rowId?.includes('-') && (
                                                                        <InputNumber
                                                                            placeholder="Row number"
                                                                            min={1}
                                                                            value={
                                                                                typeof param.rowId !== 'string'
                                                                                    ? null
                                                                                    : toNumber(param.rowId)
                                                                            }
                                                                            onChange={(value) => {
                                                                                if (typeof value !== 'number' || value <= 0) return;
                                                                                propAction.documentPathParam.setVpqRowId(
                                                                                    propIndex,
                                                                                    documentPathParamIndex,
                                                                                    value,
                                                                                );
                                                                            }}
                                                                        />
                                                                    )}
                                                                </>
                                                            )}
                                                        </div>
                                                        <Button
                                                            danger
                                                            onClick={() => {
                                                                propAction.documentPathParam.deleteDocumentPath(
                                                                    propIndex,
                                                                    documentPathParamIndex,
                                                                );
                                                            }}
                                                            icon={<DeleteOutlined />}
                                                        />
                                                    </Flex>
                                                    {param.documentPath && (
                                                        <>
                                                            <br></br>
                                                            <hr className="py-1 px-0" />
                                                            <Collapse
                                                                items={[
                                                                    {
                                                                        key: documentPathParamIndex,
                                                                        label: `${ocimfVpqTemplate?.[prop.documentType as string]?.[param.documentPath]?.Template?.name} Template`,
                                                                        children: (
                                                                            <Tree
                                                                                defaultExpandedKeys={[
                                                                                    propAction.documentPathParam.getSelectedKey(param),
                                                                                ]}
                                                                                defaultSelectedKeys={[
                                                                                    propAction.documentPathParam.getSelectedKey(param),
                                                                                ]}
                                                                                showLine={true}
                                                                                treeData={getVpqTree(
                                                                                    propIndex,
                                                                                    documentPathParamIndex,
                                                                                    prop.documentType as string,
                                                                                )}
                                                                                onSelect={([value]) => {
                                                                                    propAction.documentPathParam.vpqTreeSelect(
                                                                                        propIndex,
                                                                                        documentPathParamIndex,
                                                                                        typeof value === 'string' ? JSON.parse(value) : {},
                                                                                        {
                                                                                            ...(param.documentPath
                                                                                                ? ocimfVpqTemplate?.[
                                                                                                      prop.documentType as string
                                                                                                  ]?.[param.documentPath]?.Template
                                                                                                : {}),
                                                                                        },
                                                                                    );
                                                                                }}
                                                                            />
                                                                        ),
                                                                    },
                                                                ]}
                                                                defaultActiveKey={[documentPathParamIndex]}
                                                            />
                                                        </>
                                                    )}
                                                </Card>
                                            </Flex>
                                        ))}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VPQ, EXTERNAL_DATA_SOURCES.OCIMF_OVID_VPQ].includes(
                                            prop.documentType,
                                        ) && (
                                            <Flex
                                                vertical={false}
                                                className="w-full flex"
                                                justify="center"
                                                align="center"
                                                style={{ marginTop: 10 }}
                                            >
                                                <Button
                                                    className="gap-2"
                                                    size="middle"
                                                    onClick={() => {
                                                        propAction.documentPathParam.addDocumentPath(propIndex);
                                                    }}
                                                >
                                                    Add Template
                                                </Button>
                                            </Flex>
                                        )}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_CREW].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docSireCrewTree,
                                            label: 'Crew',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'PDF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.PDF_SIRE_CREW].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docSireCrewTree,
                                            label: 'Crew',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'PDF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.PDF_Q88].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docPdfQ88Tree,
                                            label: 'Q88',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'PDF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.PDF_CMID].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docPdfCmidTree,
                                            label: 'CMID',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_OVID_CREW].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docOvidCrewTree,
                                            label: 'Crew',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VIQ].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docSireVIQTree,
                                            label: 'VIQ',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_OVID_VIQ].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docOvidVIQTree,
                                            label: 'VIQ',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'PDF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.PDF_OVID_VIQ].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docOvidVIQTree,
                                            label: 'VIQ',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'PDF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.PDF_SIRE2_VIQ].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docVIQ2Tree,
                                            label: 'VIQ',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE2_VIQ].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docVIQ2Tree,
                                            label: 'VIQ',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_OVID_PSC].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docPSCTree,
                                            label: 'PSC',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_PSC].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docPSCTree,
                                            label: 'PSC',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_INCIDENT].includes(prop.documentType) &&
                                        renderMappingConfig({
                                            docTree: docSireIncidentTree,
                                            label: 'Sire Incident',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.OCIMF_OVID_OVMSA &&
                                        renderMappingConfig({
                                            docTree: docOvidOVMSATree,
                                            label: 'OVMSA',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OCIMF' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.OCIMF_SIRE_TMSA &&
                                        renderMappingConfig({
                                            docTree: docSireTMSATree,
                                            label: 'TMSA',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'PARIS_MOU' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.PARIS_MOU_PSC &&
                                        renderMappingConfig({
                                            docTree: docParisMouPscTree,
                                            label: 'PSC',
                                            prop,
                                            propIndex,
                                        })}

                                    {prop.datasource === 'LLI' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.LLI_VESSEL &&
                                        renderMappingConfig({
                                            docTree: docLliVesselTree,
                                            label: 'Vessel Characteristics',
                                            prop,
                                            propIndex,
                                        })}

                                    {prop.datasource === 'LLI' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.LLI_CASUALTY &&
                                        renderMappingConfig({
                                            docTree: docLliCasualtyTree,
                                            label: 'Casualty',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'LLI' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.LLI_PSC && (
                                            <PropertyMapping
                                                property={prop}
                                                index={propIndex}
                                                label={'PSC'}
                                                docTree={docLliPscTree}
                                                setDocumentPath={propAction.setDocumentPath}
                                            />
                                        )}
                                    {prop.datasource === 'USCG' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.USCG_PSC &&
                                        renderMappingConfig({
                                            docTree: docUscgPscTree,
                                            label: 'PSC',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OFAC' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.OFAC_COMPANY_SANCTION &&
                                        renderMappingConfig({
                                            docTree: docOfacCompanySanctionTree,
                                            label: 'Sanction',
                                            prop,
                                            propIndex,
                                        })}
                                    {/* PurpleTRAC */}
                                    {prop.datasource === 'PURPLETRAC' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.PURPLETRAC_VESSEL_SCREENING &&
                                        renderMappingConfig({
                                            docTree: purpleTRACTree,
                                            label: 'PurpleTRAC',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'OFAC' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.OFAC_VESSEL_SANCTION &&
                                        renderMappingConfig({
                                            docTree: docOfacVesselSanctionTree,
                                            label: 'Sanction',
                                            prop,
                                            propIndex,
                                        })}
                                    {prop.datasource === 'Q88' &&
                                        prop.documentType !== null &&
                                        prop.documentType === EXTERNAL_DATA_SOURCES.Q88_VPQ &&
                                        renderMappingConfig({
                                            docTree: q88Tree,
                                            label: 'Q88',
                                            prop,
                                            propIndex,
                                        })}
                                </Card>
                                <Button
                                    danger
                                    onClick={() => {
                                        propAction.deleteProp(propIndex);
                                    }}
                                    icon={<DeleteOutlined />}
                                ></Button>
                            </Flex>
                        </>
                    ))}
            </Space>

            {context && dataType && (
                <Flex vertical={false} className="w-full flex gap-2" justify="center" align="center" style={{ marginTop: 10 }}>
                    <Button icon={<PlusCircleOutlined />} className="lg:w-1/12 gap-2" size="middle" onClick={propAction.addNewProp} />
                </Flex>
            )}

            <SaveChangesWarningModal onBack={handleBack} isOpen={isOpenWarning} onCancel={onCancel} onSave={onSaveAndExit} />
        </>
    );
};
