import { DataProvider, FieldTypeEnum } from '@/common/enums';
import { Icon } from '@/components/icon';
import { ENDPOINT_RESOURCES, EXTERNAL_ORIGINAL_VERSION_ID, NAMESPACES, PREFIX } from '@/constants';
import { SearchOutlined } from '@/constants/icons';
import { NDataRegisterTransaction, NUser } from '@/interfaces';
import { WidgetContextTypes, WidgetExternalDataSource, WidgetMetaDataColumnTypeEnum } from '@/modules/widget/constant';

import { useEmptyTransaction } from '@/common/hooks/useEmptyTransaction';
import { dataProvider } from '@/dataProvider';
import { useFormRendererStore } from '@/stores';
import { DrawerType, useDrawerStore } from '@/stores/useDrawerStore';
import { getEnvConfig, parseFieldStyle } from '@/utils';
import { queryClient } from '@/utils/query';
import { useApiUrl, useCustomMutation, useGetIdentity, useTranslation, useUpdate } from '@refinedev/core';
import { Pagination } from '@refinedev/core/dist/contexts/data/types';
import { ConfigProvider, Input, InputRef, notification, Popconfirm, Table, TableColumnType, TablePaginationConfig } from 'antd';
import { FilterDropdownProps, FilterValue } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { get, isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import useDeepCompareEffect from 'use-deep-compare-effect';

// const SOURCE_TYPE_TO_SOURCE_TREE = {
//     [WidgetExternalDataSource.MDS_CATALOG_PSC]: [EXTERNAL_DATA_SOURCES.PARIS_MOU_PSC, EXTERNAL_DATA_SOURCES.USCG_PSC],
//     [WidgetExternalDataSource.LLI]: [EXTERNAL_DATA_SOURCES.LLI_PSC],
//     [WidgetExternalDataSource.OCIMF]: [EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VIQ, EXTERNAL_DATA_SOURCES.OCIMF_OVID_VIQ, , EXTERNAL_DATA_SOURCES.OCIMF_SIRE_PSC, EXTERNAL_DATA_SOURCES.OCIMF_OVID_PSC],
// }

import { WidgetActionEnum } from '../../utils';
import { IWidgetRenderer } from '../summary-widget/WidgetSummaryRenderer/WidgetSummaryRenderer';
import { EXTERNAL_DATA_SOURCES } from '@/constants/external';
interface IWidgetAction {
    targetAction: string;
    targetSource: string;
    targetField: string;
    targetLookupContext: string;
}
const DEFAULT_ITEMS_PER_SOURCE = 10;

const widthConfigMapper = ({ unit, value }: { unit: string; value: number }) => {
    switch (unit) {
        case 'auto':
            return null;
        case 'percent':
            return `${value}%`;
        case 'fixed':
            return `${value}px`;
    }
};

const renderFieldColumn = (field: NDataRegisterTransaction.IDataRegisterTransactionField) => {
    if (typeof field === 'string' || typeof field === 'number' || typeof field === 'boolean') return <span>{String(field)}</span>;
    const style = parseFieldStyle(field?.style ?? {});
    const value = field?.fieldValue?.toString();
    const colorStyle = { color: field?.style?.color, backgroundColor: field?.style?.backgroundColor };
    return (
        <span className="flex gap-2 items-center" title={value ?? ''}>
            {style?.icon ?? ''}
            <span style={colorStyle} className="truncate block">
                {value}
            </span>
        </span>
    );
};

function WidgetExternalRenderer({ widget, contextValues, entityId, roleId, styles, isPreview, isTesting }: IWidgetRenderer) {
    const { create: createEmptyTransaction } = useEmptyTransaction();
    const { mutateAsync: update, isLoading: isUpdatingTran } = useUpdate();
    const { setUserRoles } = useFormRendererStore();
    const [shouldRefresh, setShouldRefresh] = useState(false);
    const { translate } = useTranslation();
    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();

    const activeWidget = isTesting ? widget : widget.activeVersion?.data;
    const contexts = activeWidget?.contexts || [];
    const widgetConfig = activeWidget?.config;
    const { setDrawer } = useDrawerStore();

    const { mutateAsync } = useCustomMutation();

    const apiUrl = useApiUrl(DataProvider.Application);
    const apiDatalakeUrl = useApiUrl(DataProvider.DataLake);
    const [columns, setColumns] = useState<any[]>([]);
    const [dataSource, setDataSource] = useState([]);
    const [pagination, setPagination] = useState<Pagination>(widgetConfig?.pagination || { current: 1, pageSize: 5 });
    const [search, setSearch] = useState<Record<string, FilterValue | null>>({});
    const [columnsConfig, setColumnsConfig] = useState<any[]>(widgetConfig?.columns ?? []);
    const [total, setTotal] = useState<number>(0);
    const [allData, setAllData] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [externalAndRecordIdPair, setExternalAndRecordIdPair] = useState<{ [key: string]: string }>({});

    const searchInput = useRef<InputRef>(null);

    const searchIcon = (filtered: boolean) => <Icon icon={SearchOutlined} style={{ color: filtered ? '#1677ff' : undefined }} />;
    entityId = Array.isArray(entityId) ? entityId.filter(Boolean) : entityId ? [entityId] : [];

    useEffect(() => {
        identity?.roles && setUserRoles(identity?.roles);
    }, [identity?.roles, setUserRoles]);

    const getColumnSearchProps = (key: string): TableColumnType<any> => {
        const column = columnsConfig?.find((col: any) => col.key === key);

        return column?.enableSearch
            ? {
                  filterDropdown: ({
                      setSelectedKeys,
                      confirm,
                  }: {
                      setSelectedKeys: any;
                      selectedKeys: React.Key[];
                      confirm: FilterDropdownProps['confirm'];
                  }) => (
                      <div className="p-2" onKeyDown={(e) => e.stopPropagation()}>
                          <Input
                              allowClear
                              ref={searchInput}
                              placeholder={'Search'}
                              onChange={(e) => setSelectedKeys(e.target.value ? e.target.value : null)}
                              onPressEnter={(e) => confirm()}
                          />
                      </div>
                  ),
                  filterIcon: searchIcon,
                  onFilterDropdownOpenChange: (visible) => {
                      if (visible) {
                          setTimeout(() => searchInput.current?.select(), 100);
                      }
                  },
              }
            : {};
    };

    const mapDataSourceToTable = async (dataSource: any, pathsMap: any, action: IWidgetAction) => {
        const newData = [];
        if (isEmpty(dataSource.data)) return [];

        for (const row of dataSource.data) {
            try {
                if (!row) continue;
                const newRow: any = {
                    key: row?.id,
                    id: row?.id,
                    type_: dataSource.sourceType,
                };

                for (const k in pathsMap) {
                    const val = get(row, pathsMap[k]);
                    if (val !== undefined) {
                        newRow[k] = val;
                    } else if (row[k]) {
                        newRow[k] = row[k];
                    } else if (k === WidgetMetaDataColumnTypeEnum.Action) {
                        if (action?.targetAction && action.targetAction !== WidgetActionEnum.NONE) {
                            const rowOriginVersionId = row?.originVersionId;
                            const availableTran = await getActionRelatedForm({
                                fieldId: EXTERNAL_ORIGINAL_VERSION_ID,
                                formId: action?.targetSource,
                                originalDocVersionId: rowOriginVersionId,
                            });

                            const availableTranId = availableTran?.id;
                            if (availableTranId) {
                                newRow[k] = (
                                    <button
                                        className="min-w-[100px] text-center bg-blue-500 text-white px-4 py-1 rounded hover:bg-blue-600 transition-colors"
                                        onClick={() => {
                                            setDrawer(DrawerType.FormTransaction, {
                                                formId: action?.targetSource,
                                                recordId: availableTran?.id as string,
                                            });
                                        }}
                                    >
                                        View Transaction
                                    </button>
                                );
                            } else {
                                const isNoContext = contexts?.includes(WidgetContextTypes.No_Context);
                                const isNotExistInRegister = !externalAndRecordIdPair[row?.id];
                                const isDisable = isNoContext && isNotExistInRegister;
                                const createButton = (
                                    <button
                                        className={`min-w-[100px] text-center px-4 py-1 rounded transition-colors ${
                                            isDisable
                                                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                                : 'bg-blue-500 text-white hover:bg-blue-600'
                                        }`}
                                        disabled={isDisable}
                                        data-tooltip={isDisable ? 'Required to add the vessel into Register' : ''}
                                        title={isDisable ? 'Required to add the vessel into Register' : ''}
                                        onClick={async () => {
                                            await createAndUpdateFormTransaction({
                                                formId: action?.targetSource,
                                                targetLookupContextField: action?.targetLookupContext,
                                                altContextValue: externalAndRecordIdPair[row?.id],
                                                documentType: row?.source,
                                                originalVersionId: rowOriginVersionId,
                                            });
                                        }}
                                    >
                                        Create Transaction
                                    </button>
                                );
                                const purchaseButton = (
                                    <div>
                                        <Popconfirm
                                            title={`Purchase VIQ Report`}
                                            description={`Are you sure to purchase ${row.viqKey} ?`}
                                            onConfirm={() => purchaseOcimfViqReport(row, action, rowOriginVersionId)}
                                            okText="Yes"
                                            cancelText="No"
                                        >
                                            <button
                                                className={`min-w-[100px] text-center px-4 py-1 rounded transition-colors ${
                                                    isDisable
                                                        ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                                        : 'bg-red-500 text-white hover:bg-red-600'
                                                }`}
                                                disabled={isDisable}
                                                data-tooltip={isDisable ? 'Required to add the vessel into Register' : ''}
                                                title={isDisable ? 'Required to add the vessel into Register' : ''}
                                            >
                                                Purchase Report
                                            </button>
                                        </Popconfirm>
                                    </div>
                                );

                                if ([EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VIQ, EXTERNAL_DATA_SOURCES.OCIMF_OVID_VIQ].includes(row.source)) {
                                    newRow[k] = row.isPurchased ? createButton : purchaseButton;
                                } else {
                                    newRow[k] = createButton;
                                }
                            }
                        }
                    }
                }
                newData.push(newRow);
            } catch (error) {
                console.error(error);
            }
        }
        return newData;
    };

    const getExternalDataFilter = (dataSourceType: string) => {
        const contextsConfig = widgetConfig?.contexts;
        const contextFilter: { field: any; operator: string; value: string | undefined }[] = [];
        if (Array.isArray(contexts) && contextsConfig && Object.keys(contextsConfig).length) {
            contexts.forEach((context, index) => {
                const fieldId = contextsConfig?.[context]?.[dataSourceType];
                const value = !isEmpty(entityId)
                    ? Array.isArray(entityId)
                        ? entityId[index]
                        : entityId
                    : Array.isArray(contextValues)
                      ? contextValues[index]
                      : contextValues;

                if (fieldId && value) {
                    contextFilter.push({
                        field: fieldId,
                        operator: 'eq',
                        value: value,
                    });
                }
            });
        }

        return {
            contextFilter,
        };
    };
    const isSingleDataSource = (dataSourceConfig: Record<string, any>) => {
        const sourceKeys = Object.keys(dataSourceConfig);
        // Check if there's only one source key and it has exactly one value
        return sourceKeys.length === 1 && dataSourceConfig[sourceKeys[0]].length === 1;
    };

    const getExternalData = async (dataSourceType: WidgetExternalDataSource) => {
        const { contextFilter } = getExternalDataFilter(dataSourceType);
        const dataSourceConfig = widgetConfig?.dataSourceConfig?.[dataSourceType] || [];

        if (isEmpty(dataSourceConfig)) {
            return {
                sourceType: dataSourceType,
                source: dataSourceType,
                data: [],
                total: 0,
            };
        }

        // Get sortable columns and their mappings
        const sortableColumns = widgetConfig?.columns?.filter((col: any) => col.enableSort) || [];

        let allData: any[] = [];
        let totalCount = 0;

        // Process each data source configuration
        for (const source of dataSourceConfig) {
            // Generate sort config specific to the current source
            const sortConfig = sortableColumns
                .map((col: any) => {
                    const mappings = col.dataMapping || {};
                    // Get the field path specific to the current source
                    const fieldPath = mappings[source];
                    if (!fieldPath) return null;

                    return {
                        field: fieldPath,
                        order: col.sortOrder === 'asc' ? 'asc' : 'desc',
                    };
                })
                .filter((sort: any) => sort !== null);

            const dataSourceResponse = await mutateAsync({
                url: `${apiDatalakeUrl}/${ENDPOINT_RESOURCES.DATA_LAKE_GET_EXTERNAL_DATA}`,
                method: 'post',
                values: {
                    documentType: dataSourceType,
                    filters: [
                        ...contextFilter,
                        {
                            field: 'source',
                            operator: 'eq',
                            value: source,
                        },
                    ],
                    pageIndex: isSingleDataSource(widgetConfig?.dataSourceConfig ?? {}) ? pagination.current ?? 1 : 1,
                    pageSize: isSingleDataSource(widgetConfig?.dataSourceConfig ?? {})
                        ? pagination?.pageSize
                        : widgetConfig?.filter?.[source]?.itemsPerSource || DEFAULT_ITEMS_PER_SOURCE,
                    sorts: sortConfig,
                },
            });

            if (dataSourceResponse?.data?.data) {
                allData = [...allData, ...dataSourceResponse.data.data];
                totalCount = isSingleDataSource(widgetConfig?.dataSourceConfig ?? {}) ? dataSourceResponse?.data?.total : allData.length;
            }
        }

        return {
            sourceType: dataSourceType,
            source: dataSourceType,
            data: allData,
            total: totalCount,
        };
    };

    const updateTransactionAfterCreated = async ({
        formId,
        originalVersionId,
        contextValue,
        documentType,
        formVersionId,
        targetLookupContextField,
        transactionId,
    }: {
        formId: string;
        originalVersionId: string;
        transactionId: string;
        formVersionId: string;
        documentType: string;
        targetLookupContextField: string;
        contextValue?: string;
    }) => {
        // const transactionId = data?.data?.data?.id;
        // const formVersionId = data?.data?.data?.formVersionId;
        if (!isEmpty(contextValue) && targetLookupContextField && roleId) {
            await update(
                {
                    dataProviderName: DataProvider.Application,
                    resource: ENDPOINT_RESOURCES.FORM_TRANSACTION,
                    id: transactionId,
                    values: {
                        transactionId,
                        formId,
                        formVersionId,
                        transactionFields: [
                            {
                                fieldId: targetLookupContextField,
                                fieldValue: contextValue,
                                fieldType: FieldTypeEnum.Lookup,
                                contextType: 'form',
                                fieldOptionIds: [contextValue],
                            },
                        ],
                        payloadDocuments: {
                            [documentType]: originalVersionId,
                        },
                        roleId,
                        formValues: {
                            [targetLookupContextField]: contextValue,
                        },
                    },
                    successNotification: false,
                    errorNotification: false,
                },
                {
                    onError: (error) => {
                        notification.success({
                            message: translate('notifications.transaction_update_fail', { ns: NAMESPACES.FORM }),
                            type: 'error',
                        });
                    },
                    onSuccess: async () => {
                        notification.success({
                            message: translate('notifications.transaction_update_processing', { ns: NAMESPACES.FORM }),
                            type: 'success',
                        });
                    },
                },
            );
        }
    };

    const purchaseOcimfViqReport = async (row: any, action: IWidgetAction, rowOriginVersionId: string) => {
        setLoading(true);

        await mutateAsync(
            {
                url: `${apiDatalakeUrl}/${ENDPOINT_RESOURCES.DATA_LAKE_PURCHASE_OCIMF_VIQ}`,
                method: 'post',
                values: {
                    sourceId: row.sourceId,
                },
                errorNotification: false,
                successNotification: false,
            },
            {
                onError: (error) => {
                    notification.success({
                        message: translate('notifications.purchase_ocimf_viq_fail', { ns: NAMESPACES.FORM }),
                        type: 'error',
                    });
                    setLoading(false);
                },
                onSuccess: async (data) => {
                    notification.success({
                        message: translate('notifications.purchase_ocimf_viq_success', { ns: NAMESPACES.FORM }),
                        type: 'success',
                    });
                    await createAndUpdateFormTransaction({
                        formId: action?.targetSource,
                        targetLookupContextField: action?.targetLookupContext,
                        altContextValue: externalAndRecordIdPair[row?.id],
                        documentType: row?.source,
                        originalVersionId: rowOriginVersionId,
                    });

                    if (data.data) {
                        setTimeout(() => {
                            fetchData();
                            setShouldRefresh((prev) => !prev);
                            setLoading(false);
                        }, 9000);
                    }
                },
            },
        );
    };

    const createAndUpdateFormTransaction = async ({
        formId,
        targetLookupContextField,
        altContextValue,
        documentType,
        originalVersionId,
    }: {
        formId: string;
        targetLookupContextField: string;
        altContextValue: string;
        documentType: string;
        originalVersionId: string;
    }) => {
        setLoading(true);
        let contextValue = Array.isArray(contextValues) ? contextValues[0] : contextValues;
        contextValue = contextValue ?? altContextValue;
        const timezone = dayjs.tz.guess();
        await mutateAsync(
            {
                url: `${apiUrl}/${ENDPOINT_RESOURCES.FORM_TRANSACTION}/empty`,
                method: 'post',
                values: {
                    formId: formId,
                    timezone,
                    isReturnObjectResult: true,
                },
                errorNotification: false,
                successNotification: false,
            },
            {
                onError: (error) => {
                    notification.success({
                        message: translate('notifications.transaction_update_fail', { ns: NAMESPACES.FORM }),
                        type: 'error',
                    });
                },
                onSuccess: async (data) => {
                    await updateTransactionAfterCreated({
                        formId,
                        originalVersionId,
                        contextValue,
                        documentType,
                        formVersionId: data?.data?.data?.formVersionId,
                        targetLookupContextField,
                        transactionId: data?.data?.data?.id,
                    });
                    setTimeout(() => {
                        setShouldRefresh((prev) => !prev);
                        setLoading(false);
                    }, 9000);
                },
            },
        );
    };

    const getActionRelatedForm = async ({
        fieldId,
        formId,
        originalDocVersionId,
    }: {
        formId: string;
        fieldId: string;
        originalDocVersionId: string;
    }) => {
        const cacheKey = `form-transaction-${formId}-${fieldId}-${originalDocVersionId}-${shouldRefresh}`;

        const dataRes = await queryClient.fetchQuery({
            queryKey: [cacheKey],
            staleTime: 500,
            queryFn: async () =>
                await dataProvider(getEnvConfig.APPLICATION_API_URL).getList({
                    resource: ENDPOINT_RESOURCES.FORM_TRANSACTION,
                    filters: [
                        { field: 'formId', operator: 'eq', value: formId },
                        {
                            field: `${PREFIX.DATA_REGISTER_FIELD}_${EXTERNAL_ORIGINAL_VERSION_ID}`,
                            operator: 'eq',
                            value: originalDocVersionId,
                            queryToDataFilterOptions: true,
                        },
                    ] as any,
                    sorters: [{ field: 'updatedAt', order: 'desc' }],
                    meta: {
                        searchQuery: {
                            fieldIds: [fieldId],
                        },
                    },
                }),
        });

        return dataRes?.data[0];
    };

    useEffect(() => {
        (async () => {
            if (!widget.contexts?.includes(WidgetContextTypes.No_Context)) return;
            const regDataResponse = await getRegisterByMdsIds();

            if (isEmpty(regDataResponse)) return;

            // Create a mapping of externalId to id
            const externalIdToIdMap: { [key: string]: string } = {};

            // Iterate through the response data
            if (Array.isArray(regDataResponse)) {
                regDataResponse.forEach((item: Record<string, any>) => {
                    if (item?.externalId && item.id) {
                        externalIdToIdMap[item.externalId] = item.id;
                    }
                });
            }

            // Set the mapping in state
            setExternalAndRecordIdPair(externalIdToIdMap);
        })();
    }, []);

    const getRegisterByMdsIds = async () => {
        const registerList = await dataProvider(getEnvConfig.DEFINITION_API_URL).getList({
            resource: identity?.accountId ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER,
            filters: [{ field: 'type', operator: 'contains', value: 'Vessel' }],
            sorters: [{ field: 'updatedAt', order: 'desc' }],
        });

        if (registerList?.data?.length === 0) return [];

        const drResponse = await mutateAsync({
            url: `${apiUrl}/${identity?.accountId ? ENDPOINT_RESOURCES.DATA_REGISTER_TRANSACTION_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER_TRANSACTION}/filter`,
            method: 'post',
            values: {
                filters: [
                    {
                        field: 'dataRegisterId',
                        value: registerList.data[0]?.id,
                        operator: `eq`,
                    },
                ],
            },
        });

        return drResponse?.data ?? [];
    };

    const fetchData = async () => {
        try {
            const results: any = await Promise.all([
                await getExternalData(WidgetExternalDataSource.PSC),
                await getExternalData(WidgetExternalDataSource.MDS_CATALOG_PSC),
                await getExternalData(WidgetExternalDataSource.VIQ),
                await getExternalData(WidgetExternalDataSource.LLI),
                await getExternalData(WidgetExternalDataSource.OCIMF),
            ]);

            setAllData(results);
            const totalFromAllSources = results.reduce((acc: number, curr: any) => {
                return acc + curr?.total || 0;
            }, 0);

            setTotal(totalFromAllSources);
        } catch (error) {
            console.error('Error fetching data from providers', error);
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        if (!isPreview) {
            const contexts = widget?.contexts ?? [];

            if (contexts?.includes(WidgetContextTypes.No_Context) || isEmpty(contexts)) {
                fetchData();
            } else {
                if (isEmpty(contextValues) && isEmpty(entityId)) {
                    setLoading(false);
                    return;
                }
                fetchData();
            }
        }
    }, [JSON.stringify(contextValues), JSON.stringify(entityId), search, pagination]);

    useDeepCompareEffect(() => {
        (async () => {
            // mapping data to Table
            const columns = (widgetConfig?.columns as any) || [];
            const pathsMap: Record<string, string> = {};

            let combinedDataSources: string[] = [];
            Object.values(WidgetExternalDataSource).map((dataSourceType) => {
                const dataPerSource = widgetConfig?.dataSourceConfig?.[dataSourceType];
                if (dataPerSource) combinedDataSources = [...combinedDataSources, ...dataPerSource];
            });

            let dataResult: any = [];

            if (combinedDataSources?.length > 0) {
                for (const source of combinedDataSources) {
                    for (const col of columns) {
                        const dataIndex = col?.dataMapping?.[source];
                        pathsMap[col.dataIndex] = dataIndex;
                    }

                    const action = widgetConfig?.actions?.[source];

                    const mappingPromises = allData.map(async (dataPerSourceType) => {
                        const groupedData = dataPerSourceType?.data?.reduce((acc: any, item: any) => {
                            const source = item.source;
                            if (!acc[source]) {
                                acc[source] = [];
                            }
                            acc[source].push(item);
                            return acc;
                        }, {});

                        const data = {
                            data: groupedData?.[source] ?? [],
                            source,
                            action,
                        };
                        return await mapDataSourceToTable(data, pathsMap, action);
                    });

                    const mappedResults = await Promise.all(mappingPromises);
                    dataResult = [...dataResult, ...mappedResults.flat()];
                }
            }

            // Sort the combined data using the same logic as getExternalData
            const sortableColumns = widgetConfig?.columns?.filter((col: any) => col.enableSort) || [];
            if (sortableColumns.length > 0) {
                dataResult.sort((a: any, b: any) => {
                    for (const col of sortableColumns) {
                        const fieldPath = col?.enableSort ? col.dataIndex : '';
                        if (!fieldPath) continue;

                        const aValue = get(a, col.dataIndex);
                        const bValue = get(b, col.dataIndex);

                        if (aValue === bValue) continue;

                        const order = col.sortOrder === 'asc' ? 1 : -1;

                        // Handle null/undefined values
                        if (aValue === null || aValue === undefined) return 1 * order;
                        if (bValue === null || bValue === undefined) return -1 * order;

                        // Check if values are date strings and convert them
                        const aDate = dayjs(aValue).isValid() ? dayjs(aValue) : null;
                        const bDate = dayjs(bValue).isValid() ? dayjs(bValue) : null;

                        // If both values are valid dates, compare them
                        if (aDate && bDate) {
                            return aDate.isBefore(bDate) ? -1 * order : 1 * order;
                        }

                        // If not dates, do regular comparison
                        return aValue < bValue ? -1 * order : 1 * order;
                    }
                    return 0;
                });
            }

            setDataSource(dataResult as any);
        })();
    }, [allData, shouldRefresh]);

    useEffect(() => {
        const columns = widgetConfig?.columns as any;

        const newCols = columns?.map((c: any) => ({
            title: c.name,
            dataIndex: c.dataIndex,
            key: c.key,
            width: widthConfigMapper(c?.widthConfig ?? {}),
            render: (data: any) => {
                if (data?.type === 'button' || data?.type === 'div') return data;
                return renderFieldColumn(data);
            },
            ellipsis: true,
            ...getColumnSearchProps(c.key),
        }));
        setColumns(newCols as any);
        setPagination(widgetConfig?.pagination || { current: 1, pageSize: 5 });
        setColumnsConfig(widgetConfig?.columns);
    }, [widgetConfig]);

    const onChangeTable = (newPagination: TablePaginationConfig, tableFilters: Record<string, FilterValue | null>) => {
        setSearch(tableFilters);
        setPagination(newPagination);
    };

    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            headerBg: styles?.backgroundColor ?? '#fafafa',
                            colorBgContainer: styles?.backgroundColor ?? '#ffffff',
                            rowHoverBg: styles?.backgroundColor ?? '#fafafa',
                        },
                        Pagination: {
                            colorBgContainer: styles?.backgroundColor,
                        },
                    },
                }}
            >
                <Table
                    rowKey="key"
                    columns={columns}
                    dataSource={dataSource}
                    pagination={
                        isSingleDataSource(widgetConfig?.dataSourceConfig ?? {})
                            ? {
                                  current: pagination.current || 1,
                                  pageSize: pagination.pageSize,
                                  total: total,
                                  pageSizeOptions: [1, 5, 10, 20, 30, 40, 50, 100],
                              }
                            : false
                    }
                    loading={loading || isUpdatingTran}
                    onChange={isSingleDataSource(widgetConfig?.dataSourceConfig ?? {}) ? onChangeTable : undefined}
                    scroll={!isSingleDataSource(widgetConfig?.dataSourceConfig ?? {}) ? { y: 400 } : undefined}
                />
            </ConfigProvider>
        </>
    );
}

export { WidgetExternalRenderer };
