import { EditMode, FieldTypeEnum } from '@/common/enums';
import { ENDPOINT_RESOURCES, NAMESPACES } from '@/constants';
import { dataProvider } from '@/dataProvider';
import { NUser, NWidgetManager, NWorkspace } from '@/interfaces';
import { EditDataRegisterTransactionRenderer } from '@/modules/shared/form-builder/form-renderer';
import { getSummaryWidgetFieldFormat } from '@/modules/shared/form-builder/utils';
import { WidgetDataSourceTargetAction } from '@/modules/widget/constant';
import { WorkspaceDetail } from '@/modules/workspace/user-workspaces/WorkspaceDetail';
import { SummaryFormFieldType } from '@/modules/workspace/workspace-flow/WorkspaceLayoutStep/SummaryZoneSetting/SummaryZoneColumn';
import { getEnvConfig, parseFieldStyle } from '@/utils';
import { BaseRecord, GetOneResponse, useGetIdentity, useTranslation } from '@refinedev/core';
import { Drawer, Toolt<PERSON> } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

type TWidgetSummaryFieldRendererProps = {
    widgetField: NWidgetManager.IWidgetSummaryField;
    hashTargetIdType: Record<string, string>;
    isLast?: boolean;
};

export const WidgetSummaryFieldRenderer: React.FC<TWidgetSummaryFieldRendererProps> = ({
    widgetField,
    hashTargetIdType,
    isLast,
}: TWidgetSummaryFieldRendererProps) => {
    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();
    const isTenancy = !!identity?.accountId;

    const { translate } = useTranslation();
    const [openDrawer, setOpenDrawer] = useState(false);
    const [workSpaceData, setWorkspaceData] = useState<GetOneResponse<BaseRecord>>();
    const [entityId, setEntityId] = useState('');

    const widgetTranslate = (alias: string, param?: Record<string, string>) => translate(alias, { ns: NAMESPACES.WIDGET, ...param });

    const format = getSummaryWidgetFieldFormat(widgetField as SummaryFormFieldType);
    const openTargetAction = widgetField?.config?.targetAction ?? WidgetDataSourceTargetAction.DataRegister;
    const openTargetDRId = widgetField?.configuration?.lookupTargetId;
    const recordId = widgetField?.fieldOptionIds?.[0] ?? '';
    const canOpenDrawer = widgetField?.type === FieldTypeEnum.Lookup && recordId && openTargetDRId;

    let fieldStyle = format.style;
    if (widgetField.style?.color) fieldStyle = { ...fieldStyle, color: widgetField.style?.color };
    if (widgetField.style?.backgroundColor) fieldStyle = { ...fieldStyle, backgroundColor: widgetField.style?.backgroundColor };

    const iconRender = parseFieldStyle(widgetField.style ?? {});

    useEffect(() => {
        if (openTargetAction === WidgetDataSourceTargetAction.Workspace) {
            (async () => {
                const workspaceRes = await dataProvider(getEnvConfig.DEFINITION_API_URL).getOne({
                    resource: `${ENDPOINT_RESOURCES.WORKSPACES_TENANCY}/active-entity-workspace`,
                    id: hashTargetIdType?.[openTargetDRId],
                    meta: {
                        searchQuery: {
                            registerId: openTargetDRId,
                        },
                    },
                });
                setWorkspaceData(workspaceRes);

                if (recordId) {
                    const { data: registerRecordResponse } = await dataProvider(getEnvConfig.APPLICATION_API_URL).getOne({
                        resource: isTenancy
                            ? ENDPOINT_RESOURCES.DATA_REGISTER_TRANSACTION_TENANCY
                            : ENDPOINT_RESOURCES.DATA_REGISTER_TRANSACTION,
                        id: recordId,
                    });

                    if (registerRecordResponse?.externalId) setEntityId(registerRecordResponse?.externalId);
                }
            })();
        }
    }, [JSON.stringify(hashTargetIdType), openTargetAction, openTargetDRId, recordId]);

    const showDrawer = () => {
        canOpenDrawer && setOpenDrawer(true);
    };

    const onCloseDrawer = () => {
        setOpenDrawer(false);
    };

    const renderDrawer = () => {
        switch (openTargetAction) {
            case WidgetDataSourceTargetAction.Workspace: {
                if (!workSpaceData?.data) {
                    return <div className="flex w-full py-4 justify-center text-orange-600">No Workspace available</div>;
                }

                return (
                    <WorkspaceDetail
                        onClose={onCloseDrawer}
                        registerRecordId={recordId}
                        workspace={workSpaceData?.data as NWorkspace.WorkspaceType}
                        workspaceId={workSpaceData?.data?.id as string}
                        entityId={entityId}
                    />
                );
            }
            default:
                return (
                    <EditDataRegisterTransactionRenderer
                        id={openTargetDRId}
                        dataId={recordId}
                        mode={EditMode.Update}
                        closeDrawer={onCloseDrawer}
                    />
                );
        }
    };

    return (
        <>
            {iconRender && iconRender.icon}
            <Tooltip
                title={
                    canOpenDrawer ? widgetTranslate('action.open_record_details', { target: openTargetAction.replaceAll('_', ' ') }) : null
                }
            >
                <div style={fieldStyle} className={`${format.className} ${canOpenDrawer ? 'cursor-pointer' : ''}`} onClick={showDrawer}>
                    <span>
                        {widgetField?.config?.showLabel ? (
                            <strong>
                                {widgetField?.label}
                                {widgetField?.label && ':'}
                            </strong>
                        ) : (
                            ''
                        )}{' '}
                        &nbsp;{' '}
                        <span className={canOpenDrawer ? 'hover:text-blue-500' : ''}>
                            {widgetField?.config?.showValue && widgetField.fieldValue
                                ? widgetField.type === 'datePicker' && widgetField?.config.format
                                    ? dayjs(widgetField.fieldValue).format(widgetField?.config?.format)
                                    : widgetField.fieldValue
                                : ''}
                        </span>
                        &nbsp;
                        {widgetField?.config?.separator && widgetField?.config?.showValue && widgetField.value && !isLast
                            ? widgetField?.config?.separator
                            : ''}
                        &nbsp;
                    </span>
                </div>
            </Tooltip>
            <Drawer size="large" onClose={onCloseDrawer} open={openDrawer} width="90%" destroyOnClose>
                {renderDrawer()}
            </Drawer>
        </>
    );
};
