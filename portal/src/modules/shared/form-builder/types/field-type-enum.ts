export const fieldTypeLabels: Record<string, string> = {
    text: 'Text',
    number: 'Number',
    textArea: 'Text Area',
    select: 'Select',
    datePicker: 'Date Picker',
    datetimePicker: 'Datetime Picker',
    timePicker: 'Time Picker',
    calculation: 'Calculation',
    duration: 'Duration',
    lookup: 'Lookup',
    rollup: 'Rollup',
    separator: 'Separator',
    uiGroup: 'Ui Group',
    checkbox: 'Checkbox',
    userLookup: 'User Lookup',
    roleLookup: 'Role Lookup',
    document: 'Document',
    sire2Answer: 'Sire 2 Answer',
    definable: 'Definable',
};

export enum DurationTypeEnum {
    GreaterThan = 'greaterThan',
    EqualOrGreaterThan = 'equalOrGreaterThan',
    LessThan = 'lessThan',
    EqualOrLessThan = 'equalOrLessThan',
}
