/* eslint-disable @typescript-eslint/no-explicit-any */
import { FieldTypeEnum } from '@/common/enums';
import {
    CalculationFormField,
    CheckboxFormField,
    DateFormField,
    DurationFormField,
    LookupFormField,
    NumberFormField,
    RoleLookupFormField,
    SelectFormField,
    TextAreaFormField,
    TextFormField,
    UserLookupFormField,
} from '@/modules/form-fields';
import { DatetimeFormField } from '@/modules/form-fields/DatetimeFormField';
import { Sire2AnswerFormField } from '@/modules/form-fields/Sire2AnswerFormField';
import React from 'react';
import { InputTimeFormField } from '../../../data-register/data-register-builder/form-fields/InputTimeFormatField';
import { AdditionalFormField } from '../../../form-fields/AdditionalFormField';
import { RollupFormField } from '../../../form-fields/RollupFormField';
import { Separator, UiGroupRenderer } from '../ui-components';
type Props = {
    Field: React.FC<any>;
};

export const ADDITIONAL_FIELD_TYPE = 'additional';

export const widgetRendererDefinitions: {
    [fieldType: string]: Props;
} = {
    [FieldTypeEnum.Text]: {
        Field: TextFormField,
    },
    [FieldTypeEnum.Number]: {
        Field: NumberFormField,
    },
    [FieldTypeEnum.Select]: {
        Field: SelectFormField,
    },
    [FieldTypeEnum.TextArea]: {
        Field: (props) => <TextAreaFormField {...props} autoHeight style={{ ...(props.style ?? {}), resize: 'none' }} />,
    },
    [FieldTypeEnum.Calculation]: {
        Field: CalculationFormField,
    },
    [FieldTypeEnum.DatePicker]: {
        Field: DateFormField,
    },
    [FieldTypeEnum.DatetimePicker]: {
        Field: DatetimeFormField,
    },
    [FieldTypeEnum.TimePicker]: {
        Field: InputTimeFormField,
    },
    [FieldTypeEnum.Duration]: {
        Field: DurationFormField,
    },
    [FieldTypeEnum.Lookup]: {
        Field: LookupFormField,
    },
    [FieldTypeEnum.Rollup]: {
        Field: RollupFormField,
    },
    [FieldTypeEnum.Separator]: {
        Field: Separator,
    },
    [FieldTypeEnum.UiGroup]: {
        Field: UiGroupRenderer,
    },
    [FieldTypeEnum.Checkbox]: {
        Field: CheckboxFormField,
    },
    [FieldTypeEnum.UserLookup]: {
        Field: UserLookupFormField,
    },
    [FieldTypeEnum.Answer]: {
        Field: AdditionalFormField,
    },
    [FieldTypeEnum.Comparison]: {
        Field: AdditionalFormField,
    },
    // TODO: 2280 - Definable field
    [FieldTypeEnum.Definable]: {
        Field: AdditionalFormField,
    },
    [FieldTypeEnum.RoleLookup]: {
        Field: RoleLookupFormField,
    },
    [FieldTypeEnum.Sire2Answer]: {
        Field: Sire2AnswerFormField,
    },
};
