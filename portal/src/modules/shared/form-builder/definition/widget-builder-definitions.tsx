/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    CheckboxField,
    FloatDatePicker,
    FloatInput,
    FloatInputNumber,
    FloatInputTime,
    FloatSelect,
    FloatTextArea,
} from '@/form-components';
import { CalculationField } from '@/form-components/CalculationField/CalculationField';

import { FieldTypeEnum } from '@/common/enums';
import { FloatDuration } from '@/form-components/FloatDuration';
import { LookupFloatSelect } from '@/form-components/LookupFloatSelect';
import { TextField } from '@/form-components/TextField/TextField';
import React from 'react';
import { HHmm } from '../../../../constants';
import { GeneralAutoPopulateFormSetting } from '../setting-fields/AutoPopulateFormField/GeneralAutoPopulateFormSetting';
import {
    CalculationDataSettings,
    CalculationDisplaySettings,
    CalculationValidationSettings,
    CheckboxDataSettings,
    CheckboxDisplaySettings,
    CheckboxValidationSettings,
    DateDataSettings,
    DateDisplaySettings,
    DateValidationSettings,
    DocumentDisplaySettings,
    DurationDataSettings,
    DurationDisplaySettings,
    DurationValidationSettings,
    LookupDataSettings,
    LookupDisplaySettings,
    LookupValidationSettings,
    NumberDataSettings,
    NumberDisplaySettings,
    NumberValidationSettings,
    RoleLookupDataSettings,
    RoleLookupDisplaySettings,
    RoleLookupValidationSettings,
    RollupDisplaySettings,
    RollupValidationSettings,
    SelectDataSettings,
    SelectDisplaySettings,
    SelectValidationSettings,
    SeparatorDisplaySettings,
    TextAreaDataSettings,
    TextAreaDisplaySettings,
    TextDataSettings,
    TextDisplaySettings,
    TextValidationSettings,
    TimeDataSettings,
    UiGroupDisplaySettings,
    UserLookupDataSettings,
    UserLookupDisplaySettings,
    UserLookupValidationSettings,
} from '../settings';
import { CalculationFormulaSettings } from '../settings/calculation-formula/CalculationFormulaSettings';
import { DatetimeDataSettings } from '../settings/data-settings/DatetimeDataSettings';
import { RollupDataSettings } from '../settings/data-settings/RollupDataSettings';
import { CriteriaDisplaySettings } from '../settings/display-settings/CriteriaDisplaySetting';
import { DatetimeDisplaySettings } from '../settings/display-settings/DatetimeDisplaySettings';
import { TimeDisplaySettings } from '../settings/display-settings/TimeDisplaySetting';
import { DatetimeValidationSettings } from '../settings/validation-settings/DatetimeValidationSettings';
import { TimeValidationSettings } from '../settings/validation-settings/TimeValidationSettings';
import { Separator, UiGroupBuilder } from '../ui-components';
import ContextFieldSettings from '../settings/context-settings/ContextFieldSettings';

type Props = {
    Field: React.FC<any>;
    DisplaySettings?: React.FC<any>;
    DataSettings?: React.FC<any>;
    ValidationSettings?: React.FC<any>;
    CalculationFormulaSettings?: React.FC<any>;
    AutoPopulateSettings?: React.FC<any>;
    AutoPopulateMappingSettings?: React.FC<any>;
    ContextSettings?: React.FC<any>;
};

export const widgetBuilderDefinitions: {
    [fieldType: string]: Props;
} = {
    [FieldTypeEnum.Text]: {
        Field: FloatInput,
        DataSettings: TextDataSettings,
        DisplaySettings: TextDisplaySettings,
        ValidationSettings: TextValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Number]: {
        Field: FloatInputNumber,
        DataSettings: NumberDataSettings,
        DisplaySettings: NumberDisplaySettings,
        ValidationSettings: NumberValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Select]: {
        Field: FloatSelect,
        DisplaySettings: SelectDisplaySettings,
        DataSettings: SelectDataSettings,
        ValidationSettings: SelectValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.TextArea]: {
        Field: (res) => <FloatTextArea {...res} autoHeight style={{ ...(res.style ?? {}), resize: 'none' }} />,
        DataSettings: TextAreaDataSettings,
        DisplaySettings: TextAreaDisplaySettings,
        ValidationSettings: TextValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Calculation]: {
        Field: CalculationField,
        DataSettings: CalculationDataSettings,
        DisplaySettings: CalculationDisplaySettings,
        CalculationFormulaSettings: CalculationFormulaSettings,
        ValidationSettings: CalculationValidationSettings,
    },
    [FieldTypeEnum.DatePicker]: {
        Field: (res) => <FloatDatePicker {...res} defaultValue={undefined} />,
        DataSettings: DateDataSettings,
        DisplaySettings: DateDisplaySettings,
        ValidationSettings: DateValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.TimePicker]: {
        Field: (res) => <FloatInputTime {...res} format={HHmm} defaultValue={undefined} />,
        DataSettings: TimeDataSettings,
        DisplaySettings: TimeDisplaySettings,
        ValidationSettings: TimeValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.DatetimePicker]: {
        Field: (res) => <FloatDatePicker {...res} defaultValue={undefined} />,
        DataSettings: DatetimeDataSettings,
        DisplaySettings: DatetimeDisplaySettings,
        ValidationSettings: DatetimeValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Duration]: {
        Field: (res) => <FloatDuration {...res} defaultValue={undefined} />,
        DataSettings: DurationDataSettings,
        DisplaySettings: DurationDisplaySettings,
        ValidationSettings: DurationValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Lookup]: {
        Field: (props) => <LookupFloatSelect {...props} isHideDefaultValue={true} />,
        DisplaySettings: LookupDisplaySettings,
        DataSettings: LookupDataSettings,
        ValidationSettings: LookupValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Rollup]: {
        Field: TextField,
        DisplaySettings: RollupDisplaySettings,
        DataSettings: RollupDataSettings,
        ValidationSettings: RollupValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.Separator]: {
        Field: Separator,
        DisplaySettings: SeparatorDisplaySettings,
    },
    [FieldTypeEnum.UiGroup]: {
        Field: UiGroupBuilder,
        DisplaySettings: UiGroupDisplaySettings,
    },
    [FieldTypeEnum.Checkbox]: {
        Field: CheckboxField,
        DataSettings: CheckboxDataSettings,
        DisplaySettings: CheckboxDisplaySettings,
        ValidationSettings: CheckboxValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
    [FieldTypeEnum.UserLookup]: {
        Field: FloatSelect,
        DataSettings: UserLookupDataSettings,
        DisplaySettings: UserLookupDisplaySettings,
        ValidationSettings: UserLookupValidationSettings,
    },
    [FieldTypeEnum.Answer]: {
        Field: FloatInput,
        DisplaySettings: CriteriaDisplaySettings,
    },
    [FieldTypeEnum.Comparison]: {
        Field: FloatInput,
        DisplaySettings: CriteriaDisplaySettings,
    },
    // TODO: 2280 - Definable field
    [FieldTypeEnum.Definable]: {
        Field: FloatInput,
        DisplaySettings: CriteriaDisplaySettings,
    },
    [FieldTypeEnum.RoleLookup]: {
        Field: FloatSelect,
        DisplaySettings: RoleLookupDisplaySettings,
        ValidationSettings: RoleLookupValidationSettings,
        DataSettings: RoleLookupDataSettings,
    },
    [FieldTypeEnum.Document]: {
        Field: FloatInput,
        DisplaySettings: DocumentDisplaySettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
        ContextSettings: ContextFieldSettings,
    },
    [FieldTypeEnum.Sire2Answer]: {
        Field: FloatInput,
        DataSettings: TextDataSettings,
        DisplaySettings: TextDisplaySettings,
        ValidationSettings: TextValidationSettings,
        AutoPopulateSettings: GeneralAutoPopulateFormSetting,
        AutoPopulateMappingSettings: GeneralAutoPopulateFormSetting,
    },
};
