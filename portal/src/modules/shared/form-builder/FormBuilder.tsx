import { DataRegisterTypeEnum } from '@/common/enums';
import { useElementSize } from '@/common/hooks';
import { FormToolbar, ToolBarProps } from '@/components/form-toolbar';
import { WarningOutlined } from '@/constants/icons';
import { useFormStore } from '@/stores';
import { useTranslation } from '@refinedev/core';
import { Button, Modal } from 'antd';
import { Icon } from '../../../components/icon';
import { WorkSpaceBuilder } from './WorkSpaceBuilder';
import { DndProviderWrapper, DropZone } from './dnd';
import { PreviewDrawer } from './settings/PreviewDrawer';
import { SettingDrawer } from './settings/SettingDrawer';
import { FormFieldType } from './types';
import { AddFieldSelector } from './ui-components';
import { CalculationValueStack } from './utils/calculationValueStack';
import { useFormLock } from '@/modules/form/hooks/useFormLock';
import { NAMESPACES, UnlockOutlined, LockOutlined } from '@/constants';

type Props = {
    loading?: boolean;
    isRegister?: boolean;
    isOpenPreview?: boolean;
    dataRegister?: Record<string, unknown>;
    setIsOpenPreview?: (value: boolean) => void;
    onSave: () => void;
    toolbarProps: ToolBarProps;
    formId: string;
    formType?: 'form' | 'register';
    isAdditionalField?: boolean;
};

export const FormBuilder = ({ onSave, loading, isOpenPreview, setIsOpenPreview, formId, formType, isAdditionalField, ...props }: Props) => {
    const { translate } = useTranslation();
    const { setField, selectedFieldId, setSelectedFieldId, warningMessage, setWarningMessage } = useFormStore();
    const { isCurrentUserHoldingLock, formLock, handleRelease, handleTakeOver } = useFormLock(formId);

    const CONTAINER_ID = 'workSpaceContainer';

    const onClosePreview = () => {
        CalculationValueStack.resetInstance();
        setIsOpenPreview?.(false);
    };

    const handleDrop = (item: FormFieldType) => {
        setField(
            {
                ...item,
                groupId: undefined,
                dataGrid: {
                    ...item.dataGrid,
                    y: 9999,
                },
            },
            false,
        );
    };

    const { width: containerWidth } = useElementSize(CONTAINER_ID);

    const toolbarBoxActions = [];
    if (formType === 'form') {
        if (formLock.isLocked && isCurrentUserHoldingLock) {
            toolbarBoxActions.push({
                icon: UnlockOutlined,
                label: translate('form_lock.release_edit_control', { ns: NAMESPACES.FORM }),
                key: 'releaseEditControl',
                onClick: () => {
                    handleRelease();
                },
            });
        }
        if (!formLock.isLocked && !isCurrentUserHoldingLock) {
            toolbarBoxActions.push({
                icon: LockOutlined,
                label: translate('form_lock.take_over_edit_control', { ns: NAMESPACES.FORM }),
                key: 'takeOverEditControl',
                onClick: () => {
                    handleTakeOver();
                },
            });
        }
    }

    return (
        <>
            <DndProviderWrapper>
                <section>
                    <article className="mb-4">
                        <FormToolbar
                            {...props.toolbarProps}
                            prefixActions={
                                <>
                                    <div className="w-48">
                                        <AddFieldSelector
                                            isRegister={props.isRegister}
                                            registerType={props?.dataRegister?.type as DataRegisterTypeEnum}
                                            disabled={formType === 'form' && !isCurrentUserHoldingLock}
                                        />
                                    </div>
                                </>
                            }
                            extraActions={
                                <>
                                    <Button
                                        loading={loading}
                                        onClick={onSave}
                                        type="primary"
                                        disabled={formType === 'form' && !isCurrentUserHoldingLock}
                                    >
                                        {translate('button.save', { ns: 'common' })}
                                    </Button>
                                </>
                            }
                            toolbarBoxActions={toolbarBoxActions}
                        />
                    </article>

                    <DropZone onDrop={handleDrop} isParent>
                        <article id={CONTAINER_ID} className="border border-solid border-neutral-300 min-h-[calc(100vh-180px)]">
                            <WorkSpaceBuilder width={containerWidth} />
                        </article>
                    </DropZone>
                </section>
            </DndProviderWrapper>

            {selectedFieldId && (
                <SettingDrawer
                    open={true}
                    width="70%"
                    onClose={() => setSelectedFieldId()}
                    dataRegisterType={(props?.dataRegister?.type as DataRegisterTypeEnum) ?? ''}
                    propType={formType ?? 'form'}
                    dataRegisterVersion={props?.dataRegister?.latestVersion}
                    isAdditionalField={isAdditionalField}
                    isRegister={props.isRegister}
                />
            )}
            {isOpenPreview && <PreviewDrawer open={isOpenPreview} onClose={onClosePreview} width="1220px" formId={formId} />}
            <Modal
                title={
                    <div className="flex gap-2 items-center">
                        <Icon icon={WarningOutlined} className="text-warn text-3xl" />
                        <>{translate('unsave.title', { ns: 'common' })}</>
                    </div>
                }
                centered
                closable={false}
                open={!!warningMessage}
                footer={null}
            >
                <p className="mb-2">{warningMessage}</p>
                <div className="flex justify-end">
                    <Button onClick={() => setWarningMessage()} type="primary">
                        {translate('ok')}
                    </Button>
                </div>
            </Modal>
        </>
    );
};
