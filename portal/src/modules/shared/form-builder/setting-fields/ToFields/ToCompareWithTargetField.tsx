import { useTranslation } from '@refinedev/core';
import { useFormContext } from 'react-hook-form';
import { FieldTypeEnum } from '../../../../../common/enums';
import { useRegisterFieldOptions } from '../../../../../common/hooks';
import { useDataRegisterInfo } from '../../../../../common/hooks/useDataRegisterInfo';
import { NAMESPACES, NON_LOGIC_FIELD_TYPES } from '../../../../../constants';
import { SelectFormField } from '../../../../form-fields';

type Props = {
    formName: string;
    loading: boolean;

    formTypeName?: string;
    registerId?: string;
};

export const ToCompareWithTargetField: React.FC<Props> = ({ formName, formTypeName, loading, registerId }) => {
    const { translate } = useTranslation();
    const { options } = useRegisterFieldOptions({
        registerId,
    });

    const { unregister, setValue } = useFormContext();
    const registerInfo = useDataRegisterInfo({ id: registerId });

    const flatFieldOptions = options.filter((f) => {
        if (f.type === FieldTypeEnum.Lookup) return false;
        if (NON_LOGIC_FIELD_TYPES.includes(f.type)) return false;
        if (
            [
                FieldTypeEnum.Answer,
                FieldTypeEnum.Comparison,
                FieldTypeEnum.Definable,
                FieldTypeEnum.Sire2Answer,
                FieldTypeEnum.Document,
            ].includes(f.type)
        )
            return false;
        return true;
    });

    return (
        <SelectFormField
            name={formName}
            label={translate('auto_populate_settings.current_register_field', {
                ns: NAMESPACES.FORM,
                registerName: registerInfo?.name ?? '',
            })}
            options={flatFieldOptions}
            allowClear
            showSearch
            loading={loading}
            filterOption={(input, option) => (option?.label ?? '')?.toString().toLowerCase()?.includes(input?.toLowerCase())}
            onClear={() => {
                unregister(formName);
                unregister(formTypeName);
            }}
            onChange={(value) => {
                if (formTypeName) {
                    const field = flatFieldOptions?.find((f) => f.value === value);
                    setValue(formTypeName, field?.type);
                }
            }}
        />
    );
};
