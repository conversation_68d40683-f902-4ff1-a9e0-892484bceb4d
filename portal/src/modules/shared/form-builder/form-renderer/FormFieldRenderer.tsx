import { FieldViewMode } from '@/common/enums/fieldViewMode.enum';
import { NFormTransaction } from '@/interfaces/form-transaction';
import { AdditionalFormField } from '@/modules/form-fields/AdditionalFormField';
import { LabelWidgets } from '@/modules/form/form-builder/form-flow/form-flow-collection/renderers/CollectionLabelField';
import { CollectionSummaryFieldRenderer } from '@/modules/form/form-builder/form-flow/form-flow-collection/renderers/CollectionSummaryFieldRenderer';
import { isArray } from 'lodash';
import React, { CSSProperties, FC, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FieldTypeEnum } from '../../../../common/enums';
import { NAMESPACES } from '../../../../constants';
import { IRole, NDataRegisterBuilder } from '../../../../interfaces';
import { useFormRendererStore, useLookupDataStore } from '../../../../stores';
import { widgetRendererDefinitions } from '../definition';
import { FormFieldType, FormFields } from '../types';
import { getFormFieldFormat, handleStyleConditions } from '../utils';
import { determineRequiredByIdentifier } from '../utils/determineRequiredByIdentifier';
import { parseFormRules } from '../utils/parseFormRules';

export type FormFieldRendererProps = {
    field: FormFieldType;
    fields: FormFields;
    formId: string;
    activeStage: string;
    glbFieldStyles?: CSSProperties;
    availableRoles?: IRole[];
    onChange?: (value: any, field?: any) => void;
    onFocus?: (event: any, field?: any) => void;
    onBlur?: (event: any, field?: any) => void;
    hideMessage?: boolean;
    status?: string;
    fieldClassName?: string;
    isCollectionOrCriteria?: boolean;
    isConfigAdditionalFields?: boolean;
    isVisible?: boolean;
    identifierSettings?: NDataRegisterBuilder.IdentifierConfig[]; // only for DR
    associatedField?: string; // only for DR
    validationIcon?: React.ReactNode;
    sire2Mode?: FieldViewMode;
    transactionField?: NFormTransaction.IFormTransactionField;
    tabIndex?: number;
};

export const FormFieldRenderer = React.memo<FormFieldRendererProps>(function FormFieldRenderer({
    field,
    fields,
    formId,
    activeStage,
    glbFieldStyles,
    availableRoles,
    onChange: onChangeProp,
    onFocus: onFocusProp,
    onBlur: onBlurProps,
    hideMessage,
    status,
    fieldClassName,
    isConfigAdditionalFields,
    isVisible,
    associatedField,
    identifierSettings,
    validationIcon,
    sire2Mode,
    transactionField,
    isCollectionOrCriteria,
    tabIndex,
}) {
    const { type, ruleConfigs, fieldId, style, styleConditions, locked, ...fieldProps } = field;

    const { t } = useTranslation();

    //isSupported, (answer, comparison)`type` fields will render AdditionalFormField
    const isSupportFieldConfigurable = isConfigAdditionalFields && field.isSupportField;
    const isReadOnly = fieldProps?.readOnly || isSupportFieldConfigurable;
    const { Field } = resolveFieldComponent(isSupportFieldConfigurable, type);
    const { getValues } = useFormContext();
    const { setData } = useLookupDataStore();

    const { setSelectedAdditionalField, formType, formStates, setSelectedFieldNames, updateTransactionFieldMap } = useFormRendererStore();
    const { selectedFieldNames } = formStates[formId];

    const watchAssociatedField = useWatch({
        name: associatedField ?? '',
    });

    // --- Memoized Calculations --- START ---
    const pretreatmentResult = useMemo(() => getFormFieldFormat(fields, field, getValues), [fields, field, getValues]);

    const rules = useMemo(() => {
        const options = {
            fieldType: type,
            format: fieldProps?.format,
        };
        let parsedRules = parseFormRules(ruleConfigs, options, t);

        const isRequired = determineRequiredByIdentifier({
            associatedField,
            watchAssociatedField,
            identifierSettings,
            fieldId,
        });

        if (formType === 'form' && parsedRules) {
            parsedRules.required = false;
        }

        if (!isVisible) {
            parsedRules = { ...parsedRules, required: false };
        }

        if (associatedField && identifierSettings?.length && watchAssociatedField) {
            if (isRequired) {
                parsedRules = {
                    ...parsedRules,
                    required: { value: true, message: t('message.field_required', { ns: NAMESPACES.COMMON }) },
                };
            }
        }
        return parsedRules;
    }, [type, fieldProps?.format, ruleConfigs, t, associatedField, watchAssociatedField, identifierSettings, fieldId, formType, isVisible]);

    const { style: fieldStyle } = useMemo(
        () =>
            handleStyleConditions({
                style,
            }),
        [style],
    );

    const combineStyles: React.CSSProperties & Record<string, any> = useMemo(() => {
        const disabledColor = field.disabled ?? field.readOnly ?? field.locked;

        const styles: React.CSSProperties & Record<string, any> = {
            ...(glbFieldStyles ?? {}),
            ...(fieldStyle ?? {}),
        };

        if (disabledColor) {
            styles['--disabled-color'] = styles?.color;
        }

        return styles;
    }, [glbFieldStyles, fieldStyle, field.disabled, field.readOnly, field.locked]);
    // --- Memoized Calculations --- END ---

    if (!Field) {
        return <></>;
    }

    if (transactionField?.data?.collectionItemExternalId) {
        return (
            <CollectionSummaryFieldRenderer
                field={field}
                transactionField={transactionField}
                fieldLabel={field.label}
                sire2Mode={sire2Mode}
            />
        );
    }

    if (isCollectionOrCriteria && !selectedFieldNames?.includes(field?.name as string)) {
        const LabelField = LabelWidgets[field.type as keyof typeof LabelWidgets];
        const formValue = getValues(field?.name ?? fieldId);

        if (LabelField && field.type !== FieldTypeEnum.Rollup)
            return (
                <LabelField
                    field={field}
                    transactionField={transactionField as NFormTransaction.IFormTransactionField}
                    validationIcon={validationIcon}
                    fieldName={field.name as string}
                    formId={formId}
                    tabIndex={tabIndex}
                    formValue={
                        [
                            FieldTypeEnum.Text,
                            FieldTypeEnum.TextArea,
                            FieldTypeEnum.Number,
                            FieldTypeEnum.TimePicker,
                            FieldTypeEnum.Duration,
                        ].includes(field.type)
                            ? formValue
                            : null
                    }
                />
            );
    }

    const onSetting = () => {
        setSelectedAdditionalField(formId, field);
    };

    const parseToFieldValue = (
        fieldType: FieldTypeEnum,
        event: any,
        value: any,
    ): {
        fieldValue: any;
        fieldOptionIds: string[];
    } => {
        switch (fieldType) {
            case FieldTypeEnum.Text:
                return {
                    fieldValue: event?.target?.value,
                    fieldOptionIds: [],
                };
            case FieldTypeEnum.Number:
                return {
                    fieldValue: event,
                    fieldOptionIds: [],
                };
            case FieldTypeEnum.Lookup:
            case FieldTypeEnum.Select:
            case FieldTypeEnum.UserLookup:
            case FieldTypeEnum.RoleLookup:
                if (isArray(value)) {
                    return {
                        fieldValue: value.map((v) => v.label).join(', '),
                        fieldOptionIds: value.map((v) => v.value),
                    };
                }
                return {
                    fieldValue: value?.label,
                    fieldOptionIds: [value?.value],
                };
            case FieldTypeEnum.Checkbox:
                return {
                    fieldValue: event?.target?.checked ?? value,
                    fieldOptionIds: [],
                };
            default:
                return {
                    fieldValue: value,
                    fieldOptionIds: [],
                };
        }
    };

    const onChange = (event: any, value: any) => {
        if (isCollectionOrCriteria && transactionField) {
            const { fieldValue, fieldOptionIds } = parseToFieldValue(field.type, event, value);

            updateTransactionFieldMap(formId, [
                {
                    ...transactionField,
                    fieldValue,
                    fieldOptionIds,
                },
            ]);
        }
        if (field.type === FieldTypeEnum.UserLookup) {
            setData?.(fieldId, value);
        }
        if (onChangeProp) {
            onChangeProp(event, { name: field?.formName ?? fieldId });
            return;
        }
    };

    const onFocus = (event: any, value: any) => {
        if (onFocusProp) {
            onFocusProp(event, { name: field?.formName ?? fieldId });
        }
    };

    const onBlur = (event: any, value: any) => {
        if (
            isCollectionOrCriteria &&
            selectedFieldNames?.length &&
            [
                FieldTypeEnum.Checkbox,
                FieldTypeEnum.Text,
                FieldTypeEnum.Number,
                FieldTypeEnum.TextArea,
                FieldTypeEnum.Lookup,
                FieldTypeEnum.Duration,
                FieldTypeEnum.Select,
            ].includes(field.type as FieldTypeEnum)
        ) {
            if (transactionField) {
                setSelectedFieldNames(formId, field.name as string, 'remove');
            }
        }
        if (onBlurProps) {
            onBlurProps(event, { name: field?.formName ?? fieldId });
        }
    };

    return (
        <Field
            defaultValue={field.defaultValue}
            name={field?.formName ?? fieldId}
            {...fieldProps}
            {...pretreatmentResult.format}
            prefix={validationIcon}
            style={combineStyles}
            rules={rules}
            fields={fields}
            formId={formId}
            disabled={locked}
            activeStage={activeStage}
            availableRoles={availableRoles}
            onSetting={onSetting}
            onChange={onChange}
            onFocus={onFocus}
            onBlur={onBlur}
            hideMessage={hideMessage}
            status={status}
            fieldClassName={fieldClassName}
            mutableValidationValue={+transactionField?.style?.configuration?.icon || +(transactionField?.style?.icon ?? 0)}
            readOnly={isReadOnly}
            sire2Mode={sire2Mode}
            id={field.name}
        />
    );
});

const resolveFieldComponent = (configurabled: boolean | undefined, type: string): { Field?: FC<any> } => {
    if (configurabled) {
        return { Field: AdditionalFormField };
    }

    if (!widgetRendererDefinitions[type]) {
        console.warn(`Field [${type}] was not defined in widget Renderer`);
        return { Field: undefined };
    }

    return widgetRendererDefinitions[type];
};
