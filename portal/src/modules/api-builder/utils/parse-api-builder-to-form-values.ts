import { API_ENDPOINTS, ApiBuilderFlowEnum } from '@/common/enums/api-builder/api-builder.enum';
import { NApiBuilder } from '@/interfaces';
import { ALLOWED_ENDPOINTS, API_ENDPOINT_CONFIGURATION, API_ENDPOINT_METHOD_TYPE, ApiBuilderNodeKeys } from '../constants';
import { convertToEndpointConfiguration } from './api-builder-converters';
import { v4 as uuidv4 } from 'uuid';

export const parseApiBuilderToFormValues = (apiBuilder: NApiBuilder.ApiBuilder, isAccount?: boolean): NApiBuilder.ApiBuilderFormType => {
    if (!apiBuilder) return {};

    const { id, name, description, contextId, contextType, latestApiVersion, subscriptionId } = apiBuilder;

    if (!latestApiVersion) return {};

    const { endpoints = [], configuration = {} } = latestApiVersion;

    let parsedFieldMapping: ReturnType<typeof convertToEndpointConfiguration> = {
        fieldMapping: {
            fields: {},
            collections: {},
            collectionFields: {},
        },
    };

    if (configuration?.fieldMapping) {
        parsedFieldMapping = convertToEndpointConfiguration(configuration);
    }

    Object.entries(API_ENDPOINT_CONFIGURATION).forEach(([key, value]) => {
        const endpoint = endpoints.find((endpoint) => endpoint.endpoint === value.endpoint);
        if (!endpoint) {
            endpoints.push({
                identityId: uuidv4(),
                actionType: value.endpoint,
                name: key,
                method: value.method as API_ENDPOINT_METHOD_TYPE,
                apiVersionId: latestApiVersion?.id as string,
                endpoint: value.endpoint,
                configuration: {
                    isEnabled: false,
                },
            });
        }
    });

    const parsedEndpoints = endpoints
        .filter((endpoint) => ALLOWED_ENDPOINTS.has(endpoint.endpoint as API_ENDPOINTS))
        .map((endpoint) => {
            return {
                ...endpoint,
                configuration: endpoint.configuration ? convertToEndpointConfiguration(endpoint.configuration) : {},
            };
        });

    return {
        id,
        subscriptionId,
        apiVersionId: latestApiVersion?.id,
        [ApiBuilderFlowEnum.Information]: {
            name: name,
            description: description,
            contextId: contextId,
            contextType: contextType,
        },
        [ApiBuilderFlowEnum.Configuration]: {
            [ApiBuilderNodeKeys.endpoints]: parsedEndpoints,
            [ApiBuilderNodeKeys.fieldMapping]: parsedFieldMapping?.fieldMapping ?? {},
        },
    };
};
