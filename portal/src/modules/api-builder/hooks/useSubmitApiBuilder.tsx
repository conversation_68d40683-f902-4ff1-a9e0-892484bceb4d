import { DataProvider, FlowUpdateTypeEnum, WorkspaceFlowEnum } from '@/common/enums';
import { ApiBuilderFlowEnum } from '@/common/enums/api-builder/api-builder.enum';
import { useIsAccount } from '@/common/hooks';
import { ENDPOINT_RESOURCES, NAMESPACES } from '@/constants';
import { NApiBuilder, NWorkspace } from '@/interfaces';
import { useTranslation, useUpdate } from '@refinedev/core';
import { notification } from 'antd';
import { ApiBuilderNodeKeys } from '../constants';
import { convertFromEndpointConfiguration } from '../utils/api-builder-converters';

type SubmitParams = {
    step: ApiBuilderFlowEnum;
    onUpdated?: (type: FlowUpdateTypeEnum) => void;
};
export const useSubmitApiBuilder = (params: SubmitParams) => {
    const { mutate: update, isLoading: isUpdating } = useUpdate();
    const isAccount = useIsAccount();
    const { translate } = useTranslation();

    const BASE_PATH = ENDPOINT_RESOURCES.API_TEMPLATE;
    const { step, onUpdated } = params;

    const submit = (formValues: any, event?: { updatedType?: FlowUpdateTypeEnum } & Partial<React.BaseSyntheticEvent>) => {
        const payload = parseFormValuesToApiBuilderPayload({ step, formValues, isAccount });

        update(
            {
                dataProviderName: DataProvider.Definition,
                resource: BASE_PATH,
                id: formValues.id,
                values: payload,
                errorNotification: false,
                successNotification: false,
            },
            {
                onSuccess: () => {
                    const updatedType = event?.updatedType ?? FlowUpdateTypeEnum.OnSubmit;
                    onUpdated?.(updatedType);
                    if (updatedType === FlowUpdateTypeEnum.OnSubmit) {
                        notification.success({
                            message: translate('notifications.api_builder_updated', { ns: NAMESPACES.API_BUILDER }),
                            type: 'success',
                        });
                    }
                },
            },
        );
    };

    return { submit, isUpdating };
};

export const parseFormValuesToApiBuilderPayload = ({
    step,
    formValues,
    isAccount,
}: {
    step: ApiBuilderFlowEnum;
    formValues: NApiBuilder.ApiBuilderFormType;
    isAccount?: boolean;
}): NApiBuilder.UpdateApiBuilderType => {
    if (!formValues.id) throw new Error('ApiBuilder ID not found');

    const payload: NApiBuilder.UpdateApiBuilderType = {
        id: formValues?.id ?? '',
        step,
        apiVersionId: formValues.apiVersionId,
        stepData: {},
    };

    switch (step) {
        case ApiBuilderFlowEnum.Information: {
            if (!formValues[WorkspaceFlowEnum.Information]) break;
            const { description, name } = formValues[WorkspaceFlowEnum.Information];
            const data: NWorkspace.UpdateWorkspaceInformationType = {
                name,
                description,
            };
            payload.stepData = data;
            break;
        }

        case ApiBuilderFlowEnum.Configuration: {
            const endpoints = formValues?.[ApiBuilderFlowEnum.Configuration]?.[ApiBuilderNodeKeys.endpoints];
            const fieldMapping = formValues?.[ApiBuilderFlowEnum.Configuration]?.[ApiBuilderNodeKeys.fieldMapping];

            const parsedFieldMapping = convertFromEndpointConfiguration({ fieldMapping });

            payload.stepData = {
                configuration: {
                    ...parsedFieldMapping,
                },
                endpoints: endpoints,
            };

            break;
        }

        default:
            throw new Error('Not implemented');
    }

    return payload;
};
