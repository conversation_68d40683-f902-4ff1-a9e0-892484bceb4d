/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { NAMESPACES } from '@/constants';
import { useWfsStore } from '@/stores/useWfsStore';
import { useTranslation } from '@refinedev/core';
import { <PERSON><PERSON>, Drawer, DrawerProps, Popconfirm, Tabs, TabsProps } from 'antd';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Edge, MarkerType, Node } from 'reactflow';
import { NodeTypeEnum } from '../../enums/node-type.enum';
import { TransitionTypeEnum } from '../../enums/transition-type.enum';
import { EdgeType } from '../types';
import { WfsWidgetDefinitions } from './WfsWidgetDefinitions';
import { KpiSetting } from './kpi-setting';

type Props = Omit<DrawerProps, 'children' | 'title'>;
const getNodeById = (nodes: Node[], id: string | undefined): Node | undefined => {
    return nodes.find((node) => node.id === id);
};

const getEdgeById = (edges: Edge[], id: string | undefined): Edge | undefined => {
    return edges.find((edge) => edge.id === id);
};
export const WfsSettingDrawer: React.FC<Props> = (props: Props) => {
    const { translate } = useTranslation();
    const { selectedNodeId, setSelectedNodeId, selectedEdgeId, deleteNode, setNode, nodes, edges, setEdge, setSelectedEdgeId, deleteEdge } =
        useWfsStore();

    const selectedNode = getNodeById(nodes, selectedNodeId);
    const selectedEdge = getEdgeById(edges, selectedEdgeId);

    const [items, setItems] = useState<TabsProps['items']>([]);
    const methods = useForm();
    const { handleSubmit, reset } = methods;

    useEffect(() => {
        const items: TabsProps['items'] = [];
        let objectKey = '';

        if (selectedNode) {
            objectKey = selectedNode.type?.toString() ?? '';

            reset({
                ...selectedNode,
            });
        } else if (selectedEdge) {
            objectKey = selectedEdge.type?.toString() ?? EdgeType.Default;

            reset({
                ...selectedEdge,
            });
        } else return;

        const { DisplaySettings } = WfsWidgetDefinitions[objectKey];

        DisplaySettings &&
            items?.push({
                key: 'DisplaySettings',
                label: 'Display settings',
                children: <DisplaySettings />,
            }) &&
            items?.push({
                key: 'KpiSettings',
                label: 'KPI Settings',
                children: <KpiSetting />,
            });

        setItems([...items]);
    }, [selectedNodeId, selectedEdgeId, nodes, edges]);

    const onSubmit = (values: any) => {
        // Submit Node
        if (selectedNode) {
            handleSubmitNode(values);
            // Submit Edge
        } else if (selectedEdge) {
            handleSubmitEdge(values);
        }
    };

    const handleSubmitNode = (values: any) => {
        setNode({ ...values });
        setSelectedNodeId();
    };

    const handleSubmitEdge = (values: any) => {
        const updateEdge: Edge = values;

        if (updateEdge?.data?.transitionType === TransitionTypeEnum.TWO_WAY) {
            updateEdge.markerStart = { type: MarkerType.ArrowClosed };
            updateEdge.markerEnd = { type: MarkerType.ArrowClosed };
        }

        if (updateEdge?.style?.stroke) {
            if (updateEdge.markerStart) updateEdge.markerStart = { type: MarkerType.ArrowClosed, color: updateEdge.style?.stroke };
            if (updateEdge.markerEnd) updateEdge.markerEnd = { type: MarkerType.ArrowClosed, color: updateEdge.style?.stroke };
        }

        setEdge({ ...updateEdge });
        setSelectedEdgeId();
    };

    const handleRemove = () => {
        if (selectedNodeId) {
            deleteNode(selectedNodeId);
            setSelectedNodeId();
        } else if (selectedEdgeId) {
            deleteEdge(selectedEdgeId);
            setSelectedEdgeId();
        }
    };

    return (
        <Drawer
            {...props}
            closable={false}
            closeIcon={null}
            title={
                <div className="flex justify-between items-center">
                    <span className="text-lg">{translate('configurations', { ns: NAMESPACES.FORM })}</span>
                    <span className="flex gap-2">
                        <Popconfirm
                            title="Delete field"
                            description="Are you sure to continue?"
                            onConfirm={handleRemove}
                            okText={translate('yes')}
                            cancelText={translate('no')}
                        >
                            <Button disabled={selectedNode?.type === NodeTypeEnum.Start || selectedNode?.type === NodeTypeEnum.End} danger>
                                {translate('button.remove')}
                            </Button>
                        </Popconfirm>
                        {/* <Button type="primary" onClick={handleSubmit(onSubmit)}>
                            {translate('button.save')}
                        </Button> */}
                        <Button type="default" onClick={handleSubmit(onSubmit)}>
                            {translate('button.close')}
                        </Button>
                    </span>
                </div>
            }
        >
            <FormProvider {...methods}>
                <Tabs items={items} />
            </FormProvider>
        </Drawer>
    );
};
