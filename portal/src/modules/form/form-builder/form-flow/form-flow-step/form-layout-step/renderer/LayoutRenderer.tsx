import { BorderWidth, LegendPosition, ZoneTypeEnum } from '@/common/enums';
import { AccessControlType } from '@/common/enums/access-control-type.enum';
import { FieldViewMode } from '@/common/enums/fieldViewMode.enum';
import { useElementSize, useZoneTypeLabels } from '@/common/hooks';
import { NAMESPACES, RowHeightZoneBuilder } from '@/constants';
import { IRole, NForm } from '@/interfaces';
import { parseArrayToObject } from '@/modules/shared/form-builder/utils';
import { useFormRendererStore } from '@/stores';
import { applyAclToFields, getAvailableRoles } from '@/utils';
import { applyAclToAdditionalFields } from '@/utils/apply-acl-to-additional-fields';
import { applyAclToWidget } from '@/utils/apply-acl-to-widget';
import { useTranslation } from '@refinedev/core';
import { Button, Empty, Result } from 'antd';
import { cloneDeep, isEmpty, toLower, trim, uniqBy } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import GridLayout from 'react-grid-layout';
import { useFormContext } from 'react-hook-form';
import { useFormVersionRoleACL } from '../../../../../../../common/hooks/useFormVersionRoleACL';
import { determineLayoutZoneVisibilities } from '../../../../../../shared/form-builder/utils/determiniLayoutZoneVisibilities';
import { LayoutZoneRenderer } from '../../../../../../shared/layout-renderer/LayoutRenderer';
import { CollectionZoneRenderer } from '../../../form-flow-collection/renderers/CollectionZoneRenderer';
import { FieldZoneFormFieldType } from '../widget-setting/field/FieldZoneSetting';
import { RelationFormConfigType } from '../widget-setting/types/RelationFormFieldType';
import { CollectionFieldRenderer } from './CollectionFieldRenderer';
import { FieldsRenderer } from './FieldsRenderer';
import { FileViewerZoneRenderer } from './FileViewerRenderer/FileViewerZoneRenderer';
import { RelationFormRenderer } from './RelationFormRenderer/RelationFormRenderer';
import { RelationFormRendererPreview } from './RelationFormRendererPreview/RelationFormRendererPreview';
import { SummaryZoneRenderer } from './SummaryRenderer/SummaryRenderer';
import { TransactionHeader } from './TransactionHeader';
import { WidgetRenderer } from './WidgetRenderer';
import { WorkFlowElementPreview } from './WorkFlowElementPreview';
import { WorkFlowElementRenderer } from './WorkFlowElementRenderer';
import { TabRenderer } from './tab-renderer/TabRenderer';
import { InstructionRender } from './InstructionRender';

export const MAX_ROW_HEIGHT = 50;
export type LayoutRendererProps = {
    isPreview?: boolean;
    formId: string;
    transactionId?: string;
    defaultLayout?: NForm.Widgets;
    loading?: boolean;
    hideHeader?: boolean;
    isTabRenderer?: boolean;
    containerId?: string;
    containerWidth?: number;
    afterClose?: () => void;
    applyAcl?: boolean;
    collectionItemIdentityId?: string;
    rowKey?: string;
    relatedLoading?: boolean;
    relatedFormIds?: string[];
    sire2Mode?: FieldViewMode;
};

export const LAYOUT_RENDER_CONTAINER_ID = 'LayoutRendererContainer';

export const LayoutRenderer: React.FC<LayoutRendererProps> = (props: LayoutRendererProps) => {
    const { translate } = useTranslation();
    const containerId = props?.containerId ?? `${LAYOUT_RENDER_CONTAINER_ID}_${props.formId}`;
    const { width } = useElementSize(containerId);

    const layoutContainerWidth = props.containerWidth || width;

    const {
        formId,
        defaultLayout,
        loading,
        hideHeader,
        isTabRenderer,
        isPreview,
        applyAcl = true,
        collectionItemIdentityId,
        rowKey,
        sire2Mode,
    } = props;

    const { formStates, userRoles, transactionsLoading, setActiveRole, setOpenSelectRole, setFields, setActiveStageRole } =
        useFormRendererStore();
    const zoneTypeLabels = useZoneTypeLabels();
    const {
        relatedForms,
        fields,
        activeRole,
        activeStage,
        stageRoles,
        layouts,
        roles,
        rootFields,
        selectedCollectionFields,
        stageActiveAccessControls = [],
        transactionId,
        formVersionId,
        transaction,
        isTest,
    } = formStates[formId];

    const widgetStageActiveAccessControls = stageActiveAccessControls.filter(
        (stage) => stage.stageId === activeStage && stage.type === AccessControlType.WIDGET,
    );

    const [acls, setAcls] = useState<NForm.StageRoleAccessControl[]>([]);

    const { stageRole, isLoading: isLoadingRoleAcl } = useFormVersionRoleACL({
        formVersionId,
        stageId: activeStage,
        roleId: activeRole.id,
    });

    useEffect(() => {
        if (transactionsLoading?.[transaction?.id as string]) {
            setActiveStageRole(formId, stageRole);
        }
    }, [transactionsLoading?.[transaction?.id as string]]);

    useEffect(() => {
        setActiveStageRole(formId, stageRole);
    }, [stageRole, formId]);

    const activeLayout: NForm.Widgets = useMemo(() => {
        if (!isEmpty(defaultLayout)) return defaultLayout;
        if (isTabRenderer && !isPreview) return defaultLayout || {};

        if (!stageRole || !layouts?.length) return defaultLayout || {};

        const layout = layouts.find((layout) => layout.id === stageRole.layoutId);

        if (!layout?.layoutZones?.length) return defaultLayout || {};

        const layoutZones = layout?.layoutZones;

        return parseArrayToObject(layoutZones, 'id');
    }, [defaultLayout, layouts, stageRole, isTabRenderer, isPreview]);

    const { getValues } = useFormContext();

    const formValues = getValues();

    const mapActiveLayout = useMemo(() => {
        const _activeLayout: { [id: string]: boolean } = {};
        if (activeLayout) {
            for (const layout of Object.values(activeLayout)) {
                let _visibility = true;
                const visibility = toLower(trim(layout?.config?.visibility ?? 'true')) === 'true';
                const visibilityConditions = layout?.config?.visibilityConditions;

                _visibility = determineLayoutZoneVisibilities({
                    visibility,
                    visibilityConditions,
                    formValues: cloneDeep(formValues),
                    fields,
                });

                _activeLayout[layout.id] = _visibility;
            }
        }

        return _activeLayout;
    }, [activeLayout, fields, formValues]);

    const availableRoles = useMemo(() => getAvailableRoles({ userRoles, roles }), [userRoles, roles]);

    const availableStageRoles = uniqBy(
        stageRoles.map((item) => {
            return {
                id: item.roleId,
                name: item.roleName,
            } as IRole;
        }),
        'id',
    );

    useEffect(() => {
        if (stageRole?.accessControls) {
            let editable = true;
            if (formStates[formId].originalTransactionId) {
                Object.keys(formStates).forEach((fid) => {
                    if (formStates[fid].transactionId === formStates[formId].originalTransactionId) {
                        const relationACLs = formStates[fid].stageAccessControls.filter(
                            (acl) =>
                                acl.type === AccessControlType.RELATION &&
                                formStates[fid].activeStage === acl.stageId &&
                                acl.targetId === formId &&
                                acl.config?.editable === false,
                        );

                        if (relationACLs.length) {
                            editable = false;
                        }
                    }
                });
            }

            const _acls = (stageRole?.accessControls ?? []).reduce((acc, acl) => {
                const newAcl = { ...acl };

                if (acl.config && !editable) {
                    newAcl.config = { ...acl.config, ...{ editable } };
                }

                acc.push(newAcl);
                return acc;
            }, [] as NForm.StageRoleAccessControl[]);

            if (!editable) {
                _acls.push({
                    type: AccessControlType.TRANSACTION,
                    targetId: '',
                    stageRoleId: '',
                    config: {
                        visible: true,
                        editable: editable,
                        required: false,
                    },
                });
            }

            setAcls(_acls);
        }
    }, [stageRole?.accessControls]);

    useEffect(() => {
        if (activeRole.id) return;

        if (availableRoles.length > 1) {
            setOpenSelectRole(formId, true);
        } else if (availableRoles.length === 1) {
            setActiveRole(formId, availableRoles[0]);
        }
    }, [activeRole.id, availableRoles, formId]);

    useEffect(() => {
        if (isEmpty(rootFields) || !stageRole || !applyAcl) {
            return;
        }

        const fieldWithAcls = applyAclToFields({
            rootFields,
            stageRoleAccessControls: acls ?? [],
            stageActiveAccessControls: stageActiveAccessControls,
        });

        setFields(formId, fieldWithAcls);
    }, [formId, rootFields, stageRole, acls, applyAcl]);

    const renderWidget = ({
        identityId,
        layoutFields,
        showLegend,
        zoneName,
        legendPosition,
        backgroundColor,
        borderColor,
        borderWidth,
        fieldBackgroundColor,
    }: {
        identityId: string;
        layoutFields?: NForm.WidgetFieldType[];
        showLegend: boolean;
        zoneName?: string;
        legendPosition: LegendPosition;
        backgroundColor?: string;
        fieldBackgroundColor?: string;
        borderWidth?: BorderWidth;
        borderColor?: string;
    }) => {
        if (!layoutFields || !layoutFields[0]) return <></>;
        const widget = props.isPreview
            ? layoutFields[0]
            : applyAclToWidget({ stageRoleAccessControls: stageRole?.accessControls ?? [], widget: layoutFields[0] });

        if (!widget) {
            return (
                <div key={identityId}>
                    <LayoutZoneRenderer
                        showLegend={showLegend}
                        zoneName={zoneName ?? ''}
                        legendPosition={legendPosition}
                        backgroundColor={backgroundColor}
                        borderColor={borderColor}
                        borderWidth={borderWidth}
                    ></LayoutZoneRenderer>
                </div>
            );
        }

        return (
            <div key={identityId}>
                <LayoutZoneRenderer
                    showLegend={showLegend}
                    zoneName={zoneName ?? ''}
                    legendPosition={legendPosition}
                    backgroundColor={backgroundColor}
                    borderColor={borderColor}
                    borderWidth={borderWidth}
                >
                    <WidgetRenderer
                        widget={widget}
                        styles={{
                            backgroundColor,
                            fieldBackgroundColor,
                        }}
                        roleId={activeRole.id}
                        isPreview={props.isPreview}
                        widgetStageActiveAccessControls={widgetStageActiveAccessControls}
                        transactionId={transactionId || ''}
                    />
                </LayoutZoneRenderer>
            </div>
        );
    };

    const invalidConfiguration = () => {
        return (!activeRole.id || isEmpty(activeLayout)) && !loading && !isLoadingRoleAcl;
    };

    const getErrorMessage = () => {
        const messages = [];

        if (!activeRole?.id || isEmpty(activeLayout)) {
            messages.push(<>{translate('errors.no_permission', { ns: NAMESPACES.FORM })}</>);
        }

        return (
            <>
                {messages.map((e) => {
                    return (
                        <>
                            {e}
                            <br />
                        </>
                    );
                })}

                {availableRoles.length > 1 && (
                    <section>
                        <Button onClick={() => setOpenSelectRole(formId, true)} type="text">
                            {translate('change_role', { ns: NAMESPACES.FORM })}
                        </Button>
                    </section>
                )}
            </>
        );
    };

    const aclToAdditionalFields = useMemo(() => {
        return applyAclToAdditionalFields({
            rootFields: selectedCollectionFields,
            stageRoleAccessControls: acls,
            collectionItemIdentityId: collectionItemIdentityId as string,
            stageActiveAccessControls: stageActiveAccessControls,
        });
    }, [selectedCollectionFields, acls, collectionItemIdentityId, stageActiveAccessControls]);

    return (
        <>
            <section id={containerId} className="h-full">
                {invalidConfiguration() && (isTabRenderer ? <Empty /> : <Result status="403" title={getErrorMessage()} />)}
                {activeRole.id && !isEmpty(activeLayout) && (
                    <>
                        {!hideHeader && <TransactionHeader formId={formId} />}
                        {layoutContainerWidth > 0 && (
                            <GridLayout
                                cols={12}
                                rowHeight={RowHeightZoneBuilder}
                                margin={[4, 4]}
                                width={layoutContainerWidth}
                                useCSSTransforms={false}
                                isDraggable={false}
                                isResizable={false}
                                layout={Object.values(activeLayout).map(
                                    (f) =>
                                        ({
                                            ...(f.dataGrid ?? f.config?.dataGrid),
                                            // h: Math.min(f.dataGrid?.h ?? f.config?.dataGrid?.h, MAX_ROW_HEIGHT),
                                            h: f.dataGrid?.h ?? f.config?.dataGrid?.h,
                                            isResizable: false,
                                            isDraggable: false,
                                        }) as GridLayout.Layout,
                                )}
                            >
                                {Object.keys(activeLayout).map((key) => {
                                    const {
                                        type,
                                        id,
                                        layoutFields,
                                        identityId,
                                        config,
                                        name: zoneName,
                                        parentIdentityId,
                                    } = activeLayout[key] as NForm.ZoneType;
                                    const showLegend = !!config?.showZoneNameAsLegend;
                                    const legendPosition = config?.legendPosition;
                                    const borderWidth = config?.borderWidth;
                                    const borderColor = config?.borderColor;
                                    const backgroundColor = config?.backgroundColor;
                                    const fieldBackgroundColor = config?.fieldBackgroundColor;

                                    const visibility = mapActiveLayout[id];

                                    // include in Tab
                                    if (!isTabRenderer && parentIdentityId) return <></>;

                                    switch (type) {
                                        case ZoneTypeEnum.Field: {
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        visibility={visibility}
                                                    >
                                                        <FieldsRenderer
                                                            id={id}
                                                            fields={fields}
                                                            selectedFields={layoutFields as unknown as Array<FieldZoneFormFieldType>}
                                                            formId={formId}
                                                            glbFieldStyles={{
                                                                backgroundColor: fieldBackgroundColor,
                                                            }}
                                                            availableRoles={availableStageRoles}
                                                        />
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }

                                        case ZoneTypeEnum.CollectionField: {
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        visibility={visibility}
                                                    >
                                                        <CollectionFieldRenderer
                                                            id={id}
                                                            fields={aclToAdditionalFields}
                                                            selectedFields={layoutFields as unknown as Array<FieldZoneFormFieldType>}
                                                            formId={formId}
                                                            glbFieldStyles={{
                                                                backgroundColor: fieldBackgroundColor,
                                                            }}
                                                            availableRoles={availableStageRoles}
                                                            collectionItemIdentityId={collectionItemIdentityId}
                                                            rowKey={rowKey}
                                                            sire2Mode={sire2Mode}
                                                        />
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }

                                        case ZoneTypeEnum.Toolbar: {
                                            const editable =
                                                acls.findIndex(
                                                    (acl) => acl.config?.editable === false && acl.type === AccessControlType.TRANSACTION,
                                                ) === -1;

                                            if (!editable) {
                                                return <></>;
                                            }

                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                    >
                                                        {props.isPreview ? (
                                                            <WorkFlowElementPreview formId={formId} backgroundColor={backgroundColor} />
                                                        ) : (
                                                            <WorkFlowElementRenderer
                                                                formId={formId}
                                                                layoutZoneIdentityId={identityId}
                                                                backgroundColor={backgroundColor}
                                                                afterClose={props.afterClose}
                                                                isTest={isTest}
                                                            />
                                                        )}
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }
                                        case ZoneTypeEnum.Summary: {
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        visibility={visibility}
                                                    >
                                                        <SummaryZoneRenderer
                                                            selectedFields={(layoutFields ?? []) as any[]}
                                                            isPreview={props.isPreview}
                                                            formId={formId}
                                                            formConfig={config as NForm.SummaryZoneConfigs}
                                                        />
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }

                                        case ZoneTypeEnum.Collection: {
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        visibility={visibility}
                                                    >
                                                        <CollectionZoneRenderer
                                                            isPreview={props.isPreview}
                                                            layoutFields={layoutFields ?? []}
                                                            formId={formId}
                                                            stageRole={stageRole}
                                                            zoneStyles={{
                                                                backgroundColor,
                                                                fieldBackgroundColor,
                                                            }}
                                                            identityId={identityId}
                                                            availableFilterIds={config?.filters}
                                                            groupByIds={config?.groups ?? []}
                                                            automation={config?.automation ?? []}
                                                        />
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }

                                        case ZoneTypeEnum.Relation: {
                                            if (props.isPreview) {
                                                return (
                                                    <div key={identityId}>
                                                        <LayoutZoneRenderer
                                                            showLegend={showLegend}
                                                            zoneName={zoneName ?? ''}
                                                            legendPosition={legendPosition}
                                                            borderColor={borderColor}
                                                            borderWidth={borderWidth}
                                                            backgroundColor={backgroundColor}
                                                            visibility={visibility}
                                                        >
                                                            <RelationFormRendererPreview
                                                                formId={formId}
                                                                formConfig={config as RelationFormConfigType}
                                                                selectedFields={(layoutFields ?? []) as any[]}
                                                                forms={relatedForms}
                                                            />
                                                        </LayoutZoneRenderer>
                                                    </div>
                                                );
                                            }

                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        visibility={visibility}
                                                    >
                                                        {props.transactionId ? (
                                                            <RelationFormRenderer
                                                                transactionId={props.transactionId}
                                                                formId={formId}
                                                                formConfig={config as RelationFormConfigType}
                                                                selectedFields={(layoutFields ?? []) as any[]}
                                                                forms={relatedForms}
                                                                relatedLoading={props?.relatedLoading}
                                                                relatedFormIds={props?.relatedFormIds}
                                                            />
                                                        ) : (
                                                            <></>
                                                        )}
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }
                                        case ZoneTypeEnum.Widget: {
                                            return renderWidget({
                                                identityId,
                                                layoutFields,
                                                showLegend,
                                                zoneName,
                                                legendPosition,
                                                backgroundColor,
                                                borderColor,
                                                borderWidth,
                                            });
                                        }

                                        case ZoneTypeEnum.FilePreview: {
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        visibility={visibility}
                                                    >
                                                        <FileViewerZoneRenderer
                                                            transactionId={props.transactionId}
                                                            selectedFields={(layoutFields ?? []) as any[]}
                                                            isPreview={props.isPreview}
                                                            formId={formId}
                                                            acls={acls}
                                                        />
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }

                                        case ZoneTypeEnum.Tab: {
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        type={type as ZoneTypeEnum}
                                                        visibility={visibility}
                                                    >
                                                        <TabRenderer
                                                            identityId={identityId}
                                                            formId={formId}
                                                            isPreview={props.isPreview}
                                                            activeLayout={activeLayout}
                                                            layoutProps={cloneDeep(props)}
                                                            mapActiveLayout={mapActiveLayout}
                                                        ></TabRenderer>
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );
                                        }

                                        case ZoneTypeEnum.Instruction:
                                            return (
                                                <div key={identityId}>
                                                    <LayoutZoneRenderer
                                                        showLegend={showLegend}
                                                        zoneName={zoneName ?? ''}
                                                        legendPosition={legendPosition}
                                                        borderColor={borderColor}
                                                        borderWidth={borderWidth}
                                                        backgroundColor={backgroundColor}
                                                        type={type as ZoneTypeEnum}
                                                        visibility={visibility}
                                                    >
                                                        <InstructionRender content={config?.content ?? ''} />
                                                    </LayoutZoneRenderer>
                                                </div>
                                            );

                                        default:
                                            return (
                                                <div key={identityId} id={identityId} className="overflow-auto shadow">
                                                    <div className="w-full h-full bg-neutral-200">
                                                        {zoneTypeLabels[type as ZoneTypeEnum]}
                                                    </div>
                                                </div>
                                            );
                                    }
                                })}
                            </GridLayout>
                        )}
                    </>
                )}
            </section>
        </>
    );
};
