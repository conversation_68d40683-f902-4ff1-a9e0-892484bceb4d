import { ZoneTypeEnum } from '@/common/enums';
import { useGetRelationForms } from '@/common/hooks';
import { Icon } from '@/components/icon';
import { DeleteOutlined, NAMESPACES, NON_LOGIC_FIELD_TYPES } from '@/constants';
import { ZONE_FORM_ITEM_NAMES } from '@/constants/widget';
import { CheckboxFormField, SelectFormField, TextFormField } from '@/modules/form-fields';
import { EditorFormField } from '@/modules/form-fields/EditorFormField';
import { BackgroundColorSetting, BorderSetting, LegendSetting } from '@/modules/shared/layout-builder';
import { useTranslation } from '@refinedev/core';
import { Collapse, CollapseProps, Popconfirm } from 'antd';
import { uniq } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { LayoutZoneFieldProps, ZoneProps } from '../../LayoutZoneSetting';
import { RelationZoneGroupField } from './RelationZoneGroupField';

export type RelationZoneFormFieldType = LayoutZoneFieldProps;

export const RelationZoneSetting: React.FC<ZoneProps> = ({ formName, formTemplateId, formVersionId }) => {
    const { translate } = useTranslation();
    const [activeGroupIds, setActiveGroupIds] = useState<string[]>([]);
    const editorRef = useRef<any>(null);

    const { setValue, getValues } = useFormContext();

    const relatedFormsData = useGetRelationForms({
        formTemplateId,
        formVersionId,
    });

    const zoneNameFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.Name}`;
    const zoneFieldsFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.Fields}`;
    const selectedRelationIdsFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.SelectedIds}`;
    const showDisplayTextInDropZoneFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.ShowDisplayTextInDropZone}`;
    const disableCreateButton = `${formName}.${ZONE_FORM_ITEM_NAMES.DisableCreateButton}`;
    const displayTextInDropZoneFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.DisplayTextInDropZone}`;

    const isShowDisplayTextInDropZone = useWatch({
        name: showDisplayTextInDropZoneFormName,
    });

    const selectedRelationIdsValue: string[] =
        useWatch({
            name: selectedRelationIdsFormName,
        }) || [];

    useFieldArray({
        name: zoneFieldsFormName,
    });

    const groupConfigFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.GroupConfig.Name}`;
    useFieldArray({
        name: groupConfigFormName,
    });

    const groupConfigs =
        (useWatch({
            name: groupConfigFormName,
        }) as { order: number; formId: string }[]) || [];

    const relatedForms = relatedFormsData?.data ?? [];

    const watchZoneFieldsArrValue =
        (useWatch({
            name: zoneFieldsFormName,
        }) as RelationZoneFormFieldType[]) || [];

    const availableRelationForms = (relatedForms || []).map((relatedForm) => {
        return {
            label: relatedForm.name,
            value: relatedForm.id,
        };
    });

    const onSelectForm = (value: string) => {
        const relatedForm = relatedForms.find((rf) => rf.id === value);
        if (relatedForm) {
            setActiveGroupIds(uniq([...activeGroupIds, relatedForm?.id as string]));
            setValue(selectedRelationIdsFormName, [...selectedRelationIdsValue, relatedForm.id as string]);
        }
    };

    const onDeselectForm = (value: string) => {
        const newSelectedFormIds = selectedRelationIdsValue.filter((f) => f !== value);
        const newZoneFields = watchZoneFieldsArrValue.filter((f) => f.config?.formId !== value);
        setActiveGroupIds(newSelectedFormIds);
        setValue(selectedRelationIdsFormName, newSelectedFormIds);
        setValue(zoneFieldsFormName, newZoneFields);

        const newGroupConfigs = groupConfigs.filter((config) => config.formId !== value);
        setValue(groupConfigFormName, newGroupConfigs);
    };

    const formIds = uniq(watchZoneFieldsArrValue.map((f) => f.config?.formId as string).filter(Boolean)) || [];

    useEffect(() => {
        setActiveGroupIds(selectedRelationIdsValue);
    }, [selectedRelationIdsValue.join(',')]);

    useEffect(() => {
        const selectedRelationIdsValue = getValues(selectedRelationIdsFormName);
        if (selectedRelationIdsValue === undefined) {
            setValue(selectedRelationIdsFormName, formIds);
        }
    }, [formIds.join(',')]);

    const genConfirm = (formId: string) => (
        <div onClick={(e) => e.stopPropagation()}>
            <Popconfirm
                title={translate('delete_form', { ns: NAMESPACES.FORM })}
                description={translate('delete_form_confirm', { ns: NAMESPACES.FORM })}
                onConfirm={() => onDeselectForm(formId)}
                okText={translate('yes')}
                cancelText={translate('no')}
            >
                <Icon icon={DeleteOutlined} />
            </Popconfirm>
        </div>
    );

    const sortedSelectRelationIdsValue = selectedRelationIdsValue.sort((a, b) => {
        const aIndex = (groupConfigs || []).find((config) => config.formId === a)?.order ?? 0;
        const bIndex = (groupConfigs || []).find((config) => config.formId === b)?.order ?? 0;
        return aIndex - bIndex;
    });

    const groupRelatedFormItems: CollapseProps['items'] = sortedSelectRelationIdsValue.map((formId, index) => {
        const form = relatedForms.find((f) => f.id === formId);
        const fields = form?.activeFormVersion?.fields?.filter((f) => !NON_LOGIC_FIELD_TYPES.includes(f.type)) || [];

        return {
            key: formId,
            id: formId,
            label: form?.name,
            children: (
                <RelationZoneGroupField
                    id={formId}
                    fields={fields}
                    zoneFieldsFormName={zoneFieldsFormName}
                    key={formId}
                    formName={`${formName}.${ZONE_FORM_ITEM_NAMES.SelectedFieldIds}.${index}`}
                    formVersionId={form?.activeFormVersion?.id as string}
                    zoneFormName={formName}
                    index={index}
                />
            ),
            extra: genConfirm(formId),
            className: 'min',
        };
    });

    return (
        <section className="w-full h-full flex flex-col gap-y-2">
            <div className="w-full flex gap-x-3">
                <TextFormField
                    name={zoneNameFormName}
                    className="w-96"
                    label={translate('layout.name', { ns: NAMESPACES.FORM })}
                    rules={{
                        required: {
                            value: true,
                            message: translate('validations.required', { ns: NAMESPACES.FORM }),
                        },
                    }}
                />
                <LegendSetting formControlName={formName} />
                <BorderSetting formControlName={formName} />
                <BackgroundColorSetting formControlName={formName} zoneType={ZoneTypeEnum.Relation} />
            </div>

            <div className="flex w-full gap-x-3">
                <div className="flex-1">
                    <SelectFormField
                        className="w-full"
                        name={selectedRelationIdsFormName}
                        options={availableRelationForms}
                        label={translate('add_form', { ns: NAMESPACES.FORM })}
                        mode="multiple"
                        isConfirmToSelect={true}
                        onSelect={onSelectForm}
                        onDeselect={onDeselectForm}
                    />
                </div>
            </div>

            <div className="flex flex-col w-full">
                <CheckboxFormField
                    name={showDisplayTextInDropZoneFormName}
                    label={translate('settings.show_display_text_in_drop_zone', { ns: NAMESPACES.FORM })}
                    hideMessage={true}
                />
                {isShowDisplayTextInDropZone ? (
                    <div className="w-full">
                        <EditorFormField name={displayTextInDropZoneFormName} editorRef={editorRef} key={displayTextInDropZoneFormName} />
                    </div>
                ) : (
                    <></>
                )}
            </div>
            <div className="flex flex-col w-full">
                <CheckboxFormField
                    name={disableCreateButton}
                    label={translate('settings.disable_create_button', { ns: NAMESPACES.FORM })}
                    hideMessage={true}
                />
            </div>

            {groupRelatedFormItems?.length ? (
                <>
                    <Collapse
                        items={groupRelatedFormItems}
                        activeKey={activeGroupIds}
                        onChange={(ids) => {
                            setActiveGroupIds((ids || []) as string[]);
                        }}
                    />
                </>
            ) : (
                <></>
            )}
        </section>
    );
};
