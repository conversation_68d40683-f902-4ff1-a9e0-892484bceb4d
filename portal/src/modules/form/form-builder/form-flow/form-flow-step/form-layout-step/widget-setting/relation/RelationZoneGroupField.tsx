import { FieldTypeEnum, LayoutZoneParentType, ZoneTypeEnum } from '@/common/enums';
import { useElementSize, useTargetFormWithActionLabels } from '@/common/hooks';
import { NAMESPACES } from '@/constants';
import { RELATION_SEPARATORS, ZONE_FORM_ITEM_NAMES } from '@/constants/widget';
import { NForm } from '@/interfaces';
import { CheckboxFormField, SelectFormField, TextFormField } from '@/modules/form-fields';
import { useTranslation } from '@refinedev/core';
import React, { useEffect } from 'react';
import ReactGridLayout from 'react-grid-layout';
import { useFormContext, useWatch } from 'react-hook-form';
import { DISPLAY_DROP_ZONES, useDisplayDropZoneOptions } from '../../../../../../../../common/hooks/useDisplayDropZoneOptions';
import { BackgroundColorSetting } from '../../../../../../../shared/layout-builder';
import { WorkSpaceZone } from '../../zone/WorkSpaceZone';
import { getLayoutField } from '../utils/dataGrid';
import { RelationZoneFormFieldType } from './RelationZoneSetting';

type Props = {
    id: string;
    zoneFieldsFormName: string;
    fields: NForm.FormFieldType[];
    formName: string;
    zoneFormName: string;
    formVersionId: string;
    index: number;
};

export const RelationZoneGroupField: React.FC<Props> = ({
    id,
    zoneFieldsFormName,
    formName,
    zoneFormName,
    formVersionId,
    fields = [],
    index,
}) => {
    const { translate } = useTranslation();
    const { width: resizeWith } = useElementSize(id);
    const { setValue, getValues } = useFormContext();
    const targetFormWithActions = useTargetFormWithActionLabels();
    const { data: displayDropZoneOptions } = useDisplayDropZoneOptions();

    const selectedRelationFieldIdsFormName = `${formName}.${ZONE_FORM_ITEM_NAMES.SelectedIds}`;
    const groupConfigFormName = `${zoneFormName}.${ZONE_FORM_ITEM_NAMES.GroupConfig.Name}`;

    const allowUploadFormName = `${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.AllowUpload}`;

    const mappingFieldFormName = `${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.MappingField}`;

    const displayDropZoneFormName = `${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.DisplayDropZone}`;

    const styleDropZoneFormName = `${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.Style}`;

    const watchZoneFieldsArrValue =
        (useWatch({
            name: zoneFieldsFormName,
        }) as RelationZoneFormFieldType[]) || [];

    const allowUpload = useWatch({ name: allowUploadFormName });

    const watchFormFieldsArrValue = watchZoneFieldsArrValue.filter((f) => f.config?.formId === id) || [];
    const availableFields = fields.map((f) => ({
        label: f.label,
        value: f.fieldId,
    }));

    const mappingFieldOptions = fields
        .filter((field) => field.type === FieldTypeEnum.Document)
        ?.map((field) => ({
            label: field.label,
            value: field.fieldId,
        }));

    useEffect(() => {
        setValue(`${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.FormId}`, id);
        setValue(`${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.Order}`, index);
    }, []);

    useEffect(() => {
        setValue(selectedRelationFieldIdsFormName, watchFormFieldsArrValue.map((f) => f.fieldId) || []);
    }, [watchFormFieldsArrValue.join(',')]);

    const onLayoutChange = (layouts: ReactGridLayout.Layout[], formId: string) => {
        const fieldArrValues = getValues(zoneFieldsFormName) as RelationZoneFormFieldType[];
        const newFieldArrValues = fieldArrValues.map((field) => {
            const layout = layouts?.find((l) => l.i === field.fieldId && field.config?.formId === formId);
            if (layout && field.config) {
                field.config.dataGrid = layout;
            }
            return field;
        });

        setValue(zoneFieldsFormName, newFieldArrValues);
    };

    const onSelectField = (value: string, formId: string) => {
        const fieldArrValue = (getValues(zoneFieldsFormName) as RelationZoneFormFieldType[]) || [];

        const isExistedFieldArrValue = fieldArrValue.some((f) => f.fieldId === value && f.config?.formId === formId);
        const field = fields.find((f) => f.fieldId === value);
        if (isExistedFieldArrValue || !field) return;

        const idx = fieldArrValue.length;
        setValue(zoneFieldsFormName, [
            ...fieldArrValue,
            getLayoutField({
                id: field.fieldId,
                fieldId: field.fieldId,
                idx,
                type: field.type,
                label: field.label,
                parentType: LayoutZoneParentType.RELATION,
                formId: id,
                formVersionId,
                groupId: field.configuration?.groupId,
                fieldConfig: field.configuration,
            }),
        ]);
    };

    const onDeselectField = (value: string, formId: string) => {
        const fieldArrValue = (getValues(zoneFieldsFormName) as RelationZoneFormFieldType[]) || [];
        const newFieldArrValue = fieldArrValue.filter(
            (f) => f.config?.formId !== formId || (f.fieldId !== value && f.config?.formId === formId),
        );
        setValue(zoneFieldsFormName, newFieldArrValue);
    };

    return (
        <section className="w-full h-full flex flex-col">
            <div className="flex w-full gap-x-3">
                <div className="flex-1">
                    <SelectFormField
                        className="w-full"
                        name={selectedRelationFieldIdsFormName}
                        options={availableFields}
                        label={translate('add_fields', { ns: NAMESPACES.FORM })}
                        mode="multiple"
                        onSelect={(value) => onSelectField(value, id)}
                        onDeselect={(value) => onDeselectField(value, id)}
                    />
                </div>
            </div>
            <div className="flex w-full gap-x-3">
                <div className="flex gap-x-2">
                    <TextFormField className="hidden" name={`${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.FormId}`} />
                    <TextFormField className="hidden" name={`${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.Order}`} />
                    <SelectFormField
                        className="w-full min-w-36"
                        name={`${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.Separator}`}
                        label={translate('widget_field.separator', { ns: NAMESPACES.FORM })}
                        options={RELATION_SEPARATORS}
                        defaultValue={RELATION_SEPARATORS[0].value}
                        allowClear
                    />
                    <SelectFormField
                        className="w-full min-w-36"
                        name={`${groupConfigFormName}.${index}.${ZONE_FORM_ITEM_NAMES.GroupConfig.OpenBy}`}
                        label={translate('widget_field.open_form_by', { ns: NAMESPACES.FORM })}
                        options={targetFormWithActions}
                        defaultValue={targetFormWithActions[0].value}
                        allowClear
                    />
                    <CheckboxFormField name={allowUploadFormName} label={translate('allow_upload', { ns: NAMESPACES.FORM })} />

                    {allowUpload && (
                        <>
                            <SelectFormField
                                className="min-w-36"
                                name={mappingFieldFormName}
                                options={mappingFieldOptions}
                                label={translate('document_field', { ns: NAMESPACES.FORM })}
                                rules={{
                                    required: {
                                        value: true,
                                        message: translate('document_field_required', { ns: NAMESPACES.FORM }),
                                    },
                                }}
                            />

                            <SelectFormField
                                className="min-w-52"
                                name={displayDropZoneFormName}
                                options={displayDropZoneOptions}
                                defaultValue={DISPLAY_DROP_ZONES.HIDE_DROP_ZONE}
                                label={translate('display_drop_zone.title', { ns: NAMESPACES.FORM })}
                            />

                            <BackgroundColorSetting formControlName={styleDropZoneFormName} zoneType={ZoneTypeEnum.Relation} />
                        </>
                    )}
                </div>
            </div>
            <article id={id} className="border border-solid border-neutral-300 flex-1">
                <WorkSpaceZone
                    width={resizeWith - 40}
                    value={watchFormFieldsArrValue || []}
                    type={ZoneTypeEnum.Relation}
                    onLayoutChange={(layout) => onLayoutChange(layout, id)}
                    onDeleteField={(fieldId) => onDeselectField(fieldId, id)}
                    zoneFieldsFormName={zoneFieldsFormName}
                    relatedFormFields={fields}
                />
            </article>
        </section>
    );
};
