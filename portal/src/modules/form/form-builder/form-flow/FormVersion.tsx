import { DataProvider, FormBuilderStatus } from '@/common/enums';
import { useIsAccount } from '@/common/hooks';
import { CommentFormDrawer } from '@/components/comment/CommentFormDrawer';
import { ChangeLogsTable } from '@/components/form-change-logs/ChangeLogsTable';
import { FormToolbar, ToolBarProps } from '@/components/form-toolbar';
import { PublishFormDrawer } from '@/components/publish/PublishFormDrawer';
import {
    CheckOutlined,
    CloudUploadOutlined,
    EditOutlined,
    ENDPOINT_RESOURCES,
    NAMESPACES,
    UnlockOutlined,
    LockOutlined,
} from '@/constants';
import { NForm } from '@/interfaces';
import { useOne, useTranslation } from '@refinedev/core';
import { useEffect, useState } from 'react';
import { FormFlowStepProps } from './FormFlow';
import { useFormLock } from '../../hooks/useFormLock';

type Props = { formTemplate: NForm.FormType; toolbarProps: ToolBarProps } & FormFlowStepProps;

export const FormVersion: React.FC<Props> = ({ isSaveStep, onSaveCallBack, onChangeStepCallback, formTemplate, ...props }) => {
    const id = formTemplate?.id as string;

    const [isOpenComment, setIsOpenComment] = useState<boolean>(false);
    const [isOpenPublishForm, setIsOpenPublishForm] = useState<boolean>(false);
    const { translate } = useTranslation();
    const isAccount = useIsAccount();
    const { isCurrentUserHoldingLock, formLock, handleRelease, handleTakeOver } = useFormLock(formTemplate?.id as string);

    const { data: activeFormVersionData, isFetching } = useOne({
        dataProviderName: DataProvider.Definition,
        resource: `${isAccount ? ENDPOINT_RESOURCES.FORM_VERSION_TENANCY : ENDPOINT_RESOURCES.FORM_VERSION}`,
        id: formTemplate?.activeVersionId,
        queryOptions: {
            enabled: !!formTemplate?.activeVersionId,
        },
        meta: {
            includeRelation: false,
        },
    });
    const activeFormVersion = activeFormVersionData?.data as NForm.FormVersionType;

    useEffect(() => {
        if (isSaveStep) {
            onChangeStepCallback?.();
        }
    }, [isSaveStep]);

    const onCreateVersionCallback = () => {
        onSaveCallBack?.();
    };

    const toolbarBoxActions = [];
    if (formTemplate?.status === FormBuilderStatus.Draft && isCurrentUserHoldingLock) {
        toolbarBoxActions.push(
            isAccount
                ? {
                      icon: CheckOutlined,
                      label: translate('button.released_version'),
                      key: 'releaseNewVersion',
                      onClick: (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                          e.preventDefault();
                          setIsOpenComment(true);
                      },
                  }
                : {
                      icon: EditOutlined,
                      label: translate('button.create_new_version'),
                      key: 'createNewVersion',
                      onClick: (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                          e.preventDefault();
                          setIsOpenComment(true);
                      },
                  },
        );
    }

    if (formTemplate?.activeVersionId && !isAccount) {
        toolbarBoxActions.push({
            icon: CloudUploadOutlined,
            label: translate('button.publish', { ns: 'common' }),
            key: 'publishRegister',
            onClick: (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e.preventDefault();
                setIsOpenPublishForm(true);
            },
        });
    }

    if (formLock.isLocked && isCurrentUserHoldingLock) {
        toolbarBoxActions.push({
            icon: UnlockOutlined,
            label: translate('form_lock.release_edit_control', { ns: NAMESPACES.FORM }),
            key: 'releaseEditControl',
            onClick: () => {
                handleRelease();
            },
        });
    }
    if (!formLock.isLocked && !isCurrentUserHoldingLock) {
        toolbarBoxActions.push({
            icon: LockOutlined,
            label: translate('form_lock.take_over_edit_control', { ns: NAMESPACES.FORM }),
            key: 'takeOverEditControl',
            onClick: () => {
                handleTakeOver();
            },
        });
    }

    return (
        <section className="flex flex-col gap-y-3">
            <section>
                <FormToolbar
                    {...props.toolbarProps}
                    extraActions={<></>}
                    formName={formTemplate?.name as string}
                    onCreateNewVersion={() => setIsOpenComment(true)}
                    onReleaseVersion={() => setIsOpenComment(true)}
                    toolbarBoxActions={toolbarBoxActions}
                />
            </section>

            <ChangeLogsTable
                resource={isAccount ? ENDPOINT_RESOURCES.FORM_VERSION_TENANCY : ENDPOINT_RESOURCES.FORM_VERSION}
                id={formTemplate?.id as string}
                versionId={formTemplate?.latestVersionId as string}
            />

            <CommentFormDrawer
                drawerProps={{
                    width: '50%',
                }}
                id={id}
                isOpen={isOpenComment}
                onClose={(val?: boolean) => {
                    setIsOpenComment(false);
                    if (val) {
                        onCreateVersionCallback();
                    }
                }}
                title={translate('comment.form_version_comment', { ns: NAMESPACES.FORM })}
                resource={isAccount ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE}
                isFormVersion={true}
            />

            <PublishFormDrawer
                drawerProps={{
                    width: '50%',
                    open: isOpenPublishForm,
                    destroyOnClose: true,
                    loading: isFetching,
                }}
                onClose={() => setIsOpenPublishForm(false)}
                formId={id as string}
                subscriptionIds={formTemplate?.subscriptionIds}
                subscriptionNames={formTemplate?.subscriptionNames ?? []}
                formName={formTemplate?.name as string}
                activeFormVersion={activeFormVersion}
            />
        </section>
    );
};
