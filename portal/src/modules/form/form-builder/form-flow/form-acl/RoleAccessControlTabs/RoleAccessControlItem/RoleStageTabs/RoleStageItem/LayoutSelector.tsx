import { DataProvider } from '@/common/enums';
import { useIsAccount } from '@/common/hooks';
import { ENDPOINT_RESOURCES, NAMESPACES } from '@/constants';
import { NForm } from '@/interfaces';
import { SelectFormField } from '@/modules/form-fields';
import { useFormAccessControlStore } from '@/stores/useFormAccessControlStore';
import { useList, useTranslation } from '@refinedev/core';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { getRoleLayoutsACLKey } from '../../../../get-key';
import { queryOptions } from '../../../../../../../../../constants/react-query/query-option';

type Props = {
    role: string;
    stageId: string;
};
export const LayoutSelector: React.FC<Props> = ({ role, stageId }) => {
    const { formVersion, stageRoles } = useFormAccessControlStore();
    const { translate } = useTranslation();

    const isAccount = useIsAccount();
    const { setValue } = useFormContext();

    const {
        data: layoutData,
        isLoading,
        isFetching,
    } = useList<NForm.FormLayout>({
        resource: `${isAccount ? ENDPOINT_RESOURCES.FORM_LAYOUT_TENANCY : ENDPOINT_RESOURCES.FORM_LAYOUT}/${formVersion?.id}`,
        dataProviderName: DataProvider.Definition,
        pagination: { mode: 'off' },
        queryOptions: {
            enabled: !!formVersion?.id,
            ...queryOptions,
        },
    });

    const options = (layoutData?.data || []).map((item) => ({
        label: item.name,
        value: item.id,
    }));

    const FIELD_ARRAY_NAME = getRoleLayoutsACLKey({ role: role, stageId });

    useEffect(() => {
        const stageRole = isAccount
            ? stageRoles?.find((item) => item.roleId === role && item.stageId === stageId)
            : stageRoles?.find((item) => item.roleName === role && item.stageId === stageId);

        if (stageRole && !!stageRole?.layoutId && stageId && role) {
            setValue(FIELD_ARRAY_NAME, {
                layoutId: stageRole.layoutId,
                stageRoleId: stageRole.id,
            });
        }
    }, [role, stageId, stageRoles, formVersion?.id]);

    return (
        <section className="max-w-96">
            <h3 className="mb-1 font-semibold">{translate('role_acl.layout', { ns: NAMESPACES.FORM })}</h3>
            <SelectFormField
                loading={isLoading || isFetching}
                name={`${FIELD_ARRAY_NAME}.layoutId`}
                options={options}
                label={translate('role_acl.choose_layout', { ns: NAMESPACES.FORM })}
                rules={{
                    required: {
                        value: true,
                        message: translate('role_acl.layout_required', { ns: NAMESPACES.FORM }),
                    },
                }}
                data-testid="layout-selector-choose-layout-select"
            />
        </section>
    );
};
