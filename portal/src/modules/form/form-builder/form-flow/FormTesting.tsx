import { FormToolbar, ToolBarProps } from '@/components/form-toolbar';
import { NForm, NUser } from '@/interfaces';
import { useGetIdentity, useTranslation } from '@refinedev/core';
import { <PERSON><PERSON>, Popconfirm } from 'antd';
import { useEffect } from 'react';
import { useEmptyTransaction } from '../../../../common/hooks/useEmptyTransaction';
import { NAMESPACES, UnlockOutlined, LockOutlined } from '../../../../constants';
import { EditFormTransactionRenderer } from '../../../shared/form-builder/form-renderer/EditFormTransactionRenderer';
import { FormFlowStepProps } from './FormFlow';
import { useFormRendererStore } from '../../../../stores';
import { useDeleteTestTransaction } from '../../../../common/hooks/useDeleteTestTransaction';
import { FormVersionStatus } from '../../../../common/enums';
import { useFormLock } from '../../hooks/useFormLock';

type Props = { formTemplate: NForm.FormType; toolbarProps: ToolBarProps } & FormFlowStepProps;

export const FormTesting: React.FC<Props> = ({ isSaveStep, onSaveCallBack, refetchForm, ...props }) => {
    const { formTemplate } = props;

    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();

    const { translate } = useTranslation();

    const { formLock, isCurrentUserHoldingLock, handleRelease, handleTakeOver } = useFormLock(formTemplate?.id as string);

    const { setUserRoles, deleteStore } = useFormRendererStore();

    useEffect(() => {
        identity?.roles && setUserRoles(identity?.roles);
    }, [identity?.roles, setUserRoles]);

    useEffect(() => {
        if (isSaveStep) {
            // //TODO: move call back to update call
            // onSaveCallBack?.();
            props.onChangeStepCallback?.();
        }
    }, [isSaveStep]);

    const latestVersion = formTemplate?.latestFormVersion;
    const isEditing = latestVersion?.status === FormVersionStatus.Draft;
    const formId = formTemplate?.id;

    const accountId = identity?.accountId;

    const { create: createEmptyTransaction, loading: creating } = useEmptyTransaction();
    const { onDelete, loading: deleting } = useDeleteTestTransaction();

    const onCreateTransaction = () => {
        createEmptyTransaction(
            formId as string,
            (data: any) => {
                const transactionId = data?.data?.data;
                if (transactionId) {
                    refetchForm?.();
                }
            },
            true,
        );
    };

    const onDeleteTransaction = () => {
        if (latestVersion?.testTransactionId) {
            onDelete(latestVersion?.testTransactionId, () => {
                refetchForm?.();
            });
        }
    };

    useEffect(() => {
        return () => {
            // Clear store when unmounting
            deleteStore(formId as string);
        };
    }, []);

    const toolbarBoxActions = [];
    if (formLock.isLocked && isCurrentUserHoldingLock) {
        toolbarBoxActions.push({
            icon: UnlockOutlined,
            label: translate('form_lock.release_edit_control', { ns: NAMESPACES.FORM }),
            key: 'releaseEditControl',
            onClick: () => {
                handleRelease();
            },
        });
    }
    if (!formLock.isLocked && !isCurrentUserHoldingLock) {
        toolbarBoxActions.push({
            icon: LockOutlined,
            label: translate('form_lock.take_over_edit_control', { ns: NAMESPACES.FORM }),
            key: 'takeOverEditControl',
            onClick: () => {
                handleTakeOver();
            },
        });
    }

    return (
        <section>
            <section>
                <FormToolbar
                    {...props.toolbarProps}
                    extraActions={<></>}
                    prefixActions={
                        isEditing && accountId ? (
                            <>
                                {latestVersion?.testTransactionId ? (
                                    <>
                                        <Popconfirm
                                            title={translate('message.confirm_delete_test_transaction', { ns: NAMESPACES.COMMON })}
                                            onConfirm={onDeleteTransaction}
                                            okText={translate('yes', { ns: NAMESPACES.COMMON })}
                                            cancelText={translate('no', { ns: NAMESPACES.COMMON })}
                                        >
                                            <Button size="middle" danger type="primary" loading={deleting}>
                                                {translate('button.delete_test_transaction', { ns: NAMESPACES.COMMON })}
                                            </Button>
                                        </Popconfirm>
                                    </>
                                ) : (
                                    <>
                                        <Button size="middle" type="primary" onClick={onCreateTransaction} loading={creating}>
                                            {translate('button.create_test_transaction', { ns: NAMESPACES.COMMON })}
                                        </Button>
                                    </>
                                )}
                            </>
                        ) : (
                            <></>
                        )
                    }
                    formName={props.formTemplate?.name as string}
                    toolbarBoxActions={toolbarBoxActions}
                />
            </section>
            {accountId && isEditing && (
                <section>
                    {latestVersion?.testTransactionId && (
                        <EditFormTransactionRenderer
                            formId={formTemplate?.id as string}
                            transactionId={latestVersion?.testTransactionId}
                            isTest={true}
                        />
                    )}
                </section>
            )}
        </section>
    );
};
