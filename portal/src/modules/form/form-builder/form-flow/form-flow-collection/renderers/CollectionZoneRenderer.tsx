import { useDeleteCollectionRow } from '@/common/hooks/useDeleteCollectionRow';
import { useFormCollectionAutomationMappings } from '@/common/hooks/useFormCollectionAutomationMappings';
import { getCreateDeleteCollectionAcl } from '@/utils/get-create-delete-collection-acl';
import { JsonItem, JsonTree } from '@react-awesome-query-builder/ui';
import { mapAntdFilterToCrudFilter, mapAntdSorterToCrudSorting } from '@refinedev/antd';
import { CrudFilters, CrudSorting, useApiUrl, useCreate, useCustomMutation, useTranslate } from '@refinedev/core';
import { notification } from 'antd';
import { FilterValue, SorterResult } from 'antd/es/table/interface';
import _, { groupBy, isEmpty, merge, uniqBy } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { validate as isUUID, v4 } from 'uuid';
import { DataProvider, ManualEventSourceType, TransactionFieldEnum } from '../../../../../../common/enums';
import { FormCollectionItemEnum } from '../../../../../../common/enums/form-collection.enum';
import { useBrowserTabId, useElementSize, useFormCollectionAdditional } from '../../../../../../common/hooks';
import { DATA_PROVIDER, ENDPOINT_RESOURCES, NAMESPACES } from '../../../../../../constants';
import { NAutomation, NDataRegisterBuilder, NForm } from '../../../../../../interfaces';
import { NFormTransaction } from '../../../../../../interfaces/form-transaction';
import { useFormRendererStore } from '../../../../../../stores';
import {
    FormCollectionItemSetting,
    ICollectionAdditionalField,
    IFormCollection,
    IFormCollectionItem,
} from '../../../../../../types/FormCollectionType';
import { applyAclToCollections } from '../../../../../../utils/apply-acl-to-collection';
import { combineCollectionKeys } from '../../../../../../utils/collection';
import { formatCollectionFieldValue, useDeepCompareEffect } from '../../../../../shared/form-builder/form-renderer/utils';
import { FormFields } from '../../../../../shared/form-builder/types';
import { determineVisibility, getDataGrid, getRelatedFields } from '../../../../../shared/form-builder/utils';
import { useExtractVisibilityConditionField } from '../hooks/useExtractVisibilityConditionField';
import { useGetActiveFieldList } from '../hooks/useGetActiveFieldList';
import { useGetCollectionByIdentityId } from '../hooks/useGetCollectionByIdentityId';
import { useGetCollectionTransactionList } from '../hooks/useGetCollectionTransactionList';
import { usePostCollectionTransaction } from '../hooks/usePostCollectionTransaction';
import { useVisibleColumns } from '../hooks/useVisibleColumns';
import '../styles.scss';
import { mappingTransactionFields } from '../utils/mapping-transaction-fn';
import { onSetAcl } from '../utils/onSetAcl';
import { orderCollectionTableData } from '../utils/order-table-data';
import { CollectionBody } from './CollectionBody';
import { CollectionHeader } from './CollectionHeader';
import { ExpandCollectionZoneRender } from './ExpandCollectionZoneRender';

export type CollectionTableStyles = {
    backgroundColor?: string;
    fieldBackgroundColor?: string;
};

type Props = {
    layoutFields: NForm.WidgetFieldType[];
    formId: string;
    isPreview?: boolean;
    stageRole?: NForm.StageRole | undefined;
    zoneStyles?: CollectionTableStyles;
    identityId: string;
    availableFilterIds: string[];
    groupByIds: string[];
    automation?: NAutomation.AutomationActionCollection[];
};

export interface VisibilityCheckParams {
    fields: FormFields;
    collectionFields: Record<string, FormFields>;
    watchedValues: any;
    watchingFields: string[];
    visibilityConditions: any;
    visibility: string;
}

export type CollectionValueTableType = Record<string, any>;

export const CollectionZoneRenderer: React.FC<Props> = ({
    layoutFields,
    formId,
    isPreview,
    stageRole,
    zoneStyles,
    identityId,
    availableFilterIds,
    groupByIds,
    automation,
}) => {
    const {
        formStates,
        transactionsLoading,
        setCollectionAutomationMappings,
        setCollectionField,
        setTransaction,
        setTransactionFields,
        setCollectionAdditionalField,
        setNeedRefetch,
    } = useFormRendererStore();

    // const { mutate: deleteCollectionField, isLoading: deleting } = useCustomMutation();

    const { deleteCollectionRow, deleting } = useDeleteCollectionRow();

    const { mutate: createCollectionFields } = useCustomMutation();

    const [tableData, setTableData] = useState<CollectionValueTableType[]>([]);
    const {
        fields,
        transaction,
        formVersionId,
        transactionId,
        stageActiveAccessControls = [],
        isLoadingTransaction,
        activeStage,
        needRefetch,
        stages,
        activeRole,
        isTest,
    } = formStates[formId] ?? {};
    const [collectionFields, setCollectionFields] = useState<NFormTransaction.IFormTransactionField[] | any[]>([]);

    const { data: collectionAutomationMappings } = useFormCollectionAutomationMappings({ formVersionId: formVersionId });

    const methods = useFormContext();

    const { unregister, getValues, reset } = methods;

    const [cvfConditionMap, setCvfConditionMap] = useState<Map<string, JsonTree>>(new Map());

    const { collection = {} as IFormCollection, isLoading } = useGetCollectionByIdentityId({
        fieldId: layoutFields?.[0]?.fieldId,
        formVersionId,
    });

    useEffect(() => {
        const filters: NDataRegisterBuilder.ICollectionFilter[] = collection?.displaySetting?.filters ?? [];
        setCollectionFilters(filters?.filter((filter) => availableFilterIds?.includes(filter.id)));
    }, [collection?.displaySetting?.filters, availableFilterIds]);

    const collectionId = collection?.id ?? '';
    const dataRegisterId = collection?.dataRegisterId ?? '';
    const collectionIdentityId = useMemo(() => collection?.identityId ?? '', [collection?.identityId]);
    const dataRegisterVersionId = collection?.dataRegisterVersionId ?? '';
    const hideShowAddValue = collection?.displaySetting?.hideShowAddValue ?? false;

    useEffect(() => {
        if (collectionAutomationMappings) {
            setCollectionAutomationMappings(formId, collectionAutomationMappings);
        }
    }, [collectionAutomationMappings]);

    const collectionPermission = useMemo(() => {
        return getCreateDeleteCollectionAcl({
            collectionIdentityId,
            stageRole,
        });
    }, [collectionIdentityId, stageRole]);

    const parentGroup = useMemo(() => groupBy(collectionFields, 'parentId'), [collectionFields]);

    const [data, setData] = useState<{
        [key in FormCollectionItemEnum]: CollectionValueTableType[];
    }>({
        [FormCollectionItemEnum.DEFAULT]: [],
        [FormCollectionItemEnum.OPTION]: [],
    });

    const [aclData, setAclData] = useState<{
        [key in FormCollectionItemEnum]: CollectionValueTableType[];
    }>({
        [FormCollectionItemEnum.DEFAULT]: [],
        [FormCollectionItemEnum.OPTION]: [],
    });

    const [isAddCollectionValues, setIsAddCollectionValues] = useState<boolean>(false);
    const [deleteData, setDeleteData] = useState<CollectionValueTableType | null>(null);
    const [formCollectionItems, setFormCollectionItems] = useState<IFormCollectionItem[]>([]);
    const [criteriaAdditionalFields, setCriteriaAdditionalFields] = useState<ICollectionAdditionalField[]>([]);

    const [collapsed, setCollapsed] = useState(false);

    const { height } = useElementSize(identityId);

    const layoutConfig = layoutFields?.[0]?.config;

    const {
        collectionTransactionFields,
        isLoading: collectionTransactionLoading,
        isFetching: collectionTransactionFetching,
    } = useGetCollectionTransactionList({
        collectionIdentityId: collectionIdentityId,
        formVersionId,
        collectionId: collectionId,
    });

    const { activeFields, fieldsLoading } = useGetActiveFieldList({
        dataRegisterVersionId: dataRegisterVersionId,
        dataRegisterId: dataRegisterId,
    });

    const { getCriteriaAdditionalFields } = useFormCollectionAdditional({ formVersionId });

    useEffect(() => {
        if (!collection || isEmpty(collection)) {
            return;
        }

        const getCriteriaFields = async () => {
            const formCollectionItemIdentityIds: string[] = [];

            collection.formCollectionItems?.forEach((item) => {
                const { identityId, setting } = item;

                if (identityId) {
                    formCollectionItemIdentityIds.push(identityId);
                }

                setting?.dataGroup?.forEach((group) => {
                    if (group?.identityId) {
                        formCollectionItemIdentityIds.push(group.identityId);
                    }
                });
            });

            const criteriaFields = await getCriteriaAdditionalFields({ formCollectionItemIdentityIds, isTest });
            setCriteriaAdditionalFields(criteriaFields);
        };

        getCriteriaFields();
    }, [collection]);

    const { visibleColumns } = useVisibleColumns({
        collectionItems: formCollectionItems,
        layoutFields: (layoutFields?.[0]?.config as any) ?? [],
        stageRoleAccessControls: stageRole?.accessControls ?? [],
        stageActiveAccessControls: stageActiveAccessControls,
    });

    const {
        isFetching,
        data: filteredData = [],
        filters,
        setSorters,
        setFilters,
        refetch,
        collectionFilters,
        setCollectionFilters,
    } = usePostCollectionTransaction({
        collectionIdentityId,
        transactionId,
        isTest,
    });

    useEffect(() => {
        if (transactionsLoading?.[transaction?.id as string]) {
            refetch();
        }
    }, [transactionsLoading?.[transaction?.id as string]]);

    useDeepCompareEffect(() => {
        if (!collectionIdentityId) {
            return;
        }

        if ((filters?.length || activeStage) && !deleting) {
            refetch?.();
            setNeedRefetch(formId, collectionIdentityId, false);
        }
    }, [activeStage, filters, deleting]);

    useEffect(() => {
        if (!collectionIdentityId) {
            return;
        }

        if (needRefetch?.[collectionIdentityId]) {
            refetch?.();
            setNeedRefetch(formId, collectionIdentityId, false);
        }
    }, [needRefetch]);

    const formatArrayToObject = (array: any[], key: keyof any): Record<string, any> => {
        return array.reduce(
            (obj, item) => {
                const keyValue = item[key];
                if (typeof keyValue === 'string' || typeof keyValue === 'number') {
                    obj[keyValue] = item;
                }
                return obj;
            },
            {} as Record<string, any>,
        );
    };

    useEffect(() => {
        if (isFetching) return;

        if (filteredData?.length) {
            setTransactionFields(formId, filteredData);
            setCollectionFields(filteredData);

            const dataRegisterGroup = formatArrayToObject(activeFields?.data?.fields ?? [], 'fieldId');

            const collectionItems: IFormCollectionItem[] = [];
            collection.formCollectionItems?.forEach((item) => {
                const { setting, ..._item } = item;
                collectionItems.push(_item);

                if (setting?.dataGroup?.length) {
                    setting.dataGroup.forEach((group) => {
                        collectionItems.push(group);
                    });
                }
            });

            const collectionItemGroup = formatArrayToObject(collectionItems ?? [], 'identityId');

            const collectionFormValue: Record<string, any> = {};

            filteredData.forEach((data: any) => {
                const _field = dataRegisterGroup?.[data.fieldId];
                const _collectionItem = collectionItemGroup?.[data.collectionItemId];

                if (!_collectionItem || !_field) {
                    return;
                }

                const formName = combineCollectionKeys({
                    collectionItemKey: data?.rowKey,
                    collectionItemId: _collectionItem.id,
                    fieldId: _field.id,
                    fieldIdentityId: _field.fieldId,
                    collectionItemIdentityId: _collectionItem?.identityId ?? '',
                    collectionIdentityId: collection?.identityId ?? '',
                });

                const additionalField = criteriaAdditionalFields?.find(
                    (caf) => caf.fieldId === data.fieldId && caf.formCollectionItemIdentityId === data.collectionItemId,
                );

                let format = data?.format;
                let fieldType = data?.type;

                if (additionalField) {
                    format = additionalField?.configuration?.format ?? format;
                    fieldType = additionalField?.configuration?.type ?? fieldType;
                }

                const value = formatCollectionFieldValue(
                    {
                        formVersion: {
                            fields,
                        },
                    },
                    data.fieldId,
                    fieldType,
                    data?.fieldValue,
                    format,
                    data?.fieldOptionIds,
                );

                collectionFormValue[formName] = value;
            });

            const values = getValues();

            const changedValues = Object.keys(values).reduce((acc: Record<string, any>, key) => {
                if (JSON.stringify(values[key]) !== JSON.stringify(collectionFormValue[key])) {
                    acc[key] = collectionFormValue[key];
                }
                return acc;
            }, {});

            const mergeValues = merge(values, changedValues);
            reset(mergeValues);
        }
    }, [filteredData, formId, isFetching]);

    useEffect(() => {
        const initDefaultFilter = () => {
            if (!visibleColumns?.length) {
                return;
            }

            if (filters.length) {
                return;
            }

            const columnFilters = visibleColumns?.reduce((acc, column) => {
                if (!column?.fieldId || !column?.config?.view?.searchable || !column?.config?.view?.filterCondition?.children1?.length)
                    return acc;

                acc[column.fieldId] = [column.config.view.filterCondition];

                return acc;
            }, {});

            const crudFilters: CrudFilters = mapAntdFilterToCrudFilter(columnFilters, filters);
            crudFilters.forEach((filter) => {
                if (filter.operator == 'in') {
                    filter.operator = 'contains';
                }
                if (filter.value?.length) {
                    filter.value = JSON.stringify(filter.value[0]);
                }
            });

            if (crudFilters.length) {
                setFilters(crudFilters);
            }

            const columnSorters = visibleColumns?.reduce((acc: CrudSorting, column) => {
                if (!column?.fieldId || !column?.config?.view?.sortable) return acc;

                acc.push({
                    field: column.fieldId,
                    order: column?.config?.view?.sortOrder ? column.config.view.sortOrder.toLowerCase() : 'asc',
                });

                return acc;
            }, [] as CrudSorting);

            if (columnSorters.length) {
                setSorters(columnSorters);
            }
        };

        initDefaultFilter();
    }, [visibleColumns]);

    useEffect(() => {
        const fields: NDataRegisterBuilder.DataRegisterFieldDto[] = activeFields?.data?.fields ?? [];
        if (!fields?.length) {
            return;
        }

        fields.forEach((field) => {
            const formField = {
                id: field.id,
                fieldId: field.fieldId,
                dataGrid: getDataGrid(field.fieldId, field.type),
                type: field.type,
                label: field.label,
                format: field?.configuration?.format,
                ruleConfigs: field?.configuration?.ruleConfigs,
                options: field?.configuration?.options ?? [],
                configuration: field?.configuration,
                mode: field?.configuration?.mode,
            };

            setCollectionField(formId, collectionIdentityId, formField);
        });
    }, [activeFields?.data?.fields, formId, collectionIdentityId]);

    useEffect(() => {
        if (!criteriaAdditionalFields?.length) {
            return;
        }

        setCollectionAdditionalField(formId, criteriaAdditionalFields);
    }, [criteriaAdditionalFields]);

    const isApplyFilter = filters.length || collectionFilters.filter((f) => f.active)?.length;

    const groupByData = (defaultData: CollectionValueTableType[]) => {
        const groupByData =
            _.groupBy(
                defaultData?.sort((a, b) => a.order - b.order),
                (item) => {
                    const properties = groupByIds.map((id) => `${item?.[id]?.value || 'Unknown'}`);

                    return properties?.join(' / ');
                },
            ) ?? {};
        const keys = Object.keys(groupByData ?? {})?.filter((key) => key !== 'undefined');
        const newDefaultData: any[] = [];
        if (keys?.length) {
            keys.forEach((key, index) => {
                const names = key?.split(' / ');
                const isEmpty = names.every((name) => name === 'Unknown');

                groupByIds.forEach((id, index) => {
                    if (isUUID(names?.[index])) {
                        names[index] = groupByData[key]?.[0]?.[id]?.displayValue;
                    }
                });

                const _data = {
                    id: groupByData[key]?.[0]?.id,
                    name: isEmpty ? 'Unknown' : names.join(' / '),
                    setting: {
                        configuration: {
                            hasGroup: true,
                        },
                        dataGroup: groupByData[key],
                    },
                    hasGroup: true,
                    children: groupByData[key],
                    key: `${groupByData[key]?.[0]?.id}--${groupByData[key]?.[0]?.key}`,
                    type: FormCollectionItemEnum.DEFAULT,
                    collectionId: groupByData[key]?.[0]?.collectionId,
                    collectionIdentityId: groupByData[key]?.[0]?.collectionIdentityId,
                    identityId: groupByData[key]?.[0]?.collectionItemIdentityId,
                    collectionItemIdentityId: groupByData[key]?.[0]?.collectionItemIdentityId,
                    originKey: groupByData[key]?.[0]?.originKey,
                    order: groupByData[key]?.[0]?.order ?? index,
                };

                newDefaultData.push(_data);
            });
        }

        return newDefaultData;
    };

    useEffect(() => {
        if (isFetching) return;

        const fields: NDataRegisterBuilder.DataRegisterFieldDto[] = activeFields?.data?.fields || [];
        if (!fields?.length) {
            return;
        }

        // Memoize the initial form value object
        const _initialFormValue: Record<string, string | undefined> = {};

        // Group transaction fields by dataRegisterTransactionId
        const _collectionTransactionFieldsGroup = groupBy(collectionTransactionFields ?? [], 'dataRegisterTransactionId');

        // Initialize data structure
        let _data: {
            [key in FormCollectionItemEnum]: CollectionValueTableType[];
        } = {
            [FormCollectionItemEnum.DEFAULT]: [],
            [FormCollectionItemEnum.OPTION]: [],
        };
        const cvfConditionMap = new Map<string, JsonTree>();

        // Process collection items
        (collection?.formCollectionItems ?? []).forEach((item) => {
            const children: any[] = [];
            const rowKey = v4();
            if (item?.setting?.visibility?.children1?.length) {
                cvfConditionMap.set(item.identityId as string, item.setting.visibility);
            }
            // Process data groups if they exist
            if (item?.setting?.dataGroup?.length) {
                (item?.setting?.dataGroup ?? []).forEach((group) => {
                    const _rowKey = v4();

                    if (group?.setting?.visibility?.children1?.length) {
                        cvfConditionMap.set(item.identityId as string, group.setting.visibility);
                    }

                    const _child: CollectionValueTableType = {
                        id: group.id,
                        name: group?.setting?.configuration?.alias
                            ? `${group?.name} (${group?.setting?.configuration?.alias})`
                            : group?.name,
                        key: _rowKey,
                        addMultipleTimes: group.setting?.configuration?.addMultipleTimes,
                        parentId: item.id,
                        setting: group.setting,
                        collectionId: collection?.id,
                        identityId: group?.identityId,
                        collectionIdentityId: collection?.identityId,
                        origin: _rowKey,
                        order: group.order,
                        dataRegisterId: collection?.dataRegisterId,
                        collectionItemIdentityId: group?.identityId,
                        layout: item?.layout,
                    };

                    // Process fields for this child
                    fields.forEach((_field) => {
                        // Only clone the field if we need to modify it
                        let _fieldClone = _field;

                        const transactionField = _collectionTransactionFieldsGroup?.[group?.dataRegisterTransactionId ?? '']?.find(
                            (tf) => tf.fieldId === _field.fieldId,
                        );

                        const formName = combineCollectionKeys({
                            collectionItemKey: transactionField?.rowKey ?? _rowKey,
                            collectionItemId: group.id,
                            fieldId: _field.id,
                            fieldIdentityId: _field.fieldId,
                            collectionItemIdentityId: group?.identityId ?? '',
                            collectionIdentityId: collection?.identityId ?? '',
                        });

                        if (transactionField?.additionalFieldConfiguration) {
                            _fieldClone = _field;
                            _fieldClone.additionalFieldConfiguration = transactionField?.additionalFieldConfiguration;
                        }

                        _child[_field.fieldId] = {
                            value: transactionField?.fieldValue,
                            type: _fieldClone.type,
                            field: _fieldClone,
                            formName,
                        };

                        if (item.type === FormCollectionItemEnum.DEFAULT) {
                            _initialFormValue[formName] = transactionField?.fieldValue;
                        }
                    });
                    children.push(_child);
                });
            }

            // Create item object with minimal cloning
            const _item: CollectionValueTableType = {
                id: item.id,
                name: item?.setting?.configuration?.alias ? `${item.name} (${item?.setting?.configuration?.alias})` : item.name,
                setting: item.setting,
                hasGroup: item?.setting?.configuration?.hasGroup,
                children: children.length ? children : undefined, // No need to clone here
                key: rowKey,
                addMultipleTimes: item.setting?.configuration?.addMultipleTimes,
                type: item.type,
                collectionId: collection?.id,
                collectionIdentityId: collection?.identityId,
                identityId: item?.identityId,
                collectionItemIdentityId: item?.identityId,
                originKey: rowKey,
                order: item?.order,
                layout: item?.layout,
            };

            const _collectionTransactionFields = _collectionTransactionFieldsGroup?.[item?.dataRegisterTransactionId ?? ''] ?? [];

            // Process fields for non-group items
            if (!_item?.hasGroup) {
                fields.forEach((_field) => {
                    // Only clone the field if we need to modify it
                    let _fieldClone = _field;

                    const transactionField = _collectionTransactionFields.find((tf) => tf.fieldId === _field.fieldId);
                    const formName = combineCollectionKeys({
                        collectionItemKey: transactionField?.rowKey ?? rowKey,
                        collectionItemId: item.id,
                        fieldId: _field.id,
                        fieldIdentityId: _field.fieldId,
                        collectionItemIdentityId: item?.identityId ?? '',
                        collectionIdentityId: collection?.identityId ?? '',
                    });

                    if (transactionField?.additionalFieldConfiguration) {
                        _fieldClone = _field;
                        _fieldClone.additionalFieldConfiguration = transactionField?.additionalFieldConfiguration;
                    }

                    _item[_field.fieldId] = {
                        value: transactionField?.fieldValue,
                        type: _fieldClone.type,
                        field: _fieldClone,
                        formName,
                    };

                    if (item.type === FormCollectionItemEnum.DEFAULT) {
                        _initialFormValue[formName] = transactionField?.fieldValue;
                    }
                });
            }

            // Add item to appropriate collection
            if ((!collectionFields?.length && (transaction as NFormTransaction.IFormTransaction)?.isEmptyCollectionFields) || isPreview) {
                _data[item.type as FormCollectionItemEnum].push(_item);
            } else {
                // console.log('parentGroup', parentGroup);
                _data = mappingTransactionFields({
                    item: _item, // No need to clone here
                    data: _data,
                    parentGroup,
                    filters,
                    filteredData,
                    isApplyFilter: !!isApplyFilter,
                });
            }

            // Handle addMultipleTimes items
            if (item.setting?.configuration?.addMultipleTimes === true) {
                _data[FormCollectionItemEnum.OPTION].push(_item);
            }

            // Process children for multiple records
            if (_item.children?.length) {
                const multipleRecord: CollectionValueTableType[] = [];

                _item.children.forEach((child: CollectionValueTableType) => {
                    if (child?.addMultipleTimes) {
                        const _children = child;
                        _children.key = v4();
                        _children.parentId = _item.id;
                        multipleRecord.push(_children);
                    } else {
                        const defaultGroup = _data.default?.find((defaultValue) => defaultValue.id === child?.parentId);

                        if (defaultGroup?.children?.length) {
                            const _children = defaultGroup?.children?.find((child1: any) => child1?.id === child?.id);
                            if (_children) {
                                return;
                            }
                        }

                        const _children = child;
                        _children.key = v4();
                        _children.parentId = _item.id;
                        multipleRecord.push(_children);
                    }
                });

                if (multipleRecord?.length) {
                    _item.children = multipleRecord;

                    // Use uniqBy for deduplication as requested
                    const _options = uniqBy([_item, ..._data[FormCollectionItemEnum.OPTION]], 'id');

                    _options.forEach((option) => {
                        const defaultOption = _data?.default?.find((_default) => _default.id === option?.id);
                        if (defaultOption) {
                            option.key = defaultOption.key;
                        }
                    });

                    _data[FormCollectionItemEnum.OPTION] = _options;
                }
            }
        });

        // Use uniqBy for deduplication as requested
        _data.option = uniqBy(_data.option, 'id');

        if (!filteredData?.length) {
            setData({
                [FormCollectionItemEnum.DEFAULT]: [],
                [FormCollectionItemEnum.OPTION]: [..._data.option, ..._data.default],
            });
            return;
        }

        setData(_data);
        setCvfConditionMap(cvfConditionMap);
    }, [
        collectionTransactionFields,
        activeFields?.data?.fields,
        filteredData,
        criteriaAdditionalFields,
        transaction?.transactionFields,
        parentGroup,
    ]);

    const onAddCollectionValues = async (values: CollectionValueTableType[]) => {
        const collectionItems = values?.filter((value) => !value.hasGroup);
        if (!collectionItems?.length) {
            return;
        }

        const _data = data;

        const collectionItemsGroup = groupBy(collectionItems, 'parentId');

        let collectionRequest: CollectionValueTableType[] = [];

        for (const [parentId, collectionItems] of Object.entries(collectionItemsGroup)) {
            if (parentId === 'undefined') {
                collectionRequest = [...collectionRequest, ...collectionItems];
            } else {
                const parentOption = _data[FormCollectionItemEnum.OPTION].find((optionValue) => optionValue.id === parentId);
                if (!parentOption) {
                    continue;
                }

                const multipleItems = parentOption.children.filter(
                    (children: { addMultipleTimes: boolean }) => !!children.addMultipleTimes,
                );

                if (parentOption?.children?.length === collectionItems.length) {
                    if (!multipleItems?.length) {
                        _data[FormCollectionItemEnum.OPTION] = _data[FormCollectionItemEnum.OPTION].filter(
                            (option) => option.id !== parentId,
                        );
                    }
                } else {
                    parentOption.children = parentOption.children.filter((children: { id: string }) =>
                        collectionItems.find((item) => item.id !== children.id),
                    );
                }

                const newParentOption = parentOption;
                newParentOption.children = collectionItems;

                collectionRequest = [...collectionRequest, newParentOption];
            }
        }

        onCreateCollectionFields(collectionRequest);
        setIsAddCollectionValues(false);
    };

    const onCreateCollectionFields = (values: CollectionValueTableType[]) => {
        if (!values?.length || !transaction || !layoutConfig?.length || isPreview) {
            return;
        }

        const request: NFormTransaction.IEditFormFieldTransaction[] = [];
        const group: NFormTransaction.IEditFormFieldTransaction[] = [];

        const processValue = (value: any, fieldId: string, parentId?: string | undefined) => {
            if (value.children?.length) {
                group.push({
                    rowKey: value.key,
                    collectionItemId: value.identityId,
                    fieldId: value.id,
                    contextType: TransactionFieldEnum.COLLECTION,
                    collectionId: collection?.identityId,
                    id: value.key,
                });

                value.children.forEach((child: any) => {
                    processValue(child, fieldId, value.key);
                });
            } else {
                const field = value?.[fieldId];
                if (field?.field) {
                    request.push({
                        rowKey: value.key,
                        collectionItemId: value.identityId,
                        fieldValue: field.value,
                        defaultValue: field.value,
                        fieldType: field.field.type,
                        fieldId: field.field.fieldId,
                        contextType: TransactionFieldEnum.COLLECTION,
                        collectionId: collection?.identityId,
                        pairId: value.pairId,
                        parentId,
                    });
                }
            }
        };

        activeFields?.data?.fields?.forEach((_field) => {
            values.forEach((_value) => {
                const value = _value;
                processValue(value, _field.fieldId);
            });
        });

        const uniqueArray = group.reduce((accumulator, current) => {
            if (!accumulator.some((item) => item.id === current.id)) {
                accumulator.push(current);
            }
            return accumulator;
        }, [] as NFormTransaction.IEditFormFieldTransaction[]);

        const fields = [...request, ...uniqueArray];

        const PATH = `${apiUrl}/${isTest ? ENDPOINT_RESOURCES.TEST_FORM_TRANSACTION_FIELD : ENDPOINT_RESOURCES.FORM_TRANSACTION_FIELD}`;

        if (criteriaAdditionalFields?.length) {
            fields.forEach((field) => {
                const additionalField = criteriaAdditionalFields.find((additionalField) => additionalField.fieldId === field.fieldId);
                if (additionalField) {
                    field.fieldType = additionalField.type;
                }
            });
        }

        createCollectionFields(
            {
                url: PATH,
                method: 'post',
                values: {
                    transactionId: transaction.id,
                    formId,
                    fields,
                    isTest,
                    collectionIdentityId,
                    activeRoleId: activeRole?.id,
                },
            },
            {
                onSuccess: (data: any) => {
                    setNeedRefetch(formId, collectionIdentityId, true);
                    return;
                },
                onError: () => {
                    return;
                },
            },
        );
    };

    useEffect(() => {
        let _collectionItems = collection?.formCollectionItems ?? [];

        if (!_collectionItems?.length) {
            return;
        }

        const defaultValue = aclData[FormCollectionItemEnum.DEFAULT];

        if (defaultValue?.length) {
            _collectionItems = [];
            defaultValue.forEach((value) => {
                const _value = collection?.formCollectionItems?.find((item) => item.id === value.id);

                if (!_value) {
                    return;
                }

                if (!value?.key) {
                    value.key = v4();
                }

                if (value?.setting) {
                    _value.setting = value.setting;
                }

                _value.key = value.key;
                _value.parentId = value.parentId;
                _value.pairId = value.pairId;

                _collectionItems.push(_value);
            });
        }

        setFormCollectionItems(_collectionItems);
    }, [aclData[FormCollectionItemEnum.DEFAULT], collection]);

    useEffect(() => {
        if (!deleteData || isPreview) {
            return;
        }

        onDeleteCollectionFields(deleteData);

        if (transaction?.transactionFields?.length) {
            const rowKeys: string[] = [deleteData.key];

            if (deleteData?.children?.length) {
                deleteData.children.forEach((child: { key: string }) => rowKeys.push(child.key));
            }
            const transactionFields = transaction.transactionFields.filter((field) => !rowKeys.includes(field?.rowKey ?? ''));

            setTransaction(formId, {
                ...transaction,
                transactionFields,
            });
        }

        setDeleteData(null);
    }, [deleteData]);

    const apiUrl = useApiUrl(DATA_PROVIDER.APPLICATION);

    const onDeleteCollectionFields = (deleteData: CollectionValueTableType) => {
        if (!transaction?.id) {
            return;
        }

        deleteCollectionRow({
            transactionId: transaction?.id,
            rowKey: deleteData?.key,
            collectionIdentityId: collectionIdentityId,
            isTest: isTest ?? false,
            activeRoleId: activeRole?.id,
            onSuccess: () => {
                const unregisterFields = [deleteData?.key];

                for (const prop in deleteData) {
                    if (deleteData?.[prop]?.formName) {
                        unregisterFields.push(deleteData?.[prop]?.formName);
                    }
                }

                if (deleteData?.children?.length) {
                    deleteData?.children.forEach((children: any) => {
                        unregisterFields.push(children.key);
                        for (const prop in children) {
                            if (children?.[prop]?.formName) {
                                unregisterFields.push(children?.[prop]?.formName);
                            }
                        }
                    });
                }

                unregister(unregisterFields);
            },
        });
    };

    const watchConditionChanges = useExtractVisibilityConditionField({ configMap: cvfConditionMap });

    const processCollectionFieldMap = (item: CollectionValueTableType, collectionFieldMap: Record<string, FormFields>) => {
        if (!item?.identityId) return;
        collectionFieldMap[item.identityId] = {};
        Object.entries(item).forEach(([key, value]) => {
            if (!isUUID(key)) {
                return;
            }

            if (value?.field?.additionalFieldConfiguration && !isEmpty(value.field.additionalFieldConfiguration)) {
                collectionFieldMap[item.identityId] = {
                    ...collectionFieldMap[item.identityId],
                    [key]: value.field.additionalFieldConfiguration,
                };
            } else {
                collectionFieldMap[item.identityId] = {
                    ...collectionFieldMap[item.identityId],
                    [key]: value.field ?? {},
                };
            }
        });
    };

    const processChildVisibility = (child: CollectionValueTableType, params: VisibilityCheckParams): CollectionValueTableType | null => {
        const setting = child?.setting as FormCollectionItemSetting;
        if (!setting?.visibility?.children1?.length) {
            return child;
        }

        const localWatchingFields: string[] = [];
        getRelatedFields((setting?.visibility?.children1 as JsonItem[]) || [], localWatchingFields);

        const isVisible = determineVisibility({
            ...params,
            watchingFields: localWatchingFields,
            visibilityConditions: setting?.visibility,
        });

        // Update visibility states
        if (child.visibility1 !== isVisible) {
            child.visibility2 = child.visibility1;
            child.visibility1 = isVisible;

            if (child.visibility2 === undefined && isVisible) {
                child.visibility2 = true;
            }
        }

        return isVisible ? child : null;
    };

    const processGroupItem = useCallback(
        (value: CollectionValueTableType, params: VisibilityCheckParams): CollectionValueTableType | null => {
            if (!value?.hasGroup || !value?.children?.length) {
                return null;
            }

            const processedChildren = value.children
                .map((child: CollectionValueTableType) => processChildVisibility(child, params))
                .filter(Boolean);

            if (processedChildren.length) {
                return {
                    ...value,
                    children: processedChildren,
                };
            }

            return null;
        },
        [],
    );

    const processSingleItem = useCallback(
        (value: CollectionValueTableType, params: VisibilityCheckParams): CollectionValueTableType | null => {
            const setting = value?.setting as FormCollectionItemSetting;
            if (!setting?.visibility?.children1?.length) {
                return value;
            }

            const localWatchingFields: string[] = [];
            getRelatedFields((setting?.visibility?.children1 as JsonItem[]) || [], localWatchingFields);

            const isVisible = determineVisibility({
                ...params,
                watchingFields: localWatchingFields,
                visibilityConditions: setting?.visibility,
            });

            if (value.visibility1 !== isVisible) {
                value.visibility2 = value.visibility1;
                value.visibility1 = isVisible;

                if (value.visibility2 === undefined && isVisible) {
                    value.visibility2 = true;
                }
            }

            return isVisible ? value : null;
        },
        [],
    );

    // Main Effect
    useEffect(() => {
        const buildCollectionFieldMap = () => {
            const collectionFieldMap: Record<string, FormFields> = {};
            aclData?.[FormCollectionItemEnum?.DEFAULT]?.forEach((item) => {
                if (item?.hasGroup) {
                    item?.children?.forEach((child: CollectionValueTableType) => {
                        processCollectionFieldMap(child, collectionFieldMap);
                    });
                } else {
                    processCollectionFieldMap(item, collectionFieldMap);
                }
            });
            return collectionFieldMap;
        };

        const processData = () => {
            const collectionFieldMap = buildCollectionFieldMap();
            // const _aclData = cloneDeep(aclData);
            const visibilityParams: VisibilityCheckParams = {
                fields,
                collectionFields: collectionFieldMap,
                watchedValues: getValues(),
                watchingFields: [],
                visibilityConditions: null,
                visibility: 'true',
            };

            // const processedData = (_aclData?.[FormCollectionItemEnum?.DEFAULT] ?? [])
            //     .map((value) => (value?.hasGroup ? processGroupItem(value, visibilityParams) : processSingleItem(value, visibilityParams)))
            //     .filter(Boolean);

            const processedData = (aclData?.[FormCollectionItemEnum?.DEFAULT] ?? [])
                .map((value) => (value?.hasGroup ? processGroupItem(value, visibilityParams) : processSingleItem(value, visibilityParams)))
                .filter(Boolean);

            // setAclData(_aclData);

            if (!groupByIds?.length) {
                setTableData(orderCollectionTableData(processedData as CollectionValueTableType[]));
            } else {
                const newDefaultData = processedData?.length ? groupByData(processedData as CollectionValueTableType[]) : [];
                setTableData(orderCollectionTableData(newDefaultData as CollectionValueTableType[]));
            }
        };

        processData();
    }, [aclData, fields, stageRole, watchConditionChanges]);

    useEffect(() => {
        if (isPreview) {
            onSetAcl({
                dataValue: {
                    default: data?.default,
                    option: data?.option,
                },
                methods,
                setAclData,
            });
        } else {
            const defaultData: CollectionValueTableType[] = applyAclToCollections({
                rootCollectionValues: data?.default ?? [],
                stageRoleAccessControls: stageRole?.accessControls ?? [],
            });
            const optionalData: CollectionValueTableType[] = applyAclToCollections({
                rootCollectionValues: data?.option ?? [],
                stageRoleAccessControls: stageRole?.accessControls ?? [],
            });

            onSetAcl({
                dataValue: {
                    default: defaultData,
                    option: optionalData,
                },
                methods,
                setAclData,
            });
        }
    }, [stageRole, data, isPreview]);

    const handleFilterChange = (
        tableFilters: Record<string, FilterValue | null>,
        tableSorters: SorterResult<any> | SorterResult<any>[],
    ) => {
        const crudFilters: CrudFilters = mapAntdFilterToCrudFilter(tableFilters, filters);
        crudFilters.forEach((filter) => {
            if (filter.operator == 'in') {
                filter.operator = 'contains';
            }
            if (filter.value?.length) {
                filter.value = JSON.stringify(filter.value[0]);
            }
        });
        setFilters(crudFilters);

        const crudSorting: CrudSorting = mapAntdSorterToCrudSorting(tableSorters || []);
        if (crudSorting.length) {
            setSorters(crudSorting);
        }
    };

    const toggleFilter = (filter: NDataRegisterBuilder.ICollectionFilter) => {
        const f = collectionFilters?.find((f) => f.id === filter.id);
        if (!f) return;
        f.active = !f.active;
        setCollectionFilters([...collectionFilters]);
    };

    const skeletonLoading = collectionTransactionFetching || !!isLoadingTransaction || isLoading || fieldsLoading || isFetching;

    const { mutate: triggerEvent } = useCreate();
    const browserTabId = useBrowserTabId();

    const currStage = useMemo(() => stages?.find((s) => s.id === activeStage), [activeStage, stages]);
    const translate = useTranslate();

    const handleManualEventAction = (automationId: string, automationVersionId: string) => {
        const BASE_PATH = `${ENDPOINT_RESOURCES.FORM_TRANSACTION_EVENT}/manual`;
        const payload = {
            formVersionId: formVersionId,
            transactionId: transactionId,
            eventId: automationId,
            eventVersionId: automationVersionId,
            stageId: currStage?.id,
            roleId: activeRole.id,
            browserTabId,
            type: ManualEventSourceType.Collection,
        };
        triggerEvent(
            {
                dataProviderName: DataProvider.Application,
                resource: BASE_PATH,
                values: payload,
                errorNotification: false,
                successNotification: false,
            },
            {
                onSuccess: (res: any) => {
                    if (!res?.data?.data?.result) {
                        const errorTexts = res?.data?.data?.errors?.map((item: string) =>
                            translate(`event.error.${item}`, { ns: NAMESPACES.TRANSACTION }),
                        );
                        const error = errorTexts?.join(', ') ?? translate(`event.error.internal_error`, { ns: NAMESPACES.TRANSACTION });
                        notification.error({
                            message: error,
                            type: 'error',
                        });
                    } else {
                        notification.success({
                            message: translate('event.trigger_success', { ns: NAMESPACES.TRANSACTION }),
                            type: 'success',
                        });
                    }
                },
            },
        );
    };

    return (
        <>
            <section className="p-2 h-full" id={identityId}>
                <CollectionHeader
                    isPreview={isPreview}
                    hideShowAddValue={hideShowAddValue}
                    collectionFilters={collectionFilters}
                    onToggleFilter={toggleFilter}
                    collectionPermission={collectionPermission}
                    onAddCollectionValues={() => setIsAddCollectionValues(true)}
                    collapsed={collapsed}
                    groupByIds={groupByIds}
                    setCollapsed={setCollapsed}
                    automation={automation}
                    handleManualEventAction={handleManualEventAction}
                />

                <CollectionBody
                    formId={formId}
                    tableData={tableData}
                    criteriaAdditionalFields={criteriaAdditionalFields}
                    visibleColumns={visibleColumns}
                    formCollectionItems={formCollectionItems}
                    onDelete={setDeleteData}
                    isPreview={isPreview}
                    activeFields={activeFields?.data}
                    zoneStyles={zoneStyles}
                    isFetching={isFetching}
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    collectionIdentityId={collectionIdentityId as string}
                    height={height}
                    isLoading={collectionTransactionLoading}
                    skeletonLoading={skeletonLoading}
                    isAddCollectionValues={isAddCollectionValues}
                    onCloseAddCollectionValues={() => setIsAddCollectionValues(false)}
                    onAddCollectionValues={onAddCollectionValues}
                    aclData={aclData}
                    collection={collection}
                    collapsed={collapsed}
                    collectionPermission={collectionPermission}
                />
            </section>

            <ExpandCollectionZoneRender formId={formId} dataRegisterId={collection?.dataRegisterId ?? ''} collection={collection} />
        </>
    );
};
