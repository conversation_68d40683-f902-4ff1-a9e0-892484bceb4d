/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTrackChanges } from '@/common/hooks';
import { CollectionAutomationMapping } from '@/common/hooks/useFormCollectionAutomationMappings';
import { FormToolbar, ToolBarProps } from '@/components/form-toolbar';
import { ServerSelect } from '@/components/server-select';
import Response from '@/data-access/response';
import { useFormStore } from '@/stores';
import { useApiUrl, useCustomMutation, useList, useTranslation } from '@refinedev/core';
import { Button, Modal, notification, Spin, Tabs } from 'antd';
import { ItemType } from 'antd/es/menu/interface';
import _, { cloneDeep, compact, uniq } from 'lodash';
import React, { useEffect, useState } from 'react';
import { v4 } from 'uuid';
import { DataProvider, DataRegisterTypeEnum } from '../../../../../../common/enums';
import { FormCollectionItemEnum, FormCollectionItemTypeEnum } from '../../../../../../common/enums/form-collection.enum';
import { Icon } from '../../../../../../components/icon';
import {
    DATA_PROVIDER,
    ENDPOINT_RESOURCES,
    NAMESPACES,
    OBJECT_SELECTABLE_FIELD_TYPES,
    WarningOutlined,
    UnlockOutlined,
    LockOutlined,
} from '../../../../../../constants';
import { NDataRegisterBuilder, NForm } from '../../../../../../interfaces';
import { CollectionItemType, IFormCollection, TabPanePropsCustom } from '../../../../../../types/FormCollectionType';
import { parseFormFieldsToStoreFields } from '../../../../../shared/form-builder/utils';
import { FormFlowStepProps } from '../../FormFlow';
import { ChangeName } from '../action/ChangeName';
import { useFormCollectionContext } from '../contexts/FormCollectionContext';
import { formCollectionDefinition } from '../definition/form-collection-definition';
import '../styles.scss';
import { AutomationActionMappingDrawer } from './AutomationActionMapping';
import { ContextMappingDrawer } from './ContextMapping';
import { useFormLock } from '@/modules/form/hooks/useFormLock';
type CollectionSelectType = (ItemType & NDataRegisterBuilder.IDataRegisterBuilder) | any;

type Props = {
    formTemplate?: NForm.FormType;
    toolbarProps: ToolBarProps;
} & FormFlowStepProps;

export const FormCollection: React.FC<Props> = (props) => {
    const apiUrl = useApiUrl(DATA_PROVIDER.DEFINITION);

    const { formTemplate, onChangeStepCallback, toolbarProps } = props;
    const { translate } = useTranslation();

    const {
        accountId,
        tab,
        setTab,
        setCollectionActiveType,
        tabItems,
        setTabItems,
        collectionItems,
        setCollectionItems,
        setFormFields,
        formCollectionDeleteIds,
        setFormCollectionDeleteIds,
        setCurrentDataRegister,
        setFilters,
    } = useFormCollectionContext();

    const { setWarningMessage, warningMessage, fields } = useFormStore();

    const [collections, setCollections] = useState<CollectionSelectType[]>([]);
    const [openChangeName, setOpenChangeName] = useState<{
        key: string;
        name: string | undefined;
        hideShowAddValue?: boolean;
        hideDefaultCollectionValuesWhenEmpty?: boolean;
    }>();
    const [stageInitPayload, setStageInitPayload] = useState<{
        isInit: boolean;
        collectionItems: CollectionItemType;
        tabItems: TabPanePropsCustom[];
    }>({
        collectionItems: {},
        isInit: false,
        tabItems: [],
    });
    const [contextMappings, setContextMappings] = useState<{ collectionId: string; mappings: { contextId: string; fieldId: string }[] }[]>(
        [],
    );
    const [automationMappings, setAutomationMappings] = useState<{ collectionId: string; mappings: CollectionAutomationMapping[] }[]>([]);

    const collection = collectionItems?.[tab as string];
    const dfCollectionItems = collection?.collectionItem?.default || [];
    const opCollectionItems = collection?.collectionItem?.option || [];
    const dfDataGroups = compact((dfCollectionItems || []).flatMap((item) => item.setting?.dataGroup || []));
    const opDataGroups = compact((opCollectionItems || []).flatMap((item) => item.setting?.dataGroup || []));
    const dataRegisterTransactionIds = [...dfCollectionItems, ...opCollectionItems, ...(dfDataGroups || []), ...(opDataGroups || [])].map(
        (item) => item.dataRegisterTransactionId,
    );

    const uniqueDataRegisterTransactionIds = uniq(compact(dataRegisterTransactionIds));

    const [openCtxMapping, setOpenCtxMapping] = useState<boolean>(false);
    const [openAutomationActionMapping, setOpenAutomationActionMapping] = useState<boolean>(false);
    const [autoOpenContextMapping, setAutoOpenContextMapping] = useState<boolean>(true);
    const { setInitPayload, compare } = useTrackChanges();
    const { isCurrentUserHoldingLock, formLock, handleRelease, handleTakeOver } = useFormLock(formTemplate?.id as string);

    const BASE_PATH = !accountId ? ENDPOINT_RESOURCES.DATA_REGISTER : ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY;
    const FORM_COLLECTION_PATH = accountId ? ENDPOINT_RESOURCES.FORM_COLLECTION_TENANCY : ENDPOINT_RESOURCES.FORM_COLLECTION;
    //fetch data to init source
    const {
        data: formCollections,
        isLoading,
        refetch: getCollectionsByForm,
        isRefetching,
    } = useList<IFormCollection>({
        resource: `${FORM_COLLECTION_PATH}/get-collections-by-form/${formTemplate?.latestFormVersion?.id}`,
        dataProviderName: DataProvider.Definition,
        pagination: { mode: 'off' },
        queryOptions: {
            enabled: !!formTemplate?.latestFormVersion?.id,
        },
        errorNotification: () => {
            return false;
        },
    });

    const init = () => {
        let _collectionItems: CollectionItemType = {};
        const _tabItems: TabPanePropsCustom[] = [];

        if (formCollections?.data?.length) {
            formCollections?.data?.forEach((data) => {
                const type: DataRegisterTypeEnum | undefined = data?.type;
                const Component = type ? formCollectionDefinition?.[type] : React.Fragment;

                _tabItems.push({
                    label: data?.name,
                    key: data?.id ?? v4(),
                    children: (
                        <Component
                            onOpenContextMapping={onOpenContextMapping}
                            onOpenAutomationActionMapping={onOpenAutomationActionMapping}
                            isCurrentUserHoldingLock={isCurrentUserHoldingLock}
                        />
                    ),
                    dataRegisterId: data?.dataRegisterId,
                    type: data?.type ?? DataRegisterTypeEnum.Collection,
                    icon: data?.icon ? <Icon name={data?.icon} /> : undefined,
                    dataRegisterVersionId: data?.dataRegisterVersionId,
                    hideShowAddValue: (data?.displaySetting?.hideShowAddValue ?? false) as boolean,
                    hideDefaultCollectionValuesWhenEmpty: data?.displaySetting?.hideDefaultCollectionValuesWhenEmpty,
                });

                const _data: CollectionItemType = {
                    [`${data.id}`]: {
                        dataRegisterId: data?.dataRegisterId,
                        displaySetting: data?.displaySetting?.setting,
                        collectionItem: {
                            [FormCollectionItemTypeEnum.DEFAULT]: [],
                            [FormCollectionItemTypeEnum.OPTION]: [],
                        },
                        type: data?.type,
                        formCollectionId: data?.id,
                        collectionIdentityId: data?.identityId,
                    },
                };

                if (data?.formCollectionItems?.length) {
                    const _itemsGroup = _.groupBy(data?.formCollectionItems, 'type') ?? {};

                    for (const [key, value] of Object.entries(_itemsGroup)) {
                        value.forEach((_value) => {
                            _value.id = _value.id ?? v4();
                            if (_value?.setting?.dataGroup?.length) {
                                _value.setting.dataGroup.forEach((_group) => {
                                    _group.id = _group.id ?? v4();
                                });
                            }
                        });

                        _data[data.id ?? ''].collectionItem[key as FormCollectionItemTypeEnum] = value;
                    }
                }

                _collectionItems = { ..._collectionItems, ..._data };
            });
        }

        return {
            collectionItems: _collectionItems,
            tabItems: _tabItems,
        };
    };

    const collectionItemIdsKey = (uniqueDataRegisterTransactionIds || []).join(',');
    const contextMappingCombineKey = `${formTemplate?.latestVersionId ?? ''}_${tab}_${collectionItemIdsKey}`;

    useEffect(() => {
        if (props.isSaveStep) {
            onSubmit();
        }
    }, [props.isSaveStep]);

    useEffect(() => {
        //!This effect just to set the init payload to compare with the current values
        if (stageInitPayload.isInit) {
            const { updateRequest } = getRequests({
                collectionItems: stageInitPayload.collectionItems,
                tabItems: stageInitPayload.tabItems,
            });

            setInitPayload(updateRequest);
        }
    }, [stageInitPayload.isInit]);

    useEffect(() => {
        const _fields = parseFormFieldsToStoreFields(formTemplate?.latestFormVersion?.fields ?? []);
        setFormFields(_fields);
    }, [formTemplate]);

    useEffect(() => {
        if (!tab || !tabItems?.length) {
            return;
        }

        const _item = tabItems.find((item) => item.key === tab);

        if (_item) {
            setCurrentDataRegister((register) => ({
                ...register,
                ...{
                    dataRegisterId: _item?.dataRegisterId ?? '',
                    dataRegisterVersionId: _item?.dataRegisterVersionId ?? '',
                },
            }));
        }
    }, [tab, tabItems]);

    useEffect(() => {
        if (!formTemplate?.latestFormVersion?.id) return; //!important to track changes
        if (isLoading) return; //!important to track changes

        const { collectionItems: _collectionItems, tabItems: _tabItems } = init();

        setTabItems(_tabItems);
        let tabKey = _tabItems?.[0]?.key;
        if (tab && _tabItems?.length && _tabItems.some((i) => i.key === tab)) {
            tabKey = tab;
        }

        setTab(tabKey);
        setCollectionItems(_collectionItems);

        //set init payload to check changes
        setStageInitPayload({
            collectionItems: _collectionItems,
            tabItems: _tabItems,
            isInit: true,
        });
    }, [formCollections?.data, isLoading]);

    useEffect(() => {
        setAutoOpenContextMapping(true);
    }, [contextMappingCombineKey]);

    const ctxFields = Object.values(fields || {}).filter((field) => field?.type && OBJECT_SELECTABLE_FIELD_TYPES.includes(field.type));

    const onChangeCollectionName = ({ id, name, canShowAddValue }: { id: string; name?: string; canShowAddValue?: boolean }) => {
        setOpenChangeName((collections) => ({
            ...collections,
            key: '',
            name: undefined,
            collectionId: '',
            canShowAddValue,
        }));

        const _tabItems = _.cloneDeep(tabItems || []);

        for (const _collection of _tabItems) {
            if (_collection.key === id) {
                _collection.label = name ?? _collection.label;
                break;
            }
        }

        setTabItems(_tabItems);
    };

    const handleAddCollection = (collectionId: string) => {
        const collection = collections.find((c) => c.key === collectionId);

        let _collectionItems = _.cloneDeep(collectionItems);

        const _collection = {
            key: v4(),
            name: collection?.name,
        };

        const type: DataRegisterTypeEnum | undefined = collection?.type;
        const Component = type ? formCollectionDefinition?.[type] : React.Fragment;

        const _tabItems = _.cloneDeep(tabItems || []);

        const tabItem = {
            label: _collection.name,
            key: _collection.key,
            children: (
                <Component onOpenContextMapping={onOpenContextMapping} onOpenAutomationActionMapping={onOpenAutomationActionMapping} />
            ),
            dataRegisterId: collectionId,
            type: collection.type,
            dataRegisterVersionId: collection?.activeVersionId,
            icon: collection?.icon ? <Icon name={collection?.icon} /> : undefined,
        };

        _tabItems.push(tabItem);

        setTab(tabItem.key);
        setTabItems(_tabItems);

        setOpenChangeName((collections) => ({
            ...collections,
            ..._collection,
            canShowAddValue: true,
        }));

        _collectionItems = {
            ...(_collectionItems ?? {}),
            [tabItem.key]: {
                dataRegisterId: collectionId,
                collectionItem: {
                    [FormCollectionItemTypeEnum.DEFAULT]: [],
                    [FormCollectionItemTypeEnum.OPTION]: [],
                },
                type: collection?.type,
                displaySetting: null,
            },
        };

        setCollectionItems(_collectionItems);
    };

    const onTabClick = (key: string) => {
        setTab(key);
        setCollectionActiveType(FormCollectionItemEnum.DEFAULT);
        setFilters([]);
    };

    const { mutate: updateCollection, isLoading: updating } = useCustomMutation<Response<boolean>>();

    const PATH = `${apiUrl}/${accountId ? ENDPOINT_RESOURCES.FORM_COLLECTION_TENANCY : ENDPOINT_RESOURCES.FORM_COLLECTION}`;

    const onUpdate = (
        value: {
            update: IFormCollection[];
            create?: IFormCollection[];
            delete?: {
                formCollectionIds: string[];
            };
        },
        isClickSave?: boolean,
    ) => {
        updateCollection(
            {
                url: `${PATH}/${formTemplate?.latestFormVersion?.id}`,
                method: 'patch',
                values: value,
            },
            {
                onSuccess: (data) => {
                    if (!data?.data?.isSuccess) {
                        notification.error({
                            className: 'error-notification',
                            message: '',
                            description: 'Update failed',
                        });
                    } else {
                        isClickSave &&
                            notification.success({
                                className: 'success-notification',
                                message: '',
                                description: 'Update successfully',
                            });
                    }

                    //invalidate old init data
                    setStageInitPayload({
                        collectionItems: {},
                        tabItems: [],
                        isInit: false,
                    });
                    getCollectionsByForm();
                    setFormCollectionDeleteIds([]);
                },
                onSettled: () => {
                    onChangeStepCallback();
                },
            },
        );
    };

    const getRequests = ({ collectionItems, tabItems }: { collectionItems?: CollectionItemType; tabItems?: TabPanePropsCustom[] }) => {
        const createRequest: IFormCollection[] = [];
        const updateRequest: IFormCollection[] = [];

        if (!collectionItems) {
            return { createRequest, updateRequest };
        }

        const keys = Object.keys(collectionItems ?? {});
        for (const key of keys) {
            const _contextMappings = contextMappings.find((item) => item.collectionId === key);
            const _automationMappings = automationMappings.find((item) => item.collectionId === key);
            const _tabItem = tabItems?.find((tabItem) => tabItem.key === key);
            const _request: IFormCollection = {
                formVersionId: formTemplate?.latestFormVersion?.id ?? '',
                dataRegisterVersionId: _tabItem?.dataRegisterVersionId ?? '',
                formCollectionItems: [],
                type: _tabItem?.type,
                name: _tabItem?.label as string,
                icon: (_tabItem?.icon as any)?.props?.name,
                id: collectionItems[key]?.formCollectionId,
                dataRegisterId: collectionItems[key]?.dataRegisterId,
                contextMappings: (_contextMappings?.mappings || []).filter((m) => m.fieldId),
                displaySetting: {
                    hideShowAddValue: _tabItem?.hideShowAddValue ?? false,
                    hideDefaultCollectionValuesWhenEmpty: _tabItem?.hideDefaultCollectionValuesWhenEmpty,
                },
                automationMappings: (_automationMappings?.mappings || []).filter((m) => m.automationId),
            };

            const collectionItemKeys = Object.keys(collectionItems[key]?.collectionItem ?? {});
            if (collectionItems[key]?.collectionItem && collectionItemKeys?.length) {
                for (const _key of collectionItemKeys) {
                    const _values = collectionItems[key]?.collectionItem?.[_key as FormCollectionItemTypeEnum] ?? [];
                    _values.forEach((value) => {
                        value.type = _key;
                    });
                    _request.formCollectionItems = [...(_request.formCollectionItems ?? []), ..._values];
                }
            }

            if (collectionItems[key]?.formCollectionId) {
                updateRequest.push(_request);
            } else {
                createRequest.push(_request);
            }
        }

        return { createRequest, updateRequest };
    };

    const onSubmit = (isClickSave?: boolean) => {
        if (!collectionItems || !isCurrentUserHoldingLock) {
            onChangeStepCallback(true);
            return;
        }

        const { createRequest, updateRequest } = getRequests({
            collectionItems,
            tabItems,
        });

        if (!createRequest?.length && !updateRequest?.length) {
            onChangeStepCallback(true);
        }

        if (updateRequest?.length || createRequest.length || formCollectionDeleteIds?.length) {
            onUpdate(
                {
                    update: updateRequest ?? [],
                    create: createRequest ?? [],
                    delete: {
                        formCollectionIds: formCollectionDeleteIds ?? [],
                    },
                },
                isClickSave,
            );
        }
    };

    const onClose = () => {
        if (!stageInitPayload?.isInit) {
            toolbarProps.onClose?.(false);
            return;
        }

        //if formCollectionDeleteIds has length => has changes
        if (formCollectionDeleteIds?.length) {
            toolbarProps.onClose?.(true);
            return;
        }

        //if createRequest has length => has changes
        const { createRequest, updateRequest } = getRequests({ collectionItems, tabItems });
        if (createRequest?.length) {
            toolbarProps.onClose?.(true);
            return;
        }

        const { isChanged } = compare(updateRequest);
        toolbarProps.onClose?.(isChanged);
        return;
    };

    const onOpenContextMapping = () => {
        setOpenCtxMapping(true);
    };

    const onOpenAutomationActionMapping = () => {
        setOpenAutomationActionMapping(true);
    };

    const onCloseContextMapping = () => {
        setOpenCtxMapping(false);
    };

    const onSetContextMapping = (collectionId: string, values: { contextMappings: { contextId: string; fieldId: string }[] }) => {
        const clone = cloneDeep(contextMappings || []);
        const collectionContextMapping = clone.find((item) => item.collectionId === collectionId);
        if (!collectionContextMapping) {
            setContextMappings([
                ...clone,
                {
                    collectionId,
                    mappings: (values as { contextMappings: { contextId: string; fieldId: string }[] })?.contextMappings || [],
                },
            ]);
        } else {
            collectionContextMapping.mappings = values?.contextMappings || [];
            setContextMappings([...clone]);
        }
    };

    const onSetAutomationMapping = (collectionId: string, values: unknown) => {
        const clone = cloneDeep(automationMappings || []);
        const collectionAutomationMapping = cloneDeep(clone || []).find((item) => item.collectionId === collectionId);
        if (!collectionAutomationMapping) {
            setAutomationMappings([...clone, { collectionId, mappings: values as CollectionAutomationMapping[] }]);
        }
    };

    const toolbarBoxActions = [];
    if (formLock.isLocked && isCurrentUserHoldingLock) {
        toolbarBoxActions.push({
            icon: UnlockOutlined,
            label: translate('form_lock.release_edit_control', { ns: NAMESPACES.FORM }),
            key: 'releaseEditControl',
            onClick: () => {
                handleRelease();
            },
        });
    }
    if (!formLock.isLocked && !isCurrentUserHoldingLock) {
        toolbarBoxActions.push({
            icon: LockOutlined,
            label: translate('form_lock.take_over_edit_control', { ns: NAMESPACES.FORM }),
            key: 'takeOverEditControl',
            onClick: () => {
                handleTakeOver();
            },
        });
    }

    return (
        <>
            <Spin spinning={isLoading || updating || isRefetching}>
                <section>
                    <FormToolbar
                        {...props.toolbarProps}
                        formName={formTemplate?.name as string}
                        prefixActions={
                            <ServerSelect
                                dataProviderName={DataProvider.Definition}
                                resource={BASE_PATH}
                                allowClear={true}
                                placeholder={translate('collection.button.add_collection', { ns: NAMESPACES.FORM })}
                                value={null}
                                optionFilterProp="label"
                                className="w-52"
                                showSearch
                                onChange={handleAddCollection}
                                filters={[
                                    {
                                        field: 'type',
                                        operator: 'in',
                                        value: [DataRegisterTypeEnum.Collection, DataRegisterTypeEnum.Criteria],
                                    },
                                    {
                                        field: 'activeVersion',
                                        operator: 'gte',
                                        value: 0,
                                    },
                                ]}
                                searchProp="name"
                                emitOptions={setCollections}
                                disabled={!isCurrentUserHoldingLock}
                                data-testid="form-collection-add-collection-select"
                            />
                        }
                        extraActions={
                            <>
                                <Button
                                    size="middle"
                                    type="primary"
                                    htmlType="submit"
                                    onClick={() => onSubmit(true)}
                                    disabled={!isCurrentUserHoldingLock}
                                >
                                    {translate('button.save', { ns: NAMESPACES.COMMON })}
                                </Button>
                            </>
                        }
                        onClose={() => {
                            onClose();
                        }}
                        toolbarBoxActions={toolbarBoxActions}
                    />

                    <hr className="m-4 px-0" />

                    {!!tabItems?.length && (
                        <Tabs
                            items={tabItems}
                            activeKey={tab}
                            onTabClick={onTabClick}
                            className="tab-items-container"
                            data-testid="tab-items-container"
                        />
                    )}

                    {!!openChangeName?.key && (
                        <ChangeName
                            data={{
                                name: openChangeName.name,
                                id: openChangeName.key,
                                hideShowAddValue: openChangeName.hideShowAddValue,
                                hideDefaultCollectionValuesWhenEmpty: openChangeName.hideDefaultCollectionValuesWhenEmpty,
                            }}
                            onChange={onChangeCollectionName}
                        />
                    )}
                </section>
            </Spin>

            <Modal
                title={
                    <div className="flex gap-2 items-center">
                        <Icon icon={WarningOutlined} className="text-warn text-3xl" />
                        <>{translate('unsave.title', { ns: NAMESPACES.COMMON })}</>
                    </div>
                }
                centered
                closable={false}
                open={!!warningMessage}
                footer={null}
            >
                <p className="mb-2">{warningMessage}</p>
                <div className="flex justify-end">
                    <Button onClick={() => setWarningMessage()} type="primary">
                        {translate('ok', { ns: NAMESPACES.COMMON })}
                    </Button>
                </div>
            </Modal>

            <ContextMappingDrawer
                key={contextMappingCombineKey}
                versionId={formTemplate?.latestVersionId as string}
                ctxFields={ctxFields}
                open={openCtxMapping}
                collectionId={tab as string}
                collectionItemRegisterIds={uniqueDataRegisterTransactionIds}
                onClose={onCloseContextMapping}
                onSetMapping={onSetContextMapping}
                collectionIdentityId={collection?.collectionIdentityId as string}
                contextMappings={contextMappings}
                onAutoOpen={() => {
                    // if (autoOpenContextMapping) {
                    //     onOpenContextMapping();
                    //     setAutoOpenContextMapping(false);
                    // }
                    //todo: need to implement
                }}
            />

            <AutomationActionMappingDrawer
                formId={formTemplate?.id as string}
                versionId={formTemplate?.latestVersionId as string}
                open={openAutomationActionMapping}
                collectionId={tab as string}
                onClose={() => setOpenAutomationActionMapping(false)}
                collection={collection as any}
                onSetMapping={onSetAutomationMapping}
            />
        </>
    );
};
