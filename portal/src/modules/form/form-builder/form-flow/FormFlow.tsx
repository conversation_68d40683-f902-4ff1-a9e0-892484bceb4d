import { DataProvider, FieldTypeEnum, FormType, ZoneTypeEnum } from '@/common/enums';
import { useAnchor, useIsAccount } from '@/common/hooks';
import { useGetFormTemplate } from '@/common/hooks/useGetFormTemplate';
import { FORM_BUILDER_NODE_KEY, FORM_BUILDER_STEP_WF } from '@/components/form-toolbar/steps-config';
import { Icon } from '@/components/icon';
import { SaveChangesWarningModal } from '@/components/save-changes-warning-modal/SaveChangesWarningModal';
import { NAMESPACES, WarningOutlined } from '@/constants';
import { ENDPOINT_RESOURCES } from '@/constants/app-resources';
import { NDataRegisterBuilder, NForm, NUser } from '@/interfaces';
import { parseFormFieldsToStoreFields } from '@/modules/shared/form-builder/utils';
import { useFormStore } from '@/stores';
import { handleResError } from '@/utils/handleResError';
import { useCreate, useGetIdentity, useNavigation, useOne, useResource, useTranslation, useUpdate } from '@refinedev/core';
import { Button, Modal, notification } from 'antd';
import { cloneDeep, compact, debounce } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useFormAccessControlStore } from '../../../../stores/useFormAccessControlStore';
import { useWfsStore } from '../../../../stores/useWfsStore';
import { FormTemplateSteps } from '../../enums/form-flow.enum';
import { FormViewItemType } from '../../enums/form-view.enum';
import { WorkFlowStageBuilder } from '../../work-flow-stage/WorkFlowStageBuilder';
import { FormTemplateBuilder } from '../FormTemplateBuilder';
import { FormAccessControl } from './FormAccessControl';
import { WrapperFormApiBuilder } from './FormApiBuilder';
import { FormAutomation } from './FormAutomation';
import { FormRelation } from './FormRelation';
import { FormTesting } from './FormTesting';
import { FormVersion } from './FormVersion';
import { FormWidgets } from './FormWidgets';
import { FormCollection } from './form-flow-collection/builders/FormCollection';
import { FormCollectionContextProvider } from './form-flow-collection/contexts/FormCollectionContext';
import { FormInFormationStep } from './form-flow-step/FormInformationStep';
import { FormLayoutStep } from './form-flow-step/form-layout-step/FormLayoutStep';
import { FormViewStep } from './form-view-setting/FormViewStep';
import { TakeOverEditingModal } from '../TakeOverEditingModal';
import { useFormLock } from '../../hooks/useFormLock';

const DEBOUNCE_FETCH_LOGS = 1000;

export type FormFlowStepProps = {
    isOpenPreview: boolean;
    isSaveStep: boolean;
    formType: FormType;
    isLoading: boolean;
    isFetchedForm: boolean;
    isInit: boolean;
    onChangeStepCallback: (keepCurrentData?: boolean) => void;
    setIsOpenPreview: (value: boolean) => void;
    onSaveCallBack?: () => void;
    onAfterCallSave?: () => void;
    refetchForm?: () => void;
};

const StepComponents: { [key: number]: React.FC<any> } = {
    [FormTemplateSteps.Information]: FormInFormationStep,
    [FormTemplateSteps.Fields]: FormTemplateBuilder,
    [FormTemplateSteps.Collections]: (props) => {
        return (
            <FormCollectionContextProvider>
                <FormCollection {...props} />
            </FormCollectionContextProvider>
        );
    },
    [FormTemplateSteps.Widgets]: FormWidgets,
    [FormTemplateSteps.Relations]: FormRelation,
    [FormTemplateSteps.Stages]: WorkFlowStageBuilder,
    [FormTemplateSteps.Automation]: FormAutomation,
    [FormTemplateSteps.APIBuilder]: WrapperFormApiBuilder,
    [FormTemplateSteps.AccessControl]: FormAccessControl,
    [FormTemplateSteps.Layouts]: FormLayoutStep,
    [FormTemplateSteps.View]: FormViewStep,
    [FormTemplateSteps.Testing]: FormTesting,
    [FormTemplateSteps.Version]: FormVersion,
};

export const FormFlow: React.FC = () => {
    const { id } = useParams();
    const { list, edit } = useNavigation();
    const { resource } = useResource();
    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();
    const isAccount = useIsAccount();
    const { translate } = useTranslation();
    const { handleTakeOver, formLockStatusFromAPI, formLock, setFormLock, isCurrentUserHoldingLock } = useFormLock(id as string);

    const [haveChangedForm, setHaveChangedForm] = useState<boolean>(false);
    const { anchor, onSetAnchor } = useAnchor<FormTemplateSteps>();
    const [isOpenPreview, setIsOpenPreview] = useState<boolean>(false);
    const [isOpenWarning, setIsOpenWarning] = useState<boolean>(false);
    const [isSaveStep, setIsSaveStep] = useState<boolean>(false);
    const [isCloseAfterSave, setIsCloseAfterSave] = useState<boolean>(false);
    const [isOpenResetVersionModal, setIsOpenResetVersionModal] = useState<boolean>(false);
    const [isOpenTakeOverEditingModal, setIsOpenTakeOverEditingModal] = useState<boolean>(false);

    const initialCurrent = anchor ? +anchor : FormTemplateSteps.Information;
    const endStep = FormTemplateSteps.Version;
    const [intentCurrent, setIntentCurrent] = useState<number>(initialCurrent);
    const [current, setCurrent] = useState<number>(initialCurrent);
    const [nextStep, setNextStep] = useState<number>(initialCurrent !== endStep ? initialCurrent + 1 : -1);
    const activeNodeId = `${FORM_BUILDER_NODE_KEY}_${current}`;
    const {
        isFieldsReady,
        isLayoutsReady,
        setFields,
        setWidgets,
        setFormTemplate,
        setFormVersion,
        setFormLayouts,
        setFormViews,
        setRefetchViewFn,
        setDataRegisters,
        setReadyFields,
        setReadyLayouts,
        setBuilderType,
        resetStore,
    } = useFormStore();

    const { initStore } = useWfsStore();

    const {
        setStages,
        setStageTransitions,
        setStageAccessControls,
        setStageRoles,
        setStageRoleAccessControls,
        setFormCollections,
        setRelatedForms,
        setFormVersion: setFormVersionFormAccess,
        setSubscriptionId,
    } = useFormAccessControlStore();

    const initFormAccessControlStore = (formVersion: NForm.FormVersionType | undefined) => {
        setStages(formVersion?.stages ?? []);
        setStageTransitions(formVersion?.stageTransitions ?? []);
        // setStageAccessControls(formVersion?.stageAccessControls ?? []);
        setFormCollections(formVersion?.formCollections ?? []);
        setRelatedForms(formVersion?.relatedForms ?? []);
        setFormVersionFormAccess(formVersion);
        setStageRoles(formVersion?.stageRoles ?? []);
        // setStageRoleAccessControls(compact(formVersion?.stageRoles?.map((i) => i.accessControls)).flat() ?? []);
        setSubscriptionId(formTemplate?.subscriptionId ?? '');
    };

    const {
        data: formTemplate,
        refetch,
        isFetching,
        isFetched: isFetchedForm,
    } = useGetFormTemplate({
        id,
        searchQuery: {
            current,
        },
    });

    const formVersion = formTemplate?.latestFormVersion;

    const { mutate: getDataRegisters } = useCreate();

    useEffect(() => {
        if (!formVersion) return;

        const dataRegisterIds = compact(
            formVersion.fields?.filter((f) => f.type === FieldTypeEnum.Lookup).map((f) => f.configuration?.targetId),
        );
        if (!dataRegisterIds?.length) return;

        getDataRegisters(
            {
                resource: `${identity?.accountId ? ENDPOINT_RESOURCES.DATA_REGISTER_TENANCY : ENDPOINT_RESOURCES.DATA_REGISTER}/active-fields-by-ids`,
                dataProviderName: DataProvider.Definition,
                values: { dataRegisterIds },
                successNotification: false,
                errorNotification: false,
            },
            {
                onSuccess: (response: any) => {
                    const dataRegisterVersions: NDataRegisterBuilder.IDataRegisterVersion[] = response?.data?.data ?? [];
                    setDataRegisters(dataRegisterVersions);
                },
                onError: (err: unknown) => {
                    notification.error({
                        message: translate('message.get_data_register_failed', { ns: NAMESPACES.FORM }),
                        type: 'error',
                    });
                },
            },
        );
    }, [formVersion]);

    const parseToFormLayouts = () => {
        const layouts = compact(cloneDeep(formVersion?.formLayouts || []));
        layouts.forEach((layout) => {
            const parentIds = (layout.layoutZones || []).map((w) => w.parentIdentityId).filter(Boolean);

            const existedParentIds = (layout.layoutZones || [])
                .filter((w) => parentIds.includes(w.identityId))
                .map((w) => w.identityId)
                .filter(Boolean);

            layout.layoutZones = layout.layoutZones.filter((lz) => {
                if (lz.parentIdentityId) {
                    return existedParentIds.includes(lz.parentIdentityId);
                }

                return true;
            });

            layout.layoutZones.forEach((zone) => {
                if (zone.type === ZoneTypeEnum.Summary) {
                    (zone.config as NForm.SummaryZoneConfigs)?.columns?.forEach((col) => {
                        col.layoutFields = zone.layoutFields?.filter((f) => f.config?.columnId === col.columnId);
                    });
                }
            });
        });
        return layouts;
    };

    const { data: formViewData, refetch: refetchFormViews } = useOne<NForm.FormView[]>({
        dataProviderName: DataProvider.Definition,
        resource: isAccount ? ENDPOINT_RESOURCES.FORM_VIEW_TENANCY : ENDPOINT_RESOURCES.FORM_VIEW,
        id: formVersion?.id as string,
        errorNotification: false,
        successNotification: false,
        queryOptions: {
            enabled: !!formVersion?.id,
        },
    });

    useEffect(() => {
        resetStore();
        setBuilderType('form');
    }, [resetStore, setBuilderType]);

    useEffect(() => {
        if (!formViewData?.data) return;

        formViewData.data.forEach((data) => {
            const viewItems = data?.viewItems?.[FormViewItemType.Column] as NForm.FormViewItem[];
            const swimlaneViewItems = data?.viewItems?.[FormViewItemType.Swimlane] as NForm.FormViewItem[];

            if (viewItems?.length) {
                data.viewItems[FormViewItemType.Column] = viewItems.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0));
            }

            if (swimlaneViewItems?.length) {
                data.viewItems[FormViewItemType.Swimlane] = swimlaneViewItems.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0));
            }
        });

        setFormViews?.(formViewData?.data);
    }, [formViewData?.data]);

    useEffect(() => {
        setRefetchViewFn(refetchFormViews);
    }, [refetchFormViews, setRefetchViewFn]);

    useEffect(() => {
        if (!formVersion) return;
        setReadyFields(false); // trigger set fields
        setReadyLayouts(false);
        setFormVersion(formVersion);
        const widgets = parseFormFieldsToStoreFields(formVersion.widgets ?? []);
        setWidgets(widgets);
        setFormTemplate(formTemplate);
        initStore(formVersion);
        initFormAccessControlStore(formVersion);
    }, [formTemplate]);

    useEffect(() => {
        if (!isFieldsReady) {
            const fields = parseFormFieldsToStoreFields(formVersion?.fields ?? []);
            setFields(fields);
        } else {
            //after field ready, set layouts
            if (isFetchedForm) {
                setFormLayouts(parseToFormLayouts());
            }
        }
    }, [isFieldsReady]); //please not add isFetchedForm to dependencies

    useEffect(() => {
        if (formLockStatusFromAPI?.data) {
            const { isLocked, holderId } = formLockStatusFromAPI.data;
            const { isInitialState, hasReleased } = formLock;

            if (!isLocked && !holderId && !isInitialState && !hasReleased) {
                setIsOpenTakeOverEditingModal(true);
            }

            if (isInitialState || isLocked || holderId !== null || hasReleased) {
                setIsOpenTakeOverEditingModal(false);
            }
        }
    }, [formLockStatusFromAPI?.data?.isLocked, formLock.isInitialState]);

    useEffect(() => {
        if (formLockStatusFromAPI?.data) {
            const { isLocked, holderId, holderName } = formLockStatusFromAPI.data;
            const isAnyThingChanged =
                isLocked !== formLock.isLocked ||
                holderId !== formLock.holderId ||
                holderName !== formLock.holderName ||
                formLock.isInitialState;
            if (isAnyThingChanged) {
                setFormLock({
                    isLocked,
                    holderId,
                    holderName,
                    isInitialState: false,
                });
            }
        }
    }, [formLockStatusFromAPI?.data]);

    const previewSteps: number[] = [FormTemplateSteps.Fields, FormTemplateSteps.Layouts, FormTemplateSteps.View];
    const Step = StepComponents[current];

    const onChangeStep = (value: number) => {
        setIsSaveStep(true);
        setIntentCurrent(value);
    };

    const onSaveAndExit = () => {
        setIsSaveStep(true);
        setIsCloseAfterSave(true);
    };

    const onSaveCallBack = () => {
        refetch();
    };

    const onChangeStepCallback = (keepCurrentData?: boolean) => {
        setIsSaveStep(false);
        setCurrent(intentCurrent);
        onSetAnchor(intentCurrent);
        setNextStep(intentCurrent !== endStep ? intentCurrent + 1 : -1);
        if (keepCurrentData) {
            return;
        }

        refetch();

        if (isCloseAfterSave) {
            handleBack();
            return;
        }
    };

    const handleBack = () => {
        setIsOpenWarning(false);
        setIsCloseAfterSave(false);
        list(resource?.name as string);
    };

    const BASE_PATH = identity?.accountId ? ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY : ENDPOINT_RESOURCES.FORM_TEMPLATE;
    const BASE_CHANGE_LOG_PATH = identity?.accountId ? ENDPOINT_RESOURCES.FORM_CHANGE_LOG_TENANCY : ENDPOINT_RESOURCES.FORM_CHANGE_LOG;

    const { mutate: update } = useUpdate();

    const onDuplicate = () => {
        if (formTemplate?.id) {
            update(
                {
                    dataProviderName: DataProvider.Definition,
                    resource: `${BASE_PATH}/duplicate`,
                    values: {},
                    id: formTemplate?.id,
                    errorNotification: handleResError,
                    successNotification: false,
                },
                {
                    onSuccess: ({ data }) => {
                        notification.success({
                            message: 'Duplicate success',
                            type: 'success',
                        });
                        // refetch();
                        // navigate to duplicated form
                        if (typeof data?.data === 'string' && data?.data) {
                            edit(resource?.name as string, data?.data);
                        }
                    },
                },
            );
        }
    };

    const debouncedSetHaveChangedForm = debounce(() => setHaveChangedForm(false), DEBOUNCE_FETCH_LOGS);

    const onResetVerion = () => {
        if (formTemplate?.id) {
            update(
                {
                    dataProviderName: DataProvider.Definition,
                    resource: `${BASE_PATH}/reset-version`,
                    values: {},
                    id: formTemplate?.id,
                    errorNotification: handleResError,
                    successNotification: false,
                },
                {
                    onSuccess: ({ data }) => {
                        notification.success({
                            message: 'Reset success',
                            type: 'success',
                        });
                        debouncedSetHaveChangedForm(); // Delayed execution
                        refetch();
                    },
                },
            );
        }
        setIsOpenResetVersionModal(false);
    };

    const getStepLabel = (step: FormTemplateSteps) => {
        switch (step) {
            case FormTemplateSteps.Information:
                return translate('step.information', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Fields:
                return translate('step.fields', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Collections:
                return translate('step.collections', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Widgets:
                return translate('step.widgets', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Relations:
                return translate('step.relations', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Automation:
                return translate('step.automation', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Stages:
                return translate('step.wfs', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.AccessControl:
                return translate('step.access_control', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Layouts:
                return translate('step.layouts', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.View:
                return translate('step.view', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Testing:
                return translate('step.test', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.Version:
                return translate('step.version', { ns: NAMESPACES.FORM });
            case FormTemplateSteps.APIBuilder:
                return translate('step.api_builder', { ns: NAMESPACES.FORM });
            default:
                return '';
        }
    };

    const onClose = (isChange?: boolean) => {
        if (isChange && isCurrentUserHoldingLock) {
            setIsOpenWarning(true);
        } else {
            handleBack();
        }
    };

    const onCancel = () => {
        setIsOpenWarning(false);
    };

    const handleResetVersionModalCancel = () => {
        setIsOpenResetVersionModal(false);
    };

    const showResetVersionModal = () => {
        setIsOpenResetVersionModal(true);
    };

    const onAfterCallSave = () => {
        setIsSaveStep(false);
    };

    const isContainerLoading = isFetching || !isFieldsReady || !isLayoutsReady;

    return (
        <>
            <section className="flex flex-col h-full">
                <article className="p-0 h-full">
                    <Step
                        formTemplateRefetch={refetch}
                        formTemplate={formTemplate}
                        id={id}
                        isOpenPreview={isOpenPreview}
                        setIsOpenPreview={setIsOpenPreview}
                        isSaveStep={isSaveStep}
                        onSaveCallBack={onSaveCallBack}
                        onChangeStepCallback={onChangeStepCallback}
                        isLoading={isContainerLoading}
                        isInit={!isFetching && isFieldsReady}
                        onAfterCallSave={onAfterCallSave}
                        isFetchedForm={isFetchedForm}
                        refetchForm={() => {
                            refetch();
                        }}
                        toolbarProps={{
                            activeNodeId,
                            current,
                            next: nextStep,
                            currentLabel: getStepLabel(current),
                            nextLabel: getStepLabel(nextStep),
                            previewSteps,
                            formType: FormType.FormBuilder,
                            initialNodes: FORM_BUILDER_STEP_WF.nodes.map((node) => ({
                                ...node,
                                data: {
                                    ...node.data,
                                    label: translate(`${node.data.label}`, { ns: NAMESPACES.FORM }),
                                },
                            })),
                            initialEdges: FORM_BUILDER_STEP_WF.edges,
                            onChangeStep,
                            onClose,
                            isContainerLoading,
                            setIsOpenPreview,
                            version: formTemplate?.latestVersion,
                            status: formTemplate?.status,
                            onDuplicate,
                            showResetVersionModal,
                            resource: BASE_CHANGE_LOG_PATH,
                            formId: formTemplate?.id,
                            formVersionId: formTemplate?.latestVersionId,
                            haveChangedForm,
                            setHaveChangedForm,
                            formVersion: formTemplate?.latestVersion,
                        }}
                    />
                </article>
            </section>

            <SaveChangesWarningModal onBack={handleBack} isOpen={isOpenWarning} onCancel={onCancel} onSave={onSaveAndExit} />

            <TakeOverEditingModal
                isOpen={isOpenTakeOverEditingModal}
                onCancel={() => setIsOpenTakeOverEditingModal(false)}
                onConfirm={handleTakeOver}
            />

            <Modal
                open={isOpenResetVersionModal}
                title={
                    <div className="flex gap-2 items-center">
                        <Icon icon={WarningOutlined} className="text-warn text-3xl" />
                        <span>Warning</span>
                    </div>
                }
                centered
                footer={null}
            >
                <p className="mb-2">{'All your changes will be lost, do you want to continue?'}</p>
                <div className="flex gap-2 justify-end">
                    <Button onClick={onResetVerion} type="primary">
                        Reset Version
                    </Button>
                    <Button onClick={handleResetVersionModalCancel}>Cancel</Button>
                </div>
            </Modal>
        </>
    );
};
