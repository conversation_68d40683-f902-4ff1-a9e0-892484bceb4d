import { useFormStore } from '@/stores';
import { HttpError, useCreate, useOne } from '@refinedev/core';
import { ENDPOINT_RESOURCES } from '@/constants/app-resources';
import { DataProvider } from '@/common/enums';
import { useGetIdentity } from '@refinedev/core';
import { NUser } from '@/interfaces';
import { useTranslation } from '@refinedev/core';
import { NAMESPACES } from '@/constants';
import { notification } from 'antd';

interface IFormLockStatus {
    isLocked: boolean;
    holderId: string | null;
    holderName: string | null;
    message: string;
}

export const useFormLock = (formId: string) => {
    const { formLock, setFormLock } = useFormStore();
    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();
    const { translate } = useTranslation();

    const { data: formLockStatusFromAPI, refetch: refetchFormLockStatus } = useOne<IFormLockStatus>({
        resource: `${ENDPOINT_RESOURCES.FORM_LOCK}/${formId}/status`,
        dataProviderName: DataProvider.Definition,
        queryOptions: {
            enabled: !!formId,
        },
    });

    const { mutate: acquireFormLock } = useCreate();
    const { mutate: releaseFormLock } = useCreate();

    const handleTakeOver = () => {
        if (!formId) return;

        acquireFormLock(
            {
                resource: `${ENDPOINT_RESOURCES.FORM_LOCK}/${formId}/acquire`,
                dataProviderName: DataProvider.Definition,
                values: {},
                successNotification: false,
                errorNotification: false,
            },
            {
                onSuccess: () => {
                    // Update local store immediately for better UX
                    setFormLock({
                        isLocked: true,
                        holderId: identity?.id || null,
                        holderName: identity ? `${identity.firstName} ${identity.secondName}` : null,
                    });

                    notification.success({
                        message: translate('form_lock.acquire_form_lock_success', { ns: NAMESPACES.FORM }),
                        type: 'success',
                    });
                    refetchFormLockStatus();
                },
                onError: (err: unknown) => {
                    notification.error({
                        message: translate('form_lock.acquire_form_lock_failed', { ns: NAMESPACES.FORM }),
                        type: 'error',
                    });
                },
            },
        );
    };

    const handleRelease = () => {
        if (!formId) return;

        releaseFormLock(
            {
                resource: `${ENDPOINT_RESOURCES.FORM_LOCK}/${formId}/release`,
                dataProviderName: DataProvider.Definition,
                values: {},
                successNotification: false,
                errorNotification: false,
            },
            {
                onSuccess: () => {
                    // Update local store immediately for better UX
                    setFormLock({
                        isLocked: false,
                        holderId: null,
                        holderName: null,
                        hasReleased: true,
                    });

                    notification.success({
                        message: translate('form_lock.release_form_lock_success', { ns: NAMESPACES.FORM }),
                        type: 'success',
                    });
                    refetchFormLockStatus();
                },
                onError: (err: unknown) => {
                    notification.error({
                        message: translate('form_lock.release_form_lock_failed', { ns: NAMESPACES.FORM }),
                        type: 'error',
                    });
                },
            },
        );
    };

    const isCurrentUserHoldingLock = formLock.isLocked && formLock.holderId === identity?.id;

    return {
        formLock,
        formLockStatusFromAPI,
        isCurrentUserHoldingLock,
        handleTakeOver,
        handleRelease,
        refetchFormLockStatus,
        setFormLock,
    };
};
