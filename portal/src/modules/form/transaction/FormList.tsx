import { FavoriteTypeEnum } from '@/common/enums/favorite.enum';
import { MenuItemType } from '@/constants/layout';
import { useResourceStore } from '@/stores';
import { mapAntdFilterToCrudFilter, mapAntdSorterToCrudSorting } from '@refinedev/antd';
import {
    BaseKey,
    CrudFilters,
    CrudSorting,
    useGetIdentity,
    useList,
    useNavigation,
    useTable,
    useTranslation,
    useUpdate,
} from '@refinedev/core';
import { Button, Dropdown, Input, InputRef, notification, Table, TableColumnsType, TableColumnType, Skeleton } from 'antd';
import { FilterDropdownProps, FilterValue, SorterResult, TablePaginationConfig } from 'antd/es/table/interface';
import { ItemType } from 'antd/lib/menu/interface';
import dayjs from 'dayjs';
import { useRef } from 'react';
import { DataProvider, DefaultResource } from '../../../common/enums';
import { Icon } from '../../../components/icon';
import {
    DEFAULT_DATE_TIME_FORMAT,
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
    ENDPOINT_RESOURCES,
    HolderOutlined,
    NAMESPACES,
    SearchOutlined,
} from '../../../constants';
import { NForm, NSubscription, NUser } from '../../../interfaces';
import { getUserFullName } from '../../../utils';
import { formatDisplayDateTimeValue } from '../../shared/form-builder/utils';
import { TableWithSkeleton } from '../../../components/table/TableWithSkeleton';

const TABLE_ID = 'formList';

const DEFAULT_SORTER = [
    {
        field: 'updatedAt',
        order: 'desc',
    },
] satisfies CrudSorting;

export const FormListComponent: React.FC = () => {
    const { translate } = useTranslation();
    const { mutate } = useUpdate();

    const resourceData = useResourceStore.use.data();
    const addFavoriteItem = useResourceStore.use.addFavoriteItem();
    const removeFavoriteItem = useResourceStore.use.removeFavoriteItem();

    const navigate = useNavigation();

    const searchInput = useRef<InputRef>(null);
    const searchIcon = (filtered: boolean) => <Icon icon={SearchOutlined} style={{ color: filtered ? '#1677ff' : undefined }} />;

    const getColumnSearchProps = (): TableColumnType<NForm.FormType> => ({
        filterDropdown: ({ setSelectedKeys, confirm }: { setSelectedKeys: any; confirm: FilterDropdownProps['confirm'] }) => (
            <div className="p-2" onKeyDown={(e) => e.stopPropagation()}>
                <Input
                    allowClear
                    ref={searchInput}
                    placeholder={translate('search')}
                    onChange={(e) => setSelectedKeys(e.target.value ? e.target.value : null)}
                    onPressEnter={(_) => confirm()}
                />
            </div>
        ),
        filterIcon: searchIcon,
        onFilterDropdownOpenChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
        render: (text) => text,
    });

    const renderActions = (value: unknown, record: NForm.FormType, index: number) => {
        return (
            <Dropdown menu={{ items: getActionItems(value, record, index) }} placement="bottom" trigger={['click']}>
                <Button
                    className="more-button"
                    size="small"
                    onClick={(e) => e.stopPropagation()}
                    icon={<Icon icon={HolderOutlined} />}
                    type="default"
                />
            </Dropdown>
        );
    };

    const columns: TableColumnsType<NForm.FormType> = [
        {
            title: translate('column.name', { ns: NAMESPACES.FORM }),
            dataIndex: 'name',
            width: 300,
            ellipsis: true,
            sorter: true,
            ...getColumnSearchProps(),
        },
        {
            title: translate('column.version', { ns: NAMESPACES.FORM }),
            dataIndex: 'activeVersion',
        },
        {
            title: translate('column.created_at', { ns: NAMESPACES.FORM }),
            dataIndex: 'createdAt',
            sorter: true,
            render: (value) => value && <p>{dayjs(value).format(DEFAULT_DATE_TIME_FORMAT)}</p>,
        },
        {
            title: translate('column.created_by', { ns: NAMESPACES.FORM }),
            dataIndex: 'created',
            key: 'created',
            render: (created: NUser.IUserIdentity) => <p>{getUserFullName(created)}</p>,
        },
        {
            title: translate('column.published_at', { ns: NAMESPACES.FORM }),
            dataIndex: 'publishedAt',
            sorter: true,
            render: (value) => value && <p>{dayjs(value).format(DEFAULT_DATE_TIME_FORMAT)}</p>,
        },
        {
            title: translate('column.published_by', { ns: NAMESPACES.FORM }),
            dataIndex: 'published',
            render: (published: NUser.IUserIdentity) => <p>{getUserFullName(published)}</p>,
        },

        {
            title: translate('column.last_edited', { ns: NAMESPACES.FORM }),
            dataIndex: 'updated',
            key: 'updated',
            render: (_: string, record: NForm.FormType) => (
                <>
                    {record.updated ? (
                        <div className="flex gap-x-2">
                            <span>{getUserFullName(record.updated as unknown as NUser.IUserIdentity)}</span>
                            <span>-</span>
                            <span>
                                {formatDisplayDateTimeValue({
                                    value: record.updatedAt,
                                    format: DEFAULT_DATE_TIME_FORMAT,
                                })}
                            </span>
                        </div>
                    ) : undefined}
                </>
            ),
        },
        {
            title: translate('action'),
            key: 'operation',
            fixed: 'right',
            align: 'center',
            width: 100,
            onCell: () => {
                return {
                    onClick: (event) => {
                        event.stopPropagation();
                    },
                };
            },
            render: renderActions,
        },
    ];

    const { refetch: refreshFavorites } = useList<NUser.IFavoriteMenu>({
        resource: `${ENDPOINT_RESOURCES.USER}/favorite-menus`,
        dataProviderName: DataProvider.Configuration,
        pagination: { mode: 'off' },
        queryOptions: {
            enabled: false,
        },
    });

    const toggleFavorite = (type: NSubscription.ISubscriptionMenuType, record: NForm.FormType) => {
        if (!type) return;

        const _isFavorite = resourceData.favorites?.find((i) => {
            return i.type === FavoriteTypeEnum.FORM_TRANSACTION && i.value === `forms/${record.id}`;
        });

        const request = {
            type,
            router: `${DefaultResource.Forms}/${record?.id}`,
            favoriteType: FavoriteTypeEnum.FORM_TRANSACTION,
            imageUrl: record.icon ?? 'FileOutlined',
            displayName: record?.name,
        };

        mutate(
            {
                dataProviderName: DataProvider.Configuration,
                resource: 'v1/users/favorite',
                id: record?.id as BaseKey,
                values: request,
                errorNotification: {
                    message: _isFavorite
                        ? translate('message.remove_favorite_failed', { ns: 'register-builder' })
                        : translate('message.add_favorite_failed', { ns: 'register-builder' }),
                    type: 'error',
                },
                successNotification: false,
            },
            {
                onSuccess: () => {
                    notification.success({
                        type: 'success',
                        message: _isFavorite
                            ? translate('message.remove_favorite_successfully', { ns: 'register-builder' })
                            : translate('message.add_favorite_successfully', { ns: 'register-builder' }),
                    });

                    const menuItem: MenuItemType = {
                        type: FavoriteTypeEnum.FORM_TRANSACTION,
                        icon: (props?: { className?: string }) => <Icon name={request.imageUrl || ''} className={props?.className} />,
                        userTypes: identity?.userType ? [identity.userType] : [],
                        label: record.name,
                        value: `forms/${record.id}`,
                        key: `${record?.id}_${type}`,
                    };

                    if (_isFavorite) {
                        removeFavoriteItem(menuItem);
                    } else {
                        addFavoriteItem(menuItem);
                    }

                    refreshFavorites();

                    tableQueryResult.refetch();
                },
            },
        );
    };

    const getActionItems = (_: unknown, record: NForm.FormType, _index: number): ItemType[] => {
        const _isFavorite = resourceData.favorites?.find((i) => {
            return i.type === FavoriteTypeEnum.FORM_TRANSACTION && i.value === `forms/${record.id}`;
        });

        const items: ItemType[] = [
            {
                label: (
                    <a
                        onClick={(e) => {
                            e.stopPropagation();
                            toggleFavorite('list', record);
                        }}
                    >
                        {_isFavorite
                            ? translate('action.remove_favorites', { ns: 'register-builder' })
                            : translate('action.add_favorites', { ns: 'register-builder' })}
                    </a>
                ),
                key: 'show',
            },
        ];

        return items;
    };

    const onChangeTable = (
        pagination: TablePaginationConfig,
        tableFilters: Record<string, FilterValue | null>,
        sorter: SorterResult<any> | SorterResult<any>[],
    ) => {
        pagination.current && setCurrent(pagination.current);

        const crudSorting: CrudSorting = mapAntdSorterToCrudSorting(sorter || []);
        setSorters(crudSorting);

        const crudFilters: CrudFilters = mapAntdFilterToCrudFilter(tableFilters, filters);
        crudFilters.forEach((filter) => {
            if (filter.operator == 'eq') {
                filter.operator = 'contains';
            }
        });
        setFilters(crudFilters);
    };

    const { data: identity } = useGetIdentity<NUser.IUserIdentity>();
    const isTenancy = !!identity?.accountId;

    const PATH = isTenancy ? `${ENDPOINT_RESOURCES.FORM_TEMPLATE_TENANCY}/by-roles` : ENDPOINT_RESOURCES.FORM_TEMPLATE;

    const { tableQueryResult, setCurrent, setSorters, setFilters, filters, current, pageSize } = useTable<NForm.FormType>({
        dataProviderName: DataProvider.Definition,
        resource: PATH,
        initialPageSize: DEFAULT_PAGE_SIZE,
        initialCurrent: DEFAULT_PAGE_INDEX,
        sorters: {
            initial: DEFAULT_SORTER,
        },
        meta: {
            searchQuery: {
                isPublishedForm: true,
                roleIds: identity?.roles?.map((r) => r.id) ?? [],
            },
        },
        syncWithLocation: false,
        hasPagination: true,
    });

    return (
        <section className="p-4 flex flex-col gap-2">
            <TableWithSkeleton
                id={TABLE_ID}
                columns={columns}
                dataSource={tableQueryResult?.data?.data}
                pagination={{
                    current,
                    pageSize,
                    total: tableQueryResult.data?.total,
                    position: ['bottomCenter'],
                    showSizeChanger: false,
                }}
                onChange={onChangeTable}
                scroll={{
                    x: 'max-content',
                }}
                rowKey={(record) => record.id || Math.random().toString()}
                onRow={(record) => {
                    return {
                        onClick: (event) => {
                            event.stopPropagation();
                            record?.activeVersionId && navigate.push(`/${DefaultResource.Forms}/${record.id}`);
                        },
                    };
                }}
                rowClassName="row-selector"
                loading={tableQueryResult.isFetching}
                skeletonLoading={tableQueryResult.isLoading}
                skeletonRows={20}
                skeletonColumns={columns.length}
            />
        </section>
    );
};
