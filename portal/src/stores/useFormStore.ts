import { DataRegisterTypeEnum, FieldTypeEnum } from '@/common/enums';
import { FormFieldType, FormFields } from '@/modules/shared/form-builder/types';
import { parseArrayToObject } from '@/modules/shared/form-builder/utils';
import { validateOnD<PERSON>te<PERSON>ield, validateOnlyOneAssigneeField, validateOnlyOneRoleField } from '@/utils';
import { cloneDeep } from 'lodash';
import { create } from 'zustand';
import { ASSIGNEE_FIELD_ID, ROLE_FIELD_ID } from '../constants';
import { NDataRegisterBuilder, NForm } from '../interfaces';

export type DataRegisters = {
    [dataRegisterId: string]: NDataRegisterBuilder.IDataRegisterVersion;
};

interface FormLockState {
    isLocked: boolean;
    holderId: string | null;
    holderName: string | null;
    isInitialState?: boolean;
    hasReleased?: boolean;
}

type FormStore = {
    populatedFields?: FormFields;
    registerTransactionId?: string;
    builderType?: string;
    fields: FormFields;
    widgets: FormFields;
    selectedFieldId?: string;
    warningMessage?: string;
    fieldValues?: any;
    formTemplate?: NForm.FormType;
    formVersion?: NForm.FormVersionType;
    formLayouts: NForm.FormLayout[];
    formViews: NForm.FormView[];
    type?: DataRegisterTypeEnum;
    dataRegisters: DataRegisters;
    isFieldsReady?: boolean;
    isLayoutsReady?: boolean;
    formLock: FormLockState;
    refetchViewFn?: () => void;
    setFields: (fields?: FormFields) => void;
    setWidgets: (widgets?: FormFields) => void;
    setField: (field: FormFieldType, openSettings?: boolean) => void;
    getField: (fieldId: string) => FormFieldType | undefined;
    deleteField: (fieldId: string) => void;
    setSelectedFieldId: (selectedFieldId?: string) => void;
    setWarningMessage: (message?: string) => void;
    setFieldValues: (values: any) => void;
    updateField: (field: FormFieldType) => void;
    setFormTemplate: (formTemplate: NForm.FormType) => void;
    getFormTemplate: () => NForm.FormType | undefined;
    handleDropField: (props: { fieldId: string; groupId?: string }) => void;
    setFormVersion: (formVersion: NForm.FormVersionType) => void;
    setFormLayouts: (layouts: NForm.FormLayout[]) => void;
    setFormViews: (layouts: NForm.FormView[]) => void;
    setRefetchViewFn: (fn: () => void) => void;
    setType: (type: DataRegisterTypeEnum) => void;
    resetStore: (options?: { ignoreViews: boolean }) => void;
    setDataRegisters: (dataRegisterVersions: NDataRegisterBuilder.IDataRegisterVersion[]) => void;
    setDataRegister: (dataRegisterVersion: NDataRegisterBuilder.IDataRegisterVersion) => void;
    setReadyFields: (value: boolean) => void;
    setReadyLayouts: (value: boolean) => void;
    setBuilderType: (builderType: string) => void;
    setRegisterTransactionId: (registerTransactionId: string) => void;
    setPopulatedFields: (fields: FormFields) => void;
    setFormLock: (formLockState: FormLockState) => void;
};

const initState = {
    name: '',
    fields: {},
    widgets: {},
    fieldValues: {},
    formLayouts: [],
    formViews: [],
    registerType: undefined,
    dataRegisters: {},
    isFieldsReady: false,
    isLayoutsReady: false,
    registerTransactionId: undefined,
    formLock: {
        isLocked: false,
        holderId: null,
        holderName: null,
        isInitialState: true,
        hasReleased: false,
    },
};

export const useFormStore = create<FormStore>()((set, get) => ({
    ...initState,
    setFields: (fields) =>
        set(() => {
            return { fields, isFieldsReady: true };
        }),
    setWidgets: (widgets) =>
        set(() => {
            return { widgets };
        }),
    setField: (field: FormFieldType, openSettings = true) => {
        set((state) => {
            const warningMessage =
                validateOnlyOneAssigneeField({
                    field: field,
                    fields: state.fields,
                }) ??
                validateOnlyOneRoleField({
                    field: field,
                    fields: state.fields,
                });
            if (warningMessage) {
                return {
                    warningMessage,
                };
            }
            if (field.type === FieldTypeEnum.UserLookup && field.isAssignee) {
                field.fieldId = ASSIGNEE_FIELD_ID;
            }

            if (field.type === FieldTypeEnum.RoleLookup) {
                field.fieldId = ROLE_FIELD_ID;
            }

            state.fields = {
                ...state.fields,
                [field.fieldId]: field,
            };
            if (openSettings) {
                state.selectedFieldId = field.fieldId;
            }
            return { ...state };
        });
    },
    getField: (fieldId: string) => get().fields[fieldId],
    deleteField: (fieldId: string) => {
        set((state) => {
            const field = state.fields[fieldId];
            if (!field) {
                return state;
            }
            if (field.isDefault) {
                return {
                    warningMessage: 'Cannot delete default field',
                };
            }

            const ctxMappings = cloneDeep(state.formTemplate?.latestFormVersion?.contextMappings || []);
            const collections = state.formTemplate?.latestFormVersion?.formCollections || [];
            const formContextMappings = ctxMappings
                .filter((ctx) => collections.some((c) => c.identityId === ctx.collectionIdentityId))
                .map((ctx) => {
                    const _collection = collections.find((c) => c.identityId === ctx.collectionIdentityId);
                    ctx.collectionName = _collection?.name as string;
                    return ctx;
                });

            const warningMessage = validateOnDeleteField({
                deletedField: state.fields[fieldId],
                fields: state.fields,
                formVersion: state.formVersion,
                formLayouts: state.formLayouts,
                formView: state.formViews,
                formContextMappings,
            });

            if (warningMessage) {
                return {
                    warningMessage,
                };
            }

            const deletedFieldValue = { ...state.fields };
            delete deletedFieldValue[fieldId];

            return {
                ...state,
                fields: {
                    ...deletedFieldValue,
                },
            };
        });
    },
    setSelectedFieldId: (selectedFieldId?: string) => {
        set(() => ({
            selectedFieldId,
        }));
    },

    setWarningMessage: (warningMessage?: string) => {
        set(() => ({
            warningMessage,
        }));
    },
    setFieldValues: (values: any) => {
        set((state) => {
            state.fieldValues = {};
            Object.keys(values).map((key) => {
                state.fieldValues[key] = values[key];
            });
            return { ...state };
        });
    },
    updateField: (field: FormFieldType) => {
        set((state) => {
            state.fields[field.fieldId] = field;
            return { ...state };
        });
    },
    setFormTemplate: (value: NForm.FormType) => {
        set((state) => {
            state.formTemplate = value;
            return { ...state };
        });
    },
    getFormTemplate: () => get().formTemplate,
    handleDropField: (props: { fieldId: string; groupId?: string }) => {
        set((state) => {
            const { fieldId, groupId } = props;
            const field = state.fields[fieldId];
            if (field) {
                state.fields[fieldId] = {
                    ...field,
                    groupId,
                    dataGrid: {
                        ...field.dataGrid,
                        y: 9999,
                        x: 0,
                    },
                };
            }
            return { ...state };
        });
    },
    setFormVersion: (formVersion: NForm.FormVersionType) => {
        set((state) => {
            state.formVersion = formVersion;
            return { ...state };
        });
    },
    setFormLayouts: (layouts: NForm.FormLayout[]) => {
        set((state) => {
            state.formLayouts = layouts;
            state.isLayoutsReady = true;
            return { ...state };
        });
    },
    setFormViews: (views: NForm.FormView[]) => {
        set((state) => {
            state.formViews = views;
            return { ...state };
        });
    },
    setRefetchViewFn: (fn: () => void) => {
        set((state) => {
            state.refetchViewFn = fn;
            return { ...state };
        });
    },
    setType: (type: DataRegisterTypeEnum) => {
        set((state) => {
            state.type = type;
            return { ...state };
        });
    },
    resetStore: ({ ignoreViews = false }: { ignoreViews?: boolean } = {}) => {
        if (ignoreViews) {
            set((state) => {
                const currentFormViews = state.formViews;
                const currentFormLock = state.formLock;
                return { ...initState, formViews: currentFormViews, formLock: currentFormLock };
            });
        } else {
            set(() => ({ ...initState }));
        }
    },
    setDataRegisters: (dataRegisterVersions: NDataRegisterBuilder.IDataRegisterVersion[]) => {
        set((state) => {
            const dataRegisters = parseArrayToObject(dataRegisterVersions, 'dataRegisterId');

            state.dataRegisters = dataRegisters;
            return { ...state };
        });
    },

    setDataRegister: (dataRegisterVersion: NDataRegisterBuilder.IDataRegisterVersion) => {
        set((state) => {
            state.dataRegisters[dataRegisterVersion.dataRegisterId] = dataRegisterVersion;
            return { ...state };
        });
    },

    setReadyFields: (value: boolean) => {
        set((state) => {
            return { ...state, isFieldsReady: value };
        });
    },
    setReadyLayouts: (value: boolean) => {
        set((state) => {
            return { ...state, isFieldsReady: value };
        });
    },
    setBuilderType: (builderType: string) => {
        set((state) => {
            return { ...state, builderType };
        });
    },
    setRegisterTransactionId: (registerTransactionId: string) => {
        set((state) => {
            return { ...state, registerTransactionId };
        });
    },
    setPopulatedFields: (fields: FormFields) => {
        set((state) => {
            return { ...state, populatedFields: fields };
        });
    },
    setFormLock: (formLockState: FormLockState) => {
        set((state) => {
            return { ...state, formLock: formLockState };
        });
    },
}));
