import { AutomationActionFunctionType, AutomationContextType, DataRegisterEventEnum, TransactionEventEnum } from '../common/enums';
import { CASUALTY_REPORT_FILTER_FIELDS, PSC_REPORT_FILTER_FIELDS } from './field';

export const Automation = {
    event: {
        type: 'configuration.type',
        scheduler: 'configuration.scheduler',
    },
    rules: 'rules',
    actions: 'actions',
    rule: {
        name: 'name',
        text: 'text',
        description: 'description',
        conditions: 'configuration.conditions',
        isEnable: 'isEnable',
        runMode: 'configuration.runMode',
        triggerFields: 'configuration.triggerFields',
        triggerWorkFlowStage: 'configuration.triggerWorkFlowStage',
        dataSource: 'configuration.dataSources',
        selectedCollectionIds: 'configuration.selectedCollectionIds',
        collections: 'configuration.collections',
    },
    action: {
        name: 'name',
        description: 'description',
        order: 'order',
        configuration: 'configuration',
    },
};

export const FORM_EVENT_TYPES = [
    TransactionEventEnum.FORM_TRANSACTION_CREATED,
    TransactionEventEnum.FORM_TRANSACTION_UPDATED,
    TransactionEventEnum.FORM_TRANSACTION_STAGE_UPDATED,
    TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
    TransactionEventEnum.FORM_TRANSACTION_SCHEDULED,
    TransactionEventEnum.FORM_TRANSACTION_MANUAL,
    TransactionEventEnum.FORM_TRANSACTION_STAGE_KPI_STATUS_CHANGED,
];

export const DR_EVENT_TYPES = [
    DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED,
    DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
    DataRegisterEventEnum.DATA_REGISTER_RECORD_DELETED,
    DataRegisterEventEnum.DATA_REGISTER_RECORD_FIELD_UPDATED,
    DataRegisterEventEnum.DATA_REGISTER_SCHEDULED,
];

export const EXTERNAL_EVENT_TYPES = [TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED];

export const FORM_ACTION_TYPES = [
    AutomationActionFunctionType.CREATE_TRANSACTION,
    AutomationActionFunctionType.CREATE_RELATED_TRANSACTION,
    AutomationActionFunctionType.UPDATE_TRANSACTION_FIELD,
    AutomationActionFunctionType.CHANGE_TRANSACTION_WORKFLOW_STAGE,
    AutomationActionFunctionType.CREATE_UPDATE_REGISTER,
    AutomationActionFunctionType.UPDATE_COLLECTION_FIELD,
    AutomationActionFunctionType.EMAIL,
    AutomationActionFunctionType.PDF,
    AutomationActionFunctionType.POPULATE_PURPLETRAC,
    AutomationActionFunctionType.ROLlUP_FORM_TRANSACTIONS,
    AutomationActionFunctionType.ROLLUP_REGISTER_RECORDS,
    AutomationActionFunctionType.CALCULATE_SYSTEM_SCORE,
    AutomationActionFunctionType.DATA_PIPELINE_POPULATE_Q88,
    AutomationActionFunctionType.WEBHOOK,
    AutomationActionFunctionType.RUN_LLM,
];

export const DR_ACTION_TYPES = [
    AutomationActionFunctionType.CREATE_REGISTER,
    AutomationActionFunctionType.CREATE_TRANSACTION,
    AutomationActionFunctionType.UPDATE_REGISTER_FIELD,
    AutomationActionFunctionType.CREATE_UPDATE_REGISTER,
    AutomationActionFunctionType.EMAIL,
    AutomationActionFunctionType.CREATE_TRANSACTION_BY_DATASOURCE,
    AutomationActionFunctionType.DATA_PIPELINE_POPULATE_Q88,
];

export const EXTERNAL_ACTION_TYPES = [AutomationActionFunctionType.CREATE_TRANSACTION_BY_EXTERNAL];

export const ACTION_TYPES = {
    [AutomationContextType.FormTransaction]: FORM_ACTION_TYPES,
    [AutomationContextType.DataRegister]: DR_ACTION_TYPES,
    [AutomationContextType.EXTERNAL]: EXTERNAL_ACTION_TYPES,
};

export const DATA_SOURCE_DETAILS_FIELDS = {
    ['PSC']: [
        {
            label: 'Number of deficiencies',
            value: PSC_REPORT_FILTER_FIELDS.NumberOfDeficiencies,
            type: 'number',
        },
        {
            label: 'Detention',
            value: PSC_REPORT_FILTER_FIELDS.Detention,
            type: 'checkbox',
        },
        {
            label: 'Last updated date',
            value: PSC_REPORT_FILTER_FIELDS.LastUpdated,
            type: 'datetimePicker',
        },
    ],
    ['Casualty']: [
        {
            label: 'Casualty date',
            value: CASUALTY_REPORT_FILTER_FIELDS.CasualtyDate,
            type: 'datetimePicker',
        },
        {
            label: 'Number of missing',
            value: CASUALTY_REPORT_FILTER_FIELDS.NumberOfMissing,
            type: 'number',
        },
        {
            label: 'Number of dead',
            value: CASUALTY_REPORT_FILTER_FIELDS.NumberOfDead,
            type: 'number',
        },
        {
            label: 'Number of injured',
            value: CASUALTY_REPORT_FILTER_FIELDS.NumberOfInjured,
            type: 'number',
        },
        {
            label: 'Last updated date',
            value: CASUALTY_REPORT_FILTER_FIELDS.LastUpdated,
            type: 'datetimePicker',
        },
    ],
};
