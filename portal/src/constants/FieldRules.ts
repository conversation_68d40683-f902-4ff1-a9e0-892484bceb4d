import { FieldTypeEnum } from '../common/enums';

export const CALCULABLE_FIELD_TYPES = [
    FieldTypeEnum.Number,
    FieldTypeEnum.Duration,
    FieldTypeEnum.Lookup,
    FieldTypeEnum.DatePicker,
    FieldTypeEnum.DatetimePicker,
    FieldTypeEnum.TimePicker,
    FieldTypeEnum.Calculation,
    FieldTypeEnum.Rollup,
];
export const CALCULABLE_FIELD_RULES = {
    [FieldTypeEnum.Number]: [FieldTypeEnum.Number, FieldTypeEnum.Calculation],
    [FieldTypeEnum.DatetimePicker]: [
        FieldTypeEnum.DatePicker,
        FieldTypeEnum.DatetimePicker,
        FieldTypeEnum.Duration,
        FieldTypeEnum.Calculation,
    ],
    [FieldTypeEnum.DatePicker]: [FieldTypeEnum.DatePicker, FieldTypeEnum.DatetimePicker, FieldTypeEnum.Duration, FieldTypeEnum.Calculation],
    [FieldTypeEnum.Duration]: [FieldTypeEnum.DatePicker, FieldTypeEnum.DatetimePicker, FieldTypeEnum.Calculation],
    [FieldTypeEnum.Calculation]: CALCULABLE_FIELD_TYPES,
    [FieldTypeEnum.Text]: [] as FieldTypeEnum[],
    [FieldTypeEnum.TextArea]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Lookup]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Select]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Rollup]: [] as FieldTypeEnum[],
    [FieldTypeEnum.TimePicker]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Separator]: [] as FieldTypeEnum[],
    [FieldTypeEnum.UiGroup]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Checkbox]: [] as FieldTypeEnum[],
    [FieldTypeEnum.UserLookup]: [] as FieldTypeEnum[],
    [FieldTypeEnum.RoleLookup]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Answer]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Comparison]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Document]: [] as FieldTypeEnum[],
    [FieldTypeEnum.Sire2Answer]: [] as FieldTypeEnum[],

    [FieldTypeEnum.Definable]: [] as FieldTypeEnum[],
};
