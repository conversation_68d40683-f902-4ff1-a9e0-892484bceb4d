import { AccessControlType } from '../common/enums/access-control-type.enum';
import { ApiBuilderEventType } from '../common/enums/api-builder/api-builder-event.enum';
import {
    API_ENDPOINTS,
    ApiBuilderContextType,
    ApiBuilderFlowEnum,
    ApiBuilderStatus,
    ApiBuilderVersionStatus,
} from '../common/enums/api-builder/api-builder.enum';
import { API_ENDPOINT_METHOD_TYPE } from '../modules/api-builder/constants';
import { BaseDto } from './common';

export declare namespace NApiBuilder {
    export type ApiBuilder = BaseDto & {
        name: string;
        status: ApiBuilderStatus;
        contextType: ApiBuilderContextType;
        contextId: string;
        contextName?: string;
        activeVersion: number;
        latestVersion: number;
        activeVersionId?: string;
        latestVersionId?: string;
        description?: string;
        subscriptionId?: string;
        subscription?: string;
        publishedBy?: string | null;
        publishedByUser?: string | null;
        publishedAt?: Date;
        latestApiVersion?: ApiBuilderVersion;
        activeApiVersion?: ApiBuilderVersion;
        path?: string;
        httpMethod?: string;
    };

    export type ApiBuilderVersion = BaseDto & {
        apiBuilderId: string;
        version: number;
        contextVersionId: string;
        contextVersion?: { version: string };
        eventType?: ApiBuilderEventType;
        status: ApiBuilderVersionStatus;
        configuration?: {
            fieldMapping?: EndpointFieldMappingItem[];
            [key: string]: any;
        };
        publishedBy?: string | null;
        publishedByUser?: string | null;
        publishedAt?: Date;
        endpoints?: ApiBuilderEndpoint[];
        isRunning?: boolean;
        isEnable?: boolean;
    };

    export type ApiBuilderEndpoint = BaseDto & {
        identityId?: string;
        apiVersionId: string;
        name: string;
        actionType: string;
        endpoint?: API_ENDPOINTS;
        method: API_ENDPOINT_METHOD_TYPE;
        configuration?: {
            isEnabled?: boolean;
            exposedFields?: EndpointConfigurationExposedField[];
            fieldMapping?: EndpointFieldMappingItem[];
            [key: string]: any;
        };
    };

    export type ApiBuilderFormType = {
        id?: string;
        apiVersionId?: string;
        subscriptionId?: string;

        [ApiBuilderFlowEnum.Information]?: {
            name?: string;
            icon?: string;
            description?: string;
            contextType?: ApiBuilderContextType;
            contextId?: string;
        };

        [ApiBuilderFlowEnum.Configuration]?: Record<string, any>;
    };

    export type UpdateApiBuilderType = {
        id: string;
        apiVersionId?: string;
        step: ApiBuilderFlowEnum;
        stepData: UpdateApiBuilderInformationType | UpdateApiBuilderConfigurationType;
    };

    export type UpdateApiBuilderInformationType = {
        name?: string;
        description?: string;
        icon?: string;
    };

    export type UpdateApiBuilderConfigurationType = Record<string, any>;

    export type ScheduleConfigurationType = Record<string, any>;

    export type ApiBuilderLog = {
        [key: string]: any;
    };

    export type ApiBuilderActionLog = {
        [key: string]: any;
    };

    export type ApiBuilderActionCollection = {
        apiBuilderId: string;
        apiBuilderVersionId: string;
        config: {
            styles: {
                icon: string;
                label: string;
            };
        };
    };

    export type EndpointConfiguration = {
        isEnabled?: boolean;
        exposedFields?: {
            [stageIdentityId: string]: {
                fields: {
                    [fieldId: string]: EndpointConfigurationExposedField;
                };
                collections: {
                    [collectionIdentityId: string]: {
                        [collectionItemIdentityId: string]: {
                            [fieldId: string]: EndpointConfigurationExposedField;
                        };
                    };
                };
            };
        };
        fieldMapping: EndpointFieldMapping;
    };

    export type EndpointConfigurationExposedField = {
        targetId: string;
        type: AccessControlType.FIELD | AccessControlType.COLLECTION;
        collectionIdentityId?: string;
        collectionItemIdentityId?: string;
        stageIdentityId: string;
        isExposed: boolean;
    };

    export type EndpointFieldMappingItem = {
        fieldId: string;
        type: 'field' | 'collection' | 'collectionField';
        collectionIdentityId?: string;
        key: string;
        fieldType?: string;
    };

    export type EndpointFieldMapping = {
        fields: {
            [fieldId: string]: EndpointFieldMappingItem;
        };
        collections: {
            [collectionIdentityId: string]: EndpointFieldMappingItem;
        };

        collectionFields: {
            [collectionIdentityId: string]: {
                [fieldId: string]: EndpointFieldMappingItem;
            };
        };
    };
}
