import { DataProvider } from '@/common/enums';
import { DEFAULT_PAGE_SIZE, ENDPOINT_RESOURCES } from '@/constants';
import { FloatSelect, FloatSelectProps } from '@/form-components';
import { CrudFilters, CrudSort, LogicalFilter, useTable } from '@refinedev/core';
import { compact, debounce, uniqBy } from 'lodash';
import { useCallback, useEffect, useState } from 'react';

export type ServerSelectProps = {
    dataProviderName: DataProvider;
    resource: (typeof ENDPOINT_RESOURCES)[keyof typeof ENDPOINT_RESOURCES];
    filters?: CrudFilters;
    sorters?: CrudSort[];
    searchProp?: string | string[];
    valueProp?: string;
    labelProp?: string;
    enabled?: boolean;
    filterValues?: string[];
    setRegisterType?: (type: string) => void;
    labelBuilder?: (data: Record<string, any>) => string;
    idBuilder?: (data: Record<string, any>) => string;
    defaultOption?: boolean;
    emitOptions?: (data: Array<any>) => void;
    searchQuery?: Record<string, string>;
    selectedOptions?: string[];
    displayValue?: string;
} & FloatSelectProps;

export function ServerSelect({
    filters,
    dataProviderName,
    resource,
    enabled,
    labelProp,
    valueProp,
    filterValues,
    labelBuilder,
    idBuilder,
    emitOptions,
    setRegisterType,
    searchProp,
    defaultOption,
    prefix,
    sorters,
    searchQuery,
    selectedOptions,
    displayValue,
    ...restProps
}: ServerSelectProps) {
    // const [searchFilter, setSearchFilter] = useState<CrudFilters>(filters ? filters : []);
    const [internalSorters, setInternalSorters] = useState<CrudSort[]>(sorters ? sorters : []);

    console.log('contextIdResourceProps', filters);

    const {
        tableQueryResult,
        filters: tableFilters,
        current,
        setCurrent,
        setFilters,
    } = useTable({
        dataProviderName,
        resource,
        errorNotification: false,
        successNotification: false,
        queryOptions: {
            enabled,
            cacheTime: 5 * 60 * 1000, // Cache for 5 minutes
            staleTime: 5 * 60 * 1000,
        },
        filters: {
            initial: filters ?? [],
            // permanent: searchFilter?.length ? searchFilter : filters,
            defaultBehavior: 'replace',
        },
        sorters: {
            initial: internalSorters?.length ? internalSorters : sorters,
            permanent: internalSorters?.length ? internalSorters : sorters,
        },
        pagination: {
            mode: 'server',
            pageSize: DEFAULT_PAGE_SIZE,
        },
        syncWithLocation: false,
        meta: {
            searchQuery,
        },
    });

    const [options, setOptions] = useState<{ label: string; value?: string | number | null }[]>([]);

    const debouncedSearch = debounce((value) => {
        const searchField = searchProp ?? 'searchTerm';
        const newFilters = tableFilters?.filter((f) => (f as LogicalFilter).field !== searchField) as LogicalFilter[];
        setFilters([]);

        if (value) {
            setCurrent(1);
            if (typeof searchField === 'string') {
                newFilters.push({
                    field: searchField,
                    operator: 'contains',
                    value,
                });
            } else {
                const searchFields = (searchField as string[]) ?? [];
                searchFields.forEach((field) => {
                    const existFilterField = newFilters.find((f) => f.field === field);
                    if (existFilterField) {
                        existFilterField.value = value;
                    } else {
                        newFilters.push({
                            field: field,
                            operator: 'contains',
                            value: value,
                        });
                    }
                });
            }
        } else {
            setCurrent(1);
            if (typeof searchField === 'string') {
                const existFilterField = newFilters.find((f) => f.field === searchField);
                if (existFilterField) {
                    existFilterField.value = value;
                } else {
                    newFilters.push({
                        field: searchField,
                        operator: 'contains',
                        value,
                    });
                }
            } else {
                const searchFields = (searchField as string[]) ?? [];
                searchFields.forEach((field) => {
                    const existFilterField = newFilters.find((f) => f.field === field);
                    if (existFilterField) {
                        existFilterField.value = value;
                    } else {
                        newFilters.push({
                            field: field,
                            operator: 'contains',
                            value: value,
                        });
                    }
                });
            }
        }

        // setSearchFilter(newFilters);
        setFilters(newFilters);
    }, 300);

    useEffect(() => {
        if (
            tableQueryResult.data?.type &&
            ['vessel', 'company', 'country', 'port', 'terminal', 'berth'].includes(tableQueryResult.data?.type)
        ) {
            setRegisterType?.(tableQueryResult.data.type);
        }

        const valueArray = Array.isArray(restProps.value) ? restProps.value : compact([restProps.value]);

        const selectedOptionsExceptCurrent = selectedOptions?.filter((item) => !valueArray.includes(item)) ?? [];

        let newOptions = (tableQueryResult.data?.data || []).map((item) => {
            const value = !idBuilder ? item[valueProp ?? 'id'] : idBuilder?.(item);
            return {
                ...item,
                value: value,
                label: !labelBuilder ? (item[labelProp ?? 'name'] as string) : labelBuilder?.(item),
                key: !idBuilder ? (item.id as string) : idBuilder?.(item),
                disabled: selectedOptionsExceptCurrent?.length ? selectedOptionsExceptCurrent.includes(value) : false,
            };
        });

        if (filterValues?.length) {
            newOptions = newOptions.filter((item) => filterValues.includes(item.value));
        }

        const uniqueOptions = uniqBy(current === 1 ? newOptions : [...options, ...newOptions], 'value');

        setOptions(uniqueOptions);

        !tableQueryResult?.isLoading && emitOptions?.(uniqueOptions); //edit options list to parent component can use for formatting data or anything else
    }, [tableQueryResult.data?.data, current, selectedOptions]);

    useEffect(() => {
        if (filters?.length) {
            setFilters(filters);
        }
    }, [JSON.stringify(filters)]);

    const onPopupScroll = useCallback(
        (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
            const target = e.target as unknown as { scrollTop: number; offsetHeight: number; scrollHeight: number };
            const scrollTop = target?.scrollTop ?? 0;
            const offsetHeight = target?.offsetHeight ?? 0;
            const scrollHeight = target?.scrollHeight ?? 0;

            if (scrollHeight > 0 && scrollTop + offsetHeight >= scrollHeight) {
                if (
                    tableQueryResult?.data &&
                    options.length < tableQueryResult.data?.total &&
                    !tableQueryResult.isRefetching &&
                    !tableQueryResult.isLoading
                ) {
                    setCurrent(current + 1);
                }
            }
        },
        [current, options.length, tableQueryResult.data?.total, tableQueryResult.isRefetching, tableQueryResult.isLoading],
    );

    return (
        <div className="flex gap-2" key={(restProps as any)?.name}>
            {prefix}
            <FloatSelect
                {...restProps}
                options={options}
                defaultValue={defaultOption && options.length ? options[0].value : null}
                onPopupScroll={onPopupScroll}
                loading={tableQueryResult.isFetching}
                popupClassName="max-h-[300px] overflow-y-scroll"
                onSearch={(value: string) => {
                    debouncedSearch(value);
                }}
                displayValue={displayValue}
                filterOption={false}
            />
        </div>
    );
}
