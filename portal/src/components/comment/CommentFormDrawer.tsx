import { DATA_PROVIDER, NAMESPACES } from '@/constants';
import { useTranslation, useUpdate } from '@refinedev/core';
import { Button, Drawer, DrawerProps, Modal, notification } from 'antd';
import { compact } from 'lodash';
import React, { useState } from 'react';
import { FieldValues, FormProvider, useForm } from 'react-hook-form';
import { CommentForm } from './CommentForm';
import { useFormLock } from '@/modules/form/hooks/useFormLock';

type Props = {
    id: string;
    isOpen: boolean;
    onClose: (val?: boolean) => void;
    drawerProps: DrawerProps;
    resource: string;
    title?: string;
    isFormVersion?: boolean;
};
export const CommentFormDrawer: React.FC<Props> = ({ id, isOpen, onClose, drawerProps, resource, title, isFormVersion = false }) => {
    const methods = useForm();
    const { mutate: update, isLoading: isUpdating } = useUpdate();
    const { translate } = useTranslation();
    const { handleRelease } = useFormLock(id);

    const [error, setError] = useState<string | undefined>();
    const [names, setNames] = useState<string | undefined>();

    const onCreateNewVersion = (values: FieldValues) => {
        update(
            {
                dataProviderName: DATA_PROVIDER.DEFINITION,
                resource: `${resource}/release`,
                id,
                values: { ...values },
                successNotification: false,
                errorNotification: false,
            },
            {
                onError: (error) => {
                    const errorCode = error?.response?.data?.error ?? 'comment.create_new_version_failed';
                    if (errorCode === 'validations.invalid_mapping_context') {
                        const newCtxs: Array<{ collectionId: string; collectionName: string; contextName: string }> =
                            error?.response?.data?.data?.response || [];
                        const collectionErrorObj = newCtxs.reduce(
                            (acc, err) => {
                                acc[err.collectionId] = err.collectionName;
                                return acc;
                            },
                            {} as Record<string, string>,
                        );

                        const collectionNames = compact(Object.values(collectionErrorObj)).join(', ');
                        setNames(collectionNames);
                    }

                    setError(errorCode);
                },
                onSuccess: () => {
                    notification.success({
                        message: translate('comment.create_new_version_success', { ns: NAMESPACES.FORM }),
                        type: 'success',
                    });

                    if (isFormVersion) {
                        handleRelease();
                    }
                },
                onSettled: () => {
                    onClose(true);
                },
            },
        );
    };
    return (
        <>
            <Drawer
                open={isOpen}
                onClose={() => {
                    onClose();
                }}
                title={
                    <div className="flex gap-2 items-center justify-between">
                        <h4>{title ?? ''}</h4>
                        <div className="flex gap-x-2">
                            <Button
                                onClick={() => {
                                    onClose();
                                }}
                            >
                                {translate('button.close')}
                            </Button>

                            <Button type="primary" onClick={methods.handleSubmit(onCreateNewVersion)} loading={isUpdating}>
                                {translate('button.save')}
                            </Button>
                        </div>
                    </div>
                }
                {...drawerProps}
            >
                <FormProvider {...methods}>
                    <CommentForm />
                </FormProvider>
            </Drawer>

            <Modal
                onCancel={() => {
                    setError(undefined);
                    setNames(undefined);
                }}
                title={<h3 className="font-bold">{translate('validations.validation_errors', { ns: NAMESPACES.FORM })}</h3>}
                open={!!error}
                onOk={() => {
                    setError(undefined);
                    setNames(undefined);
                }}
                cancelButtonProps={{ hidden: true }}
            >
                <p className="font-semibold">{translate(error ?? '', { ns: NAMESPACES.FORM, names })}</p>
            </Modal>
        </>
    );
};
