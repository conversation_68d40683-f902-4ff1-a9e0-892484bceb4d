// export enum FormTenancyFlow {
//     Information = 'information',
//     Fields = 'fields',
//     Collections = 'collections',
//     Relations = 'relations',
//     Stages = 'stages',
//     AccessControl = 'accessControl',
//     Layouts = 'layouts',
//     Summary = 'summary',
//     Version = 'version',
//     View = 'view',
// }

// export enum FormTemplateFlow {
//     Information = 'information',
//     Fields = 'fields',
//     Collections = 'collections',
//     Relations = 'relations',
//     Stages = 'stages',
//     Layouts = 'layouts',
//     Summary = 'summary',
//     View = 'view',
// }

const formTemplateFlow = [
    'information',
    'fields',
    'collections',
    'relations',
    'stages',
    'layouts',
    'summary',
    'view',
    'automation',
    'api_builder',
] as const;
const formTenancyFlow = [...formTemplateFlow, 'accessControl', 'version'] as const;

export type FormTemplateFlow = (typeof formTemplateFlow)[number];
export type FormTenancyFlow = (typeof formTenancyFlow)[number];
