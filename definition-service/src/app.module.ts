import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TerminusModule } from '@nestjs/terminus';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestContextModule } from 'nestjs-request-context';

import { APP_INTERCEPTOR } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ContextInterceptor } from './common/src/application/context/ContextInterceptor';
import { CommonFormACLModule } from './common/src/modules/acl/form-acl.module';
import { CommonAuthModule } from './common/src/modules/auth/auth.module';
import { AppLoggerMiddleware } from './common/src/modules/shared';
import { DatabaseModule } from './database/src/database.module';
import { DatabaseOptions } from './database/src/interfaces/database-options-provider.interface';
import { LoggingInterceptor } from './interceptors/http-log.interceptor';
import { AggregationModule } from './modules/aggregation/aggregation.module';
import { AutomationModule } from './modules/automation/automation.module';
import { DataRegisterModule } from './modules/data-register/data-register.module';
import { DocumentModule } from './modules/document/document.module';
import { FormModule } from './modules/form/form.module';
import { GeneralAutoPopulateSettingModule } from './modules/general-auto-populate-setting/general-auto-populate-setting.module';
import { SubAccountModule } from './modules/sub-account/sub-account.module';
import { WidgetModule } from './modules/widget/widget.module';
import { WorkspaceModule } from './modules/workspace/workspace.module';
import { AppConfigService } from './shared/services/app-config.service';
import { SharedModule } from './shared/shared.module';
import { ScoreModelModule } from './modules/score-model/score-model.module';
import { ApiModule } from './modules/api/api.module';

const databaseModule = DatabaseModule.forRootAsync({
    useFactory: (appConfig: AppConfigService) => {
        return { connectionString: appConfig.typeOrmPostgreSqlConfig } as DatabaseOptions;
    },
    inject: [AppConfigService],
});

@Module({
    imports: [
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [AppConfigService],
            useFactory: async (configService: AppConfigService) => ({
                secret: configService.jwtConfig.appSecret,
                signOptions: { expiresIn: configService.jwtConfig.expire },
            }),
        }),
        RequestContextModule,
        TerminusModule,
        TypeOrmModule.forRootAsync({
            imports: [SharedModule],
            useFactory: (configService: AppConfigService) => configService.typeOrmPostgreSqlConfig,
            inject: [AppConfigService],
        }),
        SharedModule,
        CommonAuthModule.forRootAsync({
            useFactory: () => ({}),
            extraProviders: [],
            imports: [databaseModule],
        }),
        databaseModule,
        EventEmitterModule.forRoot({
            wildcard: true,
        }),
        AutomapperModule.forRoot({
            strategyInitializer: classes(),
        }),
        SubAccountModule,
        DataRegisterModule,
        DocumentModule,
        FormModule,
        WidgetModule,
        CommonFormACLModule.forRootAsync({
            useFactory: () => ({}),
            extraProviders: [],
            imports: [databaseModule],
        }),
        WorkspaceModule,
        AggregationModule,
        GeneralAutoPopulateSettingModule,
        AutomationModule,
        ScoreModelModule,
        ApiModule,
    ],
    controllers: [AppController],
    providers: [
        AppService,
        {
            provide: APP_INTERCEPTOR,
            useClass: ContextInterceptor,
        },
        {
            provide: APP_INTERCEPTOR,
            useClass: LoggingInterceptor,
        },
    ],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer): void {
        consumer.apply(AppLoggerMiddleware).forRoutes('*');
    }
}
