import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class GetFormCollectionLayoutRequest {
    @ApiProperty()
    @IsString()
    collectionItemIdentityId: string;

    @ApiProperty()
    @IsString()
    formVersionId: string;
}

export class GetRegisterCollectionLayoutRequest {
    @ApiProperty()
    @IsOptional()
    @IsString()
    registerTransactionId: string;
}
