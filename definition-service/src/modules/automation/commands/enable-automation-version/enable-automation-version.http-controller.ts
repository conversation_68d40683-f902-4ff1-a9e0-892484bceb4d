import { Body, Controller, HttpStatus, Put, UseGuards, Version } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../../guards/user-type.guard';
import { ApiErrorResponse } from '../../../../shared/api/api-error.response';
import { IdResponse } from '../../../../shared/api/id.response.dto';
import { BaseController } from '../../../../shared/common/base.controller';
import { AutomationAlreadyExistsError } from '../../domain/automation.errors';
import { EnableAutomationVersionCommand } from './enable-automation-version.command';
import { EnableAutomationVersionRequestDto } from './enable-automation-version.request.dto';
import { EnableAutomationAllVersionCommand } from './enable-automation-all-version.command';
import { FormLockGuard } from '@/modules/form/guards/form-lock.guard';

@Controller({
    path: ['automation-versions'],
})
@ApiTags('Automation version')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class EnableAutomationVersionController extends BaseController {
    constructor(private readonly _commandBus: CommandBus) {
        super();
    }

    @ApiOperation({ summary: 'Enable  Automation Version' })
    @ApiResponse({
        status: HttpStatus.OK,
        type: IdResponse,
    })
    @ApiResponse({
        status: HttpStatus.CONFLICT,
        description: AutomationAlreadyExistsError.message,
        type: ApiErrorResponse,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Version('1')
    @Put('toggle')
    @UseGuards(FormLockGuard)
    async enable(@Body() body: EnableAutomationVersionRequestDto) {
        const command = new EnableAutomationVersionCommand(body);
        const result = await this._commandBus.execute(command);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Enable Automation All Version' })
    @ApiResponse({
        status: HttpStatus.OK,
        type: IdResponse,
    })
    @ApiResponse({
        status: HttpStatus.CONFLICT,
        description: AutomationAlreadyExistsError.message,
        type: ApiErrorResponse,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Version('1')
    @Put('bulk-toggle')
    @UseGuards(FormLockGuard)
    async enableAll(@Body() body: EnableAutomationVersionRequestDto) {
        const command = new EnableAutomationAllVersionCommand(body);
        const result = await this._commandBus.execute(command);
        return this.getResponse(true, result);
    }
}

@Controller({
    path: ['automation-versions-tenancy'],
})
@ApiTags('Automation version tenancy')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class EnableAutomationVersionTenancyController extends BaseController {
    constructor(private readonly _commandBus: CommandBus) {
        super();
    }

    @ApiOperation({ summary: 'Enable  Automation Version' })
    @ApiResponse({
        status: HttpStatus.OK,
        type: IdResponse,
    })
    @ApiResponse({
        status: HttpStatus.CONFLICT,
        description: AutomationAlreadyExistsError.message,
        type: ApiErrorResponse,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.ACCOUNT, UserType.ACCOUNT_ADMIN)
    @Version('1')
    @Put('toggle')
    @UseGuards(FormLockGuard)
    async enable(@Body() body: EnableAutomationVersionRequestDto) {
        const command = new EnableAutomationVersionCommand(body);
        const result = await this._commandBus.execute(command);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Enable  Automation All Version' })
    @ApiResponse({
        status: HttpStatus.OK,
        type: IdResponse,
    })
    @ApiResponse({
        status: HttpStatus.CONFLICT,
        description: AutomationAlreadyExistsError.message,
        type: ApiErrorResponse,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.ACCOUNT, UserType.ACCOUNT_ADMIN)
    @Version('1')
    @Put('bulk-toggle')
    @UseGuards(FormLockGuard)
    async enableAll(@Body() body: EnableAutomationVersionRequestDto) {
        const command = new EnableAutomationAllVersionCommand(body);
        const result = await this._commandBus.execute(command);
        return this.getResponse(true, result);
    }
}
