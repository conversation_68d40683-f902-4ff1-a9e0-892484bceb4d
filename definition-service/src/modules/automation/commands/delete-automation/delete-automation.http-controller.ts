import { Controller, Delete, HttpStatus, NotFoundException as NotFoundHttpException, Param, UseGuards, Version } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { DeleteAutomationCommand } from './delete-automation.command';
import { NotFoundException } from '@shared/exceptions';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ApiErrorResponse } from '@shared/api/api-error.response';
import { JwtAuthGuard } from '@/guards/jwt-auth.guard';
import { UserTypeGuard } from '@/guards/user-type.guard';
import { UserTypes } from '@/decorators/user-type.decorator';
import { UserType } from '@/database/src/shared/enums/user-type.enum';
import { FormLockGuard } from '@/modules/form/guards/form-lock.guard';

@Controller({
    path: ['automation'],
})
@ApiTags('Automation templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class DeleteAutomationHttpController {
    constructor(private readonly commandBus: CommandBus) {}

    @ApiOperation({ summary: 'Delete a automation' })
    @ApiResponse({
        description: 'Automation deleted',
        status: HttpStatus.OK,
    })
    @ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: NotFoundException.message,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Version('1')
    @Delete(':id')
    @UseGuards(FormLockGuard)
    async deleteAutomation(@Param('id') id: string): Promise<void> {
        const command = new DeleteAutomationCommand({ id });
        const result = await this.commandBus.execute(command);
        return result;
    }
}

@Controller({
    path: ['automation-tenancy'],
})
@ApiTags('Automation templates tenancy')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class DeleteAutomationTenancyHttpController {
    constructor(private readonly commandBus: CommandBus) {}

    @ApiOperation({ summary: 'Delete a automation' })
    @ApiResponse({
        description: 'Automation deleted',
        status: HttpStatus.OK,
    })
    @ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: NotFoundException.message,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Version('1')
    @Delete(':id')
    @UseGuards(FormLockGuard)
    async deleteAutomation(@Param('id') id: string): Promise<void> {
        const command = new DeleteAutomationCommand({ id });
        const result = await this.commandBus.execute(command);
        return result;
    }
}
