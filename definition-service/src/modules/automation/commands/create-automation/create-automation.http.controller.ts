import { UserType } from '@/database/src/shared/enums/user-type.enum';
import { UserTypes } from '@/decorators/user-type.decorator';
import { JwtAuthGuard } from '@/guards/jwt-auth.guard';
import { UserTypeGuard } from '@/guards/user-type.guard';
import { AutomationAlreadyExistsError } from '@/modules/automation/domain/automation.errors';
import { Body, Controller, HttpStatus, Post, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ApiErrorResponse } from '@shared/api/api-error.response';
import { IdResponse } from '@shared/api/id.response.dto';
import { BaseController } from '../../../../shared/common/base.controller';
import { ResponseDto } from '../../../../shared/common/dto/response.dto';
import { CreateAutomationCommand } from './create-automation.command';
import { CreateAutomationRequestDto } from './create-automation.request.dto';
import { CreateAutomationService } from './create-automation.service';
import { FormLockGuard } from '@/modules/form/guards/form-lock.guard';

@Controller({
    path: ['automation'],
})
@ApiTags('Automation templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class CreateAutomationHttpController extends BaseController {
    constructor(private readonly _createAutomationService: CreateAutomationService) {
        super();
    }

    @ApiOperation({ summary: 'Create a Automation' })
    @ApiResponse({
        status: HttpStatus.OK,
        type: IdResponse,
    })
    @ApiResponse({
        status: HttpStatus.CONFLICT,
        description: AutomationAlreadyExistsError.message,
        type: ApiErrorResponse,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Version('1')
    @Post()
    @UseGuards(FormLockGuard)
    async create(@Body() body: CreateAutomationRequestDto): Promise<ResponseDto<string>> {
        const command = new CreateAutomationCommand(body);
        const result = await this._createAutomationService.create(command);
        return this.getResponse(true, result);
    }
}

@Controller({
    path: ['automation-tenancy'],
})
@ApiTags('Automation templates tenancy')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class CreateAutomationTenancyHttpController extends BaseController {
    constructor(private readonly _createAutomationService: CreateAutomationService) {
        super();
    }

    @ApiOperation({ summary: 'Create a Automation' })
    @ApiResponse({
        status: HttpStatus.OK,
        type: IdResponse,
    })
    @ApiResponse({
        status: HttpStatus.CONFLICT,
        description: AutomationAlreadyExistsError.message,
        type: ApiErrorResponse,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Version('1')
    @Post()
    @UseGuards(FormLockGuard)
    async create(@Body() body: CreateAutomationRequestDto): Promise<ResponseDto<string>> {
        const command = new CreateAutomationCommand(body);
        const result = await this._createAutomationService.create(command);
        return this.getResponse(true, result);
    }
}
