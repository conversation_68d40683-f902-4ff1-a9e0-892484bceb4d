import { RequestContextService } from '@/common/src/application/context/AppRequestContext';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { DataSource, EntityManager, In, Repository } from 'typeorm';
import { v7 } from 'uuid';
import { QueryBuilderService } from '../../../common/src';
import { OrderOptionDto } from '../../../common/src/modules/shared/dtos/order-option.dto';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { AutomationRuleEntity } from '../../../database/src/entities/public/automation-rule.public.entity';
import { AutomationVersionEntity } from '../../../database/src/entities/public/automation-version.public.entity';
import { AutomationEntity } from '../../../database/src/entities/public/automation.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { FormEntity } from '../../../database/src/entities/public/form.public.entity';
import { SubscriptionEntity } from '../../../database/src/entities/public/subscription.public.entity';
import { AutomationTenancyEntity } from '../../../database/src/entities/tenancy/automation.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { AutomationContextType, AutomationStatus, AutomationVersionStatus } from '../../../database/src/shared/enums/automation.enum';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { UpdateAutomationStep } from '../../../shared/enums/update-automation-step.enum';
import { DataRegisterDataService } from '../../data-register/services/data/data-register.data.service';
import { FormVersionDataService } from '../../form/services/data/form-version.data.service';
import { CreateAutomationCommand } from '../commands/create-automation/create-automation.command';
// import { DeleteAutomationAllVersionCommand } from '../commands/delete-automation-version/delete-automation-all-version.command';
import { VersioningAutomationVersionPort } from '../domain/ports/out/versioning-automation-version.port';
import { UpdateAutomationInformationRequest, UpdateAutomationRequest } from '../dtos/request/update-automation.request';
import { AutomationRuleDto } from '../dtos/respsone/automation-rule.dto';
import { AutomationVersionDto } from '../dtos/respsone/automation-version.dto';
import { AutomationDto } from '../dtos/respsone/automation.dto';
import { AutomationConfigurationService } from './automation-configuration.service';
import { AutomationContentService } from './automation.content.service';

@Injectable()
export class AutomationDataService {
    constructor(
        @InjectMapper()
        private readonly _mapper: Mapper,

        private readonly _contentService: AutomationContentService,

        private readonly _queryBuilder: QueryBuilderService,

        @Inject(PROVIDER_KEYS.DATA_SOURCE) private readonly _dataSource: DataSource,

        // @Inject(PROVIDER_KEYS.TENANT_CONNECTION) private readonly _tenantDataSource: DataSource,

        private readonly _dataSourceService: DataSourceService,

        private readonly _automationConfigurationService: AutomationConfigurationService,

        private readonly _dataRegisterDataService: DataRegisterDataService,

        private readonly _formVersionDataService: FormVersionDataService,
    ) {}

    public async getList({
        query,
        accountId,
    }: {
        query: MultipleFilterRequestDto;
        accountId?: string;
        // repository: Repository<AutomationEntity>;
    }): Promise<PaginationResponseDto<AutomationEntity>> {
        const { order, filters, sort, skip, take } = query;
        const alias = 'fb';

        const dataSource = await this.getDataSource(accountId);
        const isAccount = !!accountId;

        const repository = isAccount ? dataSource.getRepository(AutomationTenancyEntity) : dataSource.getRepository(AutomationEntity);

        const builder = repository.createQueryBuilder(alias);

        if (filters?.length) {
            this._queryBuilder.applyQueryFilters(builder, alias, filters, []);
        }

        // if (query.isPublished) {
        //     builder.andWhere(`${alias}.activeVersionId is not null`);
        // }

        if (query.sort && query.order) {
            const sorters: OrderOptionDto[] = [
                {
                    field: sort,
                    order: order,
                },
            ];
            this._queryBuilder.applySorters(builder, alias, sorters);
        }

        const [data, total] = await builder.skip(skip).take(take).getManyAndCount();

        return {
            data,
            total,
        };
    }

    public async getDetail({ id, accountId }: { id: string; accountId?: string }): Promise<AutomationDto> {
        const dataSource = await this.getDataSource(accountId);
        const isAccount = !!accountId;

        const { automation, automationVersion, automationRules, activeVersion, activeAutomationRules } = await this._contentService.get({
            dataSource,
            id,
            isAccount,
            type: 'both',
        });

        const builder = automation ? this._mapper.map(automation, AutomationEntity, AutomationDto) : null;
        if (automationVersion) {
            builder.latestAutomationVersion = this._mapper.map(automationVersion, AutomationVersionEntity, AutomationVersionDto);
            builder.latestAutomationVersion.rules = this._mapper.mapArray(automationRules, AutomationRuleEntity, AutomationRuleDto);
        }

        if (activeVersion) {
            builder.activeAutomationVersion = this._mapper.map(activeVersion, AutomationVersionEntity, AutomationVersionDto);
            builder.activeAutomationVersion.rules = this._mapper.mapArray(activeAutomationRules, AutomationRuleEntity, AutomationRuleDto);
        }

        if (builder.subscriptionId) {
            const subscriptionRepo = this._dataSource.getRepository(SubscriptionEntity);
            const subscriptionEntity = await subscriptionRepo.findOneBy({ id: builder.subscriptionId });
            builder.subscription = subscriptionEntity.name;
        }

        if (builder.contextId) {
            switch (builder.contextType) {
                case AutomationContextType.FormTransaction:
                    const formRepo = isAccount ? dataSource.getRepository(FormTenancyEntity) : dataSource.getRepository(FormEntity);
                    const form = await formRepo.findOneBy({ id: builder.contextId });
                    builder.contextName = form?.name;
                    break;
                case AutomationContextType.DataRegister:
                    const drRepo = isAccount
                        ? dataSource.getRepository(DataRegisterTenancyEntity)
                        : dataSource.getRepository(DataRegisterEntity);
                    const entity = await drRepo.findOneBy({ id: builder.contextId });
                    builder.contextName = entity?.name;
                    break;

                default:
                    break;
            }
        }

        return builder;
    }

    public async getUsers({ automations, accountId }: { automations: AutomationDto[]; accountId?: string }) {
        automations.forEach((item) => {
            const [firstNameCreatedBy, ...secondNameCreatedBy] = item.createdByUser?.split(' ') ?? [];
            const [firstNameUpdatedBy, ...secondNameUpdatedBy] = item.updatedByUser?.split(' ') ?? [];
            const [firstNamePublishedBy, ...secondNamePublishedBy] = item.publishedByUser?.split(' ') ?? [];

            item.created = {
                id: item.createdBy,
                firstName: firstNameCreatedBy ?? '',
                secondName: secondNameCreatedBy?.join(' ') ?? '',
            };

            item.updated = { id: item.updatedBy, firstName: firstNameUpdatedBy ?? '', secondName: secondNameUpdatedBy?.join(' ') ?? '' };

            item.published = {
                id: item.publishedBy,
                firstName: firstNamePublishedBy ?? '',
                secondName: secondNamePublishedBy?.join(' ') ?? '',
            };
        });
    }

    public async getContextInfo({
        automations,
        formContextIds,
        registerContextIds,
        accountId,
    }: {
        automations: AutomationDto[];
        formContextIds: string[];
        registerContextIds: string[];
        accountId?: string;
    }) {
        const dataSource = await this.getDataSource(accountId);
        const formRepo = accountId ? dataSource.getRepository(FormTenancyEntity) : dataSource.getRepository(FormEntity);
        const registerRepo = accountId ? dataSource.getRepository(DataRegisterTenancyEntity) : dataSource.getRepository(DataRegisterEntity);

        if (formContextIds?.length) {
            const forms = await formRepo.findBy({ id: In(formContextIds) });
            automations.forEach((item) => {
                const form = forms.find((f) => f.id === item.contextId);
                if (form) {
                    item.contextName = form.name;
                }
            });
        }

        if (registerContextIds?.length) {
            const registers = await registerRepo.findBy({ id: In(registerContextIds) });
            automations.forEach((item) => {
                const register = registers.find((f) => f.id === item.contextId);
                if (register) {
                    item.contextName = register.name;
                }
            });
        }

        return automations;
    }

    public async create({
        request,
        userByName,
        userId,
        accountId,
    }: {
        userId: string;
        userByName: string;
        request: CreateAutomationCommand;
        accountId?: string;
    }): Promise<string> {
        const dataSource = await this.getDataSource(accountId);
        const isAccount = !!accountId;

        return dataSource.transaction(async (manager) => {
            const { automationRepo, automationVersionRepo } = this._contentService.getRepositories({
                dataSource: manager,
                isAccount,
            });

            const { contextVersionId: newContextVersionId } = await this.checkContextVersion({
                contextType: request.contextType,
                contextId: request.contextId,
                dataSource,
                isAccount,
                userByName,
                userId,
            });

            const contextVersionId = newContextVersionId ? newContextVersionId : request.contextVersionId;
            const versionId = v7();
            const startVersion = isAccount ? 1 : 0;

            const automation = automationRepo.create({
                name: request.name,
                icon: request.icon,
                contextType: request.contextType,
                contextId: request.contextId,
                subscriptionId: request.subscriptionId,
                description: request.description,
                status: AutomationStatus.Draft,
                latestVersion: startVersion,
                latestVersionId: versionId,
            });
            await automationRepo.save(automation);

            const automationVersion = automationVersionRepo.create({
                automationId: automation.id,
                status: AutomationVersionStatus.Draft,
                contextVersionId,
                version: startVersion,
                id: versionId,
            });

            await automationVersionRepo.save(automationVersion);

            return automation.id;
        });
    }

    public async update({
        accountId,
        id,
        userId,
        userByName,
        request,
    }: {
        id: string;
        userId: string;
        userByName: string;
        request: UpdateAutomationRequest;
        accountId?: string;
    }) {
        const dataSource = await this.getDataSource(accountId);
        const isAccount = !!accountId;

        return dataSource.transaction(async (manager) => {
            const {
                automationRepo,
                automationVersionRepo,
                automationRuleRepo,
                automationActionRepo,
                stageACLRepo,
                stageRepo,
                stageRoleAclRepo,
                stageRoleRepo,
            } = this._contentService.getRepositories({
                dataSource: manager,
                isAccount,
            });
            const automation = await automationRepo.findOneBy({ id });
            if (!automation) throw new BadRequestException('automation_not_existed');

            const automationVersion = await automationVersionRepo.findOneBy({ id: automation.latestVersionId });
            if (!automationVersion) throw new BadRequestException('automation_version_not_existed');

            // TODO: reduce nested transaction
            const versionPort = await this._dataSourceService.resolveService<VersioningAutomationVersionPort>(
                accountId,
                VersioningAutomationVersionPort,
                undefined,
                {
                    user: RequestContextService.currentUser(),
                },
            );

            switch (request.step) {
                case UpdateAutomationStep.Information:
                    await this.updateInformation({ request, automationRepo, automation });
                    break;
                case UpdateAutomationStep.Configuration: {
                    const {
                        automationVersion: updatedVersion,
                        isNewVersion,
                        automationActionIdMap,
                        automationRuleIdMap,
                    } = await versionPort.versioning({
                        id: automation.id,
                    });

                    const mappedRequest = this._automationConfigurationService.mapRelationToRequest({
                        isNewVersion,
                        request,
                        versionId: updatedVersion.id,
                        automationActionIdMap,
                        automationRuleIdMap,
                    });

                    await this._automationConfigurationService.updateConfiguration({
                        request: mappedRequest,
                        automationRepo,
                        automationVersionRepo,
                        automationRuleRepo,
                        automationActionRepo,
                        stageACLRepo,
                        stageRepo,
                        stageRoleAclRepo,
                        stageRoleRepo,
                    });
                    break;
                }
            }
            await automationRepo.update({ id }, { updatedAt: new Date(), updatedBy: userId, updatedByUser: userByName });
            return true;
        });
    }

    private async updateInformation({
        request,
        automationRepo,
        automation,
    }: {
        request: UpdateAutomationRequest;
        automationRepo: Repository<AutomationEntity>;
        automation: AutomationEntity;
    }) {
        const stepData = request.stepData as UpdateAutomationInformationRequest;

        automation.name = stepData.name;
        automation.description = stepData.description;

        await automationRepo.save({
            id: automation.id,
            name: stepData.name,
            description: stepData.description,
        });
    }

    private async checkContextVersion({
        contextType,
        contextId,
        dataSource,
        isAccount,
        userByName,
        userId,
        accountId,
    }: {
        contextType: AutomationContextType;
        contextId: string;
        dataSource: DataSource | EntityManager;
        isAccount: boolean;
        userByName: string;
        userId: string;
        accountId?: string;
    }): Promise<{ contextVersionId: string }> {
        let contextVersionId = '';

        switch (contextType) {
            case AutomationContextType.FormTransaction: {
                const { isNewVersion, formVersion } = await this._formVersionDataService.verifyNewFormVersion({
                    connection: dataSource,
                    formId: contextId,
                    isAccount,
                    userByName,
                    userId,
                });

                if (isNewVersion) {
                    contextVersionId = formVersion.id;
                }

                break;
            }
            case AutomationContextType.DataRegister:
                const { isNewVersion, registerVersion } = await this._dataRegisterDataService.verifyRegisterVersion({
                    connection: dataSource,
                    registerId: contextId,
                    isAccount,
                    userByName,
                    userId,
                });

                if (isNewVersion) {
                    contextVersionId = registerVersion.id;
                }
                break;
        }

        return {
            contextVersionId: contextVersionId,
        };
    }

    private async getDataSource(accountId?: string): Promise<DataSource> {
        return await this._dataSourceService.createAccountDataSource(accountId, {
            user: RequestContextService.currentUser(),
        });
    }

    public async delete({ accountId, id }: { accountId: string; id: string }) {
        const dataSource = await this.getDataSource(accountId);
        const isAccount = !!accountId;
        const repository = isAccount ? dataSource.getRepository(AutomationTenancyEntity) : dataSource.getRepository(AutomationEntity);

        const result = await repository.softDelete({ id });

        return !!result.affected;
    }
}
