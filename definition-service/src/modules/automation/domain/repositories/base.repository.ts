import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { AutomationActionEntity } from '../../../../database/src/entities/public/automation-action.public.entity';
import { AutomationChangeLogEntity } from '../../../../database/src/entities/public/automation-change-log.public.entity';
import { AutomationRuleEntity } from '../../../../database/src/entities/public/automation-rule.public.entity';
import { AutomationVersionCommentEntity } from '../../../../database/src/entities/public/automation-version-comment.public.entity';
import { AutomationVersionEntity } from '../../../../database/src/entities/public/automation-version.public.entity';
import { AutomationEntity } from '../../../../database/src/entities/public/automation.public.entity';
import { AutomationActionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { AutomationChangeLogTenancyEntity } from '../../../../database/src/entities/tenancy/automation-change-log.tenancy.entity';
import { AutomationRuleTenancyEntity } from '../../../../database/src/entities/tenancy/automation-rule.tenancy.entity';
import { AutomationVersionCommentTenancyEntity } from '../../../../database/src/entities/tenancy/automation-version-comment.tenancy.entity';
import { AutomationVersionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { AutomationTenancyEntity } from '../../../../database/src/entities/tenancy/automation.tenancy.entity';
import { UserTenancyEntity } from '../../../../database/src/entities/tenancy/user.tenancy.entity';
import { UserEntity } from '../../../../database/src/entities/public/user.public.entity';
import { ApiTenancyEntity } from '../../../../database/src/entities/tenancy/api.tenancy.entity';

@Injectable()
export class BaseRepository {
    protected getRepositories({ accountId, dataSource }: { accountId: string; dataSource: DataSource | EntityManager }): {
        automationRepository: Repository<AutomationEntity>;
        automationVersionRepository: Repository<AutomationVersionEntity>;
        automationRuleRepository: Repository<AutomationRuleEntity>;
        automationActionRepository: Repository<AutomationActionEntity>;
        changeLogRepo: Repository<AutomationChangeLogEntity>;
        automationComment: Repository<AutomationVersionCommentEntity>;
        userRepo: Repository<UserEntity>;
    } {
        return {
            automationRepository: accountId
                ? dataSource.getRepository(AutomationTenancyEntity)
                : dataSource.getRepository(AutomationEntity),

            automationVersionRepository: accountId
                ? dataSource.getRepository(AutomationVersionTenancyEntity)
                : dataSource.getRepository(AutomationVersionEntity),

            automationRuleRepository: accountId
                ? dataSource.getRepository(AutomationRuleTenancyEntity)
                : dataSource.getRepository(AutomationRuleEntity),

            automationActionRepository: accountId
                ? dataSource.getRepository(AutomationActionTenancyEntity)
                : dataSource.getRepository(AutomationActionEntity),

            automationComment: accountId
                ? dataSource.getRepository(AutomationVersionCommentTenancyEntity)
                : dataSource.getRepository(AutomationVersionCommentEntity),
            changeLogRepo: accountId
                ? dataSource.getRepository(AutomationChangeLogTenancyEntity)
                : dataSource.getRepository(AutomationChangeLogEntity),

            userRepo: accountId
                ? (dataSource.getRepository(UserTenancyEntity) as Repository<UserEntity>)
                : dataSource.getRepository(UserEntity),
        };
    }

    protected getDataSource({ accountId, publicDs, tenantDs }: { accountId: string; publicDs: DataSource; tenantDs: DataSource }) {
        const dataSource = accountId ? tenantDs : publicDs;
        const repositories = this.getRepositories({ accountId, dataSource });
        return { dataSource, ...repositories };
    }
}
