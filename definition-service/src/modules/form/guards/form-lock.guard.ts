import { Injectable, CanActivate, ExecutionContext, ConflictException, Inject, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { FormLockService } from '../services/form-lock.service';
import { FormDataService } from '@/modules/form/services/data/form.data.service';
import { FormVersionEntity } from '@/database/src/entities/public/form-version.public.entity';
import { FormVersionTenancyEntity } from '@/database/src/entities/tenancy/form-version.tenancy.entity';
import { PROVIDER_KEYS } from '@/database/src/constants/providers';

@Injectable()
export class FormLockGuard implements CanActivate {
    constructor(
        private readonly _formLockService: FormLockService,
        private readonly _formDataService: FormDataService,
        @Inject(PROVIDER_KEYS.FORM_VERSION_REPOSITORY)
        private readonly _formVersionRepository: Repository<FormVersionEntity>,
        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly _formVersionTenancyRepository: Repository<FormVersionTenancyEntity>,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        let formId = request.body.formId || request.body.contextId || request.params.formId || null;
        const userId = request.user?.id;
        const accountId = request.user?.accountId;

        if (!formId) {
            const formVersionId =
                request.body.formVersionId || request.params.formVersionId || request.params.versionId || request.params.id || null;
            if (!formVersionId) {
                return true;
            }
            const formVersionRepo = accountId ? this._formVersionTenancyRepository : this._formVersionRepository;
            const form = await this._formDataService.getFormVersion({
                versionId: formVersionId,
                formVersionRepo: formVersionRepo as Repository<FormVersionEntity | FormVersionTenancyEntity>,
            });
            formId = form ? form.formId : null;
        }

        if (!userId || !formId) {
            return true; // Let other guards handle authentication
        }

        const status = await this._formLockService.getFormLockStatus(formId, accountId);

        if (status.isLocked && status.holderId !== userId) {
            throw new ConflictException(`This form is currently locked by ${status.holderName}`);
        }

        return true;
    }
}
