import { EventDrivenModule } from '@/common/src';
import { DataSourceService } from '@/database/src/services/connection-util.service';
import { forwardRef, Module, Provider } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { CacheFormActiveVersionService } from '../../common/src/modules/shared/services/cache/form-active-version.cache.service';
import { FormCollectionCacheService } from '../../common/src/modules/shared/services/cache/form-collection.cache.service';
import { PROVIDER_KEYS } from '../../database/src/constants/providers';
import { CaptureActiveFormVersionProvider } from '../../database/src/shared/providers/capture-active-form-version.provider';
import { SharedModule } from '../../shared/shared.module';
import { ApiModule } from '../api';
import { AutomationModule } from '../automation/automation.module';
import { sharedAutomationPortProviders } from '../automation/shared/providers';
import { GeneralAutoPopulateSettingModule } from '../general-auto-populate-setting/general-auto-populate-setting.module';
import { FormAccessControlTenancyController } from './controllers/form-access-control-tenancy.controller';
import { FormAccessControlController } from './controllers/form-access-control.controller';
import { FormCollectionTenancyController } from './controllers/form-collection-tenancy.controller';
import { FormCollectionController } from './controllers/form-collection.controller';
import { FormLayoutTenancyController } from './controllers/form-layout-tenancy.controller';
import { FormLayoutController } from './controllers/form-layout.controller';
import { FormRoleACLController } from './controllers/form-role-acl.controller';
import { FormSearchController } from './controllers/form-search';
import { FormVersionTenancyController } from './controllers/form-version-tenancy.controller';
import { FormVersionController } from './controllers/form-version.controller';
import { FormViewTenancyController } from './controllers/form-view-tenancy.controller';
import { FormViewController } from './controllers/form-view.controller';
import { FormController } from './controllers/form.controller';
import { TenancyFormController } from './controllers/tenancy-form.controller';
import { FormLockController } from './controllers/form-lock.controller';
import { formProviders } from './form-provider';
import { AdditionalFieldProfile } from './mapping-profiles/additional.profile';
import { FormAccessControlProfile } from './mapping-profiles/form-access-control.profile';
import { FormLayoutProfile } from './mapping-profiles/form-layout.profile';
import { FormViewProfile } from './mapping-profiles/form-view.profile';
import { FormProfile } from './mapping-profiles/form.profile';
import { TenancyFormProfile } from './mapping-profiles/tenancy-form.profile';
import { CollectionChangeLogService } from './services/data/change-log/collection-changelog.service';
import { CollectionItemChangeLogService } from './services/data/change-log/collection-item-changelog.service';
import { FieldChangeLogService } from './services/data/change-log/field-changelog.service';
import { LayoutChangeLogService } from './services/data/change-log/layout-changelog.service';
import { LayoutZoneChangeLogService } from './services/data/change-log/layout-zone-changelog.service';
import { LayoutZoneFieldChangeLogService } from './services/data/change-log/layout-zone-field-changelog.service';
import { StageACLChangeLogService } from './services/data/change-log/stage-acl-changelog.service';
import { StageChangeLogService } from './services/data/change-log/stage-changelog.service';
import { StageDecisionChangeLogService } from './services/data/change-log/stage-decision-changelog.service';
import { StageRoleACLChangeLogService } from './services/data/change-log/stage-role-acl-changelog.service';
import { StageTransitionChangeLogService } from './services/data/change-log/stage-transition-changelog.service';
import { ViewChangeLogService } from './services/data/change-log/view-changelog.service';
import { FormAccessControlDataService } from './services/data/form-access-control.data.service';
import { FormCollectionContextDataService } from './services/data/form-collection-context.data.service';
import { FormCollectionItemAutoPopulateSettingService } from './services/data/form-collection-item-ap.data.service';
import { FormCollectionLayoutDataService } from './services/data/form-collection-layout.data.service';
import { FormCollectionDataService } from './services/data/form-collection.data.service';
import { FormContentService } from './services/data/form-content.service';
import { FormDecisionActionDataService } from './services/data/form-decision-action.data.service';
import { FormFieldDataService } from './services/data/form-field.data.service';
import { FormLayoutDataService } from './services/data/form-layout.data.service';
import { FormRelationDataService } from './services/data/form-relation.data.service';
import { FormRoleACLDataService } from './services/data/form-role-acl.data.service';
import { FormStageAclDataService } from './services/data/form-stage-acl.data.service';
import { FormStageDataService } from './services/data/form-stage.data.service';
import { FormVersionChangeLogDataService } from './services/data/form-version-change-log.data.service';
import { FormVersionDataFactoryService } from './services/data/form-version-factory.data.service';
import { FormVersionReleaseDataService } from './services/data/form-version-release.data.service';
import { FormVersionCommentDataService } from './services/data/form-version.comment.data.service';
import { FormVersionDataService } from './services/data/form-version.data.service';
import { FormVersionLoggerService } from './services/data/form-version.logger.service';
import { FormViewDataService } from './services/data/form-view.data.service';
import { FormDataService } from './services/data/form.data.service';
import { FormDuplicateService } from './services/duplicate/form.duplicate.service';
import { FormAccessControlTenancyService } from './services/form-access-control-tenancy.service';
import { FormAccessControlService } from './services/form-access-control.service';
import { FormCollectionLayoutTenancyService } from './services/form-collection-layout-tenancy.service';
import { FormCollectionItemTenancyService } from './services/form-collection-tenancy.service';
import { FormCollectionService } from './services/form-collection.service';
import { FormLayoutTenancyService } from './services/form-layout-tenancy.service';
import { FormLayoutService } from './services/form-layout.service';
import { FormTemplateService } from './services/form-template.service';
import { FormVersionTenancyService } from './services/form-version-tenancy.service';
import { FormVersionService } from './services/form-version.service';
import { FormViewTenancyService } from './services/form-view-tenancy.service';
import { FormViewService } from './services/form-view.service';
import { FormCollectionListener } from './services/listeners/form-collection.listener';
import { FormCollectionListenerService } from './services/listeners/form-collection.listener.service';
import { FormFieldListener } from './services/listeners/form-field.listener';
import { FormRelatedListener } from './services/listeners/form-related.listener';
import { FormVersionCaptureListener } from './services/listeners/form-version-capture.listener';
import { FormVersionMapping } from './services/mapping/form-version.mapping';
import { FormPublishService } from './services/publish/form.publish.service';
import { RoleACLService } from './services/role-acl.tenancy.service';
import { SeedDataService } from './services/seed-data.service';
import { TenancyFormTemplateService } from './services/tenancy-form-template.service';
import { FormLockService } from './services/form-lock.service';

const changeLogProviders = [
    FieldChangeLogService,
    CollectionChangeLogService,
    FormVersionChangeLogDataService,
    CollectionItemChangeLogService,
    StageChangeLogService,
    StageDecisionChangeLogService,
    StageTransitionChangeLogService,
    LayoutChangeLogService,
    LayoutZoneChangeLogService,
    LayoutZoneFieldChangeLogService,
    ViewChangeLogService,
    StageACLChangeLogService,
    StageRoleACLChangeLogService,
];

const providers = [
    FormProfile,
    TenancyFormProfile,
    FormTemplateService,
    TenancyFormTemplateService,
    FormVersionService,
    FormDataService,
    FormCollectionService,
    FormCollectionItemTenancyService,
    FormCollectionDataService,
    FormLayoutDataService,
    FormLayoutTenancyService,
    FormLayoutService,
    FormLayoutProfile,
    FormAccessControlService,
    FormAccessControlTenancyService,
    FormAccessControlDataService,
    FormAccessControlProfile,
    FormCollectionListener,
    FormVersionDataService,
    FormVersionDataFactoryService,
    FormViewDataService,
    FormViewProfile,
    FormViewService,
    FormViewTenancyService,
    FormPublishService,
    FormVersionTenancyService,
    FormVersionLoggerService,
    FormCollectionListenerService,
    FormContentService,
    FormVersionMapping,
    FormRoleACLDataService,
    FormFieldListener,
    FormRelatedListener,
    FormVersionReleaseDataService,
    FormRelationDataService,
    FormVersionCommentDataService,
    FormFieldDataService,
    AdditionalFieldProfile,
    FormCollectionItemAutoPopulateSettingService,
    FormDuplicateService,
    FormCollectionContextDataService,
    FormStageDataService,
    FormDecisionActionDataService,
    FormStageAclDataService,
    FormVersionCaptureListener,
    SeedDataService,
    RoleACLService,
    FormCollectionCacheService,
    FormCollectionLayoutDataService,
    FormCollectionLayoutTenancyService,
    ...formProviders,
    ...changeLogProviders,
    CacheFormActiveVersionService,
    FormLockService,
    {
        provide: PROVIDER_KEYS.CAPTURE_ACTIVE_FORM_VERSION_PROVIDER,
        useClass: CaptureActiveFormVersionProvider,
    } as Provider,
] as Provider[];

const controllers = [
    FormCollectionTenancyController,
    FormController,
    FormVersionController,
    TenancyFormController,
    FormCollectionController,
    FormLayoutController,
    FormLayoutTenancyController,
    FormAccessControlController,
    FormAccessControlTenancyController,
    FormViewController,
    FormViewTenancyController,
    FormVersionTenancyController,
    FormSearchController,
    FormRoleACLController,
    FormLockController,
] as const;

@Module({
    imports: [
        SharedModule,
        GeneralAutoPopulateSettingModule,
        CqrsModule,
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
        forwardRef(() => AutomationModule),
        forwardRef(() => ApiModule),
    ],
    controllers: [...controllers],
    providers: [...providers, ...sharedAutomationPortProviders],
    exports: [
        FormVersionDataService,
        FormVersionDataFactoryService,
        FormAccessControlDataService,
        FormCollectionDataService,
        FormLockService,
        FormDataService,
    ],
})
export class FormModule {}
