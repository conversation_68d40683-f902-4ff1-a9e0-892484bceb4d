import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CacheFormActiveVersionService } from '../../../../common/src/modules/shared/services/cache/form-active-version.cache.service';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { ICaptureActiveFormVersionProvider } from '../../../../database/src/shared/providers/capture-active-form-version.provider';
import { FORM_VERSION } from '../../constants/form.event';

@Injectable()
export class FormVersionCaptureListener {
    constructor(
        private readonly _cacheVersion: CacheFormActiveVersionService,

        @Inject(PROVIDER_KEYS.CAPTURE_ACTIVE_FORM_VERSION_PROVIDER)
        private readonly _captureService: ICaptureActiveFormVersionProvider,
    ) {}

    @OnEvent(FORM_VERSION.RELEASED, { async: true })
    async capture({
        formId,
        formVersionId,
        accountId,
        apiVersionId,
    }: {
        formId: string;
        formVersionId: string;
        accountId: string;
        apiVersionId?: string;
    }) {
        if (!accountId || !formId || !formVersionId) return;

        const cacheValue = await this._captureService.capture({
            formVersionId,
            accountId,
        });

        await this._cacheVersion.capture({
            formVersionId,
            accountId,
            formId,
            cacheValue: JSON.stringify(cacheValue),
        });

        if (apiVersionId) {
            const apiVersionCacheValue = await this._captureService.captureApiVersion({
                apiVersionId,
                accountId,
            });

            await this._cacheVersion.captureApiVersion({
                formId,
                accountId,
                cacheValue: JSON.stringify(apiVersionCacheValue),
            });
        }
    }
}
