import { TRANSACTION_FIELD_ID } from '@/common/src/constant/field';
import { DataRegisterFieldTenancyEntity } from '@/database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { RelationTransactionEntity } from '@/database/src/entities/tenancy/relation-transaction.tenancy.entity';
import { TransactionFieldEntity } from '@/database/src/entities/tenancy/transaction-field.tenancy.entity';
import { DataRegisterFieldDto } from '@/modules/data-register/dtos';
import { getSettingsByType } from '@/modules/utils/form';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { DataSource, In, LessThan, Raw, Repository } from 'typeorm';
import { CacheService, ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { AccountToFormEntity } from '../../../database/src/entities/public/account-to-forms.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../../database/src/entities/public/form-field.public.entity';
import { FormRelatedEntity } from '../../../database/src/entities/public/form-related.public.entity';
import { FormVersionEntity } from '../../../database/src/entities/public/form-version.public.entity';
import { FormEntity } from '../../../database/src/entities/public/form.public.entity';
import { StageRoleEntity } from '../../../database/src/entities/public/stage-role.public.entity';
import { StageTransitionEntity } from '../../../database/src/entities/public/stage-transition.public.entity';
import { StageEntity } from '../../../database/src/entities/public/stage.public.entity';
import { SubscriptionEntity } from '../../../database/src/entities/public/subscription.public.entity';
import { AutomationActionTenancyEntity } from '../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { AutomationRuleTenancyEntity } from '../../../database/src/entities/tenancy/automation-rule.tenancy.entity';
import { AutomationVersionTenancyEntity } from '../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { AutomationTenancyEntity } from '../../../database/src/entities/tenancy/automation.tenancy.entity';
import { FavoriteMenuTenancyEntity } from '../../../database/src/entities/tenancy/favorite-menu.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../../database/src/entities/tenancy/form-layout.tenancy.entity';
import { FormManualEventTenancyEntity } from '../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { FormVersionCommentTenancyEntity } from '../../../database/src/entities/tenancy/form-version-comment.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageDecisionTenancyEntity } from '../../../database/src/entities/tenancy/stage-decision.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTransitionTenancyEntity } from '../../../database/src/entities/tenancy/stage-transition.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { UserFormVersionTenancyEntity } from '../../../database/src/entities/tenancy/user-form-version.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { AutoPopulateBuilderTypeEnum } from '../../../database/src/shared/enums/ap-builder-type.enum';
import { FavoriteTypeEnum } from '../../../database/src/shared/enums/favorite-menu.enum';
import { FormStatus } from '../../../database/src/shared/enums/form-status.enum';
import { FormVersionStatus } from '../../../database/src/shared/enums/form-version-status.enum';
import { UserScope } from '../../../database/src/shared/enums/user-scope.enum';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { EnableAutomationVersionCommand } from '../../automation/commands/enable-automation-version/enable-automation-version.command';
import { EnableAutomationVersionRequestDto } from '../../automation/commands/enable-automation-version/enable-automation-version.request.dto';
import { GeneralAutoPopulateSettingTenancyService } from '../../general-auto-populate-setting/services/general-auto-populate-setting-tenancy.service';
import { FormFieldDto, StageDto } from '../dtos';
import { FormCollectionDto } from '../dtos/form-collection.dto';
import { FormVersionDto } from '../dtos/form-version.dto';
import { FormDto } from '../dtos/form.dto';
import { AddRelatedFormDto, CreateFormDto, UpdateDefaultRoleRequest } from '../dtos/requests';
import { GetFormsQuery } from '../dtos/requests/get-forms.query';
import { PublishFormRequest } from '../dtos/requests/publish-form.request';
import { UpdateFormDto } from '../dtos/requests/update-form.request';
import { UpdateWidgetFieldsRequest } from '../types/fom-data.request.type';
import { LookUpOptionResponse } from '../types/fom-data.response.type';
import { FormFieldDataService } from './data/form-field.data.service';
import { FormVersionReleaseDataService } from './data/form-version-release.data.service';
import { FormDataService } from './data/form.data.service';
import { FormDuplicateService } from './duplicate/form.duplicate.service';
import { EnableAutomationAllVersionCommand } from '@/modules/automation/commands/enable-automation-version/enable-automation-all-version.command';

@Injectable()
export class TenancyFormTemplateService {
    static PREFIX = 'TFT';

    constructor(
        @Inject(PROVIDER_KEYS.FORM_TENANCY_REPOSITORY)
        private readonly _formRepository: Repository<FormTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly _formVersionRepository: Repository<FormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly _stageRepository: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private readonly _formFieldRepository: Repository<FormFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TRANSITION_TENANCY_REPOSITORY)
        private readonly _stageTransitionRepository: Repository<StageTransitionTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_DECISION_TENANCY_REPOSITORY)
        private readonly _stageDecisionRepository: Repository<StageDecisionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemRepo: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_LAYOUT_TENANCY_REPOSITORY)
        private readonly _formLayoutRepo: Repository<FormLayoutTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY)
        private readonly _stageAccessControlRepo: Repository<StageAccessControlTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_ACCESS_CONTROL_TENANCY_REPOSITORY)
        private readonly _stageRoleAccessControlRepo: Repository<StageRoleAccessControlTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_TENANCY_REPOSITORY)
        private readonly _stageRoleRepo: Repository<StageRoleTenancyEntity>,

        @Inject(PROVIDER_KEYS.USER_TENANCY_REPOSITORY)
        private readonly _userRepository: Repository<UserTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATED_TENANCY_REPOSITORY)
        private readonly _formRelatedRepository: Repository<FormRelatedTenancyEntity>,

        @Inject(PROVIDER_KEYS.SUBSCRIPTION_REPOSITORY)
        private readonly _subscriptionRepository: Repository<SubscriptionEntity>,

        @Inject(PROVIDER_KEYS.USER_FORM_VERSION_TENANCY_REPOSITORY)
        private readonly _userFormVersionRepository: Repository<UserFormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_COMMENT_TENANCY_REPOSITORY)
        private readonly _commentRepository: Repository<FormVersionCommentTenancyEntity>,

        @Inject(PROVIDER_KEYS.ACCOUNT_TO_FORM)
        private readonly _accountToFormRepository: Repository<AccountToFormEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _formTransactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private readonly _formRelationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _generalAPRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(PROVIDER_KEYS.AUTOMATION_TENANCY_REPOSITORY)
        private readonly _automationRepository: Repository<AutomationTenancyEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_VERSION_TENANCY_REPOSITORY)
        private readonly _automationVersionRepository: Repository<AutomationVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_ACTION_TENANCY_REPOSITORY)
        private readonly _automationActionRepository: Repository<AutomationActionTenancyEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_RULE_TENANCY_REPOSITORY)
        private readonly _automationRuleRepository: Repository<AutomationRuleTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_MANUAL_EVENT_TENANCY_REPOSITORY)
        private readonly _formManualEventRepo: Repository<FormManualEventTenancyEntity>,

        @Inject(PROVIDER_KEYS.FAVORITE_MENU_REPOSITORY)
        private readonly _favoriteMenuRepo: Repository<FavoriteMenuTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly _drFieldRepo: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly _formTransactionRepo: Repository<TransactionEntity>,

        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,

        private readonly _logger: LoggerService,

        @InjectMapper() readonly _mapper: Mapper,

        private readonly _dataService: FormDataService,
        private readonly _releaseService: FormVersionReleaseDataService,
        private readonly _formFieldDataService: FormFieldDataService,
        private readonly _generalAutoPopulateSettingService: GeneralAutoPopulateSettingTenancyService,
        private readonly _formDuplicateService: FormDuplicateService,
        private readonly _commandBus: CommandBus,
        private readonly _cacheService: CacheService,
    ) {}

    public async getList(query: GetFormsQuery): Promise<PaginationResponseDto<FormDto>> {
        try {
            const { data, total } = await this._dataService.getList({
                query,
                repository: this._formRepository as Repository<FormTenancyEntity | FormEntity>,
            });
            const result = this._mapper.mapArray(data, FormTenancyEntity, FormDto);

            await this._dataService.getRelatedFormUsers({
                forms: result,
            });

            const actionVersionIds: string[] = result.map((f) => f.activeVersionId).filter(Boolean);
            if (actionVersionIds?.length) {
                const comments = await this._commentRepository.findBy({ formVersionId: In(actionVersionIds) });
                result.forEach((form) => {
                    form.comment = comments.find((c) => c.formVersionId === form.activeVersionId)?.comment ?? '';
                });
            }

            return {
                data: result,
                total: total,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getByTransactionId(transactionId, isTest = false): Promise<FormDto> {
        try {
            let transactionEntity = await this._formTransactionRepo.findOne({
                where: { id: transactionId, isTest },
                withDeleted: isTest,
            });

            if (!transactionEntity) throw new NotFoundException('transaction_not_found');

            const formDto = await this.get({
                id: transactionEntity.formId,
                formVersionId: transactionEntity.formVersionId,
                fromTransaction: true,
                includeRoleAccessControls: true,
                includeStageAccessControls: false,
                includeAutomation: true,
                includeFormCollections: true,
                includeStages: true,
                includeLayouts: true,
                includeRelatedForms: true,
                includeFields: true,
                includeAutoPopulateSettings: false,
                isTest,
            });

            formDto.activeStageId = transactionEntity.stageId;
            return formDto;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async get(params: {
        id: string;
        formVersionId?: string;
        includeActiveVersion?: boolean;
        includeAutomation?: boolean;
        fromTransaction?: boolean;
        includeSubscription?: boolean;
        includeComment?: boolean;
        includeLayouts?: boolean;
        includeStages?: boolean;
        includeRoleAccessControls?: boolean;
        includeStageAccessControls?: boolean;
        includeFormCollections?: boolean;
        includeRelatedForms?: boolean;
        includeStageRoleAccessControls?: boolean;
        includeFields?: boolean;
        includeAutoPopulateSettings?: boolean;
        isTest?: boolean;
    }): Promise<FormDto> {
        const {
            id,
            formVersionId,
            fromTransaction = false,
            includeActiveVersion = false,
            includeAutomation = false,
            includeComment = false,
            includeSubscription = false,
            includeLayouts = true,
            includeStages = true,
            includeRoleAccessControls = false,
            includeStageRoleAccessControls = false,
            includeStageAccessControls = false,
            includeFormCollections = true,
            includeRelatedForms = true,
            includeFields = true,
            includeAutoPopulateSettings = true,
            isTest = false,
        } = params;
        const key = `${TenancyFormTemplateService.PREFIX}:${id}:${formVersionId}:iav_${includeActiveVersion}:ia_${includeAutomation}:is_${includeSubscription}:ic_${includeComment}:il_${includeLayouts}:ist_${includeStages}:isac_${includeStageAccessControls}:irac_${includeRoleAccessControls}:ifc_${includeFormCollections}:irf_${includeRelatedForms}:israc${includeStageRoleAccessControls}`;

        try {
            if (!isTest && fromTransaction && formVersionId) {
                const cached = await this._cacheService.get<FormDto>(key);
                if (cached) {
                    return cached;
                }
            }
        } catch (err) {
            this._logger.error(err);
        }

        try {
            const {
                form,
                formVersion,
                latestVersion,
                activeVersion,
                relatedForms,
                activeRelatedForms,
                latestRelatedForms,
                subscriptions,
                formAutomation,
            } = await this._dataService.getById({
                id: id,
                formRepo: this._formRepository as Repository<FormEntity | FormTenancyEntity>,
                formVersionRepo: this._formVersionRepository as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                collectionRepo: this._formCollectionRepo as Repository<FormCollectionEntity | FormCollectionTenancyEntity>,
                collectionItemRepo: this._formCollectionItemRepo as Repository<FormCollectionItemTenancyEntity | FormCollectionItemEntity>,
                formFieldRepo: this._formFieldRepository,
                formLayoutRepo: this._formLayoutRepo,
                stageAccessControlRepo: this._stageAccessControlRepo,
                stageRepo: this._stageRepository,
                stageTransitionRepo: this._stageTransitionRepository as Repository<StageTransitionEntity | StageTransitionTenancyEntity>,
                formRelatedRepo: this._formRelatedRepository,
                subscriptionRepo: this._subscriptionRepository,
                stageRoleRepo: this._stageRoleRepo as Repository<StageRoleEntity | StageRoleTenancyEntity>,
                isAccount: !!this._claims.accountId,
                formVersionId,
                includeActiveVersion,
                includeComment,
                includeSubscription,
                includeLayouts,
                includeRoleAccessControls,
                includeStageAccessControls,
                includeFormCollections,
                includeRelatedForms,
                includeStages,
                includeFields,
                includeStageRoleAccessControls,
                commentRepo: this._commentRepository,
                generalAPRepo: this._generalAPRepo,
                manualEventRepo: this._formManualEventRepo,
                includeAutomation: includeAutomation,
                automationActionRepo: this._automationActionRepository,
                automationVersionRepo: this._automationVersionRepository,
            });

            if (!form) throw new NotFoundException('Form not found');

            const formDto = this._mapper.map(form, FormTenancyEntity, FormDto);
            formDto.subscriptionNames = subscriptions?.map((s) => s.name);

            const mapGeneralAutoPopulateSettings = async (
                versionId: string,
                fields: FormFieldDto[],
                formCollections: FormCollectionDto[],
            ) => {
                const autoPopulateSettingEntities = await this._generalAutoPopulateSettingService.getSettings({
                    builderId: id,
                    // builderType: AutoPopulateBuilderTypeEnum.FormField,
                    builderVersionId: versionId,
                });

                fields.forEach((field) => {
                    const populateSettings = autoPopulateSettingEntities.filter(
                        (item) => item.fieldId === field.fieldId && item.builderType === AutoPopulateBuilderTypeEnum.FormField,
                    );
                    if (!populateSettings) return;

                    if (!field.configuration?.autoPopulate) field.configuration.autoPopulate = {};
                    field.configuration.autoPopulate.general = populateSettings;
                });

                (formCollections ?? []).forEach((collection) => {
                    (collection.formCollectionItems ?? []).forEach((collectionItem) => {
                        const populateSettings = autoPopulateSettingEntities.filter(
                            (item) => item.builderType === AutoPopulateBuilderTypeEnum.FormCollectionItem && collectionItem.identityId,
                        );

                        if (!populateSettings?.length) return;
                        if (!collectionItem.setting?.autoPopulate) collectionItem.setting.autoPopulate = {};

                        collectionItem.setting.autoPopulate.general = populateSettings;
                    });
                });
            };

            if (formVersionId) {
                const result = this._dataService._mapFormProperties({
                    formDto,
                    formVersion,
                    relatedForms,
                    versionType: 'target',
                    isAccount: !!this._claims.accountId,
                    formAutomation: formAutomation,
                });

                const userFormVersionEntity = await this._userFormVersionRepository.findOneBy({
                    formVersionId: result.formVersion.id,
                    userId: this._claims.userId,
                });

                if (userFormVersionEntity) {
                    result.formVersion.defaultRoleId = userFormVersionEntity.roleId;
                }

                includeAutoPopulateSettings &&
                    result.formVersion &&
                    (await mapGeneralAutoPopulateSettings(
                        result.formVersion.id,
                        result.formVersion.fields,
                        result.formVersion.formCollections,
                    ));

                if (fromTransaction && !isTest) {
                    await this._cacheService.set(key, result, 86400);
                }

                return result;
            }

            let formWithVersions = this._dataService._mapFormProperties({
                formDto,
                formVersion: latestVersion,
                relatedForms: latestRelatedForms,
                versionType: 'latest',
                isAccount: !!this._claims.accountId,
                formAutomation: formAutomation,
            });

            includeAutoPopulateSettings &&
                formWithVersions.latestFormVersion &&
                (await mapGeneralAutoPopulateSettings(
                    formWithVersions.latestFormVersion.id,
                    formWithVersions.latestFormVersion.fields,
                    formWithVersions.latestFormVersion.formCollections,
                ));

            if (includeActiveVersion) {
                formWithVersions = this._dataService._mapFormProperties({
                    formDto,
                    formVersion: activeVersion,
                    relatedForms: activeRelatedForms,
                    versionType: 'active',
                    isAccount: !!this._claims.accountId,
                    formAutomation: formAutomation,
                });
                includeAutoPopulateSettings &&
                    formWithVersions.activeFormVersion &&
                    (await mapGeneralAutoPopulateSettings(
                        formWithVersions.activeFormVersion.id,
                        formWithVersions.activeFormVersion.fields,
                        formWithVersions.latestFormVersion.formCollections,
                    ));
            }

            if (formWithVersions && formWithVersions.status === FormStatus.Draft) {
                this._dataService.getNewCollectionConfiguration({
                    formVersionId: formWithVersions.latestVersionId,
                    collectionRepository: this._formCollectionRepo as Repository<FormCollectionTenancyEntity | FormCollectionEntity>,
                });
            }

            return formWithVersions;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getRollUpContext(formId: string): Promise<LookUpOptionResponse> {
        try {
            const data = await this._dataService.getRollUpContext({
                formId,
                formRepo: this._formRepository as Repository<FormEntity | FormTenancyEntity>,
                collectionRepo: this._formCollectionRepo as Repository<FormCollectionTenancyEntity | FormCollectionEntity>,
                formRelatedRepo: this._formRelatedRepository as Repository<FormRelatedEntity | FormRelatedTenancyEntity>,
                isTenancy: true,
            });

            return data;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async getFieldById(id: string, useActiveVersion?: boolean): Promise<Array<FormFieldDto>> {
        try {
            const formFields = await this._dataService.getFieldById({
                id,
                formRepo: this._formRepository as Repository<FormTenancyEntity | FormEntity>,
                formFieldRepo: this._formFieldRepository,
                useActiveVersion,
            });
            return this._mapper.mapArray(formFields, FormFieldEntity, FormFieldDto);
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getCollectionFieldByFormVersionId(formVersionId: string): Promise<Array<DataRegisterFieldDto>> {
        try {
            const formFields = await this._dataService.getCollectionFieldById({
                formVersionId,
                formCollectionRepo: this._formCollectionRepo as Repository<FormCollectionEntity | FormCollectionTenancyEntity>,
                drFieldRepo: this._drFieldRepo,
            });
            return this._mapper.mapArray(formFields, DataRegisterFieldTenancyEntity, DataRegisterFieldDto);
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getRelatedForms(formId: string, deep?: boolean, includeFields?: boolean, includeStages?: boolean): Promise<FormDto[]> {
        try {
            const { formEntities, relatedForms } = await (deep
                ? this._dataService.getDeepRelatedForms({
                      formId,
                      formRelatedRepo: this._formRelatedRepository,
                      userRepo: this._userRepository,
                      isTenancy: true,
                  })
                : this._dataService.getRelatedForms({
                      formId,
                      formRelatedRepo: this._formRelatedRepository,
                  }));

            const formDtos = this._mapper.mapArray(formEntities, FormTenancyEntity, FormDto);
            formDtos.forEach((form) => {
                const [firstNameCreatedBy, ...secondNameCreatedBy] = form.createdByUser?.split(' ') ?? [];
                const [firstNamePublishedBy, ...secondNamePublishedBy] = form.publishedByUser?.split(' ') ?? [];

                form.created = {
                    id: form.createdBy,
                    firstName: firstNameCreatedBy ?? '',
                    secondName: secondNameCreatedBy?.join(' ') ?? '',
                };

                form.published = {
                    id: form.publishedBy,
                    firstName: firstNamePublishedBy ?? '',
                    secondName: secondNamePublishedBy?.join(' ') ?? '',
                };

                form.relatedFormConfigs = relatedForms.find(
                    (item) => item.firstFormId === form.id || item.secondFormId === form.id,
                )?.configs;
            });

            if (includeFields) {
                const activeVersions = await this._dataService.getFormVersionsByIds({
                    formVersionsRepo: this._formVersionRepository as Repository<FormVersionEntity | FormVersionTenancyEntity>,
                    formVersionIds: formDtos.map((dto) => dto.latestVersionId), //TODO: update to active version after possible to publish
                });

                const formFields = await this._dataService.getRelatedFormFields({
                    formVersionIds: activeVersions.map((version) => version.id),
                    formFieldsRepo: this._formFieldRepository,
                });
                activeVersions.forEach((version) => {
                    version.fields = formFields.filter((f) => f.formVersionId === version.id);
                });
                const formVersionDto = this._mapper.mapArray(activeVersions, FormVersionTenancyEntity, FormVersionDto);
                formDtos.forEach((form) => (form.activeFormVersion = formVersionDto.find((ver) => ver.formId === form.id)));
            }

            if (includeStages) {
                const formVersionIds = formDtos.map((item) => item.activeVersionId);

                const stages = await this._dataService.getStageByFormVersionIds(formVersionIds, this._stageRepository);
                const stageDtos = this._mapper.mapArray(stages, StageTenancyEntity, StageDto);

                formDtos.forEach((form) => {
                    if (!form.activeFormVersion) {
                        form.activeFormVersion = { version: '', formId: '', layout: {}, status: FormVersionStatus.Draft };
                    }
                    const formStages = stageDtos.filter((stage) => stage.formVersionId === form.activeVersionId);

                    form.activeFormVersion = { ...form.activeFormVersion, stages: formStages };
                });
            }

            return formDtos;
        } catch (err) {
            console.error(err);
            this._logger.error(err);
            throw err;
        }
    }

    public async getActiveFields(formId: string): Promise<{
        name: string;
        data: FormFieldDto[];
    }> {
        try {
            const form = await this._formRepository.findOneBy({ id: formId });
            if (!form || !form?.activeVersionId) {
                return null;
            }

            const fields = await this._formFieldDataService.getFormFieldsByVersion({
                formFieldRepo: this._formFieldRepository,
                versionId: form.activeVersionId,
            });

            const data = this._mapper.mapArray(fields, FormFieldTenancyEntity, FormFieldDto);

            return {
                name: form.name,
                data,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async searchForms(query: string, pageSize: number, cursor?: Date, count?: boolean): Promise<any> {
        try {
            const queryObject = {
                select: {
                    id: true,
                    fieldValue: true,
                    fieldId: true,
                    transactionId: true,
                    dependFieldId: true,
                    createdAt: true,
                    updatedBy: true,
                    transaction: {
                        formId: true,
                        id: true,
                        stageId: true,
                    },
                },
                where: {
                    fieldValue: Raw((alias) => `to_tsvector('english', ${alias}) @@ to_tsquery('english', :query)`, {
                        query: query.replaceAll(' ', ':*|'),
                    }),
                    createdAt: cursor ? LessThan(cursor) : undefined,
                },
                take: pageSize,
                order: {
                    createdAt: 'DESC' as any,
                },
                relations: ['transaction'],
            };

            const records = await this._formTransactionFieldRepository.find(queryObject);
            if (records.length == 0) {
                return {
                    data: [],
                    nextCursor: null,
                    total: 0,
                };
            }
            // add transaction id name
            const transactions = await this._formTransactionFieldRepository.find({
                select: {
                    fieldId: true,
                    fieldValue: true,
                    transactionId: true,
                },
                where: {
                    transactionId: In(records.map((record) => record.transactionId)),
                    fieldId: TRANSACTION_FIELD_ID,
                },
            });
            const transactionsNameMap = transactions.reduce((acc, transaction) => {
                acc[transaction.transactionId] = transaction.fieldValue;
                return acc;
            }, {});

            // add transaction id name to the result
            records.forEach((record) => {
                record['transactionIdName'] = transactionsNameMap[record.transactionId];
            });
            // get stage name
            const stageIds = records.map((record) => record.transaction.stageId);
            const stages = await this._stageRepository.find({
                select: {
                    id: true,
                    name: true,
                    formVersionId: true,
                },
                where: { id: In(stageIds) },
            });
            const stagesNameMap = stages.reduce((acc, stage) => {
                acc[stage.id] = { name: stage.name, formVersionId: stage.formVersionId };
                return acc;
            }, {});

            // add stage name to the result
            records.forEach((record) => {
                record['stageName'] = stagesNameMap[record.transaction.stageId]['name'];
                record['formVersionId'] = stagesNameMap[record.transaction.stageId]['formVersionId'];
            });

            // add form name to the result
            const forms = await this._formRepository.find({
                select: {
                    id: true,
                    name: true,
                },
                where: { id: In(records.map((f) => f.transaction.formId)) },
            });
            const formsNameMap = forms.reduce((acc, form) => {
                acc[form.id] = form.name;
                return acc;
            }, {});

            // add field name to the result
            const fields = await this._formFieldRepository.find({
                select: {
                    fieldId: true,
                    label: true,
                    formVersionId: true,
                },
                where: {
                    fieldId: In(records.map((f) => f.fieldId).concat(records.map((f) => f.dependFieldId))),
                },
            });
            const fieldsNameMap = fields.reduce((acc, field) => {
                acc[`${field.fieldId}_${field.formVersionId}`] = field.label;
                return acc;
            }, {});

            // add updatedBy to the result
            const userIds = records.map((record) => record.updatedBy);
            const users = await this._userRepository.find({
                select: {
                    id: true,
                    firstName: true,
                    secondName: true,
                },
                where: { id: In(userIds) },
            });
            const usersNameMap = users.reduce((acc, user) => {
                acc[user.id] = `${user.firstName} ${user.secondName}`;
                return acc;
            }, {});
            // records.forEach((record) => {
            //     record.updatedBy = usersNameMap[record.updatedBy];
            // });
            // if child trans, add the original form

            const conditions = records.map((item) => `('${item.transaction.formId}', '${item.transaction.id}')`).join(',');
            const originalForms = await this._formRelationTransactionRepository
                .createQueryBuilder('frt')
                .select(['frt.originFormId', 'frt.originTransactionId', 'frt.targetFormId', 'frt.targetTransactionId'])
                .where(`(frt.target_form_id, frt.target_transaction_id) IN (${conditions})`)
                .getMany();
            const originalFormMap = originalForms.reduce((acc, item) => {
                acc[`${item.targetFormId}_${item.targetTransactionId}`] = item;
                return acc;
            }, {});

            const data = records.map((result) => {
                const originalForm = originalFormMap[`${result.transaction.formId}_${result.transaction.id}`];
                return {
                    fieldValue: result.fieldValue,
                    fieldID: result.id,
                    fieldName:
                        fieldsNameMap[`${result.fieldId}_${result['formVersionId']}`] ||
                        fieldsNameMap[`${result.dependFieldId}_${result['formVersionId']}`],
                    formName: formsNameMap[result.transaction.formId],
                    formID: result.transaction.formId,
                    transactionID: result.transaction.id,
                    stageName: result['stageName'],
                    timestamp: result.createdAt.getTime(),
                    date: result.createdAt,
                    updatedBy: usersNameMap[result.updatedBy],
                    transactionIdName: result['transactionIdName'],
                    originFormId: originalForm?.originFormId,
                    originTransactionId: originalForm?.originTransactionId,
                };
            });
            const nextCursor = records.length === pageSize ? records[records.length - 1].createdAt.getTime() : null;

            let total = undefined;
            if (count) {
                const queryObject = {
                    select: {
                        id: true,
                    },
                    where: {
                        fieldValue: Raw((alias) => `to_tsvector('english', ${alias}) @@ to_tsquery('english', :query)`, {
                            query: query.replaceAll(' ', ':*|'),
                        }),
                    },
                };
                total = await this._formTransactionFieldRepository.count(queryObject);
                console.log('total: ', total);
            }
            return {
                data,
                nextCursor,
                total,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async create(request: CreateFormDto): Promise<FormDto> {
        try {
            const result = await this._dataService.create({
                scope: UserScope.Account,
                request,
                formRepo: this._formRepository as Repository<FormEntity | FormTenancyEntity>,
                formVersionRepo: this._formVersionRepository as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                formFieldRepo: this._formFieldRepository as Repository<FormFieldTenancyEntity | FormFieldEntity>,
                stageRepo: this._stageRepository as Repository<StageEntity | StageTenancyEntity>,
            });

            const dto = this._mapper.map(result, FormTenancyEntity, FormDto);
            return dto;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async addRelatedForms(request: AddRelatedFormDto): Promise<boolean> {
        try {
            const result = await this._dataService.update({
                request: { step: 'relations', id: request.formId },
                relationRequest: { ...request },
                dataSource: this._dataSource,
                formRepo: this._formRepository as Repository<FormEntity | FormTenancyEntity>,
                formVersionRepo: this._formVersionRepository as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                isAccount: !!this._claims.accountId,
                userId: this._claims.userId,
                accountId: this._claims.accountId,
                automationRepo: this._automationRepository,
                automationVersionRepo: this._automationVersionRepository,
                automationActionRepo: this._automationActionRepository,
                automationRuleRepo: this._automationRuleRepository,
            });

            return !!result;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async update(request: UpdateFormDto): Promise<FormDto> {
        try {
            const result = await this._dataService.update({
                request,
                formRepo: this._formRepository as Repository<FormTenancyEntity | FormEntity>,
                formVersionRepo: this._formVersionRepository as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                formFieldRepo: this._formFieldRepository,
                stageRepo: this._stageRepository,
                stageTransitionRepo: this._stageTransitionRepository as Repository<StageTransitionEntity | StageTransitionTenancyEntity>,
                stageDecisionRepo: this._stageDecisionRepository,
                dataSource: this._dataSource,
                isAccount: !!this._claims.accountId,
                accountId: this._claims.accountId,
                userId: this._claims.impersonating?.id ?? this._claims.userId,
                userByName: this._claims.impersonating ? this.fullName(this._claims.impersonating) : this._claims.userFullName,
                automationRepo: this._automationRepository,
                automationVersionRepo: this._automationVersionRepository,
                automationActionRepo: this._automationActionRepository,
                automationRuleRepo: this._automationRuleRepository,
                stageACLRepo: this._stageAccessControlRepo,
                stageRoleAclRepo: this._stageRoleAccessControlRepo,
                stageRoleRepo: this._stageRoleRepo as any,
            });

            switch (request.step) {
                case 'fields': {
                    // Save general auto populate settings
                    const autoPopulateSettings = request.fields
                        ?.map((f) => getSettingsByType(f.configuration.autoPopulate))
                        .filter(Boolean)
                        .flat();

                    if (autoPopulateSettings?.length) {
                        await this._generalAutoPopulateSettingService.saveSettings({
                            settings: autoPopulateSettings,
                            builderInfo: {
                                builderVersionId: result.latestVersionId,
                                builderId: request.id,
                                builderType: AutoPopulateBuilderTypeEnum.FormField,
                            },
                        });
                    }
                    break;
                }

                default:
                    break;
            }

            const dto = this._mapper.map(result, FormTenancyEntity, FormDto);
            return dto;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    fullName(user: { firstName: string; secondName: string }): string {
        if (!user) {
            return '';
        }

        return [user.firstName, user.secondName].filter((x) => x).join(' ');
    }

    public async updateWidgetFields(request: UpdateWidgetFieldsRequest): Promise<void> {
        try {
            await this._dataService.updateWidgetFields({
                request,
                dataSource: this._dataSource,
                isAccount: !!this._claims.accountId,
            });
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async updateDefaultRole({ formVersionId, roleId }: UpdateDefaultRoleRequest): Promise<boolean> {
        if (this._claims.impersonating?.id) {
            return false;
        }

        const userId = this._claims.userId;

        let entity = await this._userFormVersionRepository.findOneBy({
            formVersionId,
            userId,
        });

        if (!entity) {
            entity = new UserFormVersionTenancyEntity();
            entity.userId = userId;
            entity.formVersionId = formVersionId;
        }

        entity.roleId = roleId;

        const result = await this._userFormVersionRepository.save(entity);
        return !!result;
    }

    public async release(formId: string, request: PublishFormRequest): Promise<boolean> {
        try {
            const result = await this._releaseService.release({
                connection: this._dataSource,
                formId,
                isAccount: !!this._claims.accountId,
                userId: this._claims.impersonating?.id ?? this._claims.userId,
                userByName: this._claims.impersonating ? this.fullName(this._claims.impersonating) : this._claims.userFullName,
                request,
                subscriptionRepo: this._subscriptionRepository,
                accountId: this._claims.accountId,
            });

            return result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const result = await this._dataService.delete({
                id,
                formRepo: this._formRepository as Repository<FormTenancyEntity | FormEntity>,
                dataSource: this._dataSource,
                isAccount: !!this._claims.accountId,
            });

            if (result) {
                await this._accountToFormRepository.softDelete({
                    accountId: this._claims.accountId,
                    formId: id,
                });

                await this._favoriteMenuRepo.softDelete({
                    type: FavoriteTypeEnum.FORM_TRANSACTION,
                    router: `forms/${id}`,
                });
            }

            return !!result;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async resetForm(id: string): Promise<boolean> {
        const queryRunner = this._dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const result = await this._formDuplicateService.duplicateForm(id, queryRunner.manager, true, true);
            await queryRunner.commitTransaction();
            return !!result;
        } catch (error) {
            queryRunner.rollbackTransaction();
            this._logger.error(error);
            throw error;
        } finally {
            queryRunner.release();
        }
    }

    public async duplicate(formId: string): Promise<string> {
        const queryRunner = this._dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const result = await this._formDuplicateService.duplicateForm(formId, queryRunner.manager, true);
            await queryRunner.commitTransaction();
            return result;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            this._logger.error(error);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    public async enableAutomation(dto: EnableAutomationVersionRequestDto & { formVersionId: string }): Promise<boolean> {
        const command = new EnableAutomationVersionCommand({
            automationVersionId: dto.automationVersionId,
            isEnable: dto.isEnable,
        });

        const result = await this._commandBus.execute(command);
        return result;
    }

    public async bulkEnableAutomation(dto: EnableAutomationVersionRequestDto & { formVersionId: string }): Promise<boolean> {
        const command = new EnableAutomationAllVersionCommand({
            automationVersionId: dto.automationVersionId,
            isEnable: dto.isEnable,
        });

        const result = await this._commandBus.execute(command);
        return result;
    }

    public async changeActiveVersion(formId: string, version: number): Promise<boolean> {
        const [form, formVersion] = await Promise.all([
            this._formRepository.findOne({
                where: {
                    id: formId,
                },
                select: ['id'],
            }),
            this._formVersionRepository.findOne({
                where: {
                    version,
                    formId,
                    status: FormVersionStatus.Published,
                },
                select: ['id'],
            }),
        ]);
        if (!formVersion || !form) throw new Error('form or form version not found');

        try {
            await this._formRepository
                .createQueryBuilder()
                .update()
                .set({
                    activeVersion: version,
                    activeVersionId: formVersion.id,
                })
                .where({
                    id: formId,
                })
                .execute();
        } catch (error) {
            throw error;
        }

        return true;
    }
}
function GreaterThan(cursor: string) {
    throw new Error('Function not implemented.');
}
