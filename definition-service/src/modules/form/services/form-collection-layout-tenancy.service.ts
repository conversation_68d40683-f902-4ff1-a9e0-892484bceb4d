import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { LoggerService } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { CollectionLayoutTenancyEntity } from '../../../database/src/entities/tenancy/collection-layout.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { CollectionLayoutDto } from '../../data-register/dtos/collection-layout.dto';
import { FormCollectionLayoutDataService } from './data/form-collection-layout.data.service';

@Injectable()
export class FormCollectionLayoutTenancyService {
    constructor(
        @Inject(PROVIDER_KEYS.COLLECTION_LAYOUT_TENANCY_REPOSITORY)
        private readonly _formLayoutRepository: Repository<CollectionLayoutTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _collectionAdditionalFieldRepository: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        private readonly _dataService: FormCollectionLayoutDataService,
        private readonly _logger: LoggerService,
        @InjectMapper()
        private readonly _mapper: Mapper,
    ) {}

    public async getFormCollectionLayouts({
        formVersionId,
        collectionItemIdentityId,
    }: {
        formVersionId: string;
        collectionItemIdentityId: string;
    }): Promise<CollectionLayoutDto[]> {
        try {
            const { layouts, additionalFields } = await this._dataService.getFormCollectionLayouts({
                collectionAdditionalFieldRepository: this._collectionAdditionalFieldRepository,
                collectionItemIdentityId,
                formVersionId,
                layoutRepository: this._formLayoutRepository,
                isTest: true,
            });
            const dto = this._mapper.mapArray(layouts, CollectionLayoutTenancyEntity, CollectionLayoutDto);

            const allFields = dto?.flatMap((d) => d.layoutZones?.flatMap((lz) => lz.layoutFields));
            allFields?.forEach((field) => {
                field.label = additionalFields.find((f) => f.fieldId === field.fieldId)?.label;
            });

            return dto;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }
}
