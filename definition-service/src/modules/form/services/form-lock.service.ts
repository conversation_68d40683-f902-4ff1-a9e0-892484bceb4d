import { Injectable, ConflictException, NotFoundException, Inject } from '@nestjs/common';
import { CacheService, ClaimService, LoggerService } from '@/common/src';
import { USER_CLAIMS } from '@/constant';

/**
 * Custom error class for form lock-related failures.
 * Extends the base Error class to provide additional context about the form being locked.
 */
export class FormLockError extends Error {
    constructor(
        message: string,
        public readonly formId: string,
    ) {
        super(message);
        this.name = 'FormLockError';
    }
}

/**
 * Represents the current lock status of a form.
 * Contains information about whether the form is locked, who holds the lock, and a descriptive message.
 */
interface FormLockStatus {
    /** Indicates whether the form is currently locked by any user */
    isLocked: boolean;
    /** The unique identifier of the user currently holding the lock, null if not locked */
    holderId: string | null;
    /** The display name of the user holding the lock, null if not available or not locked */
    holderName: string | null;
    /** A descriptive message explaining the current lock status for UI display */
    message: string;
}

@Injectable()
export class FormLockService {
    /** Cache key prefix for storing form lock holder information */
    private readonly LOCK_HOLDER_PREFIX = 'form:lock:holder:';

    constructor(
        private readonly _cacheService: CacheService,
        @Inject(USER_CLAIMS)
        private readonly _claimService: ClaimService,
        private readonly _loggerService: LoggerService,
    ) {}

    /**
     * @param formId - The unique identifier of the form to lock
     * @param accountId - The account identifier for multi-tenant isolation
     * @param userId - The unique identifier of the user requesting the lock
     * @returns A FormLockStatus object containing lock information and status message
     * @throws ConflictException - When the form is already locked by a different user
     * @throws FormLockError - When there's a technical failure during lock acquisition
     */
    async acquireFormLock(formId: string, accountId: string, userId: string): Promise<FormLockStatus> {
        const holderKey = `${this.LOCK_HOLDER_PREFIX}${accountId || 'MDS'}:${formId}`;
        try {
            // Check if form is already locked by retrieving current lock holder
            const currentHolderRaw = await this._cacheService.get<string>(holderKey);
            let currentHolder: { userId: string; fullName: string } | null = null;

            // Parse the lock holder data, handling both new JSON format and legacy string format
            if (currentHolderRaw) {
                try {
                    currentHolder = JSON.parse(currentHolderRaw);
                } catch {
                    // Fallback for legacy format where only userId was stored as string
                    currentHolder = { userId: currentHolderRaw, fullName: '' };
                }
            }

            // If form is locked by a different user, throw conflict exception
            if (currentHolder && currentHolder.userId !== userId) {
                throw new ConflictException(`Form is already locked by user ${currentHolder.fullName || currentHolder.userId}`);
            }

            // If requesting user already holds the lock, return current status
            if (currentHolder && currentHolder.userId === userId) {
                return {
                    isLocked: true,
                    holderId: userId,
                    holderName: currentHolder.fullName || null,
                    message: 'You have already locked this form',
                };
            }

            // Get user's full name from claim service for better UX
            const fullName = this._claimService.userFullName || '';

            // Store the lock holder information in cache
            await this._cacheService.set(holderKey, JSON.stringify({ userId, fullName }));

            return {
                isLocked: true,
                holderId: userId,
                holderName: fullName || null,
                message: 'Lock acquired successfully',
            };
        } catch (error) {
            // Re-throw ConflictException as-is since it's a business logic error
            if (error instanceof ConflictException) {
                this._loggerService.error(error);
                throw error;
            }

            // Log and throw custom error for technical failures
            this._loggerService.error(`Form lock acquisition failed for form ${formId}:`, error);
            throw new FormLockError(`Failed to acquire lock for form ${formId}`, formId);
        }
    }

    /**
     * @param formId - The unique identifier of the form to unlock
     * @param accountId - The account identifier for multi-tenant isolation
     * @param userId - The unique identifier of the user attempting to release the lock
     * @returns A FormLockStatus object indicating the result of the unlock operation
     * @throws FormLockError - When there's a technical failure during lock release
     */
    async releaseFormLock(formId: string, accountId: string, userId: string): Promise<FormLockStatus> {
        const holderKey = `${this.LOCK_HOLDER_PREFIX}${accountId || 'MDS'}:${formId}`;
        try {
            // Retrieve current lock holder information
            const currentHolderRaw = await this._cacheService.get<string>(holderKey);
            let currentHolder: { userId: string; fullName: string } | null = null;

            // Parse the lock holder data, handling both formats
            if (currentHolderRaw) {
                try {
                    currentHolder = JSON.parse(currentHolderRaw);
                } catch {
                    // Fallback for legacy format
                    currentHolder = { userId: currentHolderRaw, fullName: '' };
                }
            }

            // If form is not locked, return unlocked status
            if (!currentHolder) {
                return {
                    isLocked: false,
                    holderId: null,
                    holderName: null,
                    message: 'Form is not locked',
                };
            }

            // If requesting user is not the lock holder, return current lock status
            if (currentHolder.userId !== userId) {
                return {
                    isLocked: true,
                    holderId: currentHolder.userId,
                    holderName: currentHolder.fullName || null,
                    message: 'You are not the lock holder for this form',
                };
            }

            // Remove the lock holder information from cache
            await this._cacheService.delete(holderKey);

            return {
                isLocked: false,
                holderId: null,
                holderName: null,
                message: 'Lock released successfully',
            };
        } catch (error) {
            // Re-throw business logic exceptions as-is
            if (error instanceof NotFoundException || error instanceof ConflictException) {
                this._loggerService.error(error);
                throw error;
            }

            // Log and throw custom error for technical failures
            this._loggerService.error(`Error releasing form lock for form ${formId}:`, error);
            throw new FormLockError(`Failed to release lock for form ${formId}`, formId);
        }
    }

    /**
     * @param formId - The unique identifier of the form to check
     * @param accountId - The account identifier for multi-tenant isolation
     * @returns A FormLockStatus object containing:
     *   - isLocked: Boolean indicating if the form is currently locked
     *   - holderId: The userId of the current lock holder (null if not locked)
     *   - holderName: The full name of the current lock holder (null if not available)
     *   - message: A descriptive message about the current lock status
     */
    async getFormLockStatus(formId: string, accountId: string): Promise<FormLockStatus> {
        const holderKey = `${this.LOCK_HOLDER_PREFIX}${accountId || 'MDS'}:${formId}`;
        const holderRaw = await this._cacheService.get<string>(holderKey);
        let holder: { userId: string; fullName: string } | null = null;

        // Parse the lock holder data, handling both formats
        if (holderRaw) {
            try {
                holder = JSON.parse(holderRaw);
            } catch {
                // Fallback for legacy format
                holder = { userId: null, fullName: null };
            }
        }

        return {
            isLocked: !!holder,
            holderId: holder ? holder.userId : null,
            holderName: holder ? holder.fullName || null : null,
            message: holder ? `Form is locked by user ${holder.fullName || holder.userId}` : 'Form is not locked',
        };
    }
}
