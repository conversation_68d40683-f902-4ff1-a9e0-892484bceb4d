import { FormVersionStatus } from '@/database/src/shared/enums/form-version-status.enum';
import { getSettingsByType } from '@/modules/utils/form';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Brackets, DataSource, In, Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { AccountDataRegisterEntity } from '../../../database/src/entities/public/account-data-registers.public.entity';
import { AccountSubscriptionEntity } from '../../../database/src/entities/public/account-subscription.public.entity';
import { AccountToFormEntity } from '../../../database/src/entities/public/account-to-forms.public.entity';
import { AutomationActionEntity } from '../../../database/src/entities/public/automation-action.public.entity';
import { AutomationRuleEntity } from '../../../database/src/entities/public/automation-rule.public.entity';
import { AutomationVersionEntity } from '../../../database/src/entities/public/automation-version.public.entity';
import { AutomationEntity } from '../../../database/src/entities/public/automation.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { FormAutoPopulateSettingEntity } from '../../../database/src/entities/public/form-auto-populate-setting.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../../database/src/entities/public/form-field.public.entity';
import { FormLayoutEntity } from '../../../database/src/entities/public/form-layout.public.entity';
import { FormManualEventEntity } from '../../../database/src/entities/public/form-manual-event.public.entity';
import { FormRelatedEntity } from '../../../database/src/entities/public/form-related.public.entity';
import { FormVersionCommentEntity } from '../../../database/src/entities/public/form-version-comment.public.entity';
import { FormVersionEntity } from '../../../database/src/entities/public/form-version.public.entity';
import { FormEntity } from '../../../database/src/entities/public/form.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { StageAccessControlEntity } from '../../../database/src/entities/public/stage-access-controls.public.entity';
import { StageDecisionEntity } from '../../../database/src/entities/public/stage-decision.public.entity';
import { StageRoleAccessControlEntity } from '../../../database/src/entities/public/stage-role-access-controls.public.entity';
import { StageRoleEntity } from '../../../database/src/entities/public/stage-role.public.entity';
import { StageTransitionEntity } from '../../../database/src/entities/public/stage-transition.public.entity';
import { StageEntity } from '../../../database/src/entities/public/stage.public.entity';
import { SubscriptionEntity } from '../../../database/src/entities/public/subscription.public.entity';
import { UserEntity } from '../../../database/src/entities/public/user.public.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { RoleTenancyEntity } from '../../../database/src/entities/tenancy/role.tenancy.entity';
import { RollupDependencyTenancyEntity } from '../../../database/src/entities/tenancy/roll-up-dependency.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { AutoPopulateBuilderTypeEnum } from '../../../database/src/shared/enums/ap-builder-type.enum';
import { TargetTypeEnum } from '../../../database/src/shared/enums/roll-up-dependency.enum';
import { UserScope } from '../../../database/src/shared/enums/user-scope.enum';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { EnableAutomationVersionCommand } from '../../automation/commands/enable-automation-version/enable-automation-version.command';
import { EnableAutomationVersionRequestDto } from '../../automation/commands/enable-automation-version/enable-automation-version.request.dto';
import { DataRegisterDto } from '../../data-register/dtos';
import { GeneralAutoPopulateSettingService } from '../../general-auto-populate-setting/services/general-auto-populate-setting.service';
import { FormFieldDto, StageDto } from '../dtos';
import { FormVersionDto } from '../dtos/form-version.dto';
import { FormDto } from '../dtos/form.dto';
import { AddRelatedFormDto, CreateFormDto } from '../dtos/requests';
import { GetFormsQuery } from '../dtos/requests/get-forms.query';
import { PublishFormToAccountRequest } from '../dtos/requests/publish-form-to-account.request';
import { PublishFormRequest } from '../dtos/requests/publish-form.request';
import { UpdateFormDto } from '../dtos/requests/update-form.request';
import { UpdateWidgetFieldsRequest } from '../types/fom-data.request.type';
import { LookUpOptionResponse } from '../types/fom-data.response.type';
import { FormFieldDataService } from './data/form-field.data.service';
import { FormVersionReleaseDataService } from './data/form-version-release.data.service';
import { FormDataService } from './data/form.data.service';
import { FormDuplicateService } from './duplicate/form.duplicate.service';
import { FormPublishService } from './publish/form.publish.service';

@Injectable()
export class FormTemplateService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_REPOSITORY)
        private readonly _formRepository: Repository<FormEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_REPOSITORY)
        private readonly _formVersionRepository: Repository<FormVersionEntity>,

        @Inject(PROVIDER_KEYS.STAGE_REPOSITORY)
        private readonly _stageRepository: Repository<StageEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_REPOSITORY)
        private readonly _formFieldRepository: Repository<FormFieldEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TRANSITION_REPOSITORY)
        private readonly _stageTransitionRepository: Repository<StageTransitionEntity>,

        @Inject(PROVIDER_KEYS.STAGE_DECISION_REPOSITORY)
        private readonly _stageDecisionRepository: Repository<StageDecisionEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_REPOSITORY)
        private readonly _formCollectionRepo: Repository<FormCollectionEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_REPOSITORY)
        private readonly _formCollectionItemRepo: Repository<FormCollectionItemEntity>,

        @Inject(PROVIDER_KEYS.FORM_LAYOUT_REPOSITORY)
        private readonly _formLayoutRepo: Repository<FormLayoutEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ACCESS_CONTROL_REPOSITORY)
        private readonly _stageAccessControlRepo: Repository<StageAccessControlEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_ACCESS_CONTROL_REPOSITORY)
        private readonly _stageRoleAccessControlRepo: Repository<StageRoleAccessControlEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_REPOSITORY)
        private readonly _stageRoleRepo: Repository<StageRoleEntity>,

        @Inject(PROVIDER_KEYS.USER_REPOSITORY)
        private readonly _userRepository: Repository<UserEntity>,

        @Inject(PROVIDER_KEYS.ACCOUNT_TO_FORM)
        private readonly _accountToFormsRepository: Repository<AccountToFormEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATED_REPOSITORY)
        private readonly _formRelatedRepository: Repository<FormRelatedEntity>,

        @Inject(PROVIDER_KEYS.SUBSCRIPTION_REPOSITORY)
        private readonly _subscriptionRepository: Repository<SubscriptionEntity>,

        @Inject(PROVIDER_KEYS.ACCOUNT_SUBSCRIPTION)
        private readonly _accountSubscriptionRepository: Repository<AccountSubscriptionEntity>,

        @Inject(PROVIDER_KEYS.ACCOUNT_DATA_REGISTER)
        private readonly _accountDataRegisterRepository: Repository<AccountDataRegisterEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION)
        private readonly _dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_REPOSITORY)
        private readonly _dataRegisterRepository: Repository<DataRegisterEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_COMMENT_REPOSITORY)
        private readonly _commentRepository: Repository<FormVersionCommentEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTINGS_REPOSITORY)
        private readonly _generalAPRepo: Repository<GeneralAutoPopulateSettingEntity>,

        @Inject(PROVIDER_KEYS.AUTO_POPULATE_SETTING_REPOSITORY)
        private readonly _autoPopulateSettingRepo: Repository<FormAutoPopulateSettingEntity>,

        @Inject(PROVIDER_KEYS.DATA_SOURCE)
        private readonly _dataSource: DataSource,

        private readonly _logger: LoggerService,

        @InjectMapper() readonly _mapper: Mapper,

        private readonly _dataService: FormDataService,
        private readonly _releaseService: FormVersionReleaseDataService,

        @Inject(PROVIDER_KEYS.AUTOMATION_REPOSITORY)
        private readonly _automationRepository: Repository<AutomationEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_VERSION_REPOSITORY)
        private readonly _automationVersionRepository: Repository<AutomationVersionEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_ACTION_REPOSITORY)
        private readonly _automationActionRepository: Repository<AutomationActionEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_RULE_REPOSITORY)
        private readonly _automationRuleRepository: Repository<AutomationRuleEntity>,

        @Inject(PROVIDER_KEYS.FORM_MANUAL_EVENT_REPOSITORY)
        private readonly _formManualEventRepo: Repository<FormManualEventEntity>,

        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,

        private readonly _dataUtilSource: DataSourceService,
        private readonly _publishService: FormPublishService,
        private readonly _formFieldDataService: FormFieldDataService,
        private readonly _generalAutoPopulateSettingService: GeneralAutoPopulateSettingService,
        private readonly _formDuplicateService: FormDuplicateService,

        private readonly _commandBus: CommandBus,
    ) {}

    public async getList(query: GetFormsQuery): Promise<PaginationResponseDto<FormDto>> {
        try {
            const { data, total } = await this._dataService.getList({ query, repository: this._formRepository });
            const result = this._mapper.mapArray(data, FormEntity, FormDto);

            await this._dataService.getRelatedFormUsers({
                forms: result,
            });

            //count num of use
            const formIds = result.map((item) => item.id);
            if (!formIds.length)
                return {
                    data: result,
                    total: total,
                };
            const accountToFormCounts = await this._accountToFormsRepository
                .createQueryBuilder('atf')
                .select('count(atf.account_Id)', 'count')
                .addSelect('atf.form_Id', 'id')
                .where('atf.form_Id IN (:...formIds)', { formIds })
                .groupBy('atf.form_Id')
                .getRawMany<{ count: number; id: string }>();

            if (accountToFormCounts?.length) {
                result.forEach((item) => {
                    const count = accountToFormCounts.find((c) => c.id === item.id)?.count;
                    item.numOfUse = count ?? 0;
                });
            }

            const actionVersionIds: string[] = result.map((f) => f.activeVersionId).filter(Boolean);
            if (actionVersionIds?.length) {
                const comments = await this._commentRepository.findBy({ formVersionId: In(actionVersionIds) });
                result.forEach((form) => {
                    form.comment = comments.find((c) => c.formVersionId === form.activeVersionId)?.comment ?? '';
                });
            }

            return {
                data: result,
                total: total,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async get(params: {
        id: string;
        formVersionId?: string;
        includeActiveVersion?: boolean;
        includeAutomation?: boolean;
        includeSubscription?: boolean;
        includeComment?: boolean;
        includeLayouts?: boolean;
        includeStages?: boolean;
        includeRoleAccessControls?: boolean;
        includeStageAccessControls?: boolean;
        includeFormCollections?: boolean;
        includeRelatedForms?: boolean;
        includeFields?: boolean;
    }): Promise<FormDto> {
        const {
            id,
            formVersionId,
            includeActiveVersion = true,
            includeAutomation = false,
            includeComment = false,
            includeSubscription = false,
            includeLayouts = true,
            includeStages = true,
            includeStageAccessControls = false,
            includeRoleAccessControls = false,
            includeFormCollections = true,
            includeRelatedForms = true,
            includeFields = true,
        } = params;

        try {
            const {
                form,
                formVersion,
                latestVersion,
                activeVersion,
                relatedForms,
                activeRelatedForms,
                latestRelatedForms,
                subscriptions,
                formAutomation,
            } = await this._dataService.getById({
                id: id,
                formRepo: this._formRepository,
                formVersionRepo: this._formVersionRepository,
                collectionRepo: this._formCollectionRepo,
                stageRepo: this._stageRepository,
                stageTransitionRepo: this._stageTransitionRepository,
                formFieldRepo: this._formFieldRepository,
                formLayoutRepo: this._formLayoutRepo,
                stageAccessControlRepo: this._stageAccessControlRepo,
                formRelatedRepo: this._formRelatedRepository,
                subscriptionRepo: this._subscriptionRepository,
                stageRoleRepo: this._stageRoleRepo,
                collectionItemRepo: this._formCollectionItemRepo,
                automationVersionRepo: this._automationVersionRepository,
                isAccount: !!this._claims.accountId,
                formVersionId,
                includeActiveVersion,
                includeRoleAccessControls,
                includeStageAccessControls,
                includeAutomation,
                includeComment,
                includeFormCollections,
                includeLayouts,
                includeRelatedForms,
                includeStages,
                includeSubscription,
                includeFields,
                commentRepo: this._commentRepository,
                generalAPRepo: this._generalAPRepo,
                manualEventRepo: this._formManualEventRepo,
            });

            if (!form) throw new NotFoundException('form_not_found');

            const formDto = this._mapper.map(form, FormEntity, FormDto);
            console.log('subscriptionIds', form.subscriptionIds);

            if (subscriptions?.length) {
                formDto.subscriptionNames = subscriptions.map((s) => s.name);
            }

            const mapGeneralAutoPopulateSettings = async (versionId: string, fields: FormFieldDto[]) => {
                const autoPopulateSettingEntities = await this._generalAutoPopulateSettingService.getSettings({
                    builderId: id,
                    builderType: AutoPopulateBuilderTypeEnum.FormField,
                    builderVersionId: versionId,
                });

                fields.forEach((field) => {
                    const populateSettings = autoPopulateSettingEntities.filter((item) => item.fieldId === field.fieldId);
                    if (!populateSettings) return;

                    if (!field.configuration?.autoPopulate) field.configuration.autoPopulate = {};
                    field.configuration.autoPopulate.general = populateSettings;
                });
            };
            if (formVersionId) {
                const result = this._dataService._mapFormProperties({
                    formDto,
                    formVersion,
                    relatedForms,
                    versionType: 'target',
                    isAccount: !!this._claims.accountId,
                    formAutomation: formAutomation,
                });
                result.formVersion && (await mapGeneralAutoPopulateSettings(result.formVersion.id, result.formVersion.fields));
                return result;
            }

            let formWithVersions = this._dataService._mapFormProperties({
                formDto,
                formVersion: latestVersion,
                relatedForms: latestRelatedForms,
                versionType: 'latest',
                isAccount: !!this._claims.accountId,
                formAutomation: formAutomation,
            });
            formWithVersions.latestFormVersion &&
                (await mapGeneralAutoPopulateSettings(formWithVersions.latestFormVersion.id, formWithVersions.latestFormVersion.fields));

            if (includeActiveVersion && activeVersion) {
                formWithVersions = this._dataService._mapFormProperties({
                    formDto,
                    formVersion: activeVersion,
                    relatedForms: activeRelatedForms,
                    versionType: 'active',
                    isAccount: !!this._claims.accountId,
                    formAutomation: formAutomation,
                });
                formWithVersions?.activeFormVersion &&
                    (await mapGeneralAutoPopulateSettings(
                        formWithVersions.activeFormVersion.id,
                        formWithVersions.activeFormVersion.fields,
                    ));
            }

            if (formVersion && formVersion.status === FormVersionStatus.Draft) {
                this._dataService.getNewCollectionConfiguration({
                    formVersionId: formVersion.id,
                    collectionRepository: this._formCollectionRepo,
                });
            }

            return formWithVersions;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getFieldById(id: string, useActiveVersion?: boolean): Promise<Array<FormFieldDto>> {
        try {
            const formFields = await this._dataService.getFieldById({
                id,
                formRepo: this._formRepository,
                formFieldRepo: this._formFieldRepository,
                useActiveVersion,
            });
            return this._mapper.mapArray(formFields, FormFieldEntity, FormFieldDto);
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getRollUpContext(formId: string): Promise<LookUpOptionResponse> {
        try {
            const data = await this._dataService.getRollUpContext({
                formId,
                formRepo: this._formRepository,
                collectionRepo: this._formCollectionRepo,
                formRelatedRepo: this._formRelatedRepository,
                isTenancy: false,
            });

            return data;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    bindingMeta(formDtos: FormDto[]): FormDto[] {
        formDtos.forEach((form) => {
            const [firstNameCreatedBy, ...secondNameCreatedBy] = form.createdByUser?.split(' ') ?? [];
            const [firstNamePublishedBy, ...secondNamePublishedBy] = form.publishedByUser?.split(' ') ?? [];

            form.created = {
                id: form.createdBy,
                firstName: firstNameCreatedBy ?? '',
                secondName: secondNameCreatedBy?.join(' ') ?? '',
            };

            form.published = {
                id: form.publishedBy,
                firstName: firstNamePublishedBy ?? '',
                secondName: secondNamePublishedBy?.join(' ') ?? '',
            };
        });
        return formDtos;
    }

    bindingRelatedMeta(formDtos: FormDto[], relatedForms): FormDto[] {
        formDtos.forEach((form) => {
            form.relatedFormConfigs = relatedForms.find((item) => item.firstFormId === form.id || item.secondFormId === form.id)?.configs;
        });
        return formDtos;
    }

    public async getRelatedForms(
        formId: string,
        formVersionId: string,
        deep?: boolean,
        includeFields?: boolean,
        includeStages?: boolean,
    ): Promise<FormDto[]> {
        try {
            const { formEntities, relatedForms } = await (deep
                ? this._dataService.getDeepRelatedForms({
                      formId,
                      formRelatedRepo: this._formRelatedRepository,
                      userRepo: this._userRepository,
                      isTenancy: false,
                  })
                : this._dataService.getRelatedForms({
                      formId,
                      formRelatedRepo: this._formRelatedRepository,
                  }));

            let formDtos = this._mapper.mapArray(formEntities, FormEntity, FormDto);
            formDtos = this.bindingMeta(formDtos);
            formDtos = this.bindingRelatedMeta(formDtos, relatedForms);

            if (includeFields) {
                const activeVersions = await this._dataService.getFormVersionsByIds({
                    formVersionsRepo: this._formVersionRepository,
                    formVersionIds: formDtos.map((dto) => dto.latestVersionId), //TODO: update to active version after possible to publish
                });

                const formFields = await this._dataService.getRelatedFormFields({
                    formVersionIds: activeVersions.map((version) => version.id),
                    formFieldsRepo: this._formFieldRepository,
                });

                activeVersions.forEach((version) => {
                    version.fields = formFields.filter((f) => f.formVersionId === version.id);
                });
                const formVersionDto = this._mapper.mapArray(activeVersions, FormVersionEntity, FormVersionDto);
                formDtos.forEach((form) => (form.activeFormVersion = formVersionDto.find((ver) => ver.formId === form.id)));
            }

            if (includeStages) {
                const formVersionIds = formDtos.map((item) => item.activeVersionId);

                const stages = await this._dataService.getStageByFormVersionIds(formVersionIds, this._stageRepository);
                const stageDtos = this._mapper.mapArray(stages, StageEntity, StageDto);

                formDtos.forEach((form) => {
                    if (!form.activeFormVersion) {
                        form.activeFormVersion = { version: '', formId: '', layout: {}, status: FormVersionStatus.Draft };
                    }
                    const formStages = stageDtos.filter((stage) => stage.formVersionId === form.activeVersionId);

                    form.activeFormVersion = { ...form.activeFormVersion, stages: formStages };
                });
            }

            return formDtos;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getActiveFields(formId: string): Promise<{
        name: string;
        data: FormFieldDto[];
    }> {
        try {
            const form = await this._formRepository.findOneBy({ id: formId });
            if (!form || !form?.activeVersionId) {
                return null;
            }

            const fields = await this._formFieldDataService.getFormFieldsByVersion({
                formFieldRepo: this._formFieldRepository,
                versionId: form.activeVersionId,
            });

            const data = this._mapper.mapArray(fields, FormFieldEntity, FormFieldDto);
            return {
                name: form.name,
                data,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async create(request: CreateFormDto): Promise<FormDto> {
        try {
            const result = await this._dataService.create({
                scope: UserScope.MDS,
                request,
                formRepo: this._formRepository,
                formVersionRepo: this._formVersionRepository,
                formFieldRepo: this._formFieldRepository,
                stageRepo: this._stageRepository,
            });

            const dto = this._mapper.map(result, FormEntity, FormDto);
            return dto;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async addRelatedForms(request: AddRelatedFormDto): Promise<boolean> {
        try {
            const result = await this._dataService.update({
                request: { step: 'relations', id: request.formId },
                relationRequest: { ...request },
                dataSource: this._dataSource,
                formRepo: this._formRepository,
                formVersionRepo: this._formVersionRepository,
                isAccount: !!this._claims.accountId,
                userId: this._claims.userId,
                accountId: this._claims.accountId,
                automationRepo: this._automationRepository,
                automationVersionRepo: this._automationVersionRepository,
                automationActionRepo: this._automationActionRepository,
                automationRuleRepo: this._automationRuleRepository,
            });

            return !!result;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async update(request: UpdateFormDto): Promise<FormDto> {
        try {
            const result = await this._dataService.update({
                request,
                formRepo: this._formRepository,
                formVersionRepo: this._formVersionRepository,
                formFieldRepo: this._formFieldRepository,
                stageRepo: this._stageRepository,
                stageTransitionRepo: this._stageTransitionRepository,
                stageDecisionRepo: this._stageDecisionRepository,
                dataSource: this._dataSource,
                isAccount: !!this._claims.accountId,
                accountId: this._claims.accountId,
                userId: this._claims.userId,
                automationRepo: this._automationRepository,
                automationVersionRepo: this._automationVersionRepository,
                automationActionRepo: this._automationActionRepository,
                automationRuleRepo: this._automationRuleRepository,
                stageACLRepo: this._stageAccessControlRepo,
                stageRoleAclRepo: this._stageRoleAccessControlRepo,
                stageRoleRepo: this._stageRoleRepo,
            });

            switch (request.step) {
                case 'fields': {
                    // Save general auto populate settings
                    const autoPopulateSettings = request.fields
                        ?.map((f) => getSettingsByType(f.configuration.autoPopulate))
                        .filter(Boolean)
                        .flat();

                    if (autoPopulateSettings?.length) {
                        await this._generalAutoPopulateSettingService.saveSettings({
                            settings: autoPopulateSettings,
                            builderInfo: {
                                builderVersionId: result.latestVersionId,
                                builderId: request.id,
                                builderType: AutoPopulateBuilderTypeEnum.FormField,
                            },
                        });
                    }
                    break;
                }

                default:
                    break;
            }

            const dto = this._mapper.map(result, FormEntity, FormDto);
            return dto;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async updateWidgetFields(request: UpdateWidgetFieldsRequest): Promise<void> {
        try {
            await this._dataService.updateWidgetFields({
                request,
                dataSource: this._dataSource,
                isAccount: !!this._claims.accountId,
            });
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    fullName(user: { firstName: string; secondName: string }): string {
        if (!user) {
            return '';
        }

        return [user.firstName, user.secondName].filter((x) => x).join(' ');
    }

    public async release(formId: string, request: PublishFormRequest): Promise<boolean> {
        try {
            const result = await this._releaseService.release({
                connection: this._dataSource,
                formId,
                isAccount: !!this._claims.accountId,
                userId: this._claims.impersonating?.id ?? this._claims.userId,
                userByName: this._claims.impersonating ? this.fullName(this._claims.impersonating) : this._claims.userFullName,
                request,
                subscriptionRepo: this._subscriptionRepository,
                accountId: this._claims.accountId,
            });

            return result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const result = await this._dataService.delete({
                id,
                formRepo: this._formRepository,
                dataSource: this._dataSource,
                isAccount: !!this._claims.accountId,
            });

            return !!result;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async resetForm(id: string): Promise<boolean> {
        const queryRunner = this._dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const result = await this._formDuplicateService.duplicateForm(id, queryRunner.manager, !!this._claims.accountId, true);
            await queryRunner.commitTransaction();
            return !!result;
        } catch (error) {
            queryRunner.rollbackTransaction();
            this._logger.error(error);
            throw error;
        } finally {
            queryRunner.release();
        }
    }

    public async duplicate(formId: string): Promise<string> {
        const queryRunner = this._dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const result = await this._formDuplicateService.duplicateForm(formId, queryRunner.manager, !!this._claims.accountId);
            await queryRunner.commitTransaction();
            return result;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            this._logger.error(error);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    public async enableAutomation(dto: EnableAutomationVersionRequestDto & { formVersionId: string }): Promise<boolean> {
        const command = new EnableAutomationVersionCommand({
            automationVersionId: dto.automationVersionId,
            isEnable: dto.isEnable,
        });

        const result = await this._commandBus.execute(command);
        return result;
    }

    public async publishToAccount(formId: string, request: PublishFormToAccountRequest) {
        try {
            const { unassignedDrs, unassignedForms, dataRegisterIds, masterDataIds, forms, formVersionId } =
                await this._dataService.verifyPublishForm({
                    formId,
                    request,
                    formRepo: this._formRepository,
                    accountDataRegisterRepo: this._accountDataRegisterRepository,
                    dataRegisterTransactionRepo: this._dataRegisterTransactionRepository,
                    accountSubsRepo: this._accountSubscriptionRepository,
                    formFieldsRepo: this._formFieldRepository,
                    accountToFormsRepo: this._accountToFormsRepository,
                    formRelatedRepo: this._formRelatedRepository,
                    formCollectionRepo: this._formCollectionRepo,
                    dataRegisterRepo: this._dataRegisterRepository,
                    accountToFormRepo: this._accountToFormsRepository,
                    stageRolesRepo: this._stageRoleRepo,
                    generalAPSettingRepo: this._generalAPRepo,
                });

            //TODO: account forms , widgets, registers are enough for number of forms ready to publish
            //TODO: check widgets
            const canPublish = !unassignedDrs.length;

            if (canPublish && request.forms?.length) {
                const connection = await this._publishService.createAccountConnection(request.accountId);
                await connection.transaction(async (manager) => {
                    const tenancyFormResults = await Promise.all(
                        request.forms.map((form) =>
                            this._publishService.publishToAccount({
                                accountId: request.accountId,
                                dataRegisterIds,
                                formId: form.formId,
                                formVersionId: forms.find((f) => f.formId === form.formId).formVersionId,
                                mappingRoles: form.mappingRoles,
                                masterDataIds: [],
                                connection: manager,
                            }),
                        ),
                    );

                    const relations = tenancyFormResults.flatMap((form) => form.relatedForms) || [];
                    if (relations?.length) {
                        const newRelations = relations.map((relation) => {
                            const { createdAt, ...restRelations } = relation;
                            return { ...restRelations, deletedAt: null, deletedBy: null } as FormRelatedTenancyEntity;
                        });

                        await manager.getRepository(FormRelatedTenancyEntity).save(newRelations);
                    }

                    const formIdMaps = tenancyFormResults.reduce((prev, current) => {
                        return { ...prev, [current.oldId]: current.savedForm.id };
                    }, {});

                    const rollUpDependencies = tenancyFormResults.flatMap((result) => result.originRollupDependencies || []);

                    if (rollUpDependencies.length) {
                        const newRollUpDependencies: RollupDependencyTenancyEntity[] = [];
                        const rollUpDependencyRepo = manager.getRepository(RollupDependencyTenancyEntity);
                        rollUpDependencies.forEach((rollUpDependency) => {
                            const { id, relatedFormId: oldRelatedFormId, ...restDependency } = rollUpDependency;
                            const newEntity = rollUpDependencyRepo.create(restDependency);
                            newRollUpDependencies.push({ ...newEntity, deletedAt: null, deletedBy: null });
                        });

                        await rollUpDependencyRepo.save(newRollUpDependencies);
                    }

                    const originRollupFields = tenancyFormResults.flatMap((result) => result.originRoleUpFields || []);
                    if (originRollupFields.length) {
                        //combine all field id mapping of all forms
                        const fieldIdsMap = tenancyFormResults
                            .map((result) => result.fieldIdMap)
                            .filter(Boolean)
                            .reduce((prev, current) => {
                                return { ...prev, ...current };
                            }, {});

                        //combine all collection id mapping of all forms
                        const collectionIdentityIdsMap = tenancyFormResults
                            .map((result) => result.collectionIdentityIdMap)
                            .filter(Boolean)
                            .reduce((prev, current) => {
                                return { ...prev, ...current };
                            }, {});

                        //combine all rollup  collection id mapping of all forms
                        const rollupCollectionIdsMap = tenancyFormResults
                            .map((result) => result.rollupCollectionIdMap)
                            .filter(Boolean)
                            .reduce((prev, current) => {
                                return { ...prev, ...current };
                            }, {});

                        if (originRollupFields.length) {
                            const newFieldIds = originRollupFields
                                .filter((f) => fieldIdsMap[f.id])
                                .map((rollupField) => fieldIdsMap[rollupField.id]);

                            const newRollupFields = await manager.getRepository(FormFieldTenancyEntity).findBy({
                                id: In(newFieldIds),
                            });

                            newRollupFields.forEach((f) => {
                                if (f?.configuration?.rollup?.rollupDependencies?.length) {
                                    f?.configuration?.rollup.rollupDependencies.forEach((item) => {
                                        item.relatedFormId = formIdMaps?.[item?.relatedFormId] ?? item?.relatedFormId;
                                        if (item?.targetType) {
                                            switch (item.targetType) {
                                                case TargetTypeEnum.FORM:
                                                    item.contextId = f.formVersionId;
                                                    break;

                                                default:
                                                    break;
                                            }
                                        }

                                        if (item.collectionFetchingId) {
                                            const collectionIdentityId = rollupCollectionIdsMap[item.collectionFetchingId];
                                            item.collectionFetchingId = collectionIdentityIdsMap[collectionIdentityId];
                                        }
                                    });
                                    [];
                                }
                            });

                            await manager
                                .getRepository(FormFieldTenancyEntity)
                                .save((newRollupFields || []).map((e) => ({ ...e, deletedAt: null, deletedBy: null })));
                        }
                    }

                    const publishAutoPopulateSettings: FormAutoPopulateSettingEntity[] = [];
                    for (const formResult of tenancyFormResults) {
                        if (!formResult.savedForm?.id || !formResult.newFormVersionId) {
                            continue;
                        }
                        const autoPopulateSettings = await this._autoPopulateSettingRepo
                            .createQueryBuilder('aps')
                            .where(
                                new Brackets((qb) => {
                                    qb.andWhere('aps.originFormId = :formId', { formId: formResult.savedForm.id });
                                    qb.andWhere('aps.originFormVersionId = :formVersionId', { formVersionId: formResult.newFormVersionId });
                                    return qb;
                                }),
                            )
                            .orWhere('aps.targetFormId = :formId', { formId: formResult.savedForm.id })
                            .getMany();

                        if (autoPopulateSettings?.length) {
                            const newPopulates = autoPopulateSettings.map((item) => ({
                                ...item,
                                deletedAt: null,
                                deletedBy: null,
                                deletedByUser: null,
                            }));

                            const noExistedItems = newPopulates.filter(
                                (item) => !publishAutoPopulateSettings.some((p) => item.id === p.id),
                            );
                            publishAutoPopulateSettings.push(...noExistedItems);
                        }
                    }

                    if (publishAutoPopulateSettings.length) {
                        const formAutoPopulateTenancyRepo = manager.getRepository(FormAutoPopulateSettingTenancyEntity);
                        await formAutoPopulateTenancyRepo.save(publishAutoPopulateSettings);
                    }

                    return true;
                });

                //insert into account to form table
                const accountToForms = request.forms.map((form) => ({
                    accountId: request.accountId,
                    formId: form.formId,
                }));
                await this._accountToFormsRepository.save(accountToForms);
            }

            return {
                canPublish,
                unassignedForms: this._mapper.mapArray(unassignedForms, FormEntity, FormDto),
                unassignedDrs: this._mapper.mapArray(unassignedDrs, DataRegisterEntity, DataRegisterDto),
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getPublishContents({ formId, accountId }: { formId: string; accountId: string }) {
        try {
            const result = await this._dataService.getPublishContents({
                formId,
                accountId,
                formRepo: this._formRepository,
                formFieldsRepo: this._formFieldRepository,
                accountSubsRepo: this._accountSubscriptionRepository,
                formCollectionRepo: this._formCollectionRepo,
                dataRegisterTransactionRepo: this._dataRegisterTransactionRepository,
                formRelatedRepo: this._formRelatedRepository,
                dataRegisterRepo: this._dataRegisterRepository,
                accountToFormsRepo: this._accountToFormsRepository,
                accountDataRegisterRepo: this._accountDataRegisterRepository,
                stageRolesRepo: this._stageRoleRepo,
                accountToFormRepo: this._accountToFormsRepository,
                generalAPSettingRepo: this._generalAPRepo,
            });
            return result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getAccountRoles({ accountId, formId }: { accountId: string; formId: string }) {
        if (!accountId || !formId) return [];

        let formRepo, roleRepo;
        try {
            const accountConnection = await this._dataUtilSource.createAccountDataSource(accountId);
            formRepo = accountConnection.getRepository(FormTenancyEntity);
            roleRepo = accountConnection.getRepository(RoleTenancyEntity);
        } catch (err) {
            this._logger.error(err);
            return [];
        }

        try {
            const form = await formRepo.findOne({
                where: { id: formId },
            });

            // TODO: convert to subscription ids
            if (!form?.subscriptionId) return [];

            const roles = await roleRepo
                .createQueryBuilder('rt')
                .innerJoin('rt.roleSubscriptions', 'rtr')
                .where('rtr.subscriptionId = :subscriptionId', { subscriptionId: form.subscriptionId })
                .getMany();

            return roles.map((role) => ({ id: role.id, name: role.name }));
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async changeActiveVersion(formId: string, version: number): Promise<boolean> {
        const [form, formVersion] = await Promise.all([
            this._formRepository.findOne({
                where: {
                    id: formId,
                },
                select: ['id'],
            }),
            this._formVersionRepository.findOne({
                where: {
                    version,
                    formId,
                    status: FormVersionStatus.Published,
                },
                select: ['id'],
            }),
        ]);
        if (!formVersion || !form) throw new Error('form or form version not found');

        try {
            await this._formRepository
                .createQueryBuilder()
                .update()
                .set({
                    activeVersion: version,
                    activeVersionId: formVersion.id,
                })
                .where({
                    id: formId,
                })
                .execute();
        } catch (error) {
            throw error;
        }

        return true;
    }
}
