import { FormRelatedTenancyEntity } from '@/database/src/entities/tenancy/form-related.tenancy.entity';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { compact, uniq } from 'lodash';
import { DataSource, In, Repository } from 'typeorm';
import { ClaimService } from '../../../../common/src';
import { ErrorTypesForm, TYPEORM_SAVE_OPTIONS, USER_CLAIMS } from '../../../../constant';
import { AutomationEntity } from '../../../../database/src/entities/public/automation.public.entity';
import { FormManualEventEntity } from '../../../../database/src/entities/public/form-manual-event.public.entity';
import { FormRelatedEntity } from '../../../../database/src/entities/public/form-related.public.entity';
import { SubscriptionEntity } from '../../../../database/src/entities/public/subscription.public.entity';
import { AutomationTenancyEntity } from '../../../../database/src/entities/tenancy/automation.tenancy.entity';
import { FormManualEventTenancyEntity } from '../../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { FormVersionStatus } from '../../../../database/src/shared/enums/form-version-status.enum';
import { merge2Maps } from '../../../../shared/functions/map.util';
import { ApiConfigurationDataService } from '../../../api/services/data-services/api-configuration-data.service';
import { ReleaseAutomationVersionPort } from '../../../automation/domain/ports/out/release-automation-version.port';
import { FORM_COLLECTION_EVENT, FORM_VERSION } from '../../constants/form.event';
import { PublishFormRequest } from '../../dtos/requests/publish-form.request';
import { GetByIdParams } from '../../types/fom-data.request.type';
import { FormCollectionType, FormManualEventType, FormVersionContentRepositoriesType } from '../../types/form-content.type';
import { FormCollectionContextDataService } from './form-collection-context.data.service';
import { FormCollectionDataService } from './form-collection.data.service';
import { FormContentService } from './form-content.service';
import { FormRelationDataService } from './form-relation.data.service';
import { FormVersionChangeLogDataService } from './form-version-change-log.data.service';
import { FormVersionDataFactoryService } from './form-version-factory.data.service';

@Injectable()
export class FormVersionReleaseDataService {
    constructor(
        private readonly _formContentService: FormContentService,
        private readonly _formVersionDataFactoryService: FormVersionDataFactoryService,
        private readonly _changeLogDataService: FormVersionChangeLogDataService,
        private readonly _relationDataService: FormRelationDataService,
        private readonly _formCollectionDataService: FormCollectionDataService,
        private readonly _collectionContextDataService: FormCollectionContextDataService,
        private readonly _releaseAutomationPort: ReleaseAutomationVersionPort,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _eventEmitter: EventEmitter2,
        private readonly _apiConfigurationDataService: ApiConfigurationDataService,
    ) {}

    public async release({
        connection,
        formId,
        isAccount,
        userId,
        userByName,
        request,
        subscriptionRepo,
        accountId,
    }: {
        connection: DataSource;
        isAccount: boolean;
        formId: string;
        userId: string;
        userByName: string;
        request: PublishFormRequest;
        subscriptionRepo: Repository<SubscriptionEntity>;
        accountId: string;
    }): Promise<boolean> {
        let activeApiVersionId = '';
        const _formVersionId = await connection.transaction<string>(async (manager) => {
            const repositories = this._formContentService.getFormContentRepositories({ isAccount, manager });
            const { formRepo, formVersionRepo, formCommentRepo, changeLogRepo } = repositories;

            const form = await formRepo.findOneBy({ id: formId });
            if (!form) {
                throw new BadRequestException('form_not_exist');
            }

            //validate mapping context
            await this._collectionContextDataService.verify({
                formVersionId: form.latestVersionId,
                accountId,
                dataSource: connection,
            });

            const validateForm = await this.validateFormVersion({
                id: formId,
                formVersionId: form.latestVersionId,
                isAccount,
                ...repositories,
                subscriptionRepo: subscriptionRepo,
                commentRepo: repositories.formCommentRepo,
                generalAPRepo: repositories.generalAutoPopulateRepo,
            });

            if (typeof validateForm === 'string') {
                throw new BadRequestException(validateForm);
            }

            let formVersionId = form.latestVersionId;

            const changeLogs = await this._changeLogDataService.compareVersion({
                formId,
                formVersionId,
                isAccount,
                connection,
            });

            const latestVersionId = form.latestVersionId;
            if (!latestVersionId) {
                throw new BadRequestException('latest_version_is_empty');
            }

            const formVersion = await formVersionRepo.findOneBy({ id: latestVersionId });
            if (!formVersion) {
                throw new BadRequestException('form_version_not_exist');
            }

            const service = this._formVersionDataFactoryService.create({ isAccount });
            const { formVersion: updatedFormVersion } = await service.createNewVersion({
                form,
                formVersion,
                userId,
                userByName,
                status: FormVersionStatus.Published,
                isAccount,
                repositories,
            });

            await formCommentRepo.save({
                comment: request.comment,
                formVersionId: updatedFormVersion.id,
            });

            if (changeLogs?.length) {
                const changeLogEntities = changeLogs.map((changeLog) =>
                    changeLogRepo.create({ ...changeLog, formVersionId: updatedFormVersion.id }),
                );

                await changeLogRepo.save(changeLogEntities, TYPEORM_SAVE_OPTIONS);
            }

            // Capture collection dependencies: collection transaction, additional fields
            await this.captureCollectionDepsData({ formVersionId: updatedFormVersion.id, repositories });

            //capture general auto populate settings
            await this._collectionContextDataService.capture({
                manager,
                formId,
                accountId,
                formVersionId,
            });

            const apiRepo = repositories.apiBuilderRepo;
            const apiVersionRepo = repositories.apiVersionRepo;
            const apiVersionEndpointRepo = repositories.apiVersionEndpointRepo;
            const apiVersionCommentRepo = repositories.apiVersionCommentRepo;

            const api = await apiRepo.findOneBy({
                contextId: formId,
            });

            if (api) {
                const result = await this._apiConfigurationDataService.release({
                    id: api.id,
                    apiRepo,
                    apiVersionRepo,
                    apiVersionEndpointRepo,
                    apiVersionCommentRepo,
                });

                if (result.activeVersionId) {
                    activeApiVersionId = result.activeVersionId;
                }
            }

            formVersionId = updatedFormVersion.id;

            if (updatedFormVersion?.testTransactionId) {
                // remove test transaction id
                await formVersionRepo.update(formVersionId, { testTransactionId: null });
            }

            //Cache collection
            this._eventEmitter.emit(FORM_COLLECTION_EVENT.CACHE_COLLECTION_ADDITIONAL_FIELDS, {
                accountId: this._claims.accountId,
                formVersionId,
            });

            this._eventEmitter.emit(FORM_COLLECTION_EVENT.CACHE_COLLECTION_ITEM_TRANSACTION, {
                accountId: this._claims.accountId,
                formVersionId,
            });

            return formVersionId;
        });

        const automationRepo = accountId ? connection.getRepository(AutomationTenancyEntity) : connection.getRepository(AutomationEntity);
        const automations = await automationRepo.findBy({
            contextId: formId,
        });

        let automationVersionIdsMap = new Map<string, string>();

        for (const automation of automations) {
            const { result, automationVersionIdMap } = await this._releaseAutomationPort.release({
                id: automation.id,
            });

            if (automationVersionIdMap) {
                automationVersionIdsMap = merge2Maps(automationVersionIdsMap, automationVersionIdMap);
            }
        }

        if (automationVersionIdsMap?.size) {
            const formManualEventRepo: Repository<FormManualEventType> = accountId
                ? connection.getRepository(FormManualEventTenancyEntity)
                : connection.getRepository(FormManualEventEntity);

            const manualEvents = await formManualEventRepo.findBy({
                formVersionId: _formVersionId,
            });

            const updatedManualEvents: FormManualEventType[] = [];

            manualEvents.forEach((manualEvent) => {
                const automationVersionId = automationVersionIdsMap.get(manualEvent.automationVersionId);
                if (automationVersionId) {
                    manualEvent.automationVersionId = automationVersionId;
                    updatedManualEvents.push(manualEvent);
                }
            });

            await formManualEventRepo.save(updatedManualEvents);
        }

        //TODO: update form version id to related form
        const relatedFormRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity> = accountId
            ? connection.getRepository(FormRelatedTenancyEntity)
            : connection.getRepository(FormRelatedEntity);
        const relatedForms = await relatedFormRepo.findBy({
            firstFormId: formId,
        });

        for (const form of relatedForms) {
            form.firstFormVersionId = _formVersionId;
        }

        if (relatedForms.length > 0) {
            await relatedFormRepo.save(relatedForms);
        }

        this._eventEmitter.emitAsync(FORM_VERSION.RELEASED, {
            accountId,
            formVersionId: _formVersionId,
            formId,
            apiVersionId: activeApiVersionId,
        });

        return true;
    }

    public async validateFormVersion(params: GetByIdParams): Promise<true | string> {
        const {
            collectionItemRepo,
            collectionRepo,
            commentRepo,
            formFieldRepo,
            formLayoutRepo,
            formRelatedRepo,
            id,
            isAccount,
            stageAccessControlRepo,
            stageRepo,
            stageRoleRepo,
            stageTransitionRepo,
            subscriptionRepo,
            generalAPRepo,
            formVersionId,
            manualEventRepo,
            automationActionRepo,
            automationVersionRepo,
        } = params;
        const { stageRoles } = await this._relationDataService.getFormVersionRelations({
            formId: id,
            versionId: formVersionId,
            formFieldRepo,
            stageRepo,
            stageTransitionRepo,
            stageAccessControlRepo,
            collectionRepo,
            collectionItemRepo,
            formLayoutRepo,
            formRelatedRepo,
            stageRoleRepo,
            subscriptionRepo,
            automationActionRepo,
            automationVersionRepo,
            isAccount,
            commentRepo,
            generalAPRepo,
            manualEventRepo,
            includeRoleAccessControls: true,
            includeStageAccessControls: true,
            includeAutomation: true,
            includeFormCollections: true,
            includeLayouts: true,
            includeRelatedForms: true,
            includeStages: true,
            includeFields: true,
        });

        if (!stageRoles.length) return ErrorTypesForm.MissingStageRole;

        const invalidStageRole = stageRoles.some((stageRole) => !stageRole.layoutId);
        if (invalidStageRole) return ErrorTypesForm.InvalidFormACL;

        return true;
    }

    private async captureCollectionDepsData({
        formVersionId,
        repositories,
    }: {
        formVersionId: string;
        repositories: FormVersionContentRepositoriesType;
    }): Promise<void> {
        const collections = await repositories.collectionRepo.find({
            where: {
                formVersionId: formVersionId,
            },
            relations: ['formCollectionItems'],
        });

        const collectionItems = collections?.flatMap((collection: any) => collection.formCollectionItems);
        const collectionItemIds = collectionItems.map((item) => item.id);

        await this.syncDrVersionToFormCollection({ repositories, collections: collections });
        await Promise.all([
            this._formCollectionDataService.handleCaptureFormCollectionTransaction({
                dataRegisterFieldRepo: repositories.dataRegisterFieldRepo,
                dataRegisterTransactionFieldRepo: repositories.dataRegisterTransactionFieldRepo,
                dataRegisterTransactionRepo: repositories.dataRegisterTransactionRepo,
                formCollectionItemIds: collectionItemIds,
                formCollectionItemRepo: repositories.formCollectionItemRepo,
                formCollectionItemTransactionFieldRepo: repositories.collectionTransactionRepo,
                formCollectionItemEntities: collectionItems,
                collectionLayoutRepo: repositories.collectionLayoutRepo,
                collectionZoneRepo: repositories.collectionLayoutZoneRepo,
                collectionZoneFieldRepo: repositories.collectionLayoutZoneFieldRepo,
                formVersionId,
                dataRegisterRepo: repositories.dataRegisterRepo,
            }),

            this._formCollectionDataService.handleCreateAdditionalField({
                formVersionId,
                additionalFieldRepo: repositories.dataRegisterAdditionalFieldRepo,
                formAdditionalFieldRepo: repositories.formCollectionAdditionalFieldRepo,
                formCollectionItemIds: collectionItemIds,
                formCollectionItemRepo: repositories.formCollectionItemRepo,
                accountId: this._claims.accountId,
            }),
        ]);
    }

    private async syncDrVersionToFormCollection({
        collections,
        repositories,
    }: {
        collections: FormCollectionType[];
        repositories: FormVersionContentRepositoriesType;
    }) {
        const dataRegisterIds = collections.map((collection) => collection.dataRegisterId);

        const dataRegisters = dataRegisterIds?.length
            ? await repositories.dataRegisterRepo.find({
                  where: {
                      id: In(dataRegisterIds),
                  },
                  select: ['id', 'activeVersionId'],
              })
            : [];

        if (!dataRegisters?.length) return;
        const activeVersionIds = uniq(compact(dataRegisters.map((dr) => dr.activeVersionId)));
        if (!activeVersionIds?.length) return;

        const dataRegisterVersions = await repositories.dataRegisterVersionRepo.find({
            where: { id: In(activeVersionIds) },
            select: ['id', 'dataRegisterId', 'config'],
        });

        const collectionFilters: Record<string, any> = {};
        dataRegisterVersions.forEach((version) => {
            collectionFilters[version.dataRegisterId] = version.config?.filters ?? [];
        });

        const newCollections = collections.map((collection) => {
            const dataRegister = dataRegisters.find((drv) => drv.id === collection.dataRegisterId);

            return {
                ...collection,
                dataRegisterVersionId: dataRegister?.activeVersionId ?? collection?.dataRegisterVersionId,
                displaySetting: {
                    ...(collection.displaySetting ?? {}),
                    filters: collectionFilters[collection.dataRegisterId] ?? [],
                },
            } satisfies FormCollectionType;
        });

        await repositories.collectionRepo.save(newCollections);
    }
}
