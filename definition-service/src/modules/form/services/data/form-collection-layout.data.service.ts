import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { CollectionLayoutEntity } from '../../../../database/src/entities/public/collection-layout.public.entity';
import { FormCollectionAdditionalFieldEntity } from '../../../../database/src/entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionItemEntity } from '../../../../database/src/entities/public/form-collection-item.public.entity';
import { CollectionLayoutZoneFieldTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout-zone-field.tenancy.entity';
import { CollectionLayoutZoneTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout-zone.tenancy.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormCollectionDataService } from './form-collection.data.service';

@Injectable()
export class FormCollectionLayoutDataService {
    constructor(
        private readonly _formCollectionDataService: FormCollectionDataService,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_LAYOUT_ZONE_TENANCY_REPOSITORY)
        private readonly _collectionLayoutZoneRepo: Repository<CollectionLayoutZoneTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY)
        private readonly _collectionLayoutZoneFieldRepo: Repository<CollectionLayoutZoneFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterAdditionalFieldRepo: Repository<DataRegisterAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemRepo: Repository<FormCollectionItemTenancyEntity>,
    ) {}

    public async getFormCollectionLayouts({
        formVersionId,
        layoutRepository,
        collectionAdditionalFieldRepository,
        collectionItemIdentityId,
        isTest,
    }: {
        formVersionId: string;
        collectionItemIdentityId: string;
        layoutRepository: Repository<CollectionLayoutEntity>;
        collectionAdditionalFieldRepository: Repository<FormCollectionAdditionalFieldEntity>;
        isTest?: boolean;
    }) {
        const collections = await this._formCollectionRepo.find({
            where: {
                formVersionId,
            },
            relations: {
                formCollectionItems: true,
            },
        });

        const collectionItemEntities = collections
            .flatMap((c) => c.formCollectionItems)
            ?.filter((c) => c.identityId === collectionItemIdentityId);

        if (!collectionItemEntities?.length) {
            return { layouts: [], additionalFields: [] };
        }

        const [layouts, additionalFields] = await Promise.all([
            this._formCollectionDataService.captureCollectionLayoutsOnRelease({
                formVersionId,
                collectionItemEntities,
                collectionLayoutRepo: layoutRepository,
                collectionZoneRepo: this._collectionLayoutZoneRepo,
                collectionZoneFieldRepo: this._collectionLayoutZoneFieldRepo,
                ignoreSave: !!isTest,
            }),
            this._formCollectionDataService.handleCreateAdditionalField({
                formVersionId,
                additionalFieldRepo: this._dataRegisterAdditionalFieldRepo,
                formCollectionItemIds: collectionItemEntities.map((c) => c.id),
                formCollectionItemRepo: this._formCollectionItemRepo as Repository<
                    FormCollectionItemEntity | FormCollectionItemTenancyEntity
                >,
                formAdditionalFieldRepo: collectionAdditionalFieldRepository,
                ignoreSave: !!isTest,
            }),
        ]);

        return { layouts, additionalFields };
    }
}
