import { <PERSON><PERSON> } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, forwardRef, Inject, Injectable, NotFoundException, NotImplementedException } from '@nestjs/common';
import { DataSource, EntityManager, FindOptionsSelect, ILike, In, IsNull, Not, Repository } from 'typeorm';
import { ClaimService, LoggerService, QueryBuilderService, USER_CLAIMS, UtilsService } from '../../../../common/src';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { FormEntity } from '../../../../database/src/entities/public/form.public.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { FormStatus } from '../../../../database/src/shared/enums/form-status.enum';
import { FormVersionStatus } from '../../../../database/src/shared/enums/form-version-status.enum';
import { AddRelatedFormDto, CreateFormDto, CreateFormFieldDto } from '../../dtos/requests';

import { DataRegisterFieldEntity } from '@/database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterFieldTenancyEntity } from '@/database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as _ from 'lodash';
import { v4 } from 'uuid';
import { OrderOptionDto } from '../../../../common/src/modules/shared/dtos/order-option.dto';
import { AccountDataRegisterEntity } from '../../../../database/src/entities/public/account-data-registers.public.entity';
import { AccountSubscriptionEntity } from '../../../../database/src/entities/public/account-subscription.public.entity';
import { AccountToFormEntity } from '../../../../database/src/entities/public/account-to-forms.public.entity';
import { AutomationActionEntity } from '../../../../database/src/entities/public/automation-action.public.entity';
import { AutomationVersionEntity } from '../../../../database/src/entities/public/automation-version.public.entity';
import { DataRegisterTransactionEntity } from '../../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../../database/src/entities/public/data-registers.public.entity';
import { FormAutoPopulateSettingEntity } from '../../../../database/src/entities/public/form-auto-populate-setting.public.entity';
import { FormCollectionEntity } from '../../../../database/src/entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../../../database/src/entities/public/form-field.public.entity';
import { FormLayoutZoneFieldEntity } from '../../../../database/src/entities/public/form-layout-zone-field.public.entity';
import { FormLayoutZoneEntity } from '../../../../database/src/entities/public/form-layout-zone.public.entity';
import { FormLayoutEntity } from '../../../../database/src/entities/public/form-layout.public.entity';
import { FormManualEventEntity } from '../../../../database/src/entities/public/form-manual-event.public.entity';
import { FormRelatedEntity } from '../../../../database/src/entities/public/form-related.public.entity';
import { FormVersionEntity } from '../../../../database/src/entities/public/form-version.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { StageAccessControlEntity } from '../../../../database/src/entities/public/stage-access-controls.public.entity';
import { StageRoleAccessControlEntity } from '../../../../database/src/entities/public/stage-role-access-controls.public.entity';
import { StageRoleEntity } from '../../../../database/src/entities/public/stage-role.public.entity';
import { StageEntity } from '../../../../database/src/entities/public/stage.public.entity';
import { SubscriptionEntity } from '../../../../database/src/entities/public/subscription.public.entity';
import { UserEntity } from '../../../../database/src/entities/public/user.public.entity';
import { ApiVersionEndpointTenancyEntity } from '../../../../database/src/entities/tenancy/api-version-endpoint.tenancy.entity';
import { ApiVersionTenancyEntity } from '../../../../database/src/entities/tenancy/api-version.tenancy.entity';
import { AutomationActionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { FavoriteMenuTenancyEntity } from '../../../../database/src/entities/tenancy/favorite-menu.tenancy.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormLayoutZoneFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-layout-zone-field.tenancy.entity';
import { FormLayoutZoneTenancyEntity } from '../../../../database/src/entities/tenancy/form-layout-zone.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../../../database/src/entities/tenancy/form-layout.tenancy.entity';
import { FormManualEventTenancyEntity } from '../../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { UserTenancyEntity } from '../../../../database/src/entities/tenancy/user.tenancy.entity';
import { AccessControlType, AccessOption } from '../../../../database/src/shared/enums/access-control-type.enum';
import { AutomationContextType } from '../../../../database/src/shared/enums/automation.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { ManualEventSourceType } from '../../../../database/src/shared/enums/form-manual-event.enum';
import { LayoutZoneType } from '../../../../database/src/shared/enums/layout-zone-type.enum';
import { TargetTypeEnum } from '../../../../database/src/shared/enums/roll-up-dependency.enum';
import { UserScope } from '../../../../database/src/shared/enums/user-scope.enum';
import { EditUser } from '../../../../shared/common/dto/edit-user.dto';
import { MultipleFilterRequestDto } from '../../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../../shared/common/dto/pagination-response.dto';
import { UpdateApiConfigurationRequest } from '../../../api/dtos/request';
import { ApiConfigurationDataService } from '../../../api/services/data-services/api-configuration-data.service';
import { DeleteAutomationByContextPort } from '../../../automation/domain/ports/in/delete-automation-by-context.port';
import { AutomationConfigurationService } from '../../../automation/services/automation-configuration.service';
import { DefaultFieldFactory, partitionFormFieldsByFieldValue } from '../../../utils/factory';
import { getGeneralAutoPopulates } from '../../../utils/getGeneralAutoPopulates';
import { FORM_COLLECTION_EVENT } from '../../constants/form.event';
import { IFormFieldEvent } from '../../dtos/events/form-field.event';
import { IFormManualActionEvent } from '../../dtos/events/form-manual-action.event';
import { IFormRelatedEvent } from '../../dtos/events/form-related.event';
import { FormLayoutDto, FormLayoutZoneDto } from '../../dtos/form-layout.dto';
import { FormVersionDto } from '../../dtos/form-version.dto';
import { FormDto } from '../../dtos/form.dto';
import { GetFormsQuery } from '../../dtos/requests/get-forms.query';
import { PublishFormToAccountRequest } from '../../dtos/requests/publish-form-to-account.request';
import { getRelatedFormQuery } from '../../raw-queries';
import {
    DeleteFormParams,
    GetByIdParams,
    GetFormRelationParams,
    UpdateFormFieldsParams,
    UpdateFormInformationParams,
    UpdateFormLayoutsParams,
    UpdateFormParams,
    UpdateFormStagesParams,
    UpdateFormViewsParams,
    UpdateWidgetFieldsParams,
    VerifyFormResultType,
} from '../../types/fom-data.request.type';
import { LookUpOptionResponse } from '../../types/fom-data.response.type';
import { FormVersionMapping } from '../mapping/form-version.mapping';
import { FormRelationDataService } from './form-relation.data.service';
import { FormStageDataService } from './form-stage.data.service';
import { FormVersionDataFactoryService } from './form-version-factory.data.service';
import { FormViewDataService } from './form-view.data.service';

export type FormVersionDataResponse = {
    form: FormEntity | FormTenancyEntity;
    formVersion?: FormVersionEntity | FormVersionTenancyEntity;
    relatedForms?: Array<FormEntity | FormTenancyEntity>;
    latestVersion?: FormVersionEntity | FormVersionTenancyEntity;
    subscriptions?: SubscriptionEntity[];
    activeVersion?: FormVersionEntity | FormVersionTenancyEntity;
    latestRelatedForms?: Array<FormEntity | FormTenancyEntity>;
    activeRelatedForms?: Array<FormEntity | FormTenancyEntity>;
    formAutomation?: {
        automations: (AutomationVersionEntity | AutomationVersionEntity)[];
        actions: (AutomationActionEntity | AutomationActionTenancyEntity)[];
        manualEvents: (FormManualEventEntity | FormManualEventTenancyEntity)[];
    };
};
@Injectable()
export class FormDataService {
    private FORM_VIEW_TABLE = 'form_views';
    private ROLE_VIEW_TABLE = 'roles_to_form_views';

    constructor(
        private readonly _queryBuilder: QueryBuilderService,
        @InjectMapper() readonly _mapper: Mapper,
        @Inject(PROVIDER_KEYS.DATA_SOURCE) private readonly _dataSource: DataSource,
        private readonly _loggerService: LoggerService,
        private readonly _formVersionDataFactoryService: FormVersionDataFactoryService,
        private readonly _formViewDataService: FormViewDataService,
        private readonly _formVersionMapping: FormVersionMapping,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _eventEmitter: EventEmitter2,
        private readonly _relationDataService: FormRelationDataService,
        private readonly _automationConfigurationService: AutomationConfigurationService,
        private readonly _logger: LoggerService,
        private readonly _deleteAutomationByContextPort: DeleteAutomationByContextPort,
        private readonly _formStageDataService: FormStageDataService,
        @Inject(forwardRef(() => ApiConfigurationDataService))
        private readonly _apiConfigurationDataService: ApiConfigurationDataService,
        @Inject(PROVIDER_KEYS.API_VERSION_ENDPOINT_TENANCY_REPOSITORY)
        private readonly _apiVersionEndpointRepo: Repository<ApiVersionEndpointTenancyEntity>,
        @Inject(PROVIDER_KEYS.API_VERSION_TENANCY_REPOSITORY)
        private readonly _apiVersionRepo: Repository<ApiVersionTenancyEntity>,
    ) {}
    //#region GET

    public async getList({
        query,
        repository,
    }: {
        query: GetFormsQuery;
        repository: Repository<FormEntity | FormTenancyEntity>;
    }): Promise<PaginationResponseDto<FormEntity | FormTenancyEntity>> {
        const { order, filters, sort, skip, take } = query;
        const alias = 'fb';

        const defaultValueIds = (filters?.find((v) => v.field === 'defaultValue')?.value as string[]) || [];
        let remainFilters = filters?.filter((v) => v.field !== 'defaultValue');

        const subscriptionFilter = filters?.find((v) => v.field === 'subscriptionId');

        if (subscriptionFilter) {
            remainFilters = remainFilters?.filter((v) => v.field !== 'subscriptionId');
        }

        let defaultForms: FormEntity[] | FormTenancyEntity[] = [];

        if (defaultValueIds?.length) {
            defaultForms = await repository.findBy({ id: In(defaultValueIds) });
        }

        const builder = repository.createQueryBuilder(alias);

        if (remainFilters?.length) {
            this._queryBuilder.applyQueryFilters(builder, alias, remainFilters, []);
        }

        if (subscriptionFilter) {
            const subscriptionId = subscriptionFilter.value as string;
            // Ensure the subscriptionId is treated as a JSON string primitive for the @> operator
            builder.andWhere(`${alias}.subscription_ids @> :subscriptionIdJson`, { subscriptionIdJson: JSON.stringify(subscriptionId) });
        }

        if (query.isPublishedForm) {
            builder.andWhere(`${alias}.activeVersionId is not null`);
        }

        if (query.sort && query.order) {
            const sorters: OrderOptionDto[] = [
                {
                    field: sort,
                    order: order,
                },
            ];
            this._queryBuilder.applySorters(builder, alias, sorters);
        }

        const roleIds = query.roleIds ?? [];
        const requireCheckRoleIds = query.requireCheckRoleIds;
        if (requireCheckRoleIds) {
            if (!roleIds.length) {
                return {
                    data: [],
                    total: 0,
                };
            }
            builder.innerJoin(this.FORM_VIEW_TABLE, 'fv', `fv.form_version_id = fb.active_version_id`);
            builder.innerJoin(this.ROLE_VIEW_TABLE, 'rv', `rv.form_view_id = fv.id`);
            builder.andWhere('rv.role_id IN (:...roleIds)', { roleIds: roleIds });
        }

        const [data, total] = await builder.skip(skip).take(take).getManyAndCount();

        if (defaultForms?.length) {
            const formsNoInPage = defaultForms.filter((f) => !data.some((form) => form.id === f.id)) || [];
            if (formsNoInPage.length) {
                data.unshift(...formsNoInPage);
            }
        }

        return {
            data,
            total,
        };
    }

    public async getVersionList({
        query,
        repository,
    }: {
        query: MultipleFilterRequestDto;
        repository: Repository<FormVersionEntity | FormVersionTenancyEntity>;
    }): Promise<PaginationResponseDto<FormVersionEntity | FormVersionTenancyEntity>> {
        const { order, filters, sort, skip, take } = query;
        const alias = 'fb';
        const builder = repository.createQueryBuilder(alias);

        if (query.filters?.length) {
            this._queryBuilder.applyQueryFilters(builder, alias, filters, []);
        }

        if (query.sort && query.order) {
            const sorters: OrderOptionDto[] = [
                {
                    field: sort,
                    order: order,
                },
            ];
            this._queryBuilder.applySorters(builder, alias, sorters);
        }

        const [data, total] = await builder.skip(skip).take(take).getManyAndCount();

        return {
            data,
            total,
        };
    }

    //     public async getById({
    //         id,
    //         formRepo,
    //         formVersionRepo,
    //         formFieldRepo,
    //         stageRepo,
    //         collectionRepo,
    //         collectionItemRepo,
    //         formLayoutRepo,
    //         stageTransitionRepo,
    //         stageAccessControlRepo,
    //         formRelatedRepo,
    //         stageRoleRepo,
    //         subscriptionRepo,
    //         formVersionId,
    //         commentRepo,
    //         generalAPRepo,
    //         manualEventRepo,
    //         automationVersionRepo,
    //         automationActionRepo,
    //         includeActiveVersion = false,
    //         isAccount,
    //         includeAutomation = false,
    //         includeComment = false,
    //         includeSubscription = false,
    //         includeLayouts = false,
    //         includeRoleAccessControls = false,
    //         includeStageAccessControls = false,
    //         includeFormCollections = false,
    //         includeRelatedForms = false,
    //         includeStages = false,
    //         includeFields = true,
    //         includeStageRoleAccessControls = false,
    //     }: GetByIdParams): Promise<{
    //         form: FormEntity | FormTenancyEntity;
    //         formVersion?: FormVersionEntity | FormVersionTenancyEntity;
    //         relatedForms?: Array<FormEntity | FormTenancyEntity>;
    //         latestVersion?: FormVersionEntity | FormVersionTenancyEntity;
    //         subscriptions?: SubscriptionEntity[];
    //         activeVersion?: FormVersionEntity | FormVersionTenancyEntity;
    //         latestRelatedForms?: Array<FormEntity | FormTenancyEntity>;
    //         activeRelatedForms?: Array<FormEntity | FormTenancyEntity>;
    //         formAutomation?: {
    //             automations: (AutomationVersionEntity | AutomationVersionEntity)[];
    //             actions: (AutomationActionEntity | AutomationActionTenancyEntity)[];
    //             manualEvents: (FormManualEventEntity | FormManualEventTenancyEntity)[];
    //         };
    //     }> {
    //         const form = await formRepo.findOne({
    //             where: { id },
    //             select: {
    //                 activeVersionId: true,
    //                 latestVersionId: true,
    //                 subscriptionIds: true,
    //                 description: true,
    //                 icon: true,
    //                 id: true,
    //                 name: true,
    //                 status: true,
    //                 latestVersion: true,
    //                 activeVersion: true,
    //             },
    //         });

    //         if (!form) {
    //             return null;
    //         }

    //         let subscriptions = null;
    // console.log(`includeSubscription: ${includeSubscription}`)
    //         if (form.subscriptionIds && includeSubscription) {
    //             subscriptions = await subscriptionRepo.find({
    //                 where: [{ id: In(form.subscriptionIds) }],
    //             });
    //         }

    //         if (formVersionId) {
    //             let formVersion = await formVersionRepo.findOne({
    //                 where: [{ id: formVersionId }],
    //                 select: {
    //                     formId: true,
    //                     id: true,
    //                     status: true,
    //                     version: true,
    //                 },
    //             });
    //             const { version, relatedForms, formAutomation } = await this._getFormByVersionId({
    //                 formId: id,
    //                 versionId: formVersionId,
    //                 formVersion: formVersion,
    //                 formFieldRepo,
    //                 stageRepo,
    //                 stageTransitionRepo,
    //                 stageAccessControlRepo,
    //                 collectionRepo,
    //                 collectionItemRepo,
    //                 formLayoutRepo,
    //                 formRelatedRepo,
    //                 stageRoleRepo,
    //                 subscriptionRepo,
    //                 commentRepo,
    //                 isAccount,
    //                 manualEventRepo,
    //                 generalAPRepo,
    //                 automationActionRepo,
    //                 automationVersionRepo,
    //                 includeAutomation,
    //                 includeComment,
    //                 includeSubscription,
    //                 includeLayouts,
    //                 includeRoleAccessControls,
    //                 includeStageAccessControls,
    //                 includeFormCollections,
    //                 includeRelatedForms,
    //                 includeStages,
    //                 includeFields,
    //                 includeStageRoleAccessControls,
    //             });
    //             return {
    //                 form,
    //                 relatedForms,
    //                 subscriptions,
    //                 formVersion: version,
    //                 formAutomation,
    //             };
    //         }

    //         const latestVersionId = form.latestVersionId;

    //         let latestVersion = await formVersionRepo.findOne({
    //             where: [{ id: latestVersionId }],
    //             select: {
    //                 formId: true,
    //                 id: true,
    //                 status: true,
    //                 version: true,
    //             },
    //         });

    //         const {
    //             fields: latestFields,
    //             stages: latestStages,
    //             stageTransitions: latestStageTransitions,
    //             stageAccessControls: latestStageAccessControls,
    //             collections: latestCollections,
    //             layouts: latestLayouts,
    //             relatedForms: latestRelatedForms,
    //             stageRoles: latestStageRoles,
    //             comments,
    //         } = await this._relationDataService.getFormVersionRelations({
    //             formId: id,
    //             versionId: latestVersionId,
    //             formFieldRepo,
    //             stageRepo,
    //             stageTransitionRepo,
    //             stageAccessControlRepo,
    //             collectionRepo,
    //             collectionItemRepo,
    //             formLayoutRepo,
    //             formRelatedRepo,
    //             stageRoleRepo,
    //             subscriptionRepo,
    //             commentRepo,
    //             generalAPRepo,
    //             manualEventRepo,
    //             isAccount,
    //             includeAutomation,
    //             includeRoleAccessControls,
    //             includeStageAccessControls,
    //             includeComment,
    //             includeFormCollections,
    //             includeLayouts,
    //             includeRelatedForms,
    //             includeStages,
    //             includeSubscription,
    //             includeFields,
    //             automationActionRepo,
    //             automationVersionRepo,
    //             includeStageRoleAccessControls,
    //         });

    //         latestVersion = {
    //             ...latestVersion,
    //             stages: latestStages ?? [],
    //             fields: latestFields ?? [],
    //             stageTransitions: latestStageTransitions ?? [],
    //             stageAccessControls: latestStageAccessControls ?? [],
    //             formCollections: latestCollections ?? [],
    //             formLayouts: latestLayouts ?? [],
    //             stageRoles: latestStageRoles ?? [],
    //             comments: comments || [],
    //         };

    //         if (!includeActiveVersion || !form.activeVersionId) {
    //             return {
    //                 form,
    //                 latestVersion,
    //                 subscriptions,
    //                 latestRelatedForms: latestRelatedForms,
    //             };
    //         }

    //         let activeVersion = await formVersionRepo.findOne({
    //             where: [{ id: form.activeVersionId }],
    //             select: {
    //                 formId: true,
    //                 id: true,
    //                 status: true,
    //                 version: true,
    //             },
    //         });

    //         if (activeVersion) {
    //             const {
    //                 fields: activeFields,
    //                 stages: activeStages,
    //                 stageTransitions: activeStageTransitions,
    //                 stageAccessControls: activeStageAccessControls,
    //                 collections: activeCollections,
    //                 layouts: activeLayouts,
    //                 relatedForms: activeRelatedForms,
    //                 stageRoles: activeStageRoles,
    //                 formAutomation,
    //             } = await this._relationDataService.getFormVersionRelations({
    //                 formId: id,
    //                 versionId: form.activeVersionId,
    //                 formFieldRepo,
    //                 stageRepo,
    //                 stageTransitionRepo,
    //                 stageAccessControlRepo,
    //                 collectionRepo,
    //                 collectionItemRepo,
    //                 formLayoutRepo,
    //                 formRelatedRepo,
    //                 stageRoleRepo,
    //                 subscriptionRepo,
    //                 commentRepo,
    //                 generalAPRepo,
    //                 automationActionRepo,
    //                 isAccount,
    //                 manualEventRepo,
    //                 automationVersionRepo,
    //                 includeRoleAccessControls,
    //                 includeStageAccessControls,
    //                 includeAutomation,
    //                 includeComment,
    //                 includeFormCollections,
    //                 includeLayouts,
    //                 includeRelatedForms,
    //                 includeStages,
    //                 includeSubscription,
    //                 includeFields,
    //             });

    //             activeVersion = {
    //                 ...activeVersion,
    //                 stages: activeStages ?? [],
    //                 fields: activeFields ?? [],
    //                 stageTransitions: activeStageTransitions ?? [],
    //                 stageAccessControls: activeStageAccessControls ?? [],
    //                 formCollections: activeCollections ?? [],
    //                 formLayouts: activeLayouts ?? [],
    //                 stageRoles: activeStageRoles ?? [],
    //             };
    //             return {
    //                 form,
    //                 latestVersion,
    //                 activeVersion,
    //                 subscriptions,
    //                 latestRelatedForms: latestRelatedForms,
    //                 activeRelatedForms: activeRelatedForms,
    //                 formAutomation: formAutomation,
    //             };
    //         }
    //     }

    public async getById(request: GetByIdParams): Promise<FormVersionDataResponse> {
        const { id, formRepo, formVersionId, includeActiveVersion = false, includeSubscription = false } = request;

        const form = await formRepo.findOne({
            where: { id },
            select: {
                activeVersionId: true,
                latestVersionId: true,
                subscriptionIds: true,
                description: true,
                icon: true,
                id: true,
                name: true,
                status: true,
                latestVersion: true,
                activeVersion: true,
            },
        });

        if (!form) {
            return null;
        }

        let result: FormVersionDataResponse = {
            form: form,
        };

        if (includeSubscription) {
            result = await this.bindingSubscriptions(result, request);
        }

        if (formVersionId) {
            result = await this.bindingFormVersion(result, request, formVersionId);
        }

        result = await this.bindingFormLatestVersion(result, request);

        if (includeActiveVersion && form.activeVersionId) {
            result = await this.bindingFormActiveVersion(result, request);
        }

        return result;
    }

    private async bindingFormLatestVersion(formData: FormVersionDataResponse, request: GetByIdParams): Promise<FormVersionDataResponse> {
        const latestVersionId = formData.form.latestVersionId;
        if (!latestVersionId) {
            return formData;
        }

        const select: FindOptionsSelect<FormVersionEntity | FormVersionTenancyEntity> = {
            formId: true,
            id: true,
            status: true,
            version: true,
        };

        if (this._claims.accountId) {
            (select as FindOptionsSelect<FormVersionTenancyEntity>).testTransactionId = true;
        }

        let latestVersion = await request.formVersionRepo.findOne({
            where: [{ id: latestVersionId }],
            select: {
                ...select,
            },
        });

        const {
            fields: latestFields,
            stages: latestStages,
            stageTransitions: latestStageTransitions,
            stageAccessControls: latestStageAccessControls,
            collections: latestCollections,
            layouts: latestLayouts,
            relatedForms: latestRelatedForms,
            stageRoles: latestStageRoles,
            comments,
        } = await this._relationDataService.getFormVersionRelations({
            formId: formData.form.id,
            versionId: latestVersionId,
            ...request,
        });

        latestVersion = {
            ...latestVersion,
            stages: latestStages ?? [],
            fields: latestFields ?? [],
            stageTransitions: latestStageTransitions ?? [],
            stageAccessControls: latestStageAccessControls ?? [],
            formCollections: latestCollections ?? [],
            formLayouts: latestLayouts ?? [],
            stageRoles: latestStageRoles ?? [],
            comments: comments || [],
        };

        return {
            ...formData,
            latestVersion,
            latestRelatedForms: latestRelatedForms,
        };
    }

    private async bindingFormActiveVersion(formData: FormVersionDataResponse, request: GetByIdParams): Promise<FormVersionDataResponse> {
        let activeVersion = await request.formVersionRepo.findOne({
            where: [{ id: formData.form.activeVersionId }],
            select: {
                formId: true,
                id: true,
                status: true,
                version: true,
            },
        });

        if (!activeVersion) {
            return formData;
        }

        const {
            fields: activeFields,
            stages: activeStages,
            stageTransitions: activeStageTransitions,
            stageAccessControls: activeStageAccessControls,
            collections: activeCollections,
            layouts: activeLayouts,
            relatedForms: activeRelatedForms,
            stageRoles: activeStageRoles,
            formAutomation,
        } = await this._relationDataService.getFormVersionRelations({
            formId: formData.form.id,
            versionId: formData.form.activeVersionId,
            ...request,
        });

        activeVersion = {
            ...activeVersion,
            stages: activeStages ?? [],
            fields: activeFields ?? [],
            stageTransitions: activeStageTransitions ?? [],
            stageAccessControls: activeStageAccessControls ?? [],
            formCollections: activeCollections ?? [],
            formLayouts: activeLayouts ?? [],
            stageRoles: activeStageRoles ?? [],
        };

        return {
            ...formData,
            activeVersion,
            activeRelatedForms: activeRelatedForms,
            formAutomation: formAutomation,
        };
    }

    async bindingFormVersion(
        formData: FormVersionDataResponse,
        request: GetByIdParams,
        formVersionId: string,
    ): Promise<FormVersionDataResponse> {
        if (!formVersionId) {
            return formData;
        }

        const select: FindOptionsSelect<FormVersionEntity | FormVersionTenancyEntity> = {
            formId: true,
            id: true,
            status: true,
            version: true,
        };

        if (this._claims.accountId) {
            (select as FindOptionsSelect<FormVersionTenancyEntity>).testTransactionId = true;
        }

        let formVersion = await request.formVersionRepo.findOne({
            where: [{ id: formVersionId }],
            select: {
                ...select,
            },
        });
        const { version, relatedForms, formAutomation } = await this._getFormByVersionId({
            formId: formData.form.id,
            versionId: formVersionId,
            formVersion: formVersion,
            ...request,
        });

        return {
            ...formData,
            relatedForms,
            formVersion: version,
            formAutomation,
        };
    }

    private async bindingSubscriptions(formData: FormVersionDataResponse, request: GetByIdParams): Promise<FormVersionDataResponse> {
        if (!formData.form.subscriptionIds?.length) {
            return formData;
        }

        const subscriptions = await request.subscriptionRepo.find({
            where: [{ id: In(formData.form.subscriptionIds) }],
        });

        return {
            ...formData,
            subscriptions,
        };
    }

    public async getFieldById({
        id,
        formFieldRepo,
        formRepo,
        useActiveVersion = true,
    }: {
        id: string;
        formRepo: Repository<FormEntity | FormTenancyEntity>;
        formFieldRepo: Repository<FormFieldEntity | FormFieldTenancyEntity>;
        useActiveVersion?: boolean;
    }) {
        const form = await formRepo.findOneBy({ id });

        if (!form) {
            throw new BadRequestException('form_not_found');
        }

        // By default get field from active version
        if (useActiveVersion) {
            const fields = await formFieldRepo.findBy({
                formVersionId: form.activeVersionId ?? form.latestVersionId,
            });

            return fields;
        }

        if (!form.latestVersionId) {
            throw new BadRequestException('latest_version_not_found');
        }
        return formFieldRepo.findBy({
            formVersionId: form.latestVersionId,
        });
    }

    public async getCollectionFieldById({
        formVersionId,
        formCollectionRepo,
        drFieldRepo,
    }: {
        formVersionId: string;
        formCollectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        drFieldRepo: Repository<DataRegisterFieldEntity | DataRegisterFieldTenancyEntity>;
    }) {
        const where = { formVersionId };
        const collections = await formCollectionRepo.find({
            where: where,
        });
        const dataRegisterVersionIds = collections.map((c) => c.dataRegisterVersionId);

        const drFields = await drFieldRepo.find({
            where: {
                dataRegisterVersionId: In(dataRegisterVersionIds),
            },
        });

        return drFields;
    }

    public async getRelatedFormUsers({ forms }: { forms: FormDto[] }) {
        forms.forEach((item) => {
            const [firstNameCreatedBy, ...secondNameCreatedBy] = item.createdByUser?.split(' ') ?? [];
            const [firstNameUpdatedBy, ...secondNameUpdatedBy] = item.updatedByUser?.split(' ') ?? [];
            const [firstNamePublishedBy, ...secondNamePublishedBy] = item.publishedByUser?.split(' ') ?? [];

            item.created = {
                id: item.createdBy,
                firstName: firstNameCreatedBy ?? '',
                secondName: secondNameCreatedBy?.join(' ') ?? '',
            };

            item.updated = { id: item.updatedBy, firstName: firstNameUpdatedBy ?? '', secondName: secondNameUpdatedBy?.join(' ') ?? '' };

            item.published = {
                id: item.publishedBy,
                firstName: firstNamePublishedBy ?? '',
                secondName: secondNamePublishedBy?.join(' ') ?? '',
            };
        });
    }

    public async getRelatedFormVersionUsers({ formVersions }: { formVersions: FormVersionDto[] }) {
        formVersions.forEach((item) => {
            const [firstNameCreatedBy, ...secondNameCreatedBy] = item.createdByUser?.split(' ') ?? [];
            const [firstNameUpdatedBy, ...secondNameUpdatedBy] = item.updatedByUser?.split(' ') ?? [];
            const [firstNamePublishedBy, ...secondNamePublishedBy] = item.publishedByUser?.split(' ') ?? [];

            item.created = {
                id: item.createdBy,
                firstName: firstNameCreatedBy ?? '',
                secondName: secondNameCreatedBy?.join(' ') ?? '',
            };

            item.updated = { id: item.updatedBy, firstName: firstNameUpdatedBy ?? '', secondName: secondNameUpdatedBy?.join(' ') ?? '' };

            item.published = {
                id: item.publishedBy,
                firstName: firstNamePublishedBy ?? '',
                secondName: secondNamePublishedBy?.join(' ') ?? '',
            };
        });
    }

    public async getDeepRelatedForms({
        formId,
        formRelatedRepo,
        userRepo,
        isTenancy = false,
    }: {
        formId: string;
        formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity>;
        userRepo: Repository<UserEntity | UserTenancyEntity>;
        isTenancy?: boolean;
    }): Promise<{
        formEntities: Array<FormEntity | FormTenancyEntity>;
        users: Array<EditUser>;
        relatedForms: (FormRelatedEntity | FormRelatedTenancyEntity)[];
    }> {
        try {
            const formEntities: FormEntity[] = await this._getDeepRelatedForms({
                formId,
                formRelatedRepo,
                isTenancy,
            });
            const users = await this.getRelatedUsers(formEntities, userRepo);

            return {
                formEntities,
                users,
                relatedForms: [],
            };
        } catch (error) {
            this._loggerService.error(error);
            throw new Error(error);
        }
    }

    public async getRelatedForms({
        formId,
        formRelatedRepo,
    }: {
        formId: string;
        formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity>;
    }): Promise<{
        formEntities: Array<FormEntity | FormTenancyEntity>;
        relatedForms: (FormRelatedEntity | FormRelatedTenancyEntity)[];
    }> {
        const [firstRelatedForms, secondRelatedForms] = await Promise.all([
            formRelatedRepo.find({
                where: { firstFormId: formId },
                relations: ['secondForm'],
            }),
            formRelatedRepo.find({
                where: { secondFormId: formId },
                relations: ['firstForm'],
            }),
        ]);

        const relatedFormEntities = _.uniqBy([...firstRelatedForms, ...secondRelatedForms], 'id');

        const relatedForms = relatedFormEntities.map((item) => {
            if (item.firstFormId === formId) {
                return item.secondForm;
            }
            return item.firstForm;
        });

        return {
            formEntities: relatedForms,
            relatedForms: relatedFormEntities,
        };
    }

    public async getRelatedFormFields({
        formVersionIds,
        formFieldsRepo,
    }: {
        formVersionIds: string[];
        formFieldsRepo: Repository<FormFieldEntity | FormFieldTenancyEntity>;
    }) {
        if (!formVersionIds?.length) return [];
        return formFieldsRepo.findBy({
            formVersionId: In(formVersionIds),
        });
    }

    public async getFormVersionsByIds({
        formVersionIds,
        formVersionsRepo,
    }: {
        formVersionIds: string[];
        formVersionsRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
    }) {
        if (!formVersionIds?.length) return [];
        return formVersionsRepo.findBy({ id: In(formVersionIds) });
    }

    public async getPublishContents({
        formId,
        accountId,
        formRepo,
        formFieldsRepo,
        accountSubsRepo,
        formCollectionRepo,
        dataRegisterTransactionRepo,
        formRelatedRepo,
        dataRegisterRepo,
        accountToFormsRepo,
        accountDataRegisterRepo,
        stageRolesRepo,
        accountToFormRepo,
        generalAPSettingRepo,
    }: {
        formId: string;
        accountId: string;
        formRepo: Repository<FormEntity>;
        formFieldsRepo: Repository<FormFieldEntity>;
        accountSubsRepo: Repository<AccountSubscriptionEntity>;
        formCollectionRepo: Repository<FormCollectionEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity>;
        formRelatedRepo: Repository<FormRelatedEntity>;
        dataRegisterRepo: Repository<DataRegisterEntity>;
        accountDataRegisterRepo: Repository<AccountDataRegisterEntity>;
        accountToFormsRepo: Repository<AccountToFormEntity>;
        stageRolesRepo: Repository<StageRoleEntity>;
        accountToFormRepo: Repository<AccountToFormEntity>;
        generalAPSettingRepo: Repository<GeneralAutoPopulateSettingEntity>;
    }): Promise<{ response: unknown; errors: string[] }> {
        let result;
        try {
            result = await this._verifyFormSubscription({
                accountId,
                accountSubsRepo,
                formId,
                formRepo,
                accountToFormRepo,
            });
        } catch (err) {
            switch (err.message) {
                case 'account_form_subscription_not_existed':
                    return {
                        response: { canPublish: false },
                        errors: ['account_form_subscription_not_existed'],
                    };
                case 'form_was_published_to_account':
                    return {
                        response: { canPublish: false },
                        errors: ['form_was_published_to_account'],
                    };
                default:
                    throw err;
            }
        }
        const { form, formVersionId } = result;

        const { assignedDrs, assignedForms, unassignedDrs, unassignedForms, formRoleNames } = await this._verifyFormRelatedContents({
            formId: form.id,
            formVersionId,
            accountId,
            formFieldsRepo,
            formCollectionRepo,
            dataRegisterRepo,
            formRepo,
            accountSubsRepo,
            dataRegisterTransactionRepo,
            accountDataRegisterRepo,
            formRelatedRepo,
            accountToFormsRepo,
            stageRolesRepo,
            generalAPSettingRepo,
        });

        const canPublish = !unassignedDrs.length;

        return {
            response: { assignedDrs, assignedForms, unassignedDrs, unassignedForms, formRoleNames, canPublish },
            errors: [],
        };
    }

    public async verifyPublishForm({
        formId,
        request,
        formRepo,
        formFieldsRepo,
        formCollectionRepo,
        accountSubsRepo,
        dataRegisterTransactionRepo,
        accountDataRegisterRepo,
        formRelatedRepo,
        accountToFormsRepo,
        dataRegisterRepo,
        accountToFormRepo,
        stageRolesRepo,
        generalAPSettingRepo,
    }: {
        formId: string;
        request: PublishFormToAccountRequest;
        formRepo: Repository<FormEntity>;
        formFieldsRepo: Repository<FormFieldEntity>;
        accountSubsRepo: Repository<AccountSubscriptionEntity>;
        formCollectionRepo: Repository<FormCollectionEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity>;
        accountDataRegisterRepo: Repository<AccountDataRegisterEntity>;
        formRelatedRepo: Repository<FormRelatedEntity>;
        accountToFormsRepo: Repository<AccountToFormEntity>;
        dataRegisterRepo: Repository<DataRegisterEntity>;
        accountToFormRepo: Repository<AccountToFormEntity>;
        stageRolesRepo: Repository<StageRoleEntity>;
        generalAPSettingRepo: Repository<GeneralAutoPopulateSettingEntity>;
    }) {
        let verifyResult;
        try {
            verifyResult = await this._verifyFormSubscription({
                accountId: request.accountId,
                accountSubsRepo,
                formId,
                formRepo,
                accountToFormRepo,
            });
        } catch (err) {
            switch (err.message) {
                case 'account_form_subscription_not_existed':
                    throw new BadRequestException('account_subscription_not_existed');
                case 'form_was_published_to_account':
                    throw new BadRequestException('form_was_published_to_account');
                default:
                    throw err;
            }
        }
        const { form, formVersionId } = verifyResult;

        const result = await this._verifyFormRelatedContents({
            formId: form.id,
            formVersionId,
            accountId: request.accountId,
            formFieldsRepo,
            formCollectionRepo,
            dataRegisterRepo,
            formRepo,
            accountSubsRepo,
            dataRegisterTransactionRepo,
            accountDataRegisterRepo,
            formRelatedRepo,
            accountToFormsRepo,
            stageRolesRepo,
            generalAPSettingRepo,
        });

        return { ...result };
    }

    public async getRollUpContext({
        formId,
        formRepo,
        formRelatedRepo,
        collectionRepo,
        isTenancy,
    }: {
        formId: string;
        formRepo: Repository<FormEntity | FormTenancyEntity>;
        collectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity>;
        isTenancy?: boolean;
    }): Promise<LookUpOptionResponse> {
        const form = await formRepo.findOneBy({
            id: formId,
        });

        if (!form || !form.latestVersionId) {
            throw new BadRequestException('form_not_found');
        }

        const formCollections = await collectionRepo.findBy({
            formVersionId: form.latestVersionId,
        });

        const relatedForms = await this._getDeepRelatedForms({
            formId,
            formRelatedRepo,
            isTenancy,
        });

        const relatedActiveVersionIds = _.compact(relatedForms.map((f) => f.activeVersionId ?? f.latestVersionId));

        const relatedCollections = await collectionRepo.findBy({
            formVersionId: In(relatedActiveVersionIds),
        });

        const collections: LookUpOptionResponse['collections'] = (formCollections || []).map((c) => ({
            id: c.id,
            formVersionId: c.formVersionId,
            formName: form.name,
            name: c.name,
            dataRegisterId: c.dataRegisterId,
            identityId: c.identityId,
        }));

        const relatedFormsLookUp: LookUpOptionResponse['relatedForms'] = (relatedForms || []).map((f) => ({
            id: f.id,
            name: f.name,
            activeFormVersionId: f.activeVersionId ?? f.latestVersionId,
        }));

        const relatedCollectionsLookUp: LookUpOptionResponse['relatedCollections'] = (relatedCollections || []).map((c) => {
            const form = relatedForms.find((f) => (f.activeVersionId ?? f.latestVersionId) === c.formVersionId);
            return {
                id: c.id,
                name: c.name,
                formId: form.id,
                formVersionId: c.formVersionId,
                formName: form?.name,
                dataRegisterId: c.dataRegisterId,
                identityId: c.identityId,
            };
        });

        return {
            collections: collections,
            relatedForms: relatedFormsLookUp,
            relatedCollections: relatedCollectionsLookUp,
        };
    }

    public async getStageByFormVersionIds(formVersionIds: string[], stageRepo: Repository<StageTenancyEntity | StageEntity>) {
        return await stageRepo.findBy({ formVersionId: In(formVersionIds) });
    }

    public async getNewCollectionConfiguration({
        formVersionId,
        collectionRepository,
    }: {
        formVersionId: string;
        collectionRepository: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
    }) {
        try {
            const formCollections = await collectionRepository.find({
                where: {
                    formVersionId: formVersionId,
                },
                relations: ['formCollectionItems'],
            });

            if (formCollections?.length) {
                this._eventEmitter.emitAsync(FORM_COLLECTION_EVENT.UPDATE_NEW_COLLECTION, {
                    formCollections,
                    accountId: this._claims.accountId,
                    formVersionId: formVersionId,
                });
            }
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    //#endregion GET
    public _mapFormProperties({
        formDto,
        formVersion,
        relatedForms,
        versionType,
        isAccount,
        formAutomation,
    }: {
        formDto: FormDto;
        formVersion: FormVersionEntity | FormVersionTenancyEntity;
        relatedForms: Array<FormEntity | FormTenancyEntity>;
        versionType: 'target' | 'latest' | 'active';
        isAccount: boolean;
        formAutomation?: {
            automations: (AutomationVersionEntity | AutomationVersionEntity)[];
            actions: (AutomationActionEntity | AutomationActionTenancyEntity)[];
            manualEvents: (FormManualEventEntity | FormManualEventTenancyEntity)[];
        };
    }): FormDto {
        if (!formVersion) {
            return null;
        }

        const formVersionDto = isAccount
            ? this._mapper.map(formVersion, FormVersionTenancyEntity, FormVersionDto)
            : this._mapper.map(formVersion, FormVersionEntity, FormVersionDto);

        if (relatedForms?.length) {
            formVersionDto.relatedForms = this._mapper.mapArray(relatedForms, FormEntity, FormDto);
        }
        if (formVersionDto && formVersionDto.fields.length) {
            const { regularFields: latestRegularFields, partitionedFields: latestWidgetFields } =
                partitionFormFieldsByFieldValue<FormFieldEntity>({
                    fields: formVersionDto.fields,
                    fieldName: 'type',
                    fieldVal: FormFieldTypeEnum.Widget,
                });

            formVersionDto.fields = latestRegularFields;
            formVersionDto.widgets = latestWidgetFields;
        }

        formVersionDto.formLayouts = (formVersion.formLayouts || []) as FormLayoutDto[];

        if (formAutomation?.automations?.length || formAutomation?.actions?.length) {
            formVersionDto.formAutomation = {
                automations: formAutomation?.manualEvents?.map((event) => {
                    const automation = formAutomation?.automations?.find((me) => me.id === event.automationVersionId);
                    return {
                        id: automation?.automation?.id,
                        name: automation?.automation?.name ?? '',
                        versionId: automation.id,
                        sourceId: event?.sourceId,
                        manualEventConfig: event?.config,
                    };
                }),
            };
        }
        switch (versionType) {
            case 'target':
                formDto.formVersion = formVersionDto;
                break;
            case 'latest':
                formDto.latestFormVersion = formVersionDto;
                break;
            case 'active':
                formDto.activeFormVersion = formVersionDto;
                break;
            default:
                break;
        }

        return formDto;
    }
    //#region POST

    public async create({
        scope,
        request,
        formRepo,
        formVersionRepo,
        formFieldRepo,
        stageRepo,
    }: {
        scope: UserScope;
        request: CreateFormDto;
        formRepo: Repository<FormEntity | FormTenancyEntity>;
        formVersionRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
        formFieldRepo: Repository<FormFieldEntity | FormFieldTenancyEntity>;
        stageRepo: Repository<StageEntity | StageTenancyEntity>;
    }): Promise<FormEntity | FormTenancyEntity> {
        const versionId = v4();
        const startVersion = scope === UserScope.MDS ? 0 : 1;

        const formEntity = new FormEntity();
        formEntity.name = request.name;
        formEntity.description = request.description;
        formEntity.subscriptionIds = request.subscriptionIds;
        formEntity.status = FormStatus.Draft;
        formEntity.latestVersion = startVersion;
        formEntity.latestVersionId = versionId;
        formEntity.icon = request.icon;

        const queryRunner = this._dataSource.createQueryRunner();

        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const result = await formRepo.save(formEntity);

            if (!result) {
                throw new Error('Failed to create form');
            }

            const formVersionEntity = new FormVersionEntity();
            formVersionEntity.formId = result.id;
            formVersionEntity.id = versionId;
            formVersionEntity.version = startVersion;
            formVersionEntity.status = FormVersionStatus.Draft;

            const formVersionResult = await formVersionRepo.save(formVersionEntity);

            if (!formVersionResult) {
                throw new Error('Failed to create form version');
            }

            const getInitialStages = (formVersionId: string) => {
                const start = new StageEntity();
                start.formVersionId = formVersionId;
                start.name = 'Start';
                start.identityId = v4();
                start.config = { type: 'START', label: 'Start', width: 80, height: 80, position: { x: -200, y: 0 } };

                const end = new StageEntity();
                end.formVersionId = formVersionId;
                end.name = 'End';
                end.identityId = v4();
                end.config = { type: 'END', label: 'End', width: 80, height: 80, position: { x: 300, y: 0 } };
                return [start, end];
            };
            const initialStages: StageEntity[] = getInitialStages(formVersionResult.id);

            await stageRepo.save(initialStages);

            const transField = DefaultFieldFactory.initTransIdField({
                formName: request.name,
                transNum: 1,
                formVersionId: formVersionResult.id,
            });

            const formDefaultTransFieldResult = await formFieldRepo.save([transField]);

            if (!formDefaultTransFieldResult) {
                throw new Error('Failed to create default form transaction field');
            }

            await queryRunner.commitTransaction();
            return result;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            // Release the query runner
            await queryRunner.release();
        }
    }

    //#endregion POST

    public async addRelatedForms({
        request,
        formRepo,
        connection,
        isAccount,
        accountId,
    }: {
        request: AddRelatedFormDto;
        connection: DataSource;
        isAccount: boolean;
        formRepo: Repository<FormEntity | FormTenancyEntity>;
        accountId?: string;
    }): Promise<FormEntity | FormTenancyEntity> {
        const formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity> = isAccount
            ? connection.getRepository(FormRelatedTenancyEntity)
            : connection.getRepository(FormRelatedEntity);

        const forms = await formRepo.findBy({
            id: In([...request.relatedFormIds, request.formId]),
        });

        const form = forms.find((f) => f.id === request.formId);

        const existingRelatedForms = await formRelatedRepo.find({
            where: [{ firstFormId: request.formId }, { secondFormId: request.formId }],
        });

        const relatedForms = forms.filter((f) => f.id !== request.formId);

        if (!form) {
            throw new BadRequestException('invalid_payload');
        }

        const newRelatedForms = relatedForms.map((relatedForm) => {
            const entity = new FormRelatedEntity();
            entity.firstFormId = form.id;
            entity.secondFormId = relatedForm.id;
            entity.secondFormVersionId = relatedForm.latestVersionId; //TODO: refactor to active version after possible to publish
            entity.configs = request?.configuration[relatedForm.id] ?? entity.configs;
            return entity;
        });

        // Separate new related forms into new, existing, and to be deleted
        const [toBeDeleted, toBeSaved] = this.getFormsToUpdate(existingRelatedForms, newRelatedForms);

        const toBeAdded = toBeSaved.filter((f) => !f.id);

        await connection.transaction(async (manager) => {
            const formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity> = isAccount
                ? manager.getRepository(FormRelatedTenancyEntity)
                : manager.getRepository(FormRelatedEntity);

            if (toBeDeleted.length) {
                await formRelatedRepo.softRemove(toBeDeleted);
            }

            if (toBeSaved.length) {
                await formRelatedRepo.save(toBeSaved);
            }

            await this._handleRelatedUpdateACL({
                accountId: accountId,
                addedForms: toBeAdded,
                deletedForms: toBeDeleted,
                formVersionId: form.latestVersionId,
                dataSource: manager,
            });
        });

        return form;
    }

    //#region  UPDATE
    public async update({
        request,
        formRepo,
        formVersionRepo,
        dataSource,
        isAccount,
        userId,
        userByName,
        relationRequest,
        accountId,
        automationVersionRepo,
        automationRuleRepo,
        automationActionRepo,
        stageRepo,
        stageACLRepo,
        stageRoleAclRepo,
        stageRoleRepo,
        automationRepo,
    }: UpdateFormParams): Promise<FormEntity | FormTenancyEntity> {
        const { step, id, automation, apiBuilder } = request;

        if (!id) throw new BadRequestException('form_id_required');

        const form = await formRepo.findOneBy({ id });

        const updateFormVersionResult = await this.updateFormVersion({
            isAccount,
            formVersionRepo,
            connection: dataSource,
            formId: id,
            userId,
            userByName,
        });

        await formRepo.update({ id }, { updatedAt: new Date() });

        switch (step) {
            case 'information':
                return this.updateFormInformation({ request, formRepo, dataSource, isAccount, versionResult: updateFormVersionResult });
            case 'fields':
                return this.updateFormFields({
                    request,
                    formRepo,
                    formVersionRepo,
                    dataSource,
                    isAccount,
                    accountId,
                    versionResult: updateFormVersionResult,
                });
            case 'collections':
                throw new NotImplementedException('Not implemented');
            case 'relations':
                return this.addRelatedForms({
                    request: relationRequest,
                    formRepo,
                    connection: dataSource,
                    isAccount,
                    accountId,
                });

            case 'automation': {
                const { isNewVersion, formAutomationVersionIdMap, automationActionIdMap, automationRuleIdMap } = updateFormVersionResult;
                const mappedRequest = this._automationConfigurationService.mapRelationToRequest({
                    isNewVersion: isNewVersion,
                    request: automation,
                    versionId: formAutomationVersionIdMap?.get(request.automation.automationVersionId) ?? automation.automationVersionId,
                    automationActionIdMap: automationActionIdMap,
                    automationRuleIdMap: automationRuleIdMap,
                });

                await this._automationConfigurationService.updateConfiguration({
                    request: mappedRequest,
                    automationRepo,
                    automationVersionRepo,
                    automationRuleRepo,
                    automationActionRepo,
                    stageRepo,
                    stageACLRepo,
                    stageRoleAclRepo,
                    stageRoleRepo,
                });

                return form;
            }

            case 'api_builder': {
                const { isNewVersion, formApiVersionIdMap, formApiVersionEndpointIdMap, formApiVersionCommentIdMap } =
                    updateFormVersionResult;

                const mappedRequest = this._apiConfigurationDataService.mapRelationToRequest({
                    isNewVersion: isNewVersion,
                    request: apiBuilder,
                    versionId: formApiVersionIdMap?.get(request.apiBuilder?.apiVersionId) ?? apiBuilder.apiVersionId,
                    endpointIdMap: formApiVersionEndpointIdMap,
                });

                const addEndpoint = (mappedRequest?.stepData as UpdateApiConfigurationRequest)?.addEndpoint;

                const updateEndpoint = (mappedRequest?.stepData as UpdateApiConfigurationRequest)?.updateEndpoint;

                if (addEndpoint) {
                    await this._apiConfigurationDataService.createEndpoint({
                        request: addEndpoint,
                        apiVersionEndpointRepo: this._apiVersionEndpointRepo,
                    });
                }

                if (updateEndpoint) {
                    await this._apiConfigurationDataService.updateEndpoint({
                        request: {
                            ...updateEndpoint,
                        } as ApiVersionEndpointTenancyEntity,
                        apiVersionEndpointRepo: this._apiVersionEndpointRepo,
                    });
                }

                await this._apiConfigurationDataService.updateConfiguration({
                    request: mappedRequest.stepData as UpdateApiConfigurationRequest,
                    versionId: formApiVersionIdMap?.get(request.apiBuilder?.apiVersionId) ?? apiBuilder.apiVersionId,
                    apiVersionEndpointRepo: this._apiVersionEndpointRepo,
                    apiVersionRepo: this._apiVersionRepo,
                });

                return form;
            }

            case 'stages': {
                await this.updateFormStages({
                    request,
                    formRepo,
                    formVersionRepo,
                    dataSource,
                    isAccount,
                    versionResult: updateFormVersionResult,
                    accountId,
                });
                return form;
            }

            case 'accessControl':
                throw new NotImplementedException('Not implemented');
            case 'layouts':
                return this.updateFormLayouts({
                    request,
                    formRepo,
                    formVersionRepo,
                    dataSource,
                    isAccount,
                    versionResult: updateFormVersionResult,
                });
            case 'view':
                return this.updateFormViews({
                    request,
                    formRepo,
                    formVersionRepo,
                    dataSource,
                    isAccount,
                    versionResult: updateFormVersionResult,
                });
            default:
                throw new BadRequestException('Invalid form builder step');
        }
    }

    public async updateFormVersion({
        formVersionId,
        formVersionRepo,
        connection,
        isAccount,
        userId,
        userByName,
        formId,
    }: {
        formVersionRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
        connection: DataSource | EntityManager;
        isAccount: boolean;
        userId: string;
        userByName: string;
        formVersionId?: string;
        formId?: string;
    }): Promise<VerifyFormResultType> {
        if (!formVersionId && !formId) {
            throw new BadRequestException('form_or_form_version_required');
        }

        if (formVersionId) {
            const formVersion = await this.getFormVersion({ versionId: formVersionId, formVersionRepo });

            if (!formVersion) {
                throw new NotFoundException('Form version not found');
            }

            if (formVersion.status === FormVersionStatus.Draft) {
                return { formVersion, isNewVersion: false };
            }

            const service = this._formVersionDataFactoryService.create({ isAccount });

            const verifyResult = await service.verifyNewFormVersion({
                connection,
                formId: formVersion.formId,
                isAccount,
                userId,
                userByName,
            });

            return verifyResult;
        }

        if (formId) {
            const service = this._formVersionDataFactoryService.create({ isAccount });
            const verifyResult = await service.verifyNewFormVersion({
                connection,
                formId,
                isAccount,
                userId,
                userByName,
            });

            return verifyResult;
        }
    }

    //#endregion UPDATE

    //#region DELETE
    public async delete({ id, formRepo, dataSource, isAccount }: DeleteFormParams) {
        const form = await formRepo.findOne({
            where: { id: id },
        });

        if (!form) {
            throw new NotFoundException('Form not found');
        }

        const result = await formRepo.softRemove(form);

        await this._deleteAutomationByContextPort.execute({
            contextId: id,
            contextType: [AutomationContextType.FormTransaction],
        });

        return !!result;
    }
    //#endregion DELETE

    //#region PRIVATE UPDATE METHODS
    private async updateFormInformation({ request, formRepo, dataSource, isAccount, versionResult }: UpdateFormInformationParams) {
        const form = await formRepo.findOne({
            where: { id: request.id },
        });

        if (!form) {
            throw new Error('Form not found');
        }

        let needToUpdateFavoriteMenu = false;
        if (isAccount && (request.name !== form.name || request.icon !== form.icon)) {
            needToUpdateFavoriteMenu = true;
        }

        form.name = request.name;
        form.description = request.description;
        form.icon = request.icon;
        if (request.subscriptionIds) {
            form.subscriptionIds = request.subscriptionIds;
        }

        if (!request.transactionField) {
            return await formRepo.save(form);
        }

        if (versionResult.isNewVersion && versionResult.fieldIdMap?.has(request.transactionField?.id)) {
            request.transactionField.id = versionResult.fieldIdMap.get(request.transactionField.id);
            request.transactionField.formVersionId = versionResult.formVersion.id;
        }

        const result = await dataSource.transaction(async (manager) => {
            const _formFieldRepo: Repository<FormFieldTenancyEntity | FormFieldEntity> = isAccount
                ? manager.getRepository(FormFieldTenancyEntity)
                : manager.getRepository(FormFieldEntity);
            const _formRepo = (isAccount ? manager.getRepository(FormTenancyEntity) : manager.getRepository(FormEntity)) as Repository<
                FormTenancyEntity | FormEntity
            >;

            const updatedForm = await _formRepo.save(form);

            // Update transaction field
            const transFieldUpdate = this._mapper.mapArray([request.transactionField], CreateFormFieldDto, FormFieldEntity);
            await _formFieldRepo.save(transFieldUpdate);
            return updatedForm;
        });

        if (needToUpdateFavoriteMenu) {
            const favoriteMenuRepo = dataSource.getRepository(FavoriteMenuTenancyEntity);
            this.updateFavoriteMenu({
                form: form,
                favoriteMenuRepo: favoriteMenuRepo,
            });
        }

        return result;
    }

    private async updateFavoriteMenu({
        favoriteMenuRepo,
        form,
    }: {
        form: FormEntity | FormTenancyEntity;
        favoriteMenuRepo: Repository<FavoriteMenuTenancyEntity>;
    }) {
        const favoriteMenus = await favoriteMenuRepo.find({
            where: {
                router: ILike(`%${form.id}%`),
            },
        });

        if (favoriteMenus.length) {
            favoriteMenus.forEach((favoriteMenu) => {
                favoriteMenu.imageUrl = form.icon;
                favoriteMenu.displayName = form.name;
            });

            await favoriteMenuRepo.save(favoriteMenus);
        }
    }

    private async updateFormFields({
        request,
        formRepo,
        formVersionRepo,
        dataSource,
        isAccount,
        versionResult,
        accountId,
    }: UpdateFormFieldsParams) {
        const { fields } = request;

        const { isNewVersion, collectionIdMap } = versionResult;

        if (versionResult?.isNewVersion) {
            fields.forEach((f) => {
                if (versionResult.fieldIdMap?.has(f.id)) {
                    f.id = versionResult.fieldIdMap.get(f.id);
                }
            });
        }

        fields.forEach((f) => {
            if (f.type === FormFieldTypeEnum.Lookup && f.configuration?.dataRegister) {
                delete f.configuration?.dataRegister;
            }
        });

        const form = await formRepo.findOne({
            where: { id: request.id },
        });

        const latestVersion = form.latestVersionId;

        const formVersion = await this.getFormVersion({ versionId: latestVersion, formVersionRepo });

        if (!formVersion) {
            throw new NotFoundException('Form version not found');
        }

        const result = await dataSource.transaction(async (manager) => {
            const _formFieldRepo: Repository<FormFieldTenancyEntity | FormFieldEntity> = isAccount
                ? manager.getRepository(FormFieldTenancyEntity)
                : manager.getRepository(FormFieldEntity);
            const _formRepo = (isAccount ? manager.getRepository(FormTenancyEntity) : manager.getRepository(FormEntity)) as Repository<
                FormTenancyEntity | FormEntity
            >;

            const _formAutoPopulateRepo: Repository<FormAutoPopulateSettingEntity | FormAutoPopulateSettingTenancyEntity> = isAccount
                ? manager.getRepository(FormAutoPopulateSettingTenancyEntity)
                : manager.getRepository(FormAutoPopulateSettingEntity);

            // Update existing version
            const formFieldEntities = await _formFieldRepo.find({
                where: { formVersionId: formVersion.id, type: Not(FormFieldTypeEnum.Widget) },
            });

            const autoPopulatedFields = [];

            const fieldUpdate = this._mapper.mapArray(fields || [], CreateFormFieldDto, FormFieldEntity);
            fieldUpdate.forEach((f) => {
                f.formVersionId = formVersion.id;
                f.lookupDataset = f.configuration?.dataset;
                f.lookupTargetId = f.configuration?.targetId;

                if (f.rollupDependencies?.length) {
                    f.rollupDependencies.forEach((dep) => {
                        dep.formVersionId = versionResult.formVersion.id;
                    });
                }
                if (isNewVersion && (f.type as unknown as FormFieldTypeEnum) === FormFieldTypeEnum.Rollup) {
                    if (f?.configuration.rollup?.rollupDependencies?.length) {
                        f?.configuration.rollup.rollupDependencies.forEach((item) => {
                            const newCollectionId = collectionIdMap?.get(item?.collectionFetchingId) ?? item?.collectionFetchingId;

                            item.collectionFetchingId = newCollectionId;
                            // map first and second context
                            if (item?.targetType) {
                                switch (item.targetType) {
                                    case TargetTypeEnum.FORM:
                                        item.contextId = formVersion.id;
                                        break;
                                    default:
                                        break;
                                }
                            }
                        });
                    }
                }

                getGeneralAutoPopulates({
                    autoPopulatedFields,
                    field: f,
                    formId: form.id,
                    latestVersionId: latestVersion,
                });
            });

            const deleteFields = formFieldEntities?.filter(
                (originEntity) => !fieldUpdate.some((updateEntity) => originEntity.id === updateEntity.id),
            );

            const addedFields = fieldUpdate.filter((field) => !formFieldEntities.some((f) => f.id === field.id));

            const onlyUpdateFields = fieldUpdate.filter((field) => formFieldEntities.some((f) => f.id === field.id));

            await _formAutoPopulateRepo.delete({ originFormId: form.id, originFormVersionId: latestVersion });
            await _formAutoPopulateRepo.save(autoPopulatedFields);

            await _formFieldRepo.softRemove(deleteFields);
            await _formFieldRepo.save(fieldUpdate);

            await _formRepo.update({ id: form.id }, { updatedAt: new Date() });

            await this._handleFieldUpdateACL({
                accountId: accountId,
                addedFields: addedFields,
                deletedFields: deleteFields,
                updatedFields: onlyUpdateFields,
                formVersionId: formVersion.id,
                dataSource: manager,
            });
            return form;
        });
        return result;
    }

    public async updateWidgetFields({ request, dataSource, isAccount }: UpdateWidgetFieldsParams) {
        await dataSource.transaction(async (manager) => {
            const _formFieldRepo: Repository<FormFieldTenancyEntity | FormFieldEntity> = isAccount
                ? manager.getRepository(FormFieldTenancyEntity)
                : manager.getRepository(FormFieldEntity);

            const widgetFieldEntities = await _formFieldRepo.findBy({
                formVersionId: request.formVersionId,
                type: FormFieldTypeEnum.Widget,
            });

            const requestUpdateWidgets = this._mapper.mapArray(request.widgets || [], CreateFormFieldDto, FormFieldEntity);

            const deleteFields = widgetFieldEntities?.filter(
                (originEntity) => !requestUpdateWidgets.some((updateEntity) => originEntity.fieldId === updateEntity.fieldId),
            );

            const updateFields = requestUpdateWidgets.map((field) => {
                const currentEntity = widgetFieldEntities.find((item) => item.fieldId === field.fieldId);
                if (currentEntity) field.id = currentEntity.id;
                return field;
            });

            const addedFields = updateFields.filter((field) => !widgetFieldEntities.some((f) => f.fieldId === field.fieldId));

            const onlyUpdatedFields = updateFields.filter((field) => widgetFieldEntities.some((f) => f.fieldId === field.fieldId));

            await _formFieldRepo.softRemove(deleteFields);
            await _formFieldRepo.save(updateFields);

            await this._handleFieldUpdateACL({
                accountId: this._claims.accountId,
                addedFields: addedFields,
                deletedFields: deleteFields,
                formVersionId: request.formVersionId,
                updatedFields: onlyUpdatedFields,
                type: AccessControlType.WIDGET,
                dataSource: manager,
            });
        });
    }

    private async updateFormStages(params: UpdateFormStagesParams) {
        await this._formStageDataService.updateFormStages({
            ...params,
        });
    }

    private async updateFormLayouts({ request, formRepo, formVersionRepo, dataSource, isAccount, versionResult }: UpdateFormLayoutsParams) {
        const { layouts } = request;

        const form = await formRepo.findOne({
            where: { id: request.id },
        });

        const latestVersion = form.latestVersionId;

        const formVersion = await this.getFormVersion({ versionId: latestVersion, formVersionRepo });

        if (!formVersion) {
            throw new NotFoundException('Form version not found');
        }

        const result = await dataSource.transaction(async (manager) => {
            const _formLayoutRepo: Repository<FormLayoutEntity | FormLayoutTenancyEntity> = isAccount
                ? manager.getRepository(FormLayoutTenancyEntity)
                : manager.getRepository(FormLayoutEntity);

            const _formLayoutZoneRepo: Repository<FormLayoutZoneEntity | FormLayoutZoneTenancyEntity> = isAccount
                ? manager.getRepository(FormLayoutZoneTenancyEntity)
                : manager.getRepository(FormLayoutZoneEntity);

            const _formLayoutZoneFieldRepo: Repository<FormLayoutZoneFieldEntity | FormLayoutZoneFieldTenancyEntity> = isAccount
                ? manager.getRepository(FormLayoutZoneFieldTenancyEntity)
                : manager.getRepository(FormLayoutZoneFieldEntity);

            const _formManualEventRepo: Repository<FormManualEventEntity | FormManualEventTenancyEntity> = isAccount
                ? manager.getRepository(FormManualEventTenancyEntity)
                : manager.getRepository(FormManualEventEntity);

            let result: FormTenancyEntity | FormEntity = form;
            if (!layouts?.length) {
                return result;
            }

            if (versionResult?.isNewVersion) {
                layouts.forEach((layout) => {
                    layout.id = versionResult.layoutIdMap.get(layout.id);
                    layout.formVersionId = formVersion.id;
                });

                const requestZones = layouts.flatMap((l) => l.layoutZones || []);

                requestZones.forEach((zone) => {
                    zone.id = versionResult.layoutZoneIdMap.get(zone.id);
                    zone.layoutId = versionResult.layoutIdMap.get(zone.layoutId);
                });

                layouts
                    .flatMap((l) => l.layoutZones?.flatMap((lz) => lz.layoutFields || []) || [])
                    .forEach((field) => {
                        field.layoutZoneId = versionResult.layoutZoneIdMap.get(field.layoutZoneId);
                        field.id = versionResult.layoutZoneFieldIdMap.get(field.id);
                        field.layoutId = versionResult.layoutIdMap.get(field.layoutId);
                    });
            }

            const layoutIds = layouts.map((layout) => layout.id);
            const existedLayoutZones = await _formLayoutZoneRepo.find({
                where: {
                    layoutId: In(layoutIds),
                },
                relations: {
                    layoutFields: true,
                },
            });

            const layoutZoneIds = _.compact(existedLayoutZones?.map((zone) => zone?.identityId));

            const existManualEvents = await this._getExistsManualEvent({
                formVersionId: formVersion.id,
                layoutZoneIds,
                formManualEventRepo: _formManualEventRepo,
            });

            //update layout
            const removedZones: Array<FormLayoutZoneEntity | FormLayoutZoneTenancyEntity> = [];
            const updatedZones: Array<FormLayoutZoneEntity | FormLayoutZoneTenancyEntity> = [];
            const updatedFields: Array<FormLayoutZoneFieldEntity | FormLayoutZoneFieldTenancyEntity> = [];
            const removeFields: Array<FormLayoutZoneFieldEntity | FormLayoutZoneFieldTenancyEntity> = [];

            //Update form manual event
            let formManualEvents: Array<FormManualEventEntity | FormManualEventTenancyEntity> = [];
            let deleteManualEvents: Array<FormManualEventEntity | FormManualEventTenancyEntity> = [];

            const layoutEntities = layouts.map((layout) => {
                const layoutId = layout.id;
                const { layoutZones = [], ...restLayout } = layout;
                const layoutEntity = _formLayoutRepo.create({
                    ...restLayout,
                    formVersionId: formVersion.id,
                    identityId: restLayout.identityId ?? layoutId,
                });

                const exitedZones = existedLayoutZones.filter((lz) => lz.layoutId === layout.id) || [];
                const _removeZones = exitedZones.filter((zone) => !layoutZones.some((lz) => lz.identityId === zone.identityId));

                if (_removeZones.length) {
                    removedZones.push(..._removeZones);
                    _removeZones.forEach((removeZone) => {
                        removeFields.push(...(removeZone.layoutFields || []));
                    });
                }

                for (const layoutZone of layoutZones) {
                    const { layoutFields, ...restZone } = layoutZone;
                    const layoutZoneEntity = _formLayoutZoneRepo.create({ ...restZone, layoutId, id: layoutZone.id ?? v4() });
                    layoutZoneEntity.identityId = layoutZoneEntity.identityId ?? layoutZoneEntity.id;
                    updatedZones.push(layoutZoneEntity);

                    const exitedFields = exitedZones.find((lz) => lz.id === layoutZone.id)?.layoutFields || [];
                    removeFields.push(
                        ...exitedFields.filter(
                            (ef) =>
                                !layoutFields?.some(
                                    (lf) => lf.id === ef.id,
                                    // lf.fieldId === ef.fieldId && lf.config.columnId === ef.config.columnId
                                ),
                        ),
                    );

                    const _updatedFields = (layoutFields || []).map(
                        (field: FormLayoutZoneFieldEntity | FormLayoutZoneFieldTenancyEntity) => {
                            const { id, ...restField } = field;
                            const existedId = id
                                ? exitedFields.find((f) => f.id === field.id)?.id
                                : exitedFields.find((f) => f.fieldId === field.fieldId && f.config?.columnId === field.config?.columnId)
                                      ?.id;

                            return _formLayoutZoneFieldRepo.create({
                                ...restField,
                                id: existedId,
                                layoutId,
                                layoutZoneId: layoutZoneEntity.id,
                            });
                        },
                    );
                    updatedFields.push(..._updatedFields);

                    if ([LayoutZoneType.COLLECTION, LayoutZoneType.TOOLBAR].includes(layoutZone.type)) {
                        const _existsManualEvent = existManualEvents?.filter((manual) => manual.sourceId === layoutZone.identityId);

                        const sourceType =
                            layoutZone.type === LayoutZoneType.COLLECTION
                                ? ManualEventSourceType.Collection
                                : ManualEventSourceType.Toolbar;

                        const { updateManualEvent, deleteManualEvent } = this._transformManualEvent(
                            layoutZone,
                            formVersion.id,
                            _existsManualEvent,
                            sourceType,
                        );

                        formManualEvents = [...formManualEvents, ...updateManualEvent];
                        deleteManualEvents = [...deleteManualEvents, ...deleteManualEvent];
                    }
                }
                return layoutEntity;
            });

            //sorted tab item zones
            const parentTabZones = updatedZones.filter((zone) => !zone.parentIdentityId && zone.type === LayoutZoneType.TAB);
            for (const parentTabZone of parentTabZones) {
                const children = updatedZones.filter((zone) => zone.parentIdentityId === parentTabZone.identityId);
                children.forEach((child, index) => {
                    if (child.config) {
                        child.config.priority = child.config.priority ?? index + 1;
                    }
                });
            }

            await _formLayoutRepo.save(layoutEntities);
            await Promise.all(
                [
                    removeFields.length ? _formLayoutZoneFieldRepo.remove(removeFields) : undefined,
                    updatedZones.length ? _formLayoutZoneRepo.save(updatedZones) : undefined,
                ].filter(Boolean),
            );

            await Promise.all(
                [
                    removedZones.length ? _formLayoutZoneRepo.remove(removedZones) : undefined,
                    updatedFields.length ? _formLayoutZoneFieldRepo.save(updatedFields) : undefined,
                ].filter(Boolean),
            );

            await Promise.all(
                [
                    formManualEvents.length ? _formManualEventRepo.save(formManualEvents) : undefined,
                    deleteManualEvents.length ? _formManualEventRepo.remove(deleteManualEvents) : undefined,
                    removedZones?.length
                        ? _formManualEventRepo.softDelete({
                              sourceId: In(removedZones.map((zone) => zone.identityId)),
                              formVersionId: formVersion.id,
                              //   sourceType: ManualEventSourceType.Toolbar,
                          })
                        : undefined,
                ].filter(Boolean),
            );

            const addedManualActions = formManualEvents.filter((action) => !existManualEvents.some((f) => f.id === action.id));

            await Promise.all(
                [
                    addedManualActions.length
                        ? this._handleManualEventAdded({
                              actions: addedManualActions,
                              formVersionId: formVersion.id,
                              dataSource: manager,
                              accountId: this._claims.accountId,
                          })
                        : undefined,

                    deleteManualEvents.length
                        ? this._handleManualEventDeleted({
                              actions: deleteManualEvents,
                              formVersionId: formVersion.id,
                              dataSource: manager,
                              accountId: this._claims.accountId,
                          })
                        : undefined,

                    formManualEvents.length
                        ? this._handleManualEventUpdated({
                              actions: formManualEvents,
                              formVersionId: formVersion.id,
                              dataSource: manager,
                              accountId: this._claims.accountId,
                          })
                        : undefined,
                ].filter(Boolean),
            );

            await _formLayoutZoneFieldRepo.save(updatedFields);

            return result;
        });

        return result;
    }

    private _transformManualEvent(
        layoutZone: FormLayoutZoneDto,
        formVersionId: string,
        existManualEvents: (FormManualEventEntity | FormManualEventTenancyEntity)[],
        sourceType: ManualEventSourceType,
    ): {
        updateManualEvent: (FormManualEventEntity | FormManualEventTenancyEntity)[];
        deleteManualEvent: (FormManualEventEntity | FormManualEventTenancyEntity)[];
    } {
        const automationEntities = (layoutZone?.config as any).automation?.map((automation) => {
            const id = automation?.id ?? v4();
            const manualAutomation: FormManualEventEntity = {
                id: id,
                identityId: automation?.identityId ?? id,
                formVersionId: formVersionId,
                sourceType: sourceType ?? ManualEventSourceType.Toolbar,
                sourceId: layoutZone.identityId,
                automationId: automation.automationId,
                automationVersionId: automation.automationVersionId,
                config: automation.config,
            };

            return manualAutomation;
        });

        const deleteManualEvent: (FormManualEventEntity | FormManualEventTenancyEntity)[] = [];

        if (existManualEvents.length) {
            existManualEvents.forEach((exist) => {
                const _exist = automationEntities.find((manual) => manual.id === exist.id);

                if (!_exist) {
                    deleteManualEvent.push(exist);
                } else {
                    _exist.identityId = exist.identityId;
                }
            });
        }

        return {
            updateManualEvent: automationEntities ?? [],
            deleteManualEvent: deleteManualEvent ?? [],
        };
    }

    private async _getExistsManualEvent({
        formVersionId,
        layoutZoneIds,
        formManualEventRepo,
    }: {
        formVersionId: string;
        layoutZoneIds?: string[];
        formManualEventRepo: Repository<FormManualEventEntity | FormManualEventTenancyEntity>;
    }) {
        const builder = formManualEventRepo.createQueryBuilder('manualEvent').where({
            formVersionId: formVersionId,
            sourceType: In([ManualEventSourceType.Toolbar, ManualEventSourceType.Collection]),
        });

        if (layoutZoneIds?.length) {
            builder.andWhere({ sourceId: In(layoutZoneIds) });
        }

        const existManualEvents = await builder.getMany();
        return existManualEvents;
    }

    private async updateFormViews({ request, formRepo, formVersionRepo, dataSource, isAccount, versionResult }: UpdateFormViewsParams) {
        const { views } = request;

        const { isNewVersion, viewIdMap, viewItemIdMap } = versionResult;

        if (isNewVersion) {
            views.forEach((v) => {
                if (v.id && viewIdMap?.has(v.id)) {
                    const newId = viewIdMap.get(v.id);
                    v.id = newId;

                    if (v.viewItems && !_.isEmpty(v.viewItems)) {
                        Object.values(v.viewItems).forEach((items) => {
                            items.forEach((item) => {
                                item.id = viewItemIdMap?.get(item.id) || item.id;
                                item.formViewId = newId;
                            });
                        });
                    }
                }
            });
        }

        const form = await formRepo.findOne({
            where: { id: request.id },
        });

        if (!views?.length) return form;

        await this._formViewDataService.update({
            request: views,
            dataSource,
            isAccount,
            formVersionId: versionResult?.formVersion?.id,
        });

        return form;
    }

    //#endregion PRIVATE UPDATE METHODS

    public async getFormVersion({
        versionId,
        formVersionRepo,
    }: {
        versionId: string;
        formVersionRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
    }) {
        const formVersion = await formVersionRepo.findOne({
            where: [{ id: versionId }],
        });
        return formVersion;
    }

    private getFormsToUpdate(
        existingForms: FormRelatedEntity[],
        newForms: FormRelatedEntity[],
    ): [FormRelatedEntity[], FormRelatedEntity[]] {
        const toBeDeleted: FormRelatedEntity[] = [];
        const toBeSaved: FormRelatedEntity[] = [];
        const existingMap = new Map<string, FormRelatedEntity>();

        existingForms.forEach((form) => {
            const key = this.generateKey(form.firstFormId, form.secondFormId);
            existingMap.set(key, form);
        });
        newForms.forEach((newForm) => {
            const key = this.generateKey(newForm.firstFormId, newForm.secondFormId);
            if (
                existingMap.has(key) &&
                (!newForm?.configs || !UtilsService.deepDiff(this.getFormConfigs(key, existingMap), newForm?.configs))
            ) {
                existingMap.delete(key);
            } else {
                toBeSaved.push(newForm);
            }
        });

        existingMap.forEach((form) => {
            toBeDeleted.push(form);
        });

        return [toBeDeleted, toBeSaved];
    }

    private getFormConfigs = (key: string, relations: Map<string, FormRelatedEntity>) => {
        const configs = relations?.get(key)?.configs;
        return configs;
    };

    private generateKey(firstFormId: string, secondFormId: string): string {
        return [firstFormId, secondFormId].sort().join('-');
    }

    private async getRelatedUsers(
        formEntities: Array<{ createdBy?: string; publishedBy?: string; updatedBy?: string }>,
        userRepo: Repository<UserEntity | UserTenancyEntity>,
    ) {
        const allUserIds = formEntities?.filter(Boolean)?.map((f) => [f.createdBy, f.publishedBy, f.updatedBy]);
        const userIds = Array.from(new Set(allUserIds.flat()))?.filter(Boolean);

        const userEntities = await userRepo.find({
            where: {
                id: In(userIds),
            },
            withDeleted: true,
        });
        const users: EditUser[] = userEntities.map((u) => ({ id: u.id, firstName: u.firstName, secondName: u.secondName }));
        return users;
    }

    private async _getFormByVersionId({
        formVersion,
        formId,
        versionId,
        formFieldRepo,
        stageRepo,
        stageTransitionRepo,
        stageAccessControlRepo,
        collectionRepo,
        collectionItemRepo,
        formLayoutRepo,
        formRelatedRepo,
        stageRoleRepo,
        subscriptionRepo,
        commentRepo,
        generalAPRepo,
        manualEventRepo,
        isAccount,
        includeAutomation,
        includeComment,
        includeLayouts,
        includeSubscription,
        includeRoleAccessControls,
        includeStageAccessControls,
        includeFormCollections,
        includeRelatedForms,
        includeStages,
        includeFields,
        includeStageRoleAccessControls,
        automationActionRepo,
        automationVersionRepo,
    }: GetFormRelationParams & { formVersion: FormVersionEntity | FormVersionTenancyEntity }) {
        let cloneForm = _.cloneDeep(formVersion);
        const {
            fields,
            stages,
            stageTransitions,
            stageAccessControls,
            collections,
            layouts,
            relatedForms,
            stageRoles,
            comments,
            formAutomation,
        } = await this._relationDataService.getFormVersionRelations({
            formId,
            versionId,
            formFieldRepo,
            stageRepo,
            stageTransitionRepo,
            stageAccessControlRepo,
            collectionRepo,
            collectionItemRepo,
            formLayoutRepo,
            formRelatedRepo,
            stageRoleRepo,
            subscriptionRepo,
            isAccount,
            commentRepo,
            generalAPRepo,
            manualEventRepo,
            includeAutomation,
            includeComment,
            includeLayouts,
            includeSubscription,
            includeRoleAccessControls,
            includeStageAccessControls,
            includeFormCollections,
            includeRelatedForms,
            includeStages,
            automationActionRepo,
            automationVersionRepo,
            includeFields,
            includeStageRoleAccessControls,
        });

        cloneForm = {
            ...cloneForm,
            stages: stages ?? [],
            fields: fields ?? [],
            stageTransitions: stageTransitions ?? [],
            stageAccessControls: stageAccessControls ?? [],
            formCollections: collections ?? [],
            formLayouts: layouts ?? [],
            stageRoles: stageRoles ?? [],
            comments: comments || [],
        };

        return {
            version: cloneForm,
            relatedForms,
            formAutomation,
        };
    }

    private async _getDeepRelatedForms({
        formId,
        formRelatedRepo,
        isTenancy = false,
    }: {
        formId: string;
        formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity>;
        isTenancy?: boolean;
    }) {
        const query = getRelatedFormQuery(this._claims.accountId || 'public');
        const formResults = await formRelatedRepo.query(query, [formId]);

        const distinctResults = _.uniqBy(formResults ?? [], (o: any) => o.id);

        const formEntities: FormEntity[] = distinctResults.map((raw) => {
            const {
                id,
                name,
                created_by,
                created_at,
                created_by_user,
                published_by,
                published_by_user,
                published_at,
                description,
                status,
                latest_version,
                active_version,
                active_version_id,
                latest_version_id,
            } = raw;
            let entity = isTenancy ? new FormTenancyEntity() : new FormEntity();

            entity.id = id;
            entity.name = name;
            entity.description = description;
            entity.createdBy = created_by;
            entity.createdByUser = created_by_user;
            entity.createdAt = created_at;
            entity.publishedBy = published_by;
            entity.publishedByUser = published_by_user;
            entity.publishedAt = published_at;
            entity.status = status;
            entity.latestVersion = latest_version;
            entity.activeVersion = active_version;
            entity.activeVersionId = active_version_id;
            entity.latestVersionId = latest_version_id;
            return entity;
        });

        return _.uniqBy(formEntities, 'id');
    }

    private async _verifyAssignedDataRegisters({
        formCollections,
        formFields,
        accountId,
        accountDataRegisterRepo,
        dataRegisterTransactionRepo,
        dataRegisterRepo,
        generalAPSettings,
    }: {
        accountId: string;
        formFields: FormFieldEntity[];
        formCollections: FormCollectionEntity[];
        accountDataRegisterRepo: Repository<AccountDataRegisterEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity>;
        dataRegisterRepo: Repository<DataRegisterEntity>;
        generalAPSettings: GeneralAutoPopulateSettingEntity[];
    }) {
        const result: {
            unassigned: DataRegisterEntity[];
            assigned: DataRegisterEntity[];
            formCollectionIds: string[];
            dataRegisterIds: string[];
            masterDataIds: string[];
            dataRegisterVersionIds: { registerId: string; versionId: string }[];
            dataRegisterTransactionIds: string[];
        } = {
            unassigned: [],
            assigned: [],
            formCollectionIds: [],
            dataRegisterIds: [],
            masterDataIds: [],
            dataRegisterTransactionIds: [],
            dataRegisterVersionIds: [],
        };

        const dataRegisterVersionIds: { registerId: string; versionId: string }[] = [];
        const dataRegisterIds = [...formFields.filter((f) => !!f.lookupTargetId).map((f) => f.lookupTargetId)];

        const drTransactionIds = formCollections.flatMap((f) => f.formCollectionItems.flatMap((i) => i.dataRegisterTransactionId));
        if (drTransactionIds.length) {
            const dataRegisterTransactions = await dataRegisterTransactionRepo.find({
                where: {
                    id: In(_.uniq(drTransactionIds)),
                },
            });

            dataRegisterIds.push(...dataRegisterTransactions.map((tran) => tran.dataRegisterId));
        }

        if (generalAPSettings.length) {
            dataRegisterIds.push(...generalAPSettings.map((s) => s.dataSourceId));
        }

        if (!dataRegisterIds.length) {
            return result;
        }

        const uniqDataRegisterIds = _.uniq(dataRegisterIds);
        const assignedAccountRegisters = await accountDataRegisterRepo.findBy({
            accountId,
            dataRegisterId: In(uniqDataRegisterIds),
        });

        const unassignedDataRegisterIds = uniqDataRegisterIds.filter(
            (id) => !assignedAccountRegisters.some((r) => r.dataRegisterId === id),
        );

        const assignedDataRegisterIds = assignedAccountRegisters.map((aar) => aar.dataRegisterId);

        const [unAssignedDataRegisters, assignedDataRegisters] = await Promise.all([
            unassignedDataRegisterIds.length
                ? dataRegisterRepo.findBy({
                      id: In(unassignedDataRegisterIds),
                  })
                : Promise.resolve([] as DataRegisterEntity[]),
            assignedDataRegisterIds.length
                ? dataRegisterRepo.findBy({
                      id: In(assignedDataRegisterIds),
                  })
                : Promise.resolve([] as DataRegisterEntity[]),
        ]);

        dataRegisterVersionIds.push(...unAssignedDataRegisters.map((dr) => ({ registerId: dr.id, versionId: dr.activeVersionId })));

        return {
            unassigned: unAssignedDataRegisters,
            assigned: assignedDataRegisters,
            dataRegisterIds: uniqDataRegisterIds,
            formCollectionIds: formCollections.map((f) => f.id),
            dataRegisterTransactionIds: _.uniq(drTransactionIds),
            dataRegisterVersionIds,
            masterDataIds: [],
        };
    }

    private async _verifyFormStageRoles({
        forms,
        stageRolesRepo,
    }: {
        stageRolesRepo: Repository<StageRoleEntity>;
        forms: { formId: string; formVersionId: string }[];
    }) {
        if (!forms.length) {
            return [];
        }
        const stageRoles = await stageRolesRepo.findBy({
            formVersionId: In(forms.map((f) => f.formVersionId)),
            roleName: Not(IsNull()),
        });

        const stageRolesNames = stageRoles
            .filter((sr) => sr.roleName)
            .map((sr) => {
                return {
                    formId: forms.find((f) => f.formVersionId === sr.formVersionId).formId,
                    formVersionId: sr.formVersionId,
                    roleName: sr.roleName,
                };
            });

        const uniqStageRolesNames = _.uniqBy(stageRolesNames, (item) => `${item.formId}-${item.roleName}`);
        return uniqStageRolesNames;
    }

    private async _verifyAssignedRelatedForms({
        formId,
        accountId,
        accountToFormsRepo,
        formRelatedRepo,
        formRepo,
    }: {
        formId: string;
        accountId: string;
        accountToFormsRepo: Repository<AccountToFormEntity>;
        formRelatedRepo: Repository<FormRelatedEntity>;
        formRepo: Repository<FormEntity>;
    }) {
        const result: { unassigned: FormEntity[]; formRelatedIds: string[]; assigned: FormEntity[] } = {
            unassigned: [],
            formRelatedIds: [],
            assigned: [],
        };

        const relatedFormEntities = await this._getDeepRelatedForms({
            formId,
            formRelatedRepo,
            isTenancy: false,
        });

        if (!relatedFormEntities.length) {
            return result;
        }

        const uniqRelatedFormIds = _.uniq(relatedFormEntities.map((i) => i.id)).filter((id) => id !== formId);
        if (!uniqRelatedFormIds.length) {
            return result;
        }

        const assignedAccountForms = await accountToFormsRepo.findBy({
            accountId,
            formId: In(uniqRelatedFormIds),
        });

        const unassignedFormIds = uniqRelatedFormIds.filter((id) => !assignedAccountForms.some((r) => r.formId === id));
        if (!unassignedFormIds.length) return result;
        const assignedFormIds = assignedAccountForms.map((aaf) => aaf.formId);

        const [unassigned, assigned] = await Promise.all([
            unassignedFormIds.length
                ? formRepo.findBy({
                      id: In(unassignedFormIds),
                  })
                : Promise.resolve([] as FormEntity[]),
            assignedFormIds.length
                ? formRepo.findBy({
                      id: In(assignedFormIds),
                  })
                : Promise.resolve([] as FormEntity[]),
        ]);

        return {
            unassigned,
            assigned,
            formRelatedIds: uniqRelatedFormIds,
        };
    }

    private async _verifyFormSubscription({
        accountId,
        formId,
        formRepo,
        accountSubsRepo,
        accountToFormRepo,
    }: {
        accountId: string;
        formId: string;
        formRepo: Repository<FormEntity>;
        accountSubsRepo: Repository<AccountSubscriptionEntity>;
        accountToFormRepo: Repository<AccountToFormEntity>;
    }) {
        const form = await formRepo.findOneBy({
            id: formId,
        });

        const accountSubCount = await accountSubsRepo.count({
            where: {
                accountId,
                subscriptionId: In(form.subscriptionIds),
            },
        });

        if (accountSubCount !== form.subscriptionIds.length) {
            throw new Error('account_form_subscription_not_existed');
        }

        const accountToForm = await accountToFormRepo.findOneBy({
            accountId,
            formId,
        });

        if (accountToForm) {
            throw new Error('form_was_published_to_account');
        }

        const formVersionId = form?.activeVersionId;
        if (!formVersionId) {
            throw new BadRequestException('active_version_not_found');
        }
        console.log('verify', form, formVersionId);
        return { form, formVersionId };
    }

    private async _verifyFormRelatedContents({
        formId,
        formVersionId,
        accountId,
        formFieldsRepo,
        formCollectionRepo,
        dataRegisterRepo,
        formRepo,
        dataRegisterTransactionRepo,
        accountDataRegisterRepo,
        formRelatedRepo,
        accountToFormsRepo,
        stageRolesRepo,
        generalAPSettingRepo,
    }: {
        formId: string;
        formVersionId: string;
        accountId: string;
        formFieldsRepo: Repository<FormFieldEntity>;
        formCollectionRepo: Repository<FormCollectionEntity>;
        dataRegisterRepo: Repository<DataRegisterEntity>;
        formRepo: Repository<FormEntity>;
        accountSubsRepo: Repository<AccountSubscriptionEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity>;
        accountDataRegisterRepo: Repository<AccountDataRegisterEntity>;
        formRelatedRepo: Repository<FormRelatedEntity>;
        stageRolesRepo: Repository<StageRoleEntity>;
        accountToFormsRepo: Repository<AccountToFormEntity>;
        generalAPSettingRepo: Repository<GeneralAutoPopulateSettingEntity>;
    }) {
        const {
            unassigned: unassignedForms,
            formRelatedIds,
            assigned: assignedForms,
        } = await this._verifyAssignedRelatedForms({
            formId,
            accountId,
            accountToFormsRepo,
            formRelatedRepo,
            formRepo,
        });

        const forms: { formId: string; formVersionId: string }[] = [
            {
                formId,
                formVersionId,
            },
        ];
        let relatedForms: FormEntity[] = [];
        if (formRelatedIds.length) {
            relatedForms = await formRepo.findBy({
                id: In(formRelatedIds),
            });
        }

        const relatedFormActiveVersionIds: string[] = relatedForms.map((f) => f.activeVersionId).filter(Boolean);
        forms.push(...relatedForms.map((f) => ({ formId: f.id, formVersionId: f.activeVersionId })));
        const filterFormVersionIds = [formVersionId, ...relatedFormActiveVersionIds];
        const [formFields, formCollections] = await Promise.all([
            formFieldsRepo.findBy({
                formVersionId: In(filterFormVersionIds),
            }),
            formCollectionRepo.find({
                where: { formVersionId: In(filterFormVersionIds) },
                relations: { formCollectionItems: true },
            }),
        ]);

        const generalAPSettings = await generalAPSettingRepo.findBy({
            builderVersionId: In(filterFormVersionIds),
        });

        const {
            unassigned: unassignedDrs,
            assigned: assignedDrs,
            formCollectionIds,
            dataRegisterIds,
            masterDataIds,
        } = await this._verifyAssignedDataRegisters({
            accountId,
            formFields,
            formCollections,
            accountDataRegisterRepo,
            dataRegisterTransactionRepo,
            dataRegisterRepo,
            generalAPSettings,
        });

        const formRoleNames = await this._verifyFormStageRoles({
            forms: [
                {
                    formId,
                    formVersionId,
                },
                ...relatedForms.map((f) => ({
                    formId: f.id,
                    formVersionId: f.activeVersionId,
                })),
            ],
            stageRolesRepo,
        });

        return {
            formVersionId,
            assignedDrs,
            assignedForms,
            unassignedDrs,
            unassignedForms,
            formCollectionIds,
            formRelatedIds,
            dataRegisterIds,
            masterDataIds,
            formRoleNames,
            forms,
        };
    }
    //#endregion PRIVATE
    private async _handleFieldUpdateACL({
        addedFields,
        deletedFields,
        updatedFields,
        accountId,
        formVersionId,
        dataSource,
        type,
    }: {
        addedFields: Array<FormFieldEntity | FormFieldTenancyEntity>;
        deletedFields: Array<FormFieldEntity | FormFieldTenancyEntity>;
        updatedFields: Array<FormFieldEntity | FormFieldTenancyEntity>;
        accountId: string;
        formVersionId: string;
        dataSource: DataSource | EntityManager;
        type?: AccessControlType;
    }) {
        const deleteFieldTask = this.handleFieldsDeleted({ formVersionId, accountId, fields: deletedFields, dataSource, type });
        const addFieldTask = this.handleFieldsAdded({ formVersionId, accountId, fields: addedFields, dataSource, type });
        const updateFieldTask = this.handleFieldsUpdated({ formVersionId, accountId, fields: updatedFields, dataSource, type });

        await deleteFieldTask;
        await addFieldTask;
        await updateFieldTask;
    }

    private async handleFieldsAdded(request: IFormFieldEvent) {
        try {
            if (!request.fields.length) {
                return;
            }
            this._logger.info(`Create default ACL for form: ${request.formVersionId}`);
            const isAccount = !!request.accountId;
            const dataSource = request.dataSource;

            return dataSource.transaction(async (manager) => {
                /**
                 * - Generate default stage access control (x)
                 * - Generate default stage role access control (x)
                 * - Identity id (x)
                 * - Decision acl (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRepo = (isAccount
                    ? manager.getRepository(StageTenancyEntity)
                    : manager.getRepository(StageEntity)) as unknown as Repository<StageEntity | StageTenancyEntity>;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAccessControlRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlTenancyEntity | StageRoleAccessControlEntity
                >;

                const formStages = await stageRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!formStages) {
                    return;
                }

                // Stage Access Control
                const newStageAcls: Array<StageAccessControlEntity | StageAccessControlTenancyEntity> = [];

                formStages.forEach((stage) => {
                    request.fields.forEach((field) => {
                        const id = v4();
                        newStageAcls.push(
                            stageAccessControlRepo.create({
                                id: id,
                                identityId: id,
                                formVersionId: request.formVersionId,
                                stageId: stage.id,
                                targetId: field.fieldId,
                                type: request?.type ?? AccessControlType.FIELD,
                                config: { visible: true, editable: true, required: false, access: AccessOption.Editable },
                            }),
                        );
                    });
                });

                await stageAccessControlRepo.save(newStageAcls);

                // Stage Roles
                const existingStageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                const newRoleAcls: Array<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity> = [];

                existingStageRoles.forEach((st) => {
                    request.fields.forEach((field) => {
                        const id = v4();
                        newRoleAcls.push(
                            stageRoleAccessControlRepo.create({
                                id: id,
                                identityId: id,
                                stageRoleId: st.id,
                                targetId: field.fieldId,
                                type: request?.type ?? AccessControlType.FIELD,
                                config: { visible: true, editable: true, required: false, access: AccessOption.Editable },
                            }),
                        );
                    });
                });

                // Stage Role Access Control
                await stageRoleAccessControlRepo.save(newRoleAcls);

                return true;
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    private async handleFieldsDeleted(request: IFormFieldEvent): Promise<void> {
        try {
            const isAccount = !!request.accountId;
            const dataSource = await request.dataSource;

            if (!request.fields.length) {
                return;
            }

            return dataSource.transaction(async (manager) => {
                /**
                 * - Delete stage access control (x)
                 * - Delete stage role access control (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAclRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity
                >;

                const fieldIds = _.uniq(_.compact(request.fields.map((f) => f.fieldId)));
                if (!fieldIds.length) {
                    return;
                }

                // Stage Access Control
                await stageAccessControlRepo.softDelete({
                    formVersionId: request.formVersionId,
                    targetId: In(fieldIds),
                    type: request?.type ?? AccessControlType.FIELD,
                });

                // Stage Role access control
                const stageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!stageRoles.length) {
                    return;
                }

                const stageRoleIds = _.uniq(_.compact(stageRoles.map((role) => role.id)));

                await stageRoleAclRepo.softDelete({
                    targetId: In(fieldIds),
                    stageRoleId: In(stageRoleIds),
                    type: request?.type ?? AccessControlType.FIELD,
                });

                this._logger.info(`Deleted ACL of form field: ${request.formVersionId}`);
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    private async handleFieldsUpdated(request: IFormFieldEvent): Promise<void> {
        try {
            this._logger.info(`Update ACL of form field: ${request.formVersionId}`);
            const isAccount = !!request.accountId;
            const dataSource = await request.dataSource;

            if (!request.fields.length) {
                return;
            }

            return dataSource.transaction(async (manager) => {
                /**
                 * - Delete stage access control (x)
                 * - Delete stage role access control (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAclRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity
                >;

                const fieldIds = _.uniq(
                    _.compact(request.fields.filter((f) => f.configuration?.override?.toString() !== 'true').map((f) => f.fieldId)),
                );
                if (!fieldIds.length) {
                    return;
                }

                const stageAccessControls = await stageAccessControlRepo.findBy({
                    formVersionId: request.formVersionId,
                    targetId: In(fieldIds),
                    type: request?.type ?? AccessControlType.FIELD,
                });

                stageAccessControls?.forEach((stageAccessControl) => {
                    stageAccessControl.config.override = false;
                });

                // Stage Access Control
                await stageAccessControlRepo.save(stageAccessControls);

                // Stage Role access control
                const stageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!stageRoles.length) {
                    return;
                }

                const stageRoleIds = _.uniq(_.compact(stageRoles.map((role) => role.id)));

                const stageRoleAccessControls = await stageRoleAclRepo.findBy({
                    targetId: In(fieldIds),
                    stageRoleId: In(stageRoleIds),
                    type: request?.type ?? AccessControlType.FIELD,
                });

                stageRoleAccessControls?.forEach((stageRoleAccessControl) => {
                    stageRoleAccessControl.config.override = false;
                });

                await stageRoleAclRepo.save(stageRoleAccessControls);

                this._logger.info(`Updated ACL of form field: ${request.formVersionId}`);
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    private async _handleRelatedUpdateACL({
        addedForms,
        deletedForms,
        accountId,
        formVersionId,
        dataSource,
    }: {
        dataSource: DataSource | EntityManager;
        addedForms: Array<FormRelatedEntity | FormRelatedTenancyEntity>;
        deletedForms: Array<FormRelatedEntity | FormRelatedTenancyEntity>;
        accountId: string;
        formVersionId: string;
    }) {
        const deleteRelatedTask = this.handleRelatedFormsDeleted({ formVersionId, accountId, forms: deletedForms, dataSource });
        const addRelatedTask = this.handleRelatedFormsAdded({ formVersionId, accountId, forms: addedForms, dataSource });

        await deleteRelatedTask;
        await addRelatedTask;
    }

    async handleRelatedFormsAdded(request: IFormRelatedEvent) {
        try {
            if (!request.forms.length) {
                return;
            }
            this._logger.info(`Create default ACL for form related: ${request.formVersionId}`);
            const isAccount = !!request.accountId;
            const dataSource = request.dataSource;

            return dataSource.transaction(async (manager) => {
                /**
                 * - Generate default stage access control (x)
                 * - Generate default stage role access control (x)
                 * - Identity id (x)
                 * - Decision acl (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRepo = (isAccount
                    ? manager.getRepository(StageTenancyEntity)
                    : manager.getRepository(StageEntity)) as unknown as Repository<StageEntity | StageTenancyEntity>;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAccessControlRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlTenancyEntity | StageRoleAccessControlEntity
                >;

                const formStages = await stageRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!formStages) {
                    return;
                }

                // Stage Access Control
                const newStageAcls: Array<StageAccessControlEntity | StageAccessControlTenancyEntity> = [];

                formStages.forEach((stage) => {
                    request.forms.forEach((form) => {
                        const id = v4();
                        newStageAcls.push(
                            stageAccessControlRepo.create({
                                id: id,
                                identityId: id,
                                formVersionId: request.formVersionId,
                                stageId: stage.id,
                                targetId: form.secondFormId,
                                type: AccessControlType.RELATION,
                                config: { visible: true, editable: true, access: AccessOption.Editable },
                            }),
                        );
                    });
                });

                await stageAccessControlRepo.save(newStageAcls);

                // Stage Roles
                const existingStageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                const newRoleAcls: Array<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity> = [];

                existingStageRoles.forEach((st) => {
                    request.forms.forEach((form) => {
                        const id = v4();
                        newRoleAcls.push(
                            stageRoleAccessControlRepo.create({
                                id: id,
                                identityId: id,
                                stageRoleId: st.id,
                                targetId: form.secondFormId,
                                type: AccessControlType.RELATION,
                                config: { visible: true, editable: true, access: AccessOption.Editable },
                            }),
                        );
                    });
                });

                // Stage Role Access Control
                await stageRoleAccessControlRepo.save(newRoleAcls);

                return true;
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    async handleRelatedFormsDeleted(request: IFormRelatedEvent): Promise<void> {
        try {
            this._logger.info(`Delete ACL of form related: ${request.formVersionId}`);
            const isAccount = !!request.accountId;
            const dataSource = request.dataSource;

            if (!request.forms.length) {
                return;
            }

            return dataSource.transaction(async (manager) => {
                /**
                 * - Delete stage access control (x)
                 * - Delete stage role access control (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAclRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity
                >;

                const fieldIds = _.uniq(_.compact(request.forms.map((f) => f.secondFormId)));
                if (!fieldIds.length) {
                    return;
                }

                // Stage Access Control
                await stageAccessControlRepo.softDelete({
                    formVersionId: request.formVersionId,
                    targetId: In(fieldIds),
                    type: AccessControlType.RELATION,
                });

                // Stage Role access control
                const stageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!stageRoles.length) {
                    return;
                }

                const stageRoleIds = _.uniq(_.compact(stageRoles.map((role) => role.id)));

                await stageRoleAclRepo.softDelete({
                    targetId: In(fieldIds),
                    stageRoleId: In(stageRoleIds),
                    type: AccessControlType.RELATION,
                });

                this._logger.info(`Deleted ACL of form related: ${request.formVersionId}`);
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    private _updateFormCollectionsWithNames(
        formCollections: FormCollectionEntity[],
        dataRegisterTransactions: (DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity)[],
        dataRegisterVersions: (DataRegisterVersionEntity | DataRegisterVersionTenancyEntity)[],
    ) {
        formCollections.forEach((collection) => {
            collection.formCollectionItems.forEach((item) => {
                const transaction = dataRegisterTransactions.find((tr) => tr.id === item.dataRegisterTransactionId);
                if (!transaction) return;

                const dataRegisterVersion = dataRegisterVersions.find((drv) => drv.id === transaction.dataRegisterVersionId);
                if (!dataRegisterVersion) return;

                const viewFieldIds = dataRegisterVersion.view.sort((a, b) => a.order - b.order).map((field) => field.fieldId);
                item.name = viewFieldIds.length ? this._generateItemName(viewFieldIds, dataRegisterVersion, transaction) : item.name;
            });
        });
    }

    private _extractDataRegisterTransactionIds(formCollections: FormCollectionEntity[]) {
        return formCollections.flatMap((collection) =>
            collection.formCollectionItems?.map((item) => item.dataRegisterTransactionId).filter(Boolean),
        );
    }

    private async _fetchDataRegisterVersions(
        dataRegisterVersionIds: string[],
        dataRegisterVersionRepo: Repository<DataRegisterVersionEntity | DataRegisterVersionTenancyEntity>,
    ) {
        return await dataRegisterVersionRepo
            .createQueryBuilder('version')
            .leftJoinAndSelect('version.fields', 'fields')
            .where({ id: In(dataRegisterVersionIds) })
            .andWhere(`fields.type = :type`, { type: FormFieldTypeEnum.Text })
            .select(['version.id', 'version.view', 'fields.fieldId', 'fields.id'])
            .getMany();
    }

    private _generateItemName(
        viewFieldIds: string[],
        dataRegisterVersion: DataRegisterVersionEntity | DataRegisterVersionTenancyEntity,
        transaction: DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity,
    ) {
        return viewFieldIds
            .map((viewFieldId) => {
                const textField = dataRegisterVersion.fields.find((field) => field.fieldId === viewFieldId);
                return textField ? transaction.transactionFields.find((field) => field.fieldId === textField.fieldId)?.fieldValue : null;
            })
            .filter(Boolean)
            .join('-');
    }

    private async _handleManualEventAdded(request: IFormManualActionEvent) {
        try {
            if (!request.actions.length) {
                return;
            }

            this._logger.info(`Create default ACL for form: ${request.formVersionId}`);
            const isAccount = !!request.accountId;
            const dataSource = request.dataSource;

            return dataSource.transaction(async (manager) => {
                /**
                 * - Generate default stage access control (x)
                 * - Generate default stage role access control (x)
                 * - Identity id (x)
                 * - Decision acl (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRepo = (isAccount
                    ? manager.getRepository(StageTenancyEntity)
                    : manager.getRepository(StageEntity)) as unknown as Repository<StageEntity | StageTenancyEntity>;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAccessControlRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlTenancyEntity | StageRoleAccessControlEntity
                >;

                const formStages = await stageRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!formStages) {
                    return;
                }

                // Stage Access Control
                const newStageAcls: Array<StageAccessControlEntity | StageAccessControlTenancyEntity> = [];

                formStages.forEach((stage) => {
                    request.actions.forEach((field) => {
                        const id = v4();
                        newStageAcls.push(
                            stageAccessControlRepo.create({
                                id: id,
                                identityId: id,
                                formVersionId: request.formVersionId,
                                stageId: stage.id,
                                targetId: field.identityId,
                                type: request?.type ?? AccessControlType.AUTOMATION,
                                config: { visible: true, editable: true, required: false, enable: true, access: AccessOption.Editable },
                            }),
                        );
                    });
                });

                await stageAccessControlRepo.save(newStageAcls);

                // Stage Roles
                const existingStageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                const newRoleAcls: Array<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity> = [];

                existingStageRoles.forEach((st) => {
                    request.actions.forEach((field) => {
                        const id = v4();
                        newRoleAcls.push(
                            stageRoleAccessControlRepo.create({
                                id: id,
                                identityId: id,
                                stageRoleId: st.id,
                                targetId: field.identityId,
                                type: request?.type ?? AccessControlType.AUTOMATION,
                                config: { visible: true, editable: true, required: false, enable: true, access: AccessOption.Editable },
                            }),
                        );
                    });
                });

                // Stage Role Access Control
                await stageRoleAccessControlRepo.save(newRoleAcls);

                return true;
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    private async _handleManualEventDeleted(request: IFormManualActionEvent) {
        try {
            const isAccount = !!request.accountId;
            const dataSource = request.dataSource;

            if (!request.actions.length) {
                return;
            }

            return dataSource.transaction(async (manager) => {
                /**
                 * - Delete stage access control (x)
                 * - Delete stage role access control (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAclRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity
                >;

                const actionIdentityIds = _.uniq(_.compact(request.actions.map((f) => f.identityId)));
                if (!actionIdentityIds.length) {
                    return;
                }

                // Stage Access Control
                await stageAccessControlRepo.softDelete({
                    formVersionId: request.formVersionId,
                    targetId: In(actionIdentityIds),
                    type: request?.type ?? AccessControlType.AUTOMATION,
                });

                // Stage Role access control
                const stageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!stageRoles.length) {
                    return;
                }

                const stageRoleIds = _.uniq(_.compact(stageRoles.map((role) => role.id)));

                await stageRoleAclRepo.softDelete({
                    targetId: In(actionIdentityIds),
                    stageRoleId: In(stageRoleIds),
                    type: request?.type ?? AccessControlType.FIELD,
                });

                this._logger.info(`Deleted ACL of form field: ${request.formVersionId}`);
            });
        } catch (error) {
            this._logger.error(error);
        }
    }

    private async _handleManualEventUpdated(request: IFormManualActionEvent) {
        try {
            this._logger.info(`Update ACL of form field: ${request.formVersionId}`);
            const isAccount = !!request.accountId;
            const dataSource = await request.dataSource;

            if (!request.actions.length) {
                return;
            }

            return dataSource.transaction(async (manager) => {
                /**
                 * - Delete stage access control (x)
                 * - Delete stage role access control (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAclRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity
                >;

                const fieldIdentityIds = _.uniq(
                    _.compact(request.actions.filter((f) => f.config?.override?.toString() !== 'true').map((f) => f.identityId)),
                );
                if (!fieldIdentityIds.length) {
                    return;
                }

                const stageAccessControls = await stageAccessControlRepo.findBy({
                    formVersionId: request.formVersionId,
                    targetId: In(fieldIdentityIds),
                    type: request?.type ?? AccessControlType.AUTOMATION,
                });

                stageAccessControls?.forEach((stageAccessControl) => {
                    stageAccessControl.config.override = false;
                });

                // Stage Access Control
                await stageAccessControlRepo.save(stageAccessControls);

                // Stage Role access control
                const stageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!stageRoles.length) {
                    return;
                }

                const stageRoleIds = _.uniq(_.compact(stageRoles.map((role) => role.id)));

                const stageRoleAccessControls = await stageRoleAclRepo.findBy({
                    targetId: In(fieldIdentityIds),
                    stageRoleId: In(stageRoleIds),
                    type: request?.type ?? AccessControlType.FIELD,
                });

                stageRoleAccessControls?.forEach((stageRoleAccessControl) => {
                    stageRoleAccessControl.config.override = false;
                });

                await stageRoleAclRepo.save(stageRoleAccessControls);

                this._logger.info(`Updated ACL of form field: ${request.formVersionId}`);
            });
        } catch (error) {
            this._logger.error(error);
        }
    }
}
