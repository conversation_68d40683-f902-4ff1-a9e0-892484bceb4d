import { RoleToFormViewTenancyEntity } from '@/database/src/entities/tenancy/role-to-form-view.tenancy.entity';
import { BadRequestException, Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { chunk, cloneDeep } from 'lodash';
import { DataSource, EntityManager, In, ObjectLiteral, Repository } from 'typeorm';
import { v4, v7 } from 'uuid';
import { CHUNK_SIZE, TYPEORM_SAVE_OPTIONS } from '../../../../constant';
import { CollectionTransactionEntity } from '../../../../database/src/entities/public/collection-transaction.public.entity';
import { FormCollectionAutomationMappingEntity } from '../../../../database/src/entities/public/form-collection-automation-mapping.public.entity';
import { FormCollectionItemEntity } from '../../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../../database/src/entities/public/form-collection.public.entity';
import { FormContextMappingEntity } from '../../../../database/src/entities/public/form-context-mapping.public.entity';
import { GeneralAutoPopulateExtraConfigEntity } from '../../../../database/src/entities/public/general-auto-populate-extra-config.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { RollupDependencyEntity } from '../../../../database/src/entities/public/roll-up-dependency.public.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { RollupDependencyTenancyEntity } from '../../../../database/src/entities/tenancy/roll-up-dependency.tenancy.entity';
import { AutomationContextType } from '../../../../database/src/shared/enums/automation.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { FormStatus } from '../../../../database/src/shared/enums/form-status.enum';
import { FormVersionStatus } from '../../../../database/src/shared/enums/form-version-status.enum';
import { TargetTypeEnum } from '../../../../database/src/shared/enums/roll-up-dependency.enum';
import { merge2Maps } from '../../../../shared/functions/map.util';
import { ApiConfigurationDataService } from '../../../api/services/data-services/api-configuration-data.service';
import { VersioningAutomationVersionPort } from '../../../automation/domain/ports/out/versioning-automation-version.port';
import { AutomationDto } from '../../../automation/dtos/respsone/automation.dto';
import { VerifyFormResultType } from '../../types/fom-data.request.type';
import {
    FormAutoPopulateSettingType,
    FormCollectionAdditionalFieldType,
    FormCollectionAutoPopulateContextType,
    FormCollectionContextMappingType,
    FormCollectionItemType,
    FormCollectionType,
    FormFieldType,
    FormLayoutType,
    FormLayoutZoneFieldType,
    FormLayoutZoneType,
    FormManualEventType,
    FormRelatedType,
    FormRoleViewsType,
    FormStageAccessControlType,
    FormStageDecisionType,
    FormStageRoleAccessControlType,
    FormStageRoleType,
    FormStageTransitionType,
    FormStageType,
    FormType,
    FormVersionContentRepositoriesType,
    FormVersionType,
    FormViewItemType,
    FormViewType,
} from '../../types/form-content.type';
import { RollupConfig } from '../../types/rollup-config.type';
import { FormCollectionListenerService } from '../listeners/form-collection.listener.service';
import { FormVersionMapping } from '../mapping/form-version.mapping';
import { FormCollectionDataService } from './form-collection.data.service';
import { FormContentService } from './form-content.service';
import { FormVersionLoggerService } from './form-version.logger.service';

@Injectable()
export class FormVersionDataService {
    constructor(
        private readonly _versionLogger: FormVersionLoggerService,
        private readonly _formContentService: FormContentService,
        private readonly _formVersionMapping: FormVersionMapping,
        private readonly _formCollectionDataService: FormCollectionDataService,
        private readonly _formCollectionListenerService: FormCollectionListenerService,
        private readonly _versioningPort: VersioningAutomationVersionPort,
        // private readonly _repositories: FormVersionContentRepositoriesType,
        private readonly _apiConfigurationDataService: ApiConfigurationDataService,
    ) {}

    public async verifyNewFormVersion({
        formId,
        connection,
        isAccount,
        userId,
        userByName,
    }: {
        formId: string;
        connection: DataSource | EntityManager;
        isAccount: boolean;
        userId: string;
        userByName: string;
    }): Promise<VerifyFormResultType> {
        const result = await connection.transaction<VerifyFormResultType>(async (manager) => {
            const repositories = this._formContentService.getFormContentRepositories({ isAccount, manager });
            const { formRepo, formVersionRepo } = repositories;
            const form = await formRepo?.findOneBy({
                id: formId,
            });

            if (!form) {
                throw new BadRequestException('form_not_exist');
            }

            const formVersion = await formVersionRepo?.findOneBy({
                id: form.latestVersionId,
            });

            if (formVersion.status === FormVersionStatus.Published) {
                const result = await this.createNewVersion({
                    form,
                    formVersion,
                    userId,
                    userByName,
                    isAccount,
                    repositories,
                });

                return result;
            }

            return { form, formVersion, isNewVersion: false };
        });
        return result;
    }

    public async createNewVersion({
        form,
        formVersion,
        userId,
        userByName,
        isAccount,
        repositories,
        status,
    }: {
        formVersion: FormVersionType;
        form: FormType;
        isAccount: boolean;
        userId: string;
        userByName: string;
        repositories: FormVersionContentRepositoriesType;
        status?: FormVersionStatus;
    }) {
        const { id, version, createdAt, ...restFormVersion } = formVersion;
        const isPublishedForm = status === FormVersionStatus.Published;

        const newId = isPublishedForm ? (version > 0 ? id : undefined) : undefined;
        const newVersion = isPublishedForm ? (version === 0 ? version + 1 : version) : version + 1;

        const savedFormVersion = await repositories.formVersionRepo.save({
            ...restFormVersion,
            id: newId,
            version: newVersion,
            status: status ?? FormVersionStatus.Draft,
            publishedBy: isPublishedForm ? userId : undefined,
            publishedByUser: isPublishedForm ? userByName : undefined,
            publishedAt: isPublishedForm ? dayjs().toDate() : undefined,
        });

        const savedForm = await repositories.formRepo.save({
            id: form.id,
            latestVersionId: savedFormVersion.id,
            latestVersion: savedFormVersion.version,
            status: isPublishedForm ? FormStatus.Published : FormStatus.Draft,
            activeVersionId: isPublishedForm ? savedFormVersion.id : undefined,
            activeVersion: isPublishedForm ? savedFormVersion.version : undefined,
            publishedBy: isPublishedForm ? userId : undefined,
            publishedByUser: isPublishedForm ? userByName : undefined,
            publishedAt: isPublishedForm ? dayjs().toDate() : undefined,
        });

        // Update new collection configuration
        await this._getCollections({
            isAccount,
            formVersionId: formVersion.id,
            collectionItemRepo: repositories.collectionItemRepo,
            collectionRepo: repositories.collectionRepo,
        });

        if (isPublishedForm && version > 0) {
            return { form: savedForm, formVersion: savedFormVersion, isNewVersion: false };
        }

        const mappingResult = await this.copyFormVersion({
            formId: form.id,
            previousVersion: formVersion,
            formVersion: savedFormVersion,
            userId,
            userByName,
            isAccount,
            repositories,
        });

        const formId = savedForm.id;

        const automations = await repositories.automationRepo.findBy({
            contextId: formId,
            contextType: In([AutomationContextType.FormTransaction.toString()]),
        });

        let formAutomationRuleIdMap = new Map<string, string>();
        let formAutomationActionIdMap = new Map<string, string>();
        let formAutomationVersionIdMap = new Map<string, string>();

        const savedAutomations: AutomationDto[] = [];
        for (const automation of automations) {
            const {
                automationActionIdMap,
                automationRuleIdMap,
                automationVersionIdMap,
                automation: savedAutomation,
            } = await this._versioningPort.versioning({
                id: automation.id,
                contextVersionId: savedFormVersion.id,
            });

            savedAutomations.push(savedAutomation);

            formAutomationRuleIdMap = merge2Maps(formAutomationRuleIdMap, automationRuleIdMap);

            formAutomationActionIdMap = merge2Maps(formAutomationActionIdMap, automationActionIdMap);

            formAutomationVersionIdMap = merge2Maps(formAutomationVersionIdMap, automationVersionIdMap);
        }

        // * Because manual events depend on the automation version, we need to version the manual events after the automation versioning
        const { formManualEvents, manualEventIdMap } = this.prepareManualEvents({
            formManualEvents: mappingResult.formManualEvents || [],
            formVersion: savedFormVersion,
            automations: savedAutomations,
        });
        await repositories.manualEventRepo.save(formManualEvents);

        //TODO: get api builder version
        const apiBuilder = await repositories.apiBuilderRepo.findOneBy({
            contextId: form.id,
        });

        let formApiVersionIdMap = new Map<string, string>();
        let formApiVersionEndpointIdMap = new Map<string, string>();
        let formApiVersionCommentIdMap = new Map<string, string>();

        if (apiBuilder) {
            const {
                endpointIdMap: apiVersionEndpointIdMap,
                apiVersionCommentIdMap: apiVersionCommentIdMap,
                apiVersion,
            } = await this._apiConfigurationDataService.versioning({
                id: apiBuilder.id,
                contextVersionId: savedFormVersion.id,
                apiRepo: repositories.apiBuilderRepo,
                apiVersionRepo: repositories.apiVersionRepo,
                apiVersionEndpointRepo: repositories.apiVersionEndpointRepo,
                apiVersionCommentRepo: repositories.apiVersionCommentRepo,
            });

            formApiVersionEndpointIdMap = apiVersionEndpointIdMap;
            formApiVersionCommentIdMap = apiVersionCommentIdMap;
            formApiVersionIdMap.set(apiBuilder.latestVersionId, apiVersion.id);
        }

        return {
            form: savedForm,
            formVersion: savedFormVersion,
            ...mappingResult,
            automationRuleIdMap: formAutomationRuleIdMap,
            automationActionIdMap: formAutomationActionIdMap,
            formAutomationVersionIdMap: formAutomationVersionIdMap,
            manualEventIdMap,
            isNewVersion: true,
            formApiVersionIdMap,
            formApiVersionEndpointIdMap,
            formApiVersionCommentIdMap,
        };
    }

    prepareRelations({ formRelations }: { formRelations: FormRelatedType[] }): { relations: FormRelatedType[] } {
        const newRelations = formRelations.map((formRelation) => {
            const { id, createdAt, createdBy, firstFormId, secondFormId, ...restRelation } = formRelation;

            return {
                ...restRelation,
                firstFormId,
                secondFormId,
                id,
            } satisfies FormRelatedType;
        });

        return {
            relations: newRelations,
        };
    }

    prepareStageDecisions({
        userId,
        stageIdMap,
        stageDecisions,
    }: {
        userId: string;
        stageIdMap: Map<string, string>;
        stageDecisions: FormStageDecisionType[];
    }): {
        stageDecisionIdMap: Map<string, string>;
        stageDecisions: FormStageDecisionType[];
    } {
        const stageDecisionIdMap = new Map<string, string>();

        const newStageDecisions = stageDecisions
            .map((stageDecision) => {
                const { id: oldId, createdAt, createdBy, config, stageId, identityId, ...restDecision } = stageDecision;
                if (config?.actions?.length) {
                    config?.actions.forEach((item) => {
                        item.targetId = this._formVersionMapping.mapStageDecisionTarget({
                            stageIdMap,
                            targetId: item.targetId,
                            decisionType: item.decisionType,
                        });
                    });
                }
                const newId = v4();
                stageDecisionIdMap.set(oldId, newId);
                if (stageIdMap.has(stageId)) {
                    return {
                        ...restDecision,
                        createdBy: userId,
                        config,
                        stageId: stageIdMap.get(stageId),
                        id: newId,
                        identityId: identityId ? identityId : oldId,
                    } satisfies FormStageDecisionType;
                } else {
                    this._versionLogger.warningLog(oldId, 'Mapping Stage Decision', [
                        {
                            label: 'Stage',
                            oldId: stageId,
                            newId: stageIdMap.get(stageId),
                        },
                    ]);
                }
            })
            .filter(Boolean);

        return {
            stageDecisions: newStageDecisions,
            stageDecisionIdMap,
        };
    }

    prepareStageTransitions(
        userId: string,
        formVersion: FormVersionType,
        stages: FormStageType[],
        stageTransitions: FormStageTransitionType[],
    ): {
        stageIdMap: Map<string, string>;
        stageTransitionIdMap: Map<string, string>;
        stages: FormStageType[];
        stageTransitions: FormStageTransitionType[];
    } {
        const stageIdMap = new Map<string, string>();
        const newStages = stages.map((stage) => {
            const { id, createdAt, createdBy, identityId, ...restStage } = stage;
            const newId = v4();
            stageIdMap.set(id, newId);
            return {
                ...restStage,
                formVersionId: formVersion.id,
                createdBy: userId,
                id: newId,
                identityId: identityId ? identityId : id,
            } satisfies FormStageType;
        });

        const stageTransitionIdMap = new Map<string, string>();
        const newStageTransitions = stageTransitions
            .map((stageTransition) => {
                const { id: oldId, createdAt, createdBy, sourceId, targetId, identityId, ...restTransition } = stageTransition;

                if (stageIdMap.has(targetId) && stageIdMap.has(sourceId)) {
                    const newId = v4();
                    stageTransitionIdMap.set(oldId, newId);

                    return {
                        ...restTransition,
                        createdBy: userId,
                        formVersionId: formVersion.id,
                        sourceId: stageIdMap.get(sourceId),
                        targetId: stageIdMap.get(targetId),
                        id: newId,
                        identityId: identityId ? identityId : oldId,
                    } satisfies FormStageTransitionType;
                } else {
                    this._versionLogger.warningLog(oldId, 'Mapping Stage Transition', [
                        {
                            oldId: sourceId,
                            newId: stageIdMap.get(sourceId),
                            label: 'Source',
                        },
                        {
                            oldId: targetId,
                            newId: stageIdMap.get(targetId),
                            label: 'Target',
                        },
                    ]);
                }
            })
            .filter(Boolean);

        return {
            stageIdMap,
            stageTransitionIdMap,
            stageTransitions: newStageTransitions,
            stages: newStages,
        };
    }

    async prepareCollections(params: {
        userId: string;
        userByName: string;
        formVersion: FormVersionType;
        collections: FormCollectionType[];
        collectionItems: FormCollectionItemType[];
        collectionContextMappings: FormCollectionContextMappingType[];
        collectionAdditionalFields: FormCollectionAdditionalFieldType[];
        collectionAutoPopulateContexts: FormCollectionAutoPopulateContextType[];
        repositories: FormVersionContentRepositoriesType;
    }): Promise<{
        collectionIdMap: Map<string, string>;
        collectionIdMapIdentityId: Map<string, string>;
        collectionItemIdMap: Map<string, string>;
        collectionItemIdMapIdentityId: Map<string, string>;
        collectionAdditionalFieldIdMap: Map<string, string>;
        collectionContextMappingIdMap: Map<string, string>;
        collectionItems: FormCollectionItemType[];
        collections: FormCollectionType[];
        collectionAdditionalFields: FormCollectionAdditionalFieldType[];
        collectionContextMappings: FormCollectionContextMappingType[];
        collectionAutoPopulateContexts: FormCollectionAutoPopulateContextType[];
        collectionTransactions: CollectionTransactionEntity[];
    }> {
        const {
            userId,
            userByName,
            formVersion,
            collections,
            collectionItems,
            collectionContextMappings,
            collectionAdditionalFields,
            collectionAutoPopulateContexts,
            repositories,
        } = params;

        const collectionIdMap = new Map<string, string>();
        const collectionIdMapIdentityId = new Map<string, string>();

        const dataRegisterIds = collections.map((collection) => collection.dataRegisterId);

        const dataRegisters = dataRegisterIds?.length
            ? await repositories.dataRegisterRepo.find({
                  where: {
                      id: In(dataRegisterIds),
                  },
                  select: ['id', 'activeVersionId'],
              })
            : [];

        const newCollections = collections.map((collection) => {
            const { id: oldId, createdAt, createdBy, formVersionId, identityId, ...restCollection } = collection;
            const newId = v4();
            collectionIdMap.set(oldId, newId);
            collectionIdMapIdentityId.set(oldId, identityId);

            const dataRegister = dataRegisters.find((drv) => drv.id === collection.dataRegisterId);

            return {
                ...restCollection,
                createdBy: userId,
                createdByUser: userByName,
                formVersionId: formVersion.id,
                id: newId,
                identityId: identityId ? identityId : oldId,
                dataRegisterVersionId: dataRegister?.activeVersionId ?? restCollection?.dataRegisterVersionId,
            } satisfies FormCollectionType;
        });

        const collectionItemIdMap = new Map<string, string>();
        const collectionItemIdMapIdentityId = new Map<string, string>();

        const newCollectionItems = collectionItems
            .map((collectionItem) => {
                const {
                    id: oldId,
                    createdAt,
                    createdBy,
                    createdByUser,
                    formCollectionId,
                    contextMappings,
                    identityId,
                    ...restCollectionItem
                } = collectionItem;

                const newId = v4();
                collectionItemIdMap.set(oldId, newId);
                collectionItemIdMapIdentityId.set(oldId, identityId);
                if (collectionIdMap.has(formCollectionId)) {
                    return {
                        ...restCollectionItem,
                        createdBy: userId,
                        createdByUser: userByName,
                        formCollectionId: collectionIdMap.get(formCollectionId),
                        id: newId,
                        identityId: identityId ? identityId : oldId,
                    } satisfies FormCollectionItemType;
                } else {
                    this._versionLogger.warningLog(oldId, 'Mapping collection item', [
                        {
                            oldId: formCollectionId,
                            newId: collectionIdMap.get(formCollectionId),
                            label: 'Collection',
                        },
                    ]);
                }
            })
            .filter(Boolean);

        //re-map item parentId
        newCollectionItems.forEach((item) => {
            if (item.parentId) {
                item.parentId = collectionItemIdMap.get(item.parentId);
            }
        });

        const dataRegisterTransactionIds = dataRegisters?.map((dataRegister) => dataRegister?.activeVersionId) ?? [];

        const dataRegisterAdditionalFields = dataRegisterTransactionIds?.length
            ? await repositories.dataRegisterAdditionalFieldRepo
                  .createQueryBuilder('dataRegisterAdditionalField')
                  .leftJoin('dataRegisterAdditionalField.transaction', 'transaction')
                  .where('transaction.dataRegisterVersionId In(:...dataRegisterVersionIds)', {
                      dataRegisterVersionIds: dataRegisterTransactionIds,
                  })
                  .getMany()
            : [];

        const collectionAdditionalFieldIdMap = new Map<string, string>();
        const newCollectionAdditionalFields = collectionAdditionalFields.map((item) => {
            const newId = v4();
            collectionAdditionalFieldIdMap.set(item.id, newId);

            const additionalField = dataRegisterAdditionalFields.find(
                (addF) => addF.fieldId === item.fieldId && addF.transactionId === item.transactionId,
            );

            if (!additionalField) {
                return {
                    ...item,
                    id: newId,
                    createdBy: userId,
                    createdByUser: userByName,
                    formVersionId: formVersion.id,
                } satisfies FormCollectionAdditionalFieldType;
            }

            collectionAdditionalFieldIdMap.set(item.id, additionalField.id);

            return {
                ...item,
                autoPopulateDataSourceId: additionalField.autoPopulateDataSourceId,
                autoPopulateTargetId: additionalField.autoPopulateTargetId,
                configuration: additionalField.configuration,
                autoPopulateDataSource: additionalField.autoPopulateDataSource,
                id: newId,
                createdBy: userId,
                createdByUser: userByName,
                formVersionId: formVersion.id,
            } satisfies FormCollectionAdditionalFieldType;
        });

        const collectionContextMappingIdMap = new Map<string, string>();
        const newCollectionContextMappings = collectionContextMappings.map((item) => {
            const newId = v4();
            collectionContextMappingIdMap.set(item.id, newId);

            return {
                ...item,
                id: newId,
                createdBy: userId,
                createdByUser: userByName,
                formCollectionItemId: collectionItemIdMap.get(item.formCollectionItemId),
            } satisfies FormCollectionContextMappingType;
        });

        const collectionAutoPopulateContextsIdMap = new Map<string, string>();
        const newCollectionAutoPopulateContexts = collectionAutoPopulateContexts.map((item) => {
            const newId = v4();
            collectionAutoPopulateContextsIdMap.set(item.id, newId);

            return {
                ...item,
                id: newId,
                createdBy: userId,
                createdByUser: userByName,
                additionalFieldId: collectionAdditionalFieldIdMap.get(item.additionalFieldId),
            } satisfies FormCollectionAutoPopulateContextType;
        });

        const collectionTransactions = await this._formCollectionDataService.handleCaptureFormCollectionTransaction({
            formCollectionItemIds: newCollectionItems?.map((item) => item.id) ?? [],
            formCollectionItemRepo: repositories.formCollectionItemRepo,
            dataRegisterFieldRepo: repositories.dataRegisterFieldRepo,
            dataRegisterTransactionRepo: repositories.dataRegisterTransactionRepo,
            dataRegisterTransactionFieldRepo: repositories.dataRegisterTransactionFieldRepo,
            formCollectionItemTransactionFieldRepo: repositories.collectionTransactionRepo,
            formCollectionItemEntities: newCollectionItems,
            ignoreSave: true,
            dataRegisterRepo: repositories.dataRegisterRepo,
        });

        return {
            collectionIdMapIdentityId,
            collectionIdMap,
            collectionItemIdMap,
            collectionItemIdMapIdentityId,
            collectionAdditionalFieldIdMap,
            collectionContextMappingIdMap,
            collections: newCollections,
            collectionItems: newCollectionItems,
            collectionAdditionalFields: newCollectionAdditionalFields,
            collectionContextMappings: newCollectionContextMappings,
            collectionAutoPopulateContexts: newCollectionAutoPopulateContexts,
            collectionTransactions: collectionTransactions,
        };
    }

    private prepareLayouts({
        userId,
        userByName,
        formVersion,
        formLayouts,
        layoutZones,
        layoutFields,
    }: {
        userId: string;
        userByName: string;
        formVersion: FormVersionType;
        formLayouts: FormLayoutType[];
        layoutZones: FormLayoutZoneType[];
        layoutFields: FormLayoutZoneFieldType[];
    }): {
        layoutIdMap: Map<string, string>;
        layoutZoneIdMap: Map<string, string>;
        layoutZoneFieldIdMap: Map<string, string>;
        formLayouts: FormLayoutType[];
        layoutZones: FormLayoutZoneType[];
        layoutFields: FormLayoutZoneFieldType[];
    } {
        const layoutIdMap = new Map<string, string>();
        const newLayouts = formLayouts.map((layout) => {
            const { id: oldId, createdAt, createdBy, createdByUser, identityId, ...restLayout } = layout;
            const newId = v4();
            layoutIdMap.set(oldId, newId);
            return {
                ...restLayout,
                createdBy: userId,
                createdByUser: userByName,
                formVersionId: formVersion.id,
                id: newId,
                identityId: identityId ? identityId : oldId,
            } satisfies FormLayoutType;
        });

        const layoutZoneIdMap = new Map<string, string>();
        const newLayoutZones = layoutZones.map((layoutZone) => {
            const { id: oldId, layoutId, createdAt, createdBy, config, ...restZone } = layoutZone;
            const newId = v4();
            layoutZoneIdMap.set(oldId, newId);

            const identityId = layoutZone.identityId ?? oldId;
            if (config.dataGrid?.i) {
                config.dataGrid.i = identityId;
            }

            return {
                ...restZone,
                layoutId: layoutIdMap.get(layoutId),
                id: newId,
                createdBy: userId,
                createdByUser: userByName,
                config,
                identityId,
            } satisfies FormLayoutZoneType;
        });

        const layoutZoneFieldIdMap = new Map<string, string>();
        const newLayoutZoneFields = layoutFields
            .map((layoutField) => {
                const { id: oldId, createdAt, createdBy, createdByUser, identityId, ...restField } = layoutField;
                const newId = v4();

                layoutZoneFieldIdMap.set(oldId, newId);
                if (!layoutZoneIdMap.has(layoutField.layoutZoneId)) {
                    return null;
                }

                return {
                    ...restField,
                    layoutZoneId: layoutZoneIdMap.get(layoutField.layoutZoneId),
                    layoutId: layoutIdMap.get(layoutField.layoutId),
                    createdBy: userId,
                    createdByUser: userByName,
                    identityId: identityId ? identityId : oldId,
                    id: newId,
                } satisfies FormLayoutZoneFieldType;
            })
            .filter(Boolean);

        return {
            layoutIdMap,
            layoutZoneIdMap,
            layoutZoneFieldIdMap,
            formLayouts: newLayouts,
            layoutFields: newLayoutZoneFields,
            layoutZones: newLayoutZones,
        };
    }

    private prepareACL({
        userId,
        userByName,
        formVersion,
        stageIdMap,
        stageAccessControls,
        layoutIdMap,
        stageRoles,
        stageRoleAccessControls,
    }: {
        userId: string;
        userByName: string;
        formVersion: FormVersionType;
        stageIdMap: Map<string, string>;
        stageAccessControls: FormStageAccessControlType[];
        layoutIdMap: Map<string, string>;
        stageRoles: FormStageRoleType[];
        stageRoleAccessControls: FormStageRoleAccessControlType[];
    }): {
        stageRoleIdMap: Map<string, string>;
        stageAccessControlIdMap: Map<string, string>;
        stageAccessControls: FormStageAccessControlType[];
        stageRoles: FormStageRoleType[];
        stageRoleACLs: FormStageRoleAccessControlType[];
        stageRoleAccessControlIdMap: Map<string, string>;
    } {
        const stageAccessControlIdMap = new Map<string, string>();
        const newACLs: FormStageAccessControlType[] = [];
        stageAccessControls.forEach((stageAccessControl) => {
            const { id: oldId, createdAt, createdBy, targetId, stageId, identityId, ...restAccessControl } = stageAccessControl;

            if (stageIdMap.has(stageId)) {
                const newId = v4();
                stageAccessControlIdMap.set(oldId, newId);

                const newStageACL = {
                    ...restAccessControl,
                    createdBy: userId,
                    createdByUser: userByName,
                    formVersionId: formVersion.id,
                    stageId: stageIdMap.get(stageId),
                    targetId,
                    id: newId,
                    identityId: identityId ? identityId : oldId,
                } satisfies FormStageAccessControlType;

                newACLs.push(newStageACL);
            } else {
                this._versionLogger.warningLog(oldId, 'Mapping access control', [
                    {
                        oldId: stageId,
                        newId: stageIdMap.get(stageId),
                        label: 'Stage Id',
                    },
                ]);
            }
        });

        const stageRoleIdMap = new Map<string, string>();
        const newStageRoles: FormStageRoleType[] = [];
        stageRoles.forEach((stageRole) => {
            const { id: oldId, createdAt, createdBy, createdByUser, layoutId, stageId, identityId, ...restStageRole } = stageRole;

            if (stageIdMap.has(stageId) && layoutIdMap.has(layoutId)) {
                const newId = v4();
                stageRoleIdMap.set(oldId, newId);
                const newStageRole = {
                    ...restStageRole,
                    formVersionId: formVersion.id,
                    createdBy: userId,
                    createdByUser: userByName,
                    layoutId: layoutIdMap.get(layoutId),
                    stageId: stageIdMap.get(stageId),
                    id: newId,
                    identityId: identityId ? identityId : oldId,
                } satisfies FormStageRoleType;

                newStageRoles.push(newStageRole);
            } else {
                this._versionLogger.warningLog(oldId, 'Mapping stage role', [
                    {
                        oldId: stageId,
                        newId: stageIdMap.get(stageId),
                        label: 'Stage Id',
                    },
                    {
                        oldId: layoutId,
                        newId: layoutIdMap.get(layoutId),
                        label: 'Layout Id',
                    },
                ]);
            }
        });

        const stageRoleAccessControlIdMap = new Map<string, string>();
        const newStageRoleACLs: FormStageRoleAccessControlType[] = [];
        stageRoleAccessControls.forEach((stageRoleAccessControl) => {
            const {
                id: oldId,
                createdAt,
                createdBy,
                createdByUser,
                targetId,
                stageRoleId,
                identityId,
                config,
                ...restAccessControl
            } = stageRoleAccessControl;

            if (stageRoleIdMap.has(stageRoleId)) {
                const newId = v4();
                stageRoleAccessControlIdMap.set(oldId, newId);
                const newStageRoleACL = {
                    ...restAccessControl,
                    createdBy: userId,
                    createdByUser: userByName,
                    targetId,
                    stageRoleId: stageRoleIdMap.get(stageRoleId),
                    id: newId,
                    identityId: identityId ? identityId : oldId,
                    config,
                } satisfies FormStageRoleAccessControlType;

                newStageRoleACLs.push(newStageRoleACL);
            } else {
                this._versionLogger.warningLog(oldId, 'Mapping stage role access control', [
                    {
                        oldId: stageRoleId,
                        newId: stageRoleIdMap.get(stageRoleId),
                        label: 'Stage Role Id',
                    },
                ]);
            }
        });

        return {
            stageRoleIdMap,
            stageRoleAccessControlIdMap,
            stageAccessControlIdMap,
            stageRoles: newStageRoles,
            stageRoleACLs: newStageRoleACLs,
            stageAccessControls: newACLs,
        };
    }

    private prepareViews({
        isAccount,
        userId,
        userByName,
        formVersion,
        formViews,
        roleViews,
        formViewItems,
    }: {
        isAccount: boolean;
        userId: string;
        userByName: string;
        formVersion: FormVersionType;
        formViews: FormViewType[];
        roleViews: RoleToFormViewTenancyEntity[];
        formViewItems: FormViewItemType[];
    }): {
        viewIdMap: Map<string, string>;
        viewItemIdMap: Map<string, string>;
        formViews: FormViewType[];
        roleViews: RoleToFormViewTenancyEntity[];
        formViewItems: FormViewItemType[];
    } {
        const viewIdMap = new Map<string, string>();
        const viewItemIdMap = new Map<string, string>();

        const newFormViewItems: FormViewItemType[] = [];
        const newFormViews = formViews.map((view) => {
            const { id: oldId, createdAt, createdBy, identityId, ...restView } = view;
            const newId = v4();
            viewIdMap.set(oldId, newId);

            const viewItems = formViewItems.filter((item) => item.formViewId === oldId);

            const newViewItems = viewItems.map((vi) => {
                const { id: oldItemId, createdAt, createdBy, identityId, ...restView } = vi;
                const newItemId = v4();
                viewItemIdMap.set(oldItemId, newItemId);
                return {
                    ...restView,
                    id: newItemId,
                    formViewId: newId,
                    formVersionId: formVersion.id,
                    identityId: identityId ? identityId : oldItemId,
                } satisfies FormViewItemType;
            });

            newFormViewItems.push(...newViewItems);

            return {
                ...restView,
                createdBy: userId,
                createdByUser: userByName,
                id: newId,
                formVersionId: formVersion.id,
                identityId: identityId ? identityId : oldId,
            } satisfies FormViewType;
        });

        const newFormRoleViews: FormRoleViewsType[] = [];
        if (isAccount) {
            //map role to form views
            roleViews.forEach((roleView) => {
                const { id, formViewId, ...restRoleView } = roleView;

                if (viewIdMap.has(formViewId)) {
                    newFormRoleViews.push({
                        ...restRoleView,
                        formViewId: viewIdMap.get(formViewId),
                    } satisfies FormRoleViewsType);
                } else {
                    this._versionLogger.warningLog(id, 'Mapping role view', [
                        {
                            oldId: formViewId,
                            newId: viewIdMap.get(formViewId),
                            label: 'View Id',
                        },
                    ]);
                }
            });
        }

        return {
            viewIdMap,
            viewItemIdMap,
            formViews: newFormViews,
            roleViews: newFormRoleViews,
            formViewItems: newFormViewItems,
        };
    }

    prepareFields({
        userId,
        userByName,
        formVersion,
        formFields,
        collectionIdMap,
        collectionItemIdMap,
    }: {
        userId: string;
        userByName: string;
        formVersion: FormVersionType;
        formFields: FormFieldType[];
        collectionIdMap: Map<string, string>;
        collectionItemIdMap: Map<string, string>;
    }): {
        fieldIdMap: Map<string, string>;
        formFields: FormFieldType[];
    } {
        const fieldIdMap = new Map<string, string>();
        const newVersionFields = formFields.map((field) => {
            const { id: oldId, createdAt, createdBy, formVersionId, rollupDependencies, configuration, ...restField } = field;
            const newId = v4();
            fieldIdMap.set(oldId, newId);
            if (rollupDependencies?.length) {
                rollupDependencies.forEach((item: RollupDependencyEntity | RollupDependencyTenancyEntity) => {
                    item.formVersionId = formVersion.id;
                    item.formFieldId = newId;
                    item.collectionId = collectionIdMap.get(item.collectionId) ?? item.collectionId;
                    item.collectionItemId = collectionItemIdMap.get(item.collectionItemId) ?? item.collectionItemId;
                    delete item.id;
                });
            }

            if ((field.type as unknown as FormFieldTypeEnum) === FormFieldTypeEnum.Rollup) {
                if (configuration.rollup?.rollupDependencies?.length) {
                    (configuration.rollup as RollupConfig).rollupDependencies.forEach((item) => {
                        const newCollectionId = collectionIdMap.get(item?.collectionFetchingId) ?? item?.collectionFetchingId;

                        item.collectionFetchingId = newCollectionId;
                        // map first and second context
                        if (item?.targetType) {
                            switch (item.targetType) {
                                case TargetTypeEnum.FORM:
                                    item.contextId = formVersion.id;
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
                }
            }

            return {
                ...restField,
                configuration,
                rollupDependencies,
                formVersionId: formVersion.id,
                createdBy: userId,
                createdByUser: userByName,
                id: newId,
            } satisfies FormFieldType;
        });

        newVersionFields.forEach((field) => {
            if (field.configuration?.contextValueField && fieldIdMap.has(field.configuration?.contextValueField)) {
                field.configuration.contextValueField = fieldIdMap.get(field.configuration.contextValueField);
            }
        });

        return {
            fieldIdMap,
            formFields: newVersionFields,
        };
    }

    prepareAutopopulate({
        formVersion,
        formAutoPopulateSettings,
    }: {
        formVersion: FormVersionType;
        formAutoPopulateSettings: FormAutoPopulateSettingType[];
    }): {
        formAutoPopulateSettings: FormAutoPopulateSettingType[];
    } {
        const newFormAutoPopulateSettings = formAutoPopulateSettings.map((setting) => {
            const { id, createdAt, ...restSetting } = setting;
            restSetting.originFormVersionId = formVersion.id;
            return restSetting satisfies FormAutoPopulateSettingType;
        });

        return {
            formAutoPopulateSettings: newFormAutoPopulateSettings,
        };
    }

    prepareContextMapping({
        formVersion,
        contextMappings,
        automationMappings,
    }: {
        formVersion: FormVersionType;
        contextMappings: FormContextMappingEntity[];
        automationMappings: FormCollectionAutomationMappingEntity[];
    }): {
        contextMappings: FormContextMappingEntity[];
        automationMappings: FormCollectionAutomationMappingEntity[];
    } {
        const newContextMappings = contextMappings.map((setting) => {
            const { id, createdAt, ...restSetting } = setting;
            restSetting.formVersionId = formVersion.id;
            return restSetting satisfies FormContextMappingEntity;
        });

        const newAutomationMappings = automationMappings.map((setting) => {
            const { id, createdAt, ...restSetting } = setting;
            restSetting.formVersionId = formVersion.id;
            return restSetting satisfies FormCollectionAutomationMappingEntity;
        });

        return {
            contextMappings: newContextMappings,
            automationMappings: newAutomationMappings,
        };
    }

    prepareManualEvents({
        formVersion,
        formManualEvents,
        automations,
    }: {
        formVersion: FormVersionType;
        formManualEvents: FormManualEventType[];
        automations: AutomationDto[];
    }): {
        formManualEvents: FormManualEventType[];
        manualEventIdMap: Map<string, string>;
    } {
        const manualEventIdMap = new Map<string, string>();

        const automationIds = automations.map((a) => a.id);
        const filteredManualEvents = formManualEvents.filter((event) => automationIds.includes(event.automationId));
        const newManualEvents = filteredManualEvents?.map((setting) => {
            const { id: oldId, createdAt, ...restSetting } = setting;
            const newId = v7();
            manualEventIdMap.set(oldId, newId);
            return {
                ...restSetting,
                formVersionId: formVersion.id,
                automationVersionId: (automations || []).find((automation) => automation.id === restSetting.automationId)?.latestVersionId,
                id: newId,
            } satisfies FormManualEventType;
        });

        return {
            formManualEvents: newManualEvents,
            manualEventIdMap,
        };
    }

    loadFormContent({
        formId,
        isAccount,
        formVersion,
        previousVersion,
        repositories,
    }: {
        formId: string;
        isAccount: boolean;
        previousVersion: FormVersionType;
        formVersion: FormVersionType;
        repositories: FormVersionContentRepositoriesType;
    }) {
        return this._formContentService.getFormVersionContent({
            formVersionId: previousVersion.id,
            formId,
            isAccount,
            ...repositories,
        });
    }

    public async copyFormVersion({
        formId,
        userId,
        userByName,
        isAccount,
        formVersion,
        previousVersion,
        repositories,
    }: {
        formId: string;
        userId: string;
        userByName: string;
        isAccount: boolean;
        previousVersion: FormVersionType;
        formVersion: FormVersionType;
        repositories: FormVersionContentRepositoriesType;
    }): Promise<any> {
        // #region GET CONTENTS
        const {
            formFields,
            collections,
            collectionItems,
            collectionAdditionalFields,
            collectionContextMappings,
            collectionAutoPopulateContexts,
            formRelations,
            stages,
            stageTransitions,
            stageDecisions,
            formLayouts,
            layoutZones,
            layoutFields,
            stageRoles,
            stageAccessControls,
            stageRoleAccessControls,
            formViews,
            roleViews,
            formAutoPopulateSettings,
            generalAutoPopulateSettings,
            generalAutoPopulateExtraConfigSettings,
            contextMappings,
            automationMappings,
            formViewItems,
            formManualEvents,
        } = await this.loadFormContent({
            formId,
            isAccount,
            formVersion,
            previousVersion,
            repositories,
        });

        // #endregion GET CONTENTS

        // #region MAPPING CONTENTS

        //3.Collection
        const {
            collectionItemIdMap,
            collectionIdMap,
            collectionIdMapIdentityId,
            collectionItemIdMapIdentityId,
            collectionContextMappingIdMap,
            collectionAdditionalFieldIdMap,
            collections: newCollections,
            collectionItems: newCollectionItems,
            collectionAdditionalFields: newCollectionAdditionalFields,
            collectionContextMappings: newCollectionContextMappings,
            collectionAutoPopulateContexts: newCollectionAutoPopulateContexts,
            collectionTransactions: collectionTransactions,
        } = await this.prepareCollections({
            userId,
            userByName,
            formVersion,
            collections,
            collectionItems,
            collectionAdditionalFields,
            collectionContextMappings,
            collectionAutoPopulateContexts,
            repositories,
        });

        //4.Relation
        const { relations: newRelations } = this.prepareRelations({ formRelations });

        //5.Workflow
        const {
            stageIdMap,
            stageTransitionIdMap,
            stageTransitions: newStageTransitions,
            stages: newStages,
        } = this.prepareStageTransitions(userId, formVersion, stages, stageTransitions);

        const { stageDecisionIdMap, stageDecisions: newStageDecisions } = this.prepareStageDecisions({
            userId,
            stageIdMap,
            stageDecisions,
        });

        //6.Layouts
        const {
            layoutIdMap,
            layoutZoneIdMap,
            layoutZoneFieldIdMap,
            layoutFields: newLayoutZoneFields,
            layoutZones: newLayoutZones,
            formLayouts: newLayouts,
        } = this.prepareLayouts({ userId, userByName, formVersion, formLayouts, layoutZones, layoutFields });

        //7.Access Control
        const {
            stageRoleAccessControlIdMap,
            stageAccessControlIdMap,
            stageRoleIdMap,
            stageAccessControls: newACLs,
            stageRoles: newStageRoles,
            stageRoleACLs: newStageRoleACLs,
        } = this.prepareACL({
            userId,
            userByName,
            formVersion,
            stageIdMap,
            stageAccessControls,
            layoutIdMap,
            stageRoles,
            stageRoleAccessControls,
        });

        //8.View
        const {
            roleViews: newFormRoleViews,
            formViews: newFormViews,
            formViewItems: newFormViewItems,
            viewIdMap,
            viewItemIdMap,
        } = this.prepareViews({ isAccount, userId, userByName, formVersion, formViews, formViewItems, roleViews });

        //9.General AutoPopulate
        const { newGeneralAutoPopulateSettings, newGeneralAutoPopulateExtraConfigSettings } = this.prepareGeneralSettings({
            userId,
            userByName,
            formVersion,
            generalAutoPopulateExtraConfigSettings,
            generalAutoPopulateSettings,
        });

        //1,2.Fields/Widgets
        const { fieldIdMap, formFields: newVersionFields } = this.prepareFields({
            userId,
            userByName,
            formVersion,
            formFields,
            collectionIdMap,
            collectionItemIdMap,
        });

        const { formAutoPopulateSettings: newFormAutoPopulateSettings } = this.prepareAutopopulate({
            formVersion,
            formAutoPopulateSettings,
        });

        // Form context mapping & Form collection automation mapping
        const { contextMappings: newContextMappings, automationMappings: newAutomationMappings } = this.prepareContextMapping({
            contextMappings,
            automationMappings,
            formVersion,
        });

        //#region SAVE CONTENTS
        await Promise.all([
            repositories.fieldRepo.save(newVersionFields, TYPEORM_SAVE_OPTIONS),
            repositories.collectionRepo.insert(newCollections),
            repositories.formRelatedRepo.save(newRelations),
            repositories.stageRepo.insert(newStages),
            repositories.formLayoutRepo.insert(newLayouts),
            repositories.formViewRepo.insert(newFormViews),
            repositories.autoPopulateSettingRepo.insert(newFormAutoPopulateSettings),
        ]);

        // collectionItems: depend collections
        await Promise.all([
            repositories.collectionItemRepo.insert(newCollectionItems),
            repositories.stageTransitionRepo.insert(newStageTransitions),
            repositories.stageDecisionRepo.insert(newStageDecisions),
            repositories.formLayoutZoneRepo.insert(newLayoutZones),
            repositories.stageRoleRepo.insert(newStageRoles),
            repositories.formViewItemRepo.insert(newFormViewItems),
            isAccount ? repositories.roleViewsRepo.insert(newFormRoleViews) : Promise.resolve([]),
        ]);

        // collectionAdditionalFields: depend collectionItems
        // collectionContextMappings: depend collectionItems

        await Promise.all([
            repositories.collectionAdditionalFieldRepo.insert(newCollectionAdditionalFields),
            repositories.collectionContextMappingRepo.insert(newCollectionContextMappings),
            repositories.collectionTransactionRepo.insert(collectionTransactions),
            // repositories.formLayoutZoneFieldRepo.insert(newLayoutZoneFields),
            Promise.all(
                this._chunkInsert({
                    items: newLayoutZoneFields,
                    repository: repositories.formLayoutZoneFieldRepo,
                }),
            ),
            // repositories.stageAccessControlRepo.insert(newACLs),
            // repositories.stageRoleAccessControlRepo.insert(newStageRoleACLs),
            // repositories.generalAutoPopulateRepo.insert(newGeneralAutoPopulateSettings),
            Promise.all(
                this._chunkInsert({
                    items: newGeneralAutoPopulateSettings,
                    repository: repositories.generalAutoPopulateRepo,
                }),
            ),
        ]);

        // Chunk insert stage access control & stage role access control
        await Promise.all(chunk(newACLs, CHUNK_SIZE).map((items) => repositories.stageAccessControlRepo.insert(items)));
        await Promise.all(chunk(newStageRoleACLs, CHUNK_SIZE).map((items) => repositories.stageRoleAccessControlRepo.insert(items)));

        // collectionAutoPopulateContexts: depended on collectionAdditionalFields
        await Promise.all([
            // repositories.collectionAutoPopulateContextRepo.save(newCollectionAutoPopulateContexts),
            repositories.contextMappingsRepo.insert(newContextMappings),
            repositories.automationMappingsRepo.insert(newAutomationMappings),
            // repositories.generalAutoPopulateExtraConfigRepo.insert(newGeneralAutoPopulateExtraConfigSettings),
            Promise.all(
                this._chunkInsert({
                    items: newGeneralAutoPopulateExtraConfigSettings,
                    repository: repositories.generalAutoPopulateExtraConfigRepo,
                }),
            ),
        ]);
        //#endregion SAVE CONTENTS

        return {
            layoutIdMap,
            layoutZoneIdMap,
            fieldIdMap,
            collectionIdMap,
            collectionItemIdMap,
            stageIdMap,
            stageDecisionIdMap,
            stageTransitionIdMap,
            stageRoleIdMap,
            stageRoleAccessControlIdMap,
            stageAccessControlIdMap,
            viewIdMap,
            viewItemIdMap,
            layoutZoneFieldIdMap,
            collectionIdMapIdentityId,
            collectionItemIdMapIdentityId,
            collectionContextMappingIdMap,
            collectionAdditionalFieldIdMap,
            formManualEvents,
        };
    }

    private prepareGeneralSettings({
        userId,
        userByName,
        formVersion,
        generalAutoPopulateSettings,
        generalAutoPopulateExtraConfigSettings,
    }: {
        userId: string;
        userByName: string;
        formVersion: FormVersionType;
        generalAutoPopulateSettings: GeneralAutoPopulateSettingEntity[];
        generalAutoPopulateExtraConfigSettings: GeneralAutoPopulateExtraConfigEntity[];
    }): {
        newGeneralAutoPopulateSettings: GeneralAutoPopulateSettingEntity[];
        newGeneralAutoPopulateExtraConfigSettings: GeneralAutoPopulateExtraConfigEntity[];
    } {
        const idMap = new Map<string, string>();
        const newGeneralAutoPopulateSettings = (generalAutoPopulateSettings || []).map((item) => {
            const { id, ...restItem } = item;
            const newId = v7();
            idMap.set(id, newId);

            const newItem = cloneDeep(restItem);
            newItem.builderVersionId = formVersion.id;
            newItem.createdBy = userId;
            newItem.createdByUser = userByName;
            newItem.updatedBy = userId;
            newItem.updatedByUser = userByName;
            return { ...newItem, id: newId };
        });

        const newGeneralAutoPopulateExtraConfigSettings = (generalAutoPopulateExtraConfigSettings || [])
            .filter((extraSetting) =>
                (generalAutoPopulateSettings || []).some((gap) => gap.id === extraSetting.generalAutoPopulateSettingId),
            )
            .map((item) => {
                const { id, ...restItem } = item;
                const newItem = cloneDeep(restItem);
                newItem.builderVersionId = formVersion.id;
                newItem.createdBy = userId;
                newItem.createdByUser = userByName;
                newItem.updatedBy = userId;
                newItem.updatedByUser = userByName;
                return { ...newItem, generalAutoPopulateSettingId: idMap.get(restItem.generalAutoPopulateSettingId) };
            });

        return {
            newGeneralAutoPopulateSettings,
            newGeneralAutoPopulateExtraConfigSettings,
        };
    }

    private async _getCollections({
        formVersionId,
        collectionRepo,
        collectionItemRepo,
        isAccount,
    }: {
        formVersionId: string;
        collectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        collectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;
        isAccount: boolean;
    }) {
        const formCollections = await collectionRepo.findBy({
            formVersionId: formVersionId,
        });

        if (!formCollections?.length) {
            return;
        }

        const collectionIds: string[] = formCollections.map((collection) => collection.id);
        const formCollectionItems = await collectionItemRepo.findBy({
            formCollectionId: In(collectionIds),
        });

        const newFormCollections = this._formCollectionDataService.groupItems(formCollections, formCollectionItems);

        await this._formCollectionListenerService.handleUpdateNewCollection({
            formCollections: newFormCollections,
            formVersionId,
            isAccount,
        });
    }

    private _chunkInsert = <T extends ObjectLiteral>({ items, repository }: { items: T[]; repository: Repository<T> }) => {
        return chunk(items, CHUNK_SIZE).map((items) => repository.insert(items));
    };
}
