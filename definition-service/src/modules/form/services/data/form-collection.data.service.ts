import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as _ from 'lodash';
import { DataSource, In, IsNull, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { ClaimService, LoggerService, QueryBuilderService, USER_CLAIMS } from '../../../../common/src';
import { OrderOptionDto } from '../../../../common/src/modules/shared/dtos/order-option.dto';
import { OrderType } from '../../../../common/src/modules/shared/enums/order.enum';
import { FormCollectionCacheService } from '../../../../common/src/modules/shared/services/cache/form-collection.cache.service';
import { TYPEORM_SAVE_OPTIONS } from '../../../../constant';
import { CollectionLayoutZoneFieldEntity } from '../../../../database/src/entities/public/collection-layout-zone-field.public.entity';
import { CollectionLayoutZoneEntity } from '../../../../database/src/entities/public/collection-layout-zone.public.entity';
import { CollectionLayoutEntity } from '../../../../database/src/entities/public/collection-layout.public.entity';
import { CollectionTransactionEntity } from '../../../../database/src/entities/public/collection-transaction.public.entity';
import { DataRegisterAdditionalFieldEntity } from '../../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { DataRegisterFieldEntity } from '../../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../../database/src/entities/public/data-registers.public.entity';
import { FormCollectionAdditionalFieldEntity } from '../../../../database/src/entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionAutomationMappingEntity } from '../../../../database/src/entities/public/form-collection-automation-mapping.public.entity';
import { FormCollectionContextMappingEntity } from '../../../../database/src/entities/public/form-collection-context-mapping.public.entity';
import { FormCollectionItemEntity } from '../../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../../database/src/entities/public/form-collection.public.entity';
import { FormContextMappingEntity } from '../../../../database/src/entities/public/form-context-mapping.public.entity';
import { FormVersionEntity } from '../../../../database/src/entities/public/form-version.public.entity';
import { GeneralAutoPopulateExtraConfigEntity } from '../../../../database/src/entities/public/general-auto-populate-extra-config.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { StageAccessControlEntity } from '../../../../database/src/entities/public/stage-access-controls.public.entity';
import { StageRoleAccessControlEntity } from '../../../../database/src/entities/public/stage-role-access-controls.public.entity';
import { StageRoleEntity } from '../../../../database/src/entities/public/stage-role.public.entity';
import { StageEntity } from '../../../../database/src/entities/public/stage.public.entity';
import { CollectionLayoutZoneFieldTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout-zone-field.tenancy.entity';
import { CollectionLayoutZoneTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout-zone.tenancy.entity';
import { CollectionLayoutTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout.tenancy.entity';
import { CollectionTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionAutomationMappingTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-automation-mapping.tenancy.entity';
import { FormCollectionContextMappingTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-context-mapping.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormContextMappingTenancyEntity } from '../../../../database/src/entities/tenancy/form-context-mapping.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { AccessControlType, AccessOption } from '../../../../database/src/shared/enums/access-control-type.enum';
import { AdditionalFieldType } from '../../../../database/src/shared/enums/additional-field-type.enum';
import { AutoPopulateBuilderTypeEnum } from '../../../../database/src/shared/enums/ap-builder-type.enum';
import { DataRegisterTypeEnum } from '../../../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { FormVersionStatus } from '../../../../database/src/shared/enums/form-version-status.enum';
import { MultipleFilterRequestDto } from '../../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../../shared/common/dto/pagination-response.dto';
import { IFormCollectionEvent, IUpdateFormCollectionACL } from '../../dtos/events/form-collection.event';
import { FormCollectionDto } from '../../dtos/form-collection.dto';
import { CollectionAutomationMappingRequest } from '../../dtos/requests/collection-automation-mapping.request';
import { FormCollectionItemRequest, FormCollectionRequest, FormCollectionUpdateRequest } from '../../dtos/requests/form-collection.request';
import { FormCollectionContextDataService } from './form-collection-context.data.service';

@Injectable()
export class FormCollectionDataService {
    constructor(
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _loggerService: LoggerService,
        private readonly _queryBuilder: QueryBuilderService,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _dataSourceUtil: DataSourceService,

        private readonly _collectionContextDataService: FormCollectionContextDataService,
        private readonly _formCollectionCacheService: FormCollectionCacheService,
        private readonly _eventEmitter: EventEmitter2,
    ) {}

    public async create({
        dto,
        formCollectionRepo,
        formCollectionItemRepo,
        additionalFieldRepo,
        formAdditionalFieldRepo,
        dataRegisterFieldRepo,
        dataRegisterTransactionRepo,
        dataRegisterTransactionFieldRepo,
        formCollectionItemTransactionFieldRepo,
        dataSource,
        formVersionId,
        dataRegisterRepo,
    }: {
        dto: FormCollectionRequest[];
        formCollectionRepo: Repository<FormCollectionEntity>;
        formCollectionItemRepo: Repository<FormCollectionItemEntity>;
        additionalFieldRepo: Repository<DataRegisterAdditionalFieldEntity>;
        formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity>;
        dataRegisterFieldRepo: Repository<DataRegisterFieldEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity>;
        dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldEntity>;
        formCollectionItemTransactionFieldRepo: Repository<CollectionTransactionEntity>;
        dataSource: DataSource;
        formVersionId: string;
        dataRegisterRepo: Repository<DataRegisterEntity | DataRegisterTenancyEntity>;
    }): Promise<boolean> {
        if (!dto?.length) {
            return true;
        }

        const formCollectionItemIds: string[] = [];

        const collectionMap = {};

        try {
            const collectionsContextMappings: {
                contextId: string;
                fieldId: string;
                collectionIdentityId: string;
            }[] = [];

            const collectionsAutomationMappings: CollectionAutomationMappingRequest[] = [];
            const newFormCollectionIdentityIds: string[] = [];

            const formCollectionEntities: FormCollectionEntity[] = dto.map((value) => {
                const { formCollectionItems, ..._formCollection } = value;
                const formCollection = this._mapper.map(_formCollection, FormCollectionRequest, FormCollectionEntity);
                formCollection.id = v4();
                formCollection.identityId = formCollection.identityId ?? formCollection.id;
                newFormCollectionIdentityIds.push(formCollection.identityId);
                const collectionContextMappings = (_formCollection.contextMappings || []).map((cm) => {
                    cm.collectionIdentityId = formCollection.identityId;
                    return cm;
                });
                collectionsContextMappings.push(...collectionContextMappings);
                formCollection.displaySetting = value?.displaySetting ?? {};

                const collectionAutomationMappings = (_formCollection.automationMappings || []).map((cm) => {
                    cm.collectionIdentityId = formCollection.identityId;
                    return cm;
                });
                collectionsAutomationMappings.push(...collectionAutomationMappings);

                if (formCollectionItems?.length) {
                    collectionMap[formCollection.id] = {
                        addedIds: [],
                        deletedIds: [],
                    };

                    formCollection.formCollectionItems = formCollectionItems.map((item) => {
                        const { setting, ..._formCollectionItem } = item;

                        const formCollectionItem = this._mapper.map(
                            _formCollectionItem,
                            FormCollectionItemRequest,
                            FormCollectionItemEntity,
                        );
                        formCollectionItem.identityId = formCollectionItem.identityId ?? formCollectionItem.id;
                        formCollectionItem.formCollectionId = formCollection.id;

                        if (setting) {
                            const { dataGroup, autoPopulate, ..._setting } = setting;

                            formCollectionItem.setting = _setting;
                            formCollectionItem.children = dataGroup?.length
                                ? dataGroup.map((child) => {
                                      child.formCollectionId = formCollection.id;
                                      child.id = child.id ?? v4();
                                      child.identityId = child.identityId ?? child.id;
                                      return child;
                                  })
                                : [];
                        }

                        if (item?.setting?.configuration?.hasGroup) {
                            (item.setting?.dataGroup ?? []).forEach((group) => {
                                formCollectionItemIds.push(group.id);
                                collectionMap[formCollection.id].addedIds.push(group.id);
                            });
                        }

                        formCollectionItemIds.push(formCollectionItem.id);
                        collectionMap[formCollection.id].addedIds.push(formCollectionItem.id);

                        return formCollectionItem;
                    });
                }

                return formCollection;
            });

            const result = await formCollectionRepo.save(formCollectionEntities);

            if (result?.length) {
                const collectionEvent = {
                    accountId: this._claims.accountId,
                    formCollectionItemIds,
                    formVersionId: formCollectionEntities[0].formVersionId,
                };

                await this.handleCaptureFormCollectionTransaction({
                    ...collectionEvent,
                    formCollectionItemRepo,
                    dataRegisterFieldRepo,
                    dataRegisterTransactionRepo,
                    dataRegisterTransactionFieldRepo,
                    formCollectionItemTransactionFieldRepo,
                    dataRegisterRepo,
                    formVersionId,
                });

                await this._handleUpdateCollectionACL({
                    accountId: this._claims.accountId,
                    collectionMap,
                    formVersionId: formCollectionEntities[0].formVersionId,
                    newFormCollectionIdentityIds,
                });

                const isCriteria = result.some((res) => res.type === DataRegisterTypeEnum.Criteria);

                if (isCriteria) {
                    await this.handleCreateAdditionalField({
                        ...collectionEvent,
                        formVersionId: result[0].formVersionId,
                        formAdditionalFieldRepo,
                        formCollectionItemRepo,
                        additionalFieldRepo,
                    });
                }

                if (collectionsContextMappings.length || collectionsAutomationMappings.length) {
                    await this._collectionContextDataService.update({
                        accountId: this._claims.accountId,
                        dataSource,
                        formVersionId,
                        request: {
                            contextMappings: collectionsContextMappings,
                            automationMappings: collectionsAutomationMappings,
                        },
                    });
                }
            }

            return !!result?.length;
        } catch (err) {
            this._loggerService.error(err);
            throw new BadRequestException(err);
        }
    }

    public async update({
        formVersionId,
        dto,
        formCollectionRepo,
        formCollectionItemRepo,
        additionalFieldRepo,
        formAdditionalFieldRepo,
        generalAutoPopulateSettingRepo,
        generalAutoPopulateExtraConfigRepo,
        dataSource,
    }: {
        formVersionId: string;
        dto: FormCollectionUpdateRequest[];
        formCollectionRepo: Repository<FormCollectionEntity>;
        formCollectionItemRepo: Repository<FormCollectionItemEntity>;
        generalAutoPopulateSettingRepo: Repository<GeneralAutoPopulateSettingEntity>;
        generalAutoPopulateExtraConfigRepo: Repository<GeneralAutoPopulateExtraConfigEntity>;
        additionalFieldRepo: Repository<DataRegisterAdditionalFieldEntity>;
        formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity>;
        dataSource: DataSource;
    }): Promise<boolean> {
        if (!dto?.length || !formVersionId) {
            return true;
        }

        const formCollectionItemIds: string[] = [];

        const collectionMap = {};

        try {
            const collectionsContextMappings: {
                contextId: string;
                fieldId: string;
                collectionIdentityId: string;
            }[] = [];

            const collectionsAutomationMappings: CollectionAutomationMappingRequest[] = [];

            const currentFormCollections = await formCollectionRepo.findBy({
                formVersionId,
            });
            const formCollectionEntities: FormCollectionEntity[] = dto.map((value) => {
                const { formCollectionItems, ..._formCollection } = value;
                const formCollection = this._mapper.map(_formCollection, FormCollectionUpdateRequest, FormCollectionEntity);
                formCollection.formVersionId = formVersionId;
                const collectionContextMappings = (_formCollection.contextMappings || []).map((cm) => {
                    const collection = currentFormCollections.find((c) => c.id === _formCollection.id);
                    cm.collectionIdentityId = collection?.identityId ?? collection?.id;
                    return cm;
                });
                collectionsContextMappings.push(...collectionContextMappings);

                const collectionAutomationMappings = (_formCollection.automationMappings || []).map((am) => {
                    const collection = currentFormCollections.find((c) => c.id === _formCollection.id);
                    am.collectionIdentityId = collection?.identityId ?? collection?.id;
                    return am;
                });

                collectionsAutomationMappings.push(...collectionAutomationMappings);

                if (formCollectionItems?.length) {
                    formCollection.formCollectionItems = [];

                    formCollectionItems.forEach((item) => {
                        const { setting, ..._formCollectionItem } = item;

                        const formCollectionItem = this._mapper.map(
                            _formCollectionItem,
                            FormCollectionItemRequest,
                            FormCollectionItemEntity,
                        );

                        if (setting) {
                            const { dataGroup, ..._setting } = setting;

                            formCollectionItem.setting = _setting;

                            if (dataGroup?.length) {
                                formCollectionItem.id = formCollectionItem.id ?? v4();
                                dataGroup.forEach((child) => {
                                    const _child = { ...child };
                                    _child.parentId = formCollectionItem.id;
                                    _child.formCollectionId = formCollection.id;
                                    _child.order = child.order;

                                    _child.id = _child.id ?? v4();
                                    formCollectionItemIds.push(_child.id);

                                    formCollection.formCollectionItems.push(_child);
                                });
                            }
                        }

                        formCollectionItem.formCollectionId = formCollection.id;

                        formCollectionItemIds.push(formCollectionItem.id);
                        formCollection.formCollectionItems.push(formCollectionItem);
                    });
                }

                return { ...formCollection };
            });

            const addedCollectionItems: Array<FormCollectionItemEntity | FormCollectionItemTenancyEntity> = [];
            const deletedCollectionItems: Array<FormCollectionItemEntity | FormCollectionItemTenancyEntity> = [];
            const deleteFormCollectionItemIdentityIds: string[] = [];

            const formCollectionIds = formCollectionEntities?.map((entity) => entity.id);
            const formCollectionItems = formCollectionIds?.length
                ? await formCollectionItemRepo.find({
                      where: {
                          formCollectionId: In(formCollectionIds),
                      },
                  })
                : [];

            for (const collection of formCollectionEntities) {
                collectionMap[collection.id] = {
                    addedIds: [],
                    deletedIds: [],
                };

                const existingCollectionItems = formCollectionItems.filter((item) => item.formCollectionId === collection.id);

                (collection.formCollectionItems || []).forEach((item) => {
                    const exist = existingCollectionItems.find((c) => c.id === item.id);
                    if (!exist) {
                        item.id = item.id ?? v4();
                        item.identityId = item.identityId ?? item.id;
                    }

                    if (item.setting) {
                        const { autoPopulate, ...restSetting } = item.setting as any;
                        item.setting = restSetting;
                    }
                });

                const added = collection.formCollectionItems.filter((c) => !existingCollectionItems.some((e) => e.id === c.id));
                const deleted = existingCollectionItems.filter((e) => !collection.formCollectionItems.some((c) => c.id === e.id));

                (deleted ?? []).forEach((item) => {
                    item.identityId && deleteFormCollectionItemIdentityIds.push(item.identityId);
                });

                addedCollectionItems.push(...added);
                deletedCollectionItems.push(...deleted);

                const addedIds = added.filter((i) => !(i.setting as any)?.configuration?.hasGroup)?.map((i) => i.id);
                const deletedIds = deleted.filter((i) => !(i.setting as any)?.configuration?.hasGroup)?.map((i) => i.identityId);
                collectionMap[collection.id].addedIds.push(..._.uniq(_.compact(addedIds)));
                collectionMap[collection.id].deletedIds.push(..._.uniq(_.compact(deletedIds)));
            }

            if (deleteFormCollectionItemIdentityIds?.length) {
                //remove collection auto populate setting
                generalAutoPopulateSettingRepo.softDelete({
                    itemIdentityId: In(deleteFormCollectionItemIdentityIds),
                    builderVersionId: formVersionId,
                });

                generalAutoPopulateExtraConfigRepo.softDelete({
                    itemIdentityId: In(deleteFormCollectionItemIdentityIds),
                    builderVersionId: formVersionId,
                });
            }

            const result = await formCollectionRepo.save(formCollectionEntities);

            if (result?.length) {
                // CRITERIA
                for (const collection of result) {
                    if (collection.type !== DataRegisterTypeEnum.Criteria) {
                        continue;
                    }

                    const deleteFormCollectionItemIdentityIds = collectionMap[collection.id]?.deletedIds ?? [];

                    const addedCollectionItems = collectionMap[collection.id]?.addedIds ?? [];

                    if (deleteFormCollectionItemIdentityIds?.length) {
                        await this.handleDeleteCriteriaAdditionalField({
                            formVersionId: collection.formVersionId,
                            deleteFormCollectionItemIdentityIds,
                            formAdditionalFieldRepo,
                        });

                        //remove auto populate setting
                        generalAutoPopulateSettingRepo.softDelete({
                            itemIdentityId: In(deleteFormCollectionItemIdentityIds),
                        });

                        generalAutoPopulateExtraConfigRepo.softDelete({
                            itemIdentityId: In(deleteFormCollectionItemIdentityIds),
                        });
                    }

                    if (addedCollectionItems?.length) {
                        await this.handleCreateAdditionalField({
                            accountId: this._claims.accountId,
                            formCollectionItemIds: addedCollectionItems,
                            formVersionId: collection.formVersionId,
                            formAdditionalFieldRepo,
                            formCollectionItemRepo,
                            additionalFieldRepo,
                        });
                    }
                }
            }

            if (collectionsContextMappings.length || collectionsAutomationMappings.length) {
                await this._collectionContextDataService.update({
                    accountId: this._claims.accountId,
                    dataSource,
                    formVersionId,
                    request: {
                        contextMappings: collectionsContextMappings,
                        automationMappings: collectionsAutomationMappings,
                    },
                });
            }

            await this._handleUpdateCollectionACL({
                accountId: this._claims.accountId,
                collectionMap,
                formVersionId: formVersionId,
            });

            return !!result?.length;
        } catch (err) {
            throw new InternalServerErrorException(err);
        }
    }

    public async delete({
        formVersionId,
        formCollectionIds,
        formCollectionIdentityIds,
        formCollectionRepo,
        formCollectionItemRepo,
        generalAutoPopulateSettingRepo,
        generalAutoPopulateExtraConfigRepo,
        formAdditionalFieldRepo,
        contextMappingRepo,
        automationMappingRepo,
    }: {
        formVersionId: string;
        formCollectionIds: string[];
        formCollectionIdentityIds: string[];
        formCollectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        formCollectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;
        generalAutoPopulateSettingRepo: Repository<GeneralAutoPopulateSettingEntity>;
        generalAutoPopulateExtraConfigRepo: Repository<GeneralAutoPopulateExtraConfigEntity>;
        formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity>;
        contextMappingRepo: Repository<FormContextMappingTenancyEntity>;
        automationMappingRepo: Repository<FormCollectionAutomationMappingTenancyEntity>;
    }): Promise<boolean> {
        const collectionItems = await formCollectionItemRepo.find({
            where: {
                formCollectionId: In(formCollectionIds),
            },
        });

        const deletedItemIdentityIds: string[] = [];

        const collectionMap = collectionItems.reduce((acc, item) => {
            if (!acc[item.formCollectionId]) {
                acc[item.formCollectionId] = {
                    addedIds: [],
                    deletedIds: [],
                };
            }

            deletedItemIdentityIds.push(item.identityId);
            acc[item.formCollectionId].deletedIds.push(item.identityId);
            return acc;
        }, {});

        const result = await formCollectionRepo.softDelete({
            id: In(formCollectionIds),
        });

        if (collectionItems?.length) {
            await this._handleUpdateCollectionACL({
                accountId: this._claims.accountId,
                collectionMap,
                formVersionId: formVersionId,
            });
        }

        // this._eventEmitter.emitAsync(FORM_COLLECTION_EVENT.DELETE_FORM_COLLECTION, {
        //     accountId: this._claims.accountId,
        //     formVersionId: formVersionId,
        //     formCollectionIds,
        // });

        await this.handleDeleteFormCollection({
            formVersionId: formVersionId,
            formCollectionIds,
            formCollectionItemRepo,
            formAdditionalFieldRepo,
            generalAutoPopulateSettingRepo,
            generalAutoPopulateExtraConfigRepo,
            contextMappingRepo,
            formCollectionIdentityIds,
            automationMappingRepo,
        });

        // //delete auto populate settings
        // if (deletedItemIdentityIds?.length) {
        //     generalAutoPopulateSettingRepo.softDelete({
        //         itemIdentityId: In(deletedItemIdentityIds),
        //     });

        //     generalAutoPopulateExtraConfigRepo.softDelete({
        //         itemIdentityId: In(deletedItemIdentityIds),
        //     });
        // }

        return !!result.affected;
    }

    public async getCollectionsByFormVersionId({
        formVersionId,
        formCollectionRepo,
        formCollectionItemRepo,
        formCollectionContextMappingRepo,
        generalAutoPopulateRepo,
        formVersionRepo,
        dataRegisterTransactionRepo,
        dataRegisterTransactionFieldRepo,
        dataRegisterVersionRepo,
        dataRegisterRepo,
    }: {
        formVersionId: string;
        formCollectionRepo: Repository<FormCollectionEntity>;
        formCollectionItemRepo: Repository<FormCollectionItemEntity>;
        formCollectionContextMappingRepo?: Repository<FormCollectionContextMappingEntity>;
        generalAutoPopulateRepo: Repository<GeneralAutoPopulateSettingEntity>;
        formVersionRepo?: Repository<FormVersionEntity | FormVersionTenancyEntity>;
        dataRegisterTransactionRepo?: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterVersionRepo?: Repository<DataRegisterVersionEntity | DataRegisterVersionTenancyEntity>;
        dataRegisterRepo?: Repository<DataRegisterEntity | DataRegisterTenancyEntity>;
        dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>;
    }): Promise<FormCollectionEntity[]> {
        if (!formVersionId) {
            return [];
        }

        const tasks: any[] = [
            formCollectionRepo.find({
                where: {
                    formVersionId,
                },
                order: {
                    name: 'asc',
                },
                select: [
                    'id',
                    'name',
                    'type',
                    'dataRegisterId',
                    'formVersionId',
                    'identityId',
                    'dataRegisterVersionId',
                    'displaySetting',
                    'icon',
                ],
            }),
        ];

        if (formVersionRepo) {
            tasks.push(
                formVersionRepo.find({
                    where: {
                        id: formVersionId,
                    },
                    select: ['id', 'status'],
                }),
            );
        }

        let [formCollections, formVersion] = (await Promise.all(tasks)) as [FormCollectionEntity[], FormVersionEntity];

        if (!formCollections?.length) {
            return [];
        }

        const formCollectionIds = formCollections.map((formCollection) => formCollection.id);

        try {
            const formCollectionItems = await formCollectionItemRepo.find({
                select: [
                    'id',
                    'formCollectionId',
                    'identityId',
                    'dataRegisterTransactionId',
                    'type',
                    'setting',
                    'name',
                    'parentId',
                    'order',
                ],
                where: {
                    formCollectionId: In(formCollectionIds),
                },
                order: {
                    order: 'ASC',
                },
            });

            if (formCollectionItems?.length) {
                formCollections = this.groupItems(formCollections, formCollectionItems);
            }

            if (formCollectionContextMappingRepo) {
                const formCriteriaItemIds: string[] = formCollectionItems.map((item) => item.id);
                const contextMappings = await formCollectionContextMappingRepo.find({
                    where: {
                        formCollectionItemId: In(formCriteriaItemIds),
                    },
                });

                formCollections = this._contextMapping(formCollections, contextMappings);
            }

            if (formVersion && formVersion.status === FormVersionStatus.Draft) {
                const dataRegisterTransactionIds = this.extractDataRegisterTransactionIds(formCollections);

                const dataRegisterTransactions = await this.fetchDataRegisterTransactions(
                    dataRegisterTransactionIds,
                    dataRegisterTransactionRepo,
                    dataRegisterTransactionFieldRepo,
                );

                if (dataRegisterTransactions.length) {
                    const dataRegisterVersionIds: string[] = dataRegisterTransactions.map(
                        (transaction) => transaction.dataRegisterVersionId,
                    );

                    const dataRegisterIds: string[] = formCollections.map((collection) => collection.dataRegisterId);

                    const dataRegisterVersions = await this.fetchDataRegisterVersions(dataRegisterVersionIds, dataRegisterVersionRepo);
                    const dataRegisters = await this.fetchDataRegisters(dataRegisterIds, dataRegisterRepo);

                    formCollections = this.updateFormCollections(
                        formCollections,
                        dataRegisterTransactions,
                        dataRegisterVersions,
                        dataRegisters,
                    );
                }
            }

            return formCollections;
        } catch (err) {
            this._loggerService.error(err);
            throw new BadRequestException(err);
        }
    }

    public async getFormCollectionPagination({
        query,
        formCollectionRepo,
        formCollectionItemRepo,
    }: {
        query: MultipleFilterRequestDto & { includeItemGroup: string };
        formCollectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        formCollectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;
    }): Promise<PaginationResponseDto<FormCollectionDto>> {
        const alias = 'formCollections';
        const builder = formCollectionRepo.createQueryBuilder(alias);

        if (query.filters?.length) {
            this._queryBuilder.applyQueryFilters(builder, alias, query.filters, []);
        }

        const sorters: OrderOptionDto[] = [
            {
                field: query.sort || 'name',
                order: query.order || OrderType.ASC,
            },
        ];

        this._queryBuilder.applySorters(builder, alias, sorters);

        try {
            const [data, total] = await builder.skip(query.skip).take(query.take).getManyAndCount();

            const dto = this._mapper.mapArray(data, FormCollectionEntity, FormCollectionDto);

            if (query?.includeItemGroup === 'true') {
                const collectionIds = data?.length ? data?.map((value) => value.id) : [];

                if (collectionIds?.length) {
                    const collectionItems = await formCollectionItemRepo
                        .createQueryBuilder('entity')
                        .where(`entity.setting -> 'configuration' ->> 'hasGroup' = 'true'`)
                        .andWhere({ formCollectionId: In(collectionIds) })
                        .getMany();

                    const group = _.groupBy(collectionItems, 'formCollectionId');

                    dto.forEach((collection) => {
                        if (group?.[collection?.id]) {
                            collection.hasGroup = true;
                        }
                    });
                }
            }

            return {
                data: dto,
                total: total,
            };
        } catch (err) {
            this._loggerService.error(err);
            throw new InternalServerErrorException(err);
        }
    }

    public async getCollectionDetail({
        id,
        formCollectionRepo,
    }: {
        id: string;
        formCollectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
    }) {
        let data = await formCollectionRepo.findOne({
            where: {
                id,
            },
            relations: ['formCollectionItems'],
        });

        if (data?.formCollectionItems?.length) {
            const { formCollectionItems, ...formCollection } = data;

            const _data = this.groupItems([formCollection], formCollectionItems);
            data = _data?.length ? _data[0] : null;
        }

        const dto = this._mapper.map(data, FormCollectionEntity, FormCollectionDto);
        return dto;
    }

    public async getCollectionDetailByIdentityIdAndFormId({
        formCollectionIdentityId,
        formVersionId,
        formCollectionRepo,
        collectionLayoutRepo,
    }: {
        formCollectionIdentityId: string;
        formVersionId: string;
        formCollectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        collectionLayoutRepo: Repository<CollectionLayoutEntity | CollectionLayoutTenancyEntity>;
    }) {
        let data = await formCollectionRepo.findOne({
            where: {
                identityId: formCollectionIdentityId,
                formVersionId: formVersionId,
            },
            relations: ['formCollectionItems'],
        });

        if (data?.formCollectionItems?.length) {
            const { formCollectionItems, ...formCollection } = data;

            const collectionIdentityIds = formCollectionItems.map((item) => item.identityId);

            const layouts = await this._getCollectionLayout(collectionIdentityIds, formVersionId, collectionLayoutRepo);
            if (layouts.length) {
                formCollectionItems.forEach((item) => {
                    const layout = layouts.find((layout) => layout.collectionItemIdentityId === item.identityId);
                    item.layout = layout;
                });
            }

            const _data = this.groupItems([formCollection], formCollectionItems);
            data = _data?.length ? _data[0] : null;
        }

        const dto = this._mapper.map(data, FormCollectionEntity, FormCollectionDto);
        return dto;
    }

    public async getCollectionAdditionalFields({
        formCollectionItemIdentityId,
        formVersionId,
        repo,
    }: {
        formCollectionItemIdentityId: string;
        formVersionId: string;
        repo: Repository<FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity>;
    }) {
        const data = await repo.find({
            where: {
                formCollectionItemIdentityId: formCollectionItemIdentityId,
                formVersionId: formVersionId,
            },
            relations: ['autoPopulateContexts'],
        });
        return data;
    }

    public async getFormCollectionAdditionalFields({
        formCollectionItemIdentityIds,
        formVersionId,
        repo,
    }: {
        formCollectionItemIdentityIds: string[];
        formVersionId: string;
        repo: Repository<FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity>;
    }) {
        if (!formCollectionItemIdentityIds?.length) {
            return [];
        }

        // const cachedData = await this._formCollectionCacheService.getCollectionAdditionalFields<
        //     FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity
        // >(formVersionId, formCollectionItemIdentityIds);

        // if (cachedData?.length) {
        //     return cachedData;
        // }

        const where: Record<string, any> = {
            formVersionId,
        };

        if (formCollectionItemIdentityIds?.length) {
            where.formCollectionItemIdentityId = In(formCollectionItemIdentityIds);
        }

        const data = await repo.find({
            where,
            // relations: ['autoPopulateContexts'],
        });

        // this._eventEmitter.emitAsync(FORM_COLLECTION_EVENT.CACHE_COLLECTION_ADDITIONAL_FIELDS, {
        //     formVersionId,
        //     accountId: this._claims.accountId,
        // });

        return data;
    }

    public groupItems(
        formCollections: (FormCollectionEntity | FormCollectionTenancyEntity)[],
        formCollectionItems: (FormCollectionItemEntity | FormCollectionItemTenancyEntity)[],
    ) {
        formCollections.forEach((collection) => {
            const _formCollectionItems = formCollectionItems.filter(
                (item) => item.formCollectionId === collection.id,
            ) as FormCollectionItemTenancyEntity[];

            const formCollectionsGroup = _.groupBy(_formCollectionItems || [], 'parentId') as Record<string, FormCollectionItemEntity[]>;

            const formCollectionsParent = formCollectionsGroup['null'] || [];

            for (const [key, value] of Object.entries(formCollectionsGroup)) {
                if (!key) {
                    continue;
                }

                for (const _formCollection of formCollectionsParent) {
                    if (value[0]?.parentId === _formCollection.id) {
                        _formCollection.children = _.orderBy(value, ['order']);
                        break;
                    }
                }
            }

            collection.formCollectionItems = _.orderBy(formCollectionsParent, ['order']);
        });

        return formCollections;
    }

    private _contextMapping(
        formCollections: (FormCollectionEntity | FormCollectionTenancyEntity)[],
        contextMappings: (FormCollectionContextMappingEntity | FormCollectionContextMappingTenancyEntity)[],
    ) {
        formCollections.forEach((collection) => {
            if (collection.type !== DataRegisterTypeEnum.Criteria) {
                return;
            }

            collection.formCollectionItems?.forEach((formCollectionItem: FormCollectionItemEntity | FormCollectionItemTenancyEntity) => {
                const _contexts = contextMappings.filter((mapping) => mapping.formCollectionItemId === formCollectionItem.id);
                formCollectionItem.contextMappings = _contexts ?? [];
            });
        });

        return formCollections;
    }

    private async _handleUpdateCollectionACL({
        accountId,
        collectionMap,
        formVersionId,
        newFormCollectionIdentityIds,
    }: {
        collectionMap: {
            [collectionId: string]: {
                addedIds: Array<string>;
                deletedIds: Array<string>;
            };
        };
        accountId: string;
        formVersionId: string;
        newFormCollectionIdentityIds?: string[];
    }) {
        if (_.isEmpty(collectionMap)) {
            return;
        }

        const addTasks = [];
        const deleteTasks = [];

        if (newFormCollectionIdentityIds?.length) {
            addTasks.push(
                this.handleAddDefaultCreateDeleteCollectionACL({
                    formVersionId,
                    collectionIdentityIds: newFormCollectionIdentityIds,
                    accountId,
                }),
            );
        }

        for (const collectionId in collectionMap) {
            const { addedIds, deletedIds } = collectionMap[collectionId];

            if (deletedIds.length) {
                deleteTasks.push(this.handleDeleteCollection({ formVersionId, collectionItemIds: deletedIds, collectionId, accountId }));
            }

            if (addedIds.length) {
                addTasks.push(this.handleAddCollectionACL({ formVersionId, collectionItemIds: addedIds, collectionId, accountId }));
            }
        }

        await Promise.all([...deleteTasks]);
        await Promise.all([...addTasks]);
    }

    private async handleDeleteCollection(request: IFormCollectionEvent): Promise<void> {
        try {
            this._loggerService.info(`Delete ACL of form collection: ${request.formVersionId}`);
            if (!request.collectionItemIds?.length) {
                return;
            }

            const isAccount = !!request.accountId;
            const dataSource = await this._dataSourceUtil.createAccountDataSource(request?.accountId);

            return dataSource.transaction(async (manager) => {
                /**
                 * - Delete stage access control (x)
                 * - Delete stage role access control (x)
                 */

                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAclRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity
                >;

                const fieldIds = _.uniq(_.compact(request.collectionItemIds));
                if (!fieldIds.length) {
                    return;
                }

                // Stage Access Control
                await stageAccessControlRepo.softDelete({
                    formVersionId: request.formVersionId,
                    collectionItemIdentityId: In(fieldIds),
                    type: AccessControlType.COLLECTION,
                });

                // Stage Role access control
                const stageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!stageRoles.length) {
                    return;
                }

                const stageRoleIds = _.uniq(_.compact(stageRoles.map((role) => role.id)));

                await stageRoleAclRepo.softDelete({
                    collectionItemIdentityId: In(fieldIds),
                    stageRoleId: In(stageRoleIds),
                    type: AccessControlType.COLLECTION,
                });

                this._loggerService.info(`Deleted ACL of form collection: ${request.formVersionId}`);
            });
        } catch (error) {
            this._loggerService.error(error);
        }
    }

    private async handleAddCollectionACL(request: IFormCollectionEvent): Promise<void> {
        this._loggerService.info(`Create default ACL for collection: ${request.formVersionId}`);
        if (!request.collectionItemIds?.length) {
            return;
        }

        if (!request.collectionId) {
            this._loggerService.error('Collection Id is required');
            return;
        }

        try {
            const isAccount = !!request.accountId;
            const dataSource = await this._dataSourceUtil.createAccountDataSource(request?.accountId);

            return dataSource.transaction(async (manager) => {
                const stageAccessControlRepo = (isAccount
                    ? manager.getRepository(StageAccessControlTenancyEntity)
                    : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
                    StageAccessControlEntity | StageAccessControlTenancyEntity
                >;

                const collectionItemRepo = (isAccount
                    ? manager.getRepository(FormCollectionItemTenancyEntity)
                    : manager.getRepository(FormCollectionItemEntity)) as unknown as Repository<
                    FormCollectionItemEntity | FormCollectionItemTenancyEntity
                >;

                const collectionRepo = (isAccount
                    ? manager.getRepository(FormCollectionTenancyEntity)
                    : manager.getRepository(FormCollectionEntity)) as unknown as Repository<
                    FormCollectionEntity | FormCollectionTenancyEntity
                >;

                const stageRepo = (isAccount
                    ? manager.getRepository(StageTenancyEntity)
                    : manager.getRepository(StageEntity)) as unknown as Repository<StageEntity | StageTenancyEntity>;

                const dataRegisterFieldRepo = (isAccount
                    ? manager.getRepository(DataRegisterFieldTenancyEntity)
                    : manager.getRepository(DataRegisterFieldEntity)) as unknown as Repository<
                    DataRegisterFieldEntity | DataRegisterFieldTenancyEntity
                >;

                const stageRoleRepo = (isAccount
                    ? manager.getRepository(StageRoleTenancyEntity)
                    : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

                const stageRoleAccessControlRepo = (isAccount
                    ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                    : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                    StageRoleAccessControlTenancyEntity | StageRoleAccessControlEntity
                >;

                const registerTransactionRepo = (isAccount
                    ? manager.getRepository(DataRegisterTransactionTenancyEntity)
                    : manager.getRepository(DataRegisterTransactionEntity)) as unknown as Repository<
                    DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity
                >;

                const collection = await collectionRepo.findOne({
                    where: {
                        id: request.collectionId,
                    },
                });

                const drFields = await dataRegisterFieldRepo.find({
                    where: {
                        dataRegisterVersionId: collection.dataRegisterVersionId,
                    },
                });

                let availableFields: Array<DataRegisterFieldEntity | DataRegisterFieldTenancyEntity> = [];

                if (collection?.type === DataRegisterTypeEnum.Criteria) {
                    availableFields = (drFields || []).filter(
                        (i) =>
                            [FormFieldTypeEnum.Answer, FormFieldTypeEnum.Comparison].includes(i.type) || !!i.configuration?.isSupportField,
                    );
                } else {
                    availableFields = (drFields || [])
                        .filter((i) => !!i.configuration?.isSupportField)
                        ?.filter((f) => f.type !== FormFieldTypeEnum.Definable);
                }

                // if (!availableFields.length) {
                //     this._loggerService.error('No available fields');
                //     return;
                // }

                const collectionItems = await collectionItemRepo.find({
                    where: {
                        id: In(request.collectionItemIds),
                    },
                });

                const registerTransactionIds = collectionItems.map((i) => i.dataRegisterTransactionId);

                const registerTransactions = await registerTransactionRepo.find({
                    where: {
                        id: In(registerTransactionIds),
                    },
                    relations: {
                        additionalFields: true,
                    },
                });

                const additionalFields = registerTransactions
                    .flatMap((i) => i.additionalFields || [])
                    .filter((f) => ![AdditionalFieldType.Answer, AdditionalFieldType.Comparison].includes(f.additionalType));

                const definableAdditionalFields = additionalFields.filter((f) => f.additionalType === AdditionalFieldType.Definable);

                const withOutGroup = collectionItems.filter((item) => !(item.setting as any)?.configuration?.hasGroup);

                if (!withOutGroup.length) {
                    return;
                }

                const formStages = await stageRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                if (!formStages) {
                    return;
                }

                // Stage Access Control
                const newStageAcls: Array<StageAccessControlEntity | StageAccessControlTenancyEntity> = [];

                formStages.forEach((stage) => {
                    withOutGroup.forEach((collectionItem) => {
                        const filterAdded = additionalFields.filter((f) => f.transactionId === collectionItem.dataRegisterTransactionId);
                        const uniqueFields = _.uniqBy([...availableFields, ...filterAdded], 'fieldId');

                        uniqueFields?.forEach((af) => {
                            const id = v4();
                            newStageAcls.push(
                                stageAccessControlRepo.create({
                                    id: id,
                                    identityId: id,
                                    formVersionId: request.formVersionId,
                                    stageId: stage.id,
                                    targetId: af.fieldId,
                                    type: AccessControlType.COLLECTION,
                                    collectionItemIdentityId: collectionItem.identityId,
                                    config: {
                                        visible: true,
                                        editable: true,
                                        required: false,
                                        access: AccessOption.Editable,
                                        collectionItemIdentityId: collectionItem.identityId,
                                    },
                                }),
                            );
                        });
                    });
                });

                await stageAccessControlRepo.insert(newStageAcls);

                // Stage Roles
                const existingStageRoles = await stageRoleRepo.findBy({
                    formVersionId: request.formVersionId,
                });

                const newRoleAcls: Array<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity> = [];

                existingStageRoles.forEach((st) => {
                    withOutGroup.forEach((collectionItem) => {
                        const filterAdded = additionalFields.filter((f) => f.transactionId === collectionItem.dataRegisterTransactionId);
                        const uniqueFields = _.uniqBy([...availableFields, ...filterAdded], 'fieldId');

                        uniqueFields?.forEach((af) => {
                            const id = v4();
                            newRoleAcls.push(
                                stageRoleAccessControlRepo.create({
                                    id: id,
                                    identityId: id,
                                    stageRoleId: st.id,
                                    targetId: af.fieldId,
                                    type: AccessControlType.COLLECTION,
                                    collectionItemIdentityId: collectionItem.identityId,
                                    config: {
                                        visible: true,
                                        editable: true,
                                        required: false,
                                        access: AccessOption.Editable,
                                        collectionItemIdentityId: collectionItem.identityId,
                                    },
                                }),
                            );
                        });
                    });
                });

                // Stage Role Access Control
                await stageRoleAccessControlRepo.insert(newRoleAcls);
            });
        } catch (error) {
            this._loggerService.error(error);
        }
    }

    private async handleAddDefaultCreateDeleteCollectionACL({
        formVersionId,
        collectionIdentityIds,
        accountId,
    }: {
        formVersionId: string;
        collectionIdentityIds: string[];
        accountId: string;
    }) {
        const dataSource = await this._dataSourceUtil.createAccountDataSource(accountId);
        return dataSource.transaction(async (manager) => {
            const stageRoleAccessControlRepo = (accountId
                ? manager.getRepository(StageRoleAccessControlTenancyEntity)
                : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
                StageRoleAccessControlTenancyEntity | StageRoleAccessControlEntity
            >;
            const stageRoleRepo = (accountId
                ? manager.getRepository(StageRoleTenancyEntity)
                : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

            // Stage Roles
            const existingStageRoles = await stageRoleRepo.findBy({
                formVersionId: formVersionId,
            });

            const newRoleAcls: Array<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity> = [];

            existingStageRoles.forEach((st) => {
                collectionIdentityIds.forEach((collectionIdentityId) => {
                    const id = v4();
                    newRoleAcls.push(
                        stageRoleAccessControlRepo.create({
                            id: id,
                            identityId: id,
                            stageRoleId: st.id,
                            targetId: collectionIdentityId,
                            type: AccessControlType.CREATE_DELETE_COLLECTION,
                            config: {
                                canCreate: true,
                                canDelete: true,
                            },
                        }),
                    );
                });
            });

            // Stage Role Access Control
            await stageRoleAccessControlRepo.insert(newRoleAcls);
        });
    }

    public async handleUpdateCollectionACL(request: IUpdateFormCollectionACL): Promise<void> {
        this._loggerService.info(`Update default ACL for collection: ${request.formVersionId}`);
        // if (!request.collectionItemIdentityIds?.length) {
        //     return;
        // }

        // if (!request.collectionId) {
        //     this._loggerService.error('Collection Id is required');
        //     return;
        // }

        // try {
        //     const isAccount = request.isAccount;
        //     const dataSource = await this._dataSourceUtil.createAccountDataSource(this._claims?.accountId);

        //     return dataSource.transaction(async (manager) => {
        //         const stageAccessControlRepo = (isAccount
        //             ? manager.getRepository(StageAccessControlTenancyEntity)
        //             : manager.getRepository(StageAccessControlEntity)) as unknown as Repository<
        //             StageAccessControlEntity | StageAccessControlTenancyEntity
        //         >;

        //         const collectionItemRepo = (isAccount
        //             ? manager.getRepository(FormCollectionItemTenancyEntity)
        //             : manager.getRepository(FormCollectionItemEntity)) as unknown as Repository<
        //             FormCollectionItemEntity | FormCollectionItemTenancyEntity
        //         >;

        //         const collectionRepo = (isAccount
        //             ? manager.getRepository(FormCollectionTenancyEntity)
        //             : manager.getRepository(FormCollectionEntity)) as unknown as Repository<
        //             FormCollectionEntity | FormCollectionTenancyEntity
        //         >;

        //         const stageRepo = (isAccount
        //             ? manager.getRepository(StageTenancyEntity)
        //             : manager.getRepository(StageEntity)) as unknown as Repository<StageEntity | StageTenancyEntity>;

        //         const dataRegisterFieldRepo = (isAccount
        //             ? manager.getRepository(DataRegisterFieldTenancyEntity)
        //             : manager.getRepository(DataRegisterFieldEntity)) as unknown as Repository<
        //             DataRegisterFieldEntity | DataRegisterFieldTenancyEntity
        //         >;

        //         const stageRoleRepo = (isAccount
        //             ? manager.getRepository(StageRoleTenancyEntity)
        //             : manager.getRepository(StageRoleEntity)) as unknown as Repository<StageRoleEntity | StageRoleTenancyEntity>;

        //         const stageRoleAccessControlRepo = (isAccount
        //             ? manager.getRepository(StageRoleAccessControlTenancyEntity)
        //             : manager.getRepository(StageRoleAccessControlEntity)) as unknown as Repository<
        //             StageRoleAccessControlTenancyEntity | StageRoleAccessControlEntity
        //         >;

        //         const registerTransactionRepo = (isAccount
        //             ? manager.getRepository(DataRegisterTransactionTenancyEntity)
        //             : manager.getRepository(DataRegisterTransactionEntity)) as unknown as Repository<
        //             DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity
        //         >;

        //         const collection = await collectionRepo.findOne({
        //             where: {
        //                 id: request.collectionId,
        //             },
        //         });

        //         const drFields = await dataRegisterFieldRepo.find({
        //             where: {
        //                 dataRegisterVersionId: collection.dataRegisterVersionId,
        //             },
        //         });

        //         let availableFields: Array<DataRegisterFieldEntity | DataRegisterFieldTenancyEntity> = [];

        //         if (collection?.type === DataRegisterTypeEnum.Criteria) {
        //             availableFields = (drFields || []).filter(
        //                 (i) =>
        //                     [FormFieldTypeEnum.Answer, FormFieldTypeEnum.Comparison].includes(i.type) || !!i.configuration?.isSupportField,
        //             );
        //         } else {
        //             availableFields = (drFields || [])
        //                 .filter((i) => !!i.configuration?.isSupportField)
        //                 ?.filter((f) => f.type !== FormFieldTypeEnum.Definable);
        //         }

        //         // if (!availableFields.length) {
        //         //     this._loggerService.error('No available fields');
        //         //     return;
        //         // }

        //         const collectionItems = await collectionItemRepo.find({
        //             where: {
        //                 identityId: In(request.collectionItemIdentityIds),
        //                 formCollectionId: request.collectionId,
        //             },
        //         });

        //         const existingStageRoles = await stageRoleRepo.findBy({
        //             formVersionId: request.formVersionId,
        //         });

        //         const existingStageACL = await stageAccessControlRepo.find({
        //             where: {
        //                 formVersionId: request.formVersionId,
        //                 collectionItemIdentityId: In(request.collectionItemIdentityIds),
        //             },
        //         });

        //         const existingStageRoleACL = await stageRoleAccessControlRepo.find({
        //             where: {
        //                 stageRoleId: In(existingStageRoles.map((role) => role.id)),
        //                 collectionItemIdentityId: In(request.collectionItemIdentityIds),
        //             },
        //         });

        //         const withOutGroup = collectionItems.filter((item) => !(item.setting as any)?.configuration?.hasGroup);

        //         if (!withOutGroup.length) {
        //             return;
        //         }

        //         const formStages = await stageRepo.findBy({
        //             formVersionId: request.formVersionId,
        //         });

        //         if (!formStages) {
        //             return;
        //         }

        //         const registerTransactionIds = collectionItems.map((i) => i.dataRegisterTransactionId);

        //         const registerTransactions = await registerTransactionRepo.find({
        //             where: {
        //                 id: In(registerTransactionIds),
        //             },
        //             relations: {
        //                 additionalFields: true,
        //             },
        //         });

        //         const additionalFields = registerTransactions
        //             .flatMap((i) => i.additionalFields || [])
        //             .filter((f) => ![AdditionalFieldType.Answer, AdditionalFieldType.Comparison].includes(f.additionalType));

        //         const definableAdditionalFields = additionalFields.filter((f) => f.additionalType === AdditionalFieldType.Definable);

        //         // Stage Access Control
        //         const newStageAcls: Array<StageAccessControlEntity | StageAccessControlTenancyEntity> = [];

        //         formStages.forEach((stage) => {
        //             withOutGroup.forEach((collectionItem) => {
        //                 availableFields.forEach((af) => {
        //                     if (
        //                         !!existingStageACL.find(
        //                             (acl) =>
        //                                 acl.stageId === stage.id &&
        //                                 acl.targetId === af.fieldId &&
        //                                 acl.collectionItemIdentityId === collectionItem.identityId,
        //                         )
        //                     ) {
        //                         return;
        //                     }

        //                     const id = v4();
        //                     newStageAcls.push(
        //                         stageAccessControlRepo.create({
        //                             id: id,
        //                             identityId: id,
        //                             formVersionId: request.formVersionId,
        //                             stageId: stage.id,
        //                             targetId: af.fieldId,
        //                             type: AccessControlType.COLLECTION,
        //                             collectionItemIdentityId: collectionItem.identityId,
        //                             config: {
        //                                 visible: true,
        //                                 editable: true,
        //                                 required: false,
        //                                 access: AccessOption.Editable,
        //                                 collectionItemIdentityId: collectionItem.identityId,
        //                             },
        //                         }),
        //                     );
        //                 });
        //             });
        //         });

        //         await stageAccessControlRepo.insert(newStageAcls);

        //         // Stage Roles
        //         const newRoleAcls: Array<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity> = [];

        //         existingStageRoles.forEach((st) => {
        //             withOutGroup.forEach((collectionItem) => {
        //                 [...availableFields, ...definableAdditionalFields].forEach((af) => {
        //                     if (
        //                         !!existingStageRoleACL.find(
        //                             (acl) =>
        //                                 acl.stageRoleId === st.id &&
        //                                 acl.targetId === af.fieldId &&
        //                                 acl.collectionItemIdentityId === collectionItem.identityId,
        //                         )
        //                     ) {
        //                         return;
        //                     }

        //                     const id = v4();
        //                     newRoleAcls.push(
        //                         stageRoleAccessControlRepo.create({
        //                             id: id,
        //                             identityId: id,
        //                             stageRoleId: st.id,
        //                             targetId: af.fieldId,
        //                             type: AccessControlType.COLLECTION,
        //                             collectionItemIdentityId: collectionItem.identityId,
        //                             config: {
        //                                 visible: true,
        //                                 editable: true,
        //                                 required: false,
        //                                 access: AccessOption.Editable,
        //                                 collectionItemIdentityId: collectionItem.identityId,
        //                             },
        //                         }),
        //                     );
        //                 });
        //             });
        //         });

        //         // Stage Role Access Control
        //         await stageRoleAccessControlRepo.insert(newRoleAcls);
        //     });
        // } catch (error) {
        //     this._loggerService.error(error);
        // }
    }

    public async handleCreateAdditionalField({
        accountId,
        formVersionId,
        formCollectionItemIds,
        formCollectionItemRepo,
        additionalFieldRepo,
        formAdditionalFieldRepo,
        ignoreSave = false,
    }: {
        accountId?: string;
        formVersionId: string;
        formCollectionItemIds: string[];
        formCollectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;
        additionalFieldRepo: Repository<DataRegisterAdditionalFieldEntity | DataRegisterAdditionalFieldTenancyEntity>;
        formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity>;
        ignoreSave?: boolean;
    }): Promise<FormCollectionAdditionalFieldEntity[] | FormCollectionAdditionalFieldTenancyEntity[]> {
        const formCollectionItems = await formCollectionItemRepo.find({
            where: {
                id: In(formCollectionItemIds),
            },
            select: ['id', 'dataRegisterTransactionId', 'identityId'],
        });

        const dataTransactionIds = formCollectionItems?.map((item) => item.dataRegisterTransactionId);
        if (!dataTransactionIds?.length) {
            return [];
        }

        const additionalFieldsGroup = _.groupBy(formCollectionItems, 'dataRegisterTransactionId') ?? {};

        const additionalFields = await additionalFieldRepo.find({
            where: {
                transactionId: In(dataTransactionIds),
            },
            // relations: ['autoPopulateContexts'],
        });

        if (!additionalFields?.length) {
            return [];
        }

        const formCollectionIdentityIds = formCollectionItems.map((item) => item.identityId);

        //* First, delete all additional fields of the form collection items
        if (!ignoreSave) {
            await formAdditionalFieldRepo.softDelete({
                formCollectionItemIdentityId: In(formCollectionIdentityIds),
                formVersionId: formVersionId,
            });
        }

        const newAdditionalFields: (FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity)[] = [];

        additionalFields.forEach((field) => {
            const newField = accountId
                ? this._mapper.map(field, DataRegisterAdditionalFieldEntity, FormCollectionAdditionalFieldEntity)
                : this._mapper.map(field, DataRegisterAdditionalFieldTenancyEntity, FormCollectionAdditionalFieldTenancyEntity);

            newField.formVersionId = formVersionId;

            const items = additionalFieldsGroup?.[field.transactionId];
            items.forEach((item) => {
                newField.id = v4();

                // newField.autoPopulateContexts.forEach((context) => {
                //     context.id = v4();
                //     context.additionalFieldId = newField.id;
                // });

                newField.formCollectionItemIdentityId = item.identityId;
                newAdditionalFields.push(_.cloneDeep(newField));
            });
        });

        try {
            if (!ignoreSave) {
                await formAdditionalFieldRepo.save(newAdditionalFields, TYPEORM_SAVE_OPTIONS);
            }
            return newAdditionalFields;
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    private async handleDeleteCriteriaAdditionalField({
        deleteFormCollectionItemIdentityIds,
        formVersionId,
        formAdditionalFieldRepo,
    }: {
        accountId?: string;
        deleteFormCollectionItemIdentityIds: string[];
        formVersionId: string;
        formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity>;
    }) {
        try {
            await formAdditionalFieldRepo.softDelete({
                formCollectionItemIdentityId: In(deleteFormCollectionItemIdentityIds),
                formVersionId,
            });
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    private async handleDeleteFormCollection({
        formCollectionIds,
        formCollectionIdentityIds,
        formVersionId,
        formCollectionItemRepo,
        formAdditionalFieldRepo,
        generalAutoPopulateSettingRepo,
        generalAutoPopulateExtraConfigRepo,
        contextMappingRepo,
        automationMappingRepo,
    }: {
        formCollectionIds: string[];
        formCollectionIdentityIds: string[];
        formVersionId: string;
        formCollectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;
        formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity>;
        generalAutoPopulateSettingRepo: Repository<GeneralAutoPopulateSettingEntity>;
        generalAutoPopulateExtraConfigRepo: Repository<GeneralAutoPopulateExtraConfigEntity>;
        contextMappingRepo: Repository<FormContextMappingEntity>;
        automationMappingRepo: Repository<FormCollectionAutomationMappingEntity>;
    }) {
        const formCollectionItems = await formCollectionItemRepo.find({
            where: {
                formCollectionId: In(formCollectionIds),
            },
            select: ['id', 'identityId'],
        });

        const formCollectionItemIdentityIds = _.uniq(_.compact((formCollectionItems ?? []).map((item) => item.identityId)));

        if (!formCollectionItemIdentityIds?.length) {
            return;
        }

        try {
            await formAdditionalFieldRepo.softDelete({
                formCollectionItemIdentityId: In(formCollectionItemIdentityIds),
                formVersionId,
            });

            if (_.compact(formCollectionIdentityIds)?.length) {
                await contextMappingRepo.softDelete({
                    collectionIdentityId: In(_.compact(formCollectionIdentityIds)),
                    formVersionId,
                });

                await automationMappingRepo.softDelete({
                    collectionIdentityId: In(_.compact(formCollectionIdentityIds)),
                    formVersionId,
                });
            }

            const generalAps = await generalAutoPopulateSettingRepo.findBy({
                builderVersionId: formVersionId,
                itemIdentityId: In(formCollectionItemIdentityIds),
                builderType: AutoPopulateBuilderTypeEnum.FormCollectionItem,
            });

            const generalApIds = generalAps.map((item) => item.id);

            if (generalApIds.length) {
                await generalAutoPopulateExtraConfigRepo.softDelete({
                    generalAutoPopulateSettingId: In(generalApIds),
                });

                await generalAutoPopulateSettingRepo.softDelete({
                    id: In(generalApIds),
                });
            }
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    public async handleCaptureFormCollectionTransaction({
        formCollectionItemIds,
        formCollectionItemRepo,
        dataRegisterFieldRepo,
        dataRegisterTransactionRepo,
        dataRegisterTransactionFieldRepo,
        formCollectionItemTransactionFieldRepo,
        collectionLayoutRepo,
        collectionZoneFieldRepo,
        collectionZoneRepo,
        formCollectionItemEntities,
        ignoreSave,
        formVersionId,
        dataRegisterRepo,
    }: {
        formCollectionItemIds: string[];
        formCollectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;

        dataRegisterRepo: Repository<DataRegisterEntity | DataRegisterTenancyEntity>;
        dataRegisterFieldRepo: Repository<DataRegisterFieldEntity | DataRegisterFieldTenancyEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>;
        formCollectionItemTransactionFieldRepo: Repository<CollectionTransactionEntity | CollectionTransactionTenancyEntity>;

        collectionLayoutRepo?: Repository<CollectionLayoutTenancyEntity | CollectionLayoutEntity>;
        collectionZoneRepo?: Repository<CollectionLayoutZoneTenancyEntity | CollectionLayoutZoneEntity>;
        collectionZoneFieldRepo?: Repository<CollectionLayoutZoneFieldTenancyEntity | CollectionLayoutZoneFieldEntity>;

        formCollectionItemEntities?: FormCollectionItemEntity[] | FormCollectionItemTenancyEntity[];
        ignoreSave?: boolean;
        formVersionId?: string;
    }): Promise<CollectionTransactionEntity[]> {
        const uniqFormCollectionItemIds = _.uniq(formCollectionItemIds);

        if (!uniqFormCollectionItemIds?.length && !formCollectionItemEntities?.length) {
            return [];
        }

        const formCollectionItems = formCollectionItemEntities?.length
            ? formCollectionItemEntities
            : await formCollectionItemRepo.find({
                  where: {
                      id: In(uniqFormCollectionItemIds),
                  },
                  select: ['id', 'dataRegisterTransactionId', 'identityId', 'setting'],
              });

        if (!formCollectionItems.length) {
            return [];
        }

        const formCollectionItemsGroup = _.groupBy(formCollectionItems, 'dataRegisterTransactionId');
        const dataRegisterTransactionIds = Object.keys(formCollectionItemsGroup);

        const [dataTransactionFields, dataRegisterTransactions] = await Promise.all([
            dataRegisterTransactionFieldRepo.find({
                where: { dataRegisterTransactionId: In(dataRegisterTransactionIds) },
            }),
            dataRegisterTransactionIds?.length
                ? dataRegisterTransactionRepo.find({
                      where: { id: In(dataRegisterTransactionIds) },
                  })
                : Promise.resolve([]),
        ]);

        let dataRegisterVersionIds = [];
        let dataRegisterIds = [];

        for (const drt of dataRegisterTransactions) {
            dataRegisterIds.push(drt.dataRegisterId);
            dataRegisterVersionIds.push(drt.dataRegisterVersionId);
        }

        dataRegisterIds = _.uniq(dataRegisterIds);
        dataRegisterVersionIds = _.uniq(dataRegisterVersionIds);

        const dataRegisterFields = await this._getActiveFields({
            dataRegisterIds: dataRegisterIds,
            dataRegisterRepo: dataRegisterRepo,
            dataRegisterFieldRepo: dataRegisterFieldRepo,
        });

        const createFormCollectionItemTransactionFields: CollectionTransactionEntity[] = [];

        await formCollectionItemTransactionFieldRepo.softDelete({
            formCollectionItemId: In(uniqFormCollectionItemIds),
        });

        formCollectionItems.forEach((formCollectionItem) => {
            if ((formCollectionItem?.setting as any)?.configuration?.hasGroup) {
                return;
            }

            const cloneTransactionFields: Array<
                (DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity) & {
                    configuration?: Record<string, any>;
                }
            > = _.cloneDeep(dataTransactionFields);

            if (dataRegisterFields.length) {
                dataRegisterFields.forEach((field) => {
                    const dataRegisterTransaction = dataRegisterTransactions.find(
                        (transaction) =>
                            transaction.dataRegisterVersionId === field.dataRegisterVersionId &&
                            formCollectionItem?.dataRegisterTransactionId === transaction.id,
                    );

                    const entity: DataRegisterTransactionFieldEntity & {
                        configuration?: Record<string, any>;
                    } = {
                        fieldId: field.fieldId,
                        fieldValue: null,
                        fieldOptionIds: [],
                        fieldType: field.type,
                        dataRegisterTransactionId: dataRegisterTransaction?.id ?? formCollectionItem?.dataRegisterTransactionId,
                        validationValue: null,
                        configuration: {
                            ...field?.configuration,
                            type: field?.type,
                        },
                    };

                    cloneTransactionFields.push(entity);
                });
            }

            let _formCollectionItemTransactionFields = cloneTransactionFields.filter(
                (field) => field.dataRegisterTransactionId === formCollectionItem.dataRegisterTransactionId,
            );

            _formCollectionItemTransactionFields = _.uniqWith(
                _formCollectionItemTransactionFields,
                (a, b) => a.fieldId === b.fieldId && a.dataRegisterTransactionId === b.dataRegisterTransactionId,
            );

            if (!_formCollectionItemTransactionFields.length) {
                return;
            }

            _formCollectionItemTransactionFields.forEach((field: any) => {
                createFormCollectionItemTransactionFields.push({
                    ...field,
                    id: v4(),
                    formCollectionItemIdentityId: formCollectionItem?.identityId,
                    formCollectionItemId: formCollectionItem?.id,
                    formVersionId: formVersionId,
                });
            });
        });

        try {
            if (createFormCollectionItemTransactionFields?.length && !ignoreSave) {
                await formCollectionItemTransactionFieldRepo.save(createFormCollectionItemTransactionFields, TYPEORM_SAVE_OPTIONS);
            }

            if (
                !ignoreSave &&
                collectionLayoutRepo &&
                collectionZoneFieldRepo &&
                collectionZoneRepo &&
                formVersionId &&
                formCollectionItems?.length
            ) {
                await this.captureCollectionLayoutsOnRelease({
                    collectionItemEntities: formCollectionItems,
                    collectionLayoutRepo,
                    collectionZoneFieldRepo,
                    collectionZoneRepo,
                    formVersionId,
                });
            }

            return createFormCollectionItemTransactionFields ?? [];
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    public updateFormCollections(
        formCollections: FormCollectionEntity[],
        dataRegisterTransactions: (DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity)[],
        dataRegisterVersions: (DataRegisterVersionEntity | DataRegisterVersionTenancyEntity)[],
        dataRegisters: (DataRegisterEntity | DataRegisterTenancyEntity)[],
    ) {
        const newFormCollections = [];

        const processItem = (item) => {
            const transaction = dataRegisterTransactions.find((tr) => tr.id === item.dataRegisterTransactionId);

            if (transaction?.deletedAt) return null;

            if (item.children?.length) {
                item.children = item.children.map(processItem).filter(Boolean);
            }

            const dataRegisterVersion = transaction
                ? dataRegisterVersions.find((drv) => drv.id === transaction.dataRegisterVersionId)
                : null;

            if (!transaction || !dataRegisterVersion) return item;

            const viewFieldIds = dataRegisterVersion.displayAttributes;

            item.name = viewFieldIds.length ? this._generateItemName(viewFieldIds, dataRegisterVersion, transaction) : item.name;

            return item;
        };

        formCollections.forEach((collection) => {
            const _dataRegister = dataRegisters.find((d) => d.id === collection.dataRegisterId);
            if (_dataRegister?.deletedAt) {
                return;
            }

            collection.formCollectionItems = collection.formCollectionItems.map(processItem).filter(Boolean);
            newFormCollections.push(collection);
        });

        return newFormCollections;
    }

    public async captureCollectionLayoutsOnRelease(params: {
        formVersionId: string;
        collectionItemEntities: FormCollectionItemEntity[];
        collectionLayoutRepo: Repository<CollectionLayoutTenancyEntity | CollectionLayoutEntity>;
        collectionZoneRepo: Repository<CollectionLayoutZoneTenancyEntity | CollectionLayoutZoneEntity>;
        collectionZoneFieldRepo: Repository<CollectionLayoutZoneFieldTenancyEntity | CollectionLayoutZoneFieldEntity>;
        ignoreSave?: boolean;
    }) {
        const { collectionItemEntities, formVersionId, collectionLayoutRepo, collectionZoneFieldRepo, collectionZoneRepo, ignoreSave } =
            params;

        const registerTransactionIds = collectionItemEntities.map((c) => c.dataRegisterTransactionId);

        const collectionLayouts = await collectionLayoutRepo.find({
            where: {
                registerTransactionId: In(registerTransactionIds),
                formVersionId: IsNull(),
                collectionItemIdentityId: IsNull(),
            },
            relations: {
                layoutZones: {
                    layoutFields: true,
                },
            },
        });

        const result = await Promise.all(
            collectionItemEntities.map((collectionItem) => {
                const layout = collectionLayouts.find((l) => l.registerTransactionId === collectionItem.dataRegisterTransactionId);
                return layout
                    ? this.cloneLayout({
                          refLayout: layout,
                          formVersionId,
                          collectionItemIdentityId: collectionItem?.identityId,
                          collectionLayoutRepo,
                          collectionZoneFieldRepo,
                          collectionZoneRepo,
                          options: {
                              ignoreSave,
                          },
                      })
                    : Promise.resolve<CollectionLayoutEntity | CollectionLayoutTenancyEntity>(null);
            }),
        );

        return result?.filter(Boolean) ?? [];
    }

    private async cloneLayout(params: {
        refLayout: CollectionLayoutEntity | CollectionLayoutTenancyEntity;
        formVersionId: string;
        collectionItemIdentityId: string;
        collectionLayoutRepo: Repository<CollectionLayoutTenancyEntity | CollectionLayoutEntity>;
        collectionZoneRepo: Repository<CollectionLayoutZoneTenancyEntity | CollectionLayoutZoneEntity>;
        collectionZoneFieldRepo: Repository<CollectionLayoutZoneFieldTenancyEntity | CollectionLayoutZoneFieldEntity>;
        options?: {
            ignoreSave?: boolean;
        };
    }): Promise<CollectionLayoutEntity | CollectionLayoutTenancyEntity> {
        const {
            refLayout,
            formVersionId,
            collectionItemIdentityId,
            collectionLayoutRepo,
            collectionZoneRepo,
            collectionZoneFieldRepo,
            options,
        } = params;

        const { ignoreSave = false } = options || {};

        let id = v4();
        const newLayout = collectionLayoutRepo.create({
            name: refLayout.name,
            registerTransactionId: refLayout.registerTransactionId,
            id,
            identityId: id,
            formVersionId,
            collectionItemIdentityId,
        });

        let layout: CollectionLayoutEntity | CollectionLayoutTenancyEntity;

        if (!ignoreSave) {
            layout = await collectionLayoutRepo.save(newLayout);
        } else {
            layout = newLayout;
        }

        if (refLayout) {
            //copy zone
            const refZones = refLayout.layoutZones || [];
            const refZoneFields: Array<CollectionLayoutZoneFieldEntity | CollectionLayoutZoneFieldTenancyEntity> = [];
            const mappingZoneIdentityId: Record<string, string> = {};
            const newZones: Array<CollectionLayoutZoneEntity | CollectionLayoutZoneTenancyEntity> = refZones.map(
                (zone: CollectionLayoutZoneTenancyEntity | CollectionLayoutZoneEntity) => {
                    const zoneId = v4();
                    const copyLayoutFields = (zone.layoutFields || []).map(
                        (lf: CollectionLayoutZoneFieldTenancyEntity | CollectionLayoutZoneFieldEntity) => {
                            const { id, updatedAt, updatedBy, createdAt, createdBy, ...contentField } = lf;
                            const newId = v4();
                            return collectionZoneFieldRepo.create({
                                ...contentField,
                                layoutZoneId: zoneId,
                                layoutId: layout.id,
                                fieldId: lf.fieldId,
                                config: lf.config
                                    ? _.isArray(lf.config)
                                        ? lf.config
                                        : {
                                              ...lf.config,
                                              dataGrid: lf.config?.dataGrid ? { ...lf.config?.dataGrid, i: lf.fieldId } : undefined,
                                          }
                                    : undefined,
                                identityId: newId,
                                id: newId,
                            });
                        },
                    );
                    refZoneFields.push(...copyLayoutFields);

                    mappingZoneIdentityId[zone.identityId ?? zone.id] = zoneId;

                    return collectionZoneRepo.create({
                        ...zone,
                        id: zoneId,
                        config: zone.config
                            ? _.isArray(zone.config)
                                ? zone.config
                                : {
                                      ...zone.config,
                                      dataGrid: zone.config?.dataGrid ? { ...zone.config.dataGrid, i: zoneId } : undefined,
                                  }
                            : undefined,
                        layoutId: layout.id,
                        identityId: zoneId,
                    });
                },
            );

            newZones.forEach((zone) => {
                if (zone.parentIdentityId && mappingZoneIdentityId[zone.parentIdentityId]) {
                    zone.parentIdentityId = mappingZoneIdentityId[zone.parentIdentityId];
                }
            });

            //copy zone field
            if (!ignoreSave) {
                await collectionZoneRepo.save(newZones);
                await collectionZoneFieldRepo.save(refZoneFields);
            } else {
                newZones.forEach((zone) => {
                    zone.layoutFields = refZoneFields.filter((field) => field.layoutZoneId === zone.id);
                });
                layout.layoutZones = newZones;
            }
        }

        return layout;
    }

    private _generateItemName(
        viewFieldIds: string[],
        dataRegisterVersion: DataRegisterVersionEntity | DataRegisterVersionTenancyEntity,
        transaction: DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity,
    ) {
        return viewFieldIds
            .map((viewFieldId) => {
                const textField = dataRegisterVersion.fields.find((field) => field.fieldId === viewFieldId);
                return textField ? transaction.transactionFields.find((field) => field.fieldId === textField.fieldId)?.fieldValue : null;
            })
            .filter(Boolean)
            .join('-');
    }

    public async fetchDataRegisterVersions(
        dataRegisterVersionIds: string[],
        dataRegisterVersionRepo: Repository<DataRegisterVersionEntity | DataRegisterVersionTenancyEntity>,
    ) {
        return await dataRegisterVersionRepo
            .createQueryBuilder('version')
            .leftJoinAndSelect('version.fields', 'fields')
            .where(`version.id In(:...ids)`, { ids: dataRegisterVersionIds })
            .andWhere(`fields.type = :type`, { type: FormFieldTypeEnum.Text })
            .withDeleted()
            .getMany();
    }

    public async fetchDataRegisters(
        dataRegisterIds: string[],
        dataRegisterRepo: Repository<DataRegisterEntity | DataRegisterTenancyEntity>,
    ) {
        return await dataRegisterRepo.find({
            where: {
                id: In(dataRegisterIds),
            },
            select: ['id', 'deletedAt', 'activeVersionId'],
            withDeleted: true,
        });
    }

    public extractDataRegisterTransactionIds(formCollections: FormCollectionEntity[]) {
        const dataRegisterTransactionIds: string[] = [];
        formCollections.forEach((collection) => {
            collection.formCollectionItems?.forEach((item) => {
                if (item.dataRegisterTransactionId) {
                    dataRegisterTransactionIds.push(item.dataRegisterTransactionId);
                }

                if (item.children?.length) {
                    item.children.forEach((child) => [dataRegisterTransactionIds.push(child.dataRegisterTransactionId)]);
                }
            });
        });

        return _.uniq(dataRegisterTransactionIds);
    }

    public async fetchDataRegisterTransactions(
        dataRegisterTransactionIds: string[],
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>,
        dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>,
    ) {
        const [transactions, transactionFields] = await Promise.all([
            dataRegisterTransactionRepo.find({
                where: { id: In(dataRegisterTransactionIds) },
            }),
            dataRegisterTransactionFieldRepo.find({
                where: { dataRegisterTransactionId: In(dataRegisterTransactionIds) },
            }),
        ]);

        if (!transactions?.length) {
            return [];
        }

        transactions.forEach((transaction) => {
            transaction.transactionFields = transactionFields.filter((field) => field.dataRegisterTransactionId === transaction.id);
        });

        return transactions;
    }

    private async _getCollectionLayout(
        collectionItemIdentityIds: string[],
        formVersionId: string,
        collectionLayoutRepo: Repository<CollectionLayoutEntity | CollectionLayoutTenancyEntity>,
    ) {
        if (!collectionItemIdentityIds?.length || !formVersionId) {
            return [];
        }

        const collectionLayouts = await collectionLayoutRepo.find({
            where: {
                collectionItemIdentityId: In(collectionItemIdentityIds),
                formVersionId,
            },
            select: ['id', 'collectionItemIdentityId'],
        });

        return collectionLayouts;
    }

    private async _getActiveFields({
        dataRegisterIds,
        dataRegisterRepo,
        dataRegisterFieldRepo,
    }: {
        dataRegisterIds: string[];
        dataRegisterRepo: Repository<DataRegisterEntity | DataRegisterTenancyEntity>;
        dataRegisterFieldRepo: Repository<DataRegisterFieldEntity | DataRegisterFieldTenancyEntity>;
    }) {
        const activeVersions = await dataRegisterRepo.find({
            where: { id: In(dataRegisterIds) },
            select: ['id', 'activeVersionId'],
        });

        const activeVersionIds = activeVersions.map(({ activeVersionId }) => activeVersionId);
        if (!activeVersionIds.length) return [];

        const newActiveFields =
            (await dataRegisterFieldRepo
                .createQueryBuilder('drf')
                .where('drf.dataRegisterVersionId IN (:...activeVersionIds)', { activeVersionIds })
                .getMany()) ?? [];

        newActiveFields.forEach((field) => {
            const activeVersion = activeVersions.find((version) => version.activeVersionId === field.dataRegisterVersionId);

            if (activeVersion) {
                field.dataRegisterVersion = Object.assign(new DataRegisterVersionEntity(), {
                    dataRegisterId: activeVersion.id,
                    id: field.dataRegisterVersionId,
                });
            }
        });

        return newActiveFields;
    }
}
