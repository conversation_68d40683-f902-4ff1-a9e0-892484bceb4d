import { PROVIDER_KEYS } from '@/database/src/constants/providers';
import { Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ApiConfigurationDataService } from '../../../api/services/data-services/api-configuration-data.service';
import { VersioningAutomationVersionPort } from '../../../automation/domain/ports/out/versioning-automation-version.port';
import { FormCollectionListenerService } from '../listeners/form-collection.listener.service';
import { FormVersionMapping } from '../mapping/form-version.mapping';
import { FormCollectionDataService } from './form-collection.data.service';
import { FormContentService } from './form-content.service';
import { FormVersionDataService } from './form-version.data.service';
import { FormVersionLoggerService } from './form-version.logger.service';

@Injectable()
export class FormVersionDataFactoryService {
    constructor(
        private readonly _versionLogger: FormVersionLoggerService,
        private readonly _formContentService: FormContentService,
        private readonly _formVersionMapping: FormVersionMapping,
        private readonly _formCollectionDataService: FormCollectionDataService,
        private readonly _formCollectionListenerService: FormCollectionListenerService,

        @Inject(PROVIDER_KEYS.DATA_SOURCE)
        private readonly _dataSource: DataSource,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _tenancyDataSource: DataSource,
        private readonly _versioningPort: VersioningAutomationVersionPort,
        private readonly _apiConfigurationDataService: ApiConfigurationDataService,
    ) {}

    create(params: { isAccount: boolean }): FormVersionDataService {
        const dataSource = params.isAccount ? this._tenancyDataSource : this._dataSource;

        return new FormVersionDataService(
            this._versionLogger,
            this._formContentService,
            this._formVersionMapping,
            this._formCollectionDataService,
            this._formCollectionListenerService,
            this._versioningPort,
            this._apiConfigurationDataService,

            // { ...repositories },
        );
    }
}
