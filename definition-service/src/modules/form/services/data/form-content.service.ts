import { FormCollectionAutoPopulateContextEntity } from '@/database/src/entities/public/form-collection-auto-populate-contexts.public.entity';
import { FormCollectionContextMappingEntity } from '@/database/src/entities/public/form-collection-context-mapping.public.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '@/database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionAutoPopulateContextTenancyEntity } from '@/database/src/entities/tenancy/form-collection-auto-populate-contexts.tenancy.entity';
import { FormCollectionContextMappingTenancyEntity } from '@/database/src/entities/tenancy/form-collection-context-mapping.tenancy.entity';
import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, In } from 'typeorm';
import { AutomationVersionEntity } from '../../../../database/src/entities/public/automation-version.public.entity';
import { AutomationEntity } from '../../../../database/src/entities/public/automation.public.entity';
import { CollectionLayoutZoneFieldEntity } from '../../../../database/src/entities/public/collection-layout-zone-field.public.entity';
import { CollectionLayoutZoneEntity } from '../../../../database/src/entities/public/collection-layout-zone.public.entity';
import { CollectionLayoutEntity } from '../../../../database/src/entities/public/collection-layout.public.entity';
import { CollectionTransactionEntity } from '../../../../database/src/entities/public/collection-transaction.public.entity';
import { DataRegisterAdditionalFieldEntity } from '../../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { DataRegisterFieldEntity } from '../../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../../database/src/entities/public/data-registers.public.entity';
import { FormAutoPopulateSettingEntity } from '../../../../database/src/entities/public/form-auto-populate-setting.public.entity';
import { FormChangeLogEntity } from '../../../../database/src/entities/public/form-change-log.public.entity';
import { FormCollectionAdditionalFieldEntity } from '../../../../database/src/entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionAutomationMappingEntity } from '../../../../database/src/entities/public/form-collection-automation-mapping.public.entity';
import { FormCollectionItemEntity } from '../../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../../database/src/entities/public/form-collection.public.entity';
import { FormContextMappingEntity } from '../../../../database/src/entities/public/form-context-mapping.public.entity';
import { FormFieldEntity } from '../../../../database/src/entities/public/form-field.public.entity';
import { FormLayoutZoneFieldEntity } from '../../../../database/src/entities/public/form-layout-zone-field.public.entity';
import { FormLayoutZoneEntity } from '../../../../database/src/entities/public/form-layout-zone.public.entity';
import { FormLayoutEntity } from '../../../../database/src/entities/public/form-layout.public.entity';
import { FormManualEventEntity } from '../../../../database/src/entities/public/form-manual-event.public.entity';
import { FormRelatedEntity } from '../../../../database/src/entities/public/form-related.public.entity';
import { FormVersionCommentEntity } from '../../../../database/src/entities/public/form-version-comment.public.entity';
import { FormVersionEntity } from '../../../../database/src/entities/public/form-version.public.entity';
import { FormViewItemEntity } from '../../../../database/src/entities/public/form-view-item.public.entity';
import { FormEntity } from '../../../../database/src/entities/public/form.public.entity';
import { GeneralAutoPopulateExtraConfigEntity } from '../../../../database/src/entities/public/general-auto-populate-extra-config.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { RollupDependencyEntity } from '../../../../database/src/entities/public/roll-up-dependency.public.entity';
import { StageAccessControlEntity } from '../../../../database/src/entities/public/stage-access-controls.public.entity';
import { StageDecisionEntity } from '../../../../database/src/entities/public/stage-decision.public.entity';
import { StageRoleAccessControlEntity } from '../../../../database/src/entities/public/stage-role-access-controls.public.entity';
import { StageRoleEntity } from '../../../../database/src/entities/public/stage-role.public.entity';
import { StageTransitionEntity } from '../../../../database/src/entities/public/stage-transition.public.entity';
import { StageEntity } from '../../../../database/src/entities/public/stage.public.entity';
import { AutomationVersionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { AutomationTenancyEntity } from '../../../../database/src/entities/tenancy/automation.tenancy.entity';
import { CollectionLayoutZoneFieldTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout-zone-field.tenancy.entity';
import { CollectionLayoutZoneTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout-zone.tenancy.entity';
import { CollectionLayoutTenancyEntity } from '../../../../database/src/entities/tenancy/collection-layout.tenancy.entity';
import { CollectionTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormChangeLogTenancyEntity } from '../../../../database/src/entities/tenancy/form-change-log.tenancy.entity';
import { FormCollectionAutomationMappingTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-automation-mapping.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormContextMappingTenancyEntity } from '../../../../database/src/entities/tenancy/form-context-mapping.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormLayoutZoneFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-layout-zone-field.tenancy.entity';
import { FormLayoutZoneTenancyEntity } from '../../../../database/src/entities/tenancy/form-layout-zone.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../../../database/src/entities/tenancy/form-layout.tenancy.entity';
import { FormManualEventTenancyEntity } from '../../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { FormVersionCommentTenancyEntity } from '../../../../database/src/entities/tenancy/form-version-comment.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormViewItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-view-item.tenancy.entity';
import { FormViewTenancyEntity } from '../../../../database/src/entities/tenancy/form-view.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from '../../../../database/src/entities/tenancy/general-auto-populate-extra-config.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { RoleToFormViewTenancyEntity } from '../../../../database/src/entities/tenancy/role-to-form-view.tenancy.entity';
import { RoleTenancyEntity } from '../../../../database/src/entities/tenancy/role.tenancy.entity';
import { RollupDependencyTenancyEntity } from '../../../../database/src/entities/tenancy/roll-up-dependency.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageDecisionTenancyEntity } from '../../../../database/src/entities/tenancy/stage-decision.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTransitionTenancyEntity } from '../../../../database/src/entities/tenancy/stage-transition.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import {
    CollectionTransactionType,
    DataRegisterTransactionType,
    DataRegisterType,
    FormAutoPopulateSettingType,
    FormChangeLogType,
    FormCollectionAdditionalFieldType,
    FormCollectionAutoPopulateContextType,
    FormCollectionContextMappingType,
    FormCollectionItemType,
    FormCollectionType,
    FormCommentType,
    FormFieldType,
    FormLayoutType,
    FormLayoutZoneFieldType,
    FormLayoutZoneType,
    FormRelatedType,
    FormRoleViewsType,
    FormRollupDependencyType,
    FormStageAccessControlType,
    FormStageDecisionType,
    FormStageRoleAccessControlType,
    FormStageRoleType,
    FormStageTransitionType,
    FormStageType,
    FormType,
    FormVersionContentRepositoriesType,
    FormVersionContentType,
    FormVersionType,
    FormViewItemType,
    FormViewType,
} from '../../types/form-content.type';
import { ApiTenancyEntity } from '../../../../database/src/entities/tenancy/api.tenancy.entity';
import { ApiVersionTenancyEntity } from '../../../../database/src/entities/tenancy/api-version.tenancy.entity';
import { ApiVersionEndpointTenancyEntity } from '../../../../database/src/entities/tenancy/api-version-endpoint.tenancy.entity';
import { ApiVersionCommentTenancyEntity } from '../../../../database/src/entities/tenancy/api-version-comment.tenancy.entity';

@Injectable()
export class FormContentService {
    public getFormContentRepositories({
        manager,
        isAccount,
    }: {
        manager: DataSource | EntityManager;
        isAccount: boolean;
    }): FormVersionContentRepositoriesType {
        return {
            formRepo: manager.getRepository<FormType>(isAccount ? FormTenancyEntity : FormEntity),
            formVersionRepo: manager.getRepository<FormVersionType>(isAccount ? FormVersionTenancyEntity : FormVersionEntity),
            fieldRepo: manager.getRepository<FormFieldType>(isAccount ? FormFieldTenancyEntity : FormFieldEntity),
            collectionRepo: manager.getRepository<FormCollectionType>(isAccount ? FormCollectionTenancyEntity : FormCollectionEntity),
            collectionItemRepo: manager.getRepository<FormCollectionItemType>(
                isAccount ? FormCollectionItemTenancyEntity : FormCollectionItemEntity,
            ),
            collectionAdditionalFieldRepo: manager.getRepository<FormCollectionAdditionalFieldType>(
                isAccount ? FormCollectionAdditionalFieldTenancyEntity : FormCollectionAdditionalFieldEntity,
            ),
            collectionAutoPopulateContextRepo: manager.getRepository<FormCollectionAutoPopulateContextType>(
                isAccount ? FormCollectionAutoPopulateContextTenancyEntity : FormCollectionAutoPopulateContextEntity,
            ),
            collectionContextMappingRepo: manager.getRepository<FormCollectionContextMappingType>(
                isAccount ? FormCollectionContextMappingTenancyEntity : FormCollectionContextMappingEntity,
            ),
            formRelatedRepo: manager.getRepository<FormRelatedType>(isAccount ? FormRelatedTenancyEntity : FormRelatedEntity),
            stageRepo: manager.getRepository<FormStageType>(isAccount ? StageTenancyEntity : StageEntity),
            stageDecisionRepo: manager.getRepository<StageDecisionTenancyEntity | StageDecisionEntity>(
                isAccount ? StageDecisionTenancyEntity : StageDecisionEntity,
            ),
            stageTransitionRepo: manager.getRepository<FormStageTransitionType>(
                isAccount ? StageTransitionTenancyEntity : StageTransitionEntity,
            ),
            stageRoleRepo: manager.getRepository<FormStageRoleType>(isAccount ? StageRoleTenancyEntity : StageRoleEntity),
            stageAccessControlRepo: manager.getRepository<FormStageAccessControlType>(
                isAccount ? StageAccessControlTenancyEntity : StageAccessControlEntity,
            ),
            stageRoleAccessControlRepo: manager.getRepository<FormStageRoleAccessControlType>(
                isAccount ? StageRoleAccessControlTenancyEntity : StageRoleAccessControlEntity,
            ),
            formLayoutRepo: manager.getRepository<FormLayoutType>(isAccount ? FormLayoutTenancyEntity : FormLayoutEntity),
            formLayoutZoneRepo: manager.getRepository<FormLayoutZoneType>(isAccount ? FormLayoutZoneTenancyEntity : FormLayoutZoneEntity),
            formLayoutZoneFieldRepo: manager.getRepository<FormLayoutZoneFieldType>(
                isAccount ? FormLayoutZoneFieldTenancyEntity : FormLayoutZoneFieldEntity,
            ),
            formViewRepo: manager.getRepository<FormViewType>(isAccount ? FormViewTenancyEntity : FormViewTenancyEntity),
            formCommentRepo: manager.getRepository<FormCommentType>(isAccount ? FormVersionCommentTenancyEntity : FormVersionCommentEntity),
            roleViewsRepo: manager.getRepository<FormRoleViewsType>(RoleToFormViewTenancyEntity),
            rollupDependencyRepo: manager.getRepository<FormRollupDependencyType>(
                isAccount ? RollupDependencyTenancyEntity : RollupDependencyEntity,
            ),
            changeLogRepo: manager.getRepository<FormChangeLogType>(isAccount ? FormChangeLogTenancyEntity : FormChangeLogEntity),
            dataRegisterRepo: manager.getRepository<DataRegisterType>(isAccount ? DataRegisterTenancyEntity : DataRegisterEntity),
            dataRegisterTransactionRepo: manager.getRepository<DataRegisterTransactionType>(
                isAccount ? DataRegisterTransactionTenancyEntity : DataRegisterTransactionEntity,
            ),
            dataRegisterVersionRepo: manager.getRepository<DataRegisterVersionEntity>(
                isAccount ? DataRegisterVersionTenancyEntity : DataRegisterVersionEntity,
            ),
            dataRegisterTransactionFieldRepo: manager.getRepository<DataRegisterTransactionFieldEntity>(
                isAccount ? DataRegisterTransactionFieldTenancyEntity : DataRegisterTransactionFieldEntity,
            ),
            dataRegisterAdditionalFieldRepo: manager.getRepository<DataRegisterAdditionalFieldEntity>(
                isAccount ? DataRegisterAdditionalFieldTenancyEntity : DataRegisterAdditionalFieldEntity,
            ),
            autoPopulateSettingRepo: manager.getRepository<FormAutoPopulateSettingType>(
                isAccount ? FormAutoPopulateSettingTenancyEntity : FormAutoPopulateSettingEntity,
            ),
            collectionTransactionRepo: manager.getRepository<CollectionTransactionType>(
                isAccount ? CollectionTransactionTenancyEntity : CollectionTransactionEntity,
            ),
            formFieldRepo: manager.getRepository<FormFieldType>(isAccount ? FormFieldTenancyEntity : FormFieldEntity),
            roleRepo: manager.getRepository(RoleTenancyEntity),
            generalAutoPopulateRepo: manager.getRepository<GeneralAutoPopulateSettingEntity>(
                isAccount ? GeneralAutoPopulateSettingTenancyEntity : GeneralAutoPopulateSettingEntity,
            ),
            generalAutoPopulateExtraConfigRepo: manager.getRepository<GeneralAutoPopulateExtraConfigEntity>(
                isAccount ? GeneralAutoPopulateExtraConfigTenancyEntity : GeneralAutoPopulateExtraConfigEntity,
            ),
            formCollectionAdditionalFieldRepo: manager.getRepository<FormCollectionAdditionalFieldEntity>(
                isAccount ? FormCollectionAdditionalFieldTenancyEntity : FormCollectionAdditionalFieldEntity,
            ),
            formCollectionItemRepo: manager.getRepository<FormCollectionItemEntity>(
                isAccount ? FormCollectionItemTenancyEntity : FormCollectionItemEntity,
            ),
            dataRegisterFieldRepo: manager.getRepository<DataRegisterFieldEntity>(
                isAccount ? DataRegisterFieldTenancyEntity : DataRegisterFieldEntity,
            ),
            contextMappingsRepo: manager.getRepository<FormContextMappingEntity>(
                isAccount ? FormContextMappingTenancyEntity : FormContextMappingEntity,
            ),
            automationMappingsRepo: manager.getRepository(
                isAccount ? FormCollectionAutomationMappingTenancyEntity : FormCollectionAutomationMappingEntity,
            ),
            automationRepo: manager.getRepository<AutomationEntity>(isAccount ? AutomationTenancyEntity : AutomationEntity),
            automationVersionRepo: manager.getRepository<AutomationVersionEntity>(
                isAccount ? AutomationVersionTenancyEntity : AutomationVersionEntity,
            ),
            formViewItemRepo: manager.getRepository<FormViewItemType>(isAccount ? FormViewItemTenancyEntity : FormViewItemEntity),
            manualEventRepo: manager.getRepository(isAccount ? FormManualEventTenancyEntity : FormManualEventEntity),
            collectionLayoutRepo: manager.getRepository(isAccount ? CollectionLayoutTenancyEntity : CollectionLayoutEntity),
            collectionLayoutZoneRepo: manager.getRepository(isAccount ? CollectionLayoutZoneTenancyEntity : CollectionLayoutZoneEntity),
            collectionLayoutZoneFieldRepo: manager.getRepository(
                isAccount ? CollectionLayoutZoneFieldTenancyEntity : CollectionLayoutZoneFieldEntity,
            ),
            apiBuilderRepo: manager.getRepository(ApiTenancyEntity),
            apiVersionRepo: manager.getRepository(ApiVersionTenancyEntity),
            apiVersionEndpointRepo: manager.getRepository(ApiVersionEndpointTenancyEntity),
            apiVersionCommentRepo: manager.getRepository(ApiVersionCommentTenancyEntity),
        };
    }

    public async getFormVersionContent({
        formId,
        formVersionId,
        isAccount,
        fieldRepo,
        collectionRepo,
        collectionItemRepo,
        collectionAdditionalFieldRepo,
        collectionContextMappingRepo,
        collectionAutoPopulateContextRepo,
        formRelatedRepo,
        stageRepo,
        stageDecisionRepo,
        stageTransitionRepo,
        stageRoleRepo,
        stageAccessControlRepo,
        stageRoleAccessControlRepo,
        formLayoutRepo,
        formLayoutZoneRepo,
        formLayoutZoneFieldRepo,
        formViewRepo,
        formRepo,
        formVersionRepo,
        roleViewsRepo,
        generalAutoPopulateRepo,
        generalAutoPopulateExtraConfigRepo,
        form: formParam,
        formVersion: formVersionParam,
        autoPopulateSettingRepo,
        contextMappingsRepo,
        automationMappingsRepo,
        formViewItemRepo,
        manualEventRepo,
        withDeleted = false,
    }: {
        formId: string;
        isAccount: boolean;
        formVersionId: string;
        form?: FormEntity;
        formVersion?: FormVersionEntity;
        withDeleted?: boolean;
    } & FormVersionContentRepositoriesType): Promise<FormVersionContentType> {
        const [
            formFields,
            collections,
            stages,
            stageTransitions,
            stageRoles,
            stageAccessControls,
            formLayouts,
            formRelations,
            formViews,
            form,
            formVersion,
            formAutoPopulateSettings,
            generalAutoPopulateSettings,
            generalAutoPopulateExtraConfigSettings,
            contextMappings,
            automationMappings,
            formViewItems,
            formManualEvents,
        ] = await Promise.all([
            fieldRepo.find({
                where: { formVersionId },
                relations: ['rollupDependencies'],
                withDeleted,
            }),
            collectionRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            stageRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            stageTransitionRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            stageRoleRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            stageAccessControlRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            formLayoutRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            formRelatedRepo.find({
                where: [{ firstFormId: formId }, { secondFormId: formId }],
            }),
            formViewRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            formParam ? Promise.resolve(formParam) : formRepo.findOneBy({ id: formId }),
            formVersionParam ? Promise.resolve(formVersionParam) : formVersionRepo.findOneBy({ id: formVersionId }),
            autoPopulateSettingRepo.findBy({ originFormVersionId: formVersionId }),
            generalAutoPopulateRepo.findBy({ builderVersionId: formVersionId }),
            generalAutoPopulateExtraConfigRepo.findBy({ builderVersionId: formVersionId }),
            contextMappingsRepo.findBy({ formVersionId: formVersionId }),
            automationMappingsRepo.findBy({ formVersionId: formVersionId }),
            formViewItemRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
            // Manual events
            manualEventRepo.find({
                where: {
                    formVersionId,
                },
                withDeleted,
            }),
        ]);

        const collectionIds = collections.map((collection) => collection.id);
        const stageIds = stages.map((stage) => stage.id);
        const stageRoleIds = stageRoles.map((stageRole) => stageRole.id);
        const layoutIds = formLayouts.map((layout) => layout.id);
        const viewIds = formViews.map((view) => view.id);

        const [collectionItems, stageDecisions, stageRoleAccessControls, layoutZones, layoutFields, roleViews] = await Promise.all([
            collectionIds.length
                ? collectionItemRepo.find({
                      where: {
                          formCollectionId: In(collectionIds),
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormCollectionItemType[]),

            stageIds.length
                ? stageDecisionRepo.find({
                      where: { stageId: In(stageIds) },
                      withDeleted,
                  })
                : Promise.resolve([] as FormStageDecisionType[]),
            stageRoleIds.length
                ? stageRoleAccessControlRepo.find({
                      where: {
                          stageRoleId: In(stageRoleIds),
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormStageRoleAccessControlType[]),
            layoutIds.length
                ? formLayoutZoneRepo.find({
                      where: {
                          layoutId: In(layoutIds),
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormLayoutZoneType[]),
            layoutIds.length
                ? formLayoutZoneFieldRepo.find({
                      where: {
                          layoutId: In(layoutIds),
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormLayoutZoneFieldType[]),

            isAccount && viewIds.length
                ? roleViewsRepo.find({
                      where: {
                          formViewId: In(viewIds),
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormRoleViewsType[]),
        ]);

        const collectionItemIds = collectionItems.map((item) => item.id);
        const collectionItemIdentityIds = collectionItems.map((item) => item.identityId);

        const [collectionContextMappings, collectionAdditionalFields, collectionAutoPopulateContexts] = await Promise.all([
            collectionItemIds.length
                ? collectionContextMappingRepo.find({
                      where: {
                          formCollectionItemId: In(collectionItemIds),
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormCollectionContextMappingType[]),
            collectionItemIdentityIds.length
                ? collectionAdditionalFieldRepo.find({
                      where: {
                          formCollectionItemIdentityId: In(collectionItemIdentityIds),
                          formVersionId: formVersionId,
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormCollectionAdditionalFieldType[]),
            collectionItemIdentityIds.length
                ? collectionAutoPopulateContextRepo.find({
                      where: {
                          additionalField: {
                              formCollectionItemIdentityId: In(collectionItemIdentityIds),
                              formVersionId: formVersionId,
                          },
                      },
                      withDeleted,
                  })
                : Promise.resolve([] as FormCollectionAutoPopulateContextType[]),
        ]);

        return {
            formFields,
            collections,
            collectionItems,
            collectionContextMappings,
            collectionAdditionalFields,
            collectionAutoPopulateContexts,
            formRelations,
            stages,
            stageTransitions,
            stageDecisions,
            formLayouts,
            layoutZones,
            layoutFields,
            stageAccessControls,
            stageRoles,
            stageRoleAccessControls,
            formViews,
            roleViews,
            form,
            formVersion,
            formAutoPopulateSettings,
            generalAutoPopulateSettings,
            generalAutoPopulateExtraConfigSettings,
            contextMappings,
            automationMappings,
            formViewItems,
            formManualEvents,
        };
    }
}
