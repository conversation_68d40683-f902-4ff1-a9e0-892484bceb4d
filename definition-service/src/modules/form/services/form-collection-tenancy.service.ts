import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { compact } from 'lodash';
import { DataSource, In, Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { CollectionTransactionEntity } from '../../../database/src/entities/public/collection-transaction.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormVersionEntity } from '../../../database/src/entities/public/form-version.public.entity';
import { CollectionTransactionTenancyEntity } from '../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionContextMappingTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-context-mapping.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormContextMappingTenancyEntity } from '../../../database/src/entities/tenancy/form-context-mapping.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-extra-config.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { FORM_COLLECTION_EVENT } from '../constants/form.event';
import { FormCollectionAdditionalFieldDto } from '../dtos/form-collection-additional-field.dto';
import { FormCollectionDto } from '../dtos/form-collection.dto';
import { FormCollectionCreateAndUpdateRequest, FormCollectionRequest } from '../dtos/requests/form-collection.request';
import { VerifyCollectionContextMappingRequest } from '../dtos/requests/verify-collection-context-mapping.request';
import { FormCollectionContextDataService } from './data/form-collection-context.data.service';
import { FormCollectionDataService } from './data/form-collection.data.service';
import { FormDataService } from './data/form.data.service';
import { GetAdditionalFields } from '../dtos/requests/get-additional-field';
import { FormCollectionAdditionalFieldEntity } from '../../../database/src/entities/public/form-collection-additional-fields.public.entity';
import { CollectionLayoutTenancyEntity } from '../../../database/src/entities/tenancy/collection-layout.tenancy.entity';
import { CollectionAutomationMappingRequest } from '../dtos/requests/collection-automation-mapping.request';
import { CollectionAutomationMappingDto } from '../dtos/collection-automation-mapping.dto';
import { FormCollectionAutomationMappingTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-automation-mapping.tenancy.entity';

@Injectable()
export class FormCollectionItemTenancyService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTenancyRepository: Repository<FormCollectionTenancyEntity>,
        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemTenancyRepository: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly _formVersionRepo: Repository<FormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterAdditionalFieldRepo: Repository<DataRegisterAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _collectionAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_CONTEXT_MAPPING_TENANCY_REPOSITORY)
        private readonly _formCollectionContextMappingRepo: Repository<FormCollectionContextMappingTenancyEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _generalAutoPopulateRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_EXTRA_CONFIG_TENANCY_REPOSITORY)
        private readonly _generalAutoPopulateExtraConfigRepo: Repository<GeneralAutoPopulateExtraConfigTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterFieldRepo: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly _dataRegisterTransactionRepo: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly _dataRegisterRepo: Repository<DataRegisterTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly _dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_TRANSACTION_TENANCY_REPOSITORY)
        private readonly _formCollectionItemTransactionFieldRepo: Repository<CollectionTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_TENANCY_REPOSITORY)
        private readonly _dataRegisterVersionTenancyRepo: Repository<DataRegisterVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_CONTEXT_MAPPING_TENANCY_REPOSITORY)
        private readonly _contextMappingRepo: Repository<FormContextMappingTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_AUTOMATION_MAPPING_TENANCY_REPOSITORY)
        private readonly _automationMappingRepo: Repository<FormCollectionAutomationMappingTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_LAYOUT_TENANCY_REPOSITORY)
        private readonly _collectionTenancyLayout: Repository<CollectionLayoutTenancyEntity>,

        private readonly _formDataService: FormDataService,
        private readonly _formCollectionDataService: FormCollectionDataService,
        private readonly _logger: LoggerService,
        private readonly _eventEmitter: EventEmitter2,

        @InjectMapper() readonly _mapper: Mapper,

        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _collectionContextDataService: FormCollectionContextDataService,
    ) {}

    public async create(dto: FormCollectionRequest[]): Promise<boolean> {
        try {
            const formVersionId = dto?.[0].formVersionId;

            const { formVersion, isNewVersion } = await this._formDataService.updateFormVersion({
                formVersionId,
                formVersionRepo: this._formVersionRepo as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                isAccount: !!this._claims.accountId,
                connection: this._dataSource,
                userId: this._claims.impersonating?.id ?? this._claims.userId,
                userByName: this._claims.impersonating ? this.fullName(this._claims.impersonating) : this._claims.userFullName,
            });

            if (isNewVersion) {
                dto.forEach((d) => {
                    d.formVersionId = formVersion.id;
                });
            }

            return await this._formCollectionDataService.create({
                dto,
                formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity>,
                formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<FormCollectionItemEntity>,
                additionalFieldRepo: this._dataRegisterAdditionalFieldRepo,
                formAdditionalFieldRepo: this._collectionAdditionalFieldRepo,
                dataRegisterFieldRepo: this._dataRegisterFieldRepo,
                dataRegisterTransactionFieldRepo: this._dataRegisterTransactionFieldRepo,
                dataRegisterTransactionRepo: this._dataRegisterTransactionRepo,
                formCollectionItemTransactionFieldRepo: this
                    ._formCollectionItemTransactionFieldRepo as Repository<CollectionTransactionEntity>,
                dataSource: this._dataSource,
                formVersionId: formVersion.id,
                dataRegisterRepo: this._dataRegisterRepo,
            });
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async getCollectionsByFormVersionId(formVersionId: string): Promise<FormCollectionDto[]> {
        const result = (await this._formCollectionDataService.getCollectionsByFormVersionId({
            formVersionId,
            formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionTenancyEntity | FormCollectionEntity>,
            formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<
                FormCollectionItemEntity | FormCollectionItemTenancyEntity
            >,
            formCollectionContextMappingRepo: this._formCollectionContextMappingRepo,
            generalAutoPopulateRepo: this._generalAutoPopulateRepo,
            formVersionRepo: this._formVersionRepo as Repository<FormVersionEntity | FormVersionTenancyEntity>,
            dataRegisterTransactionRepo: this._dataRegisterTransactionRepo,
            dataRegisterVersionRepo: this._dataRegisterVersionTenancyRepo,
            dataRegisterRepo: this._dataRegisterRepo,
            dataRegisterTransactionFieldRepo: this._dataRegisterTransactionFieldRepo,
        })) as FormCollectionTenancyEntity[];

        const tranIds = this._formCollectionDataService.extractDataRegisterTransactionIds(result);

        const dto = this._mapper.mapArray(result, FormCollectionTenancyEntity, FormCollectionDto);

        if (tranIds.length) {
            const additionalFields = await this._dataRegisterAdditionalFieldRepo.find({
                where: {
                    transactionId: In(tranIds),
                },
            });

            dto.forEach((d) => {
                d.formCollectionItems.forEach((item) => {
                    if (item.setting?.configuration?.hasGroup) {
                        item.setting?.dataGroup?.forEach((groupItem) => {
                            const groupFields = additionalFields.filter(
                                (field) => field.transactionId === groupItem.dataRegisterTransactionId,
                            );
                            groupItem.additionalFields = this._mapper.mapArray(
                                groupFields,
                                DataRegisterAdditionalFieldTenancyEntity,
                                FormCollectionAdditionalFieldDto,
                            );
                        });
                    } else {
                        const fields = additionalFields.filter((field) => field.transactionId === item.dataRegisterTransactionId);
                        item.additionalFields = this._mapper.mapArray(
                            fields,
                            DataRegisterAdditionalFieldTenancyEntity,
                            FormCollectionAdditionalFieldDto,
                        );
                    }
                });
            });
        }

        return dto;
    }

    public async update({ formVersionId, dto }: { formVersionId: string; dto: FormCollectionCreateAndUpdateRequest }): Promise<boolean> {
        try {
            const {
                formVersion,
                collectionIdMap,
                isNewVersion,
                collectionItemIdMap,
                collectionIdMapIdentityId,
                collectionItemIdMapIdentityId,
                collectionContextMappingIdMap,
                form,
            } = await this._formDataService.updateFormVersion({
                formVersionId: formVersionId,
                formVersionRepo: this._formVersionRepo as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                isAccount: !!this._claims.accountId,
                connection: this._dataSource,
                userId: this._claims.impersonating?.id ?? this._claims.userId,
                userByName: this._claims.impersonating ? this.fullName(this._claims.impersonating) : this._claims.userFullName,
            });

            if (isNewVersion) {
                // Remap ids
                dto?.update?.forEach((d) => {
                    d.formVersionId = formVersion.id;
                    if (!collectionIdMap.get(d.id)) {
                        return;
                    }

                    d.identityId = collectionIdMapIdentityId.get(d.id);
                    d.id = collectionIdMap.get(d.id);

                    (d.formCollectionItems ?? [])?.forEach((item) => {
                        if (item.setting?.configuration?.hasGroup) {
                            item.setting?.dataGroup.forEach((group) => {
                                if (!collectionItemIdMap.get(group.id)) {
                                    return;
                                }

                                group.identityId = collectionItemIdMapIdentityId.get(group.id);

                                group.formCollectionId = d.id;
                                group.id = collectionItemIdMap.get(group.id);
                            });
                        }

                        item.formCollectionId = d.id;

                        if (!collectionItemIdMap.get(item.id)) {
                            return;
                        }

                        item.identityId = collectionItemIdMapIdentityId.get(item.id);
                        item.id = collectionItemIdMap.get(item.id);

                        item.contextMappings?.forEach((ctxMapping) => {
                            if (!collectionContextMappingIdMap.get(ctxMapping.id)) {
                                return;
                            }

                            ctxMapping.id = collectionContextMappingIdMap.get(ctxMapping.id);
                        });
                    });
                });

                dto.create?.forEach((d) => (d.formVersionId = formVersion.id));
            }

            const tasks = [
                this._formCollectionDataService.update({
                    formVersionId: formVersion?.id,
                    dto: dto?.update,
                    formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity>,
                    formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<FormCollectionItemEntity>,
                    generalAutoPopulateSettingRepo: this._generalAutoPopulateRepo,
                    generalAutoPopulateExtraConfigRepo: this._generalAutoPopulateExtraConfigRepo,
                    additionalFieldRepo: this._dataRegisterAdditionalFieldRepo,
                    formAdditionalFieldRepo: this._collectionAdditionalFieldRepo,
                    dataSource: this._dataSource,
                }),

                this._formCollectionDataService.create({
                    dto: dto.create ?? [],
                    formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity>,
                    formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<FormCollectionItemEntity>,
                    additionalFieldRepo: this._dataRegisterAdditionalFieldRepo,
                    formAdditionalFieldRepo: this._collectionAdditionalFieldRepo,
                    dataRegisterFieldRepo: this._dataRegisterFieldRepo,
                    dataRegisterTransactionFieldRepo: this._dataRegisterTransactionFieldRepo,
                    dataRegisterTransactionRepo: this._dataRegisterTransactionRepo,
                    formCollectionItemTransactionFieldRepo: this
                        ._formCollectionItemTransactionFieldRepo as Repository<CollectionTransactionEntity>,
                    dataSource: this._dataSource,
                    formVersionId: formVersion.id,
                    dataRegisterRepo: this._dataRegisterRepo,
                }),
            ];

            const formCollectionIds = dto.delete?.formCollectionIds;

            if (formCollectionIds?.length) {
                const collectionIds = isNewVersion ? compact(formCollectionIds.map((id) => collectionIdMap.get(id))) : formCollectionIds;
                const collections = await this._formCollectionItemTenancyRepository.findBy({
                    id: In(collectionIds),
                });
                tasks.push(
                    this._formCollectionDataService.delete({
                        formVersionId: formVersion.id,
                        formCollectionIds: collectionIds,
                        formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity>,
                        formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<FormCollectionItemEntity>,
                        generalAutoPopulateSettingRepo: this._generalAutoPopulateRepo,
                        generalAutoPopulateExtraConfigRepo: this._generalAutoPopulateExtraConfigRepo,
                        formAdditionalFieldRepo: this._collectionAdditionalFieldRepo,
                        contextMappingRepo: this._contextMappingRepo,
                        automationMappingRepo: this._automationMappingRepo,
                        formCollectionIdentityIds: collections.map((c) => c.identityId ?? c.id),
                    }),
                );
            }

            await Promise.all(tasks);

            if (isNewVersion && !form.activeVersionId && !formCollectionIds?.length) {
                const eventRequest = {
                    accountId: this._claims.accountId,
                    formCollectionIds,
                };
                this._eventEmitter.emitAsync(FORM_COLLECTION_EVENT.DELETE_FORM_COLLECTION_FIELD_TRANSACTION, eventRequest);
            }

            return true;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async delete(formCollectionIds: string[]): Promise<boolean> {
        if (!formCollectionIds || formCollectionIds.length === 0) return false;
        try {
            const formCollection = await this._formCollectionTenancyRepository.findOneBy({ id: formCollectionIds[0] });

            const { collectionIdMap, isNewVersion, form, formVersion } = await this._formDataService.updateFormVersion({
                formVersionId: formCollection?.formVersionId,
                formVersionRepo: this._formVersionRepo as Repository<FormVersionTenancyEntity | FormVersionEntity>,
                isAccount: !!this._claims.accountId,
                connection: this._dataSource,
                userId: this._claims.impersonating?.id ?? this._claims.userId,
                userByName: this._claims.impersonating ? this.fullName(this._claims.impersonating) : this._claims.userFullName,
            });

            const result = await this._formCollectionDataService.delete({
                formVersionId: formVersion.id,
                formCollectionIds: isNewVersion ? compact(formCollectionIds.map((id) => collectionIdMap.get(id))) : formCollectionIds,
                formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionTenancyEntity | FormCollectionEntity>,
                formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<
                    FormCollectionItemTenancyEntity | FormCollectionItemEntity
                >,
                generalAutoPopulateSettingRepo: this._generalAutoPopulateRepo,
                generalAutoPopulateExtraConfigRepo: this._generalAutoPopulateExtraConfigRepo,
                formAdditionalFieldRepo: this._collectionAdditionalFieldRepo,
                contextMappingRepo: this._contextMappingRepo,
                automationMappingRepo: this._automationMappingRepo,
                formCollectionIdentityIds: [formCollection.identityId],
            });

            if (result && isNewVersion && !form.activeVersionId) {
                const eventRequest = {
                    accountId: this._claims.accountId,
                    formCollectionIds,
                };
                this._eventEmitter.emitAsync(FORM_COLLECTION_EVENT.DELETE_FORM_COLLECTION_FIELD_TRANSACTION, eventRequest);
            }

            return result;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async getCollectionPagination(
        query: MultipleFilterRequestDto & { includeItemGroup: string },
    ): Promise<PaginationResponseDto<FormCollectionDto>> {
        const result = await this._formCollectionDataService.getFormCollectionPagination({
            query,
            formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity | FormCollectionTenancyEntity>,
            formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<
                FormCollectionItemEntity | FormCollectionItemTenancyEntity
            >,
        });

        return result;
    }

    public async getCollectionDetail(id: string): Promise<FormCollectionDto> {
        const result = await this._formCollectionDataService.getCollectionDetail({
            id,
            formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity | FormCollectionTenancyEntity>,
        });

        return result;
    }

    public async getCollectionDetailByIdentityIdAndFormId(
        formCollectionIdentityId: string,
        formVersionId: string,
    ): Promise<FormCollectionDto> {
        const result = await this._formCollectionDataService.getCollectionDetailByIdentityIdAndFormId({
            formCollectionIdentityId,
            formVersionId,
            formCollectionRepo: this._formCollectionTenancyRepository as Repository<FormCollectionEntity | FormCollectionTenancyEntity>,
            collectionLayoutRepo: this._collectionTenancyLayout,
        });

        return result;
    }

    public async getCollectionAdditionalFields(
        formCollectionItemIdentityId: string,
        formVersionId: string,
    ): Promise<FormCollectionAdditionalFieldDto[]> {
        const data = await this._formCollectionDataService.getCollectionAdditionalFields({
            formCollectionItemIdentityId,
            formVersionId,
            repo: this._collectionAdditionalFieldRepo,
        });

        return this._mapper.mapArray(data, FormCollectionAdditionalFieldTenancyEntity, FormCollectionAdditionalFieldDto);
    }

    fullName(user: { firstName: string; secondName: string }): string {
        if (!user) {
            return '';
        }

        return [user.firstName, user.secondName].filter((x) => x).join(' ');
    }

    public async getFormCollectionAdditionalFields(dto: GetAdditionalFields): Promise<FormCollectionAdditionalFieldDto[]> {
        const isTest = dto.isTest ?? false;

        if (isTest) {
            if (!dto.formCollectionItemIdentityIds?.length) {
                return [];
            }

            const collections = await this._formCollectionTenancyRepository.find({
                where: {
                    formVersionId: dto.formVersionId,
                },
                select: {
                    id: true,
                    formCollectionItems: {
                        id: true,
                        identityId: true,
                    },
                },
                relations: {
                    formCollectionItems: true,
                },
            });

            if (!collections.length) {
                return [];
            }

            const collectionItems = collections.map((c) => c.formCollectionItems).flat();

            const filteredCollectionItems = collectionItems.filter((c) => dto.formCollectionItemIdentityIds.includes(c.identityId));

            if (!filteredCollectionItems.length) {
                return [];
            }

            const collectionItemIds = filteredCollectionItems.map((c) => c.id);

            const data = await this._formCollectionDataService.handleCreateAdditionalField({
                formVersionId: dto.formVersionId,
                formCollectionItemIds: collectionItemIds,
                formCollectionItemRepo: this._formCollectionItemTenancyRepository as Repository<
                    FormCollectionItemEntity | FormCollectionItemTenancyEntity
                >,
                additionalFieldRepo: this._dataRegisterAdditionalFieldRepo,
                formAdditionalFieldRepo: this._collectionAdditionalFieldRepo,
                ignoreSave: true,
            });

            return this._mapper.mapArray(data, FormCollectionAdditionalFieldEntity, FormCollectionAdditionalFieldDto);
        } else {
            const data = await this._formCollectionDataService.getFormCollectionAdditionalFields({
                formCollectionItemIdentityIds: dto.formCollectionItemIdentityIds,
                formVersionId: dto.formVersionId,
                repo: this._collectionAdditionalFieldRepo,
            });

            return this._mapper.mapArray(data, FormCollectionAdditionalFieldEntity, FormCollectionAdditionalFieldDto);
        }
    }

    async verifyContextMappings(
        formVersionId: string,
        request: VerifyCollectionContextMappingRequest,
    ): ReturnType<FormCollectionContextDataService['verify']> {
        try {
            const result = await this._collectionContextDataService.verify({
                accountId: this._claims.accountId,
                dataSource: this._dataSource,
                formVersionId,
                request,
            });

            return result;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    async getAutomationMappings(formVersionId: string): Promise<CollectionAutomationMappingDto[]> {
        try {
            const result = await this._collectionContextDataService.getAutomationMappings({
                accountId: this._claims.accountId,
                dataSource: this._dataSource,
                formVersionId,
            });

            const dtos = this._mapper.mapArray(result, FormCollectionAutomationMappingTenancyEntity, CollectionAutomationMappingDto);
            return dtos;
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }
}
