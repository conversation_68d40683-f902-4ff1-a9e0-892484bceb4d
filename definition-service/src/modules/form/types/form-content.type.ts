import { FormCollectionAdditionalFieldEntity } from '@/database/src/entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionAutoPopulateContextEntity } from '@/database/src/entities/public/form-collection-auto-populate-contexts.public.entity';
import { FormCollectionContextMappingEntity } from '@/database/src/entities/public/form-collection-context-mapping.public.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '@/database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionAutoPopulateContextTenancyEntity } from '@/database/src/entities/tenancy/form-collection-auto-populate-contexts.tenancy.entity';
import { FormCollectionContextMappingTenancyEntity } from '@/database/src/entities/tenancy/form-collection-context-mapping.tenancy.entity';
import { RoleTenancyEntity } from '@/database/src/entities/tenancy/role.tenancy.entity';
import { Repository } from 'typeorm';
import { AutomationEntity } from '../../../database/src/entities/public/automation.public.entity';
import { CollectionTransactionEntity } from '../../../database/src/entities/public/collection-transaction.public.entity';
import { DataRegisterAdditionalFieldEntity } from '../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { DataRegisterFieldEntity } from '../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { FormAutoPopulateSettingEntity } from '../../../database/src/entities/public/form-auto-populate-setting.public.entity';
import { FormChangeLogEntity } from '../../../database/src/entities/public/form-change-log.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormContextMappingEntity } from '../../../database/src/entities/public/form-context-mapping.public.entity';
import { FormFieldEntity } from '../../../database/src/entities/public/form-field.public.entity';
import { FormLayoutZoneFieldEntity } from '../../../database/src/entities/public/form-layout-zone-field.public.entity';
import { FormLayoutZoneEntity } from '../../../database/src/entities/public/form-layout-zone.public.entity';
import { FormLayoutEntity } from '../../../database/src/entities/public/form-layout.public.entity';
import { FormManualEventEntity } from '../../../database/src/entities/public/form-manual-event.public.entity';
import { FormRelatedEntity } from '../../../database/src/entities/public/form-related.public.entity';
import { FormVersionCommentEntity } from '../../../database/src/entities/public/form-version-comment.public.entity';
import { FormVersionEntity } from '../../../database/src/entities/public/form-version.public.entity';
import { FormViewItemEntity } from '../../../database/src/entities/public/form-view-item.public.entity';
import { FormViewEntity } from '../../../database/src/entities/public/form-view.public.entity';
import { FormEntity } from '../../../database/src/entities/public/form.public.entity';
import { GeneralAutoPopulateExtraConfigEntity } from '../../../database/src/entities/public/general-auto-populate-extra-config.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { RollupDependencyEntity } from '../../../database/src/entities/public/roll-up-dependency.public.entity';
import { StageAccessControlEntity } from '../../../database/src/entities/public/stage-access-controls.public.entity';
import { StageDecisionEntity } from '../../../database/src/entities/public/stage-decision.public.entity';
import { StageRoleAccessControlEntity } from '../../../database/src/entities/public/stage-role-access-controls.public.entity';
import { StageRoleEntity } from '../../../database/src/entities/public/stage-role.public.entity';
import { StageTransitionEntity } from '../../../database/src/entities/public/stage-transition.public.entity';
import { StageEntity } from '../../../database/src/entities/public/stage.public.entity';
import { CollectionTransactionTenancyEntity } from '../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormChangeLogTenancyEntity } from '../../../database/src/entities/tenancy/form-change-log.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormLayoutZoneFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-layout-zone-field.tenancy.entity';
import { FormLayoutZoneTenancyEntity } from '../../../database/src/entities/tenancy/form-layout-zone.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../../database/src/entities/tenancy/form-layout.tenancy.entity';
import { FormManualEventTenancyEntity } from '../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { FormVersionCommentTenancyEntity } from '../../../database/src/entities/tenancy/form-version-comment.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormViewItemTenancyEntity } from '../../../database/src/entities/tenancy/form-view-item.tenancy.entity';
import { FormViewTenancyEntity } from '../../../database/src/entities/tenancy/form-view.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { RoleToFormViewTenancyEntity } from '../../../database/src/entities/tenancy/role-to-form-view.tenancy.entity';
import { RollupDependencyTenancyEntity } from '../../../database/src/entities/tenancy/roll-up-dependency.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageDecisionTenancyEntity } from '../../../database/src/entities/tenancy/stage-decision.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTransitionTenancyEntity } from '../../../database/src/entities/tenancy/stage-transition.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { ChangeLogTargetType } from '../../../database/src/shared/enums/change-log.type.enum';
import { CollectionLayoutEntity } from '../../../database/src/entities/public/collection-layout.public.entity';
import { CollectionLayoutZoneEntity } from '../../../database/src/entities/public/collection-layout-zone.public.entity';
import { CollectionLayoutZoneFieldEntity } from '../../../database/src/entities/public/collection-layout-zone-field.public.entity';
import { FormCollectionAutomationMappingEntity } from '../../../database/src/entities/public/form-collection-automation-mapping.public.entity';
import { FormCollectionAutomationMappingTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-automation-mapping.tenancy.entity';
import { AutomationVersionEntity } from '../../../database/src/entities/public/automation-version.public.entity';
import { ApiTenancyEntity } from '../../../database/src/entities/tenancy/api.tenancy.entity';
import { ApiVersionTenancyEntity } from '../../../database/src/entities/tenancy/api-version.tenancy.entity';
import { ApiVersionEndpointTenancyEntity } from '../../../database/src/entities/tenancy/api-version-endpoint.tenancy.entity';
import { ApiVersionCommentTenancyEntity } from '../../../database/src/entities/tenancy/api-version-comment.tenancy.entity';

export type FormFieldType = FormFieldEntity | FormFieldTenancyEntity;
export type FormCollectionType = FormCollectionEntity | FormCollectionTenancyEntity;
export type FormCollectionItemType = FormCollectionItemEntity | FormCollectionItemTenancyEntity;
export type FormCollectionAdditionalFieldType = FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity;
export type FormCollectionAutoPopulateContextType =
    | FormCollectionAutoPopulateContextEntity
    | FormCollectionAutoPopulateContextTenancyEntity;
export type FormCollectionContextMappingType = FormCollectionContextMappingEntity | FormCollectionContextMappingTenancyEntity;
export type FormCollectionAutomationMappingType = FormCollectionAutomationMappingEntity | FormCollectionAutomationMappingTenancyEntity;
export type FormRelatedType = FormRelatedEntity | FormRelatedTenancyEntity;
export type FormStageType = StageEntity | StageTenancyEntity;
export type FormStageTransitionType = StageTransitionEntity | StageTransitionTenancyEntity;
export type FormStageDecisionType = StageDecisionEntity | StageDecisionTenancyEntity;
export type FormLayoutType = FormLayoutEntity | FormLayoutTenancyEntity;
export type FormLayoutZoneType = FormLayoutZoneEntity | FormLayoutZoneTenancyEntity;
export type FormLayoutZoneFieldType = FormLayoutZoneFieldEntity | FormLayoutZoneFieldTenancyEntity;
export type FormStageRoleType = StageRoleEntity | StageRoleTenancyEntity;
export type FormStageAccessControlType = StageAccessControlEntity | StageAccessControlTenancyEntity;
export type FormStageRoleAccessControlType = StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity;
export type FormViewType = FormViewEntity | FormViewTenancyEntity;
export type FormViewItemType = FormViewItemEntity | FormViewItemTenancyEntity;
export type FormType = FormEntity | FormTenancyEntity;
export type FormCommentType = FormVersionCommentEntity | FormVersionCommentTenancyEntity;
export type FormVersionType = FormVersionEntity | FormVersionTenancyEntity;
export type FormRoleViewsType = RoleToFormViewTenancyEntity;
export type FormChangeLogType = (FormChangeLogEntity | FormChangeLogTenancyEntity) & { changedByUser?: string };
export type FormRollupDependencyType = RollupDependencyTenancyEntity | RollupDependencyEntity;
export type DataRegisterType = DataRegisterEntity | DataRegisterTenancyEntity;
export type DataRegisterTransactionType = DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity;
export type FormAutoPopulateSettingType = FormAutoPopulateSettingEntity | FormAutoPopulateSettingTenancyEntity;
export type CollectionTransactionType = CollectionTransactionTenancyEntity | CollectionTransactionEntity;
export type FormManualEventType = FormManualEventEntity | FormManualEventTenancyEntity;

export type FormVersionContentRepositoriesType = {
    fieldRepo: Repository<FormFieldType>;
    collectionRepo: Repository<FormCollectionType>;
    collectionItemRepo: Repository<FormCollectionItemType>;
    collectionContextMappingRepo: Repository<FormCollectionContextMappingType>;
    collectionAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldType>;
    collectionAutoPopulateContextRepo: Repository<FormCollectionAutoPopulateContextType>;
    formRelatedRepo: Repository<FormRelatedType>;
    formCommentRepo: Repository<FormCommentType>;
    stageRepo: Repository<FormStageType>;
    stageTransitionRepo: Repository<FormStageTransitionType>;
    stageDecisionRepo: Repository<FormStageDecisionType>;
    formLayoutRepo: Repository<FormLayoutType>;
    formLayoutZoneRepo: Repository<FormLayoutZoneType>;
    formLayoutZoneFieldRepo: Repository<FormLayoutZoneFieldType>;
    stageRoleRepo: Repository<FormStageRoleType>;
    stageAccessControlRepo: Repository<FormStageAccessControlType>;
    stageRoleAccessControlRepo: Repository<FormStageRoleAccessControlType>;
    formViewRepo: Repository<FormViewType>;
    formViewItemRepo: Repository<FormViewItemType>;
    formRepo: Repository<FormType>;
    formVersionRepo: Repository<FormVersionType>;
    roleViewsRepo: Repository<FormRoleViewsType>;
    rollupDependencyRepo: Repository<FormRollupDependencyType>;
    dataRegisterRepo: Repository<DataRegisterType>;
    dataRegisterTransactionRepo: Repository<DataRegisterTransactionType>;
    autoPopulateSettingRepo: Repository<FormAutoPopulateSettingType>;
    changeLogRepo: Repository<FormChangeLogType>;
    collectionTransactionRepo: Repository<CollectionTransactionType>;
    formFieldRepo: Repository<FormFieldType>;
    roleRepo: Repository<RoleTenancyEntity>;
    generalAutoPopulateRepo: Repository<GeneralAutoPopulateSettingEntity>;
    generalAutoPopulateExtraConfigRepo: Repository<GeneralAutoPopulateExtraConfigEntity>;
    formCollectionAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity>;
    dataRegisterVersionRepo: Repository<DataRegisterVersionEntity>;
    dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldEntity>;
    dataRegisterAdditionalFieldRepo: Repository<DataRegisterAdditionalFieldEntity>;
    formCollectionItemRepo: Repository<FormCollectionItemEntity>;
    dataRegisterFieldRepo: Repository<DataRegisterFieldEntity>;
    contextMappingsRepo: Repository<FormContextMappingEntity>;
    automationMappingsRepo: Repository<FormCollectionAutomationMappingEntity>;
    automationRepo: Repository<AutomationEntity>;
    automationVersionRepo: Repository<AutomationVersionEntity>;
    manualEventRepo: Repository<FormManualEventType>;
    collectionLayoutRepo: Repository<CollectionLayoutEntity>;
    collectionLayoutZoneRepo: Repository<CollectionLayoutZoneEntity>;
    collectionLayoutZoneFieldRepo: Repository<CollectionLayoutZoneFieldEntity>;
    apiBuilderRepo: Repository<ApiTenancyEntity>;
    apiVersionRepo: Repository<ApiVersionTenancyEntity>;
    apiVersionEndpointRepo: Repository<ApiVersionEndpointTenancyEntity>;
    apiVersionCommentRepo: Repository<ApiVersionCommentTenancyEntity>;
};

export type FormVersionContentType = {
    formFields: FormFieldType[];
    collections: FormCollectionType[];
    collectionItems: FormCollectionItemType[];
    collectionAdditionalFields: FormCollectionAdditionalFieldType[];
    collectionContextMappings: FormCollectionContextMappingType[];
    collectionAutoPopulateContexts: FormCollectionAutoPopulateContextType[];
    formRelations: FormRelatedType[];
    stages: FormStageType[];
    stageTransitions: FormStageTransitionType[];
    stageDecisions: FormStageDecisionType[];
    formLayouts: FormLayoutType[];
    layoutZones: FormLayoutZoneType[];
    layoutFields: FormLayoutZoneFieldType[];
    stageRoles: FormStageRoleType[];
    stageAccessControls: FormStageAccessControlType[];
    stageRoleAccessControls: FormStageRoleAccessControlType[];
    formViews: FormViewType[];
    roleViews: FormRoleViewsType[];
    form: FormType;
    formVersion: FormVersionType;
    formAutoPopulateSettings: FormAutoPopulateSettingType[];
    generalAutoPopulateSettings: GeneralAutoPopulateSettingEntity[];
    generalAutoPopulateExtraConfigSettings: GeneralAutoPopulateExtraConfigEntity[];
    contextMappings: FormContextMappingEntity[];
    automationMappings: FormCollectionAutomationMappingEntity[];
    formViewItems: FormViewItemType[];
    formManualEvents: FormManualEventType[];
};

export const ChangeLogToContentField = {
    [ChangeLogTargetType.FIELDS]: 'formFields',
    [ChangeLogTargetType.COLLECTIONS]: 'collections',
    [ChangeLogTargetType.COLLECTION_ITEMS]: 'collectionItems',
    [ChangeLogTargetType.RELATIONS]: 'formRelations',
    [ChangeLogTargetType.STAGES]: 'stages',
    [ChangeLogTargetType.STAGE_TRANSITION]: 'stageTransitions',
    [ChangeLogTargetType.STAGE_DECISION]: 'stageDecisions',
    [ChangeLogTargetType.LAYOUT]: 'formLayouts',
    [ChangeLogTargetType.LAYOUT_ZONE]: 'layoutZones',
    [ChangeLogTargetType.LAYOUT_ZONE_FIELD]: 'layoutFields',
    [ChangeLogTargetType.STAGE_ROLE]: 'stageRoles',
    [ChangeLogTargetType.STAGE_ACCESS_CONTROL]: 'stageAccessControls',
    [ChangeLogTargetType.STAGE_ROLE_ACCESS_CONTROL]: 'stageRoleAccessControls',
    [ChangeLogTargetType.VIEW]: 'formViews',
    [ChangeLogTargetType.INFORMATION]: 'form',
};

export type FormViewChangeLogRequest = {
    origins: FormViewType[];
    compares: FormViewType[];
    formVersionId: string;
    comparedViewRoles: Partial<{ viewId: string; roleId: string }>[];
    originViewRoles: Partial<{ viewId: string; roleId: string }>[];
    originViewItems: FormViewItemType[];
    compareViewItems: FormViewItemType[];
    viewNameMap: Map<string, string>;
    fieldNameMap: Map<string, string>;
};
