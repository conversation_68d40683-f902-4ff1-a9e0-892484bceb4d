import { DataSource, Repository } from 'typeorm';
import { AutomationActionEntity } from '../../../database/src/entities/public/automation-action.public.entity';
import { AutomationRuleEntity } from '../../../database/src/entities/public/automation-rule.public.entity';
import { AutomationVersionEntity } from '../../../database/src/entities/public/automation-version.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../../database/src/entities/public/form-field.public.entity';
import { FormLayoutEntity } from '../../../database/src/entities/public/form-layout.public.entity';
import { FormRelatedEntity } from '../../../database/src/entities/public/form-related.public.entity';
import { FormVersionCommentEntity } from '../../../database/src/entities/public/form-version-comment.public.entity';
import { FormVersionEntity } from '../../../database/src/entities/public/form-version.public.entity';
import { FormViewEntity } from '../../../database/src/entities/public/form-view.public.entity';
import { FormEntity } from '../../../database/src/entities/public/form.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { StageAccessControlEntity } from '../../../database/src/entities/public/stage-access-controls.public.entity';
import { StageDecisionEntity } from '../../../database/src/entities/public/stage-decision.public.entity';
import { StageRoleAccessControlEntity } from '../../../database/src/entities/public/stage-role-access-controls.public.entity';
import { StageRoleEntity } from '../../../database/src/entities/public/stage-role.public.entity';
import { StageTransitionEntity } from '../../../database/src/entities/public/stage-transition.public.entity';
import { StageEntity } from '../../../database/src/entities/public/stage.public.entity';
import { SubscriptionEntity } from '../../../database/src/entities/public/subscription.public.entity';
import { AutomationActionTenancyEntity } from '../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { AutomationRuleTenancyEntity } from '../../../database/src/entities/tenancy/automation-rule.tenancy.entity';
import { AutomationVersionTenancyEntity } from '../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { AutomationTenancyEntity } from '../../../database/src/entities/tenancy/automation.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../../database/src/entities/tenancy/form-layout.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { FormVersionCommentTenancyEntity } from '../../../database/src/entities/tenancy/form-version-comment.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormViewTenancyEntity } from '../../../database/src/entities/tenancy/form-view.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { RoleToFormViewTenancyEntity } from '../../../database/src/entities/tenancy/role-to-form-view.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageDecisionTenancyEntity } from '../../../database/src/entities/tenancy/stage-decision.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTransitionTenancyEntity } from '../../../database/src/entities/tenancy/stage-transition.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { AddRelatedFormDto } from '../dtos/requests';
import { UpdateFormFieldDto } from '../dtos/requests/update-form-field.request';
import { UpdateFormDto, UpdateTenancyFormDto } from '../dtos/requests/update-form.request';
import { FormManualEventType } from './form-content.type';
import { AutomationEntity } from '../../../database/src/entities/public/automation.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';

export type VerifyFormResultType = {
    formVersion: FormVersionEntity | FormVersionTenancyEntity;
    isNewVersion: boolean;
    form?: FormEntity | FormTenancyEntity;
    layoutIdMap?: Map<string, string>;
    collectionIdMap?: Map<string, string>;
    stageIdMap?: Map<string, string>;
    collectionItemIdMap?: Map<string, string>;
    stageDecisionIdMap?: Map<string, string>;
    viewIdMap?: Map<string, string>;
    viewItemIdMap?: Map<string, string>;
    stageTransitionIdMap?: Map<string, string>;
    stageRoleIdMap?: Map<string, string>;
    stageAccessControlIdMap?: Map<string, string>;
    stageRoleAccessControlIdMap?: Map<string, string>;
    fieldIdMap?: Map<string, string>;
    layoutZoneIdMap?: Map<string, string>;
    layoutZoneFieldIdMap?: Map<string, string>;

    collectionIdMapIdentityId?: Map<string, string>;
    collectionItemIdMapIdentityId?: Map<string, string>;
    collectionContextMappingIdMap?: Map<string, string>;
    collectionAdditionalFieldIdMap?: Map<string, string>;

    formAutomationVersionIdMap?: Map<string, string>;

    automationActionIdMap?: Map<string, string>;
    automationRuleIdMap?: Map<string, string>;
    manualEventIdMap?: Map<string, string>;

    formApiVersionIdMap?: Map<string, string>;
    formApiVersionEndpointIdMap?: Map<string, string>;
    formApiVersionCommentIdMap?: Map<string, string>;
};

export type UpdateFormParams = {
    request: UpdateFormDto | UpdateTenancyFormDto;
    formRepo: Repository<FormEntity | FormTenancyEntity>;
    formVersionRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
    dataSource: DataSource;
    isAccount: boolean;
    userId?: string;
    userByName?: string;
    relationRequest?: AddRelatedFormDto;
    formFieldRepo?: Repository<FormFieldEntity | FormFieldTenancyEntity>;
    stageTransitionRepo?: Repository<StageTransitionEntity | StageTransitionTenancyEntity>;
    stageDecisionRepo?: Repository<StageDecisionEntity | StageDecisionTenancyEntity>;
    formViewRepo?: Repository<FormViewEntity | FormViewTenancyEntity>;
    roleToFormViewRepo?: Repository<RoleToFormViewTenancyEntity>;
    accountId?: string;

    automationRepo: Repository<AutomationEntity | AutomationTenancyEntity>;
    automationVersionRepo: Repository<AutomationVersionEntity | AutomationVersionTenancyEntity>;
    automationRuleRepo: Repository<AutomationRuleEntity | AutomationRuleTenancyEntity>;
    automationActionRepo: Repository<AutomationActionEntity | AutomationActionTenancyEntity>;

    stageRepo?: Repository<StageEntity | StageTenancyEntity>;
    stageACLRepo?: Repository<StageAccessControlEntity | StageAccessControlTenancyEntity>;
    stageRoleAclRepo?: Repository<StageRoleAccessControlEntity | StageRoleAccessControlTenancyEntity>;
    stageRoleRepo?: Repository<StageRoleEntity | StageRoleTenancyEntity>;
};

export type UpdateFormInformationParams = Pick<UpdateFormParams, 'request' | 'formRepo' | 'dataSource' | 'isAccount'> & {
    versionResult: VerifyFormResultType;
};

export type UpdateFormFieldsParams = Pick<
    UpdateFormParams,
    'request' | 'formRepo' | 'formVersionRepo' | 'dataSource' | 'isAccount' | 'accountId'
> & {
    versionResult: VerifyFormResultType;
};

export type UpdateFormViewsParams = Pick<UpdateFormParams, 'request' | 'formRepo' | 'formVersionRepo' | 'dataSource' | 'isAccount'> & {
    versionResult: VerifyFormResultType;
};

export type UpdateFormStagesParams = Pick<
    UpdateFormParams,
    'request' | 'formRepo' | 'formVersionRepo' | 'dataSource' | 'isAccount' | 'accountId'
> & {
    versionResult: VerifyFormResultType;
};

export type UpdateFormLayoutsParams = Pick<UpdateFormParams, 'request' | 'formRepo' | 'formVersionRepo' | 'dataSource' | 'isAccount'> & {
    versionResult: VerifyFormResultType;
};

export type UpdateWidgetFieldsRequest = {
    widgets: UpdateFormFieldDto[];
    formVersionId: string;
};

export type UpdateWidgetFieldsParams = {
    request: UpdateWidgetFieldsRequest;
    dataSource: DataSource;
    isAccount: boolean;
};
export type UpdateFormViewParams = Pick<
    UpdateFormParams,
    'request' | 'formRepo' | 'formVersionRepo' | 'dataSource' | 'isAccount' | 'formViewRepo' | 'roleToFormViewRepo'
>;

export type DeleteFormParams = {
    id: string;
    formRepo: Repository<FormEntity | FormTenancyEntity>;
    dataSource: DataSource;
    isAccount: boolean;
};

export type SoftResetFormVersionParams = {
    id: string;
    formRepo: Repository<FormEntity | FormTenancyEntity>;
    formVersionRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
    dataSource: DataSource;
    isAccount: boolean;
};

export type GetByIdParams = {
    id: string;
    isAccount: boolean;
    formVersionId?: string;
    includeActiveVersion?: boolean;
    includeAutomation?: boolean;
    includeSubscription?: boolean;
    includeComment?: boolean;
    includeLayouts?: boolean;
    includeStages?: boolean;
    includeRoleAccessControls?: boolean;
    includeStageAccessControls?: boolean;
    includeFormCollections?: boolean;
    includeRelatedForms?: boolean;
    includeFields?: boolean;
    includeStageRoleAccessControls?: boolean;
    formRepo: Repository<FormEntity | FormTenancyEntity>;
    formVersionRepo: Repository<FormVersionEntity | FormVersionTenancyEntity>;
    formFieldRepo: Repository<FormFieldEntity | FormFieldTenancyEntity>;
    stageRepo: Repository<StageEntity | StageTenancyEntity>;
    stageTransitionRepo: Repository<StageTransitionEntity | StageTransitionTenancyEntity>;
    collectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
    collectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity>;
    formLayoutRepo: Repository<FormLayoutEntity | FormLayoutTenancyEntity>;
    stageAccessControlRepo: Repository<StageAccessControlEntity | StageAccessControlTenancyEntity>;
    stageRoleRepo: Repository<StageRoleEntity | StageRoleTenancyEntity>;
    formRelatedRepo: Repository<FormRelatedEntity | FormRelatedTenancyEntity>;
    subscriptionRepo: Repository<SubscriptionEntity>;
    commentRepo: Repository<FormVersionCommentEntity | FormVersionCommentTenancyEntity>;
    generalAPRepo: Repository<GeneralAutoPopulateSettingEntity>;
    automationVersionRepo?: Repository<AutomationVersionEntity | AutomationVersionTenancyEntity>;
    manualEventRepo?: Repository<FormManualEventType>;
    automationActionRepo?: Repository<AutomationActionEntity | AutomationActionTenancyEntity>;
    dataRegisterTransactionFieldRepo?: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>;
};

export type GetFormRelationParams = Omit<
    GetByIdParams,
    'includeActiveVersion' | 'formVersionRepo' | 'formRepo' | 'id' | 'formVersionId'
> & {
    formId: string;
    versionId: string;
};
