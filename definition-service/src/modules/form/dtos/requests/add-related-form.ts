import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsObject, IsUUID } from 'class-validator';

export class AddRelatedFormDto {
    @ApiProperty()
    @IsUUID()
    @AutoMap()
    formId: string;

    @ApiProperty()
    @IsArray()
    @AutoMap()
    relatedFormIds?: string[];

    @IsObject()
    @ApiProperty({
        required: false,
        type: Object,
    })
    @AutoMap(() => [Object])
    configuration: Record<string, any>;
}
