import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { FormStatus } from '../../../../database/src/shared/enums/form-status.enum';
import { FormVersionStatus } from '../../../../database/src/shared/enums/form-version-status.enum';
import { FormTemplateFlow, FormTenancyFlow } from '../../../../shared/enums/form-builder-step.enum';
import { UpdateApiRequestDto } from '../../../api/dtos/request';
import { UpdateAutomationRequest } from '../../../automation/dtos/request/update-automation.request';
import { FormLayoutDto } from '../form-layout.dto';
import { UpdateFormViewRequest } from './form-view';
import { UpdateFormFieldDto } from './update-form-field.request';
import { UpdateStageTransitionDto } from './update-stage-transition.request';
import { UpdateStageDto } from './update-stage.request';

export class BaseUpdateFormDto {
    @ApiProperty()
    @IsUUID()
    @AutoMap()
    id: string;

    @ApiPropertyOptional()
    @AutoMap()
    @IsString()
    @IsOptional()
    icon?: string;

    @ApiPropertyOptional()
    @AutoMap()
    @IsNumber()
    @IsOptional()
    version?: number;

    @ApiPropertyOptional()
    @AutoMap()
    @IsUUID()
    @IsOptional()
    formVersionId?: string;

    @ApiPropertyOptional()
    @AutoMap()
    @IsString()
    @IsOptional()
    name?: string;

    @ApiPropertyOptional()
    @AutoMap()
    @IsString()
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({
        isArray: true,
        type: String,
    })
    @IsOptional()
    @IsArray()
    subscriptionIds?: string[];

    @ApiProperty({
        isArray: true,
        type: UpdateFormFieldDto,
    })
    @AutoMap()
    @IsArray()
    @IsOptional()
    fields?: UpdateFormFieldDto[];

    @ApiPropertyOptional()
    @AutoMap()
    @IsOptional()
    transactionField?: UpdateFormFieldDto;

    @AutoMap()
    @ApiPropertyOptional({
        isArray: true,
        type: UpdateStageDto,
    })
    @IsArray()
    @IsOptional()
    stages?: UpdateStageDto[];

    @AutoMap()
    @ApiPropertyOptional({
        isArray: true,
        type: UpdateStageDto,
    })
    @IsArray()
    @IsOptional()
    stageTransitions?: UpdateStageTransitionDto[];

    @AutoMap()
    @ApiPropertyOptional({
        isArray: true,
        type: UpdateStageDto,
    })
    @IsArray()
    @IsOptional()
    layouts?: FormLayoutDto[];

    @AutoMap()
    @ApiPropertyOptional({
        isArray: true,
        type: UpdateFormViewRequest,
    })
    @IsArray()
    @IsOptional()
    views?: UpdateFormViewRequest[];

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    automation?: UpdateAutomationRequest;

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    apiBuilder?: UpdateApiRequestDto;
}

export class UpdateFormDto extends BaseUpdateFormDto {
    @ApiProperty()
    @AutoMap()
    @IsString()
    step: FormTemplateFlow;
}

export class UpdateTenancyFormDto extends BaseUpdateFormDto {
    @ApiProperty()
    @AutoMap()
    @IsString()
    step: FormTenancyFlow;
}

export class UpdateFormVersionsDto extends UpdateFormDto {
    @ApiProperty({
        enum: FormStatus,
    })
    @AutoMap()
    @IsEnum(FormStatus)
    @IsOptional()
    status?: FormStatus;
}

export class UpdateFormStatusDto {
    @ApiProperty()
    @IsUUID()
    @AutoMap()
    id: string;

    @ApiProperty({
        enum: FormStatus,
    })
    @AutoMap()
    @IsEnum(FormStatus)
    status: FormStatus;
}

export class UpdateFormVersionStatusDto {
    @ApiProperty()
    @IsUUID()
    @AutoMap()
    id: string;

    @ApiProperty()
    @IsNumber()
    @AutoMap()
    version: number;

    @ApiProperty({
        enum: FormVersionStatus,
    })
    @AutoMap()
    @IsEnum(FormVersionStatus)
    status: FormVersionStatus;
}
