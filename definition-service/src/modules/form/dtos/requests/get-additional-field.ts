import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';

export class GetAdditionalFields {
    @ApiProperty()
    @IsString()
    formVersionId: string;

    @ApiPropertyOptional()
    @IsArray()
    @IsOptional()
    @IsString({ each: true })
    formCollectionItemIdentityIds: string[];

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    isTest?: boolean;
}
