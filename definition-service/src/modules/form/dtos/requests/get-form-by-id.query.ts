import { AutoMap } from '@automapper/classes';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, IsUUID } from 'class-validator';

export class GetFormByIdQuery {
    @AutoMap()
    @ApiPropertyOptional({ name: 'formVersionId' })
    @IsUUID()
    @IsOptional()
    formVersionId?: string;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeActiveVersion' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeActiveVersion?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeAutomation' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeAutomation?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeLayouts' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeLayouts?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeSubscription' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeSubscription?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeComment' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeComment?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeStages' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeStages?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeStageRoleAccessControls' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeStageRoleAccessControls?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeRoleAccessControls' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeRoleAccessControls?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeAccessControls' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeAccessControls?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeFormCollections' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeFormCollections?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'includeRelatedForms' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    includeRelatedForms?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ name: 'fromTransaction' })
    @IsOptional()
    @Transform((value) => {
        return value?.value?.toString()?.toLowerCase()?.trim() === 'true';
    })
    fromTransaction?: boolean;

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    @Transform((value) => {
        return parseInt(value?.value);
    })
    current?: FormTemplateSteps;
}

export enum FormTemplateSteps {
    Information,
    Fields,
    Collections,
    Widgets,
    Relations,
    Stages,
    Automation,
    ApiBuilder,
    Layouts,
    AccessControl,
    View,
    Testing,
    Version,
}

export const queryParamsByStep: {
    [key in FormTemplateSteps]: {
        includeStageAccessControls: boolean;
        includeRoleAccessControls: boolean;
        includeAutomation: boolean;
        includeComments: boolean;
        includeFormCollections: boolean;
        includeLayouts: boolean;
        includeRelatedForms: boolean;
        includeSubscription: boolean;
        includeStages: boolean;
        includeWidgets: boolean;
        includeFields: boolean;
        includeStageRoleAccessControls?: boolean;
    };
} = {
    [FormTemplateSteps.Information]: {
        includeComments: true,
        includeSubscription: true,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: false,
        includeFormCollections: false,
        includeLayouts: false,
        includeRelatedForms: false,
        includeStages: false,
        includeWidgets: false,
        includeFields: true,
    },
    [FormTemplateSteps.Fields]: {
        includeRelatedForms: true,
        includeComments: true,
        includeSubscription: true,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: true,
        includeFormCollections: true,
        includeLayouts: true,
        includeStages: true,
        includeWidgets: false,
        includeFields: true,
    },
    [FormTemplateSteps.Collections]: {
        includeFormCollections: true,
        includeRelatedForms: true,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: true,
        includeLayouts: true,
        includeStages: true,
        includeWidgets: false,
        includeFields: true,
    },
    [FormTemplateSteps.Widgets]: {
        includeFormCollections: true,
        includeRelatedForms: true,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: true,
        includeLayouts: true,
        includeStages: true,
        includeWidgets: true,
        includeFields: true,
    },
    [FormTemplateSteps.Relations]: {
        includeRelatedForms: true,
        includeFormCollections: true,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: true,
        includeLayouts: true,
        includeStages: true,
        includeWidgets: true,
        includeFields: true,
    },
    [FormTemplateSteps.Stages]: {
        includeAutomation: true,
        includeStages: true,
        includeFormCollections: true,
        includeRelatedForms: true,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeLayouts: true,
        includeWidgets: true,
        includeFields: true,
    },
    [FormTemplateSteps.Automation]: {
        includeStages: true,
        includeRelatedForms: true,
        includeFormCollections: true,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: true,
        includeLayouts: true,
        includeWidgets: true,
        includeFields: true,
    },
    [FormTemplateSteps.ApiBuilder]: {
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: false,
        includeFormCollections: true,
        includeLayouts: false,
        includeStages: true,
        includeWidgets: false,
        includeFields: true,
        includeRelatedForms: false,
        includeSubscription: false,
        includeComments: false,
    },
    [FormTemplateSteps.AccessControl]: {
        includeStageAccessControls: false,
        includeRoleAccessControls: true,
        includeStageRoleAccessControls: false,
        includeFormCollections: true,
        includeLayouts: true,
        includeStages: true,
        includeAutomation: true,
        includeRelatedForms: true,
        includeComments: false,
        includeSubscription: false,
        includeWidgets: true,
        includeFields: true,
    },
    [FormTemplateSteps.Layouts]: {
        includeAutomation: true,
        includeFormCollections: true,
        includeLayouts: true,
        includeRelatedForms: true,
        includeStages: true,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeWidgets: true,
        includeFields: true,
    },
    [FormTemplateSteps.View]: {
        includeLayouts: false,
        includeFormCollections: false,
        includeRelatedForms: false,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: true,
        includeStages: true,
        includeWidgets: false,
        includeFields: true,
    },
    [FormTemplateSteps.Testing]: {
        includeFormCollections: false,
        includeRelatedForms: false,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: false,
        includeLayouts: false,
        includeStages: false,
        includeWidgets: false,
        includeFields: false,
    },
    [FormTemplateSteps.Version]: {
        includeFormCollections: false,
        includeRelatedForms: false,
        includeComments: false,
        includeSubscription: false,
        includeStageAccessControls: false,
        includeRoleAccessControls: false,
        includeAutomation: false,
        includeLayouts: false,
        includeStages: false,
        includeWidgets: false,
        includeFields: false,
    },
};
