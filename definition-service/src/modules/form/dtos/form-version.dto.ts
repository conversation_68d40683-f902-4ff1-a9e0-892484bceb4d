import { AutoMap } from '@automapper/classes';
import { FormVersionStatus } from '../../../database/src/shared/enums/form-version-status.enum';
import { AbstractDto } from '../../../shared/common/dto/abstract.dto';
import { EditUser } from '../../../shared/common/dto/edit-user.dto';
import { FormAutomationDto } from './form-automation.dto';
import { FormCollectionDto } from './form-collection.dto';
import { FormCommentDto } from './form-comment.dto';
import { FormFieldDto } from './form-fields.dto';
import { FormLayoutDto } from './form-layout.dto';
import { FormManualEventDto } from './form-manual-event.dto';
import { FormDto } from './form.dto';
import { StageAccessControlDto } from './stage-access-control.dto';
import { StageRoleDto } from './stage-role.dto';
import { StageTransitionDto } from './stage-transition.dto';
import { StageDto } from './stage.dto';

export class FormVersionDto extends AbstractDto {
    @AutoMap()
    publishedBy?: string | null;

    @AutoMap()
    publishedByUser?: string | null;

    @AutoMap()
    publishedAt?: Date;

    @AutoMap()
    version: string;

    @AutoMap()
    formId: string;

    @AutoMap()
    layout: Record<string, string>;

    @AutoMap()
    fields?: FormFieldDto[];

    @AutoMap()
    status: FormVersionStatus;

    @AutoMap()
    stages?: StageDto[];

    @AutoMap(() => [StageRoleDto])
    stageRoles?: StageRoleDto[];

    @AutoMap()
    stageTransitions?: StageTransitionDto[];

    @AutoMap(() => [StageAccessControlDto])
    stageAccessControls?: StageAccessControlDto[];

    @AutoMap(() => [FormCollectionDto])
    formCollections?: FormCollectionDto[];

    @AutoMap(() => [FormManualEventDto])
    manualEvents?: FormManualEventDto[];

    @AutoMap()
    relatedForms?: FormDto[];

    @AutoMap()
    testTransactionId?: string;

    created?: EditUser;
    updated?: EditUser;
    published?: EditUser;
    numOfTransactions?: number;
    widgets?: FormFieldDto[];

    defaultRoleId?: string;

    comments?: FormCommentDto[];
    formLayouts?: FormLayoutDto[];
    formAutomation?: FormAutomationDto;
}
