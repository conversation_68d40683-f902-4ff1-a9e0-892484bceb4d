import { Controller, Get, HttpCode, HttpStatus, Query, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { BaseController } from '../../../shared/common/base.controller';
import { RoleACLService } from '../services/role-acl.tenancy.service';

@Controller({
    path: 'form-role-acl',
})
@ApiTags('Form Role ACL')
@ApiBearerAuth()
export class FormRoleACLController extends BaseController {
    constructor(private readonly _service: RoleACLService) {
        super();
    }

    @ApiOperation({ summary: 'Get register record' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('fra')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async getActiveRoleAccessControls(
        @Query('formVersionId') formVersionId: string,
        @Query('roleId') roleId: string,
        @Query('stageId') stageId: string,
    ) {
        const result = await this._service.get({ formVersionId, roleId, stageId });
        return this.getResponse(true, result);
    }
}
