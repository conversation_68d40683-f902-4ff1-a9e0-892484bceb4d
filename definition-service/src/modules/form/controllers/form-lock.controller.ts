import { <PERSON>, Get, Param, Post, UseGuards, Version } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { FormLockService } from '../services/form-lock.service';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { User } from '../../../decorators/customer.decorator';
import { BaseController } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';

class FormLockDto {
    isLocked: boolean;
    holderId: string | null;
    holderName: string | null;
    message: string;
}

@ApiTags('Form Lock')
@ApiBearerAuth()
@Controller({
    path: 'form-lock',
})
@UseGuards(JwtAuthGuard)
export class FormLockController extends BaseController {
    constructor(private readonly _service: FormLockService) {
        super();
    }

    @Post(':formId/acquire')
    @ApiOperation({ summary: 'Acquire a lock for a form' })
    @Version('1')
    async acquireLock(
        @Param('formId') formId: string,
        @User('accountId') accountId: string,
        @User('id') userId: string,
    ): Promise<ResponseDto<FormLockDto>> {
        const lockResult = await this._service.acquireFormLock(formId, accountId, userId);
        return this.getResponse(true, lockResult);
    }

    @Post(':formId/release')
    @ApiOperation({ summary: 'Release a lock for a form' })
    @Version('1')
    async releaseLock(
        @Param('formId') formId: string,
        @User('accountId') accountId: string,
        @User('id') userId: string,
    ): Promise<ResponseDto<FormLockDto>> {
        const releaseResult = await this._service.releaseFormLock(formId, accountId, userId);
        return this.getResponse(true, releaseResult);
    }

    @Get(':formId/status')
    @ApiOperation({ summary: 'Check form lock status' })
    @Version('1')
    async getLockStatus(@Param('formId') formId: string, @User('accountId') accountId: string): Promise<ResponseDto<FormLockDto>> {
        const status = await this._service.getFormLockStatus(formId, accountId);
        return this.getResponse(true, status);
    }
}
