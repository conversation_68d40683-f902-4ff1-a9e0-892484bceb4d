import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../guards/user-type.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { FormVersionResponse, ItemWithFormVersionResponse } from '../../../shared/dtos/form.response';
import { CreateFormViewRequest, FormViewDto } from '../dtos/requests/form-view';
import { GetFormViewRequest } from '../dtos/requests/form-view/form-view.request';
import { UpdateFormViewNameRequest } from '../dtos/requests/form-view/update-form-view-name.request';
import { FormViewTenancyService } from '../services/form-view-tenancy.service';
import { FormLockGuard } from '../guards/form-lock.guard';

@Controller({
    path: 'form-views-tenancy',
})
@ApiTags('Form Views Tenancy')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class FormViewTenancyController extends BaseController {
    constructor(private readonly _service: FormViewTenancyService) {
        super();
    }

    @ApiOperation({
        summary: 'Get form views by roles',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Get('/form-views-by-roles')
    async getFormViewsByRoles(@Query() query: GetFormViewRequest): Promise<ResponseDto<FormViewDto[]>> {
        const res = await this._service.getByFormViewsByRoles(query);

        return this.getResponse<FormViewDto[]>(true, res, []);
    }

    @ApiOperation({ summary: 'Get form views' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Get(':versionId')
    async getList(@Param('versionId') formVersionId: string): Promise<ResponseDto<FormViewDto[]>> {
        const res = await this._service.getByFormVersion({
            formVersionId,
        });

        return this.getResponse<FormViewDto[]>(true, res, []);
    }

    @ApiOperation({ summary: 'Create form view' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Post(':versionId')
    @UseGuards(FormLockGuard)
    async create(
        @Param('versionId') formVersionId: string,
        @Body() request: CreateFormViewRequest,
    ): Promise<ResponseDto<ItemWithFormVersionResponse>> {
        const result = await this._service.create(formVersionId, request);
        return this.getResponse<ItemWithFormVersionResponse>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Update form layout' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Put(':id')
    @UseGuards(FormLockGuard)
    async update(@Param('id') id: string, @Body() request: UpdateFormViewNameRequest): Promise<ResponseDto<FormVersionResponse>> {
        const result = await this._service.updateName(id, request);
        return this.getResponse<FormVersionResponse>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Delete form views' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Delete(':id')
    @UseGuards(FormLockGuard)
    async delete(@Param('id') id: string, @Body() request: { formId: string }): Promise<ResponseDto<FormVersionResponse>> {
        const result = await this._service.delete(id);
        return this.getResponse<FormVersionResponse>(!!result, result, []);
    }
}
