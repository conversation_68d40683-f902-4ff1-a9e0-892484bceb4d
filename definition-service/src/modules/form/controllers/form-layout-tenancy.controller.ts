import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../guards/user-type.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { FormVersionResponse, ItemWithFormVersionResponse } from '../../../shared/dtos/form.response';
import { FormLayoutDto } from '../dtos/form-layout.dto';
import { CreateLayoutRequest } from '../dtos/requests/create-form-layout.request';
import { UpdateLayoutRequest } from '../dtos/requests/update-form-layout.request';
import { FormLayoutTenancyService } from '../services/form-layout-tenancy.service';
import { GetFormCollectionLayoutRequest } from '../../data-register/dtos/requests/get-collection-layout.request';
import { FormCollectionLayoutTenancyService } from '../services/form-collection-layout-tenancy.service';
import { FormLockGuard } from '../guards/form-lock.guard';

@Controller({
    path: 'form-layouts-tenancy',
})
@ApiTags('Form Layouts Tenancy')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class FormLayoutTenancyController extends BaseController {
    constructor(
        private readonly _service: FormLayoutTenancyService,
        private readonly _formCollectionLayoutService: FormCollectionLayoutTenancyService,
    ) {
        super();
    }

    @ApiOperation({ summary: 'Get form collection layouts' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('collection-layout')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async getFormCollectionLayouts(@Query() query: GetFormCollectionLayoutRequest): Promise<ResponseDto<FormLayoutDto[]>> {
        const data = await this._formCollectionLayoutService.getFormCollectionLayouts(query);
        return this.getResponse(true, data);
    }

    @ApiOperation({ summary: 'Get form layouts' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get(':versionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getList(
        @Param('versionId') formVersionId: string,
        @Query('fromTransaction') fromTransaction: boolean,
    ): Promise<ResponseDto<FormLayoutDto[]>> {
        const res = await this._service.getFormVersionLayouts({
            formVersionId,
            fromTransaction,
        });

        return this.getResponse<FormLayoutDto[]>(!!res, res, []);
    }

    @ApiOperation({ summary: 'Get form layouts by transaction id' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('transaction/:transactionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getByTransactionId(@Param('transactionId') transactionId: string): Promise<ResponseDto<FormLayoutDto[]>> {
        const res = await this._service.getByTransactionId(transactionId);

        return this.getResponse<FormLayoutDto[]>(!!res, res, []);
    }

    @ApiOperation({ summary: 'Create form layout' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post(':versionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(FormLockGuard)
    async create(
        @Param('versionId') formVersionId: string,
        @Body() request: CreateLayoutRequest,
    ): Promise<ResponseDto<ItemWithFormVersionResponse>> {
        const result = await this._service.create(formVersionId, request);
        return this.getResponse<ItemWithFormVersionResponse>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Update form layout' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Put(':id')
    @UseGuards(FormLockGuard)
    async update(@Param('id') id: string, @Body() request: UpdateLayoutRequest): Promise<ResponseDto<FormVersionResponse>> {
        const result = await this._service.update(id, request);
        return this.getResponse<FormVersionResponse>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Delete form layout' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Delete(':id')
    @UseGuards(FormLockGuard)
    async delete(@Body() request: { formId: string }, @Param('id') id: string): Promise<ResponseDto<FormVersionResponse>> {
        const result = await this._service.delete(id);
        return this.getResponse<FormVersionResponse>(!!result, result, []);
    }
}
