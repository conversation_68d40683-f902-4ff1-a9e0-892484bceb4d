import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../guards/user-type.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { FormDto } from '../dtos/form.dto';
import { AddRelatedFormDto, CreateFormDto } from '../dtos/requests';
import { GetFormByIdQuery, queryParamsByStep } from '../dtos/requests/get-form-by-id.query';
import { GetFormsQuery } from '../dtos/requests/get-forms.query';
import { GetRelatedFormQuery } from '../dtos/requests/get-related-form.request';
import { PublishFormToAccountRequest } from '../dtos/requests/publish-form-to-account.request';
import { PublishFormRequest } from '../dtos/requests/publish-form.request';
import { UpdateFormDto } from '../dtos/requests/update-form.request';
import { FormTemplateService } from '../services/form-template.service';
import { UpdateWidgetFieldsRequest } from '../types/fom-data.request.type';
import { UpdateFormGuard } from '../../../guards/update-form.guard';
import { EnableAutomationVersionRequestDto } from '../../automation/commands/enable-automation-version/enable-automation-version.request.dto';
import { FormLockGuard } from '../guards/form-lock.guard';

@Controller({
    path: 'form-templates',
})
@ApiTags('Form Templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class FormController extends BaseController {
    constructor(private readonly _service: FormTemplateService) {
        super();
    }

    // #region GET
    @ApiOperation({ summary: 'Get form template list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get()
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getList(@Query() query: GetFormsQuery): Promise<PaginationResponseDto<FormDto>> {
        return this._service.getList(query);
    }

    @ApiOperation({ summary: 'Get related forms' })
    @Version('1')
    @Get('related-forms/:id/:versionId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getRelatedForms(@Param('id') id: string, @Param('versionId') versionId: string, @Query() query?: GetRelatedFormQuery) {
        const result = await this._service.getRelatedForms(id, versionId, query.deep, query.includeFields, query.includeStages);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get related forms' })
    @Version('1')
    @Get('account-roles/:id/:accountId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getAccountRoles(@Param('accountId') accountId: string, @Param('id') formId: string) {
        const result = await this._service.getAccountRoles({
            accountId,
            formId,
        });
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get publish content for form' })
    @Version('1')
    @Get('publish-contents/:id/:accountId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getPublishContents(@Param('accountId') accountId: string, @Param('id') formId: string) {
        const result = await this._service.getPublishContents({
            accountId,
            formId,
        });
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form fields by id' })
    @Version('1')
    @Get('/fields/:id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getFields(@Param('id') id: string, @Query('useActiveVersion') useActiveVersion?: string) {
        const result = await this._service.getFieldById(id, useActiveVersion?.toLocaleLowerCase() === 'true');
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form fields of active form version' })
    @Version('1')
    @Get('/active-fields/:formId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getActiveFields(@Param('formId') formId: string) {
        const result = await this._service.getActiveFields(formId);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get rollup context of form' })
    @Version('1')
    @Get('rollup-options/:id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getRollupContext(@Param('id') formId: string) {
        const result = await this._service.getRollUpContext(formId);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form by id' })
    @Version('1')
    @Get(':id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async getById(@Param('id') id: string, @Query() query?: GetFormByIdQuery) {
        let combinedQuery = query;
        const currentStep = query?.current;
        if (currentStep !== undefined) {
            const searchQuery = queryParamsByStep[currentStep];
            combinedQuery = {
                ...combinedQuery,
                ...searchQuery,
            };
        }
        const result = await this._service.get({
            id,
            ...combinedQuery,
        });
        return this.getResponse(true, result);
    }

    // @ApiOperation({ summary: 'Get form by id' })
    // @Version('1')
    // @Get(':id/version/:version')
    // @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    // async getByVersion(@Param('id') id: string, @Param('version') version: number) {
    //     const result = await this._service.getByVersion(id, version);
    //     return this.getResponse(true, result);
    // }
    // #endregion

    // #region POST

    @ApiOperation({ summary: 'Create form template' })
    @Version('1')
    @Post()
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async create(@Body() payload: CreateFormDto) {
        const result = await this._service.create(payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Add related forms' })
    @Version('1')
    @Post('related-forms')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @UseGuards(FormLockGuard)
    async addRelatedForms(@Body() payload: AddRelatedFormDto) {
        const result = await this._service.addRelatedForms(payload);
        return this.getResponse(true, result);
    }
    // #endregion

    // #region PUT
    @ApiOperation({ summary: 'Change form active version' })
    @Version('1')
    @Put('change-active-version/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async changeActiveVersion(@Param('formId') formId: string, @Body() payload: { version: number }) {
        const result = await this._service.changeActiveVersion(formId, payload.version);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Update form template' })
    @Version('1')
    @Put()
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @UseGuards(UpdateFormGuard, FormLockGuard)
    async update(@Body() payload: UpdateFormDto) {
        const result = await this._service.update(payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Change form to ready to publish' })
    @Version('1')
    @Put('release/:formId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @UseGuards(FormLockGuard)
    async publish(@Param('formId') formId: string, @Body() payload: PublishFormRequest) {
        const result = await this._service.release(formId, payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Publish form to account' })
    @Version('1')
    @Put('publish-to-account/:formId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @UseGuards(FormLockGuard)
    async publishToAccount(@Param('formId') formId: string, @Body() payload: PublishFormToAccountRequest) {
        const result = await this._service.publishToAccount(formId, payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Update form widgets' })
    @Version('1')
    @Put('widgets')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @UseGuards(FormLockGuard)
    async updateWidgetFields(@Body() payload: UpdateWidgetFieldsRequest) {
        await this._service.updateWidgetFields(payload);
        return this.getResponse(true);
    }
    // #endregion

    // #region DELETE
    @ApiOperation({ summary: 'Delete form' })
    @Version('1')
    @Delete(':id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async delete(@Param('id') id: string) {
        const result = await this._service.delete(id);
        return this.getResponse(true, result);
    }
    // #endregion

    @ApiOperation({ summary: 'Reset form by id' })
    @Version('1')
    @Put('/reset-version/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async resetById(@Param('id') id: string) {
        const result = await this._service.resetForm(id);
        return this.getResponse(true, result);
    }

    // #region DUPLICATE
    @ApiOperation({ summary: 'Duplicate form' })
    @Version('1')
    @Put('duplicate/:formId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    async duplicate(@Param('formId') formId: string) {
        const result = await this._service.duplicate(formId);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Enable automation version',
    })
    @Version('1')
    @Put('enable-automation/:formVersionId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @UseGuards(FormLockGuard)
    async enableAutomation(@Param('formVersionId') formVersionId: string, @Body() dto: EnableAutomationVersionRequestDto) {
        const result = await this._service.enableAutomation({
            formVersionId,
            ...dto,
        });
        return this.getResponse(true, result);
    }
}
