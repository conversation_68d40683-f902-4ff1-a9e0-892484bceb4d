import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FormRoleStageACLDto } from '../../../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { StageRoleEntity } from '../../../database/src/entities/public/stage-role.public.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../guards/user-type.guard';
import { Base<PERSON>ontroller } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { CreateRoleAccessControlRequest } from '../dtos/requests/create-role-access-control.request';
import { CreateStageAccessControlRequest } from '../dtos/requests/create-stage-access-control.request';
import { CreateStageRoleAccessControlRequest } from '../dtos/requests/create-stage-role-access-control.request';
import { DeleteRoleAccessControlRequest } from '../dtos/requests/delete-role-access-control.request';
import { DeleteStageRoleAccessControlRequest } from '../dtos/requests/delete-stage-role-access-control.request';
import { LoadFormRoleStageACLRequest } from '../dtos/requests/form-stage-role.request';
import { UpdateStageRoleAccessControlRequest } from '../dtos/requests/update-stage-role-access-control.request';
import { StageAccessControlDto } from '../dtos/stage-access-control.dto';
import { StageRoleAccessControlDto } from '../dtos/stage-role-access-control.dto';
import { FormAccessControlTenancyService } from '../services/form-access-control-tenancy.service';
import { FormLockGuard } from '../guards/form-lock.guard';
@Controller({
    path: 'form-access-controls-tenancy',
})
@ApiTags('Form Access Control Tenancy')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class FormAccessControlTenancyController extends BaseController {
    constructor(private readonly _service: FormAccessControlTenancyService) {
        super();
    }

    @ApiOperation({ summary: 'Get stage access control by form version id and stage id' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Get('/stage/:formVersionId/:stageId')
    async getAclByStage(
        @Param('formVersionId') formVersionId: string,
        @Param('stageId') stageId: string,
    ): Promise<ResponseDto<StageAccessControlDto[]>> {
        const stageRoleACLs = await this._service.getAclByStage(formVersionId, stageId);
        return this.getResponse<StageAccessControlDto[]>(true, stageRoleACLs, []);
    }

    @ApiOperation({ summary: 'Get stage role access control by stage role id' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Get('/stage-role/:stageRoleId')
    @UseGuards(FormLockGuard)
    async getAclByStageRole(@Param('stageRoleId') stageRoleId: string): Promise<ResponseDto<StageRoleAccessControlDto[]>> {
        const stageRoleACLs = await this._service.getAclByStageRole(stageRoleId);
        return this.getResponse<StageRoleAccessControlDto[]>(true, stageRoleACLs, []);
    }

    @ApiOperation({ summary: 'Combine transaction acl per role per stage' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Post('/stage-role-acl')
    async getStageACLByRole(@Body() request: LoadFormRoleStageACLRequest): Promise<ResponseDto<FormRoleStageACLDto[]>> {
        const stageRoleACLs = await this._service.getStageRoleACLs(request);
        return this.getResponse<FormRoleStageACLDto[]>(true, stageRoleACLs, []);
    }

    @ApiOperation({ summary: 'Update form stage access control' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Put('/stage/:versionId')
    @UseGuards(FormLockGuard)
    async update(
        @Param('versionId') formVersionId: string,
        @Body() request: CreateStageAccessControlRequest,
    ): Promise<ResponseDto<boolean>> {
        const result = await this._service.updateStageAcl(formVersionId, request);
        return this.getResponse<boolean>(result, result, []);
    }

    @ApiOperation({ summary: 'Update form stage role access control' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Put('/stage-role/:versionId')
    @UseGuards(FormLockGuard)
    async updateStageRoleAcl(
        @Param('versionId') formVersionId: string,
        @Body() request: UpdateStageRoleAccessControlRequest,
    ): Promise<ResponseDto<Array<StageRoleEntity | StageRoleTenancyEntity>>> {
        const result = await this._service.updateStageRoleAcl(formVersionId, request);
        return this.getResponse<Array<StageRoleEntity | StageRoleTenancyEntity>>(!!result, result, []);
    }
    @ApiOperation({ summary: 'Create form stage role access control' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Post('/role')
    @UseGuards(FormLockGuard)
    async createStageRoleAcl(@Body() request: CreateRoleAccessControlRequest): Promise<ResponseDto<boolean>> {
        const result = await this._service.createStageRoleAcl(request);
        return this.getResponse<boolean>(result, result, []);
    }

    @ApiOperation({ summary: 'Delete form stage role access control' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Delete('/role')
    @UseGuards(FormLockGuard)
    async deleteRoleAccessControl(@Body() request: DeleteRoleAccessControlRequest): Promise<ResponseDto<boolean>> {
        const result = await this._service.deleteRoleAccessControl(request);
        return this.getResponse<boolean>(result, result, []);
    }

    @ApiOperation({ summary: 'Create form stage role access control' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Post('/stage-role')
    @UseGuards(FormLockGuard)
    async createStageRoleAccessControl(@Body() request: CreateStageRoleAccessControlRequest): Promise<ResponseDto<boolean>> {
        const result = await this._service.createStageRoleAccessControl(request);
        return this.getResponse<boolean>(result, result, []);
    }

    @ApiOperation({ summary: 'Delete form stage role access control' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Delete('/stage-role')
    @UseGuards(FormLockGuard)
    async deleteStageRoleAccessControl(@Body() request: DeleteStageRoleAccessControlRequest): Promise<ResponseDto<boolean>> {
        const result = await this._service.deleteStageRoleAccessControl(request);
        return this.getResponse<boolean>(result, result, []);
    }
}
