import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../guards/user-type.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { CreateFormViewRequest, FormViewDto } from '../dtos/requests/form-view';
import { FormViewService } from '../services/form-view.service';
import { FormVersionResponse, ItemWithFormVersionResponse } from '../../../shared/dtos/form.response';
import { UpdateFormViewNameRequest } from '../dtos/requests/form-view/update-form-view-name.request';
import { FormLockGuard } from '../guards/form-lock.guard';

@Controller({
    path: 'form-views',
})
@ApiTags('Form Views')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class FormViewController extends BaseController {
    constructor(private readonly _service: FormViewService) {
        super();
    }

    @ApiOperation({ summary: 'Get form views' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Get(':versionId')
    async getList(@Param('versionId') formVersionId: string): Promise<ResponseDto<FormViewDto[]>> {
        const res = await this._service.getByFormVersion({
            formVersionId,
        });

        return this.getResponse<FormViewDto[]>(true, res, []);
    }

    @ApiOperation({ summary: 'Create form view' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Post(':versionId')
    @UseGuards(FormLockGuard)
    async create(
        @Param('versionId') formVersionId: string,
        @Body() request: CreateFormViewRequest,
    ): Promise<ResponseDto<ItemWithFormVersionResponse>> {
        const result = await this._service.create(formVersionId, request);
        return this.getResponse<ItemWithFormVersionResponse>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Update form layout' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Put(':id')
    @UseGuards(FormLockGuard)
    async update(@Param('id') id: string, @Body() request: UpdateFormViewNameRequest): Promise<ResponseDto<FormVersionResponse>> {
        const result = await this._service.updateName(id, request);
        return this.getResponse<FormVersionResponse>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Delete form views' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    @Delete(':id')
    @UseGuards(FormLockGuard)
    async delete(@Param('id') id: string): Promise<ResponseDto<FormVersionResponse>> {
        const result = await this._service.delete(id);
        return this.getResponse<FormVersionResponse>(!!result, result, []);
    }
}
