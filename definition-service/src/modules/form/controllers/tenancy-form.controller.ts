import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UpdateFormGuard } from '../../../guards/update-form.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { EnableAutomationVersionRequestDto } from '../../automation/commands/enable-automation-version/enable-automation-version.request.dto';
import { FormDto } from '../dtos/form.dto';
import { AddRelatedFormDto, CreateFormDto, UpdateDefaultRoleRequest } from '../dtos/requests';
import { GetFormByIdQuery, queryParamsByStep } from '../dtos/requests/get-form-by-id.query';
import { GetFormsQuery } from '../dtos/requests/get-forms.query';
import { GetRelatedFormQuery } from '../dtos/requests/get-related-form.request';
import { PublishFormRequest } from '../dtos/requests/publish-form.request';
import { UpdateFormDto } from '../dtos/requests/update-form.request';
import { TenancyFormTemplateService } from '../services/tenancy-form-template.service';
import { UpdateWidgetFieldsRequest } from '../types/fom-data.request.type';
import { FormLockGuard } from '../guards/form-lock.guard';

@Controller({
    path: 'tenancy-form-templates',
})
@ApiTags('Tenancy Form Templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class TenancyFormController extends BaseController {
    constructor(private readonly _formService: TenancyFormTemplateService) {
        super();
    }

    // #region GET
    @ApiOperation({ summary: 'Get form template list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get()
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getList(@Query() query: GetFormsQuery): Promise<PaginationResponseDto<FormDto>> {
        return this._formService.getList(query);
    }

    @ApiOperation({ summary: 'Get form template list for transantion view' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/by-roles')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getListForTransactionView(@Query() query: GetFormsQuery): Promise<PaginationResponseDto<FormDto>> {
        query.requireCheckRoleIds = true;
        return this._formService.getList(query);
    }

    @ApiOperation({ summary: 'Get form fields by id' })
    @Version('1')
    @Get('/fields/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getFields(@Param('id') id: string, @Query('useActiveVersion') useActiveVersion?: string) {
        const result = await this._formService.getFieldById(id, useActiveVersion?.toLocaleLowerCase() === 'true');
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get rollup context of form' })
    @Version('1')
    @Get('rollup-options/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getRollupContext(@Param('id') formId: string) {
        const result = await this._formService.getRollUpContext(formId);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form by id' })
    @Version('1')
    @Get(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getById(@Param('id') id: string, @Query() query?: GetFormByIdQuery) {
        let combinedQuery = query;
        const currentStep = query?.current;
        if (currentStep !== undefined) {
            const searchQuery = queryParamsByStep[currentStep];
            combinedQuery = {
                ...combinedQuery,
                ...searchQuery,
            };
        }
        console.log('🐛 ~ TenancyFormController ~ getById ~ combinedQuery:', combinedQuery);
        const result = await this._formService.get({
            id,
            ...combinedQuery,
        });
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form by transaction id' })
    @Version('1')
    @Get('transaction/:transactionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getByTransactionId(@Param('transactionId') transactionId: string, @Query() query?: { isTest?: boolean }) {
        const result = await this._formService.getByTransactionId(transactionId, query.isTest?.toString() === 'true');
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Reset form by id' })
    @Version('1')
    @Put('/reset-version/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async resetById(@Param('id') id: string) {
        const result = await this._formService.resetForm(id);
        return this.getResponse(true, result);
    }

    // @ApiOperation({ summary: 'Get form by id' })
    // @Version('1')
    // @Get(':id/version/:versionId')
    // @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    // async getByVersion(@Param('id') id: string, @Param('versionId') versionId: string) {
    //     const result = await this._formService.getByIdAndVersion(id, versionId);
    //     return this.getResponse(true, result);
    // }

    @ApiOperation({ summary: 'Get related forms' })
    @Version('1')
    @Get('related-forms/:id/:versionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getRelatedForms(@Param('id') id: string, @Query() query?: GetRelatedFormQuery) {
        const result = await this._formService.getRelatedForms(id, query.deep, query.includeFields, query.includeStages);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form fields of active form version' })
    @Version('1')
    @Get('/active-fields/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getActiveFields(@Param('formId') formId: string) {
        const result = await this._formService.getActiveFields(formId);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get form collection fields' })
    @Version('1')
    @Get('/collection-fields/:formVersionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getCollectionFields(@Param('formVersionId') formVersionId: string) {
        const result = await this._formService.getCollectionFieldByFormVersionId(formVersionId);
        return this.getResponse(true, result);
    }

    //#endregion

    // #region POST

    @ApiOperation({ summary: 'Create form template' })
    @Version('1')
    @Post()
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async create(@Body() payload: CreateFormDto) {
        const result = await this._formService.create(payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Add related forms' })
    @Version('1')
    @Post('related-forms')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(FormLockGuard)
    async addRelatedForms(@Body() payload: AddRelatedFormDto) {
        const result = await this._formService.addRelatedForms(payload);
        return this.getResponse(true, result);
    }
    // #endregion POST

    // #region PUT
    @ApiOperation({ summary: 'Change form active version' })
    @Version('1')
    @Put('change-active-version/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async changeActiveVersion(@Param('formId') formId: string, @Body() payload: { version: number }) {
        const result = await this._formService.changeActiveVersion(formId, payload.version);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Update form template' })
    @Version('1')
    @Put()
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(UpdateFormGuard, FormLockGuard)
    async update(@Body() payload: UpdateFormDto) {
        const result = await this._formService.update(payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Change form to released' })
    @Version('1')
    @Put('release/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(FormLockGuard)
    async publish(@Param('formId') formId: string, @Body() payload: PublishFormRequest) {
        const result = await this._formService.release(formId, payload);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Update form widgets' })
    @Version('1')
    @Put('widgets')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(FormLockGuard)
    async updateWidgetFields(@Body() payload: UpdateWidgetFieldsRequest) {
        await this._formService.updateWidgetFields(payload);
        return this.getResponse(true);
    }

    @ApiOperation({ summary: 'Update default role for user' })
    @Version('1')
    @Put('default-role')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async updateDefaultRole(@Body() payload: UpdateDefaultRoleRequest) {
        await this._formService.updateDefaultRole(payload);
        return this.getResponse(true);
    }
    // #endregion PUT

    // #region DELETE
    @ApiOperation({ summary: 'Delete form' })
    @Version('1')
    @Delete(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN)
    async delete(@Param('id') id: string) {
        const result = await this._formService.delete(id);
        return this.getResponse(true, result);
    }
    // #endregion DELETE

    // #region DUPLICATE
    @ApiOperation({ summary: 'Duplicate form' })
    @Version('1')
    @Put('/duplicate/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN)
    async duplicate(@Param('formId') formId: string) {
        const result = await this._formService.duplicate(formId);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Enable automation version',
    })
    @Version('1')
    @Put('enable-automation/:formVersionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async enableAutomation(@Param('formVersionId') formVersionId: string, @Body() dto: EnableAutomationVersionRequestDto) {
        const result = await this._formService.enableAutomation({
            formVersionId,
            ...dto,
        });
        return this.getResponse(true, result);
    }
}
