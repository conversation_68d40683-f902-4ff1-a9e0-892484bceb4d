import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserTypes } from '../../../decorators/user-type.decorator';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../guards/user-type.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { CollectionAutomationMappingDto } from '../dtos/collection-automation-mapping.dto';
import { FormCollectionAdditionalFieldDto } from '../dtos/form-collection-additional-field.dto';
import { FormCollectionDto } from '../dtos/form-collection.dto';
import { FormCollectionCreateAndUpdateRequest, FormCollectionDeleteRequest } from '../dtos/requests/form-collection.request';
import { GetAdditionalFields } from '../dtos/requests/get-additional-field';
import { VerifyCollectionContextMappingRequest } from '../dtos/requests/verify-collection-context-mapping.request';
import { FormCollectionService } from '../services/form-collection.service';
import { FormLockGuard } from '../guards/form-lock.guard';

@Controller({
    path: 'form-collections',
})
@ApiTags('Form Collections')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
@UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
export class FormCollectionController extends BaseController {
    constructor(private readonly _formCollectionService: FormCollectionService) {
        super();
    }

    @ApiOperation({ summary: 'Get form collections by form version' })
    @Version('1')
    @Get('/get-collections-by-form/:formVersionId')
    public async getCollectionsByFormVersionId(@Param('formVersionId') formVersionId: string): Promise<ResponseDto<FormCollectionDto[]>> {
        const result = await this._formCollectionService.getCollectionsByFormVersionId(formVersionId);
        return this.getResponse(!!result?.length, result);
    }

    @ApiOperation({ summary: 'Update form collection' })
    @Version('1')
    @Patch(':formVersionId')
    @UseGuards(FormLockGuard)
    public async update(
        @Param('formVersionId') formVersionId: string,
        @Body() request: FormCollectionCreateAndUpdateRequest,
    ): Promise<ResponseDto<boolean>> {
        const result = await this._formCollectionService.update({
            formVersionId: formVersionId,
            dto: request,
        });
        return this.getResponse(result, result);
    }

    @ApiOperation({
        summary: 'Delete collections',
    })
    @Version('1')
    @Delete()
    @UseGuards(FormLockGuard)
    public async deleteCollections(@Body() dto: FormCollectionDeleteRequest): Promise<ResponseDto<boolean>> {
        const result = await this._formCollectionService.delete(dto.formCollectionIds);
        return this.getResponse(result, result);
    }

    @ApiOperation({
        summary: 'Get collection pagination',
    })
    @Version('1')
    @Get('/get-collection-pagination')
    public async getCollectionPagination(
        @Query() query: MultipleFilterRequestDto & { includeItemGroup: string },
    ): Promise<ResponseDto<PaginationResponseDto<FormCollectionDto>>> {
        const result = await this._formCollectionService.getCollectionPagination(query);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Get collection detail',
    })
    @Version('1')
    @Get('/get-collection/:id')
    public async getCollectionDetail(@Param('id') id: string): Promise<ResponseDto<FormCollectionDto>> {
        const result = await this._formCollectionService.getCollectionDetail(id);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Get collection detail by identityId and form version id',
    })
    @Version('1')
    @Get('/get-collection-by-identityId/:formCollectionIdentityId/:formVersionId')
    public async getCollectionDetailByIdentity(
        @Param('formCollectionIdentityId') formCollectionIdentityId: string,
        @Param('formVersionId') formVersionId: string,
    ): Promise<ResponseDto<FormCollectionDto>> {
        const result = await this._formCollectionService.getCollectionDetailByIdentityIdAndFormId(formCollectionIdentityId, formVersionId);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Get collection additional fields by item identityId and form version id',
    })
    @Version('1')
    @Get('/additional-fields/:formVersionId/:formCollectionItemIdentityId')
    public async getCollectionAdditionalFields(
        @Param('formVersionId') formVersionId: string,
        @Param('formCollectionItemIdentityId') formCollectionItemIdentityId: string,
    ): Promise<ResponseDto<FormCollectionAdditionalFieldDto[]>> {
        const result = await this._formCollectionService.getCollectionAdditionalFields(formCollectionItemIdentityId, formVersionId);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Get collection additional fields by item identityId and form version id',
    })
    @Version('1')
    @Post('/additional-fields')
    public async getFormCollectionAdditionalFields(
        @Body() dto: GetAdditionalFields,
    ): Promise<ResponseDto<FormCollectionAdditionalFieldDto[]>> {
        const result = await this._formCollectionService.getFormCollectionAdditionalFields(dto);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Get form collection automation mappings',
    })
    @Version('1')
    @Get('automation-mappings/:formVersionId')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async automationMappings(@Param('formVersionId') formVersionId: string): Promise<ResponseDto<CollectionAutomationMappingDto[]>> {
        const result = await this._formCollectionService.getAutomationMappings(formVersionId);
        return this.getResponse(true, result);
    }

    @ApiOperation({
        summary: 'Verify collection item contexts mapping',
    })
    @Version('1')
    @Post('context-mappings/:id')
    @UseGuards(FormLockGuard)
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async verifyContextMappings(@Param('id') id: string, @Body() request: VerifyCollectionContextMappingRequest) {
        return await this._formCollectionService.verifyContextMappings(id, request);
    }
}
