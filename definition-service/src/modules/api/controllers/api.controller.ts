import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PaginatedResponseDto } from '../../../common/src/api/paginated.response.base';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { ResponseBaseDto } from '../../../shared/api/response.base';
import { BaseController } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { BulkToggleApiRequestDto, CreateApiRequestDto, ToggleApiRequestDto, UpdateApiRequestDto } from '../dtos/request';
import { CreateApiEndpointRequestDto } from '../dtos/request/create-api-endpoint.request.dto';
import { ApiResponseDto } from '../dtos/response';
import { ApiDetailResponseDto } from '../dtos/response/api-detail.response.dto';
import { ApiService } from '../services/api.service';
import { GetApiVersionsRequest } from '../dtos/request/get-api-versions.request.dto';
import { ApiVersionResponseDto } from '../dtos/response/api-version.response.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { UpdateApiEndpointRequestDto } from '../dtos/request/update-api-endpoint.request.dto';
import { ApiErrorResponse } from '../../../shared/api/api-error.response';
import { ReleaseAutomationRequest } from '../../automation/dtos/request/update-automation.request';

@Controller('api-template')
@ApiTags('Api templates')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class ApiController extends BaseController {
    constructor(private readonly apiService: ApiService) {
        super();
    }

    @Get('versions')
    @ApiOperation({ summary: 'Get list of api versions' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getListVersions(@Query() query: GetApiVersionsRequest): Promise<PaginationResponseDto<ApiVersionResponseDto>> {
        const result = await this.apiService.getListVersions({ query });
        return result;
    }

    @Get()
    @ApiOperation({ summary: 'Get list of api' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getList(@Query() query: any): Promise<PaginatedResponseDto<ApiResponseDto>> {
        return this.apiService.getList({ query });
    }

    @ApiOperation({ summary: 'Get a api change log' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/change-logs/:id/:versionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async getAutomation(@Param('id') id: string, @Param('versionId') versionId: string) {
        return this.getResponse(true, []);
    }

    @Get('context/:contextId')
    @ApiOperation({ summary: 'Get a api by context' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getListByContext(@Param('contextId') contextId: string): Promise<ResponseBaseDto<ApiDetailResponseDto>> {
        const result = await this.apiService.getByContext(contextId);
        return this.getResponse(!!result, result);
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a api' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getDetail(@Param('id') id: string): Promise<ResponseDto<ApiDetailResponseDto>> {
        const data = await this.apiService.getDetail(id);
        return this.getResponse(true, data);
    }

    @Get('docs/:id')
    @ApiOperation({ summary: 'Get api document' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async getDocs(@Param('id') id: string): Promise<ResponseDto<ApiDetailResponseDto>> {
        const data = await this.apiService.getApiDocs(id);
        return this.getResponse(true, data);
    }

    @Post('endpoint')
    @ApiOperation({ summary: 'Create a api endpoint' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async createEndpoint(@Body() body: CreateApiEndpointRequestDto): Promise<ResponseDto<string>> {
        const result = await this.apiService.createEndpoint(body);
        return this.getResponse(true, result?.id);
    }

    @Put('endpoint/:id')
    @ApiOperation({ summary: 'Update a api endpoint' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async updateEndpoint(@Param('id') id: string, @Body() body: UpdateApiEndpointRequestDto): Promise<ResponseDto<string>> {
        const result = await this.apiService.updateEndpoint(id, body);
        return this.getResponse(true, result?.id);
    }

    @Post()
    @ApiOperation({ summary: 'Create a api' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async create(@Body() body: CreateApiRequestDto): Promise<{ id: string }> {
        return this.apiService.create(body);
    }

    @Put('toggle')
    @ApiOperation({ summary: 'Toggle a api' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async toggle(@Body() body: ToggleApiRequestDto): Promise<boolean> {
        return this.apiService.toggle(body);
    }

    @Put('bulk-toggle')
    @ApiOperation({ summary: 'Toggle all api' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async toggleAll(@Body() body: BulkToggleApiRequestDto): Promise<boolean> {
        return this.apiService.toggleAll(body);
    }

    @Put(':id')
    @ApiOperation({ summary: 'Update a api' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    async update(@Param('id') id: string, @Body() body: UpdateApiRequestDto): Promise<void> {
        return this.apiService.update(id, body);
    }

    @ApiOperation({ summary: 'Update active api version' })
    @ApiResponse({
        status: HttpStatus.OK,
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: ApiErrorResponse,
    })
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @Version('1')
    @Put('release/:id')
    async release(@Param('id') id: string, @Body() payload: ReleaseAutomationRequest) {
        const result = await this.apiService.release(id, payload);
        return this.getResponse(true, result);
    }
}
