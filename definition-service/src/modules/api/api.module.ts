import { forwardRef, Module } from '@nestjs/common';
import { EventDrivenModule } from '../../common/src';
import { DataSourceService } from '../../database/src/services/connection-util.service';
import { SharedModule } from '../../shared/shared.module';
import { DataRegisterModule } from '../data-register/data-register.module';
import { FormModule } from '../form/form.module';
import { ApiController } from './controllers';
import { ApiProfile } from './mapping-profiles/api.profile';
import { ApiService } from './services';
import { ApiComponentSchemaService } from './services/data-services/api-component-schema.service';
import { ApiConfigurationDataService } from './services/data-services/api-configuration-data.service';
import { ApiDataService } from './services/data-services/api-data.service';
import { ApiDocumentService } from './services/data-services/api-document.service';

const providers = [ApiDataService, ApiService, ApiConfigurationDataService, ApiDocumentService, ApiComponentSchemaService];
const mappingProfiles = [ApiProfile];

@Module({
    imports: [
        SharedModule,
        forwardRef(() => FormModule),
        forwardRef(() => DataRegisterModule),
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
    ],
    controllers: [ApiController],
    providers: [...providers, ...mappingProfiles],
    exports: [...providers],
})
export class ApiModule {}
