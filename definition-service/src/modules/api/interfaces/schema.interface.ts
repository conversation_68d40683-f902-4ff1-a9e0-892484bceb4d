export interface CreateTransactionRequestDto {
    formFields: {
        [key: string]: string | number | string[] | { [key: string]: string };
    };
}

export interface UpdateTransactionRequestDto extends CreateTransactionRequestDto {
    collections?: {
        addCollectionRows?: string[];
        updateCollectionFields?: {
            [collectionName: string]: Array<{
                rowKey: string;
                [key: string]: string | number | string[] | { [key: string]: string };
            }>;
        };
        deleteCollectionsRows?: string[];
    };
}

export interface ErrorResponseDto {
    code: number;
    timestamp: string;
    path: string;
    error: string;
    response?: {
        message: string | string[];
        error: string;
        statusCode: number;
    };
}

export interface SuccessResponseDto {
    status: boolean;
    message: string;
    data: Record<string, any>;
}
