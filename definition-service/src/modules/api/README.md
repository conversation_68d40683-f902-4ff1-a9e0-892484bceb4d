# API Module

This module provides endpoints for managing APIs using the `ApiTenancyEntity`.

## Endpoints

-   `GET /api-endpoint` - List APIs (with pagination/filtering)
-   `GET /api-endpoint/:id` - Get API detail
-   `POST /api-endpoint` - Create API
-   `PUT /api-endpoint/:id` - Update API

## Structure

-   `dtos/` - Request and response DTOs
-   `domain/entities/` - Entity references (uses `ApiTenancyEntity` from database)
-   `api.service.ts` - Business logic
-   `api.controller.ts` - REST endpoints

## Database Entity

This module uses `ApiTenancyEntity` from `src/database/src/entities/tenancy/api.tenancy.entity.ts` for all CRUD operations.

## DTOs and Domain

-   `dtos/request/` - Request DTOs for create and update
-   `dtos/response/` - Response DTOs for list and detail
-   `domain/entities/` - Entity references (uses `ApiTenancyEntity`)

## Service and Controller

-   `api.service.ts` - Handles business logic and database interaction
-   `api.controller.ts` - Exposes REST endpoints for API management

## Extending

To add more endpoints or business logic, extend the service and controller as needed. Use the existing structure as a template.
