import { ApiTenancyEntity } from '@/database/src/entities/tenancy/api.tenancy.entity';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PaginatedResponseDto } from '../../../common/src/api/paginated.response.base';
import { ApiVersionTenancyEntity } from '../../../database/src/entities/tenancy/api-version.tenancy.entity';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { BulkToggleApiRequestDto, CreateApiRequestDto, ToggleApiRequestDto, UpdateApiRequestDto } from '../dtos/request';
import { CreateApiEndpointRequestDto } from '../dtos/request/create-api-endpoint.request.dto';
import { ApiResponseDto } from '../dtos/response';
import { ApiDetailResponseDto } from '../dtos/response/api-detail.response.dto';
import { ApiVersionResponseDto } from '../dtos/response/api-version.response.dto';
import { ApiConfigurationDataService } from './data-services/api-configuration-data.service';
import { ApiDataService } from './data-services/api-data.service';
import { UpdateApiEndpointRequestDto } from '../dtos/request/update-api-endpoint.request.dto';
import { ApiDocumentService } from './data-services/api-document.service';
import { ReleaseAutomationRequest } from '../../automation/dtos/request/update-automation.request';

@Injectable()
export class ApiService {
    constructor(
        private readonly _apiDataService: ApiDataService,
        private readonly _apiDocumentService: ApiDocumentService,
        private readonly _apiConfigurationDataService: ApiConfigurationDataService,
        @Inject(USER_CLAIMS)
        private _claims: ClaimService,
        @InjectMapper()
        private readonly _mapper: Mapper,

        private readonly _logger: LoggerService,
    ) {}

    public async getList({ query }: { query: MultipleFilterRequestDto }): Promise<PaginatedResponseDto<ApiResponseDto>> {
        const { data, total, pageSize, pageIndex } = await this._apiDataService.getList({ query });
        const result = this._mapper.mapArray([...data], ApiTenancyEntity, ApiResponseDto);

        this._apiDataService.getUsers({
            apis: result,
        });

        await this._apiConfigurationDataService.getContextInfo({
            apis: result,
            accountId: this._claims.accountId,
            formContextIds: result.filter((f) => [ApiContextType.FormTransaction].includes(f.contextType)).map((f) => f.contextId),
            registerContextIds: result.filter((f) => f.contextType === ApiContextType.DataRegister).map((f) => f.contextId),
        });

        const actionVersionIds: string[] = result.map((f) => f.activeVersionId).filter(Boolean);
        if (actionVersionIds?.length) {
            await this._apiDataService.loadComment({
                apiVersionIds: actionVersionIds,
                apis: result,
            });
        }

        return {
            data: result,
            total,
            pageSize: pageSize,
            pageIndex: pageIndex,
        };
    }

    public async getListVersions({ query }: { query: MultipleFilterRequestDto }): Promise<PaginationResponseDto<ApiVersionResponseDto>> {
        const { data, total } = await this._apiDataService.getListVersions({ query });
        const apiId = query.filters.find((f) => f.field === 'apiId')?.value as string;

        if (!apiId) {
            return {
                data: [],
                total: 0,
            };
        }

        try {
            let result = this._mapper.mapArray(data, ApiVersionTenancyEntity, ApiVersionResponseDto);

            await this._apiDataService.getUsers({
                apis: result,
            });

            const actionVersionIds: string[] = result.map((f) => f.id);
            result = await this._apiDataService.loadVersionComments({ apiVersions: result, actionVersionIds });

            return {
                data: result,
                total: total,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getByContext(contextId: string): Promise<ApiDetailResponseDto> {
        const result = await this._apiDataService.getByContext({ contextId });
        return result;
    }

    public async getDetail(id: string): Promise<ApiDetailResponseDto> {
        const api = await this._apiDataService.getDetail({ id, accountId: this._claims.accountId });
        return api;
    }

    public async create(body: CreateApiRequestDto): Promise<{ id: string }> {
        try {
            const result = await this._apiDataService.create({
                request: body,
                userByName: this._claims.userFullName,
                userId: this._claims.userId,
                accountId: this._claims.accountId,
            });
            return { id: result };
        } catch (error) {
            this._logger.error(error);
            throw error;
        }
    }

    public async createEndpoint(body: CreateApiEndpointRequestDto): Promise<{ id: string }> {
        const result = await this._apiDataService.createEndpoint({
            request: body,
            accountId: this._claims.accountId,
        });
        return { id: result };
    }

    public async updateEndpoint(id: string, body: UpdateApiEndpointRequestDto): Promise<{ id: string }> {
        const result = await this._apiDataService.updateEndpoint({
            id,
            request: body,
            accountId: this._claims.accountId,
        });
        return { id: result };
    }

    public async update(id: string, body: UpdateApiRequestDto): Promise<void> {
        await this._apiDataService.update({
            id,
            request: body,
            userByName: this._claims.userFullName,
            userId: this._claims.userId,
            accountId: this._claims.accountId,
        });
    }

    public async toggle(body: ToggleApiRequestDto): Promise<boolean> {
        return await this._apiDataService.toggle(body);
    }

    public async toggleAll(body: BulkToggleApiRequestDto): Promise<boolean> {
        return await this._apiDataService.toggleAll(body);
    }

    public async getApiDocs(id: string): Promise<any> {
        return await this._apiDocumentService.getApiDocs({ id, accountId: this._claims.accountId });
    }

    public async release(id: string, payload: ReleaseAutomationRequest): Promise<boolean> {
        return await this._apiDataService.release(id, payload, this._claims.accountId);
    }
}
