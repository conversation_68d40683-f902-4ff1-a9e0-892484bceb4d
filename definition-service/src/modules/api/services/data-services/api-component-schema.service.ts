import { Injectable } from '@nestjs/common';

@Injectable()
export class ApiComponentSchemaService {
    /**
     * Generate complete components schema for Swagger
     */
    public generateComponentsSchema() {
        return {
            securitySchemes: this.generateSecuritySchemes(),
            schemas: {
                CreateTransactionRequestDto: this.generateCreateTransactionRequestDto(),
                UpdateTransactionRequestDto: this.generateUpdateTransactionRequestDto(),
                ErrorResponseDto: this.generateErrorResponseDto(),
                SuccessResponseDto: this.generateSuccessResponseDto(),
            },
        };
    }

    /**
     * Get all available schema names
     */
    public getAvailableSchemaNames(): string[] {
        return ['CreateTransactionRequestDto', 'UpdateTransactionRequestDto', 'ErrorResponseDto', 'SuccessResponseDto'];
    }

    public getAvailableSchemasPath(): {
        [key: string]: string;
    } {
        return {
            CreateTransactionRequestDto: '#/components/schemas/CreateTransactionRequestDto',
            UpdateTransactionRequestDto: '#/components/schemas/UpdateTransactionRequestDto',
            ErrorResponseDto: '#/components/schemas/ErrorResponseDto',
            SuccessResponseDto: '#/components/schemas/SuccessResponseDto',
        };
    }

    /**
     * Generate security schemes for Swagger
     */
    private generateSecuritySchemes() {
        return {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
            },
        };
    }

    /**
     * Generate the base form fields schema that can be reused
     */
    private generateFormFieldsSchema() {
        return {
            type: 'object',
            description:
                'Form field data. Keys are field names; values can be a string, number, date, time, datetime, duration, array of strings, or a nested record<string, string>.',
            additionalProperties: {
                oneOf: [
                    {
                        type: 'string',
                        format: 'date',
                        description: 'A date string - MM/DD/YYYY (e.g., "06/18/2025")',
                    },
                    {
                        type: 'number',
                        description: 'A time string in HHmm format. This will be parsed as total minutes. (e.g., "1:30" -> "90")',
                        example: 90,
                    },
                    {
                        type: 'string',
                        format: 'date-time',
                        description: 'A datetime string - MM/DD/YYYY HH:mm  (e.g., "06/18/2025 11:11")',
                    },
                    {
                        type: 'string',
                        description: 'A plain string',
                    },
                    {
                        type: 'number',
                        description: 'A numeric value',
                    },
                    {
                        type: 'number',
                        description: 'A duration value in minutes. This will be parsed as total minutes. (e.g., "1h1d1m" -> 1440)',
                        example: 90,
                    },
                    {
                        type: 'boolean',
                        description: 'A checkbox value',
                        example: true,
                    },
                    {
                        type: 'object',
                        additionalProperties: {
                            type: 'string',
                            description: 'A record of key-value string pairs',
                        },
                    },
                ],
            },
            example: {
                vessel: ['c36638b6-6c23-4fbf-aea8-127b61f3b2bd', '818359af-bcbd-4d02-86d4-d4c4bdd7c091'],
                flag: 'Viet Nam',
                date: '06/18/2025',
                time: 90,
                datetime: '06/18/2025 14:30',
                notes: 'Some free text',
                quantity: 42,
            },
        };
    }

    /**
     * Generate CreateTransactionRequestDto schema
     */
    private generateCreateTransactionRequestDto() {
        return {
            type: 'object',
            properties: {
                formFields: this.generateFormFieldsSchema(),
            },
        };
    }

    /**
     * Generate UpdateTransactionRequestDto schema
     */
    private generateUpdateTransactionRequestDto() {
        return {
            allOf: [
                { $ref: '#/components/schemas/CreateTransactionRequestDto' },
                {
                    type: 'object',
                    properties: {
                        collections: {
                            type: 'object',
                            properties: {
                                addCollectionRows: {
                                    type: 'object',
                                    description:
                                        'A mapping of collection keys to arrays of collection item identity UUIDs. Each key represents a specific collection, and each value is an array of unique identifiers (UUIDs) for the items within that collection.',
                                    additionalProperties: {
                                        type: 'array',
                                        items: {
                                            type: 'string',
                                            format: 'uuid',
                                        },
                                    },
                                    example: {
                                        q88_sts: ['bdf3cd62-7f59-4f1f-b829-9e2714e5dc88', 'f8a22a94-33d6-4c25-9960-91a4fe60f195'],
                                    },
                                },
                                updateCollectionFields: {
                                    type: 'object',
                                    description: 'Collection data. Keys are field names; values are array',
                                    additionalProperties: {
                                        type: 'array',
                                        items: {
                                            type: 'object',
                                            properties: {
                                                rowKey: {
                                                    type: 'string',
                                                    format: 'uuid',
                                                },
                                            },
                                            additionalProperties: {
                                                oneOf: [
                                                    {
                                                        type: 'string',
                                                        format: 'date',
                                                        description: 'A date string - MM/DD/YYYY (e.g., "06/18/2025")',
                                                    },
                                                    {
                                                        type: 'string',
                                                        format: 'time',
                                                        description:
                                                            'A time string in HHmm format. This will be parsed as total minutes. (e.g., "1:30" -> "90")',
                                                        example: '90',
                                                    },
                                                    {
                                                        type: 'string',
                                                        format: 'date-time',
                                                        description: 'A datetime string - MM/DD/YYYY HH:mm  (e.g., "06/18/2025 11:11")',
                                                    },
                                                    {
                                                        type: 'string',
                                                        description: 'A plain string',
                                                    },
                                                    {
                                                        type: 'number',
                                                        description: 'A numeric value',
                                                    },
                                                    {
                                                        type: 'array',
                                                        items: {
                                                            type: 'string',
                                                            format: 'uuid',
                                                            description: 'An array of strings',
                                                        },
                                                        example: [
                                                            'c36638b6-6c23-4fbf-aea8-127b61f3b2bd',
                                                            '818359af-bcbd-4d02-86d4-d4c4bdd7c091',
                                                        ],
                                                    },
                                                    {
                                                        type: 'object',
                                                        additionalProperties: {
                                                            type: 'string',
                                                            description: 'A record of key-value string pairs',
                                                        },
                                                    },
                                                ],
                                            },
                                            example: {
                                                vessel: ['c36638b6-6c23-4fbf-aea8-127b61f3b2bd', '818359af-bcbd-4d02-86d4-d4c4bdd7c091'],
                                                flag: 'Viet Nam',
                                                date: '2025-06-18',
                                                time: '90',
                                                datetime: '2025-06-18T14:30:00Z',
                                                notes: 'Some free text',
                                                quantity: 42,
                                                metadata: {
                                                    key1: 'value1',
                                                    key2: 'value2',
                                                },
                                            },
                                        },
                                    },
                                    example: {
                                        q88_sts: [
                                            {
                                                rowKey: 'f7f6f42b-c984-443f-9645-e9b051fc8179',
                                                vessel: ['c36638b6-6c23-4fbf-aea8-127b61f3b2bd', '818359af-bcbd-4d02-86d4-d4c4bdd7c091'],
                                                flag: 'Viet Nam',
                                                date: '06/18/2025',
                                                time: '0930',
                                                datetime: '06/18/2025 14:30',
                                                notes: 'Test entry for pressure vessel',
                                                quantity: 42,
                                                metadata: {
                                                    manufacturer: 'ABC Corp',
                                                    inspector: 'John Doe',
                                                },
                                            },
                                        ],
                                    },
                                },
                                deleteCollectionsRows: {
                                    type: 'object',
                                    description:
                                        'An object of collection keys to arrays of collection row UUIDs. Each key represents a specific collection, and each value is an array of unique identifiers (UUIDs) for the rows within that collection.',
                                    additionalProperties: {
                                        type: 'array',
                                        items: {
                                            type: 'string',
                                            format: 'uuid',
                                        },
                                    },
                                    example: {
                                        q88_sts: ['bdf3cd62-7f59-4f1f-b829-9e2714e5dc88', 'f8a22a94-33d6-4c25-9960-91a4fe60f195'],
                                    },
                                },
                            },
                            example: {
                                addCollectionRows: {
                                    q88_sts: ['bdf3cd62-7f59-4f1f-b829-9e2714e5dc88', 'f8a22a94-33d6-4c25-9960-91a4fe60f195'],
                                },
                                updateTransactionFields: {
                                    q88_sts: [
                                        {
                                            rowKey: 'f7f6f42b-c984-443f-9645-e9b051fc8179',
                                            vessel: ['c36638b6-6c23-4fbf-aea8-127b61f3b2bd', '818359af-bcbd-4d02-86d4-d4c4bdd7c091'],
                                            flag: 'Viet Nam',
                                            date: '06/18/2025',
                                            notes: 'Test entry for pressure vessel',
                                            quantity: 42,
                                        },
                                    ],
                                },
                                deleteCollectionsRows: {
                                    q88_sts: ['bdf3cd62-7f59-4f1f-b829-9e2714e5dc88', 'f8a22a94-33d6-4c25-9960-91a4fe60f195'],
                                },
                            },
                        },
                    },
                },
            ],
        };
    }

    /**
     * Generate ErrorResponseDto schema
     */
    private generateErrorResponseDto() {
        return {
            type: 'object',
            description: 'Standard error response structure',
            properties: {
                code: {
                    type: 'integer',
                    description: 'HTTP status code',
                },
                timestamp: {
                    type: 'string',
                    format: 'date-time',
                    description: 'The time the error occurred',
                },
                path: {
                    type: 'string',
                    description: 'The API endpoint path',
                },
                error: {
                    type: 'string',
                    description: 'Error summary message',
                },
                response: {
                    type: 'object',
                    properties: {
                        message: {
                            description: 'A single error message or a list of specific error messages',
                            oneOf: [
                                { type: 'string' },
                                {
                                    type: 'array',
                                    items: { type: 'string' },
                                },
                            ],
                        },
                        error: {
                            type: 'string',
                            description: 'Error type',
                        },
                        statusCode: {
                            type: 'integer',
                            description: 'HTTP status code',
                        },
                    },
                },
            },
        };
    }

    /**
     * Generate SuccessResponseDto schema
     */
    private generateSuccessResponseDto() {
        return {
            type: 'object',
            description: 'Standard success response',
            properties: {
                status: {
                    type: 'boolean',
                },
                message: {
                    type: 'string',
                },
                data: {
                    type: 'object',
                },
            },
        };
    }
}
