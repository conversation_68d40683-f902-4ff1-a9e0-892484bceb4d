import { ApiProperty } from '@nestjs/swagger';
import { ApiStatus, ApiContextType } from '@/database/src/shared/enums/automation.enum';
import { AutoMap } from '@automapper/classes';
import { ApiVersionResponseDto } from './api-version.response.dto';
import { EditUser } from '../../../../shared/common/dto/edit-user.dto';
import { AbstractDto } from '../../../../shared/common/dto/abstract.dto';

export class ApiResponseDto {
    @ApiProperty({
        description: 'ID of Api',
    })
    @AutoMap()
    id: string;

    @ApiProperty({
        description: 'Name of Api',
    })
    @AutoMap()
    name: string;

    @ApiProperty({
        description: 'Api status',
    })
    @AutoMap()
    status: ApiStatus;

    @ApiProperty({
        description: 'Api context type',
    })
    @AutoMap()
    contextType: ApiContextType;

    @ApiProperty({
        description: 'Api context ID',
    })
    @AutoMap()
    contextId: string;

    @ApiProperty({
        description: 'Subscription ID',
    })
    @AutoMap()
    subscriptionId?: string;

    @ApiProperty({
        description: 'Subscription Name',
    })
    @AutoMap()
    subscription?: string;

    @ApiProperty({
        description: 'Context Name',
    })
    @AutoMap()
    contextName?: string;

    @ApiProperty({
        description: 'Api description',
    })
    @AutoMap()
    description?: string;

    @ApiProperty({
        description: 'Api active version',
    })
    @AutoMap()
    activeVersion?: number;

    @ApiProperty({
        description: 'Api active version',
    })
    activeAutomationVersion?: ApiVersionResponseDto;

    @ApiProperty({
        description: 'Automation active version Id',
    })
    @AutoMap()
    activeVersionId?: string;

    @ApiProperty({
        description: 'Automation latest version',
    })
    @AutoMap()
    latestVersion: number;

    @ApiProperty({
        description: 'Automation latest version',
    })
    latestAutomationVersion: ApiVersionResponseDto;

    @ApiProperty({
        description: 'Automation latest version Id',
    })
    @AutoMap()
    latestVersionId?: string;

    @ApiProperty({
        description: 'Automation versions',
    })
    @AutoMap()
    versions: ApiVersionResponseDto[];

    @ApiProperty({
        description: 'Created full name automation',
    })
    @AutoMap()
    createdByUser?: string;

    @ApiProperty({
        description: 'Updated full name automation',
    })
    @AutoMap()
    updatedByUser?: string;

    @ApiProperty({
        description: 'Date when Automation was deleted',
    })
    @AutoMap()
    deletedByUser?: string;

    @AutoMap()
    publishedBy?: string;

    @AutoMap()
    publishedAt?: Date | string;

    @AutoMap()
    publishedByUser?: string;

    @AutoMap()
    createdBy?: string;

    @AutoMap()
    updatedBy?: string;

    created?: EditUser;
    published?: EditUser;
    updated?: EditUser;

    comment?: string;
}
