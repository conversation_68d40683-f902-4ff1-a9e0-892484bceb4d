import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsObject } from 'class-validator';
import { ApiVersionStatus } from '../../../../database/src/shared/enums/api.enum';
import { AbstractDto } from '../../../../shared/common/dto/abstract.dto';
import { EditUser } from '../../../../shared/common/dto/edit-user.dto';
import { ApiVersionCommentResponseDto } from './api-comment.response.dto';
import { ApiEndpointResponseDto } from './api-enpoint.response.dto';

export class ApiVersionResponseDto extends AbstractDto {
    @ApiProperty({
        description: 'ID of Api',
    })
    @AutoMap()
    id: string;

    @ApiProperty({
        description: 'Api ID of Api version',
    })
    @AutoMap()
    apiId: string;

    @AutoMap()
    contextVersionId: string;

    @ApiProperty({
        description: 'Version number',
    })
    @AutoMap()
    version: number;

    @AutoMap()
    status: ApiVersionStatus;

    @ApiProperty({
        description: 'Api data',
    })
    @AutoMap()
    @IsObject()
    configuration?: Object;

    @ApiProperty({
        description: 'Api endpoints',
    })
    @AutoMap(() => [ApiEndpointResponseDto])
    endpoints?: ApiEndpointResponseDto[];

    @AutoMap()
    publishedBy?: string;

    @AutoMap()
    publishedAt?: string;

    @AutoMap()
    isEnable?: boolean;

    @AutoMap()
    publishedByUser?: string;

    created?: EditUser;
    published?: EditUser;
    updated?: EditUser;

    comments?: ApiVersionCommentResponseDto[];
}
