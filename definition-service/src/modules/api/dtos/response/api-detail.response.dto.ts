import { ApiProperty } from '@nestjs/swagger';
import { ApiStatus, ApiContextType } from '@/database/src/shared/enums/automation.enum';
import { AutoMap } from '@automapper/classes';
import { EditUser } from '../../../../shared/common/dto/edit-user.dto';
import { ApiVersionResponseDto } from './api-version.response.dto';

export class ApiDetailResponseDto {
    @ApiProperty({
        description: 'ID of Api',
    })
    @AutoMap()
    id: string;

    @ApiProperty({
        description: 'Name of Api',
    })
    @AutoMap()
    name: string;

    @ApiProperty({
        description: 'Api status',
    })
    @AutoMap()
    status: ApiStatus;

    @ApiProperty({
        description: 'Api context type',
    })
    @AutoMap()
    contextType: ApiContextType;

    @ApiProperty({
        description: 'Api context ID',
    })
    @AutoMap()
    contextId: string;

    @ApiProperty({
        description: 'Subscription ID',
    })
    @AutoMap()
    subscriptionId?: string;

    @ApiProperty({
        description: 'Subscription Name',
    })
    @AutoMap()
    subscription?: string;

    @ApiProperty({
        description: 'Context Name',
    })
    @AutoMap()
    contextName?: string;

    @ApiProperty({
        description: 'Api description',
    })
    @AutoMap()
    description?: string;

    @ApiProperty({
        description: 'Api active version',
    })
    @AutoMap()
    activeVersion?: number;

    @ApiProperty({
        description: 'Api active version',
    })
    activeApiVersion?: ApiVersionResponseDto;

    @ApiProperty({
        description: 'Api active version Id',
    })
    @AutoMap()
    activeVersionId?: string;

    @ApiProperty({
        description: 'Api latest version',
    })
    @AutoMap()
    latestVersion: number;

    @ApiProperty({
        description: 'Api latest version',
    })
    latestApiVersion: ApiVersionResponseDto;

    @ApiProperty({
        description: 'Api latest version Id',
    })
    @AutoMap()
    latestVersionId?: string;

    @ApiProperty({
        description: 'Api versions',
    })
    @AutoMap()
    versions: ApiVersionResponseDto[];

    @ApiProperty({
        description: 'Created full name automation',
    })
    @AutoMap()
    createdByUser?: string;

    @ApiProperty({
        description: 'Updated full name automation',
    })
    @AutoMap()
    updatedByUser?: string;

    @ApiProperty({
        description: 'Date when Automation was deleted',
    })
    @AutoMap()
    deletedByUser?: string;

    @AutoMap()
    publishedBy?: string;

    @AutoMap()
    publishedAt?: Date | string;

    @AutoMap()
    publishedByUser?: string;

    @AutoMap()
    createdBy?: string;

    @AutoMap()
    updatedBy?: string;

    created?: EditUser;
    published?: EditUser;
    updated?: EditUser;

    comment?: string;
}
