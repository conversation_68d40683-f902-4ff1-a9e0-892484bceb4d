import { AutoMap } from '@automapper/classes';
import { AbstractDto } from '../../../../shared/common/dto/abstract.dto';
import { API_ENDPOINT_METHODS, API_ENDPOINTS } from '../../../../database/src/constants/api-endpoints';

export class ApiEndpointResponseDto extends AbstractDto {
    @AutoMap()
    identityId?: string;

    @AutoMap()
    name: string;

    @AutoMap()
    configuration: Record<string, any>;

    @AutoMap()
    apiVersionId?: string;

    @AutoMap()
    isEnable?: boolean;

    @AutoMap()
    method: API_ENDPOINT_METHODS;

    @AutoMap()
    endpoint: API_ENDPOINTS;
}
