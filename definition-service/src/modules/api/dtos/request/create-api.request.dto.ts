import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsUUID } from 'class-validator';
import { ApiContextType } from '@/database/src/shared/enums/automation.enum';

export class CreateApiRequestDto {
    @ApiProperty()
    @IsString()
    name: string;

    @ApiProperty({ enum: ApiContextType })
    @IsEnum(ApiContextType)
    contextType: ApiContextType;

    @ApiProperty()
    @IsUUID()
    contextId: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsUUID()
    subscriptionId?: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsUUID()
    contextVersionId?: string;
}
