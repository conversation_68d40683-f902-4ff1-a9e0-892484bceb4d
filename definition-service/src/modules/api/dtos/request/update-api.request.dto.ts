import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsUUID, IsObject } from 'class-validator';
import { UpdateApiStep } from '../../../../shared/enums/update-api-step.enum';
import { ApiEndpointDto } from './api-endpoint.dto';
import { CreateApiEndpointRequestDto } from './create-api-endpoint.request.dto';
import { UpdateApiEndpointRequestDto } from './update-api-endpoint.request.dto';

export class UpdateApiRequestDto {
    @IsEnum(UpdateApiStep)
    @ApiProperty({
        enum: UpdateApiStep,
    })
    step: UpdateApiStep;

    @ApiProperty()
    @IsUUID()
    id: string;

    @ApiPropertyOptional()
    @IsUUID()
    @IsOptional()
    apiVersionId: string;

    @ApiProperty()
    @IsObject()
    stepData: UpdateApiInformationRequest | UpdateApiConfigurationRequest;
}

export class UpdateApiInformationRequest {
    name: string;
    description: string;
}

export class UpdateApiConfigurationRequest {
    endpoints?: ApiEndpointDto[];
    addEndpoint?: CreateApiEndpointRequestDto;
    updateEndpoint?: UpdateApiEndpointRequestDto;
    configuration?: Record<string, any>;
}
