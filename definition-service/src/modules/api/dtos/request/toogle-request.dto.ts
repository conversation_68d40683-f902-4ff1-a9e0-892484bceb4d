import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsUUID } from 'class-validator';

export class ToggleApiRequestDto {
    @ApiProperty()
    @IsUUID()
    apiVersionId: string;

    @ApiProperty()
    @IsBoolean()
    isEnable: boolean;
}

export class BulkToggleApiRequestDto {
    @ApiProperty()
    @IsUUID()
    apiId: string;

    @ApiProperty()
    @IsBoolean()
    isEnable: boolean;
}
