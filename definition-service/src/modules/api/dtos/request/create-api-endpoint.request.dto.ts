import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsObject, IsOptional, IsString, IsUUID } from 'class-validator';
import { API_ENDPOINT_METHODS, API_ENDPOINTS } from '../../../../database/src/constants/api-endpoints';

export class CreateApiEndpointRequestDto {
    @ApiProperty()
    @IsString()
    name: string;

    @ApiPropertyOptional()
    @IsObject()
    @IsOptional()
    configuration?: Record<string, any>;

    @ApiProperty({ enum: API_ENDPOINTS })
    @IsEnum(API_ENDPOINTS)
    actionType: API_ENDPOINTS;

    @ApiProperty()
    @IsUUID()
    apiVersionId: string;

    @ApiProperty()
    @IsEnum(API_ENDPOINT_METHODS)
    method: API_ENDPOINT_METHODS;
}
