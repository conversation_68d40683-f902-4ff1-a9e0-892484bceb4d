import { ApiVersionTenancyEntity } from '@/database/src/entities/tenancy/api-version.tenancy.entity';
import { ApiTenancyEntity } from '@/database/src/entities/tenancy/api.tenancy.entity';
import { createMap, forMember, mapFrom, Mapper, MappingProfile } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { ApiVersionEndpointTenancyEntity } from '../../../database/src/entities/tenancy/api-version-endpoint.tenancy.entity';
import { ApiDetailResponseDto } from '../dtos/response/api-detail.response.dto';
import { ApiEndpointResponseDto } from '../dtos/response/api-enpoint.response.dto';
import { ApiVersionResponseDto } from '../dtos/response/api-version.response.dto';
import { ApiResponseDto } from '../dtos/response/api.response.dto';
import { ApiVersionCommentTenancyEntity } from '../../../database/src/entities/tenancy/api-version-comment.tenancy.entity';
import { ApiVersionCommentResponseDto } from '../dtos/response/api-comment.response.dto';

@Injectable()
export class ApiProfile extends AutomapperProfile {
    constructor(@InjectMapper() mapper: Mapper) {
        super(mapper);
    }

    get profile(): MappingProfile {
        return (mapper: Mapper) => {
            createMap(mapper, ApiTenancyEntity, ApiResponseDto);
            createMap(
                mapper,
                ApiVersionTenancyEntity,
                ApiVersionResponseDto,
                forMember(
                    (dest) => dest.configuration,
                    mapFrom((src) => src.configuration),
                ),
            );
            createMap(mapper, ApiTenancyEntity, ApiDetailResponseDto);
            createMap(
                mapper,
                ApiVersionEndpointTenancyEntity,
                ApiEndpointResponseDto,
                forMember(
                    (dest) => dest.configuration,
                    mapFrom((src) => src.configuration),
                ),
            );
            createMap(mapper, ApiVersionCommentTenancyEntity, ApiVersionCommentResponseDto);
        };
    }
}
