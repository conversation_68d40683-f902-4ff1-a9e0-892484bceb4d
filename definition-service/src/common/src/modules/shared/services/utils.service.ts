import { JsonTree } from '@react-awesome-query-builder/core';
import * as bcrypt from 'bcryptjs';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import * as _ from 'lodash';
import { v4 } from 'uuid';
import * as xml2js from 'xml2js';
import { ClientOperatorMapper } from '../../../constant/client-operator';
import { DurationFormatEnum } from '../enums/duration-format-type.enum';
import { FormFieldTypeEnum } from '../enums/form-field-type.enum';
import { OperatorType } from '../enums/operator.enum';
import { PREFIX } from '../enums/prefix.enum';
import { Duration } from '../types/duration';
import { YYYYMMDDHHmm, YYYYMMDD } from '../../../constant/date';

dayjs.extend(utc);

dayjs.extend(timezone);

type DateType = 'day' | 'month' | 'year';
export interface IDynamicObjectDetail<T> {
    [key: string]: T;
}

export class UtilsService {
    /**
     * generate hash from password or string
     * @param {string} password
     * @returns {string}
     */
    static generateHash(password: string): string {
        return bcrypt.hashSync(password, 10);
    }

    /**
     * generate random string
     * @param length
     */
    static generateRandomString(length: number): string {
        return Math.random()
            .toString(36)
            .replace(/[^a-zA-Z0-9]+/g, '')
            .substr(0, length);
    }
    /**
     * validate text with hash
     * @param {string} password
     * @param {string} hash
     * @returns {Promise<boolean>}
     */
    public static validateHash(password: string, hash: string): Promise<boolean> {
        return bcrypt.compare(password, hash || '');
    }

    public static getUtcNow(): Date {
        return new Date(new Date().toUTCString());
    }

    public static generateHashWithSalt(password: string, salt: string): any {
        return bcrypt.hash(password, salt);
    }

    /**
     * Duration string to number
     * @param {string} durationString string duration in format HH:mm
     */
    public static durationStringToNumber(durationString: string): number {
        if (!durationString) {
            return null;
        }
        const [hours, minutes] = durationString.split(':').map(Number);

        const totalMinutes = hours * 60 + minutes;
        return totalMinutes;
    }

    public static dateDiff(date1: Date, date2: Date, getBy: string): number {
        const diffInTime = date1.getTime() - date2.getTime();
        let result = 0;
        switch (getBy) {
            case 'month':
                result = Math.round(diffInTime / (1000 * 3600 * 24 * 30));
                break;
            case 'day':
                result = Math.round(diffInTime / (1000 * 3600 * 24));
                break;
            case 'hour':
                result = Math.round(diffInTime / (1000 * 3600));
                break;
            case 'minute':
                result = Math.round(diffInTime / (1000 * 60));
                break;
            case 'second':
                result = Math.round(diffInTime / 1000);
                break;
            default:
                break;
        }
        return result;
    }

    public static getUctDate(date: Date): Date {
        if (!date) return null;
        return new Date(date.toUTCString());
    }

    public static encodeBase64(str: string): string {
        return Buffer.from(str).toString('base64');
    }

    public static decodeBase64(str: string): string {
        return Buffer.from(str, 'base64').toString('utf8');
    }

    public static serializeQueryString(obj: { [x: string]: any; hasOwnProperty: (arg0: any) => any }, prefix: string): string {
        const str = [];
        let p;
        for (p in obj) {
            // eslint-disable-next-line no-prototype-builtins
            if (obj.hasOwnProperty(p)) {
                const k = prefix ? prefix + '[' + p + ']' : p,
                    v = obj[p];

                str.push(
                    v !== null && typeof v === 'object'
                        ? this.serializeQueryString(v, k)
                        : encodeURIComponent(k) + '=' + encodeURIComponent(v),
                );
            }
        }

        return str.join('&');
    }

    public static mapToCamelCase(obj: any): any {
        /* eslint-disable */
        return _.mapKeys(obj, (value, key) => _.camelCase(key));
    }

    public static getTetantHost(url: string): string {
        const urlObj = new URL(url);
        return urlObj?.host || '';
    }

    public static isNotEmpty<T>(array?: T[]): boolean {
        return array && array.length > 0;
    }

    public static getTaxAmount(price: number, tax?: number): number {
        if (!tax) {
            return 0;
        }

        return (price * tax) / 100;
    }

    public static formatDecimalNumber(number: number | string, scale: number = 2): number {
        return parseFloat((+number).toFixed(scale));
    }

    private static _getDiscountWeight(discountValue: number, denominator: number): number {
        const weight = discountValue / denominator;
        return weight;
    }

    public static async getXMLResponseKey(key: string, xml: string): Promise<string> {
        if (!xml) return null;

        //format
        xml = `<RESPONSE>${xml}</RESPONSE>`;
        const xmlParser = new xml2js.Parser();
        const result = await xmlParser.parseStringPromise(xml);
        const value = result?.['RESPONSE']?.[key]?.[0];
        return value ?? '';
    }

    public static async getXMLResponseObject(xml: string): Promise<string> {
        if (!xml) return null;

        //format
        xml = `<RESPONSE>${xml}</RESPONSE>`;
        const xmlParser = new xml2js.Parser();
        const result = await xmlParser.parseStringPromise(xml);
        const value = result?.['RESPONSE'];
        return value ?? '';
    }

    public static formatUTCDateTime(date, offSet) {
        const time = dayjs(date).utcOffset(offSet).format('HH:mm');
        const splitTime = time.split(':');

        const newDate = dayjs().set('hour', +splitTime[0]).set('minute', +splitTime[1]);
        return newDate;
    }

    public static calculatePriceIncludedTax(price: number, tax: number): number {
        if (!price || !tax) {
            return price;
        }

        return (price * 100) / (100 + tax);
    }

    public static calculateIncludedTax(price: number, tax: number): number {
        if (!price) {
            return 0;
        }

        return price - (price * 100) / (100 + tax);
    }

    static keyBy<T>(array: T[], condition: keyof T): { [key: string]: T } {
        const result: { [key: string]: T } = {};
        array.forEach((item) => {
            const key = item[condition];
            result[key as string] = item;
        });

        return result;
    }

    static convertSearchStringToObject = <T>(paramsStr: string): T => {
        return paramsStr.split('&').reduce(
            (p, c) => {
                const [key, value] = c.split('=');
                return { ...p, [key]: value };
            },
            {} as Record<string, string>,
        ) as T;
    };

    // function handle get list range of date
    public static getDatesInRange = (startDate: Date, endDate: Date, timeZone: string): string[] => {
        const date = new Date(startDate.getTime());
        const dateRange = [];
        while (date <= endDate) {
            dateRange.push(dayjs(date).tz(timeZone).format('MM-DD-YYYY'));
            date.setDate(date.getDate() + 1);
        }
        return dateRange;
    };

    public static isEqualString(fromStr: string, withStr: string): boolean {
        return fromStr?.trim().toLowerCase() === withStr?.trim().toLowerCase();
    }

    public static transformOperator(clientOperator: string): string {
        const transformedValue = ClientOperatorMapper[clientOperator];
        if (!transformedValue) {
            return OperatorType.equals;
        }
        return transformedValue;
    }

    public static getEmailDomain(email: string): string {
        if (!email) return null;
        return email.split('@')?.pop() ?? '';
    }

    public static mutateFormatFilterCondition({
        ruleOrGroup,
        prefix = PREFIX.FIELD,
        mutateNumberField = true,
    }: {
        ruleOrGroup: Record<string, any>;
        prefix?: string;
        mutateNumberField?: boolean;
    }): void {
        if (!ruleOrGroup) return;
        if (ruleOrGroup.properties?.field) {
            const formatField = `${prefix}_${ruleOrGroup.properties.field.split('-').join('_')}`;
            if (ruleOrGroup.properties?.valueType?.[0] === 'number' && mutateNumberField) {
                ruleOrGroup.properties.field = `${formatField}::float8`;
            } else {
                ruleOrGroup.properties.field = `${formatField}`;
            }
        }

        if (!ruleOrGroup.children1?.length) {
            return;
        }

        ruleOrGroup.children1.forEach((child) =>
            UtilsService.mutateFormatFilterCondition({
                ruleOrGroup: child,
                prefix,
                mutateNumberField,
            }),
        );

        return;
    }

    public static convertStringToDuration = (strDuration: string): Duration | null => {
        const regex = /(\d+d)?\s?(\d+h)?\s?(\d+m)?/; // Regular expression to match days, hours, and minutes

        try {
            const matches = strDuration?.match(regex);

            const duration: Duration = {
                days: 0,
                hours: 0,
                minutes: 0,
            };

            if (matches) {
                duration.days = matches[1] ? parseInt(matches[1]) : 0;
                duration.hours = matches[2] ? parseInt(matches[2]) : 0;
                duration.minutes = matches[3] ? parseInt(matches[3]) : 0;
            }
            return duration;
        } catch {
            return null;
        }
    };

    public static convertTimeStringToDate = (timeString: string): Date | null => {
        const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
        const isValidTime = timeRegex.test(timeString);
        if (!isValidTime) {
            return null;
        }

        const [hoursString, minutesString] = timeString.split(':');

        const hours = parseInt(hoursString, 10);
        const minutes = parseInt(minutesString, 10);

        const date = new Date();
        date.setHours(hours, minutes, 0, 0);

        return date;
    };

    public static convertDateTimeToTimeString = (date: Date): string | null => {
        if (!date) {
            return null;
        }
        const hours = date.getHours();
        const minutes = date.getMinutes();

        const formattedHours = hours < 10 ? `0${hours}` : `${hours}`;
        const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`;

        return `${formattedHours}:${formattedMinutes}`;
    };

    public static convertMinutesToDuration = ({ value, format }: { value: number; format: DurationFormatEnum }): string => {
        const days = Math.floor(value / (24 * 60));
        const hours = Math.floor((value % (24 * 60)) / 60);
        const minutes = value % 60;

        switch (format) {
            case DurationFormatEnum.DHM:
                return `${days}d ${hours}h ${minutes}m`;

            case DurationFormatEnum.HM:
                return `${hours}h ${minutes}m`;

            case DurationFormatEnum.M:
                return `${value}m`;

            default:
                return `${value}`;
        }
    };

    public static deepDiff = <T extends any>(fromObject: T, toObject: T) => {
        const changes = {};

        const buildPath = (path, obj, key) => (_.isUndefined(path) ? key : `${path}.${key}`);

        const walk = (fromObject, toObject, path?: any) => {
            for (const key of _.keys(fromObject)) {
                const currentPath = buildPath(path, fromObject, key);
                if (!_.has(toObject, key)) {
                    changes[currentPath] = { from: _.get(fromObject, key) };
                }
            }

            for (const [key, to] of _.entries(toObject)) {
                const currentPath = buildPath(path, toObject, key);
                if (!_.has(fromObject, key)) {
                    changes[currentPath] = { to };
                } else {
                    const from = _.get(fromObject, key);
                    if (!_.isEqual(from, to)) {
                        if (_.isObjectLike(to) && _.isObjectLike(from)) {
                            walk(from, to, currentPath);
                        } else {
                            changes[currentPath] = { from, to };
                        }
                    }
                }
            }
        };

        walk(fromObject, toObject);

        return changes;
    };

    public static extractCollectionKeys(key: string): Partial<{
        collectionItemKey: string;
        collectionItemId: string;
        fieldId: string;
        fieldIdentityId: string;
        collectionItemIdentityId: string;
        collectionIdentityId: string;
    }> {
        if (!key) return {};

        const [collectionItemKey, collectionItemId, fieldId, fieldIdentityId, collectionItemIdentityId, collectionIdentityId] =
            key.split('_');

        return {
            collectionItemKey,
            collectionItemId,
            fieldId,
            fieldIdentityId,
            collectionItemIdentityId,
            collectionIdentityId,
        };
    }

    public static combineCollectionKeys = ({
        collectionItemKey,
        collectionItemId,
        fieldId,
        fieldIdentityId,
        collectionItemIdentityId,
        collectionIdentityId,
    }) => {
        return `${collectionItemKey}_${collectionItemId}_${fieldId}_${fieldIdentityId}_${collectionItemIdentityId}_${collectionIdentityId}`;
    };

    /**
     *
     * @param ruleOrGroup: Json Conditions
     * @returns fieldIds: array fieldId in conditions
     */
    public static loadConditionFieldIds({
        ruleOrGroup,
        fieldIds,
        fieldTypes,
    }: {
        ruleOrGroup: Record<string, any>;
        fieldIds: string[];
        fieldTypes?: Record<string, string>;
    }) {
        if (!ruleOrGroup) return;
        if (ruleOrGroup.properties?.field) {
            fieldIds.push(ruleOrGroup.properties.field);
            if (fieldTypes) fieldTypes[ruleOrGroup.properties.field] = ruleOrGroup.properties.valueType?.[0] ?? FormFieldTypeEnum.Text;
        }

        if (!ruleOrGroup.children1?.length) {
            return;
        }

        ruleOrGroup.children1.forEach((child) =>
            this.loadConditionFieldIds({
                ruleOrGroup: child,
                fieldIds,
                fieldTypes,
            }),
        );

        return;
    }

    public static transformObject(obj: Record<string, any>, options: { pick?: string[]; omit?: string[] }): Record<string, any> {
        const result: any = {};

        for (const [key, value] of Object.entries(obj)) {
            if (options.pick && !options.pick.includes(key)) continue;
            if (options.omit && options.omit.includes(key)) continue;

            if (Array.isArray(value)) {
                result[key] = value.map((item) => UtilsService.transformObject(item, options));
            } else if (typeof value === 'object' && value !== null) {
                result[key] = UtilsService.transformObject(value, options);
            } else {
                result[key] = value;
            }
        }

        return result;
    }

    public static isValidDateRegex(dateString: string): boolean {
        const isoDateRegex = /^\d{2}\/\d{2}\/\d{4}/;
        return isoDateRegex.test(dateString);
    }

    public static isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && this.isValidDateRegex(dateString);
    }

    public static formatDateToYYYYMMDD(date: Date): string {
        const year = date.getFullYear();
        const month = _.padStart(String(date.getMonth() + 1), 2, '0');
        const day = _.padStart(String(date.getDate()), 2, '0');

        return `${year}-${month}-${day}`;
    }

    public static getUserId(user: any) {
        return user?.impersonating?.id ?? user?.id;
    }

    public static getUserFullName(user: any) {
        return user?.impersonating ? this.fullName(user?.impersonating) : this.fullName(user);
    }

    public static fullName(data: { firstName: string; secondName: string }): string {
        if (!data) {
            return '';
        }

        return [data.firstName, data.secondName].join(' ');
    }

    public static hashFileName(originalFileName: string): string {
        const timestamp = Date.now().toString();
        const fileExtension = originalFileName.split('.').pop();
        const rawName = `${timestamp}-${v4()}`;
        return fileExtension ? `${rawName}.${fileExtension}` : rawName;
    }

    public static padNumber(num: number, length: number): string {
        return num.toString().padStart(length, '0');
    }

    public static isValidEmail(value: string): boolean {
        const EMAIL_PATTERN = /^[\w\-.]+@([\w-]+\.)+[\w-]{2,4}$/g;
        return value && new RegExp(EMAIL_PATTERN).test(value);
    }

    public static generateWhereQqlFromConditionBuilder(
        conditions: JsonTree,
        fieldTypes: {
            [fieldId: string]: {
                type: string;
                operator: string;
            };
        },
    ): string | undefined {
        const clonedCondition = _.cloneDeep(conditions);

        const prefix = 'f';
        UtilsService.mutateFormatFilterCondition({
            ruleOrGroup: clonedCondition,
            prefix,
            mutateNumberField: false,
        });
        function buildSqlCondition(condition: any): string {
            if (condition.type === 'group' && condition.children1) {
                const conjunction = condition.properties?.conjunction || 'AND';
                const childConditions = condition.children1.map((child) => buildSqlCondition(child)).join(` ${conjunction} `);
                return `(${childConditions})`;
            } else if (condition?.type === 'rule' && condition?.properties) {
                const { field, operator, valueType, value } = condition?.properties;
                const fieldId = field;

                let formattedValue = null;

                const type = valueType?.[0];
                if (['custom_datePicker', 'custom_dateTimePicker'].includes(type)) {
                    // This part might need adjustment depending on how your UtilsService.getCustomDateValue handles different date types
                    formattedValue = `'${UtilsService.getCustomDateValue(value?.[0]) ?? ''}'`;
                } else {
                    formattedValue = value.map((v) => `'${v}'`).join(', ');
                }

                // Helper function to generate the CASE statement for date conversion
                const getDateConversionCase = (fieldAlias: string) => {
                    return `(CASE
        -- ISO 8601 with optional milliseconds (e.g., 2025-04-13T10:30:00 or 2025-04-13T10:30:00.123)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{1,6})?$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'YYYY-MM-DD"T"HH24:MI:SS.US')
        -- YYYY-MM-DD (e.g., 2025-04-13)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{4}-\\d{2}-\\d{2}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'YYYY-MM-DD')
        -- MM/DD/YYYY (e.g., 04/13/2025)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{2}/\\d{2}/\\d{4}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'MM/DD/YYYY')
        -- DD/MM/YYYY (e.g., 13/04/2025)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{2}/\\d{2}/\\d{4}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'DD/MM/YYYY')
        -- YYYY/MM/DD (e.g., 2025/04/13)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{4}/\\d{2}/\\d{2}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'YYYY/MM/DD')
        -- MM-DD-YYYY (e.g., 04-13-2025)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{2}-\\d{2}-\\d{4}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'MM-DD-YYYY')
        -- DD-MON-YYYY (e.g., 13-APR-2025)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{2}-[A-Za-z]{3}-\\d{4}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'DD-MON-YYYY')
        -- DD MON YYYY (e.g., 13 APR 2025)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{2} [A-Za-z]{3} \\d{4}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'DD MON YYYY')
        -- MON DD, YYYY (e.g., APR 13, 2025)
        WHEN "${fieldAlias}"."field_value" ~ '^[A-Za-z]{3} \\d{1,2}, \\d{4}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'MON DD, YYYY')
        -- YYYY-MM-DD HH:MI:SS (e.g., 2025-04-13 10:30:00)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'YYYY-MM-DD HH24:MI:SS')
        -- MM/DD/YYYY HH:MI:SS (e.g., 04/13/2025 10:30:00)
        WHEN "${fieldAlias}"."field_value" ~ '^\\d{2}/\\d{2}/\\d{4} \\d{2}:\\d{2}:\\d{2}$' THEN TO_TIMESTAMP("${fieldAlias}"."field_value", 'MM/DD/YYYY HH24:MI:SS')
        ELSE NULL
    END)`;
                };

                switch (operator) {
                    case 'equal':
                        if (['custom_datePicker', 'date'].includes(type)) {
                            return `${getDateConversionCase(fieldId)} = ${formattedValue}`;
                        }
                        return `"${fieldId}"."field_value" = ${formattedValue}`;
                    case 'not_equal':
                        if (['custom_datePicker', 'date'].includes(type)) {
                            return `${getDateConversionCase(fieldId)} != ${formattedValue}`;
                        }
                        return `"${fieldId}"."field_value" != ${formattedValue}`;
                    case 'select_equals':
                        return `jsonb_exists("${fieldId}"."field_option_ids", '${value[0]}')`;
                    case 'select_not_equals':
                        return `NOT jsonb_exists("${fieldId}"."field_option_ids", '${value[0]}')`;
                    case 'is_empty':
                        return `("${fieldId}"."field_value" = '' OR "${fieldId}"."field_value" IS NULL)`;
                    case 'is_not_empty':
                        return `("${fieldId}"."field_value" != '' OR "${fieldId}"."field_value" IS NOT NULL)`;
                    case 'select_any_in':
                        return `"${fieldId}"."field_option_ids" ?| array['${value[0].join("', '")}']`;
                    case 'greater':
                        switch (valueType[0]) {
                            case 'number':
                                return `"${fieldId}"."field_value"::float8 > ${formattedValue}`;
                            case 'custom_datePicker':
                            case 'date':
                                // Apply the CASE statement for date conversion
                                return `${getDateConversionCase(fieldId)} > ${formattedValue}`;
                            default:
                                return `"${fieldId}"."field_value" > '${formattedValue}'`;
                        }
                    case 'greater_or_equal':
                        switch (valueType[0]) {
                            case 'number':
                                return `"${fieldId}"."field_value"::float8 >= ${formattedValue}`;
                            case 'custom_datePicker':
                            case 'date':
                                // Apply the CASE statement for date conversion
                                return `${getDateConversionCase(fieldId)} >= ${formattedValue}`;
                            default:
                                return `"${fieldId}"."field_value" >= '${formattedValue}'`;
                        }
                    case 'less':
                        switch (valueType[0]) {
                            case 'number':
                                return `"${fieldId}"."field_value"::float8 < ${formattedValue}`;
                            case 'custom_datePicker':
                            case 'date':
                                // Apply the CASE statement for date conversion
                                return `${getDateConversionCase(fieldId)} < ${formattedValue}`;
                            default:
                                return `"${fieldId}"."field_value" < '${formattedValue}'`;
                        }
                    case 'less_or_equal':
                        switch (valueType[0]) {
                            case 'number':
                                return `"${fieldId}"."field_value"::float8 <= ${formattedValue}`;
                            case 'custom_datePicker':
                            case 'date':
                                // Apply the CASE statement for date conversion
                                return `${getDateConversionCase(fieldId)} <= ${formattedValue}`;
                            default:
                                return `"${fieldId}"."field_value" <= '${formattedValue}'`;
                        }
                    default:
                        return `"${fieldId}"."field_value" = ${formattedValue}`;
                }
            }
            return '';
        }


        try {
            const filterConditionSql = buildSqlCondition(clonedCondition);
            return filterConditionSql;
        } catch (error) {
            return '';
        }
    }

    public static getActiveFormVersionCacheKeys = ({
        accountId,
        formId,
        formVersionId,
    }: {
        accountId: string;
        formId: string;
        formVersionId: string;
    }) => {
        if (!accountId || !formId || !formVersionId) return null;
        return {
            formVersionKey: `av:acc_${accountId}:f_${formId}:fv_${formVersionId}`,
        };
    };

    public static getActiveApiVersionCacheKeys = ({
        accountId,
        formId,
    }: {
        accountId: string;
        formId: string;
    }) => {
        if (!accountId || !formId) return null;
        return {
            apiVersionKey: `apiv:acc_${accountId}:f_${formId}`,
        };
    };


    public static getActiveDrVersionCacheKeys = ({
        accountId,
        drId,
        drVersionId,
    }: {
        accountId: string;
        drId: string;
        drVersionId: string;
    }) => {
        if (!accountId || !drId || !drVersionId) return null;
        const drVersionFolder: string = `av:acc_${accountId}:dr_${drId}`;
        return {
            drVersionKey: `${drVersionFolder}:drv_${drVersionId}`,
            drVersionFolder,
        };
    };

    public static getPurpleTRACCacheKeys = ({
        accountId,
        transactionId,
        imoNumber,
    }: {
        accountId: string;
        transactionId: string;
        imoNumber: string;
    }) => {
        if (!accountId || !transactionId || !imoNumber) return null;
        return {
            transactionKey: `ppt:acc_${accountId}:t_${transactionId}:imo_${imoNumber}`,
        };
    };

    public static format<
        T extends Partial<{
            createdAt: Date;
            createdBy: string;
            updatedAt: Date;
            updatedBy: string;
            deletedAt: Date;
            deletedBy: string;
            deletedByUser: string;
            updatedByUser: string;
            createdByUser: string;
        }>,
    >(
        items: Array<T>,
    ): Array<
        Omit<
            T,
            | 'createdAt'
            | 'createdBy'
            | 'updatedAt'
            | 'updatedBy'
            | 'deletedAt'
            | 'deletedBy'
            | 'deletedByUser'
            | 'updatedByUser'
            | 'createdByUser'
        >
    > {
        return items.map((item) => {
            const {
                createdAt,
                createdBy,
                updatedAt,
                updatedBy,
                deletedAt,
                deletedBy,
                deletedByUser,
                updatedByUser,
                createdByUser,
                ...rest
            } = item;

            return rest;
        });
    }

    public static getCustomDateValue(value: any) {
        if (!value) return value;
        if (value.additionalType === 'fixedDay') {
            return [value.additionalDate];
        }
        switch (value.additionalDurationType) {
            case 'day': {
                return UtilsService.calculationDate('day', value.operator, value.additionalNumber, value.additionalTime);
            }
            case 'month': {
                return UtilsService.calculationDate('month', value.operator, value.additionalNumber, value.additionalTime);
            }
            case 'year': {
                return UtilsService.calculationDate('year', value.operator, value.additionalNumber, value.additionalTime);
            }
        }
    };

    public static calculationDate(
        type: DateType,
        operator: string,
        number: number,
        additionalTime?: string, // Optional parameter
    ) {
        let newDate = dayjs();

        // Handle operator and type
        if (operator === '+') {
            switch (type) {
                case 'day':
                    newDate = newDate.add(number, 'day');
                    break;
                case 'month':
                    newDate = newDate.add(number, 'month');
                    break;
                case 'year':
                    newDate = newDate.add(number, 'year');
                    break;
                default:
                    throw new Error('Invalid type');
            }
        } else if (operator === '-') {
            switch (type) {
                case 'day':
                    newDate = newDate.subtract(number, 'day');
                    break;
                case 'month':
                    newDate = newDate.subtract(number, 'month');
                    break;
                case 'year':
                    newDate = newDate.subtract(number, 'year');
                    break;
                default:
                    throw new Error('Invalid type');
            }
        } else {
            throw new Error('Invalid operator');
        }

        // Parse and add the additional time if provided
        if (additionalTime) {
            const [hours, minutes] = additionalTime.split(':').map(Number);
            newDate = newDate.set('hour', hours).set('minute', minutes);
            return [newDate.format(YYYYMMDDHHmm)];
        }
        return [newDate.format(YYYYMMDD)]; // Includes time if `additionalTime` is set
    };
}


