import { Injectable } from '@nestjs/common';
import { uniq } from 'lodash';
import { Brackets, FindManyOptions, In } from 'typeorm';
import { LoggerService } from '../../../modules/shared';
import {
    CommonFormFieldBase,
    CommonFormStageBase,
    FormRoleStageACLDto,
    StageRoleACLBase,
    StageRoleBase,
} from '../dto/form-role-stage-acl.dto';
import { AccessControlType } from '../enums/access-control-type.enum';
import { IStageRoleACLProvider } from '../interfaces/stage-role-acl.interface';
import {
    GetFieldsConfigurationsRequest,
    GetFormStagesRequest,
    GetRoleStageACLsRequest,
    LoadFormRoleStageACLRequest,
} from '../request/form-stage-role.request';
import { StageRoleAclCacheService } from './stage-role-acl.cache.service';

@Injectable()
export class StageRoleACLProvider implements IStageRoleACLProvider {
    constructor(
        private readonly cacheService: StageRoleAclCacheService,
        private readonly loggerService: LoggerService,
    ) { }

    public async getRoleStageACLs({
        request,
        stageRoleRepo,
        stageRoleACLRepo,
        fieldsRepo,
    }: GetRoleStageACLsRequest): Promise<FormRoleStageACLDto[]> {
        const cacheKey = this.getCacheKey(request);

        try {
            const result = await this.cacheService.get<FormRoleStageACLDto[]>(request.accountId ?? 'public', cacheKey);
            if (result) {
                return result;
            }
        } catch (err) {
            this.loggerService.error(err);
        }

        try {
            const { formVersionId, stageRoles } = request;
            if (!stageRoles?.length) return [];

            const builder = stageRoleRepo.createQueryBuilder('sr').where({ formVersionId });

            builder.andWhere(
                new Brackets((qb) => {
                    stageRoles.forEach((sr, i) => {
                        qb.orWhere(
                            new Brackets((qb1) => {
                                qb1.andWhere(`sr.roleId = :roleId${i}`, { roleId: sr.roleId }).andWhere(`sr.stageId = :stageId${i}`, {
                                    [`roleId${i}`]: sr.roleId,
                                    [`stageId${i}`]: sr.stageId,
                                });
                            }),
                        );
                    });
                }),
            );

            const entities = await builder.getMany();

            const stageRoleIds = entities.map((sr) => sr.id);
            if (!stageRoleIds.length) {
                return [];
            }
            const stageRoleACLs = await stageRoleACLRepo.findBy({
                stageRoleId: In(stageRoleIds),
            });

            // role has no permission on stage
            if (!stageRoleACLs.length) return [];

            let fieldsConfigurations: CommonFormFieldBase[] = [];
            if (request.includeFieldConfig) {
                const fieldIds = stageRoleACLs.filter((sra) => sra.type === 'field').map((sra) => sra.targetId);
                const uniqFieldIds = uniq(fieldIds);
                if (uniqFieldIds.length) {
                    fieldsConfigurations = await fieldsRepo.find({
                        where: {
                            fieldId: In(uniqFieldIds),
                            formVersionId,
                        },
                        select: ['fieldId', 'type', 'label', 'configuration'],
                    });
                }
            }

            const roleStageACL = this._buildACLs({
                entities: stageRoleACLs,
                stageRoles: entities,
                fieldsConfigurations,
            });

            await this.cacheService.set(request.accountId ?? 'public', cacheKey, roleStageACL);

            return roleStageACL;
        } catch (err) {
            console.error(err);
            return null;
        }
    }

    private getCacheKey(request: LoadFormRoleStageACLRequest): string {
        return `FORM_VERSION:${request.formVersionId}:ROLE_STAGE:${request.stageRoles
            .sort((a, b) => {
                const r = a.roleId.localeCompare(b.roleId);
                if (r === 0) {
                    return a.stageId.localeCompare(b.stageId);
                }
                return r;
            })
            .map((sr) => `${sr.roleId}:${sr.stageId}`)}:${request.includeFieldConfig ? 'T' : 'F'}`;
    }

    public async getFieldsConfigurations<T>({ fieldIds, formVersionId, fieldRepo, select }: GetFieldsConfigurationsRequest) {
        if (!fieldIds?.length || !formVersionId) return [];

        const fields = await fieldRepo.find({
            where: {
                fieldId: In(fieldIds),
                formVersionId,
            },
            select: select as FindManyOptions<CommonFormFieldBase>['select'],
        });
        return fields as T[];
    }

    public async getFormStages<T>({ formId, stageRepo, select, formRepo }: GetFormStagesRequest): Promise<T[]> {
        if (!formId) return [];

        const form = await formRepo.findOneBy({ id: formId });
        if (!form?.activeVersionId) return [];

        const stages = await stageRepo.find({
            where: {
                formVersionId: form.activeVersionId,
            },
            select: select as FindManyOptions<CommonFormStageBase>['select'],
        });
        return stages as T[];
    }

    private _buildACLs({
        entities,
        stageRoles,
        fieldsConfigurations = [],
    }: {
        entities: StageRoleACLBase[];
        stageRoles: StageRoleBase[];
        fieldsConfigurations?: CommonFormFieldBase[];
    }): FormRoleStageACLDto[] {
        const initialValue: FormRoleStageACLDto[] = [];
        const result = entities.reduce((acls, entity) => {
            const { targetId, type, config, stageRoleId, collectionItemIdentityId } = entity;
            const existStageRole = stageRoles.find((sr) => sr.id === stageRoleId);
            if (!existStageRole) {
                return acls;
            }

            let existedStageRoleACL = acls.find((acl) => acl.roleId === existStageRole.roleId && acl.stageId === existStageRole.stageId);
            if (!existedStageRoleACL) {
                existedStageRoleACL = {
                    roleId: existStageRole.roleId,
                    stageId: existStageRole.stageId,
                    field: {},
                    collection: {},
                    decision: {},
                    relation: {},
                    fieldsConfigurations: [],
                    canCreate: existStageRole.canCreate,
                    canDelete: existStageRole.canDelete,
                    createDeleteCollection: {},
                };
                acls.push(existedStageRoleACL);
            }

            switch (type) {
                case AccessControlType.FIELD:
                    existedStageRoleACL.field[targetId] = {
                        visible: config?.visible,
                        editable: config?.editable,
                        required: config?.required,
                        override: config?.override,
                    };
                    const fieldConfiguration = fieldsConfigurations.find((f) => f.fieldId === targetId);
                    if (fieldConfiguration) {
                        existedStageRoleACL.fieldsConfigurations.push(fieldConfiguration);
                    }
                    break;

                case AccessControlType.COLLECTION:
                    existedStageRoleACL.collection[`${targetId}_${collectionItemIdentityId}`] = {
                        visible: config?.visible,
                        editable: config?.editable,
                        required: config?.required,
                        override: config?.override,
                    };
                    break;

                case AccessControlType.DECISION:
                    existedStageRoleACL.decision[targetId] = {
                        enable: config?.enable,
                    };
                    break;

                case AccessControlType.RELATION:
                    existedStageRoleACL.relation[targetId] = {
                        visible: config?.visible,
                        editable: config?.editable,
                        required: config?.required,
                    };
                    break;

                case AccessControlType.CREATE_DELETE_COLLECTION:
                    existedStageRoleACL.createDeleteCollection[targetId] = {
                        canCreate: config?.canCreate,
                        canDelete: config?.canDelete,
                    };
                    break;
            }

            return acls;
        }, initialValue);

        return result;
    }
}
