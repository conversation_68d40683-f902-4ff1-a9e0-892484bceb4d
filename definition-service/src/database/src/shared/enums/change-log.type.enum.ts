export enum ChangeLogTargetType {
    INFORMATION = 'information',
    FIELDS = 'fields',
    COLLECTIONS = 'collections',
    COLLECTION_ITEMS = 'collectionItems',
    STAGES = 'stages',
    STAGE_TRANSITION = 'stageTransitions',
    STAGE_DECISION = 'stageDecisions',
    LAYOUT = 'layout',
    LAYOUT_ZONE = 'layoutZones',
    LAYOUT_ZONE_FIELD = 'layoutZoneFields',
    STAGE_ROLE = 'stageRoles',
    STAGE_ACCESS_CONTROL = 'stageAccessControls',
    STAGE_ROLE_ACCESS_CONTROL = 'stageRoleAccessControls',
    WIDGET = 'widget',
    RELATIONS = 'relations',
    VIEW = 'formViews',
    SETTING = 'setting',
    AUTOMATION_RULE_CONFIGURATION = 'automationRuleConfiguration',
    AUTOMATION_ACTION_CONFIGURATION = 'automationActionConfiguration',
    AUTOMATION_EVENT_CONFIGURATION = 'automationEventConfiguration',
    KANBAN_VIEW_COLUMNS = 'kanbanViewColumns',
    KANBAN_VIEW_SWIMLANE = 'kanbanViewSwimlanes',
    KANBAN_VIEW_LAYOUT = 'kanbanViewLayouts',
}

export enum WsChangeLogTargetType {
    INFORMATION = 'information',
    LAYOUT = 'layout',
    LAYOUT_ZONE = 'layoutZones',
    LAYOUT_ZONE_FIELD = 'layoutZoneFields',
    ACCESS_CONTROL = 'workspace_accessControls',
    CONFIGURATION = 'workspace_configuration',
    WIDGET = 'workspace_widget',
}

export enum ChangeLogActionType {
    ADDED = 'added',
    UPDATED = 'updated',
    DELETED = 'deleted',
}

enum ActionLogType {
    STATUS_UPDATE = 'STATUS_UPDATE',
    RETRY_ATTEMPT = 'RETRY_ATTEMPT',
    EXECUTE_ACTION = 'EXECUTE_ACTION',
}

export enum DocumentChangeLogTargetType {
    INFORMATION = 'information',
    DOCUMENT_ZONE = 'documentZones',
    DOCUMENT_FIELD = 'documentFields',
}

export enum SourceOfChangeType {
    MANUAL = 'manual',
    AUTOMATION = 'automation',
    AUTO_POPULATE = 'auto_populate',
}

export enum TransactionDataType {
    REGISTER = 'register',
    FORM_TRANSACTION = 'form_transaction',
    FORM_TRANSACTION_FIELD = 'form_transaction_field',
}

export enum TransactionActionTypeEnum {
    EMAIL_SENDING = 'email_sending',
    EMAIL_SENT = 'email_sent',
    EMAIL_SENT_FAILED = 'email_sent_failed',
    EMAIL_NOTIFICATION = 'email_notification',
    EXTERNAL_USER_ACCESS = 'external_user_access',
    // TODO: other action should be here
}

export enum TransactionActionStatusEnum {
    EMAIL_SENDING = 'email_sending',
    EMAIL_DELIVERED = 'email_delivered',
    EMAIL_FAILED = 'email_sending_failed',
}
