import { AccessControlType } from '../enums/access-control-type.enum';
import { StageRoleAccessControlConfig } from './stage-role-acl.config.dto';

export class StageRoleBase {
    id?: string;
    formVersionId: string;
    roleId: string;
    stageId: string;
    canCreate?: boolean;
    canDelete?: boolean;
}

export class StageRoleACLBase {
    id?: string;
    stageRoleId: string;
    targetId: string;
    collectionItemIdentityId?: string;
    type?: AccessControlType;
    config?: StageRoleAccessControlConfig;
}

export class StageDecisionBase {
    id?: string;
    stageId: string;
    config: Array<any>;
}

export class FormFieldBase {
    fieldId: string;
    id?: string;
    type?: string;
    label?: string;
    configuration?: Record<string, any>;
}

export class CommonFormFieldBase extends FormFieldBase {
    formVersionId?: string;
}

export class CommonFormStageBase {
    id?: string;
    formVersionId?: string;
    identityId?: string;
}

export class CommonFormBase {
    id?: string;
    activeVersionId?: string;
    latestVersionId?: string;
}

export class FormRoleStageACLDto {
    collection: Record<string, StageRoleAccessControlConfig>;
    decision: Record<string, StageRoleAccessControlConfig>;
    field: Record<string, StageRoleAccessControlConfig>;
    relation: Record<string, StageRoleAccessControlConfig>;
    createDeleteCollection: Record<string, StageRoleAccessControlConfig>;
    roleId: string;
    stageId: string;
    canCreate?: boolean;
    canDelete?: boolean;
    fieldsConfigurations: CommonFormFieldBase[];
}
