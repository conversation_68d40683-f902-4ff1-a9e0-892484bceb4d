import { Repository } from 'typeorm';
import { CommonFormBase, CommonFormFieldBase, CommonFormStageBase, StageRoleACLBase, StageRoleBase } from '../dto/form-role-stage-acl.dto';

export class StageRoleACLRequest {
    roleId: string;
    stageId: string;
}

export class LoadFormRoleStageACLRequest {
    accountId?: string;
    stageRoles: StageRoleACLRequest[];
    formVersionId: string;
    includeFieldConfig?: boolean;
}

export type GetRoleStageACLsRequest = {
    request: LoadFormRoleStageACLRequest;
    stageRoleRepo: Repository<StageRoleBase>;
    stageRoleACLRepo: Repository<StageRoleACLBase>;
    fieldsRepo?: Repository<CommonFormFieldBase>;
    ignoreCache?: boolean;
};

export type GetFieldsConfigurationsRequest = {
    fieldIds: string[];
    formVersionId: string;
    fieldRepo: Repository<CommonFormFieldBase>;
    select?: string[];
};

export type GetFormStagesRequest = {
    formId: string;
    select?: string[];
    stageRepo: Repository<CommonFormStageBase>;
    formRepo: Repository<CommonFormBase>;
    isTest?: boolean;
};
