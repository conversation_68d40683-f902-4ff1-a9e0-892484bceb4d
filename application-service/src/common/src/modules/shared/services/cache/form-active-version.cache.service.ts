import { Injectable } from '@nestjs/common';
import { CacheService } from '../cache.service';
import { LoggerService } from '../logger.service';
import { UtilsService } from '../utils.service';

@Injectable()
export class CacheFormActiveVersionService {
    constructor(
        private readonly _cacheService: CacheService,
        private readonly _logger: LoggerService,
    ) {}

    async capture({
        formVersionId,
        accountId,
        formId,
        cacheValue,
    }: {
        formVersionId: string;
        accountId: string;
        formId: string;
        cacheValue: string;
    }) {
        const cacheKeys = UtilsService.getActiveFormVersionCacheKeys({
            accountId,
            formId,
            formVersionId,
        });

        if (!cacheKeys) return;

        const { formVersionKey } = cacheKeys;

        this._logger.info(`START CACHE - ACTIVE FORM - ${formVersionKey}`);
        //save cache key in 1 day
        const ttl = 60 * 60 * 24;
        await this._cacheService.set(formVersionKey, cacheValue, ttl);
        this._logger.info(`END CACHE - ACTIVE FORM - ${formVersionKey}`);
    }

    async captureApiVersion({ accountId, cacheValue, formId }: { accountId: string; cacheValue: string; formId: string }) {
        const cacheKeys = UtilsService.getActiveApiVersionCacheKeys({
            accountId,
            formId,
        });

        if (!cacheKeys) return;

        const { apiVersionKey } = cacheKeys;

        this._logger.info(`START CACHE - ACTIVE API VERSION - ${apiVersionKey}`);
        //save cache key in 1 day
        const ttl = 60 * 60 * 24;
        await this._cacheService.set(apiVersionKey, cacheValue, ttl);
        this._logger.info(`END CACHE - ACTIVE API VERSION - ${apiVersionKey}`);
    }
}
