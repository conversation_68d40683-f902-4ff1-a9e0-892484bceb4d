export enum ActionEventEnum {
    EMAIL = 'ACTION.EMAIL',
    NOTIFICATION = 'ACTION.NOTIFICATION',
    CREATE_TRANSACTION = 'ACTION.CREATE_TRANSACTION',
    CHANGE_TRANSACTION_WORKFLOW_STAGE = 'ACTION.CHANGE_TRANSACTION_WORKFLOW_STAGE',
    UPDATE_TRANSACTION_FIELD = 'ACTION.UPDATE_TRANSACTION_FIELD',
    UPDATE_REGISTER_FIELD = 'ACTION.UPDATE_REGISTER_FIELD',
    CREATE_REGISTER = 'ACTION.CREATE_REGISTER',
    ROLLBACK = 'ACTION.ROLLBACK',
    EXPORT_PDF = 'ACTION.PDF',
    CREATE_TRANSACTION_BY_EXTERNAL = 'ACTION.CREATE_TRANSACTION_BY_EXTERNAL',
    CREATE_UPDATE_REGISTER = 'ACTION.CREATE_UPDATE_REGISTER',
    DOC_PDF_EXTRACTION = 'ACTION.DOC_PDF_EXTRACTION',
    REFRESH_FROM_TRANSACTION_DATA = 'ACTION.REFRESH_FROM_TRANSACTION_DATA',
    REFRESH_FROM_TRANSACTION_FIELDS_DATA = 'ACTION.REFRESH_FROM_TRANSACTION_FIELDS_DATA',
    REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA = 'ACTION.REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA',
    CREATE_RELATED_TRANSACTION = 'ACTION.CREATE_RELATED_TRANSACTION',
    POPULATE_PURPLETRAC = 'ACTION.POPULATE_PURPLETRAC',
    UPDATE_COLLECTION_FIELD = 'ACTION.UPDATE_COLLECTION_FIELD',
    CALCULATE_SYSTEM_SCORE = 'ACTION.CALCULATE_SYSTEM_SCORE',
    CREATE_TRANSACTION_BY_DATASOURCE = 'ACTION.CREATE_TRANSACTION_BY_DATASOURCE',
    ROLLUP_FORM_TRANSACTIONS = 'ACTION.ROLLUP_FORM_TRANSACTIONS',
    ROLLUP_DATA_REGISTER_TRANSACTIONS = 'ACTION.ROLLUP_REGISTER_RECORDS',
    REFRESH_DATA_REGISTER_DISPLAY_VALUE = 'ACTION.REFRESH_DATA_REGISTER_DISPLAY_VALUE',
    POPULATE_Q88 = 'ACTION.DATA_PIPELINE_POPULATE_Q88',
    Q88_HVPQ_DATA_POPULATE = 'ACTION.Q88_HVPQ_DATA_POPULATE',
    WEBHOOK = 'ACTION.WEBHOOK',
    RUN_LLM = 'ACTION.RUN_LLM',
}
