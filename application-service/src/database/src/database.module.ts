import { DynamicModule, Global, Module, Provider, Scope, UnauthorizedException, ValueProvider } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { jwtDecode } from 'jwt-decode';
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { DATABASE_OPTIONS, TENANT_HEADER } from './constants/database-option.constant';
import { PROVIDER_KEYS } from './constants/providers';
import { DatabaseAsyncOptions } from './interfaces/database-options-async-provider.interface';
import { DatabaseOptionsFactory } from './interfaces/database-options-factory.interface';
import { DatabaseOptions } from './interfaces/database-options-provider.interface';
import { actionLogProviders } from './providers/action-log-providers';
import { authProviders } from './providers/auth.provider';
import { automationProviders } from './providers/automation.provider';
import { dataRegisterProviders } from './providers/data-register-provider';
import { documentProviders } from './providers/document-provider';
import { eventLogProviders } from './providers/event-log-provider';
import { formProviders } from './providers/form-provider';
import { docProviders } from './providers/pdf-doc-provider';
import { storageProviders } from './providers/storage-provider';
import { widgetProviders } from './providers/widget-provider';
import { workspaceProviders } from './providers/workspace-providers';
import { AccountConnectionService } from './services/account-connection.service';
import { DataSourceService } from './services/connection-util.service';
import { EntitySubscriber } from './shared/subscribers/entity.subscriber';
import { apiProviders } from './providers/api-providers';

const moduleProviders = [
    AccountConnectionService,
    DataSourceService,
    ...authProviders,
    ...dataRegisterProviders,
    ...documentProviders,
    ...formProviders,
    ...workspaceProviders,
    ...widgetProviders,
    ...eventLogProviders,
    ...automationProviders,
    ...storageProviders,
    ...actionLogProviders,
    ...docProviders,
    ...apiProviders,
    EntitySubscriber,
    {
        provide: PROVIDER_KEYS.DATA_SOURCE,
        useFactory: async (options: DatabaseOptions, subscriber: EntitySubscriber) => {
            const dataSource = new DataSource(options.connectionString);
            const connection = await dataSource.initialize();
            if (connection.subscribers.length === 0) {
                connection.subscribers.push(subscriber);
            }
            return connection;
        },
        inject: [DATABASE_OPTIONS, EntitySubscriber],
    },
    {
        provide: PROVIDER_KEYS.TENANT_CONNECTION,
        inject: [REQUEST, AccountConnectionService],
        scope: Scope.REQUEST,
        useFactory: async (request, connectionService: AccountConnectionService) => {
            if (!request?.headers?.authorization && !request?.headers?.[TENANT_HEADER]) {
                return new DataSource({
                    type: 'postgres',
                });
            }

            let schema;
            if (request.headers['x-tenant']) {
                schema = request.headers['x-tenant'];
            } else if (request.headers.authorization) {
                try {
                    const user: any = jwtDecode(request.headers.authorization);
                    schema = request.headers['x-tenant'] ?? user?.accountId;
                } catch (err) {
                    console.error(err);
                }
            }

            if (!schema) {
                schema = 'no-schema';
            }

            const options = {
                schema: schema,
            } as PostgresConnectionOptions;
            try {
                return await connectionService.getAccountConnection(options);
            } catch (er) {
                console.error(er);
            }

            throw new UnauthorizedException();
        },
    },
];
const exporters = moduleProviders;
const importers = [
    EventEmitterModule.forRoot({
        wildcard: true,
    }),
];

@Global()
@Module({
    providers: moduleProviders,
    exports: exporters,
    imports: importers,
})
export class DatabaseModule {
    static forRoot(options: DatabaseOptions): DynamicModule {
        const databaseOptionsProvider: ValueProvider<DatabaseOptions> = {
            provide: DATABASE_OPTIONS,
            useValue: options,
        };

        return {
            module: DatabaseModule,
            imports: [...importers],
            providers: [databaseOptionsProvider, ...moduleProviders],
            exports: exporters,
        };
    }

    public static forRootAsync(options: DatabaseAsyncOptions): DynamicModule {
        const providers: Provider[] = this.createAsyncProviders(options);
        return {
            module: DatabaseModule,
            imports: [...importers, ...(options.imports ?? [])],
            providers: [...providers, ...moduleProviders, ...(options.extraProviders || [])],
            exports: exporters,
        };
    }

    private static createAsyncProviders(options: DatabaseAsyncOptions): Provider[] {
        const providers: Provider[] = [this.createAsyncOptionsProvider(options)];

        if (options.useClass) {
            providers.push({
                provide: options.useClass,
                useClass: options.useClass,
            });
        }

        return providers;
    }

    private static createAsyncOptionsProvider(options: DatabaseAsyncOptions): Provider {
        if (options.useFactory) {
            return {
                name: DATABASE_OPTIONS,
                provide: DATABASE_OPTIONS,
                useFactory: options.useFactory,
                inject: options.inject || [],
            };
        }

        return {
            name: DATABASE_OPTIONS,
            provide: DATABASE_OPTIONS,
            useFactory: async (optionsFactory: DatabaseOptionsFactory) => {
                return optionsFactory.createOptions();
            },
            inject: [options.useExisting! || options.useClass!],
        };
    }
}
