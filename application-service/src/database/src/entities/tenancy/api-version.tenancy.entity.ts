import { AutoMap } from '@automapper/classes';
import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { ApiTenancyEntity } from './api.tenancy.entity';
import { ApiVersionStatus } from '../../shared/enums/api.enum';
import { ApiVersionCommentTenancyEntity } from './api-version-comment.tenancy.entity';
import { ApiVersionChangeLogTenancyEntity } from './api-version-change-log.tenancy.entity';
import { ApiVersionEndpointTenancyEntity } from './api-version-endpoint.tenancy.entity';

@Entity({
    name: 'api_versions',
    synchronize: false,
})
export class ApiVersionTenancyEntity extends AbstractEntity {
    @Column({
        name: 'api_id',
        type: 'uuid',
    })
    @AutoMap()
    apiId: string;

    @Column({
        name: 'version',
        type: 'int',
    })
    @AutoMap()
    version: number;

    @Column({
        name: 'context_version_id',
        type: 'uuid',
    })
    @AutoMap()
    contextVersionId: string;

    @Column({ type: 'varchar', length: 100, default: ApiVersionStatus.Draft })
    @AutoMap()
    status: ApiVersionStatus;

    @Column({
        default: true,
    })
    @AutoMap()
    isEnable: boolean;

    @Column({
        name: 'configuration',
        type: 'jsonb',
        nullable: true,
    })
    configuration?: Record<string, any>;

    @AutoMap()
    @Column({ default: null, nullable: true })
    publishedBy?: string | null;

    @Column({ name: 'published_by_user', default: null, nullable: true })
    @AutoMap()
    publishedByUser?: string | null;

    @AutoMap()
    @Column({ default: null, nullable: true })
    publishedAt?: Date;

    //#region  Relations

    @ManyToOne(() => ApiTenancyEntity, (d) => d.versions)
    @JoinColumn({ name: 'api_id' })
    api?: ApiTenancyEntity;

    @OneToMany(() => ApiVersionCommentTenancyEntity, (d) => d.apiVersion, { cascade: ['soft-remove', 'insert', 'update'] })
    @AutoMap(() => [ApiVersionCommentTenancyEntity])
    comments?: ApiVersionCommentTenancyEntity[];

    @OneToMany(() => ApiVersionChangeLogTenancyEntity, (d) => d.apiVersionId, { cascade: ['soft-remove', 'insert', 'update'] })
    @AutoMap(() => [ApiVersionChangeLogTenancyEntity])
    changeLogs?: ApiVersionChangeLogTenancyEntity[];

    @OneToMany(() => ApiVersionEndpointTenancyEntity, (d) => d.apiVersionId, { cascade: ['soft-remove', 'insert', 'update'] })
    @AutoMap(() => [ApiVersionEndpointTenancyEntity])
    endpoints?: ApiVersionEndpointTenancyEntity[];

    //#endregion Relations
}
