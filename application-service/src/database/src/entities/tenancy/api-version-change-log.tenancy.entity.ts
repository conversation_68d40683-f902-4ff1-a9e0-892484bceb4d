import { AutoMap } from '@automapper/classes';
import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { ChangeLogActionType, ChangeLogTargetType } from '../../shared/enums/change-log.type.enum';

@Entity({ name: 'api_version_change_logs', synchronize: false })
export class ApiVersionChangeLogTenancyEntity extends AbstractEntity {
    @AutoMap()
    @Column({
        type: 'timestamp',
        name: 'changed_at',
        nullable: true,
    })
    changedAt: string;

    @AutoMap()
    @Column({
        name: 'changed_by',
        nullable: true,
    })
    changedBy: string;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    comparedId: string;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    originId: string;

    @AutoMap()
    @Column({ type: 'uuid' })
    apiVersionId: string;

    @AutoMap()
    @Column({ type: 'jsonb', nullable: true })
    content: Record<string, any>;

    @AutoMap(() => String)
    @Column({ type: 'varchar', length: 50 })
    type: ChangeLogTargetType;

    @AutoMap(() => String)
    @Column({ type: 'varchar', length: 20 })
    actionType: ChangeLogActionType;
}
