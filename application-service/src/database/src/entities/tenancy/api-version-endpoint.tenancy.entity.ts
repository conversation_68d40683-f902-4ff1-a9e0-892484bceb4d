import { AutoMap } from '@automapper/classes';
import { Column, <PERSON>tity, Join<PERSON>olumn, ManyToOne, Unique } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { ApiVersionTenancyEntity } from './api-version.tenancy.entity';
import { API_ENDPOINT_METHODS, API_ENDPOINTS } from '../../constants/api-endpoints';

@Entity({
    name: 'api_version_endpoints',
    synchronize: false,
})
@Unique(['endpoint', 'method', 'apiVersionId'])
export class ApiVersionEndpointTenancyEntity extends AbstractEntity {
    @Column({
        name: 'api_version_id',
        type: 'uuid',
    })
    @AutoMap()
    apiVersionId: string;

    @Column({
        name: 'method',
        type: 'varchar',
        length: 100,
    })
    @AutoMap()
    method: API_ENDPOINT_METHODS;

    @Column({
        name: 'endpoint',
        type: 'varchar',
        length: 1000,
    })
    @AutoMap()
    endpoint: API_ENDPOINTS;

    @Column({
        name: 'name',
        type: 'varchar',
        length: 150,
        default: '',
        nullable: true,
    })
    @AutoMap()
    name: string;

    @Column({ name: 'identity_id', type: 'uuid', nullable: true })
    @AutoMap()
    identityId?: string;

    @Column({
        name: 'configuration',
        type: 'jsonb',
        nullable: true,
        default: {},
    })
    @AutoMap()
    configuration?: Record<string, any>;

    @ManyToOne(() => ApiVersionTenancyEntity, (version) => version.endpoints)
    @JoinColumn({ name: 'api_version_id' })
    apiVersion?: ApiVersionTenancyEntity;
}
