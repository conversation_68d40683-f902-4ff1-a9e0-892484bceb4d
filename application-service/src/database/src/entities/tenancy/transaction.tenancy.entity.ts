import { AutoMap } from '@automapper/classes';
import { <PERSON>umn, CreateDateColumn, Entity, Join<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { FormVersionTenancyEntity } from './form-version.tenancy.entity';
import { TransactionFieldStyleEntity } from './transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from './transaction-field.tenancy.entity';
import { AutoCreationStatus, RollupStatus } from '../../enums/transaction-status.enum';

@Entity({
    name: 'transaction',
    synchronize: false,
})
export class TransactionEntity extends AbstractEntity {
    @AutoMap()
    @CreateDateColumn({ name: 'stage_entered_at' })
    stageEnteredAt: Date;

    @Column({
        name: 'stage_id',
        type: 'uuid',
    })
    @AutoMap()
    stageId: string;

    @Column({
        name: 'previous_stage_id',
        type: 'uuid',
        nullable: true,
    })
    @AutoMap()
    previousStageId: string;

    @Column({
        name: 'stage_name',
        type: 'varchar',
        length: 300,
        nullable: true,
    })
    @AutoMap()
    stageName?: string;

    @Column({
        name: 'previous_stage_name',
        type: 'varchar',
        length: 300,
        nullable: true,
    })
    @AutoMap()
    previousStageName?: string;

    @Column({
        name: 'form_version_id',
        type: 'uuid',
    })
    @AutoMap()
    formVersionId: string;

    @Column({
        name: 'form_id',
        type: 'uuid',
    })
    @AutoMap()
    formId?: string;

    @Column({ name: 'rollup_status', type: 'jsonb', nullable: true })
    @AutoMap()
    rollupStatus: RollupStatus;

    @Column({ name: 'auto_creation_status', type: 'jsonb', nullable: true })
    @AutoMap()
    autoCreationStatus: AutoCreationStatus;

    @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
    @AutoMap()
    metadata: Record<string, any>;

    @Column({ name: 'is_test', type: 'boolean', default: false })
    @AutoMap()
    isTest: boolean;

    @ManyToOne(() => FormVersionTenancyEntity, (d) => d.transactions)
    @JoinColumn({ name: 'form_version_id' })
    formVersion?: FormVersionTenancyEntity;

    @OneToMany(() => TransactionFieldEntity, (dr) => dr.transaction)
    @AutoMap(() => [TransactionFieldEntity])
    transactionFields?: TransactionFieldEntity[];

    @OneToMany(() => TransactionFieldStyleEntity, (dr) => dr.transaction)
    @AutoMap(() => [TransactionFieldEntity])
    transactionFieldStyles?: TransactionFieldStyleEntity[];
}
