import { AutoMap } from '@automapper/classes';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { FormVersionStatus } from '../../shared/enums/form-version-status.enum';
import { FormCollectionTenancyEntity } from './form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from './form-field.tenancy.entity';
import { FormLayoutTenancyEntity } from './form-layout.tenancy.entity';
import { FormManualEventTenancyEntity } from './form-manual-event.tenancy.entity';
import { FormVersionCommentTenancyEntity } from './form-version-comment.tenancy.entity';
import { FormViewTenancyEntity } from './form-view.tenancy.entity';
import { FormTenancyEntity } from './form.tenancy.entity';
import { StageAccessControlTenancyEntity } from './stage-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from './stage-role.tenancy.entity';
import { StageTransitionTenancyEntity } from './stage-transition.tenancy.entity';
import { StageTenancyEntity } from './stage.tenancy.entity';
import { TransactionEntity } from './transaction.tenancy.entity';
import { UserFormVersionTenancyEntity } from './user-form-version.tenancy.entity';

@Entity({ name: 'form_versions', synchronize: false })
export class FormVersionTenancyEntity extends AbstractEntity {
    @Column({
        name: 'form_id',
        type: 'uuid',
    })
    @AutoMap()
    formId: string;

    @Column({
        name: 'version',
        type: 'int',
    })
    @AutoMap()
    version: number;

    @AutoMap()
    @Column({ default: null, nullable: true })
    publishedBy?: string | null;

    @Column({ name: 'published_by_user', default: null, nullable: true })
    @AutoMap()
    publishedByUser?: string | null;

    @AutoMap()
    @Column({ default: null, nullable: true })
    publishedAt?: Date;

    @Column({ type: 'varchar', length: 20, default: FormVersionStatus.Draft })
    @AutoMap()
    status: FormVersionStatus;

    @Column({
        name: 'layout',
        type: 'jsonb',
        nullable: true,
    })
    layout?: Record<string, any>;

    @Column({ name: 'test_transaction_id', type: 'uuid', nullable: true })
    @AutoMap()
    testTransactionId?: string;

    //#region Relations
    @ManyToOne(() => FormTenancyEntity, (d) => d.formVersions)
    @JoinColumn({ name: 'form_id' })
    form?: FormTenancyEntity;

    @OneToMany(() => FormFieldTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [FormFieldTenancyEntity])
    fields?: FormFieldTenancyEntity[];

    @OneToMany(() => StageTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [StageTenancyEntity])
    stages?: StageTenancyEntity[];

    @OneToMany(() => StageTransitionTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [StageTransitionTenancyEntity])
    stageTransitions?: StageTransitionTenancyEntity[];

    @OneToMany(() => FormVersionCommentTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [FormVersionCommentTenancyEntity])
    comments?: FormVersionCommentTenancyEntity[];

    @OneToMany(() => FormCollectionTenancyEntity, (fv) => fv.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [FormCollectionTenancyEntity])
    formCollections?: FormCollectionTenancyEntity[];

    // Access controls
    @OneToMany(() => StageAccessControlTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [StageAccessControlTenancyEntity])
    stageAccessControls?: StageAccessControlTenancyEntity[];

    // Layouts
    @OneToMany(() => FormLayoutTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [FormLayoutTenancyEntity])
    formLayouts?: FormLayoutTenancyEntity[];

    @OneToMany(() => StageRoleTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [StageRoleTenancyEntity])
    stageRoles?: StageRoleTenancyEntity[];

    @OneToMany(() => TransactionEntity, (dr) => dr.formVersion)
    transactions?: TransactionEntity[];

    // Layouts
    @OneToMany(() => FormViewTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [FormViewTenancyEntity])
    views?: FormViewTenancyEntity[];

    @OneToMany(() => UserFormVersionTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [UserFormVersionTenancyEntity])
    userFormVersions?: UserFormVersionTenancyEntity[];

    // Manual Events
    @OneToMany(() => FormManualEventTenancyEntity, (d) => d.formVersion, { cascade: ['soft-remove'] })
    @AutoMap(() => [FormManualEventTenancyEntity])
    manualEvents?: FormManualEventTenancyEntity[];

    //#endregion Relations
}
