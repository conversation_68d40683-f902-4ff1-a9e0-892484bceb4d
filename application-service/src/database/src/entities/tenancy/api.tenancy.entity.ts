import { AutoMap } from '@automapper/classes';
import { Column, Entity, OneToMany } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { ApiContextType, ApiStatus } from '../../shared/enums/automation.enum';
import { ApiVersionTenancyEntity } from './api-version.tenancy.entity';

@Entity({
    name: 'api',
    synchronize: false,
})
export class ApiTenancyEntity extends AbstractEntity {
    @Column({
        name: 'name',
        type: 'varchar',
        length: 300,
    })
    @AutoMap()
    name: string;

    @Column({ type: 'varchar', length: 100, default: ApiStatus.Draft })
    @AutoMap()
    status: ApiStatus;

    @Column({ type: 'varchar', length: 100, name: 'context_type' })
    @AutoMap()
    contextType: ApiContextType;

    @Column({
        name: 'context_id',
        type: 'uuid',
    })
    @AutoMap()
    contextId: string;

    @Column({
        name: 'active_version',
        type: 'int',
        nullable: true,
    })
    @AutoMap()
    activeVersion: number;

    @Column({ name: 'latest_version', type: 'int', nullable: true })
    @AutoMap()
    latestVersion: number;

    @Column({ name: 'active_version_id', type: 'uuid', nullable: true })
    @AutoMap()
    activeVersionId?: string;

    @Column({ name: 'latest_version_id', type: 'uuid', nullable: true })
    @AutoMap()
    latestVersionId?: string;

    @Column({ type: 'varchar', length: 1000, nullable: true })
    @AutoMap()
    description?: string;

    @Column({
        name: 'subscription_id',
        type: 'uuid',
        nullable: true,
    })
    @AutoMap()
    subscriptionId?: string;

    @AutoMap()
    @Column({ default: null, nullable: true })
    publishedBy?: string | null;

    @Column({ name: 'published_by_user', default: null, nullable: true })
    @AutoMap()
    publishedByUser?: string | null;

    @AutoMap()
    @Column({ default: null, nullable: true })
    publishedAt?: Date;

    //#region Relations

    @OneToMany(() => ApiVersionTenancyEntity, (d) => d.api, { cascade: ['soft-remove', 'insert', 'update'] })
    @AutoMap(() => [ApiVersionTenancyEntity])
    versions?: ApiVersionTenancyEntity[];

    //#endregion Relations
}
