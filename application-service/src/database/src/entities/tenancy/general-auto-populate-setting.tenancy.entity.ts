import { AutoMap } from '@automapper/classes';
import { Column, Entity, OneToMany } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { AutoPopulateBuilderTypeEnum } from '../../shared/enums/ap-builder-type.enum';
import { AutoPopulateDataSourceTypeEnum } from '../../shared/enums/ap-data-source-type.enum';
import { AutoPopulateExtraConfigTypeEnum } from '../../shared/enums/ap-extra-config-type.enum';
import { FormFieldTypeEnum } from '../../shared/enums/form-field-type.enum';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from './general-auto-populate-extra-config.tenancy.entity';

@Entity({ name: 'general_auto_populate_settings', synchronize: false })
export class GeneralAutoPopulateSettingTenancyEntity extends AbstractEntity {
    @AutoMap(() => String)
    @Column({ type: 'varchar', length: 30, nullable: true })
    type: AutoPopulateExtraConfigTypeEnum;

    @AutoMap()
    @Column({ type: 'varchar', nullable: true, length: 50 })
    dataSourceType: AutoPopulateDataSourceTypeEnum;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    dataSourceId: string;

    @AutoMap()
    @Column({ type: 'varchar', nullable: true, length: 100 })
    dataSourceCode: string;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    targetFieldId: string;

    @Column({ type: 'varchar', length: 30, nullable: true })
    @AutoMap(() => String)
    targetFieldType: FormFieldTypeEnum;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    fieldId: string;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    itemIdentityId: string;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    parentItemIdentityId: string;

    @AutoMap()
    @Column({ type: 'int', nullable: true })
    priority: number;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    builderId: string;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    builderVersionId: string;

    @AutoMap(() => String)
    @Column({ type: 'varchar', length: 30, nullable: true })
    builderType: AutoPopulateBuilderTypeEnum;

    @AutoMap()
    @Column({ type: 'boolean', nullable: true })
    isArray?: boolean;

    @AutoMap()
    @Column({ type: 'boolean', nullable: true })
    includeValidationValue?: boolean;

    @AutoMap()
    @Column({ type: 'uuid', nullable: true })
    targetComparedFieldId?: string;

    @Column({ type: 'varchar', length: 30, nullable: true })
    @AutoMap(() => String)
    targetComparedFieldType?: FormFieldTypeEnum;

    @OneToMany(() => GeneralAutoPopulateExtraConfigTenancyEntity, (e) => e.autoPopulateSetting)
    extraConfigurations: GeneralAutoPopulateExtraConfigTenancyEntity[];
}
