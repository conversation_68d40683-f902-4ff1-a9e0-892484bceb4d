import { AutoMap } from '@automapper/classes';
import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '../../shared/abstract.entity';
import { ApiVersionTenancyEntity } from './api-version.tenancy.entity';

@Entity({
    name: 'api_version_comments',
    synchronize: false,
})
export class ApiVersionCommentTenancyEntity extends AbstractEntity {
    @Column({
        name: 'api_version_id',
        type: 'uuid',
    })
    @AutoMap()
    apiVersionId: string;

    @Column({
        name: 'content',
        type: 'text',
    })
    @AutoMap()
    content: string;

    @Column({
        name: 'created_by',
        type: 'varchar',
        length: 255,
    })
    @AutoMap()
    createdBy: string;

    @Column({
        name: 'created_by_user',
        type: 'varchar',
        length: 255,
    })
    @AutoMap()
    createdByUser: string;

    @ManyToOne(() => ApiVersionTenancyEntity, (version) => version.comments)
    @JoinColumn({ name: 'api_version_id' })
    apiVersion?: ApiVersionTenancyEntity;
}
