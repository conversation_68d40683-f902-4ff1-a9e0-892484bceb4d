import { ApiContextType } from '../shared/enums/automation.enum';

export enum API_ENDPOINT_METHODS {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DELETE',
}

export enum API_ENDPOINTS {
    TRANSACTION_LIST = '/api/v1/third-party/transaction/list/:formId',
    TRANSACTION_DETAIL = '/api/v1/third-party/transaction/detail/:formId/:id',
    TRANSACTION_CREATE = '/api/v1/third-party/transaction/create/:formId',
    TRANSACTION_UPDATE = '/api/v1/third-party/transaction/update/:formId/:id',
    TRANSACTION_DELETE = '/api/v1/third-party/transaction/delete/:formId/:id',
}

export const API_ENDPOINT_MATCHERS = {
    [API_ENDPOINTS.TRANSACTION_LIST]:
        /\/api\/v1\/third-party\/transaction\/list\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    [API_ENDPOINTS.TRANSACTION_DETAIL]:
        /\/api\/v1\/third-party\/transaction\/detail\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    [API_ENDPOINTS.TRANSACTION_CREATE]:
        /\/api\/v1\/third-party\/transaction\/create\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    [API_ENDPOINTS.TRANSACTION_UPDATE]:
        /\/api\/v1\/third-party\/transaction\/update\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
    [API_ENDPOINTS.TRANSACTION_DELETE]:
        /\/api\/v1\/third-party\/transaction\/delete\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
};

export const API_ENDPOINT_CONTEXT_TYPE = {
    [ApiContextType.FormTransaction]: [
        API_ENDPOINTS.TRANSACTION_LIST,
        API_ENDPOINTS.TRANSACTION_DETAIL,
        API_ENDPOINTS.TRANSACTION_CREATE,
        API_ENDPOINTS.TRANSACTION_UPDATE,
        API_ENDPOINTS.TRANSACTION_DELETE,
    ],
    [ApiContextType.DataRegister]: [],
};

export const API_ENDPOINT_CONFIGURATION = {
    TRANSACTION_LIST: {
        method: API_ENDPOINT_METHODS.GET,
        endpoint: API_ENDPOINTS.TRANSACTION_LIST,
    },
    TRANSACTION_DETAIL: {
        method: API_ENDPOINT_METHODS.GET,
        endpoint: API_ENDPOINTS.TRANSACTION_DETAIL,
    },
    TRANSACTION_CREATE: {
        method: API_ENDPOINT_METHODS.POST,
        endpoint: API_ENDPOINTS.TRANSACTION_CREATE,
    },
    TRANSACTION_UPDATE: {
        method: API_ENDPOINT_METHODS.PUT,
        endpoint: API_ENDPOINTS.TRANSACTION_UPDATE,
    },
    TRANSACTION_DELETE: {
        method: API_ENDPOINT_METHODS.DELETE,
        endpoint: API_ENDPOINTS.TRANSACTION_DELETE,
    },
};
