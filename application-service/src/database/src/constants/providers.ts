export const PROVIDER_KEYS = {
    DATA_SOURCE: 'DATA_SOURCE',
    TENANT_CONNECTION: 'TENANT_CONNECTION',

    // User & Auth Repositories
    ACCOUNT_REPOSITORY: 'ACCOUNT_REPOSITORY',
    ACCOUNT_USER_REPOSITORY: 'ACCOUNT_USER_REPOSITORY',
    ACCOUNT_TO_FORM: 'ACCOUNT_TO_FORM',

    USER_REPOSITORY: 'USER_REPOSITORY',
    ROLE_REPOSITORY: 'ROLE_REPOSITORY',
    ROLE_TENANCY_REPOSITORY: 'ROLE_TENANCY_REPOSITORY',
    USER_TENANCY_REPOSITORY: 'USER_TENANCY_REPOSITORY',
    USER_ROLE_TENANCY_REPOSITORY: 'USER_ROLE_TENANCY_REPOSITORY',
    R<PERSON><PERSON>_SUBSCRIPTION_TENANCY_REPOSITORY: 'ROLE_SUBSCRIPTION_TENANCY_REPOSITORY',
    ACCOUNT_SUBSCRIPTION_REPOSITORY: 'ACCOUNT_SUBSCRIPTION_REPOSITORY',
    FAVORITE_MENU_REPOSITORY: 'FAVORITE_MENU_REPOSITORY',
    SUBSCRIPTION_REPOSITORY: 'SUBSCRIPTION_REPOSITORY',
    ACCOUNT_SUBSCRIPTION: 'ACCOUNT_SUBSCRIPTION',

    // Data Register Repository
    DATA_REGISTER_REPOSITORY: 'DATA_REGISTER_REPOSITORY',
    DATA_REGISTER_TENANCY_REPOSITORY: 'DATA_REGISTER_TENANCY_REPOSITORY',
    DATA_REGISTER_FIELD_REPOSITORY: 'DATA_REGISTER_FIELD_REPOSITORY',
    DATA_REGISTER_FIELD_TENANCY_REPOSITORY: 'DATA_REGISTER_FIELD_TENANCY_REPOSITORY',
    ACCOUNT_DATA_REGISTER: 'ACCOUNT_DATA_REGISTER',
    DATA_REGISTER_TRANSACTION: 'DATA_REGISTER_TRANSACTION',
    DATA_REGISTER_TRANSACTION_FIELD: 'DATA_REGISTER_TRANSACTION_FIELD',
    FAVORITE_MENU: 'FAVORITE_MENU',
    DATA_REGISTER_TRANSACTION_TENANCY: 'DATA_REGISTER_TRANSACTION_TENANCY',
    DATA_REGISTER_TRANSACTION_FIELD_TENANCY: 'DATA_REGISTER_TRANSACTION_FIELD_TENANCY',
    DATA_REGISTER_VERSION_REPOSITORY: 'DATA_REGISTER_VERSION_REPOSITORY',
    DATA_REGISTER_VERSION_TENANCY_REPOSITORY: 'DATA_REGISTER_VERSION_TENANCY_REPOSITORY',
    DATA_PROVIDER_AUTO_POPULATE_SETTING_REPOSITORY: 'DATA_PROVIDER_AUTO_POPULATE_SETTING_REPOSITORY',
    DATA_PROVIDER_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY: 'DATA_PROVIDER_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY',
    DATA_REGISTER_TRANSACTION_FIELD_OVERRIDE_REPOSITORY: 'DATA_REGISTER_TRANSACTION_FIELD_OVERRIDE_REPOSITORY',
    DATA_REGISTER_TRANSACTION_FIELD_OVERRIDE_TENANCY_REPOSITORY: 'DATA_REGISTER_TRANSACTION_FIELD_OVERRIDE_TENANCY_REPOSITORY',
    DATA_REGISTER_TRANSACTION_CHANGE_LOG_REPOSITORY: 'DATA_REGISTER_TRANSACTION_CHANGE_LOG_REPOSITORY',
    DATA_REGISTER_TRANSACTION_CHANGE_LOG_TENANCY_REPOSITORY: 'DATA_REGISTER_TRANSACTION_CHANGE_LOG_TENANCY_REPOSITORY',
    DATA_REGISTER_ACCESS_CONTROL_REPOSITORY: 'DATA_REGISTER_ACCESS_CONTROL_REPOSITORY',
    DATA_REGISTER_ACCESS_CONTROL_TENANCY_REPOSITORY: 'DATA_REGISTER_ACCESS_CONTROL_TENANCY_REPOSITORY',

    DATA_REGISTER_SCORE_MODEL_REPOSITORY: 'DATA_REGISTER_SCORE_MODEL_REPOSITORY',
    DATA_REGISTER_SCORE_MODEL_TENANCY_REPOSITORY: 'DATA_REGISTER_SCORE_MODEL_TENANCY_REPOSITORY',
    DATA_REGISTER_SCORE_MODEL_ITEM_REPOSITORY: 'DATA_REGISTER_SCORE_MODEL_ITEM_REPOSITORY',
    DATA_REGISTER_SCORE_MODEL_ITEM_TENANCY_REPOSITORY: 'DATA_REGISTER_SCORE_MODEL_ITEM_TENANCY_REPOSITORY',
    SCORE_MODEL_TEMPLATE_REPOSITORY: 'SCORE_MODEL_TEMPLATE_REPOSITORY',
    SCORE_MODEL_TEMPLATE_TENANCY_REPOSITORY: 'SCORE_MODEL_TEMPLATE_TENANCY_REPOSITORY',

    // Document Repository
    DOCUMENT_REPOSITORY: 'DOCUMENT_REPOSITORY',
    DOCUMENT_TENANCY_REPOSITORY: 'DOCUMENT_TENANCY_REPOSITORY',
    DOCUMENT_FIELD_REPOSITORY: 'DOCUMENT_FIELD_REPOSITORY',
    DOCUMENT_FIELD_TENANCY_REPOSITORY: 'DOCUMENT_FIELD_TENANCY_REPOSITORY',
    DOCUMENT_VERSION_REPOSITORY: 'DOCUMENT_VERSION_REPOSITORY',
    DOCUMENT_VERSION_TENANCY_REPOSITORY: 'DOCUMENT_VERSION_TENANCY_REPOSITORY',
    DOCUMENT_CHANGE_LOG_REPOSITORY: 'DOCUMENT_CHANGE_LOG_REPOSITORY',
    DOCUMENT_CHANGE_LOG_TENANCY_REPOSITORY: 'DOCUMENT_CHANGE_LOG_TENANCY_REPOSITORY',
    DOCUMENT_LAYOUT_ZONE_REPOSITORY: 'DOCUMENT_LAYOUT_ZONE_REPOSITORY',
    DOCUMENT_LAYOUT_ZONE_TENANCY_REPOSITORY: 'DOCUMENT_LAYOUT_ZONE_TENANCY_REPOSITORY',
    ACCOUNT_DOCUMENT: 'ACCOUNT_DOCUMENT',

    // Form Repository
    FORM_PACKAGE_REPOSITORY: 'FORM_PACKAGE_REPOSITORY',
    FORM_REPOSITORY: 'FORM_REPOSITORY',
    FORM_VERSION_REPOSITORY: 'FORM_VERSION_REPOSITORY',
    FORM_FIELD_REPOSITORY: 'FORM_FIELD_REPOSITORY',
    FORM_VERSION_COMMENT_REPOSITORY: 'FORM_VERSION_COMMENT_REPOSITORY',
    STAGE_REPOSITORY: 'STAGE_REPOSITORY',
    STAGE_TRANSITION_REPOSITORY: 'STAGE_TRANSITION_REPOSITORY',
    STAGE_DECISION_REPOSITORY: 'STAGE_DECISION_REPOSITORY',
    STAGE_ACCESS_CONTROL_REPOSITORY: 'STAGE_ACCESS_CONTROL_REPOSITORY',
    STAGE_ROLE_REPOSITORY: 'STAGE_ROLE_REPOSITORY',
    STAGE_ROLE_ACCESS_CONTROL_REPOSITORY: 'STAGE_ROLE_ACCESS_CONTROL_REPOSITORY',
    FORM_LAYOUT_REPOSITORY: 'FORM_LAYOUT_REPOSITORY',
    FORM_LAYOUT_ZONE_REPOSITORY: 'FORM_LAYOUT_ZONE_REPOSITORY',
    FORM_LAYOUT_ZONE_FIELD_REPOSITORY: 'FORM_LAYOUT_ZONE_FIELD_REPOSITORY',
    FORM_RELATED_REPOSITORY: 'FORM_RELATED_REPOSITORY',
    FORM_VIEW_REPOSITORY: 'FORM_VIEW_REPOSITORY',

    // Form Tenancy Repositories
    FORM_TENANCY_REPOSITORY: 'FORM_TENANCY_REPOSITORY',
    FORM_VERSION_TENANCY_REPOSITORY: 'FORM_VERSION_TENANCY_REPOSITORY',
    FORM_FIELD_TENANCY_REPOSITORY: 'FORM_FIELD_TENANCY_REPOSITORY',
    FORM_VERSION_COMMENT_TENANCY_REPOSITORY: 'FORM_VERSION_COMMENT_TENANCY_REPOSITORY',
    STAGE_TENANCY_REPOSITORY: 'STAGE_TENANCY_REPOSITORY',
    STAGE_TRANSITION_TENANCY_REPOSITORY: 'STAGE_TRANSITION_TENANCY_REPOSITORY',
    STAGE_DECISION_TENANCY_REPOSITORY: 'STAGE_DECISION_TENANCY_REPOSITORY',
    STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY: 'STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY',
    STAGE_ROLE_TENANCY_REPOSITORY: 'STAGE_ROLE_TENANCY_REPOSITORY',
    STAGE_ROLE_ACCESS_CONTROL_TENANCY_REPOSITORY: 'STAGE_ROLE_ACCESS_CONTROL_TENANCY_REPOSITORY',
    FORM_LAYOUT_TENANCY_REPOSITORY: 'FORM_LAYOUT_TENANCY_REPOSITORY',
    FORM_LAYOUT_ZONE_TENANCY_REPOSITORY: 'FORM_LAYOUT_ZONE_TENANCY_REPOSITORY',
    FORM_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY: 'FORM_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY',
    FORM_RELATED_TENANCY_REPOSITORY: 'FORM_RELATED_TENANCY_REPOSITORY',
    FORM_VIEW_TENANCY_REPOSITORY: 'FORM_VIEW_TENANCY_REPOSITORY',
    USER_FORM_VERSION_TENANCY_REPOSITORY: 'USER_FORM_VERSION_REPOSITORY',

    // Roles to form views tenancy repository
    ROLE_TO_FORM_VIEW_TENANCY_REPOSITORY: 'ROLE_TO_FORM_VIEW_TENANCY_REPOSITORY',

    //Form collection repository
    FORM_COLLECTION_REPOSITORY: 'FORM_COLLECTION_REPOSITORY',
    FORM_COLLECTION_ITEM_REPOSITORY: 'FORM_COLLECTION_ITEM_REPOSITORY',

    //Form collection tenancy repository
    FORM_COLLECTION_TENANCY_REPOSITORY: 'FORM_COLLECTION_TENANCY_REPOSITORY',
    FORM_COLLECTION_ITEM_TENANCY_REPOSITORY: 'FORM_COLLECTION_ITEM_TENANCY_REPOSITORY',

    // Block
    BLOCK_REPOSITORY: 'BLOCK_REPOSITORY',

    // Widget
    WIDGET_REPOSITORY: 'WIDGET_REPOSITORY',
    WIDGET_VERSION_REPOSITORY: 'WIDGET_VERSION_REPOSITORY',
    WIDGET_TENANCY_REPOSITORY: 'WIDGET_TENANCY_REPOSITORY',
    WIDGET_VERSION_TENANCY_REPOSITORY: 'WIDGET_VERSION_TENANCY_REPOSITORY',
    ACCOUNT_TO_WIDGET_REPOSITORY: 'ACCOUNT_TO_WIDGET_REPOSITORY',

    // From transaction
    FORM_TRANSACTION_REPOSITORY: 'FORM_TRANSACTION_REPOSITORY',
    FORM_RELATION_TRANSACTION_REPOSITORY: 'FORM_RELATION_TRANSACTION_REPOSITORY',
    FORM_TRANSACTION_FIELD_REPOSITORY: 'FORM_TRANSACTION_FIELD_REPOSITORY',
    FORM_TRANSACTION_FIELD_STYLE_REPOSITORY: 'FORM_TRANSACTION_FIELD_STYLE_REPOSITORY',
    FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY: 'FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY',

    // Rollup field
    ROLL_UP_DEPENDENCY_REPOSITORY: 'ROLL_UP_DEPENDENCY_REPOSITORY',
    ROLL_UP_DEPENDENCY_TENANCY_REPOSITORY: 'ROLL_UP_DEPENDENCY_TENANCY_REPOSITORY',

    AUTO_POPULATE_SETTING_REPOSITORY: 'AUTO_POPULATE_SETTING_REPOSITORY',
    AUTO_POPULATE_SETTING_TENANCY_REPOSITORY: 'AUTO_POPULATE_SETTING_TENANCY_REPOSITORY',

    //Collection transaction
    COLLECTION_TRANSACTION_REPOSITORY: 'COLLECTION_TRANSACTION_REPOSITORY',
    COLLECTION_TRANSACTION_TENANCY_REPOSITORY: 'COLLECTION_TRANSACTION_TENANCY_REPOSITORY',

    //#region Data Register Additional Fields
    DATA_REGISTER_ADDITIONAL_FIELD_REPOSITORY: 'DATA_REGISTER_ADDITIONAL_FIELD_REPOSITORY',
    DATA_REGISTER_AUTO_POPULATE_CONTEXT_REPOSITORY: 'DATA_REGISTER_AUTO_POPULATE_CONTEXT_REPOSITORY',
    FORM_COLLECTION_ADDITIONAL_FIELD_REPOSITORY: 'FORM_COLLECTION_ADDITIONAL_FIELD_REPOSITORY',
    FORM_COLLECTION_AUTO_POPULATE_CONTEXT_REPOSITORY: 'FORM_COLLECTION_AUTO_POPULATE_CONTEXT_REPOSITORY',
    FORM_COLLECTION_CONTEXT_MAPPING_REPOSITORY: 'FORM_COLLECTION_CONTEXT_MAPPING_REPOSITORY',

    DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY: 'DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY',
    FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY: 'FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY',
    FORM_COLLECTION_AUTO_POPULATE_CONTEXT_TENANCY_REPOSITORY: 'FORM_COLLECTION_AUTO_POPULATE_CONTEXT_TENANCY_REPOSITORY',
    DATA_REGISTER_AUTO_POPULATE_CONTEXT_TENANCY_REPOSITORY: 'DATA_REGISTER_AUTO_POPULATE_CONTEXT_TENANCY_REPOSITORY',
    FORM_COLLECTION_CONTEXT_MAPPING_TENANCY_REPOSITORY: 'FORM_COLLECTION_CONTEXT_MAPPING_TENANCY_REPOSITORY',
    DATA_REGISTER_TRANSACTION_FIELD_STYLE_REPOSITORY: 'DATA_REGISTER_TRANSACTION_FIELD_STYLE_REPOSITORY',
    DATA_REGISTER_TRANSACTION_FIELD_STYLE_TENANCY_REPOSITORY: 'DATA_REGISTER_TRANSACTION_FIELD_STYLE_TENANCY_REPOSITORY',

    //#endregion Data Register Additional Fields

    //Workspace
    ACCOUNT_TO_WORKSPACE_REPOSITORY: 'ACCOUNT_TO_WORKSPACE_REPOSITORY',
    USER_WORKSPACE_REPOSITORY: 'USER_WORKSPACE_REPOSITORY',
    IMPERSONATING_SESSION_REPOSITORY: 'IMPERSONATING_SESSION_REPOSITORY',
    WORKSPACE_REPOSITORY: 'WORKSPACE_REPOSITORY',
    WORKSPACE_VERSION_REPOSITORY: 'WORKSPACE_VERSION_REPOSITORY',
    WORKSPACE_ACCESS_CONTROL_REPOSITORY: 'WORKSPACE_ACCESS_CONTROL_REPOSITORY',
    WORKSPACE_HEADER_ITEM_REPOSITORY: 'WORKSPACE_HEADER_ITEM_REPOSITORY',
    WORKSPACE_LAYOUT_REPOSITORY: 'WORKSPACE_LAYOUT_REPOSITORY',
    WORKSPACE_LAYOUT_ZONE_REPOSITORY: 'WORKSPACE_LAYOUT_ZONE_REPOSITORY',
    WORKSPACE_LAYOUT_ZONE_FIELD_REPOSITORY: 'WORKSPACE_LAYOUT_ZONE_FIELD_REPOSITORY',
    WORKSPACE_WIDGET_REPOSITORY: 'WORKSPACE_WIDGET_REPOSITORY',
    WORKSPACE_CHANGE_LOG_REPOSITORY: 'WORKSPACE_CHANGE_LOG_REPOSITORY',
    WORKSPACE_COMMENT_REPOSITORY: 'WORKSPACE_COMMENT_REPOSITORY',

    USER_WORKSPACE_TENANCY_REPOSITORY: 'USER_WORKSPACE_TENANCY_REPOSITORY',
    WORKSPACE_TENANCY_REPOSITORY: 'WORKSPACE_TENANCY_REPOSITORY',
    WORKSPACE_VERSION_TENANCY_REPOSITORY: 'WORKSPACE_VERSION_TENANCY_REPOSITORY',
    WORKSPACE_ACCESS_CONTROL_TENANCY_REPOSITORY: 'WORKSPACE_ACCESS_CONTROL_TENANCY_REPOSITORY',
    WORKSPACE_HEADER_ITEM_TENANCY_REPOSITORY: 'WORKSPACE_HEADER_ITEM_TENANCY_REPOSITORY',
    WORKSPACE_LAYOUT_TENANCY_REPOSITORY: 'WORKSPACE_LAYOUT_TENANCY_REPOSITORY',
    WORKSPACE_LAYOUT_ZONE_TENANCY_REPOSITORY: 'WORKSPACE_LAYOUT_ZONE_TENANCY_REPOSITORY',
    WORKSPACE_WIDGET_TENANCY_REPOSITORY: 'WORKSPACE_WIDGET_TENANCY_REPOSITORY',
    WORKSPACE_CHANGE_LOG_TENANCY_REPOSITORY: 'WORKSPACE_CHANGE_LOG_TENANCY_REPOSITORY',
    WORKSPACE_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY: 'WORKSPACE_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY',
    WORKSPACE_COMMENT_TENANCY_REPOSITORY: 'WORKSPACE_COMMENT_TENANCY_REPOSITORY',
    USER_WORKSPACE_VERSION_TENANCY_REPOSITORY: 'USER_WORKSPACE_VERSION_TENANCY_REPOSITORY',

    // USER SETTINGS
    USER_SETTINGS_REPOSITORY: 'USER_SETTINGS_REPOSITORY',

    // GENERAL AUTO POPULATE SETTINGS
    GENERAL_AUTO_POPULATE_SETTINGS_REPOSITORY: 'GENERAL_AUTO_POPULATE_SETTINGS_REPOSITORY',
    GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY: 'GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY',
    GENERAL_AUTO_POPULATE_EXTRA_CONFIG_REPOSITORY: 'GENERAL_AUTO_POPULATE_EXTRA_CONFIG_REPOSITORY',
    GENERAL_AUTO_POPULATE_EXTRA_CONFIG_TENANCY_REPOSITORY: 'GENERAL_AUTO_POPULATE_EXTRA_CONFIG_TENANCY_REPOSITORY',

    // EVENT
    EVENT_LOG_REPOSITORY: 'EVENT_LOG_REPOSITORY',
    EVENT_LOG_TENANCY_REPOSITORY: 'EVENT_LOG_TENANCY_REPOSITORY',

    // AGGREGATION
    AGGREGATION_REPOSITORY: 'AGGREGATION_REPOSITORY',
    AGGREGATION_VERSION_REPOSITORY: 'AGGREGATION_VERSION_REPOSITORY',
    ACCOUNT_TO_AGGREGATION_REPOSITORY: 'ACCOUNT_TO_AGGREGATION_REPOSITORY',
    AGGREGATION_TENANCY_REPOSITORY: 'AGGREGATION_TENANCY_REPOSITORY',
    AGGREGATION_VERSION_TENANCY_REPOSITORY: 'AGGREGATION_VERSION_TENANCY_REPOSITORY',

    //#region AUTOMATION
    AUTOMATION_REPOSITORY: 'AUTOMATION_REPOSITORY',
    AUTOMATION_TENANCY_REPOSITORY: 'AUTOMATION_TENANCY_REPOSITORY',
    AUTOMATION_VERSION_REPOSITORY: 'AUTOMATION_VERSION_REPOSITORY',
    AUTOMATION_VERSION_TENANCY_REPOSITORY: 'AUTOMATION_VERSION_TENANCY_REPOSITORY',
    AUTOMATION_ACTION_REPOSITORY: 'AUTOMATION_ACTION_REPOSITORY',
    AUTOMATION_ACTION_TENANCY_REPOSITORY: 'AUTOMATION_ACTION_TENANCY_REPOSITORY',
    AUTOMATION_RULE_REPOSITORY: 'AUTOMATION_RULE_REPOSITORY',
    AUTOMATION_RULE_TENANCY_REPOSITORY: 'AUTOMATION_RULE_TENANCY_REPOSITORY',
    ACCOUNT_TO_AUTOMATION_REPOSITORY: 'ACCOUNT_TO_AUTOMATION_REPOSITORY',
    ACCOUNT_TO_AUTOMATION_COMMENT_REPOSITORY: 'ACCOUNT_TO_AUTOMATION_COMMENT_REPOSITORY',
    ACCOUNT_TO_AUTOMATION_COMMENT_TENANCY_REPOSITORY: 'ACCOUNT_TO_AUTOMATION_COMMENT_TENANCY_REPOSITORY',
    ACCOUNT_TO_AUTOMATION_CHANGE_LOG_REPOSITORY: 'ACCOUNT_TO_AUTOMATION_CHANGE_LOG_REPOSITORY',
    ACCOUNT_TO_AUTOMATION_CHANGE_LOG_TENANCY_REPOSITORY: 'ACCOUNT_TO_AUTOMATION_CHANGE_LOG_TENANCY_REPOSITORY',
    //#endregion AUTOMATION

    FORM_CONTEXT_MAPPING_REPOSITORY: 'FORM_CONTEXT_MAPPING_REPOSITORY',
    FORM_CONTEXT_MAPPING_TENANCY_REPOSITORY: 'FORM_CONTEXT_MAPPING_TENANCY_REPOSITORY',

    FORM_VIEW_ITEM_REPOSITORY: 'FORM_VIEW_ITEM_REPOSITORY',
    FORM_VIEW_ITEM_TENANCY_REPOSITORY: 'FORM_VIEW_ITEM_TENANCY_REPOSITORY',

    FORM_MANUAL_EVENT_REPOSITORY: 'FORM_MANUAL_EVENT_REPOSITORY',
    FORM_MANUAL_EVENT_TENANCY_REPOSITORY: 'FORM_MANUAL_EVENT_TENANCY_REPOSITORY',

    //#region ACCOUNT STORAGE
    ACCOUNT_STORAGE: 'ACCOUNT_STORAGE',
    //#endregion ACCOUNT STORAGE

    //#region COLLECTION LAYOUT
    COLLECTION_LAYOUT_REPOSITORY: 'COLLECTION_LAYOUT_REPOSITORY',
    COLLECTION_LAYOUT_TENANCY_REPOSITORY: 'COLLECTION_LAYOUT_TENANCY_REPOSITORY',

    COLLECTION_LAYOUT_ZONE_REPOSITORY: 'COLLECTION_LAYOUT_ZONE_REPOSITORY',
    COLLECTION_LAYOUT_ZONE_TENANCY_REPOSITORY: 'COLLECTION_LAYOUT_ZONE_TENANCY_REPOSITORY',

    COLLECTION_LAYOUT_ZONE_FIELD_REPOSITORY: 'COLLECTION_LAYOUT_ZONE_FIELD_REPOSITORY',
    COLLECTION_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY: 'COLLECTION_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY',
    //#endregion COLLECTION LAYOUT

    TRANSACTION_ACTION_LOG_REPOSITORY: 'TRANSACTION_ACTION_LOG_REPOSITORY',
    TRANSACTION_ACTION_LOG_TENANCY_REPOSITORY: 'TRANSACTION_ACTION_LOG_TENANCY_REPOSITORY',
    EMAIL_LOG_REPOSITORY: 'EMAIL_LOG',

    //#region DOC
    DOC_CREW_TENANCY_REPOSITORY: 'DOC_CREW_TENANCY_REPOSITORY',
    DOC_CREW_DETAIL_TENANCY_REPOSITORY: 'DOC_CREW_DETAIL_TENANCY_REPOSITORY',
    DOC_PDF_SIRE_CREW_TENANCY_REPOSITORY: 'DOC_PDF_SIRE_CREW_TENANCY_REPOSITORY',
    //#endregion DOC

    // #region collection automation mapping
    COLLECTION_AUTOMATION_MAPPING_REPOSITORY: 'COLLECTION_AUTOMATION_MAPPING_REPOSITORY',
    COLLECTION_AUTOMATION_MAPPING_TENANCY_REPOSITORY: 'COLLECTION_AUTOMATION_MAPPING_TENANCY_REPOSITORY',
    // #endregion

    //
    CAPTURE_ACTIVE_FORM_VERSION_PROVIDER: 'CAPTURE_ACTIVE_FORM_VERSION_PROVIDER',

    API_TENANCY_REPOSITORY: 'API_TENANCY_REPOSITORY',
    API_VERSION_TENANCY_REPOSITORY: 'API_VERSION_TENANCY_REPOSITORY',
    API_VERSION_COMMENT_TENANCY_REPOSITORY: 'API_VERSION_COMMENT_TENANCY_REPOSITORY',
    API_VERSION_CHANGE_LOG_TENANCY_REPOSITORY: 'API_VERSION_CHANGE_LOG_TENANCY_REPOSITORY',
    API_VERSION_ENDPOINT_TENANCY_REPOSITORY: 'API_VERSION_ENDPOINT_TENANCY_REPOSITORY',
};
