import { DataSource } from 'typeorm';
import { PROVIDER_KEYS } from '../constants/providers';
import { ApiTenancyEntity } from '../entities/tenancy/api.tenancy.entity';
import { ApiVersionTenancyEntity } from '../entities/tenancy/api-version.tenancy.entity';
import { ApiVersionCommentTenancyEntity } from '../entities/tenancy/api-version-comment.tenancy.entity';
import { ApiVersionChangeLogTenancyEntity } from '../entities/tenancy/api-version-change-log.tenancy.entity';
import { ApiVersionEndpointTenancyEntity } from '../entities/tenancy/api-version-endpoint.tenancy.entity';

export const apiProviders = [
    //#region Tenancy
    {
        provide: PROVIDER_KEYS.API_TENANCY_REPOSITORY,
        useFactory: (dataSource: DataSource) => dataSource.getRepository(ApiTenancyEntity),
        inject: [PROVIDER_KEYS.TENANT_CONNECTION],
    },
    {
        provide: PROVIDER_KEYS.API_VERSION_TENANCY_REPOSITORY,
        useFactory: (dataSource: DataSource) => dataSource.getRepository(ApiVersionTenancyEntity),
        inject: [PROVIDER_KEYS.TENANT_CONNECTION],
    },
    {
        provide: PROVIDER_KEYS.API_VERSION_COMMENT_TENANCY_REPOSITORY,
        useFactory: (dataSource: DataSource) => dataSource.getRepository(ApiVersionCommentTenancyEntity),
        inject: [PROVIDER_KEYS.TENANT_CONNECTION],
    },
    {
        provide: PROVIDER_KEYS.API_VERSION_CHANGE_LOG_TENANCY_REPOSITORY,
        useFactory: (dataSource: DataSource) => dataSource.getRepository(ApiVersionChangeLogTenancyEntity),
        inject: [PROVIDER_KEYS.TENANT_CONNECTION],
    },
    {
        provide: PROVIDER_KEYS.API_VERSION_ENDPOINT_TENANCY_REPOSITORY,
        useFactory: (dataSource: DataSource) => dataSource.getRepository(ApiVersionEndpointTenancyEntity),
        inject: [PROVIDER_KEYS.TENANT_CONNECTION],
    },
    //#endregion Tenancy
];
