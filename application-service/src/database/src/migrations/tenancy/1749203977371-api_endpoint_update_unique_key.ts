import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class ApiEndpointUpdateUniqueKey1749203977371 implements MigrationInterface {
    name = 'ApiEndpointUpdateUniqueKey1749203977371';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" DROP CONSTRAINT "UQ_c87aede666740413aa676179580"`);
        await queryRunner.query(
            `ALTER TABLE "${schema}"."api_version_endpoints" ADD CONSTRAINT "UQ_5603362c62b7cf655ef513cd6d8" UNIQUE ("endpoint", "method", "api_version_id")`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" DROP CONSTRAINT "UQ_5603362c62b7cf655ef513cd6d8"`);
        await queryRunner.query(
            `ALTER TABLE "${schema}"."api_version_endpoints" ADD CONSTRAINT "UQ_c87aede666740413aa676179580" UNIQUE ("method", "endpoint")`,
        );
    }
}
