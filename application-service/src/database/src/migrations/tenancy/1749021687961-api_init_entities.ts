import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class ApiInitEntities1749021687961 implements MigrationInterface {
    name = 'ApiInitEntities1749021687961';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';

        await queryRunner.query(
            `CREATE TABLE "${schema}"."api" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_by" character varying, "created_by_user" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "updated_by_user" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_by" character varying, "deleted_by_user" character varying, "deleted_at" TIMESTAMP, "name" character varying(300) NOT NULL, "status" character varying(100) NOT NULL DEFAULT 'draft', "context_type" character varying(100) NOT NULL, "context_id" uuid NOT NULL, "active_version" integer, "latest_version" integer, "active_version_id" uuid, "latest_version_id" uuid, "description" character varying(1000), "subscription_id" uuid, "published_by" character varying, "published_by_user" character varying, "published_at" TIMESTAMP, CONSTRAINT "PK_12f6cbe9e79197c2bf4c79c009d" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "${schema}"."api_version_comments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_by" character varying, "created_by_user" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "updated_by_user" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_by" character varying, "deleted_by_user" character varying, "deleted_at" TIMESTAMP, "api_version_id" uuid NOT NULL, "content" text NOT NULL, CONSTRAINT "PK_7773b44e18ab7a57ec5fd627bee" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "${schema}"."api_version_change_logs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_by" character varying, "created_by_user" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "updated_by_user" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_by" character varying, "deleted_by_user" character varying, "deleted_at" TIMESTAMP, "changed_at" TIMESTAMP, "changed_by" character varying, "compared_id" uuid, "origin_id" uuid, "api_version_id" uuid NOT NULL, "content" jsonb, "type" character varying(50) NOT NULL, "action_type" character varying(20) NOT NULL, CONSTRAINT "PK_0a5b092304a14251cf1f2b7c7ce" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "${schema}"."api_versions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_by" character varying, "created_by_user" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "updated_by_user" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_by" character varying, "deleted_by_user" character varying, "deleted_at" TIMESTAMP, "api_id" uuid NOT NULL, "version" integer NOT NULL, "context_version_id" uuid NOT NULL, "status" character varying(100) NOT NULL DEFAULT 'DRAFT', "is_enable" boolean NOT NULL DEFAULT true, "configuration" jsonb, "published_by" character varying, "published_by_user" character varying, "published_at" TIMESTAMP, CONSTRAINT "PK_d84f6e984b5a582d4ef8f804a02" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "${schema}"."api_version_endpoints" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_by" character varying, "created_by_user" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_by" character varying, "updated_by_user" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_by" character varying, "deleted_by_user" character varying, "deleted_at" TIMESTAMP, "api_version_id" uuid NOT NULL, "method" character varying(100) NOT NULL, "endpoint" character varying(1000) NOT NULL, "configuration" jsonb DEFAULT '{}', CONSTRAINT "PK_8dfb7836fc12fe058c9e2b4f424" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "${schema}"."api_version_comments" ADD CONSTRAINT "FK_c7b24ab79f6e2ef5519d1b3bbf7" FOREIGN KEY ("api_version_id") REFERENCES "${schema}"."api_versions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "${schema}"."api_versions" ADD CONSTRAINT "FK_dfeb238c1d42370cf07ff9ffc66" FOREIGN KEY ("api_id") REFERENCES "${schema}"."api"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "${schema}"."api_version_endpoints" ADD CONSTRAINT "FK_37c0e29aa8001cd792cf55125b6" FOREIGN KEY ("api_version_id") REFERENCES "${schema}"."api_versions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';

        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" DROP CONSTRAINT "FK_37c0e29aa8001cd792cf55125b6"`);
        await queryRunner.query(`ALTER TABLE "${schema}"."api_versions" DROP CONSTRAINT "FK_dfeb238c1d42370cf07ff9ffc66"`);
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_comments" DROP CONSTRAINT "FK_c7b24ab79f6e2ef5519d1b3bbf7"`);
        await queryRunner.query(`DROP TABLE "${schema}"."api_version_endpoints"`);
        await queryRunner.query(`DROP TABLE "${schema}"."api_versions"`);
        await queryRunner.query(`DROP TABLE "${schema}"."api_version_change_logs"`);
        await queryRunner.query(`DROP TABLE "${schema}"."api_version_comments"`);
        await queryRunner.query(`DROP TABLE "${schema}"."api"`);
    }
}
