import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class GenTestTranIdColumnToFormVersionTable1750149460799 implements MigrationInterface {
    name = 'GenTestTranIdColumnToFormVersionTable1750149460799';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."form_versions" ADD "test_transaction_id" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."form_versions" DROP COLUMN "test_transaction_id"`);
    }
}
