import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class GenIsTestColumnToTransactionTable1750147240746 implements MigrationInterface {
    name = 'GenIsTestColumnToTransactionTable1750147240746';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."transaction" ADD "is_test" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."transaction" DROP COLUMN "is_test"`);
    }
}
