import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class ApiEndpointAddUniqueKey1749196200613 implements MigrationInterface {
    name = 'ApiEndpointAddUniqueKey1749196200613';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" ADD "name" character varying(150) DEFAULT ''`);
        await queryRunner.query(
            `ALTER TABLE "${schema}"."api_version_endpoints" ADD CONSTRAINT "UQ_c87aede666740413aa676179580" UNIQUE ("endpoint", "method")`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" DROP CONSTRAINT "UQ_c87aede666740413aa676179580"`);
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" DROP COLUMN "name"`);
    }
}
