import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class ApiAddEndpointIdentity1749105739901 implements MigrationInterface {
    name = 'ApiAddEndpointIdentity1749105739901';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" ADD "identity_id" uuid`);
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';
        await queryRunner.query(`ALTER TABLE "${schema}"."api_version_endpoints" DROP COLUMN "identity_id"`);
    }
}
