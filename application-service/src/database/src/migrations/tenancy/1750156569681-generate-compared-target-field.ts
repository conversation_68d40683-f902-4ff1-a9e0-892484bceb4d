import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class GenerateComparedTargetField1750156569681 implements MigrationInterface {
    name = 'GenerateComparedTargetField1750156569681';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';

        await queryRunner.query(`ALTER TABLE "${schema}"."general_auto_populate_settings" ADD "target_compared_field_id" uuid`);
        await queryRunner.query(
            `ALTER TABLE "${schema}"."general_auto_populate_settings" ADD "target_compared_field_type" character varying(30)`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';

        await queryRunner.query(`ALTER TABLE "${schema}"."general_auto_populate_settings" DROP COLUMN "target_compared_field_type"`);
        await queryRunner.query(`ALTER TABLE "${schema}"."general_auto_populate_settings" DROP COLUMN "target_compared_field_id"`);
    }
}
