import { MigrationInterface, QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export class TransactionEnteredStageLog1750661151563 implements MigrationInterface {
    name = 'TransactionEnteredStageLog1750661151563';

    public async up(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';

        await queryRunner.query(`ALTER TABLE "${schema}"."transaction" ADD "stage_entered_at" TIMESTAMP NOT NULL DEFAULT now()`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        let { schema } = queryRunner.connection.options as PostgresConnectionOptions;
        schema = schema ?? 'public';

        await queryRunner.query(`ALTER TABLE "${schema}"."transaction" DROP COLUMN "stage_entered_at"`);
    }
}
