import { MigrationInterface, QueryRunner } from 'typeorm';

export class GenerateComparedTargetField1750156536752 implements MigrationInterface {
    name = 'GenerateComparedTargetField1750156536752';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "general_auto_populate_settings" ADD "target_compared_field_id" uuid`);
        await queryRunner.query(`ALTER TABLE "general_auto_populate_settings" ADD "target_compared_field_type" character varying(30)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "general_auto_populate_settings" DROP COLUMN "target_compared_field_type"`);
        await queryRunner.query(`ALTER TABLE "general_auto_populate_settings" DROP COLUMN "target_compared_field_id"`);
    }
}
