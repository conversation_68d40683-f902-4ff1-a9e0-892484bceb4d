export enum TransactionEventEnum {
    // CRUD
    FORM_TRANSACTION_CREATED = 'FORM_TRANSACTION.CREATED',
    FORM_TRANSACTION_UPDATED = 'FORM_TRANSACTION.UPDATED',
    FORM_TRANSACTION_DELETED = 'FORM_TRANSACTION.DELETED',
    // RELATION
    FORM_RELATION_TRANSACTION_CREATED = 'FORM_TRANSACTION.RELATION_TRANSACTION_CREATED',
    // STAGE
    FORM_TRANSACTION_STAGE_UPDATED = 'FORM_TRANSACTION.STAGE_UPDATED',
    // FIELD
    FORM_TRANSACTION_FIELD_CREATED = 'FORM_TRANSACTION.FIELD_CREATED',
    FORM_TRANSACTION_FIELD_UPDATED = 'FORM_TRANSACTION.FIELD_UPDATED',
    FORM_TRANSACTION_FIELD_DELETED = 'FORM_TRANSACTION.FIELD_DELETED',
    // FIELD STYLE
    FORM_TRANSACTION_FIELD_STYLE_CREATED = 'FORM_TRANSACTION.FIELD_STYLE_CREATED',
    FORM_TRANSACTION_FIELD_STYLE_UPDATED = 'FORM_TRANSACTION.FIELD_STYLE_UPDATED',
    FORM_TRANSACTION_FIELD_STYLE_DELETED = 'FORM_TRANSACTION.FIELD_STYLE_DELETED',
    // COLLECTION FIELD
    FORM_TRANSACTION_COLLECTION_FIELD_CREATED = 'FORM_TRANSACTION.COLLECTION_FIELD_CREATED',
    FORM_TRANSACTION_COLLECTION_FIELD_UPDATED = 'FORM_TRANSACTION.COLLECTION_FIELD_UPDATED',
    FORM_TRANSACTION_COLLECTION_FIELD_DELETED = 'FORM_TRANSACTION.COLLECTION_FIELD_DELETED',

    // COMMAND
    FORM_TRANSACTION_AUTO_CREATION = 'FORM_TRANSACTION.AUTO_CREATION',

    // STATUS
    FORM_TRANSACTION_STATUS_UPDATED = 'FORM_TRANSACTION.STATUS_UPDATED',

    // SCHEDULED
    FORM_TRANSACTION_SCHEDULED = 'FORM_TRANSACTION.SCHEDULED',

    // MANUAL
    FORM_TRANSACTION_MANUAL = 'FORM_TRANSACTION.MANUAL',

    // WFS KPI STATUS
    FORM_STAGE_KPI_STATUS_CHANGED = 'FORM_TRANSACTION.STAGE_KPI_STATUS_CHANGED',

    //EXTERNAL
    EXTERNAL_NEW_DOCUMENT_CREATED = 'EXTERNAL_NEW_DOCUMENT.CREATED',
}

export enum DataRegisterEventEnum {
    // Builder
    DATA_REGISTER_RELEASED = 'DATA_REGISTER.RELEASED',
    DATA_REGISTER_PUBLISHED = 'DATA_REGISTER.PUBLISHED',
    DATA_REGISTER_CREATED = 'DATA_REGISTER.CREATED',
    DATA_REGISTER_UPDATED = 'DATA_REGISTER.UPDATED',
    DATA_REGISTER_DELETED = 'DATA_REGISTER.DELETED',
    // Record
    DATA_REGISTER_RECORD_CREATED = 'DATA_REGISTER.RECORD_CREATED',
    DATA_REGISTER_RECORD_UPDATED = 'DATA_REGISTER.RECORD_UPDATED',
    DATA_REGISTER_RECORD_DELETED = 'DATA_REGISTER.RECORD_DELETED',
    DATA_REGISTER_RECORD_FIELD_UPDATED = 'DATA_REGISTER.RECORD_FIELD_UPDATED',
    DATA_REGISTER_RECORD_STATUS_UPDATED = 'DATA_REGISTER.RECORD_STATUS_UPDATED',
    // Automation
    DATA_REGISTER_SCHEDULED = 'DATA_REGISTER.SCHEDULED',

    // Manual
    DATA_REGISTER_MANUAL = 'DATA_REGISTER.MANUAL',

    DATA_REGISTER_REFRESH_DATA = 'DATA_REGISTER.REFRESH_DATA',
}
