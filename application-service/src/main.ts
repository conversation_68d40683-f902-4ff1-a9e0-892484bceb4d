import 'newrelic'; // Import New Relic before anything else

import 'source-map-support/register';

import { ValidationPipe, VersioningType } from '@nestjs/common';
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { SASLOptions } from '@nestjs/microservices/external/kafka.interface';
import { ExpressAdapter, NestExpressApplication } from '@nestjs/platform-express';
import * as cors from 'cors';
import { json } from 'express';
import * as morgan from 'morgan'; // HTTP request logger
import { AppApiModule } from './app-api.module';
import { AppModule } from './app.module';
import { LoggerService } from './common/src';
import { AllExceptionsFilter } from './filters/all-exception.filter';
import { AppConfigService } from './shared/services/app-config.service';
import { SharedModule } from './shared/shared.module';
import { setupSwagger } from './shared/swagger/setup';

async function bootstrap() {
    let app: NestExpressApplication;

    if (process.env.MODULE == 'api') {
        console.log('Creating API Application');
        app = await NestFactory.create<NestExpressApplication>(AppApiModule, new ExpressAdapter(), {
            cors: true,
        });
    } else {
        console.log('Creating Main Application');
        app = await NestFactory.create<NestExpressApplication>(AppModule, new ExpressAdapter(), {
            cors: true,
        });
    }

    app.setGlobalPrefix('api');

    const loggerService = app.select(SharedModule).get(LoggerService);
    app.useLogger(loggerService);
    app.use(
        morgan('combined', {
            stream: {
                write: (message) => {
                    loggerService.log(message);
                },
            },
        }),
    );

    app.useGlobalPipes(
        new ValidationPipe({
            whitelist: true,
            transform: true,
            validationError: {
                target: false,
            },
        }),
    );

    app.enableVersioning({
        type: VersioningType.URI,
    });

    app.use(json({ limit: '50mb' }));

    const configService = app.select(SharedModule).get(AppConfigService);

    // if (["development", "staging"].includes(configService.nodeEnv)) {
    const document = setupSwagger(app, configService.swaggerConfig);
    app.use('/swagger.json', (req, res) => {
        res.json(document);
    });
    // }

    const httpAdapter = app.get(HttpAdapterHost);
    app.useGlobalFilters(new AllExceptionsFilter(httpAdapter));

    const port = configService.getNumber('PORT') || 3000;
    const host = configService.get('HOST') || '127.0.0.1';
    const origin = configService.get('ORIGIN') || '*';
    const corsOptions = {
        origin: origin,
        methods: 'GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS',
        credentials: origin !== '*',
        allowedHeaders:
            'Content-Type, Authorization, X-Requested-With, X-Tenant-Host, x-tenant-slug, Accept, X-XSRF-TOKEN, secret, recaptchavalue, baggage, request-id',
    };
    app.use(cors(corsOptions));

    app.enableCors({
        exposedHeaders: 'request-id',
    });

    if (process.env.DISABLE_WORKER != 'true' && process.env.MODULE != 'api') {
        app.connectMicroservice<MicroserviceOptions>({
            transport: Transport.KAFKA,
            options: {
                client: {
                    brokers: configService.kafkaConfig.broker ? configService.kafkaConfig.broker.split(',') : [],
                    ssl: configService.kafkaConfig.sslEnabled
                        ? {
                              rejectUnauthorized: false,
                          }
                        : false,
                    sasl: configService.kafkaConfig.saslEnabled
                        ? ({
                              mechanism: configService.kafkaConfig.mechanism,
                              username: configService.kafkaConfig.username,
                              password: configService.kafkaConfig.password,
                          } as SASLOptions)
                        : null,
                    connectionTimeout: +configService.kafkaConfig.connectionTimeout,
                    requestTimeout: +configService.kafkaConfig.requestTimeout,
                },
                consumer: {
                    groupId: configService.kafkaConsumerConfg.groupId,
                    heartbeatInterval: +configService.kafkaConsumerConfg.heartbeatInterval,
                    sessionTimeout: +configService.kafkaConsumerConfg.sessionTimeout,
                },
            },
        });

        app.startAllMicroservices();
    }

    await app.listen(port, host);

    console.log(`server running on port ${host}:${port}`);
    loggerService.warn(`server running on port ${host}:${port}`);
}

bootstrap();
