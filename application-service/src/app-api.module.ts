import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { BullModule } from '@nestjs/bull';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TerminusModule } from '@nestjs/terminus';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestContextModule } from 'nestjs-request-context';

import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AppLoggerMiddleware } from './common/src';
import { ContextInterceptor } from './common/src/application/context/ContextInterceptor';
import { CommonFormACLModule } from './common/src/modules/acl/form-acl.module';
import { CommonAuthModule } from './common/src/modules/auth/auth.module';
import { DatabaseModule } from './database/src/database.module';
import { DatabaseOptions } from './database/src/interfaces/database-options-provider.interface';
import { LoggingInterceptor } from './interceptors/http-log.interceptor';
import { DataRegisterModule } from './modules/data-register/data-register.module';
import { FormModule } from './modules/form/form.module';
import { LogModule } from './modules/log/log.module';
import { ValidationFormFieldModule } from './modules/validation/validation.module';
import { WebhookModule } from './modules/webhook/webhook.module';
import { AppConfigService } from './shared/services/app-config.service';
import { SharedModule } from './shared/shared.module';
import { ApiModule } from './modules/api/api.module';

const databaseModule = DatabaseModule.forRootAsync({
    useFactory: (appConfig: AppConfigService) => {
        return { connectionString: appConfig.typeOrmPostgreSqlConfig } as DatabaseOptions;
    },
    inject: [AppConfigService],
});

@Module({
    imports: [
        RequestContextModule,
        TerminusModule,
        TypeOrmModule.forRootAsync({
            imports: [SharedModule],
            useFactory: (configService: AppConfigService) => configService.typeOrmPostgreSqlConfig,
            inject: [AppConfigService],
        }),
        SharedModule,
        CommonAuthModule.forRootAsync({
            useFactory: () => ({}),
            extraProviders: [],
            imports: [databaseModule],
        }),
        databaseModule,
        EventEmitterModule.forRoot({
            wildcard: true,
        }),
        AutomapperModule.forRoot({
            strategyInitializer: classes(),
        }),
        BullModule.forRootAsync({
            imports: [SharedModule],
            useFactory: (configService: AppConfigService) => configService.bull,
            inject: [AppConfigService],
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [AppConfigService],
            useFactory: async (configService: AppConfigService) => ({
                secret: configService.jwtConfig.appSecret,
                signOptions: { expiresIn: configService.jwtConfig.expire },
            }),
        }),
        DataRegisterModule,
        ValidationFormFieldModule,
        FormModule,
        CommonFormACLModule.forRootAsync({
            useFactory: () => ({}),
            extraProviders: [],
            imports: [databaseModule],
        }),
        LogModule,
        WebhookModule,
        ApiModule,
    ],
    controllers: [AppController],
    providers: [
        AppService,
        {
            provide: APP_INTERCEPTOR,
            useClass: ContextInterceptor,
        },
        {
            provide: APP_INTERCEPTOR,
            useClass: LoggingInterceptor,
        },
    ],
})
export class AppApiModule implements NestModule {
    configure(consumer: MiddlewareConsumer): void {
        consumer.apply(AppLoggerMiddleware).forRoutes('*');
    }
}
