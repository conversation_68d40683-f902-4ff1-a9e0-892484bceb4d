import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import * as _ from 'lodash';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { DataSource, In, Repository } from 'typeorm';
import { ClaimService, LoggerService } from '../common/src';
import { FormRoleStageACLDto } from '../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { IStageRoleACL, IStageRoleACLProvider } from '../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { DEFAULT_STAGE_KPI_FIELD_ID, DEFAULT_TRANSACTION_FIELD_ID, USER_CLAIMS } from '../constant';
import { PROVIDER_KEYS } from '../database/src/constants/providers';
import { DataRegisterFieldTenancyEntity } from '../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { FormCollectionTenancyEntity } from '../database/src/entities/tenancy/form-collection.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageRoleAccessControlConfig } from '../database/src/shared/dto/access-control.dto';
import { TransactionFieldContextTypeEnum } from '../database/src/shared/enums/transaction-field.enum';
import { FormTransactionDto } from '../modules/form/dtos';
import { FormTransactionDataService } from '../modules/form/services/data/form-transaction.data.service';
import { ResponseDto } from '../shared/common/dto/response.dto';
import { CommonService } from '../shared/services/common.service';

@Injectable()
export class FormTransactionDetailInterceptor implements NestInterceptor<any, any> {
    constructor(
        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,

        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        private readonly _commonService: CommonService,

        private readonly dataService: FormTransactionDataService,

        private readonly loggerService: LoggerService,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly formCollectionRepository: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity>,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler<ResponseDto<FormTransactionDto>>): Observable<any> {
        return next.handle().pipe(
            switchMap(async (responseDto) => {
                if (!responseDto.data) return responseDto;
                let transaction = responseDto.data;
                const roleIds: string[] = this._claims.roles.map((role) => role.id);

                if (!roleIds.length) {
                    return null;
                }

                try {
                    transaction = await this.transactionUsersInterceptor(transaction);
                } catch (err) {
                    this.loggerService.error(err);
                }

                try {
                    transaction = await this.fieldsInterceptor(transaction, roleIds);
                } catch (err) {
                    this.loggerService.error(err);
                }

                return {
                    ...responseDto,
                    data: transaction,
                };
            }),
        );
    }

    async fieldsInterceptor(transaction: FormTransactionDto, roleIds: string[]): Promise<FormTransactionDto> {
        const stageRoles = roleIds.map((roleId) => ({ roleId, stageId: transaction.stageId }));

        const aclResult: FormRoleStageACLDto[] = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: transaction.formVersionId,
                stageRoles,
            },
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
        });

        const parentIds = transaction.transactionFields?.map((field) => field.parentId);

        const collectionFields = transaction?.transactionFields?.filter(
            (f) => f.contextType === TransactionFieldContextTypeEnum.COLLECTION,
        );

        const formCollectionEntities = await this.formCollectionRepository.find({
            where: {
                formVersionId: transaction.formVersionId,
            },
            select: ['dataRegisterVersionId'],
        });

        let collectionFieldEntities: DataRegisterFieldTenancyEntity[] = [];
        const collectionFieldIds = collectionFields?.map((f) => f.fieldId) ?? [];
        const drVersionIds = formCollectionEntities?.map((c) => c.dataRegisterVersionId) ?? [];

        if (collectionFieldIds?.length && drVersionIds?.length) {
            collectionFieldEntities = await this.dataRegisterFieldRepository.find({
                where: {
                    fieldId: In(collectionFieldIds),
                    dataRegisterVersionId: In(drVersionIds),
                },
            });
        }

        const readableTransactionFields = transaction.transactionFields
            ?.map((transactionField) => {
                if (
                    transactionField.fieldId === DEFAULT_TRANSACTION_FIELD_ID ||
                    transactionField.fieldId === DEFAULT_STAGE_KPI_FIELD_ID ||
                    parentIds?.includes(transactionField.id)
                ) {
                    return transactionField;
                }

                const collectionFieldEntity = collectionFieldEntities.find((f) => f.fieldId === transactionField.fieldId);
                if (collectionFieldEntity?.configuration?.isSupportField?.toString() !== 'true') {
                    return transactionField;
                }

                const fieldAcls: StageRoleAccessControlConfig[] = _.compact(aclResult.map((acl) => acl.field[transactionField.fieldId]));

                const collectionAcls: StageRoleAccessControlConfig[] = _.compact(
                    aclResult.map((acl) => acl.collection[`${transactionField.fieldId}_${transactionField.collectionItemId}`]),
                );

                const canRead =
                    fieldAcls.some((fieldAcl) => fieldAcl.visible?.toString() === 'true') ||
                    collectionAcls.some((fieldAcl) => fieldAcl.visible?.toString() === 'true');

                if (!canRead) {
                    return null;
                }

                return transactionField;
            })
            .filter(Boolean);

        transaction.transactionFields = readableTransactionFields;
        return transaction;
    }

    async transactionUsersInterceptor(transaction: FormTransactionDto): Promise<FormTransactionDto> {
        const [firstNameCreatedBy, ...secondNameCreatedBy] = transaction.createdByUser?.split(' ') ?? [];
        const [firstNameUpdatedBy, ...secondNameUpdatedBy] = transaction.updatedByUser?.split(' ') ?? [];

        transaction.created = {
            id: transaction.createdBy,
            firstName: firstNameCreatedBy ?? '',
            secondName: secondNameCreatedBy?.join(' ') ?? '',
        };

        transaction.updated = {
            id: transaction.updatedBy,
            firstName: firstNameUpdatedBy ?? '',
            secondName: secondNameUpdatedBy?.join(' ') ?? '',
        };

        return transaction;
    }
}
