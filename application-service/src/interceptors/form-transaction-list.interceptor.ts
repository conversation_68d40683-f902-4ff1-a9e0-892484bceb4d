import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import * as _ from 'lodash';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { DataSource } from 'typeorm';
import { ClaimService } from '../common/src';
import { FormRoleStageACLDto } from '../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { IStageRoleACL, IStageRoleACLProvider } from '../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { DEFAULT_STAGE_KPI_FIELD_ID, DEFAULT_TRANSACTION_FIELD_ID, USER_CLAIMS } from '../constant';
import { PROVIDER_KEYS } from '../database/src/constants/providers';
import { StageRoleAccessControlTenancyEntity } from '../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageRoleAccessControlConfig } from '../database/src/shared/dto/access-control.dto';
import { FormTransactionDto } from '../modules/form/dtos';
import { PaginationResponseDto } from '../shared/common/dto/pagination-response.dto';
import { ResponseDto } from '../shared/common/dto/response.dto';

@Injectable()
export class FormTransactionListInterceptor implements NestInterceptor<any, any> {
    constructor(
        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler<ResponseDto<PaginationResponseDto<FormTransactionDto>>>): Observable<any> {
        return next.handle().pipe(
            switchMap(async (pagedResult) => {
                const { data: transactions, total } = pagedResult?.data;

                if (!transactions?.length) {
                    return pagedResult;
                }

                const roleIds: string[] = this._claims.roles.map((role) => role.id);

                if (!roleIds.length)
                    return {
                        data: [],
                        total: 0,
                    };

                const aclResults = await this._groupACLs({
                    roleIds,
                    transactions: transactions as any[],
                });

                for (const transaction of transactions) {
                    const aclResult: FormRoleStageACLDto[] =
                        (aclResults || []).find((acl) => acl.transactionId === transaction.id)?.aclResult || [];
                    const transactionFields = transaction.transactionFields || [];
                    transactionFields.forEach((transactionField) => {
                        if (
                            transactionField.fieldId === DEFAULT_TRANSACTION_FIELD_ID ||
                            transactionField.fieldId === DEFAULT_STAGE_KPI_FIELD_ID
                        ) {
                            return;
                        }
                        const fieldAcls: StageRoleAccessControlConfig[] = _.compact(
                            aclResult.map((acl) => acl.field[transactionField.fieldId]),
                        );

                        const canRead = fieldAcls.some((fieldAcl) => fieldAcl.visible?.toString() === 'true');
                        if (!canRead) {
                            delete transactionField.fieldOptionIds;
                            delete transactionField.displayValue;

                            transactionField.fieldValue = '******';
                        }
                    });
                }

                return pagedResult;
            }),
        );
    }

    async _groupACLs({ roleIds, transactions }: { roleIds: string[]; transactions: FormTransactionDto[] }): Promise<
        {
            aclResult: FormRoleStageACLDto[];
            transactionId: string;
        }[]
    > {
        // Create a map to group transactions by formVersionId and stageId
        const groupedTransactions = new Map<
            string,
            {
                formVersionId: string;
                stageId: string;
                transactions: FormTransactionDto[];
            }
        >();

        // Group transactions
        transactions.forEach((transaction) => {
            const key = `${transaction.formVersionId}_${transaction.stageId}`;
            if (!groupedTransactions.has(key)) {
                groupedTransactions.set(key, {
                    formVersionId: transaction.formVersionId,
                    stageId: transaction.stageId,
                    transactions: [],
                });
            }
            groupedTransactions.get(key).transactions.push(transaction);
        });

        // Get ACLs for each group
        const groupResults = await Promise.all(
            Array.from(groupedTransactions.values()).map(async (group) => {
                const stageRoles = roleIds.map((roleId) => ({ roleId, stageId: group.stageId }));
                const aclResult = await this._stageRoleACL.getRoleStageACLs({
                    request: {
                        formVersionId: group.formVersionId,
                        stageRoles,
                    },
                    stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
                    stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
                });
                return { aclResult, transactions: group.transactions };
            }),
        );

        // Apply ACLs to each transaction
        return transactions.map((transaction) => {
            const group = groupResults.find((g) => g.transactions.some((t) => t.id === transaction.id));
            return {
                aclResult: group.aclResult,
                transactionId: transaction.id,
            };
        });
    }
}
