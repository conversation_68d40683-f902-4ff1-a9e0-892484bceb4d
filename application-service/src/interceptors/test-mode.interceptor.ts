import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class TestModeInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const { method, query, body } = request;

        // Check if it's a create/update API and has test=true parameter
        const isCreateUpdateMethod = ['POST', 'PUT'].includes(method);
        const isDeleteMethod = ['DELETE'].includes(method);
        const isTestMode = query.test === 'true';

        if (isCreateUpdateMethod && isTestMode) {
            // Return example response instead of running the service
            const exampleResponse = this.generateExampleResponse(request);
            return of(exampleResponse);
        }

        if (isDeleteMethod && isTestMode) {
            // Return example response instead of running the service
            return of({
                success: true,
                message: 'This is only testing result',
            });
        }

        // If not test mode, continue with normal processing
        return next.handle();
    }

    private generateExampleResponse(request: any): any {
        const { method, url, body } = request;
        const timestamp = new Date().toISOString();

        // Create example response based on method and URL
        const baseResponse = {
            success: true,
            data: {
                id: '8d1916d7-152f-4b25-85ce-c110eff42a41',
                status: 'processing',
            },
            message: 'This is only testing result',
        };

        return baseResponse;
    }
}
