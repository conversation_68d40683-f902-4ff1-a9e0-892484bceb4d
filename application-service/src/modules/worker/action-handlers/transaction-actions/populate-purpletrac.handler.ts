import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import * as dayjs from 'dayjs';
import { getFolderUploadPath } from 'src/modules/file/utils';
import { FileService } from 'src/shared/services/file-service.service';
import { Repository } from 'typeorm';
import { CacheService, MqttService, UtilsService } from '../../../../common/src';
import { PURPLETRAC_REPORT_PROPERTY_ID } from '../../../../common/src/constant/field';
import { TransactionMQTTTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { AutoPopulateBuilderTypeEnum } from '../../../../database/src/shared/enums/ap-builder-type.enum';
import { CaptureActiveFormVersionType } from '../../../../database/src/shared/providers/capture-active-form-version.provider';
import { AutoPopulateDataLakeResponse } from '../../../../shared/common/dto/autopopulate-datalake.dto';
import { EditFormTransactionRequest } from '../../../form/dtos/requests/create-form-transaction.request';
import { FormTransactionDataService } from '../../../form/services/data/form-transaction.data.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { ScreeningObject } from '../../dtos/purpleTRAC/get-vessel-screening-result-response.dto';
import { PurpleTRACDataService } from '../../services/purpleTRAC.data.service';
import { PurpleTracProperty } from '../../services/purpleTRAC.interface';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';
import { FormCollectionDataService } from '../../../form/services/form-collection.data.service';

@Injectable()
export class PopulatePurpleTRACHandler extends AbstractActionHandler {
    // private readonly _logger = new Logger(PopulatePurpleTRACHandler.name);

    constructor(
        moduleRef: ModuleRef,
        private readonly _purpleTRACDataService: PurpleTRACDataService,
        private readonly _cacheService: CacheService,
        private readonly _dataSourceService: DataSourceService,
        private readonly _fileService: FileService,
        private readonly _mqttService: MqttService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(parsedEvent: ParsedActionDto): ValidateEventResult {
        const formId = parsedEvent.contextTransactionId;
        if (!formId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${parsedEvent.type} Form with id ${formId} not found in configuration!`,
            };
        }

        if (!parsedEvent?.contextTransactionId || !parsedEvent?.contextVersionId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${parsedEvent.type} Form with transactionId or formVersionId  not found in configuration!`,
            };
        }

        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const formId = parsedEvent.contextId;
            const formVersionId = parsedEvent.contextVersionId;
            const transactionId = parsedEvent.contextTransactionId;
            const accountId = parsedEvent.metadata.tenantId;
            const dataSource = await this._getDataSource(accountId);

            const publicDataSource = await this._getDataSource();

            const transactionRepo = dataSource.getRepository(TransactionEntity);
            const transactionFieldRepo = dataSource.getRepository(TransactionFieldEntity);
            const apSettingRepo = dataSource.getRepository(GeneralAutoPopulateSettingTenancyEntity);
            const formCollectionRepo = dataSource.getRepository(FormCollectionTenancyEntity);

            const transaction = await transactionRepo.findOne({
                where: {
                    id: transactionId,
                },
                withDeleted: true,
                select: ['id', 'stageId', 'isTest', 'deletedAt'],
            });

            if (!transaction.isTest && transaction.deletedAt) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'Transaction is deleted' };
            }

            // * Check auto populate config has PurpleTRAC datasource
            const cacheKey = UtilsService.getActiveFormVersionCacheKeys({
                accountId: accountId,
                formId: formId,
                formVersionId: formVersionId,
            });

            let cachedFormVersion: CaptureActiveFormVersionType;

            if (cacheKey) {
                cachedFormVersion = await this._cacheService.jsonGet<CaptureActiveFormVersionType>(cacheKey.formVersionKey);
            }

            let apSettings: GeneralAutoPopulateSettingTenancyEntity[] = [];

            if (transaction.isTest) {
                apSettings = await this.getTestAutoPopulateSettings({
                    formId,
                    formVersionId,
                    dataSourceType: 'PURPLETRAC' as any,
                    accountId,
                    apSettingRepo,
                    formCollectionRepo,
                });
            } else {
                apSettings = cachedFormVersion?.formAutoPopulateSettings?.length
                    ? cachedFormVersion?.generalAutoPopulateSettings.filter((ap) => ap.dataSourceType === ('PURPLETRAC' as any))
                    : await apSettingRepo.find({
                          where: {
                              builderId: formId,
                              builderVersionId: formVersionId,
                              dataSourceType: 'PURPLETRAC' as any,
                          },
                          relations: ['extraConfigurations'],
                      });
            }

            if (!apSettings.length) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No auto populate config' };
            }
            // * Extract imo number
            const { vesselIMO, IMO } = parsedEvent.payload?.configuration || {};

            if (!vesselIMO && !IMO) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No IMO config' };
            }

            const imoNumberFieldId = IMO ?? vesselIMO;

            const imoNumber = await transactionFieldRepo.findOne({
                where: {
                    transactionId: transactionId,
                    fieldId: imoNumberFieldId,
                },
                select: ['id', 'fieldId', 'fieldValue'],
            });

            if (!imoNumber?.fieldValue) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No IMO value' };
            }

            // * Check if PurpleTRAC properties exist
            const purpleTracProperties = (await publicDataSource.query(
                "SELECT id, name, type, data_type, document_type, document_path FROM public.ctx_properties WHERE datasource = 'PURPLETRAC' and deleted_at is null",
            )) as PurpleTracProperty[];

            if (!purpleTracProperties.length) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No PurpleTRAC properties' };
            }

            // * Integrate with PurpleTRAC to get data
            console.log('Trigger PurpleTRAC with IMO: ', imoNumber?.fieldValue);
            this._mqttService.publish(
                `${parsedEvent.metadata?.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_PURPLETRAC}/${transactionId}`,
                { status: 'purpletrac_processing' },
            );

            const purpleTRACScreeningResult = await this._purpleTRACDataService.submitAndGetResult({
                carbon_rating: false,
                registered_name: imoNumber?.fieldValue,
                accountId: accountId,
                mdsTransactionId: transactionId,
            });

            if (!purpleTRACScreeningResult) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No PurpleTRAC screening result' };
            }

            // * Integrate with PurpleTRAC to get report file
            const purpleTRACReport = await this._purpleTRACDataService.getPdfReport({
                transactionId: purpleTRACScreeningResult.id,
            });

            // * Store file to storage and return path
            const PURPLETRAC_REPORT_FILE_NAME = `purpleTRACReport-${imoNumber?.fieldValue}-${dayjs().format('YYYY-MM-DD')}.pdf`;
            const reportPath = await this.handleUpload(purpleTRACReport, PURPLETRAC_REPORT_FILE_NAME, formId, transactionId, accountId);

            // * Parse to auto-populate payload
            const autoPopulatePurpleTRACPayload: Array<Partial<AutoPopulateDataLakeResponse>> = this.parsePurpleTRACPayload({
                apSettings,
                purpleTracProperties,
                purpleTRACScreeningResult,
                reportFileName: PURPLETRAC_REPORT_FILE_NAME,
                reportPath,
                transactionId,
            });
            // * Run update transaction with autoPopulatePurpleTRACPayload
            const service = await this._dataSourceService.resolveService<FormTransactionDataService>(accountId, FormTransactionDataService);

            const updateRequest: EditFormTransactionRequest = {
                transactionId: transactionId,
                transactionFields: [],
                formId: formId,
                formValues: {},
                isTest: transaction?.isTest,
            };

            await service.update({
                id: transactionId,
                request: updateRequest,
                option: {
                    shouldRunPopulateFormFields: true,
                    forceRunAutoPopulate: true,
                    purpleTRACPopulatedData: autoPopulatePurpleTRACPayload,
                },
            });

            this._mqttService.publish(
                `${parsedEvent.metadata?.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_PURPLETRAC}/${transactionId}`,
                { status: 'purpletrac_completed' },
            );

            return { type: 'TriggeredActionSuccess' };
        } catch (error) {
            Logger.error(error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            // TODO: rollback
            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            Logger.error(err);
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }

    private parsePurpleTRACPayload({
        apSettings,
        purpleTracProperties,
        purpleTRACScreeningResult,
        reportFileName,
        reportPath,
        transactionId,
    }: {
        apSettings: GeneralAutoPopulateSettingTenancyEntity[];
        purpleTracProperties: PurpleTracProperty[];
        purpleTRACScreeningResult: ScreeningObject;
        reportFileName: string;
        reportPath: string;
        transactionId: string;
    }) {
        const autoPopulatePurpleTRACPayload: Array<Partial<AutoPopulateDataLakeResponse>> = [];
        apSettings.forEach((apSetting) => {
            const { targetFieldId, fieldId } = apSetting;

            if (targetFieldId === PURPLETRAC_REPORT_PROPERTY_ID) {
                // * push path to autoPopulatePurpleTRACPayload if purpleTRACReport is stored in storage
                autoPopulatePurpleTRACPayload.push({
                    fieldId: fieldId,
                    value: reportFileName,
                    ctxPropertyId: targetFieldId,
                    mdsId: transactionId,
                    metadata: {
                        path: reportPath,
                    },
                });
                return;
            }

            const purpleTracProperty = purpleTracProperties.find((property) => property.id === targetFieldId);

            if (!purpleTracProperty || !purpleTracProperty.document_path) {
                return;
            }

            const { document_path } = purpleTracProperty;

            const isScreeningResultPath = document_path.includes('.');

            if (isScreeningResultPath) {
                const screeningResultPath = document_path.split('.');

                const [screeningResult, path] = screeningResultPath;

                const screeningResultItem = purpleTRACScreeningResult?.screen_results?.find((item) => item.check === screeningResult);

                if (!screeningResultItem) {
                    return;
                }

                const screeningResultValue = screeningResultItem[path];

                if (!screeningResultValue) {
                    return;
                }

                autoPopulatePurpleTRACPayload.push({
                    fieldId: fieldId,
                    value: screeningResultValue,
                    ctxPropertyId: targetFieldId,
                    mdsId: transactionId,
                });
            } else {
                const screeningResultPath = document_path;

                if (purpleTRACScreeningResult?.[screeningResultPath]) {
                    autoPopulatePurpleTRACPayload.push({
                        fieldId: fieldId,
                        value: purpleTRACScreeningResult[screeningResultPath],
                        ctxPropertyId: targetFieldId,
                        mdsId: transactionId,
                    });
                }
            }
        });

        return autoPopulatePurpleTRACPayload;
    }

    private handleUpload = async (file: Buffer, fileName: string, formId: string, transactionId: string, accountId: string) => {
        if (!file?.length) {
            return null;
        }
        const folderPath = getFolderUploadPath({ formId, transactionId });
        try {
            const result = await this._fileService.uploadFile(fileName, file, folderPath, accountId);
            return result.filePath;
        } catch (error) {
            Logger.error(error);
            return null;
        }
    };

    private getTestAutoPopulateSettings = async ({
        formId,
        formVersionId,
        dataSourceType,
        accountId,
        apSettingRepo,
        formCollectionRepo,
    }: {
        formId: string;
        formVersionId: string;
        dataSourceType: string;
        accountId: string;
        apSettingRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>;
        formCollectionRepo: Repository<FormCollectionTenancyEntity>;
    }) => {
        const testService = await this._dataSourceService.resolveService<FormCollectionDataService>(accountId, FormCollectionDataService);
        // AP form fields
        const apFormSettings = await apSettingRepo.find({
            where: {
                builderId: formId,
                builderVersionId: formVersionId,
                dataSourceType: dataSourceType as any,
                builderType: AutoPopulateBuilderTypeEnum.FormField,
            },
            relations: ['extraConfigurations'],
        });
        // AP collection fields
        const formCollections = await formCollectionRepo.find({
            where: {
                formVersionId,
            },
            relations: ['formCollectionItems'],
        });

        const formCollectionItems = formCollections?.flatMap((collection) => collection.formCollectionItems);

        const allApCollectionSettings = await testService.getTestCollectionAutoPopulateSettings({
            formVersionId,
            formId,
            formCollectionItems,
            collections: formCollections,
        });

        const apCollectionSettings = allApCollectionSettings.filter((ap) => ap.dataSourceType === dataSourceType);

        return [...apFormSettings, ...apCollectionSettings];
    };
}
