import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { ParsedActionDto } from '../../dtos/action.dto';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';
import { CreationRelatedTransactionEventDto } from '../../../../modules/form/dtos/requests/auto-creation.request';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { AutoCreationTransactionService } from '../../../../modules/form/services/auto-creation.tenancy.service';

@Injectable()
export class CreateRelatedTransactionActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(parsedEvent: ParsedActionDto): ValidateEventResult {
        const formId = parsedEvent.payload?.configuration?.formId;
        const targetField = parsedEvent.payload?.configuration?.targetField;

        if (!formId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${parsedEvent.type} Form id not found in configuration!`,
            };
        }

        if (!targetField) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${parsedEvent.type} Target field not found in configuration!`,
            };
        }

        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const formId = parsedEvent.contextId;
            const formVersionId = parsedEvent.contextVersionId;
            const transactionId = parsedEvent.contextTransactionId;
            const accountId = parsedEvent.metadata.tenantId;

            const dataSource = await this._getDataSource(accountId);
            const formFieldRepo = dataSource.getRepository(FormFieldTenancyEntity);
            const transRepo = dataSource.getRepository(TransactionEntity);
            const transFieldRepo = dataSource.getRepository(TransactionFieldEntity);
            const stageRepo = dataSource.getRepository(StageTenancyEntity);

            const formFields = await formFieldRepo.findBy({
                formVersionId,
            });
            const trans = await transRepo.findOneBy({
                id: transactionId,
            });

            if (!trans) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${parsedEvent.type} Transaction with id ${transactionId} not found in database!`,
                };
            }

            const tranFields = await transFieldRepo.findBy({
                transactionId,
            });
            const stage = await stageRepo.findOneBy({
                formVersionId: formVersionId,
                id: trans.stageId,
            });

            const request: CreationRelatedTransactionEventDto = {
                formId,
                formVersionId,
                accountId,
                transactionId,
                tranFields: tranFields,
                fields: formFields,
                stageId: trans.stageId,
                stageIdentityId: stage.identityId,
                relatedFormId: parsedEvent.payload?.configuration?.formId,
                configs: {
                    fieldId: parsedEvent.payload?.configuration?.targetField,
                },
                user: RequestContextService.currentUser(),
            };

            const service = await this._dataSourceService.resolveService<AutoCreationTransactionService>(
                accountId,
                AutoCreationTransactionService,
                undefined,
                {
                    user: RequestContextService.currentUser() ?? {},
                },
            );
            await service.handleRelatedCreationAction(request);

            return { type: 'TriggeredActionSuccess', metadata: {} };
        } catch (error) {
            Logger.error(error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            const transactionId = actionResult?.metadata?.transactionId;

            if (!transactionId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Transaction id not found in action result!',
                };
            }

            const dataSource = await this._getDataSource(context.event.metadata.tenantId);
            const transRepo = dataSource.getRepository(TransactionEntity);

            await transRepo.softDelete({
                id: transactionId,
            });

            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            Logger.error(err);
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }
}
