import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { LoggerService } from 'src/common/src';
import { EventDrivenService } from 'src/common/src/modules/event-driven/event-driven.service';
import { ActionEventEnum } from 'src/common/src/modules/shared/enums/event-driven/action-event.enum';
import { TransactionTopicEnum } from 'src/common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { PROVIDER_KEYS } from 'src/database/src/constants/providers';
import { CollectionTransactionTenancyEntity } from 'src/database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from 'src/database/src/entities/tenancy/data-registers.tenancy.entity';
import { TransactionEntity } from 'src/database/src/entities/tenancy/transaction.tenancy.entity';
import { DataRegisterTypeEnum } from 'src/database/src/shared/enums/data-register-type.enum';
import { FormTransactionTenancyService } from 'src/modules/form/services/form-transaction.tenancy.service';
import { In, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { HandleActionResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';
import { ConfigService } from '../../../../common/src';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';

@Injectable()
export class FormTransactionRefreshService extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _loggerService: LoggerService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _configService: ConfigService,
    ) {
        super(moduleRef);
    }
    validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(actionDto: ParsedActionDto): Promise<HandleActionResult> {
        try {
            // ignore collection record
            const drRepo = await this._dataSourceService.resolveService<Repository<DataRegisterTenancyEntity>>(
                actionDto.metadata.tenantId,
                PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY,
            );
            const dataRegister = await drRepo.findOne({
                where: {
                    id: actionDto.payload.external.dataRegisterId,
                },
            });
            if (dataRegister.type === DataRegisterTypeEnum.Collection) {
                return { type: 'TriggeredActionSuccess', metadata: {} };
            }
            const formTransactionTenancyService = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
                actionDto.metadata.tenantId,
                FormTransactionTenancyService,
            );
            // query all transaction use that register in form fields
            const transactions = await formTransactionTenancyService.getTransactionUseRegisterRecord(actionDto.payload.external.id);
            const recentTransactionIds = await formTransactionTenancyService.getRecentTransaction();
            const sortedTrans = this.sortTransactions(transactions, recentTransactionIds) || [];
            if (!sortedTrans?.length) {
                return { type: 'TriggeredActionSuccess', metadata: {} };
            }

            // Process first 5 transactions with high priority
            // Update these lines to use the constant
            const numberOfHotItems = this._configService.refreshTransaction?.hotItems ?? 5;
            const highPriorityTrans = sortedTrans.slice(0, numberOfHotItems);
            const normalPriorityTrans = sortedTrans.slice(numberOfHotItems);

            const highPriorityTranIds = highPriorityTrans.map((item) => item.id);
            if (highPriorityTranIds.length) {
                const highPriorityKey = v4();
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        ...actionDto.payload,
                        actionId: v4(),
                        transactionIds: highPriorityTranIds,
                    },
                    correlationId: actionDto.metadata.correlationId,
                    aggregateId: highPriorityKey,
                    type: ActionEventEnum.REFRESH_FROM_TRANSACTION_FIELDS_DATA,
                    name: ActionEventEnum.REFRESH_FROM_TRANSACTION_FIELDS_DATA,
                });

                this._loggerService.debug('publish high priority refresh transaction fields for: ' + highPriorityTranIds.join(','));
                await this._eventDrivenService.pushEvent(
                    message,
                    TransactionTopicEnum.FORM_TRANSACTION_REFRESH_FORM_FIELDS_TOPIC_HIGHT_PRIORITY,
                    highPriorityKey,
                );
            }

            // query all transaction use that register in collections
            const transactionsUseInCollections = await formTransactionTenancyService.getTransactionUseRegisterRecordInCollection(
                actionDto.payload.external.id,
            );
            const sortedCollectionTrans = this.sortTransactions(transactionsUseInCollections, recentTransactionIds) || [];
            const highPriorityCollectionTrans = sortedCollectionTrans.slice(0, numberOfHotItems)?.map((item) => item.id);
            const normalPriorityCollectionTrans = sortedCollectionTrans.slice(numberOfHotItems);

            if (highPriorityCollectionTrans.length) {
                const highPriorityKey = v4();
                const collectionTranMessage = EventDrivenService.createCommonEvent({
                    payload: {
                        ...actionDto.payload,
                        actionId: v4(),
                        transactionIds: highPriorityCollectionTrans,
                    },
                    correlationId: actionDto.metadata.correlationId,
                    aggregateId: highPriorityKey,
                    type: ActionEventEnum.REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA,
                    name: ActionEventEnum.REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA,
                });
                await this._eventDrivenService.pushEvent(
                    collectionTranMessage,
                    TransactionTopicEnum.FORM_TRANSACTION_REFRESH_COLLECTION_FIELDS_TOPIC_HIGHT_PRIORITY,
                    highPriorityKey,
                );
            }

            // process normal priority transactions
            if (normalPriorityTrans.length) {
                const normalPriorityKey = v4();
                const normalPriorityMessage = EventDrivenService.createCommonEvent({
                    payload: {
                        ...actionDto.payload,
                        actionId: v4(),
                        transactionIds: normalPriorityTrans?.map((item) => item.id) ?? [],
                        isLowPriority: true,
                    },
                    tenantId: RequestContextService.accountId,
                    correlationId: actionDto.metadata.correlationId,
                    aggregateId: normalPriorityKey,
                    type: ActionEventEnum.REFRESH_FROM_TRANSACTION_FIELDS_DATA,
                    name: ActionEventEnum.REFRESH_FROM_TRANSACTION_FIELDS_DATA,
                });
                await this._eventDrivenService.pushEvent(
                    normalPriorityMessage,
                    TransactionTopicEnum.FORM_TRANSACTION_REFRESH_FORM_FIELDS_TOPIC,
                    normalPriorityKey,
                );
            }

            if (normalPriorityCollectionTrans.length) {
                const normalPriorityKey = v4();
                const normalPriorityMessage = EventDrivenService.createCommonEvent({
                    payload: {
                        ...actionDto.payload,
                        actionId: v4(),
                        transactionIds: normalPriorityCollectionTrans?.map((item) => item.id) ?? [],
                        isLowPriority: true,
                    },
                    correlationId: actionDto.metadata.correlationId,
                    aggregateId: normalPriorityKey,
                    type: ActionEventEnum.REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA,
                    name: ActionEventEnum.REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA,
                });
                await this._eventDrivenService.pushEvent(
                    normalPriorityMessage,
                    TransactionTopicEnum.FORM_TRANSACTION_REFRESH_COLLECTION_FIELDS_TOPIC,
                    normalPriorityKey,
                );
            }

            return { type: 'TriggeredActionSuccess', metadata: {} };
        } catch (error) {
            this._loggerService.error(error);
            return { type: 'TriggeredActionErrorGoRetry', message: error.message };
        }
    }

    sortTransactions = (transactions: TransactionEntity[], recentTransactionIds: string[]) => {
        const sortedTransactions = [...transactions].sort((a, b) => {
            const aIndex = recentTransactionIds.indexOf(a.id);
            const bIndex = recentTransactionIds.indexOf(b.id);

            // If both are in recent transactions, sort by their position
            if (aIndex !== -1 && bIndex !== -1) {
                return aIndex - bIndex;
            }

            // If only one is in recent transactions, prioritize it
            if (aIndex !== -1) return -1;
            if (bIndex !== -1) return 1;

            // If both have updatedAt, sort by updatedAt descending
            if (a.updatedAt && b.updatedAt) {
                return b.updatedAt.getTime() - a.updatedAt.getTime();
            }

            // If only one has updatedAt, prioritize the one with updatedAt
            if (a.updatedAt && !b.updatedAt) return -1;
            if (!a.updatedAt && b.updatedAt) return 1;

            // If neither is in recent transactions and neither has updatedAt, maintain original order
            return 0;
        });

        return sortedTransactions;
    };

    async updateCollectionTransactionTenancyEntity(actionDto: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const collectionTransactionTenancyEntity = await this._dataSourceService.resolveService<
                Repository<CollectionTransactionTenancyEntity>
            >(actionDto.metadata.tenantId, PROVIDER_KEYS.COLLECTION_TRANSACTION_TENANCY_REPOSITORY);
            const fieldIds = actionDto.payload.external.transactionFields.map((field) => field.fieldId);
            const collectionTrans = await collectionTransactionTenancyEntity.find({
                where: {
                    dataRegisterTransactionId: actionDto.payload.external.id,
                    fieldId: In(fieldIds),
                },
            });
            const fieldMaps = actionDto.payload.external.transactionFields.map((field) => ({
                [field.fieldId]: field,
            }));
            const collectionTransUpdate = collectionTrans.map((item) => {
                const field = fieldMaps.find((fieldMap) => fieldMap[item.fieldId]);
                if (field) {
                    item.fieldValue = field[item.fieldId].fieldValue;
                    item.fieldOptionIds = field[item.fieldId].fieldOptionIds;
                }
                return item;
            });
            await collectionTransactionTenancyEntity.upsert(collectionTransUpdate, ['id']);
            return { type: 'TriggeredActionSuccess', metadata: {} };
        } catch (error) {
            this._loggerService.error(error);
            throw error;
        }
    }
}
