import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { LoggerService } from '../../../../common/src';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';
import { ActionEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/action-event.enum';
import { TransactionTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { ParsedActionDto } from '../../dtos/action.dto';
import { HandleActionResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

@Injectable()
export class RunLLMActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _logger: LoggerService,
        private readonly _eventDrivenService: EventDrivenService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const transactionId = actionDto?.payload?.context?.transactionId;
        const docField = actionDto?.payload?.configuration?.documentField;
        const formId = actionDto?.payload?.external?.formId;

        if (!transactionId || !docField || !formId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${actionDto.type} Form with id ${formId} is lacking data!`,
            };
        }

        return { type: 'EventValidatedSuccess' };
    }

    public async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const { payload, metadata } = parsedEvent;
            const { context, configuration, external } = payload;
            const { tenantId } = metadata;
            const { transactionId } = context;
            const { formVersionId } = external;
            const { documentField } = configuration;

            const dataSource = await this._getDataSource(tenantId);
            const transactionFieldRepo = dataSource.getRepository(TransactionFieldEntity);
            const formFieldRepo = dataSource.getRepository(FormFieldTenancyEntity);

            const docField = await transactionFieldRepo.findOneBy({
                transactionId: transactionId,
                fieldId: documentField,
            });

            const formField = await formFieldRepo.findOneBy({
                formVersionId: formVersionId,
                fieldId: documentField,
            });

            if (!docField || !formField) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${parsedEvent.type} Document field with id ${documentField} not found in database!`,
                };
            }

            const message = EventDrivenService.createCommonEvent({
                payload: {
                    transactionId: transactionId,
                    transactionFields: [docField],
                    fileType: formField?.configuration?.fileType,
                },

                tenantId: tenantId,
                aggregateId: transactionId,
                type: ActionEventEnum.DOC_PDF_EXTRACTION,
                name: ActionEventEnum.DOC_PDF_EXTRACTION,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_PDF_DOC_EXTRACTION_TOPIC, message);

            return { type: 'TriggeredActionSuccess', metadata: { transactionId: transactionId } };
        } catch (error) {
            this._logger.error(error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }
}
