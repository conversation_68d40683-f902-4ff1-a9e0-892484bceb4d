import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { MqttService } from '../../../../common/src';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';
import { TransactionMQTTTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { ConditionalRequireService } from '../../../form/services/conditional-required.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

@Injectable()
export class ChangeTransactionWFSActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _mqttService: MqttService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(parsedEvent: ParsedActionDto): ValidateEventResult {
        const toStageIdentityId = parsedEvent.payload?.configuration?.toStageIdentityId;
        if (!toStageIdentityId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${event.type} toStageIdentityId not found in configuration!`,
            };
        }

        return { type: 'EventValidatedSuccess' };
    }

    async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const toStageIdentityId = parsedEvent.payload?.configuration?.toStageIdentityId;

            const dataSource = await this._getDataSource(parsedEvent.metadata.tenantId);
            const transactionRepo = dataSource.getRepository(TransactionEntity);
            const stageRepo = dataSource.getRepository(StageTenancyEntity);

            const conditionalRequiredService = await this._dataSourceService.resolveService<ConditionalRequireService>(
                parsedEvent.metadata.tenantId,
                ConditionalRequireService,
                undefined,
                {
                    user: RequestContextService.currentUser() ?? {},
                },
            );

            const transaction = await transactionRepo.findOne({
                where: {
                    id: parsedEvent.contextTransactionId,
                },
                withDeleted: true, // Purpose for Testing Step on Form Builder
            });

            // Purpose for Testing Step on Form Builder
            if (!transaction || (!transaction.isTest && transaction.deletedAt)) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${parsedEvent.type} Transaction with id ${parsedEvent.contextTransactionId} not found in database!`,
                };
            }

            const stage = await stageRepo.findOne({
                where: {
                    identityId: toStageIdentityId,
                    formVersionId: transaction.formVersionId,
                },
            });

            if (!stage) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${parsedEvent.type} Stage with identityId ${toStageIdentityId} not found in database!`,
                };
            }

            const conditionalRequired = await conditionalRequiredService.verify({
                targetStageId: stage.id,
                transactionId: transaction.id,
                isTest: transaction.isTest,
            });

            if (!conditionalRequired.canChangeStage) {
                this._mqttService.publish(
                    `${parsedEvent.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_REQUIRED_TO_CHANGE_STAGE}/${transaction.id}`,
                    JSON.stringify(conditionalRequired),
                );

                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    metadata: {
                        transactionId: transaction.id,
                        required: conditionalRequired,
                    },
                    message: 'Encounter the conditional required fields/relations. No update new stage id for transaction',
                };
            }

            transaction.previousStageId = transaction.stageId;
            transaction.previousStageName = transaction.stageName;

            transaction.stageId = stage.id;
            transaction.stageName = stage.name;

            await transactionRepo.save(transaction);

            return {
                type: 'TriggeredActionSuccess',
                metadata: {
                    transactionId: transaction.id,
                    previousStageId: transaction.stageId,
                    stageId: stage.id,
                },
            };
        } catch (error) {
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            const transactionId = actionResult?.metadata?.transactionId;
            if (!transactionId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Transaction id not found in action result!',
                };
            }

            const previousStageId = actionResult?.metadata?.previousStageId;
            const previousStageName = actionResult?.metadata?.previousStageName;
            if (!previousStageId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Previous stage id not found in action result!',
                };
            }

            const stageId = actionResult?.metadata?.stageId;
            const stageName = actionResult?.metadata?.stageName;
            if (!stageId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Stage id not found in action result!',
                };
            }

            const dataSource = await this._getDataSource(context.event.metadata.tenantId);
            const transactionRepo = dataSource.getRepository(TransactionEntity);

            const transaction = await transactionRepo.findOne({
                where: {
                    id: transactionId,
                },
            });

            transaction.previousStageId = stageId;
            transaction.previousStageName = previousStageName;

            transaction.stageId = previousStageId;
            transaction.stageName = stageName;
            await transactionRepo.save(transaction);

            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            Logger.error(err);
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }
}
