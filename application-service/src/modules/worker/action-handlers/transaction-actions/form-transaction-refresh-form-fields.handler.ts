import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { chunk, uniq } from 'lodash';
import { LoggerService } from 'src/common/src';
import { RequestContextService } from 'src/common/src/application/context/AppRequestContext';
import { ParsedActionDto } from '../../dtos/action.dto';
import { HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';
import { ProcessRefreshTransactionService } from '../../services/process-refresh-transaction.service';
import { RefreshTransactionQueueService } from '../../services/refresh-transaction-queue.service';

@Injectable()
export class FormTransactionRefreshFormFieldsService extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _loggerService: LoggerService,
        private readonly _processRefreshTransactionService: ProcessRefreshTransactionService,
        private readonly _refreshTransactionQueueService: RefreshTransactionQueueService,
    ) {
        super(moduleRef);
    }
    validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        if (!actionDto?.payload) {
            return {
                type: 'EventValidatedFailed',
                message: 'Payload is required',
            };
        }
        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(actionDto: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const isLowPriority = actionDto.payload?.isLowPriority;

            const transactionIds = actionDto.payload?.transactionIds || [];
            if (isLowPriority) {
                await this._processLowPriorityTransaction(actionDto, transactionIds as string[]);
            } else {
                await this._processHighPriorityTransaction(actionDto, transactionIds as string[]);
            }

            return { type: 'TriggeredActionSuccess', metadata: {} };
        } catch (error) {
            this._loggerService.error(error);
            return { type: 'TriggeredActionErrorGoRetry', message: error.message };
        }
    }

    private async _processHighPriorityTransaction(actionDto: ParsedActionDto, transactionIds: string[]) {
        const uniqueTransactionIds = uniq(transactionIds);
        await this._processRefreshTransactionService.doRefresh(
            actionDto,
            uniqueTransactionIds,
            RequestContextService.currentUser(),
            'form_fields',
        );
    }

    private async _processLowPriorityTransaction(actionDto: ParsedActionDto, transactionIds: string[]) {
        const chunkedTransactionIds = chunk(transactionIds, 10);
        for (const transactionIds of chunkedTransactionIds) {
            await this._refreshTransactionQueueService.addTask(
                {
                    actionDto,
                    transactionIds,
                    user: RequestContextService.currentUser(),
                    refreshFor: 'form_fields',
                },
                actionDto.metadata.tenantId,
                'form_fields',
            );
        }
    }

    async rollback(actionDto: ParsedActionDto): Promise<HandleRollbackResult> {
        try {
            // TODO: this logic seems do be wrong, need to log previous data before do update
            // terminate correlation action
            // const key = `${TERMINATE_ACTION_PREFIX}:${actionDto.metadata.tenantId}`;
            // await this._cacheService.sAdd(key, actionDto.metadata.correlationId);

            // // const formTransactionTenancyService = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            // //     actionDto.metadata.tenantId,
            // //     FormTransactionTenancyService,
            // // );

            // const logTenancyService = await this._dataSourceService.resolveService<LogTenancyService>(
            //     actionDto.metadata.tenantId,
            //     LogTenancyService,
            // );

            // // Get the change logs for this correlation ID to find previous values
            // const eventLogs = await logTenancyService.getFieldChangeLogList(actionDto.metadata.correlationId);

            // // Extract previous field values from logs
            // const previousFields = eventLogs
            //     .filter((item) => item.stageId != null)
            //     .map((item) => item.payload.previous)
            //     .flat();

            // // Get current fields that were changed
            // // const currentFields = eventLogs
            // //     .map((item) => item.payload.fields)
            // //     .filter((item) => item.stageId != null)
            // //     .flat();

            // if (previousFields.length > 0) {
            //     const transactionFieldRepo = await this._dataSourceService.resolveService<Repository<TransactionFieldEntity>>(
            //         actionDto.metadata.tenantId,
            //         PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY,
            //     );
            //     // Restore previous field values
            //     await transactionFieldRepo.upsert(previousFields, ['id']);
            // }

            return {
                type: 'RollbackActionSuccess',
                metadata: {},
            };
        } catch (error) {
            this._loggerService.error(error);
            return {
                type: 'RollbackActionError',
                message: error.message,
            };
        }
    }
}
