import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';
import { ActionEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/action-event.enum';
import { CommonTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { AutomationContextType } from '../../../../database/src/shared/enums/automation.enum';
import { ParsedActionDto } from '../../dtos/action.dto';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

@Injectable()
export class PopulateQ88Handler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _eventDrivenService: EventDrivenService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(parsedEvent: ParsedActionDto): ValidateEventResult {
        const transactionId = parsedEvent.contextTransactionId;
        if (!transactionId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${parsedEvent.type} Form with transaction ${transactionId} does not exist!`,
            };
        }

        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const contextType = parsedEvent.contextType;
            const transactionId = parsedEvent.contextTransactionId;
            const accountId = parsedEvent.metadata.tenantId;
            const dataSource = await this._getDataSource(accountId);

            const transactionRepo = dataSource.getRepository(TransactionEntity);

            const transaction = await transactionRepo.findOne({
                where: {
                    id: transactionId,
                },
            });

            if (!transaction) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${parsedEvent.type} Transaction with id ${transactionId} not found in database!`,
                };
            }

            // * Extract imo number
            const { vesselIMO, IMO } = parsedEvent.payload?.configuration || {};

            if (!vesselIMO && !IMO) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No IMO config' };
            }

            const imoNumberFieldId = !!IMO ? IMO : vesselIMO;
            if (!imoNumberFieldId) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No IMO config' };
            }
            let imoField = null;

            if (contextType === AutomationContextType.DataRegister) {
                const dataRegisterFieldRepo = dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity);
                imoField = await dataRegisterFieldRepo.findOne({
                    where: {
                        dataRegisterTransactionId: transactionId,
                        fieldId: imoNumberFieldId,
                    },
                    select: ['id', 'fieldId', 'fieldValue'],
                });
            } else if (contextType === AutomationContextType.FormTransaction) {
                const transactionFieldRepo = dataSource.getRepository(TransactionFieldEntity);
                imoField = await transactionFieldRepo.findOne({
                    where: {
                        transactionId: transactionId,
                        fieldId: imoNumberFieldId,
                    },
                    select: ['id', 'fieldId', 'fieldValue'],
                });
            }

            if (!imoField?.fieldValue) {
                return { type: 'TriggeredActionErrorGoTerminate', message: 'No IMO value' };
            }
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    imo: imoField.fieldValue,
                    accountId: accountId,
                    transactionId: transactionId,
                },
                tenantId: RequestContextService.accountId,
                aggregateId: transactionId,
                type: ActionEventEnum.POPULATE_Q88,
                name: ActionEventEnum.POPULATE_Q88,
            });

            this._eventDrivenService.publishMessage(CommonTopicEnum.MDS_DATA_PIPELINE_TOPIC, message);

            return { type: 'TriggeredActionSuccess' };
        } catch (error) {
            Logger.error(error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            // TODO: rollback
            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            Logger.error(err);
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }
}
