import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ConfigService, LoggerService } from '../../../../common/src';
import { DocPDFSireCrewTenancyEntity } from '../../../../database/src/entities/tenancy/doc-pdf-sire-crew.tenancy.entity';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { FileService } from '../../../../shared/services/file-service.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { HandleActionResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

import { DocPDFSire2ViqTenancyEntity } from 'src/database/src/entities/tenancy/doc-pdf-sire2-viq.tenancy.entity';
import { DocViqDetailTenancyEntity } from 'src/database/src/entities/tenancy/doc-viq-details.tenancy.entity';
import { DocVIQ2DetailEntity } from 'src/database/src/entities/tenancy/doc-viq2-details.tenancy.entity';
import { Readable } from 'stream';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';
import { MqttService } from '../../../../common/src/modules/mqtt/mqtt.service';
import { TransactionMQTTTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { DocCMIDTenancyEntity } from '../../../../database/src/entities/tenancy/doc-cmid.tenancy.entity';
import { DocCrewDetailTenancyEntity } from '../../../../database/src/entities/tenancy/doc-crew-details.tenancy.entity';
import { DocCrewTenancyEntity } from '../../../../database/src/entities/tenancy/doc-crew.tenancy.entity';
import { DocPDFOvidViqTenancyEntity } from '../../../../database/src/entities/tenancy/doc-pdf-ovid-viq.tenancy.entity';
import { DocPDFQ88TenancyEntity } from '../../../../database/src/entities/tenancy/doc-pdf-q88.tenancy.entity';
import { DocQ88BallastTankTenancyEntity } from '../../../../database/src/entities/tenancy/doc-q88-ballast-tanks.tenancy.entity';
import { DocQ88CargoTankTenancyEntity } from '../../../../database/src/entities/tenancy/doc-q88-cargo-tanks.tenancy.entity';
import { DocQ88CertTenancyEntity } from '../../../../database/src/entities/tenancy/doc-q88-certs.tenancy.entity';
import { DocQ88TenancyEntity } from '../../../../database/src/entities/tenancy/doc-q88.tenancy.entity';
import { DocViqTenancyEntity } from '../../../../database/src/entities/tenancy/doc-viq.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { FileTypeEnum } from '../../../../shared/enums/file-type.enum';
import { FormTransactionDataService } from '../../../form/services/data/form-transaction.data.service';
import { DocCmidDataService } from '../../services';
import { DocCrewDataService } from '../../services/doc-crew.data.service';
import { DocOvidViqDataService } from '../../services/doc-ovid-viq.service';
import { DocQ88DataService } from '../../services/doc-q88.service';
import { DocSire2ViqDataService } from '../../services/doc-sire2-viq.service';
import { PdfExtractionService } from '../../services/pdf-extraction/pdf-extraction.data.service';

@Injectable()
export class DocPdfExtractionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _logger: LoggerService,
        private readonly _fileService: FileService,
        private readonly _pdfService: PdfExtractionService,
        private readonly _docCrewDataService: DocCrewDataService,
        private readonly _dataSourceService: DataSourceService,
        private readonly _configService: ConfigService,
        private readonly _mqttService: MqttService,
        private readonly _docOvidViqDataService: DocOvidViqDataService,
        private readonly _docSire2ViqDataService: DocSire2ViqDataService,
        private readonly _docQ88DataService: DocQ88DataService,
        private readonly _docCmidDataService: DocCmidDataService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const transactionId = actionDto?.payload?.transactionId;
        const transactionFields = actionDto?.payload?.transactionFields;
        const formId = actionDto?.payload?.formId;
        const docField = transactionFields?.find((item) => item.fieldType === FormFieldTypeEnum.Document);
        if (!transactionId || !docField?.fieldValue || !docField?.data?.filePath) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${actionDto.type} Form with id ${formId} is lacking data!`,
            };
        }

        const regex = /\.pdf$/i;
        if (!regex.test(docField.data.filePath)) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${actionDto.type} Document is not a PDF file!`,
            };
        }

        return { type: 'EventValidatedSuccess' };
    }

    public async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const { payload, metadata } = parsedEvent;
            const { transactionFields, transactionId, fileType } = payload;
            const docField = transactionFields.find((item) => item.fieldType === FormFieldTypeEnum.Document);
            const { data } = docField;
            const fileContent = await this._fileService.downloadFile(data.filePath);
            const content = fileContent.content;
            const blob = await this.readableToBlob(content);

            const pdfData = await this._pdfService.extractFromBlob(blob, fileType, this._configService.llm?.provider ?? 'OpenAI');

            if (!pdfData?.objectData) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: 'No data extracted from the PDF file!',
                };
            }
            const dataSource = await this._getDataSource(metadata?.tenantId);

            let result: any;
            if (fileType == FileTypeEnum.SIRE_CREW) {
                const docPdfSireCrewRepo = dataSource.getRepository(DocPDFSireCrewTenancyEntity);
                const docCrewRepo = dataSource.getRepository(DocCrewTenancyEntity);
                const docCrewDetailRepo = dataSource.getRepository(DocCrewDetailTenancyEntity);

                result = await this._docCrewDataService.save({
                    transactionId: transactionId,
                    dto: pdfData,
                    docPdfSireCrewRepo: docPdfSireCrewRepo,
                    docCrewRepo: docCrewRepo,
                    docCrewDetailRepo: docCrewDetailRepo,
                });
            } else if (fileType == FileTypeEnum.OVID_VIQ) {
                const docPdfOvidViqRepo = dataSource.getRepository(DocPDFOvidViqTenancyEntity);
                const docViqRepo = dataSource.getRepository(DocViqTenancyEntity);
                const docViqDetailRepo = dataSource.getRepository(DocViqDetailTenancyEntity);

                result = await this._docOvidViqDataService.save({
                    transactionId: transactionId,
                    dto: pdfData,
                    docPdfOvidViqRepo: docPdfOvidViqRepo,
                    docViqRepo: docViqRepo,
                    docViqDetailRepo: docViqDetailRepo,
                });
            } else if (fileType == FileTypeEnum.SIRE2_VIQ) {
                const docPdfSire2ViqRepo = dataSource.getRepository(DocPDFSire2ViqTenancyEntity);
                const docViqRepo = dataSource.getRepository(DocViqTenancyEntity);
                const docViq2DetailRepo = dataSource.getRepository(DocVIQ2DetailEntity);

                result = await this._docSire2ViqDataService.save({
                    transactionId: transactionId,
                    dto: pdfData,
                    docPdfSire2ViqRepo: docPdfSire2ViqRepo,
                    docViqRepo: docViqRepo,
                    docViq2DetailRepo: docViq2DetailRepo,
                });
            } else if (fileType == FileTypeEnum.Q88) {
                const docPdfQ88Repo = dataSource.getRepository(DocPDFQ88TenancyEntity);
                const docQ88Repo = dataSource.getRepository(DocQ88TenancyEntity);
                const docQ88CertRepo = dataSource.getRepository(DocQ88CertTenancyEntity);
                const docQ88CargoTankRepo = dataSource.getRepository(DocQ88CargoTankTenancyEntity);
                const docQ88BallastTankRepo = dataSource.getRepository(DocQ88BallastTankTenancyEntity);

                result = await this._docQ88DataService.save({
                    transactionId: transactionId,
                    dto: pdfData,
                    docPdfQ88Repo: docPdfQ88Repo,
                    docQ88Repo: docQ88Repo,
                    docQ88CertRepo: docQ88CertRepo,
                    docQ88CargoTankRepo: docQ88CargoTankRepo,
                    docQ88BallastTankRepo: docQ88BallastTankRepo,
                });
            } else if (fileType == FileTypeEnum.CMID) {
                const docPdfCmidRepo = dataSource.getRepository(DocCMIDTenancyEntity);

                result = await this._docCmidDataService.save({
                    transactionId: transactionId,
                    dto: pdfData,
                    docPdfCmidRepo,
                });
            }

            parsedEvent.payload = { ...parsedEvent.payload, status: 'extraction_completed' };
            this._mqttService
                .publish(
                    `${metadata?.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_DOC_PDF_EXTRACTION}/${transactionId}`,
                    JSON.stringify(parsedEvent),
                )
                .catch((error) => {
                    console.error(`Failed to publish MQTT message: ${error}`);
                });

            if (!result) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: 'Failed to save extracted data!',
                };
            } else {
                const transactionRepo = dataSource.getRepository(TransactionEntity);
                const transaction = await transactionRepo.findOne({
                    where: {
                        id: transactionId,
                    },
                    withDeleted: true,
                });

                if (!transaction || (!transaction.isTest && transaction.deletedAt)) {
                    return {
                        type: 'TriggeredActionErrorGoTerminate',
                        message: 'Transaction not found!',
                    };
                }

                const service = await this._dataSourceService.resolveService<FormTransactionDataService>(
                    metadata?.tenantId,
                    FormTransactionDataService,
                );

                await service.update({
                    id: transactionId,
                    request: {
                        transactionId: transactionId,
                        formId: transaction.formId,
                        transactionFields: [],
                        formValues: {},
                        isTest: transaction.isTest,
                    },
                    option: {
                        forceRunAutoPopulate: true,
                        shouldRunPopulateFormFields: true,
                    },
                    user: RequestContextService.currentUser(),
                });

                return { type: 'TriggeredActionSuccess', metadata: { transactionId: transactionId } };
            }
        } catch (error) {
            this._logger.error(error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }

    private async readableToBlob(readable: Readable, mimeType: string = 'application/octet-stream'): Promise<Blob> {
        const chunks: Uint8Array[] = [];
        for await (const chunk of readable) {
            chunks.push(chunk as Uint8Array);
        }
        return new Blob(chunks, { type: mimeType });
    }
}
