import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { ParsedActionDto } from '../../dtos/action.dto';
import { TransactionCreationService } from '../../services/transaction-creation.service';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

@Injectable()
export class CreateTransactionActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _transCreationService: TransactionCreationService,
    ) {
        super(moduleRef);
    }

    validateRequiredActionParams(parsedEvent: ParsedActionDto): ValidateEventResult {
        const formId = parsedEvent.payload?.configuration?.formId;
        if (!formId) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${parsedEvent.type} Form with id ${formId} not found in configuration!`,
            };
        }

        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const formId = parsedEvent.payload?.configuration?.formId;
            const currentTransactionId = parsedEvent.payload?.context?.transactionId;

            const dataSource = await this._getDataSource(parsedEvent.metadata.tenantId);

            if (currentTransactionId) {
                const transRepo = dataSource.getRepository(TransactionEntity);
                const transaction = await transRepo.findOne({
                    where: {
                        id: currentTransactionId,
                    },
                });

                if (!transaction)
                    return {
                        type: 'TriggeredActionErrorGoTerminate',
                        message: `[ACTION] ${parsedEvent.type} Transaction with id ${currentTransactionId} not found!`,
                    };
            }

            const formRepo = dataSource.getRepository(FormTenancyEntity);
            const form = await formRepo.findOne({
                where: {
                    id: formId,
                },
            });
            const formFieldRepo = dataSource.getRepository(FormFieldTenancyEntity);

            if (!form) {
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${parsedEvent.type} Form with id ${formId} not found in database!`,
                };
            }

            const formVersionId = form.activeVersionId;
            const formFields = await formFieldRepo.findBy({
                formVersionId,
            });

            const transaction = await this._transCreationService.createEmptyTransaction({
                formId,
                formVersionId,
                timezone: '',
                accountId: parsedEvent.metadata.tenantId,

                // because we call update after so can use skip options here
                skipPopulate: true,
                skipValidation: true,
                skipRollup: true,
            });

            await this._transCreationService.updateTransactionAfterCreate({ transaction, parsedEvent, formFields });

            return { type: 'TriggeredActionSuccess', metadata: { transactionId: transaction.id } };
        } catch (error) {
            Logger.error(error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            const transactionId = actionResult?.metadata?.transactionId;

            if (!transactionId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Transaction id not found in action result!',
                };
            }

            const dataSource = await this._getDataSource(context.event.metadata.tenantId);
            const transRepo = dataSource.getRepository(TransactionEntity);

            await transRepo.softDelete({
                id: transactionId,
            });

            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            Logger.error(err);
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }
}
