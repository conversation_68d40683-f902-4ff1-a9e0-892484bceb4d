import { Injectable } from '@nestjs/common';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { FormTransactionDataService } from 'src/modules/form/services/data/form-transaction.data.service';
import { RequestContextService } from 'src/common/src/application/context/AppRequestContext';
import { AbstractActionHandler } from '../common/action.abstraction';
import { ParsedActionDto } from '../../dtos/action.dto';
import { ValidateEventResult, HandleActionResult, HandleRollbackResult } from '../common/action-types';
import { ModuleRef } from '@nestjs/core';
import { Repository } from 'typeorm';
import { PROVIDER_KEYS } from 'src/database/src/constants/providers';
import { LogTenancyService } from 'src/modules/log/services/log.tenancy.service';
import { TransactionFieldEntity } from 'src/database/src/entities/tenancy/transaction-field.tenancy.entity';
import { CacheService, LoggerService } from 'src/common/src';
import { TERMINATE_ACTION_PREFIX } from 'src/common/src/constant/cache';
import { chunk, uniq } from 'lodash';
import { RefreshTransactionQueueService } from '../../services/refresh-transaction-queue.service';
import { ProcessRefreshTransactionService } from '../../services/process-refresh-transaction.service';
import { FormTransactionTenancyService } from '../../../form/services/form-transaction.tenancy.service';

@Injectable()
export class FormTransactionRefreshCollectionFieldsService extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _loggerService: LoggerService,
        private readonly _cacheService: CacheService,
        private readonly _processRefreshTransactionService: ProcessRefreshTransactionService,
        private readonly _refreshTransactionQueueService: RefreshTransactionQueueService,
    ) {
        super(moduleRef);
    }
    validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        if (!actionDto?.payload) {
            return {
                type: 'EventValidatedFailed',
                message: 'Payload is required',
            };
        }

        return {
            type: 'EventValidatedSuccess',
        };
    }

    async handleAction(actionDto: ParsedActionDto): Promise<HandleActionResult> {
        try {
            const isLowPriority = actionDto.payload.isLowPriority;

            const transactionIds = actionDto.payload.transactionIds as string[];
            if (!transactionIds?.length) {
                return { type: 'TriggeredActionSuccess', metadata: {} };
            }
            const uniqueTransactionIds = uniq(transactionIds);

            if (isLowPriority) {
                await this._processLowPriorityTransaction(actionDto, uniqueTransactionIds);
            } else {
                await this._processHighPriorityTransaction(actionDto, uniqueTransactionIds);
            }

            return { type: 'TriggeredActionSuccess', metadata: {} };
        } catch (error) {
            this._loggerService.error(error);
            return { type: 'TriggeredActionErrorGoTerminate', message: error.message };
        }
    }

    private async _processHighPriorityTransaction(actionDto: ParsedActionDto, transactionIds: string[]) {
        const uniqueTransactionIds = uniq(transactionIds);
        await this._processRefreshTransactionService.doRefresh(
            actionDto,
            uniqueTransactionIds,
            RequestContextService.currentUser(),
            'collection_fields',
        );
    }

    private async _processLowPriorityTransaction(actionDto: ParsedActionDto, transactionIds: string[]) {
        const chunkedTransactionIds = chunk(transactionIds, 10);
        for (const transactionIds of chunkedTransactionIds) {
            await this._refreshTransactionQueueService.addTask(
                {
                    actionDto,
                    transactionIds,
                    user: RequestContextService.currentUser(),
                    refreshFor: 'collection_fields',
                },
                actionDto.metadata.tenantId,
                'collection_fields',
            );
        }
    }

    private async _processTransaction(actionDto: ParsedActionDto, transactionId: string) {
        const formTransactionDataService = await this._dataSourceService.resolveService<FormTransactionDataService>(
            actionDto.metadata.tenantId,
            FormTransactionDataService,
        );
        const transaction = await formTransactionDataService.getById(transactionId);

        const targetFieldChangeIds = actionDto?.payload?.external?.fieldChanged?.length
            ? actionDto.payload.external.fieldChanged.map((item) => item.fieldId)
            : [];
        const targetFieldChangeFromRegisterId = actionDto?.payload?.external?.dataRegisterId;

        await formTransactionDataService.update({
            id: transaction.id,
            request: {
                transactionId: transaction.id,
                formId: transaction.formId,
                transactionFields: [],
            },
            option: {
                shouldRunPopulateFormFields: true,
                forceRunAutoPopulate: true,
                forceUsingTransactionFieldValues: true,
                onlyUpdateIfRelevantRegisterRecordIds: [actionDto.payload.external.id],
                targetFieldChangeIds: targetFieldChangeIds,
                targetFieldChangeFromRegisterId,
            },
            user: RequestContextService.currentUser(),
        });
    }

    async rollback(actionDto: ParsedActionDto): Promise<HandleRollbackResult> {
        try {
            // terminate correlation action
            const key = `${TERMINATE_ACTION_PREFIX}:${actionDto.metadata.tenantId}`;
            await this._cacheService.sAdd(key, actionDto.metadata.correlationId);

            // const formTransactionTenancyService = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            //     actionDto.metadata.tenantId,
            //     FormTransactionTenancyService,
            // );

            const logTenancyService = await this._dataSourceService.resolveService<LogTenancyService>(
                actionDto.metadata.tenantId,
                LogTenancyService,
            );

            // Get the change logs for this correlation ID to find previous values
            const eventLogs = await logTenancyService.getFieldChangeLogList(actionDto.metadata.correlationId);

            // Extract previous field values from logs
            const previousFields = eventLogs
                .filter((item) => item.stageId != null)
                .map((item) => item.payload.previous)
                .flat();

            // Get current fields that were changed
            // const currentFields = eventLogs
            //     .map((item) => item.payload.fields)
            //     .filter((item) => item.stageId != null)
            //     .flat();

            if (previousFields.length > 0) {
                const transactionFieldRepo = await this._dataSourceService.resolveService<Repository<TransactionFieldEntity>>(
                    actionDto.metadata.tenantId,
                    PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY,
                );
                // Restore previous field values
                await transactionFieldRepo.upsert(previousFields, ['id']);
            }

            return {
                type: 'RollbackActionSuccess',
                metadata: {},
            };
        } catch (error) {
            this._loggerService.error(error);
            return {
                type: 'RollbackActionError',
                message: error.message,
            };
        }
    }
}
