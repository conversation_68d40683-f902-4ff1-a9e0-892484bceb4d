import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import type { JsonTree } from '@react-awesome-query-builder/core';
import { CoreConfig, Utils as QbUtils } from '@react-awesome-query-builder/core';
import { apply } from 'json-logic-js';
import { isEmpty, uniqBy } from 'lodash';
import { DataRegisterTransactionFieldOverrideEntity } from 'src/database/src/entities/public/data-register-transaction-field-override.public.entity';
import { DataRegisterTransactionFieldStyleEntity } from 'src/database/src/entities/public/data-register-transaction-field-style.public.entity';
import { DataRegisterTransactionFieldOverrideTenancyEntity } from 'src/database/src/entities/tenancy/data-register-transaction-field-override.tenancy.entity';
import { DataRegisterTransactionFieldStyleTenancyEntity } from 'src/database/src/entities/tenancy/data-register-transaction-field-style.tenancy.entity';
import { TransactionFieldOverrideEntity } from 'src/database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { TransactionFieldStyleEntity } from 'src/database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { OverrideStatusEnum } from 'src/database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from 'src/database/src/shared/enums/override-type.enum';
import { DataSource } from 'typeorm';
import { validate } from 'uuid';
import { DATA_REGISTER_CODE_FIELD_ID } from '../../../../constant';
import { SELECTABLE_FIELD_TYPES } from '../../../../database/src/constants/field';
import { DataRegisterTransactionFieldEntity } from '../../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../../database/src/entities/public/data-registers.public.entity';
import { FormVersionEntity } from '../../../../database/src/entities/public/form-version.public.entity';
import { AutomationActionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { AutomationContextType } from '../../../../database/src/shared/enums/automation.enum';
import { SourceOfChangeType } from '../../../../database/src/shared/enums/change-log.type.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { DatePickerTypeEnum } from '../../../../shared/enums/date-picker.enum';
import { getFieldValue, getFieldValueBasedOnPicker } from '../../../../utils/format-data';
import {
    EditDataRegisterTransactionFieldRequest,
    EditDataRegisterTransactionRequest,
    StyleAndOverrideFields,
} from '../../../data-register/dtos/requests/create-data-register-transaction.request';
import { DataRegisterTransactionService } from '../../../data-register/services/data-register-transaction.service';
import { DataRegisterTransactionTenancyService } from '../../../data-register/services/data-register-transaction.tenancy.service';
import { DataRegisterTransactionIdentifierDataService } from '../../../data-register/services/data/data-register-transaction-identifier.data.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { CreateUpdateRegisterDto } from '../../dtos/register-action.dto';
import { RegisterActionService } from '../../services/register-action.service';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

@Injectable()
export class CreateOrUpdateRegisterActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _registerActionService: RegisterActionService,
        private readonly _identifierService: DataRegisterTransactionIdentifierDataService,
    ) {
        super(moduleRef);
    }

    private _handleStyleAndOverride(
        styleAndOverrideFields: StyleAndOverrideFields,
        transactionField: DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity | TransactionFieldEntity,
        includeValidationValue: boolean,
        fieldId: string,
        value: any,
    ) {
        if (transactionField && includeValidationValue) {
            const override = transactionField.transactionFieldOverrides.find(
                (o) => o.type === OverrideTypeEnum.User && o.status === OverrideStatusEnum.Active,
            );

            styleAndOverrideFields[fieldId] = {
                validationValue: transactionField.validationValue,
            };

            if (override) {
                styleAndOverrideFields[fieldId].override = {
                    overrideValue: override.validationValue,
                    createdBy: override.createdBy,
                    createdByUser: override.createdByUser,
                    fromValue: override.fromValue,
                    validationValue: override.validationValue,
                    comment: override.comment,
                    dependencyValues: {
                        [fieldId]: validate(value) ? [value] : value,
                    },
                    fieldId: fieldId,
                };
            }

            if (transactionField.transactionFieldStyle) {
                styleAndOverrideFields[fieldId].style = {
                    fieldId: fieldId,
                    style: transactionField.transactionFieldStyle.configuration,
                };
            }
        }
    }

    public validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const actionId = actionDto?.payload?.actionId;
        const contextTransactionId = actionDto?.contextTransactionId;
        const contextType = actionDto?.contextType;
        const payload = actionDto?.payload;

        const actionConfig = payload?.configuration;

        const headerMessage = `[Correlation:${actionDto?.metadata?.correlationId}] - [ACTION:${actionDto?.metadata.name}]: `;

        if (!actionId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} ${actionDto.type} Action Id is not existed!`,
            };
        }

        if (!contextTransactionId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} ${actionDto.type} Context Transaction Id is not existed!`,
            };
        }

        if (!contextType || ![AutomationContextType.DataRegister, AutomationContextType.FormTransaction].includes(contextType)) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} ${actionDto.type} Context Type ${contextType} is not supported!`,
            };
        }

        if (!actionConfig?.dataRegisterId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} ${actionDto.type} Data Register Id is not exited!`,
            };
        }

        // if (!actionConfig?.associatedId) {
        //     return {
        //         type: 'EventValidatedFailed',
        //         message: `${headerMessage} ${actionDto.type} Missing associated field or value!`,
        //     };
        // }

        return { type: 'EventValidatedSuccess' };
    }
    public async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        const { metadata, payload, contextType, contextTransactionId } = parsedEvent ?? {};
        const { configuration } = payload ?? {};
        const {
            dataRegisterId,
            updateFields: updateFieldsRaw,
            associatedId,
            mappingFields,
        } = (configuration ?? {}) as CreateUpdateRegisterDto;

        const updateFields = uniqBy(updateFieldsRaw ?? [], 'fieldId');

        const headerMessage = `[Correlation:${metadata.correlationId}] - [ACTION:${metadata.name}]: `;

        const accountId = metadata?.tenantId;

        const dataSource = await this._dataSourceService.createAccountDataSource(accountId);

        const {
            dataRegisterRepo,
            dataRegisterVersionRepo,
            transactionRepo,
            registerTransactionFieldRepo,
            registerTransactionRepo,
            formVersionRepo,
        } = this._getRepositories(dataSource, !!accountId);

        if (isEmpty(mappingFields)) {
            return this._returnSuccessWithNoAction(`${headerMessage} No context mappings found`);
        }

        // in order to create empty transaction register record without mapping fields
        // if (isEmpty(updateFields)) {
        //     return this._returnSuccessWithNoAction(`${headerMessage} No update fields found`);
        // }

        const dataRegister = await dataRegisterRepo.findOneBy({
            id: dataRegisterId,
        });

        if (!dataRegister) {
            return this._returnSuccessWithNoAction(`${headerMessage} No data register found`);
        }

        if (!dataRegister.activeVersionId) {
            return this._returnSuccessWithNoAction(`${headerMessage} No data register active version found`);
        }

        const dataRegisterVersion = await dataRegisterVersionRepo.findOne({
            where: {
                id: dataRegister.activeVersionId,
            },
            relations: ['fields'],
        });

        let contexts: string[] = [];

        if (associatedId) {
            if (!dataRegisterVersion || !dataRegisterVersion.associateField) {
                return this._returnSuccessWithNoAction(`${headerMessage} No identifier config found`);
            }

            const identifierConfigs = dataRegisterVersion.config?.identifier;

            if (!identifierConfigs || isEmpty(identifierConfigs)) {
                return this._returnSuccessWithNoAction(`${headerMessage} No identifier Config found`);
            }

            const identifierConfig = identifierConfigs.find((config) => config?.condition?.includes(associatedId));

            if (!identifierConfig || isEmpty(identifierConfig)) {
                return this._returnSuccessWithNoAction(`${headerMessage} No identifier Config found`);
            }

            contexts = identifierConfig.contexts;
        } else {
            // Need to convert mappingFields
            contexts = mappingFields.map((m) => m.toFieldId);
        }

        let sourceData: DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity | TransactionEntity;

        let sourceBuilder: DataRegisterVersionEntity | DataRegisterVersionTenancyEntity | FormVersionEntity | FormVersionTenancyEntity;

        if (contextType === AutomationContextType.DataRegister) {
            sourceData = await registerTransactionRepo.findOne({
                where: {
                    id: contextTransactionId,
                },
                relations: {
                    transactionFields: {
                        transactionFieldOverrides: true,
                        transactionFieldStyle: true,
                    },
                },
            });

            const dr = await dataRegisterRepo.findOneBy({
                id: sourceData.dataRegisterId,
            });

            if (!dr?.activeVersionId) {
                return this._returnSuccessWithNoAction(`${headerMessage} No data register active version found`);
            }

            sourceBuilder = await dataRegisterVersionRepo.findOne({
                where: {
                    id: dr?.activeVersionId,
                },
                relations: ['fields'],
            });
        } else {
            sourceData = await transactionRepo.findOne({
                where: {
                    id: contextTransactionId,
                },
                relations: {
                    transactionFields: {
                        transactionFieldStyle: true,
                        transactionFieldOverrides: true,
                    },
                },
            });

            if (sourceData) {
                sourceBuilder = await formVersionRepo.findOne({
                    where: {
                        id: sourceData.formVersionId,
                    },
                    relations: ['fields'],
                });
            }
        }

        if (!sourceData) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] error: No transaction found`,
            };
        }

        if (!contexts || isEmpty(contexts)) {
            return this._returnSuccessWithNoAction(`${headerMessage} No contexts Config found`);
        }

        const associateField = dataRegisterVersion.associateField;

        // Step 1: Build context maps
        const contextMaps: Record<string, any> = {};

        if (associateField) {
            contextMaps[associateField] = associatedId;
        }

        const sourceDataFields = sourceData.transactionFields ?? [];

        for (const context of contexts) {
            const contextMapping = mappingFields.find((m) => m.toFieldId === context);
            if (!contextMapping || !contextMapping?.fromFieldId) {
                return this._returnSuccessWithNoAction(`${headerMessage} Missing context mapping for ${context}`);
            }

            const builderField = sourceBuilder.fields.find((f) => f.fieldId === contextMapping.fromFieldId);
            const sourceField = sourceDataFields.find((f) => f.fieldId === contextMapping.fromFieldId);
            const fieldValue = SELECTABLE_FIELD_TYPES.includes(builderField?.type)
                ? sourceField.fieldOptionIds?.[0]
                : sourceField.fieldValue;

            if (!fieldValue) {
                return this._returnSuccessWithNoAction(`${headerMessage} Missing context mapping value for ${context}`);
            }

            contextMaps[context] = fieldValue;
        }

        try {
            // Step 2: Get record by identifier
            const records = await this._identifierService.getRecordByIdentifier({
                contextMaps: contextMaps,
                dataRegisterTransactionFieldRepo: registerTransactionFieldRepo,
                dataRegisterTransactionRepo: registerTransactionRepo,
                dataRegisterVersion,
            });

            let service: DataRegisterTransactionTenancyService | DataRegisterTransactionService;
            if (!accountId) {
                service = await this._dataSourceService.resolveService<DataRegisterTransactionService>('', DataRegisterTransactionService);
            } else {
                service = await this._dataSourceService.resolveService<DataRegisterTransactionTenancyService>(
                    accountId,
                    DataRegisterTransactionTenancyService,
                );
            }

            const actionResult: HandleActionResult = {
                type: 'TriggeredActionSuccess',
                metadata: {
                    transactionId: null,
                    transactionFields: [],
                    isCreate: false,
                    message: headerMessage,
                },
            };

            // Step 3: Create or update record
            if (!records?.length) {
                actionResult.metadata.isCreate = true;

                const result = await this._createRegisterRecord({
                    associatedId,
                    associateField,
                    contextMaps,
                    dataRegisterId,
                    dataRegisterVersion,
                    service,
                    updateFields: updateFields ?? [],
                    sourceDataFields,
                });

                if (typeof result === 'object') {
                    return result as HandleActionResult;
                }
                actionResult.metadata.transactionId = result;
            } else {
                const record = records[0];
                actionResult.metadata.transactionId = record.id;

                const recordFields = await registerTransactionFieldRepo.findBy({
                    dataRegisterTransactionId: record.id,
                });

                const updateTransactionFields: EditDataRegisterTransactionFieldRequest[] = [];

                const styleAndOverrideFields: StyleAndOverrideFields = {};

                updateFields?.forEach((updateField) => {
                    const {
                        fieldId,
                        fieldValue,
                        pickerType,
                        defaultValue,
                        fieldValueSource,
                        typeName,
                        filterName,
                        includeValidationValue,
                    } = updateField;
                    const builderField = dataRegisterVersion.fields.find((f) => f.fieldId === fieldId);
                    if (!builderField) {
                        return;
                    }

                    let value = fieldValue;

                    if (fieldValueSource === 'field') {
                        switch (typeName) {
                            case 'collection_item':
                                {
                                    const collectionFields = (
                                        (sourceDataFields as TransactionFieldEntity[])?.filter(
                                            (sdf) => sdf.contextType === TransactionFieldContextTypeEnum.COLLECTION,
                                        ) ?? []
                                    ).sort((a, b) => (a.data?.externalOrder ?? 0) - (b.data?.externalOrder ?? 0));

                                    const [collectionItemIdentityId, fieldIdFromCollection] = fieldValue.split('--');

                                    if (!filterName?.children1?.length) {
                                        const transactionCollectionFields = collectionFields.filter(
                                            (tf) => tf.collectionItemId == collectionItemIdentityId && tf.fieldId === fieldIdFromCollection,
                                        )?.[0];

                                        value = getFieldValue(builderField, transactionCollectionFields);
                                        this._handleStyleAndOverride(
                                            styleAndOverrideFields,
                                            transactionCollectionFields,
                                            includeValidationValue,
                                            fieldId,
                                            value,
                                        );
                                    } else {
                                        const collectionFieldsQuery = collectionFields.map((cf) => {
                                            return {
                                                id: cf.id,
                                                [`${cf.collectionItemId}--${cf.fieldId}`]: cf.fieldOptionIds?.length
                                                    ? cf.fieldOptionIds
                                                    : cf.fieldValue,
                                                rowKey: cf.rowKey,
                                            };
                                        });

                                        const jsonLogic = QbUtils.jsonLogicFormat(QbUtils.loadTree(filterName as JsonTree), CoreConfig);

                                        const rowKey = collectionFieldsQuery.filter((item) => apply(jsonLogic?.logic, item))?.[0]?.rowKey;

                                        if (!rowKey) {
                                            value = null;
                                        } else {
                                            const collectionValue = collectionFields.find(
                                                (cf) => cf.rowKey === rowKey && cf.fieldId === fieldIdFromCollection,
                                            );

                                            value = getFieldValue(builderField, collectionValue);
                                            this._handleStyleAndOverride(
                                                styleAndOverrideFields,
                                                collectionValue,
                                                includeValidationValue,
                                                fieldId,
                                                value,
                                            );
                                        }
                                    }
                                }
                                break;
                            // TODO: keep typeName = form for tracking logic, the thing is we should treat mapping field in same for mapping not a collection
                            case 'form':
                            default:
                                {
                                    const transactionField = sourceDataFields.find((f) => f.fieldId === fieldValue);
                                    value = getFieldValue(builderField, transactionField);

                                    if (
                                        builderField?.type === FormFieldTypeEnum.Rollup &&
                                        builderField?.configuration?.rollup?.dataType === 'validationResult'
                                    ) {
                                        styleAndOverrideFields[fieldId] = {
                                            validationValue: value,
                                            style: {
                                                fieldId,
                                                style: transactionField.transactionFieldStyle.configuration,
                                            },
                                        };
                                    }

                                    this._handleStyleAndOverride(
                                        styleAndOverrideFields,
                                        transactionField,
                                        includeValidationValue,
                                        fieldId,
                                        value,
                                    );
                                }
                                break;
                        }
                    } else {
                        value = getFieldValueBasedOnPicker({
                            pickerType,
                            value: defaultValue,
                            fieldValue: fieldValue,
                            fieldType: builderField.type,
                        });
                    }

                    if (!value) {
                        return;
                    }

                    const field = recordFields.find((f) => f.fieldId === fieldId);

                    if (field) {
                        updateTransactionFields.push({
                            id: field?.id,
                            fieldId,
                            fieldValue: value,
                            type: builderField.type,
                        });

                        actionResult.metadata.transactionFields.push({
                            currentValue: field.fieldValue,
                            newValue: value,
                            fieldId: fieldId,
                            fieldType: builderField.type,
                        });
                    }
                });

                const payload: EditDataRegisterTransactionRequest = {
                    transactionFields: updateTransactionFields,
                    dataRegisterId,
                    styleAndOverrideFields,
                };
                await service.update(record.id, payload, SourceOfChangeType.AUTOMATION);
            }

            return actionResult;
        } catch (error) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] error ${error}`,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            const transactionId = actionResult?.metadata?.transactionId;
            const isCreate = actionResult?.metadata?.isCreate;

            if (!transactionId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Transaction id not found in action result!',
                };
            }

            const dataSource = await this._getDataSource(context.event.metadata.tenantId);
            const transRepo = context.event.metadata.tenantId
                ? dataSource.getRepository(DataRegisterTransactionTenancyEntity)
                : dataSource.getRepository(DataRegisterTransactionEntity);

            if (isCreate) {
                await transRepo.softDelete({
                    id: transactionId,
                });
            } else {
                await this._registerActionService.rollbackUpdateTransaction(context, actionResult);
            }

            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }

    private _getRepositories(dataSource: DataSource, isAccount: boolean) {
        const automationActionRepo = isAccount
            ? dataSource.getRepository(AutomationActionTenancyEntity)
            : dataSource.getRepository(AutomationActionTenancyEntity);
        const dataRegisterRepo = isAccount
            ? dataSource.getRepository(DataRegisterTenancyEntity)
            : dataSource.getRepository(DataRegisterEntity);

        const dataRegisterVersionRepo = isAccount
            ? dataSource.getRepository(DataRegisterVersionTenancyEntity)
            : dataSource.getRepository(DataRegisterVersionEntity);

        const registerTransactionRepo = isAccount
            ? dataSource.getRepository(DataRegisterTransactionTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionEntity);

        const registerTransactionFieldRepo = isAccount
            ? dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionFieldEntity);

        const formVersionRepo = isAccount
            ? dataSource.getRepository(FormVersionTenancyEntity)
            : dataSource.getRepository(FormVersionEntity);

        const registerFieldOverrideRepo = isAccount
            ? dataSource.getRepository(DataRegisterTransactionFieldOverrideTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionFieldOverrideEntity);

        const registerFieldStyleRepo = isAccount
            ? dataSource.getRepository(DataRegisterTransactionFieldStyleTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionFieldStyleEntity);

        const transactionRepo = dataSource.getRepository(TransactionEntity);

        const transactionFieldOverrideRepo = dataSource.getRepository(TransactionFieldOverrideEntity);

        const transactionFieldStyleRepo = dataSource.getRepository(TransactionFieldStyleEntity);

        return {
            automationActionRepo,
            dataRegisterRepo,
            dataRegisterVersionRepo,
            registerTransactionRepo,
            transactionRepo,
            registerTransactionFieldRepo,
            formVersionRepo,
            registerFieldOverrideRepo,
            transactionFieldOverrideRepo,
            registerFieldStyleRepo,
            transactionFieldStyleRepo,
        };
    }

    private _returnSuccessWithNoAction(reason: string): HandleActionResult {
        return {
            type: 'TriggeredActionSuccess',
            metadata: {
                message: reason,
            },
        };
    }

    private async _createRegisterRecord({
        associateField,
        associatedId,
        contextMaps,
        dataRegisterId,
        dataRegisterVersion,
        updateFields,
        service,
        sourceDataFields,
    }: {
        dataRegisterId: string;
        dataRegisterVersion: DataRegisterVersionEntity | DataRegisterVersionTenancyEntity;
        associateField?: string;
        associatedId?: string;
        updateFields: Array<{
            fieldValueSource?: 'field' | 'value' | null;
            fieldId: string;
            fieldValue: any;
            pickerType?: DatePickerTypeEnum;
            defaultValue?: string | number;
            typeName?: 'collection_item' | 'form';
            filterName?: JsonTree;
            includeValidationValue?: boolean;
        }>;
        contextMaps: Record<string, any>;
        service: DataRegisterTransactionService | DataRegisterTransactionTenancyService;
        sourceDataFields: DataRegisterTransactionFieldTenancyEntity[] | DataRegisterTransactionFieldEntity[] | TransactionFieldEntity[];
    }) {
        const payload: EditDataRegisterTransactionRequest = {
            transactionFields: [],
            dataRegisterId,
        };

        let createFields: EditDataRegisterTransactionFieldRequest[] = [];

        if (associateField) {
            const associateFieldBuilder = dataRegisterVersion.fields.find((f) => f.fieldId === associateField);
            createFields = [
                {
                    fieldId: associateField,
                    fieldType: associateFieldBuilder.type,
                    fieldValue: associatedId,
                },
            ];
        }

        const styleAndOverrideFields: StyleAndOverrideFields = {};

        updateFields.forEach(
            ({ fieldId, fieldValue, pickerType, defaultValue, fieldValueSource, typeName, filterName, includeValidationValue }) => {
                let value = fieldValue;
                const builderField = dataRegisterVersion.fields.find((f) => f.fieldId === fieldId);
                if (!builderField) {
                    return;
                }

                if (fieldValueSource === 'field') {
                    switch (typeName) {
                        case 'collection_item':
                            {
                                const collectionFields = (
                                    (sourceDataFields as TransactionFieldEntity[])?.filter(
                                        (sdf) => sdf.contextType === TransactionFieldContextTypeEnum.COLLECTION,
                                    ) ?? []
                                ).sort((a, b) => (a.data?.externalOrder ?? 0) - (b.data?.externalOrder ?? 0));

                                const [collectionItemIdentityId, fieldIdFromCollection] = fieldValue.split('--');

                                if (!filterName?.children1?.length) {
                                    const transactionCollectionFields = collectionFields.filter(
                                        (tf) => tf.collectionItemId == collectionItemIdentityId && tf.fieldId === fieldIdFromCollection,
                                    )?.[0];

                                    value = getFieldValue(builderField, transactionCollectionFields);
                                    this._handleStyleAndOverride(
                                        styleAndOverrideFields,
                                        transactionCollectionFields,
                                        includeValidationValue,
                                        fieldId,
                                        value,
                                    );
                                } else {
                                    const collectionFieldsQuery = collectionFields.map((cf) => {
                                        return {
                                            id: cf.id,
                                            [`${cf.collectionItemId}--${cf.fieldId}`]: cf.fieldOptionIds?.length
                                                ? cf.fieldOptionIds
                                                : cf.fieldValue,
                                            rowKey: cf.rowKey,
                                        };
                                    });

                                    const jsonLogic = QbUtils.jsonLogicFormat(QbUtils.loadTree(filterName as JsonTree), CoreConfig);

                                    const rowKey = collectionFieldsQuery.filter((item) => apply(jsonLogic?.logic, item))?.[0]?.rowKey;

                                    if (!rowKey) {
                                        value = null;
                                    } else {
                                        const collectionValue = collectionFields.find(
                                            (cf) => cf.rowKey === rowKey && cf.fieldId === fieldIdFromCollection,
                                        );

                                        value = getFieldValue(builderField, collectionValue);
                                        this._handleStyleAndOverride(
                                            styleAndOverrideFields,
                                            collectionValue,
                                            includeValidationValue,
                                            fieldId,
                                            value,
                                        );
                                    }
                                }
                            }
                            break;
                        // TODO: keep typeName = form for tracking logic, the thing is we should treat mapping field in same for mapping not a collection
                        case 'form':
                        default:
                            const transactionField = sourceDataFields.find((f) => f.fieldId === fieldValue);
                            value = getFieldValue(builderField, transactionField);

                            if (
                                builderField?.type === FormFieldTypeEnum.Rollup &&
                                builderField?.configuration?.rollup?.dataType === 'validationResult'
                            ) {
                                styleAndOverrideFields[fieldId] = {
                                    validationValue: value,
                                    style: {
                                        fieldId,
                                        style: transactionField.transactionFieldStyle.configuration,
                                    },
                                };
                            }

                            this._handleStyleAndOverride(styleAndOverrideFields, transactionField, includeValidationValue, fieldId, value);

                            break;
                    }
                } else {
                    value = getFieldValueBasedOnPicker({
                        pickerType,
                        value: defaultValue,
                        fieldValue: fieldValue,
                        fieldType: builderField.type,
                    });
                }

                createFields.push({
                    fieldId,
                    fieldValue: value,
                    type: builderField.type,
                });
            },
        );

        Object.keys(contextMaps).forEach((fieldId) => {
            const value = contextMaps[fieldId];

            if (createFields.find((f) => f.fieldId === fieldId)) {
                return;
            }

            const builderField = dataRegisterVersion.fields.find((f) => f.fieldId === fieldId);
            if (!builderField) {
                return;
            }
            createFields.push({
                fieldId,
                fieldValue: value,
                type: builderField.type,
            });
        });

        const codeField = dataRegisterVersion.fields.find((f) => f.fieldId === DATA_REGISTER_CODE_FIELD_ID);

        if (!createFields.find((f) => f.fieldId === codeField.fieldId) && !codeField?.configuration?.isAutoGenerateCode) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] error: Missing data register code field`,
            };
        }

        payload.transactionFields = createFields;
        payload.styleAndOverrideFields = styleAndOverrideFields;

        const result = await service.create(payload, SourceOfChangeType.AUTOMATION);
        return result;
    }
}
