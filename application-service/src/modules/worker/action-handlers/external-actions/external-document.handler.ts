import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { compact, uniq } from 'lodash';
import { DataSource, In, IsNull, Not } from 'typeorm';
import { LoggerService } from '../../../../common/src';
import { EXTERNAL_ORIGINAL_VERSION_ID, MDS_ID, ORIGIN_DATA } from '../../../../common/src/constant/field';
import { OperatorType } from '../../../../common/src/modules/shared/enums/operator.enum';
import { OrderType } from '../../../../common/src/modules/shared/enums/order.enum';
import { PREFIX } from '../../../../common/src/modules/shared/enums/prefix.enum';
import { AutomationActionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { AutoPopulateBuilderTypeEnum } from '../../../../database/src/shared/enums/ap-builder-type.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { DatePickerTypeEnum } from '../../../../shared/enums/date-picker.enum';
import { convertDateTimeValueToDate } from '../../../../utils';
import { FormTransactionTenancyService } from '../../../form/services/form-transaction.tenancy.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { NewExternalDocumentPayload } from '../../dtos/external-document.dto';
import { TransactionCreationService } from '../../services/transaction-creation.service';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';

@Injectable()
export class ExternalDocumentActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _createTransactionService: TransactionCreationService,
        private readonly _logger: LoggerService,
    ) {
        super(moduleRef);
    }

    public validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const actionId = actionDto?.payload?.actionId;
        const report = actionDto?.payload?.external?.report;
        const headerMessage = `[Correlation:${actionDto?.metadata?.correlationId}] - [ACTION:${actionDto?.metadata.name}]: `;
        if (!actionId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} ${actionDto.type} Action Id is not existed!`,
            };
        }

        return { type: 'EventValidatedSuccess' };
    }
    public async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        const { metadata, payload } = parsedEvent ?? {};
        const { report, mdsId, fields, originVersionId, isModify, datasource, sourceId } = (payload?.external ??
            {}) as NewExternalDocumentPayload;

        const headerMessage = `[Correlation:${metadata.correlationId}] - [ACTION:${metadata.name}]: `;

        const accountId = metadata?.tenantId;
        const actionId = payload?.actionId;

        const dataSource = await this._dataSourceService.createAccountDataSource(accountId);

        const uniqKeys: {
            reportId: string;
            datasource: string;
            sourceId: string;
        } = {
            reportId: originVersionId,
            datasource: datasource,
            sourceId,
        };

        try {
            // get action detail
            const {
                automationActionRepository,
                fieldRepository,
                formRepository,
                generalSettingRepository,
                registerRepo,
                transactionFieldRepo,
            } = this._getRepositories(dataSource);

            const action = await automationActionRepository.findOneBy({ id: actionId });
            const sourceId = action?.sourceId;

            if (!sourceId) {
                return this._returnSuccessWithNoAction(`${headerMessage} No source form settings`);
            }

            if (!mdsId) {
                return this._returnSuccessWithNoAction(`${headerMessage} No MDS ID`);
            }

            const publishedForm = await formRepository.findOneBy({ id: sourceId, activeVersionId: Not(IsNull()) });
            if (!publishedForm) {
                return this._returnSuccessWithNoAction(`${headerMessage} No published forms found`);
            }

            // if (report?.ctxPropertyId) {
            //     const generalAutoPopulateSettings = await generalSettingRepository.findBy({
            //         targetFieldId: report?.ctxPropertyId,
            //         builderType: AutoPopulateBuilderTypeEnum.FormField,
            //         builderVersionId: publishedForm.activeVersionId,
            //     });

            //     // check the current active version of form has populate setting on report property
            //     const canCreateTransaction = generalAutoPopulateSettings.some(
            //         (setting) => setting.builderId === publishedForm.id && setting.builderVersionId === publishedForm.activeVersionId,
            //     );

            //     if (!canCreateTransaction) {
            //         return this._returnSuccessWithNoAction(`${headerMessage} No populated settings found`);
            //     }
            // }

            const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
                accountId,
                FormTransactionTenancyService,
            );

            const actionMappingFields: Array<{ toFieldId: string; fromFieldId: string }> =
                (action?.configuration?.mappingFields as Array<{ toFieldId: string; fromFieldId: string }>) || [];

            const updatingFormFields = this._preprocessUpdatingFields<{
                fieldId: string;
                fieldType: FormFieldTypeEnum;
                fieldValue: string;
            }>(action?.configuration?.updatingFormFields || []);

            const updatingCollectionFields = this._preprocessUpdatingFields<{
                collectionId: string;
                collectionItemId: string;
                fieldId: string;
                fieldType: FormFieldTypeEnum;
                fieldValue: string;
            }>(action?.configuration?.updatingCollectionFields || []);

            const transactionFieldIds = actionMappingFields.filter((mapping) => mapping.fromFieldId === MDS_ID).map((f) => f.toFieldId);
            const uniqFieldIds = uniq(compact(transactionFieldIds));
            const fieldConfigurations = await service.getFieldConfiguration({
                fieldIds: uniqFieldIds,
                formVersionId: publishedForm?.activeVersionId,
            });

            const registerIds = uniq(compact(fieldConfigurations.map((f) => f.configuration?.targetId)));
            if (!registerIds?.length) {
                return this._returnSuccessWithNoAction(`${headerMessage} No Registers was found`);
            }

            const registers = await registerRepo.find({
                where: {
                    dataRegisterId: In(registerIds),
                    externalId: mdsId,
                },
            });

            if (!registers?.length) {
                return this._returnSuccessWithNoAction(`${headerMessage} No Registers was found with mds id ${mdsId}`);
            }

            const buildTransactionFormFields: any[] = [];

            let mdsLookupFieldValue: string = undefined;

            actionMappingFields.forEach((map) => {
                const fieldId = map.toFieldId;
                const formField = (fieldConfigurations || []).find((f) => f.fieldId === fieldId);
                const register = registers.find((r) => r.dataRegisterId === formField?.configuration?.targetId);
                const registerId = register?.id;
                if (registerId) {
                    mdsLookupFieldValue = registerId;
                    buildTransactionFormFields.push({
                        fieldId,
                        fieldValue: registerId,
                        fieldType: formField?.type as FormFieldTypeEnum,
                        type: formField?.type as FormFieldTypeEnum,
                    });
                }
            });

            const formVersionId = publishedForm.activeVersionId;
            const formFields = await fieldRepository.findBy({
                formVersionId,
            });

            let isCreateNew = true;
            let transaction = undefined;

            //check if need to checking for update exsisted transaction
            if (uniqKeys?.reportId && mdsLookupFieldValue) {
                // const documentFieldId = actionMappingFields.find((mapping) => mapping.fromFieldId === REPORT_ID)?.toFieldId;
                const entityFieldId = actionMappingFields.find((mapping) => mapping.fromFieldId === MDS_ID)?.toFieldId;

                if (entityFieldId) {
                    const existedTransactions = await service.getList({
                        filters: [
                            {
                                field: `${PREFIX.DATA_REGISTER_FIELD}_${EXTERNAL_ORIGINAL_VERSION_ID}`,
                                operator: OperatorType.equals,
                                value: uniqKeys.reportId,
                                queryToDataFilterOptions: true,
                            },
                            {
                                field: `${PREFIX.DATA_REGISTER_FIELD}_${entityFieldId}`,
                                operator: OperatorType.equals,
                                value: mdsLookupFieldValue,
                                queryToOptionIds: true,
                            } as any,
                            {
                                field: 'formId',
                                operator: OperatorType.equals,
                                value: sourceId,
                            },
                        ],
                        take: 1,
                        skip: 0,
                        sorters: [
                            {
                                field: 'createdAt',
                                order: OrderType.DESC,
                            },
                        ],
                    });
                    transaction = existedTransactions?.data?.[0];

                    if (transaction) {
                        this._logger.info(`Got existed transaction ${transaction?.id} for external document event`);
                        isCreateNew = false;

                        updatingFormFields.map((field) => {
                            const existed = buildTransactionFormFields.find((f) => f.fieldId === field.fieldId);
                            if (existed) {
                                existed.fieldValue = field.fieldValue;
                            } else {
                                buildTransactionFormFields.push({
                                    fieldId: field.fieldId,
                                    fieldValue: field.fieldValue,
                                    fieldType: field.fieldType as FormFieldTypeEnum,
                                    type: field.fieldType as FormFieldTypeEnum,
                                });
                            }
                        });
                    }
                }
            }

            if (isCreateNew) {
                //create empty transactions
                transaction = await this._createTransactionService.createEmptyTransaction({
                    accountId,
                    formId: publishedForm.id,
                    formVersionId,
                    timezone: '',

                    // because we call update after so can use skip options here
                    skipPopulate: true,
                    skipRollup: true,
                    skipValidation: true,
                });
            }

            if (!transaction) {
                return this._returnSuccessWithNoAction(`${headerMessage} No transaction was created`);
            }

            const transactionId = transaction.id;

            const parsedEvent = {
                metadata: {
                    tenantId: accountId,
                },
                payload: {
                    configuration: {
                        mappingFields: action.configuration?.mappingFields || [],
                    },
                    context: {
                        transactionId,
                    },
                },
            };

            const externalValues: Array<{ ctxPropertyId: string; value: string }> = report?.ctxPropertyId
                ? [
                      {
                          ctxPropertyId: report.ctxPropertyId,
                          value: report.value,
                      },
                  ]
                : [];

            if (Object.keys(fields ?? {})?.length) {
                Object.keys(fields).forEach((key) => {
                    externalValues.push({
                        ctxPropertyId: key,
                        value: fields[key],
                    });
                });
            }

            let externalReportTransactionField = await transactionFieldRepo.findOne({
                where: {
                    transactionId,
                    collectionId: IsNull(), // form fields
                    fieldId: EXTERNAL_ORIGINAL_VERSION_ID,
                },
                select: ['id', 'fieldId', 'data'],
            });

            if (externalReportTransactionField) {
                const data = externalReportTransactionField?.data ?? {};
                if (!data[ORIGIN_DATA.REPORT]) {
                    data[ORIGIN_DATA.REPORT] = {};
                }

                if (!data[ORIGIN_DATA.FILTER_OPTIONS]) {
                    data[ORIGIN_DATA.FILTER_OPTIONS] = [];
                }
                data[ORIGIN_DATA.REPORT][uniqKeys.datasource] = uniqKeys.reportId;
                const reportIds = Array.from(new Set(Object.values(data[ORIGIN_DATA.REPORT] as string[]).filter(Boolean)));
                data[ORIGIN_DATA.FILTER_OPTIONS] = reportIds;

                externalReportTransactionField.data = data;
            } else {
                externalReportTransactionField = new TransactionFieldEntity();
                externalReportTransactionField.fieldId = EXTERNAL_ORIGINAL_VERSION_ID;
                externalReportTransactionField.fieldValue = null;
                externalReportTransactionField.fieldOptionIds = [];
                externalReportTransactionField.transactionId = transactionId;
                externalReportTransactionField.contextType = TransactionFieldContextTypeEnum.FORM;
                externalReportTransactionField.fieldType = FormFieldTypeEnum.Text;
                externalReportTransactionField.data = {
                    [ORIGIN_DATA.REPORT]: {
                        [uniqKeys.datasource]: uniqKeys.reportId,
                    },
                    [ORIGIN_DATA.FILTER_OPTIONS]: [uniqKeys.reportId],
                };
            }

            await transactionFieldRepo.save(externalReportTransactionField);

            for (const item of buildTransactionFormFields) {
                const populatedResult: Record<string, any> = await this._createTransactionService.runGeneralPopulate({
                    fieldChangeId: item.fieldId,
                    parsedEvent,
                    transaction: {
                        transactionFields: buildTransactionFormFields.map((f) => {
                            return {
                                fieldId: f.fieldId,
                                fieldValue: f.fieldValue,
                                fieldType: f.fieldType as FormFieldTypeEnum,
                            };
                        }),
                        formVersionId,
                    },
                    externalValues,
                    payloadDocuments: uniqKeys.reportId ? { [datasource]: uniqKeys.reportId } : undefined,
                });
                const transactionFields = !Array.isArray(populatedResult?.transactionFields) ? populatedResult?.transactionFields : {};
                const populateValues = transactionFields.populateValues;
                const metadata = transactionFields.metadata;

                if (populateValues) {
                    Object.keys(populateValues).forEach((fieldId) => {
                        const fieldValueObj = populateValues[fieldId];
                        const fieldMetadata = metadata?.find((f) => f.fieldId === fieldId);
                        if (!fieldValueObj) return;
                        const formField = formFields.find((f) => f.fieldId === fieldId);
                        const transactionField = buildTransactionFormFields.find((item) => item.fieldId === fieldId);
                        if (transactionField) {
                            transactionField.fieldValue = fieldValueObj?.fieldValue;
                            if (formField.type === FormFieldTypeEnum.Document) {
                                transactionField.data = {
                                    docId: fieldMetadata?.docId,
                                    dataSourceType: fieldMetadata?.dataSourceType,
                                    docFieldType: fieldMetadata?.docFieldType,
                                    filePath: fieldMetadata?.filePath,
                                };
                            }
                        } else {
                            buildTransactionFormFields.push({
                                fieldId,
                                fieldValue: fieldValueObj?.value,
                                fieldType: formField?.type as FormFieldTypeEnum,
                                type: formField?.type as FormFieldTypeEnum,
                                data: fieldMetadata
                                    ? {
                                          docId: fieldMetadata?.docId,
                                          dataSourceType: fieldMetadata?.dataSourceType,
                                          docFieldType: fieldMetadata?.docFieldType,
                                          filePath: fieldMetadata?.filePath,
                                      }
                                    : undefined,
                            });
                        }
                    });
                }
            }

            await this._createTransactionService.updateTransactionWithFields({
                transactionId,
                formId: transaction.formId,
                transactionStageId: transaction.stageId,
                parsedEvent,
                transactionFields: buildTransactionFormFields,
                updatingCollectionFields,
                isModify,
                payloadDocuments: uniqKeys.reportId ? { [datasource]: uniqKeys.reportId } : undefined,
            });

            return {
                type: 'TriggeredActionSuccess',
                metadata: {
                    transactionId,
                    message: headerMessage,
                },
            };
        } catch (error) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] error ${error}`,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            const transactionId = actionResult?.metadata?.transactionId;

            if (!transactionId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Transaction id not found in action result!',
                };
            }

            const dataSource = await this._getDataSource(context.event.metadata.tenantId);
            const transRepo = dataSource.getRepository(TransactionEntity);

            await transRepo.softDelete({
                id: transactionId,
            });

            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }

    private _getRepositories(dataSource: DataSource) {
        const automationActionRepository = dataSource.getRepository(AutomationActionTenancyEntity);
        const formRepository = dataSource.getRepository(FormTenancyEntity);
        const fieldRepository = dataSource.getRepository(FormFieldTenancyEntity);
        const generalSettingRepository = dataSource.getRepository(GeneralAutoPopulateSettingTenancyEntity);
        const registerRepo = dataSource.getRepository(DataRegisterTransactionTenancyEntity);
        const transactionFieldRepo = dataSource.getRepository(TransactionFieldEntity);

        return {
            automationActionRepository,
            formRepository,
            fieldRepository,
            generalSettingRepository,
            registerRepo,
            transactionFieldRepo,
        };
    }

    private _returnSuccessWithNoAction(reason: string): HandleActionResult {
        return {
            type: 'TriggeredActionSuccess',
            metadata: {
                message: reason,
            },
        };
    }

    private _preprocessUpdatingFields<
        T extends {
            fieldId: string;
            fieldType: FormFieldTypeEnum;
            fieldValue: string;
            pickerType?: DatePickerTypeEnum;
            collectionId?: string;
            collectionItemId?: string;
        },
    >(fields: T[]): T[] {
        return fields.map((f) => {
            switch (f.fieldType) {
                case FormFieldTypeEnum.DatePicker:
                case FormFieldTypeEnum.DatetimePicker: {
                    f.fieldValue = convertDateTimeValueToDate({
                        pickerType: f.pickerType,
                        value: f.fieldValue,
                    })?.toString();
                    break;
                }
                default:
                    break;
            }

            return f;
        });
    }
}
