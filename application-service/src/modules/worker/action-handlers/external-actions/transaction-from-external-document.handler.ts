import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { compact, uniq } from 'lodash';
import { DataSource, In, IsNull, Not } from 'typeorm';
import { LoggerService } from '../../../../common/src';
import { EXTERNAL_ORIGINAL_VERSION_ID, MDS_ID, ORIGIN_DATA, REPORT_ID } from '../../../../common/src/constant/field';
import { OperatorType } from '../../../../common/src/modules/shared/enums/operator.enum';
import { OrderType } from '../../../../common/src/modules/shared/enums/order.enum';
import { PREFIX } from '../../../../common/src/modules/shared/enums/prefix.enum';
import { AutomationActionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../../database/src/services/connection-util.service';
import { AutoPopulateBuilderTypeEnum } from '../../../../database/src/shared/enums/ap-builder-type.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { FormTransactionTenancyService } from '../../../form/services/form-transaction.tenancy.service';
import { ParsedActionDto } from '../../dtos/action.dto';
import { TransactionCreationService } from '../../services/transaction-creation.service';
import { ActionContext, HandleActionResult, HandleRollbackResult, ValidateEventResult } from '../common/action-types';
import { AbstractActionHandler } from '../common/action.abstraction';
import { FormTransactionDataService } from '../../../form/services/data/form-transaction.data.service';

@Injectable()
export class TransactionFromExternalDocumentActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _createTransactionService: TransactionCreationService,
        private readonly _logger: LoggerService,
    ) {
        super(moduleRef);
    }

    public validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const actionId = actionDto?.payload?.actionId;
        const headerMessage = `[Correlation:${actionDto?.metadata?.correlationId}] - [ACTION:${actionDto?.metadata.name}]: `;
        if (!actionId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} ${actionDto.type} Action Id is not existed!`,
            };
        }

        return { type: 'EventValidatedSuccess' };
    }

    public async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        const { metadata, payload } = parsedEvent ?? {};
        const { report, mdsId, fields, originVersionId, datasourceType } = payload ?? {};

        const headerMessage = `[Correlation:${metadata.correlationId}] - [ACTION:${metadata.name}]: `;

        if (!originVersionId) {
            return this._returnSuccessWithNoAction(`${headerMessage} No reportId found`);
        }

        const accountId = metadata?.tenantId;
        const actionId = payload?.triggerId;

        const dataSource = await this._dataSourceService.createAccountDataSource(accountId);

        try {
            // get action detail
            const { automationActionRepository, fieldRepository, formRepository, generalSettingRepository, registerRepo } =
                this._getRepositories(dataSource);

            const action = await automationActionRepository.findOneBy({ id: actionId });
            if (!action) {
                return this._returnSuccessWithNoAction(`${headerMessage} No action found`);
            }

            const { configuration } = action;
            const formId = configuration?.formId;

            if (!mdsId) {
                return this._returnSuccessWithNoAction(`${headerMessage} No MDS ID`);
            }

            const publishedForm = await formRepository.findOneBy({ id: formId, activeVersionId: Not(IsNull()) });
            if (!publishedForm) {
                return this._returnSuccessWithNoAction(`${headerMessage} No published forms found`);
            }

            if (report?.ctxPropertyId) {
                const generalAutoPopulateSettings = await generalSettingRepository.findBy({
                    targetFieldId: report?.ctxPropertyId,
                    builderType: AutoPopulateBuilderTypeEnum.FormField,
                    builderVersionId: publishedForm.activeVersionId,
                });

                // check the current active version of form has populate setting on report property
                const canCreateTransaction = generalAutoPopulateSettings.some(
                    (setting) => setting.builderId === publishedForm.id && setting.builderVersionId === publishedForm.activeVersionId,
                );

                if (!canCreateTransaction) {
                    return this._returnSuccessWithNoAction(`${headerMessage} No populated settings found`);
                }
            }

            const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
                accountId,
                FormTransactionTenancyService,
            );

            const actionMappingFields: Array<{ toFieldId: string; fromFieldId: string }> =
                (configuration?.mappingFields as Array<{ toFieldId: string; fromFieldId: string }>) || [];

            const transactionFieldIds = actionMappingFields.filter((mapping) => mapping.fromFieldId === MDS_ID).map((f) => f.toFieldId);
            const uniqFieldIds = uniq(compact(transactionFieldIds));
            const fieldConfigurations = await service.getFieldConfiguration({
                fieldIds: uniqFieldIds,
                formVersionId: publishedForm?.activeVersionId,
            });

            const registerIds = uniq(compact(fieldConfigurations.map((f) => f.configuration?.targetId)));
            if (!registerIds?.length) {
                return this._returnSuccessWithNoAction(`${headerMessage} No Registers was found`);
            }

            const registers = await registerRepo.find({
                where: {
                    dataRegisterId: In(registerIds),
                    externalId: mdsId,
                },
            });

            if (!registers?.length) {
                return this._returnSuccessWithNoAction(`${headerMessage} No Registers was found with mds id ${mdsId}`);
            }

            const buildTransactionFormFields: any[] = [];

            actionMappingFields.forEach((map) => {
                const fieldId = map.toFieldId;
                const fromField = map.fromFieldId;
                const formField = (fieldConfigurations || []).find((f) => f.fieldId === fieldId);
                const register = registers.find((r) => r.dataRegisterId === formField?.configuration?.targetId);
                const registerId = register?.id;
                if (registerId) {
                    buildTransactionFormFields.push({
                        fieldId,
                        fieldValue: registerId,
                        fieldType: formField?.type as FormFieldTypeEnum,
                        type: formField?.type as FormFieldTypeEnum,
                    });
                } else if (fromField == REPORT_ID) {
                    buildTransactionFormFields.push({
                        fieldId,
                        fieldValue: originVersionId,
                        fieldType: formField?.type as FormFieldTypeEnum,
                        type: formField?.type as FormFieldTypeEnum,
                    });
                }
            });

            const formVersionId = publishedForm.activeVersionId;
            const formFields = await fieldRepository.findBy({
                formVersionId,
            });

            const filters = [
                {
                    field: 'formId',
                    operator: OperatorType.equals,
                    value: formId,
                },
                {
                    field: `${PREFIX.DATA_REGISTER_FIELD}_${EXTERNAL_ORIGINAL_VERSION_ID}`,
                    operator: OperatorType.equals,
                    value: originVersionId,
                    queryToDataFilterOptions: true,
                },
            ];
            const existedTransactions = await service.getList({
                filters,
                take: 1,
                skip: 0,
                sorters: [
                    {
                        field: 'createdAt',
                        order: OrderType.DESC,
                    },
                ],
            });
            const existedTransaction = existedTransactions?.data?.[0];
            if (existedTransaction) {
                return this._returnSuccessWithNoAction(`${headerMessage} Transaction with reportId ${originVersionId} already exists`);
            }

            //create empty transactions
            const transaction = await this._createTransactionService.createEmptyTransaction({
                accountId,
                formId: publishedForm.id,
                formVersionId,
                timezone: '',

                // because we call update after so can use skip options here
                skipPopulate: true,
                skipRollup: true,
                skipValidation: true,
            });

            if (!transaction) {
                return this._returnSuccessWithNoAction(`${headerMessage} No transaction was created`);
            }
            const transactionId = transaction.id;
            const formTransactionDataService = await this._dataSourceService.resolveService<FormTransactionDataService>(
                accountId,
                FormTransactionDataService,
            );
            await formTransactionDataService.update({
                id: transactionId,
                request: {
                    transactionFields: buildTransactionFormFields,
                    formId: transaction.formId,
                    transactionId,
                    payloadDocuments: { [datasourceType]: originVersionId },
                    formValues: buildTransactionFormFields?.reduce((acc, item) => {
                        acc[item.fieldId] = item.fieldValue;
                        return acc;
                    }, {}),
                },
                option: {
                    shouldRunPopulateFormFields: true,
                },
            });

            return {
                type: 'TriggeredActionSuccess',
                metadata: {
                    transactionId,
                    message: headerMessage,
                },
            };
        } catch (error) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] error ${error}`,
            };
        }
    }

    async rollbackSaga(context: ActionContext, actionResult: Record<string, any> | null): Promise<HandleRollbackResult> {
        try {
            const transactionId = actionResult?.metadata?.transactionId;

            if (!transactionId) {
                return {
                    type: 'RollbackActionError',
                    message: 'Transaction id not found in action result!',
                };
            }

            const dataSource = await this._getDataSource(context.event.metadata.tenantId);
            const transRepo = dataSource.getRepository(TransactionEntity);

            await transRepo.softDelete({
                id: transactionId,
            });

            return {
                type: 'RollbackActionSuccess',
            };
        } catch (err) {
            return {
                type: 'RollbackActionError',
                message: err.message,
            };
        }
    }

    private _getRepositories(dataSource: DataSource) {
        const automationActionRepository = dataSource.getRepository(AutomationActionTenancyEntity);
        const formRepository = dataSource.getRepository(FormTenancyEntity);
        const fieldRepository = dataSource.getRepository(FormFieldTenancyEntity);
        const generalSettingRepository = dataSource.getRepository(GeneralAutoPopulateSettingTenancyEntity);
        const registerRepo = dataSource.getRepository(DataRegisterTransactionTenancyEntity);

        return {
            automationActionRepository,
            formRepository,
            fieldRepository,
            generalSettingRepository,
            registerRepo,
        };
    }

    private _returnSuccessWithNoAction(reason: string): HandleActionResult {
        return {
            type: 'TriggeredActionSuccess',
            metadata: {
                message: reason,
            },
        };
    }
}
