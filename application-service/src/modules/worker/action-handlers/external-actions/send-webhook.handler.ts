import { Injectable } from '@nestjs/common';
import { AbstractActionHandler } from '../common/action.abstraction';
import { ModuleRef } from '@nestjs/core';
import { HandleActionResult, ValidateEventResult } from '../common/action-types';
import { ParsedActionDto } from '../../dtos/action.dto';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';

import { LoggerService } from '../../../../common/src';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';

@Injectable()
export class WebhookActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _httpService: HttpService,
        private readonly _logger: LoggerService,
        private readonly _eventDrivenService: EventDrivenService,
    ) {
        super(moduleRef);
    }

    public validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const { payload } = actionDto;
        const { configuration } = payload;
        const { url, method } = configuration;

        if (!url) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${payload.actionId} url is required for webhook action`,
            };
        }

        if (method && !['get', 'post', 'put', 'patch', 'delete'].includes(method.toLowerCase())) {
            return {
                type: 'EventValidatedFailed',
                message: `[ACTION] ${payload.actionId} invalid method ${method} for webhook action`,
            };
        }

        return { type: 'EventValidatedSuccess' };
    }

    public async handleAction(actionDto: ParsedActionDto): Promise<HandleActionResult> {
        const { payload, metadata } = actionDto;
        const { configuration } = payload;
        const { url, headers, method } = configuration;

        this._logger.log(`[ACTION] Executing webhook action ${payload.actionId} to ${url}`);

        try {
            const causationId = metadata.causationId;
            const eventLog = await this._eventDrivenService.getEventLogById(causationId);

            if (!eventLog) {
                this._logger.error(`[ACTION] Webhook action ${payload.actionId} failed to find event log by causationId ${causationId}`);
                return {
                    type: 'TriggeredActionErrorGoTerminate',
                    message: `[ACTION] ${payload.actionId} failed to find event log by causationId ${causationId}`,
                };
            }

            const data = {
                metadata: eventLog.metadata,
                aggreationId: eventLog.aggregateId,
                id: eventLog.id,
            };

            const requestConfig: AxiosRequestConfig = {
                url,
                method: method || 'POST',
                headers: headers ? Object.fromEntries(headers.map((h) => [h.key, h.value])) : {},
                data,
            };

            await firstValueFrom(this._httpService.request(requestConfig));
            // Do not need to check the response status because we want to retry the action if it fails
            // if (response.status >= 400) {
            //     this._logger.error(
            //         `[ACTION] Webhook action ${payload.actionId} failed`,
            //         `HTTP request failed with status ${response.status}: ${response.statusText}`,
            //     );
            //     return {
            //         type: 'TriggeredActionErrorGoRetry',
            //         message: `HTTP request failed with status ${response.status}: ${response.statusText}`,
            //     };
            // }

            this._logger.log(`[ACTION] Webhook action ${payload.actionId} executed successfully`);

            return {
                type: 'TriggeredActionSuccess',
            };
        } catch (error) {
            this._logger.error(`[ACTION] Webhook action ${payload.actionId} failed`, error);
            return {
                type: 'TriggeredActionErrorGoRetry',
                message: error.message,
            };
        }
    }
}
