import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { AbstractAction<PERSON>andler } from '../../common/action.abstraction';
import { DataSourceService } from '../../../../../database/src/services/connection-util.service';
import { LoggerService } from '../../../../../common/src';
import { ParsedActionDto } from '../../../dtos/action.dto';
import { HandleActionResult, ValidateEventResult } from '../../common/action-types';
import { DataSource } from 'typeorm';
import { DataRegisterTransactionTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { EventDrivenService } from '../../../../../common/src/modules/event-driven/event-driven.service';
import { DataPopulateEventEnum } from '../../../../../common/src/modules/shared/enums/event-driven/data-populate-event.enum';
import { CommonTopicEnum } from '../../../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { AutomationContextType } from '../../../../../database/src/shared/enums/automation.enum';
import { RequestContextService } from '../../../../../common/src/application/context/AppRequestContext';

@Injectable()
export class Q88HVPQActionHandler extends AbstractActionHandler {
    constructor(
        moduleRef: ModuleRef,
        private readonly _dataSourceService: DataSourceService,
        private readonly _logger: LoggerService,
        private readonly _eventDrivenService: EventDrivenService,
    ) {
        super(moduleRef);
    }

    public validateRequiredActionParams(actionDto: ParsedActionDto): ValidateEventResult {
        const { metadata, contextTransactionId } = actionDto ?? {};

        const headerMessage = `[Correlation:${actionDto?.metadata?.correlationId}] - [ACTION:${actionDto?.metadata.name}]: `;

        if (metadata?.accountId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} Tenant Id is not found!`,
            };
        }

        if (!contextTransactionId) {
            return {
                type: 'EventValidatedFailed',
                message: `${headerMessage} Transaction Id is not existed!`,
            };
        }

        // if (!mdsId) {
        //     return {
        //         type: 'EventValidatedFailed',
        //         message: `${headerMessage} MDS Id is not existed!`,
        //     };
        // }

        // if (!originVersionId) {
        //     return {
        //         type: 'EventValidatedFailed',
        //         message: `${headerMessage} Origin Version Id is not existed!`,
        //     };
        // }

        return { type: 'EventValidatedSuccess' };
    }

    public async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
        const { metadata, payload, contextTransactionId, contextType } = parsedEvent ?? {};
        const { datasource, datasourceType, context } = payload ?? {};
        const accountId = metadata?.tenantId;

        const headerMessage = `[Correlation:${metadata.correlationId}] - [ACTION:${metadata.name}]: `;
        const dataSource = await this._dataSourceService.createAccountDataSource(accountId);

        try {
            if (contextType === AutomationContextType.DataRegister) {
                return await this.handleDataRegisterUpdate(
                    {
                        datasource,
                        transactionId: contextTransactionId,
                        datasourceType,
                        mdsId: context?.mdsId,
                        transactionFields: context?.transaction ?? {},
                    },
                    dataSource,
                );
            }
            return {
                type: 'TriggeredActionSuccess',
                metadata: {
                    message: headerMessage,
                },
            };
        } catch (err) {
            this._logger.error(err);
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: err.message,
            };
        }
    }

    private async handleDataRegisterUpdate(
        {
            datasource,
            transactionId,
            datasourceType,
            mdsId,
            transactionFields,
        }: { datasource: string; transactionId: string; datasourceType: string; mdsId: string; transactionFields: Record<string, any> },
        appDataSource: DataSource,
    ): Promise<HandleActionResult> {
        const transactionRepo = appDataSource.getRepository(DataRegisterTransactionTenancyEntity);
        // const transactionFieldRepo = appDataSource.getRepository(DataRegisterTransactionFieldTenancyEntity);
        const transaction = await transactionRepo.findOne({
            where: {
                id: transactionId,
            },
        });
        if (!transaction) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: 'Transaction not found',
            };
        }
        // const transactionFields = await transactionFieldRepo.find({
        //     where: {
        //         dataRegisterTransactionId: transactionId,
        //     },
        // });
        // const fieldKeys = transactionFields?.reduce((acc, item) => {
        //     acc[item.fieldId] = item.fieldValue;
        //     return acc;
        // }, {});

        const dataPopulateMessage = EventDrivenService.createCommonEvent({
            payload: {
                contextId: mdsId, // data register id
                contextVersionId: transaction.dataRegisterVersionId, // data register version id
                contextTransactionId: transactionId, // data register transaction id
                contextSource: datasource, // data source
                contextType: datasourceType, // data source type
                transaction: transactionFields,
            },
            tenantId: RequestContextService.accountId,
            aggregateId: transactionId,
            type: DataPopulateEventEnum.EXTERNAL_DATA_FIELD_CHANGED,
            name: DataPopulateEventEnum.EXTERNAL_DATA_FIELD_CHANGED,
        });

        this._eventDrivenService.publishMessage(CommonTopicEnum.DATA_POPULATE_TOPIC, dataPopulateMessage);

        return {
            type: 'TriggeredActionSuccess',
        };
    }
}
