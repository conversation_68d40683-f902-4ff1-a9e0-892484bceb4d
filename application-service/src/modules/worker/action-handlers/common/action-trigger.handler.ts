import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../../../common/src';
import { ActionLogService } from '../../../../common/src/modules/action-log/action-log.service';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';
import { ActionEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/action-event.enum';
import { CommonTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { ActionStatus } from '../../../../database/src/shared/enums/action-log.type.enum';
import { ParsedActionDto } from '../../dtos/action.dto';
import { ActionContext, ActionResult, HandleActionResult, HandleRollbackResult, IActionRegistration } from './action-types';
import { AbstractActionHandler } from './action.abstraction';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';

@Injectable()
export class ActionTriggerHandler {
    private handlers: Map<ActionEventEnum, AbstractActionHandler> = new Map();

    constructor(
        private readonly _loggerService: LoggerService,
        private readonly _actionLogService: ActionLogService,
        private readonly _eventDrivenService: EventDrivenService,
    ) {}

    // Register handlers class
    registerActionHandlers(actionHandlers: IActionRegistration[]) {
        actionHandlers?.map((action) => this.setHandler(action.type, action.handler));
    }

    private setHandler(actionType: ActionEventEnum, handler: AbstractActionHandler): void {
        this.handlers.set(actionType, handler);
    }

    // Execute handler, perform retry if failed & rollback (optional) if retried failed too
    private async execute(handler: AbstractActionHandler, actionDto: ParsedActionDto): Promise<ActionResult> {
        const handleActionResult: HandleActionResult = await handler.handleAction(actionDto);
        if (handleActionResult.type === 'TriggeredActionErrorGoRetry') {
            const retryResult = await this.performRetry(handler, actionDto);
            return retryResult;
        } else {
            return handleActionResult;
        }
    }

    // Trigger an action by type
    async triggerAction(actionType: ActionEventEnum, context: ActionContext): Promise<ActionResult> {
        // 1. Get handler, validate & transform input
        const handler = this.handlers.get(actionType);

        if (!handler) {
            return {
                type: 'EventValidatedFailed',
                message: `No handler found for action type: ${actionType}`,
            };
        }

        const validatedEventResult = handler.validateEventContext(context);
        if (validatedEventResult.type === 'EventValidatedFailed') {
            return validatedEventResult;
        }

        const { event } = validatedEventResult;
        const actionDto = handler.transformEventContextToActionInput(event);

        // 2. Log action execution & execute handler
        // await this._actionLogService.logExecution(event.payload.actionId, ExecuteType.AUTO, event.metadata.tenantId);

        const validateRequiredActionParams = handler.validateRequiredActionParams(actionDto);
        if (validateRequiredActionParams.type === 'EventValidatedFailed') {
            await this._actionLogService.logCompleted(
                event.payload.actionId,
                ActionStatus.FAILURE,
                event.metadata.tenantId,
                validateRequiredActionParams,
                event.metadata,
                event.payload.context,
            );

            this._loggerService.error(validateRequiredActionParams.message);
            return validatedEventResult;
        }

        try {
            let actionExecutedResult: ActionResult = await this.execute(handler, actionDto);
            this._loggerService.log(`[ACTION] Finish execution with result: ${JSON.stringify(actionExecutedResult, null, 2)}`);
            // 3. Log action completion
            switch (actionExecutedResult.type) {
                case 'TriggeredActionSuccess':
                case 'TriggeredActionErrorGoTerminate': {
                    await this._actionLogService.logCompleted(
                        event.payload.actionId,
                        actionExecutedResult.type === 'TriggeredActionSuccess' ? ActionStatus.SUCCESS : ActionStatus.FAILURE,
                        event.metadata.tenantId,
                        actionExecutedResult,
                        event.metadata,
                        event.payload.context,
                    );
                    break;
                }

                case 'TriggeredActionSuccessWithRetry': {
                    if (actionExecutedResult.attempts > 0) {
                        await this._actionLogService.logRetryAttempt(
                            event.payload.actionId,
                            actionExecutedResult.attempts,
                            event.metadata.tenantId,
                            event.metadata,
                            event.payload.context,
                        );
                    }

                    await this._actionLogService.logCompleted(
                        event.payload.actionId,
                        ActionStatus.SUCCESS,
                        event.metadata.tenantId,
                        actionExecutedResult,
                        event.metadata,
                        event.payload.context,
                    );
                    break;
                }
                case 'RollbackActionSuccess':
                case 'RollbackActionError': {
                    const actionStatus =
                        actionExecutedResult.type === 'RollbackActionSuccess' ? ActionStatus.SUCCESS : ActionStatus.FAILURE;
                    await this._actionLogService.logCompleted(
                        event.payload.actionId,
                        actionStatus,
                        event.metadata.tenantId,
                        actionExecutedResult,
                        event.metadata,
                        event.payload.context,
                    );
                    break;
                }
                default:
                    this._loggerService.error(`[ACTION] Action result type is not defined for ${actionExecutedResult.type}`);
            }
        } catch (err) {
            console.error(err);
            this._loggerService.log(`[ACTION] Execution error: ${err.message}`);
        }
    }

    private async performRetry(
        handler: AbstractActionHandler,
        actionDto: ParsedActionDto,
    ): Promise<HandleActionResult | HandleRollbackResult> {
        const { retries, delay } = handler.getRetryConfig();
        let attempt = 0;

        while (attempt <= retries) {
            attempt++;

            this._loggerService.error(`[Action Retry] Action ${actionDto.type} - Attempt ${attempt}/${retries}`);
            const retriedResult = await handler.handleAction(actionDto);

            switch (retriedResult.type) {
                case 'TriggeredActionSuccess':
                case 'TriggeredActionSuccessWithRetry': {
                    return {
                        type: 'TriggeredActionSuccessWithRetry',
                        attempts: attempt,
                    };
                }
                case 'TriggeredActionErrorGoRetry': {
                    if (attempt >= retries) {
                        this._loggerService.error(`[Action Retry] Failed after ${retries} retries: ${retriedResult.message}`);

                        const message = EventDrivenService.createCommonEvent({
                            tenantId: RequestContextService.accountId,
                            aggregateId: actionDto?.payload?.actionId,
                            payload: actionDto,
                            type: ActionEventEnum.ROLLBACK,
                            name: ActionEventEnum.ROLLBACK,
                        });
                        await this._eventDrivenService.publishMessage(CommonTopicEnum.ROLLBACK_TOPIC, message);

                        if (handler?.rollback) {
                            return await this.performRollback(handler, actionDto);
                        }

                        return {
                            type: 'TriggeredActionErrorGoTerminate',
                            message: retriedResult.message,
                        };
                    }
                    this._loggerService.log(`[Action Retry] Retrying in ${delay}ms...`);
                    await new Promise((resolve) => setTimeout(resolve, delay));
                }
                default: {
                    this._loggerService.log(`[Action Retry] Action result is not defined.`);
                }
            }
        }
    }

    private async performRollback(handler: AbstractActionHandler, actionDto: ParsedActionDto): Promise<HandleRollbackResult> {
        try {
            this._loggerService.log(`[Action Rollback] Rollback running for action ${actionDto.type}.`);
            const result = await handler.rollback(actionDto);
            this._loggerService.log(`[Action Rollback] Rollback completed successfully for action ${actionDto.type}.`);
            return result;
        } catch (error) {
            this._loggerService.error(`[Action Rollback] Rollback failed ${error.message}.`);

            return {
                type: 'RollbackActionError',
                message: error.message,
                metadata: actionDto,
            };
        }
    }

    public async triggerSagaRollback(context: ActionContext): Promise<void> {
        try {
            // Step 1: Get all correlation action ids
            const eventLogs = await this._eventDrivenService.getCorrelationEvents();

            // Step 2: Get all actions by action ids
            const actionIds = eventLogs.map((event) => event.payload.actionId).filter(Boolean);
            const actionLogs = (await this._actionLogService.findAll(actionIds)).filter(
                (actionLog) => actionLog.status === ActionStatus.SUCCESS,
            );

            // Step 3: Rollback all actions
            for (const actionLog of actionLogs) {
                const action = actionLog.metadata;
                const actionType = action?.type as ActionEventEnum;
                const actionHandler = this.handlers.get(actionType);

                if (!actionHandler) {
                    this._loggerService.error(`[Saga Rollback] No handler found for action type: ${actionType}`);
                    continue;
                }

                if (actionHandler?.rollbackSaga) {
                    await actionHandler.rollbackSaga(context, actionLog.actionResult);
                }
            }
        } catch (error) {
            this._loggerService.error(`[Saga Rollback] Saga Rollback failed ${error.message}.`);
        }
    }
}
