import { Controller, UseInterceptors } from '@nestjs/common';
import { Ctx, KafkaContext, MessagePattern, Payload, Transport } from '@nestjs/microservices';
import { chunk, compact, omit, pick, uniq } from 'lodash';
import { nanoid } from 'nanoid';
import * as newrelic from 'newrelic';
import { ActionEventEnum } from 'src/common/src/modules/shared/enums/event-driven/action-event.enum';
import { DATA_PASSED_CODE, DEFAULT_STAGE_KPI_FIELD_ID, FROM_STAGE_FIELD } from 'src/constant';
import { AutomationActionFunctionType, AutomationContextType } from 'src/database/src/shared/enums/automation.enum';
import { CacheService, LoggerService } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { ContextInterceptor } from '../../../common/src/application/context/ContextInterceptor';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { MqttService } from '../../../common/src/modules/mqtt/mqtt.service';
import { CommonEvent } from '../../../common/src/modules/shared/domain/events';
import { AutomationTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/automation-topic.enum';
import { CommonTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { DataRegisterEventEnum } from '../../../common/src/modules/shared/enums/event-driven/data-register-event';
import { DataRegisterTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/data-register-topic.enum';
import { DataLakeTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/datalake-topic.enum';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import {
    TopicMapping,
    TransactionMQTTTopicEnum,
    TransactionTopicEnum,
} from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { ChangeLogActionType, SourceOfChangeType, TransactionDataType } from '../../../database/src/shared/enums/change-log.type.enum';
import { ScheduleService } from '../../crontab/services/schedule.service';
import { RollUpCalculationRequest } from '../../form/dtos/requests/rollup-calculation.request';
import { FormTransactionDataService } from '../../form/services/data/form-transaction.data.service';
import { RollUpCalculationService } from '../../form/services/rollup-calculation.service';
import { TransactionAutoCreationListener } from '../../worker/services/related-transaction-auto-creation.listener';
import { TransactionRecordChangeLogMessageDto } from '../dtos/data-register.job.dto';
import { ActionDataService } from '../services/action.data.service';
import { AutomationDataService } from '../services/automation.data.service';
import { DataPopulateDataService } from '../services/data-populate.data.service';
import { DataRegisterDataService } from '../services/data-register.data.service';
import { TransactionCommittedService } from '../services/transaction-committed.service';
import { ExternalDocumentDataService } from '../services/external-document.data.service';
import { ScheduleWfsKpiStatusService } from '../../bull-mq/services';
import { UtilsService } from '../../../common/src/modules/shared/services/utils.service';

const FIELD_KEYS = [
    'id',
    'fieldId',
    'dependFieldId',
    'parentId',
    'collectionId',
    'collectionItemId',
    'deletedAt',
    'createdAt',
    'updatedAt',
    'rowKey',
];

const convertFieldsPayload = (eventData: CommonEvent): string => {
    return JSON.stringify({
        id: eventData.id,
        aggregateId: eventData.aggregateId,
        payload: {
            id: eventData.payload.id,
            stageId: eventData.payload.stageId,
            formId: eventData.payload.formId,
            formVersionId: eventData.payload.formVersionId,
            fields: eventData?.payload?.fields?.map((field) => pick(field, FIELD_KEYS)) || [],
            fieldStyles: eventData?.payload?.fieldStyles?.map((fieldStyle) => pick(fieldStyle, FIELD_KEYS)) || [],
        },
        metadata: eventData.metadata,
    });
};

const convertFieldsPayloadWithChunk = (eventData: CommonEvent, filterFunc?: (fields: Array<any>) => Array<any>): Array<string> => {
    const result: Array<string> = [];

    const CHUNK_SIZE = 250;
    let chunkedFields;

    if (filterFunc) {
        chunkedFields = chunk(filterFunc(eventData.payload.fields), CHUNK_SIZE);
    } else {
        chunkedFields = chunk(eventData.payload.fields, CHUNK_SIZE);
    }

    for (const fields of chunkedFields) {
        result.push(
            JSON.stringify({
                id: eventData.id,
                aggregateId: eventData.aggregateId,
                payload: {
                    id: eventData.payload.id,
                    stageId: eventData.payload.stageId,
                    formId: eventData.payload.formId,
                    formVersionId: eventData.payload.formVersionId,
                    fields: fields.map((field) => pick(field, FIELD_KEYS)),
                },
                metadata: eventData.metadata,
            }),
        );
    }

    return result;
};

const KAFKA_TOPIC: number = parseInt(process.env.KAFKA_TOPIC || '0');

@Controller()
export class KafkaProcessor {
    constructor(
        private readonly _actionDataService: ActionDataService,
        private readonly _dataRegisterDataService: DataRegisterDataService,
        private readonly _formTransactionDataService: FormTransactionDataService,
        private readonly _automationDataService: AutomationDataService,
        private readonly _mqttService: MqttService,
        private readonly _loggerService: LoggerService,
        private readonly _cacheService: CacheService,
        private readonly _dataSourceService: DataSourceService,
        private readonly _scheduleService: ScheduleService,
        private readonly _dataPopulateService: DataPopulateDataService,
        private readonly _transactionCommittedService: TransactionCommittedService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _externalDocumentDataService: ExternalDocumentDataService,
        private readonly _scheduleWfsKpiStatusService: ScheduleWfsKpiStatusService,
    ) {}

    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [
            // TransactionTopicEnum.FORM_TRANSACTION_AUTO_CREATION,
            TransactionTopicEnum.FORM_TRANSACTION_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_STAGE_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_COLLECTION_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_ROLLUP_TOPIC,
            DataRegisterTopicEnum.DATA_REGISTER_BUILDER_TOPIC,
            CommonTopicEnum.ACTION_TOPIC,
            CommonTopicEnum.ROLLBACK_TOPIC,
            CommonTopicEnum.POPULATED_SCHEDULE_TOPIC,
            AutomationTopicEnum.AUTOMATION_BUILDER_TOPIC,
            DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE,
            TransactionTopicEnum.FORM_TRANSACTION_PDF_DOC_EXTRACTION_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC,
            CommonTopicEnum.MDS_DATA_CALLBACK_TOPIC,
            CommonTopicEnum.CATALOG_REFRESH_TOPIC,
        ].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handleMessage(@Payload() eventData: any, @Ctx() context: KafkaContext): Promise<any> {
        const topic = context.getTopic();
        return newrelic.startBackgroundTransaction(`Kafka Consume: ${topic}`, true, async () => {
            const transaction = newrelic.getTransaction();
            const originalMessage = context.getMessage();
            const headers = originalMessage.headers;
            newrelic.getTransaction()?.insertDistributedTraceHeaders(headers);

            // const eventData = JSON.parse(message) as CommonEvent;
            try {
                try {
                    await context.getHeartbeat();
                    // Handle event here
                    await this._automationDataService.handleEvent(eventData);
                } catch (error) {
                    this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                    this._loggerService.error(error);
                }

                switch (topic) {
                    case TransactionTopicEnum.FORM_TRANSACTION_COLLECTION_TOPIC:
                        try {
                            await context.getHeartbeat();
                            this._mqttService
                                .publish(
                                    `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${eventData.payload.id}`,
                                    JSON.stringify({
                                        id: eventData.id,
                                        aggregateId: eventData.aggregateId,
                                        payload: {
                                            id: eventData.payload.id,
                                            collectionId: eventData.payload.collectionId,
                                            sourceOfChangeType: eventData.payload.sourceOfChangeType,
                                        },
                                        metadata: eventData.metadata,
                                    }),
                                )
                                .catch((error) => {
                                    console.error(`Failed to publish MQTT message: ${error}`);
                                });
                            this._loggerService.info('Published event to MQTT: ' + eventData.metadata.type);
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC:
                        try {
                            let fieldsPayload;
                            if (eventData?.payload?.sourceFunction === 'detectAndPublishFieldChangedEvent') {
                                // Don't push collection fields to MQTT from detectAndPublishFieldChangedEvent
                                const populatedFields = uniq(compact(eventData?.payload?.populatedFields));
                                const formFieldIds = uniq(compact(eventData?.payload?.fields.map((item) => item.id)));
                                fieldsPayload = convertFieldsPayloadWithChunk(eventData, (fields) =>
                                    // fields.filter((field) => !field?.data?.collectionItemExternalId),
                                    fields.filter((field) => {
                                        if (formFieldIds.includes(field.id)) return true;
                                        else if (!populatedFields.includes(field.id)) return true;
                                        return false;
                                    }),
                                );
                            } else {
                                fieldsPayload = convertFieldsPayloadWithChunk(eventData);
                            }
                            for (const payload of fieldsPayload) {
                                await context.getHeartbeat();
                                this._mqttService
                                    .publish(
                                        `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${eventData.payload.id}`,
                                        payload,
                                    )
                                    .catch((error) => {
                                        console.error(`Failed to publish MQTT message: ${error}`);
                                    });
                            }

                            this._loggerService.info('Published event to MQTT: ' + eventData.metadata.type);

                            const currentVal = omit(eventData.payload, ['previous', 'fields']);
                            const changeLogData: TransactionRecordChangeLogMessageDto = {
                                current: {
                                    ...currentVal,
                                    fields: compact(eventData.payload.fields),
                                },
                                previous: {
                                    ...currentVal,
                                    fields: compact(eventData.payload.previous),
                                },
                                actionType: null,
                                sourceOfChange:
                                    eventData?.payload?.sourceOfChange ??
                                    (eventData?.metadata?.source === 'user' ? SourceOfChangeType.MANUAL : SourceOfChangeType.AUTOMATION),
                            };
                            const metadata: Record<string, any> = {};

                            // All about collection fields
                            switch (eventData.metadata.type) {
                                case TransactionEventEnum.FORM_TRANSACTION_FIELD_CREATED:
                                    changeLogData.actionType = ChangeLogActionType.ADDED;
                                    changeLogData.previous = null;
                                    metadata.createdAt = eventData?.payload?.fields[0]?.createdAt;
                                    metadata.createdBy = eventData?.payload?.fields[0]?.createdBy;
                                    metadata.createdByUser = eventData?.payload?.fields[0]?.createdByUser;
                                    break;
                                case TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED:
                                    // TODO: LS-1509
                                    // Maybe we need to use status of transaction for this case
                                    // wait for the transaction to be completed
                                    // push the change log
                                    changeLogData.actionType = ChangeLogActionType.UPDATED;
                                    metadata.updatedAt = eventData?.payload?.fields[0]?.updatedAt;
                                    metadata.updatedBy = eventData?.payload?.fields[0]?.updatedBy;
                                    metadata.updatedByUser = eventData?.payload?.fields[0]?.updatedByUser;
                                    break;
                                case TransactionEventEnum.FORM_TRANSACTION_FIELD_DELETED:
                                    changeLogData.actionType = ChangeLogActionType.DELETED;
                                    metadata.deletedAt = eventData?.payload?.fields[0]?.deletedAt;
                                    metadata.deletedBy = eventData?.payload?.fields[0]?.deletedBy;
                                    metadata.deletedByUser = eventData?.payload?.fields[0]?.deletedByUser;
                                    break;
                            }

                            if (changeLogData.actionType) {
                                await context.getHeartbeat();
                                await this._formTransactionDataService.addTransactionRecordChangeLog({
                                    data: changeLogData,
                                    objectType: TransactionDataType.FORM_TRANSACTION,
                                    subObjectType: TransactionDataType.FORM_TRANSACTION_FIELD,
                                    accountId: eventData?.metadata?.tenantId,
                                    metadata,
                                });
                                await context.getHeartbeat();
                            }
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case TransactionTopicEnum.FORM_TRANSACTION_STAGE_TOPIC:
                        try {
                            const payload: TransactionEntity = eventData.payload;

                            try {
                                const service = await this._dataSourceService.resolveService(
                                    eventData?.metadata?.tenantId,
                                    FormTransactionDataService,
                                );

                                await service.updateStageEnteredAt(payload.id);
                            } catch (error) {
                                this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                                this._loggerService.error(error);
                                // continue to publish event to MQTT
                            }

                            this._mqttService.publish(
                                `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${eventData.payload.id}`,
                                JSON.stringify({
                                    id: eventData.id,
                                    aggregateId: eventData.aggregateId,
                                    payload: {
                                        id: payload.id,
                                        stageId: payload.stageId,
                                        formVersionId: payload.formVersionId,
                                    },
                                    metadata: eventData.metadata,
                                }),
                            );
                            this._loggerService.info('Published event to MQTT: ' + eventData.metadata.type);

                            if (eventData?.metadata?.source !== 'user') {
                                const changeLogData: TransactionRecordChangeLogMessageDto = {
                                    current: {
                                        id: payload.id,
                                        fields: [
                                            {
                                                fieldId: FROM_STAGE_FIELD,
                                                stageId: payload.stageId,
                                                fieldValue: payload.stageName,
                                                prevStageId: payload.previousStageId,
                                                prevFieldValue: payload.previousStageName,
                                            },
                                        ],
                                        stageId: payload.stageId,
                                        stageName: payload.stageName,
                                        previousStageId: payload.previousStageId,
                                        previousStageName: payload.previousStageName,
                                    },
                                    previous: {
                                        stageId: payload.previousStageId,
                                        stageName: payload.previousStageName,
                                        fields: [
                                            {
                                                fieldId: FROM_STAGE_FIELD,
                                                stageId: payload.previousStageId,
                                                fieldValue: payload.previousStageName,
                                            },
                                        ],
                                    },
                                    actionType: ChangeLogActionType.UPDATED,
                                    sourceOfChange: SourceOfChangeType.AUTOMATION,
                                };

                                const metadata: Record<string, any> = {};
                                metadata.updatedAt = eventData?.payload?.updatedAt;
                                metadata.updatedBy = eventData?.payload?.updatedBy;
                                metadata.updatedByUser = eventData?.payload?.updatedByUser;

                                await this._formTransactionDataService.addTransactionRecordChangeLog({
                                    data: changeLogData,
                                    objectType: TransactionDataType.FORM_TRANSACTION,
                                    subObjectType: TransactionDataType.FORM_TRANSACTION,
                                    accountId: eventData?.metadata?.tenantId,
                                    metadata,
                                });

                                this._loggerService.info('Add Form Transaction Stage Change Log: ' + JSON.stringify(changeLogData));
                            }

                            const formVersionId = eventData?.payload?.formVersionId;
                            const stageId = eventData?.payload?.stageId;
                            if (formVersionId && stageId) {
                                const message = EventDrivenService.createCommonEvent({
                                    payload: {
                                        actionId: '539efd73-0824-4e2d-93d4-67ef038835a1',
                                        functionType: AutomationActionFunctionType.UPDATE_TRANSACTION_FIELD,
                                        configuration: {
                                            formFields: [
                                                {
                                                    fieldId: DEFAULT_STAGE_KPI_FIELD_ID,
                                                    fieldValue: 'OK',
                                                    validationValue: DATA_PASSED_CODE,
                                                },
                                            ],
                                        },
                                        context: {
                                            type: TransactionEventEnum.FORM_STAGE_KPI_STATUS_CHANGED,
                                            id: eventData?.payload?.formId,
                                            versionId: eventData?.payload?.formVersionId,
                                            transactionId: eventData?.aggregateId,
                                        },
                                        actionName: '539efd73-0824-4e2d-93d4-67ef038835a1',
                                    },
                                    aggregateId: eventData?.aggregateId,
                                    type: ActionEventEnum.UPDATE_TRANSACTION_FIELD,
                                    name: ActionEventEnum.UPDATE_TRANSACTION_FIELD,
                                });
                                await this._eventDrivenService.publishMessage(CommonTopicEnum.ACTION_TOPIC, message);
                                const service = await this._dataSourceService.resolveService(
                                    eventData?.metadata?.tenantId,
                                    FormTransactionDataService,
                                );
                                const formVersion = await service.getFormByVersion({ formVersionId });
                                if (formVersion && formVersion.stages) {
                                    const currentStageConfig = formVersion.stages.find((item) => item.id === stageId);
                                    if (currentStageConfig) {
                                        const delays = UtilsService.getWfsKpiSetting(currentStageConfig.config) || [];
                                        await this._scheduleWfsKpiStatusService.createJobs(
                                            {
                                                tenantId: eventData?.metadata?.tenantId,
                                                source: 'automation',
                                                transactionId: eventData?.payload?.id,
                                                formId: eventData?.payload?.formId,
                                                formVersionId: formVersionId,
                                            },
                                            delays,
                                        );
                                    }
                                }
                            }
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC:
                        switch (eventData.metadata.type) {
                            case TransactionEventEnum.FORM_TRANSACTION_ROLLUP_STATUS_FAILED:
                            case TransactionEventEnum.FORM_TRANSACTION_ROLLUP_STATUS_COMPLETED:
                            case TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED:
                            case TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_COMPLETED:
                            case TransactionEventEnum.FORM_TRANSACTION_UPDATED:
                                eventData.payload.fields = [];
                                eventData.payload.tranFields = [];
                                this._mqttService.publish(
                                    `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${eventData.payload.id}`,
                                    convertFieldsPayload(eventData),
                                );
                                this._loggerService.info('Published event to MQTT: ' + eventData.metadata.type);

                                await this._transactionCommittedService.handleEvent(eventData);

                                if (eventData?.metadata?.tenantId && eventData.payload.id) {
                                    // public an event for parent transaction to refetch data in the Relation Transactions Zone
                                    const formTransactionDataService = await this._dataSourceService.resolveService(
                                        eventData?.metadata?.tenantId,
                                        FormTransactionDataService,
                                    );
                                    const parentTransactions = await formTransactionDataService.getParentTransactionId(
                                        eventData.payload.id,
                                    );
                                    const message = EventDrivenService.createCommonEvent({
                                        payload: eventData.payload,
                                        aggregateId: eventData.aggregateId,
                                        tenantId: eventData.metadata.tenantId,
                                        type: TransactionEventEnum.FORM_RELATION_TRANSACTION_UPDATED,
                                        name: TransactionEventEnum.FORM_RELATION_TRANSACTION_UPDATED,
                                    });
                                    if (parentTransactions.length > 0) {
                                        for (const parentTransaction of parentTransactions) {
                                            console.log('parentTransaction: ', parentTransaction.targetTransactionId);
                                            await context.getHeartbeat();
                                            this._mqttService.publish(
                                                `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${parentTransaction.originTransactionId}`,
                                                convertFieldsPayload(message),
                                            );
                                        }
                                    }
                                }
                                break;
                            case TransactionEventEnum.FORM_RELATION_TRANSACTION_CREATED:
                                await this._mqttService.publish(
                                    `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_RELATION_TRANSACTION_CREATED}/${eventData.payload.id}`,
                                    JSON.stringify({
                                        id: eventData.id,
                                        aggregateId: eventData.aggregateId,
                                        payload: eventData.payload,
                                        metadata: eventData.metadata,
                                    }),
                                );
                                break;
                        }
                        break;
                    case TransactionTopicEnum.FORM_TRANSACTION_TOPIC:
                        try {
                            switch (eventData.metadata.type) {
                                case TransactionEventEnum.FORM_TRANSACTION_UPDATE:
                                    {
                                        const service = await this._dataSourceService.resolveService(
                                            eventData?.metadata?.tenantId,
                                            FormTransactionDataService,
                                        );
                                        if (eventData?.payload?.fromRequest) {
                                            RequestContextService.source = 'user';
                                        }
                                        await service.update(eventData.payload);
                                    }
                                    break;
                                case TransactionEventEnum.FORM_STAGE_KPI_STATUS_CHANGED: {
                                    const message = EventDrivenService.createCommonEvent({
                                        payload: {
                                            actionId: '539efd73-0824-4e2d-93d4-67ef038835a1',
                                            functionType: AutomationActionFunctionType.UPDATE_TRANSACTION_FIELD,
                                            configuration: {
                                                formFields: [
                                                    {
                                                        fieldId: DEFAULT_STAGE_KPI_FIELD_ID,
                                                        fieldValue: eventData?.payload?.kpiStatusId,
                                                        validationValue: eventData?.payload?.kpiStatus,
                                                    },
                                                ],
                                            },
                                            context: {
                                                type: TransactionEventEnum.FORM_STAGE_KPI_STATUS_CHANGED,
                                                id: eventData?.payload?.formId,
                                                versionId: eventData?.payload?.formVersionId,
                                                transactionId: eventData?.payload?.transactionId,
                                            },
                                            actionName: '539efd73-0824-4e2d-93d4-67ef038835a1',
                                        },
                                        aggregateId: eventData?.aggregateId,
                                        type: ActionEventEnum.UPDATE_TRANSACTION_FIELD,
                                        name: ActionEventEnum.UPDATE_TRANSACTION_FIELD,
                                    });
                                    await this._eventDrivenService.publishMessage(CommonTopicEnum.ACTION_TOPIC, message);
                                    break;
                                }
                            }
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    // case TransactionTopicEnum.FORM_TRANSACTION_AUTO_CREATION:
                    //     if (eventData.metadata.type === TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION) {
                    //         const eventDto = eventData.payload as AutoCreationEventDto;
                    //         await this._transactionAutoCreationListener.onAutoCreateTransaction(eventDto);
                    //     }
                    //     break;
                    case TransactionTopicEnum.FORM_TRANSACTION_ROLLUP_TOPIC:
                        try {
                            const service = await this._dataSourceService.resolveService(
                                eventData?.metadata?.tenantId,
                                RollUpCalculationService,
                            );
                            const start = performance.now();
                            await service.calculateRollup(eventData.payload as RollUpCalculationRequest);
                            const end = performance.now();
                            this._loggerService.log(
                                `Execution Time :${eventData.payload.formId}: ${eventData.payload.transactionId} ${end - start} milliseconds`,
                            );
                        } catch (err) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(err);
                        }
                        break;
                    case DataRegisterTopicEnum.DATA_REGISTER_BUILDER_TOPIC:
                        try {
                            const type = eventData.metadata.type as DataRegisterEventEnum;
                            if (type === DataRegisterEventEnum.DATA_REGISTER_RELEASED) {
                                await context.getHeartbeat();

                                if (eventData?.metadata?.tenantId) {
                                    this._dataRegisterDataService.cacheActiveDataRegisterVersionBuilder({
                                        id: eventData?.payload?.id,
                                        accountId: eventData?.metadata?.tenantId,
                                    });
                                }

                                await this._dataRegisterDataService.processReleaseDataRegisterVersion({
                                    id: eventData?.payload?.id,
                                    accountId: eventData?.metadata?.tenantId,
                                });
                            }
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case CommonTopicEnum.ACTION_TOPIC:
                    case CommonTopicEnum.MDS_DATA_CALLBACK_TOPIC:
                        try {
                            await context.getHeartbeat();
                            // Handle event here
                            await this._actionDataService.handleEvent(eventData);
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case CommonTopicEnum.ROLLBACK_TOPIC:
                        try {
                            await context.getHeartbeat();
                            // Handle event here
                            await this._actionDataService.handleRollbackEvent(eventData);
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case AutomationTopicEnum.AUTOMATION_BUILDER_TOPIC:
                        try {
                            await context.getHeartbeat();
                            // Handle event here
                            if (eventData.payload.automationVersionId) {
                                await this._scheduleService.automationTriggerUpdate(
                                    eventData.payload.automationVersionId,
                                    eventData.metadata.tenantId,
                                );
                            }
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE:
                        context.getHeartbeat();
                        this._loggerService.info(`Received external document change event ${eventData.id}`);
                        break;
                    case TransactionTopicEnum.FORM_TRANSACTION_PDF_DOC_EXTRACTION_TOPIC:
                        try {
                            const pushingData = {
                                id: eventData.id,
                                aggregateId: eventData.aggregateId,
                                payload: { ...eventData.payload, status: 'extraction_processing' },
                                metadata: eventData.metadata,
                            };
                            await this._mqttService.publish(
                                `${eventData.metadata.tenantId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_DOC_PDF_EXTRACTION}/${eventData.payload.transactionId}`,
                                JSON.stringify(pushingData),
                            );

                            await context.getHeartbeat();
                            this._actionDataService.handleEvent(eventData);
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                    case CommonTopicEnum.CATALOG_REFRESH_TOPIC:
                        try {
                            await context.getHeartbeat();
                            await this._dataRegisterDataService.refreshDataRegister(eventData);
                        } catch (error) {
                            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
                            this._loggerService.error(error);
                        }
                        break;
                }
                // Cache event
                await this._cacheService.set(eventData.id, eventData, 1000 * 60 * 4);
            } catch (error) {
                newrelic.noticeError(error);
            } finally {
                transaction.end();
            }
        });
    }

    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [CommonTopicEnum.SCHEDULE_TOPIC].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handleSchedule(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<any> {
        const topic = context.getTopic();

        try {
            await context.getHeartbeat();
            // Handle event here
            await this._automationDataService.handleScheduledEvent(eventData, context);
        } catch (error) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error(error);
        }

        // Cache event
        await this._cacheService.set(eventData.id, eventData, 1000 * 60 * 4);
    }

    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [CommonTopicEnum.DATA_POPULATE_TOPIC].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handleDataPopulateMessage(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<any> {
        const topic = context.getTopic();
        try {
            await context.getHeartbeat();
            // Handle event here
            await this._dataPopulateService.handleEvent(eventData, context);
        } catch (error) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error(error);
        }

        await this._cacheService.set(eventData.id, eventData, 1000 * 60 * 4);
    }
    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [DataLakeTopicEnum.EXTERNAL_DOCUMENT_POPULATE_DATA].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handlePopulateDataFromExternalDocument(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<any> {
        const topic = context.getTopic();
        try {
            await context.getHeartbeat();
            // Handle event here
            await this._externalDocumentDataService.handleEvent(eventData, context);
        } catch (error) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error(error);
        }

        await this._cacheService.set(eventData.id, eventData, 1000 * 60 * 4);
    }

    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [DataRegisterTopicEnum.DATA_REGISTER_RECORD_CHANGE_LOG_TOPIC].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handleRecordChangeLog(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<any> {
        const topic = context.getTopic();
        const type = eventData.metadata.type as DataRegisterEventEnum;
        if (
            ![
                DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED,
                DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
                DataRegisterEventEnum.DATA_REGISTER_RECORD_DELETED,
            ].includes(type)
        ) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error('Invalid event type ' + type);
            return;
        }

        try {
            await context.getHeartbeat();
            switch (topic) {
                case DataRegisterTopicEnum.DATA_REGISTER_RECORD_CHANGE_LOG_TOPIC:
                    {
                        //create data register record change log
                        const changeLogData = eventData?.payload as TransactionRecordChangeLogMessageDto;
                        switch (type) {
                            case DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED: {
                                changeLogData.actionType = ChangeLogActionType.ADDED;
                                break;
                            }
                            case DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED: {
                                changeLogData.actionType = ChangeLogActionType.UPDATED;
                                break;
                            }
                            case DataRegisterEventEnum.DATA_REGISTER_RECORD_DELETED: {
                                changeLogData.actionType = ChangeLogActionType.DELETED;
                                break;
                            }

                            default:
                                break;
                        }
                        await this._dataRegisterDataService.addTransactionRecordChangeLog({
                            data: changeLogData,
                            objectType: TransactionDataType.REGISTER,
                            accountId: eventData?.metadata?.tenantId,
                        });
                    }
                    break;

                default:
                    break;
            }
        } catch (error) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error(error);
        }

        // Cache event
        await this._cacheService.set(eventData.id, eventData, 1000 * 60 * 4);
    }

    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handleDataRegisterRecordChange(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<any> {
        const type = eventData.metadata.type as DataRegisterEventEnum;
        const topic = context.getTopic();
        try {
            await context.getHeartbeat();
            // Handle event here
            await this._automationDataService.handleEvent(eventData);

            switch (type) {
                case DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED:
                    {
                        const message = EventDrivenService.createCommonEvent({
                            payload: {
                                actionId: eventData.payload.id,
                                functionType: ActionEventEnum.REFRESH_FROM_TRANSACTION_DATA,
                                actionName: ActionEventEnum.REFRESH_FROM_TRANSACTION_DATA,
                                context: {
                                    type: AutomationContextType.DataRegister,
                                    id: eventData.payload.dataRegisterId,
                                    versionId: eventData.payload.dataRegisterVersionId,
                                    transactionId: eventData.payload.id,
                                },
                                external: {
                                    ...(eventData.payload ?? {}),
                                },
                            },
                            aggregateId: eventData.aggregateId,
                            tenantId: RequestContextService.accountId,
                            correlationId: nanoid(),
                            type: ActionEventEnum.REFRESH_FROM_TRANSACTION_DATA,
                            name: ActionEventEnum.REFRESH_FROM_TRANSACTION_DATA,
                        });
                        await this._eventDrivenService.publishMessage(CommonTopicEnum.ACTION_TOPIC, message);

                        if (eventData.payload.newDisplayValue) {
                            const message = EventDrivenService.createCommonEvent({
                                payload: {
                                    actionId: eventData.payload.id,
                                    functionType: ActionEventEnum.REFRESH_DATA_REGISTER_DISPLAY_VALUE,
                                    actionName: ActionEventEnum.REFRESH_DATA_REGISTER_DISPLAY_VALUE,
                                    context: {
                                        type: AutomationContextType.DataRegister,
                                        id: eventData.payload.dataRegisterId,
                                        versionId: eventData.payload.dataRegisterVersionId,
                                        transactionId: eventData.payload.id,
                                    },
                                    external: {
                                        ...(eventData.payload ?? {}),
                                    },
                                },
                                tenantId: RequestContextService.accountId,
                                aggregateId: eventData.aggregateId,
                                correlationId: nanoid(),
                                type: ActionEventEnum.REFRESH_DATA_REGISTER_DISPLAY_VALUE,
                                name: ActionEventEnum.REFRESH_DATA_REGISTER_DISPLAY_VALUE,
                            });

                            await this._eventDrivenService.publishMessage(CommonTopicEnum.ACTION_TOPIC, message);
                        }
                    }
                    break;

                default:
                    break;
            }
        } catch (error) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error(error);
        }
    }

    @UseInterceptors(ContextInterceptor)
    @MessagePattern(
        [
            TransactionTopicEnum.FORM_TRANSACTION_REFRESH_FORM_FIELDS_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_REFRESH_COLLECTION_FIELDS_TOPIC,
            TransactionTopicEnum.FORM_TRANSACTION_REFRESH_FORM_FIELDS_TOPIC_HIGHT_PRIORITY,
            TransactionTopicEnum.FORM_TRANSACTION_REFRESH_COLLECTION_FIELDS_TOPIC_HIGHT_PRIORITY,
        ].filter((topic) => {
            if (!KAFKA_TOPIC) return topic;
            return KAFKA_TOPIC & TopicMapping[topic];
        }),
        Transport.KAFKA,
    )
    async handleTransactionRefreshOne(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<any> {
        console.log('handleTransactionRefreshOne: start');
        console.time('handleTransactionRefreshOne');
        const topic = context.getTopic();
        try {
            await context.getHeartbeat();
            await this._actionDataService.handleEvent(eventData);
        } catch (error) {
            this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
            this._loggerService.error(error);
        }
        console.log('handleTransactionRefreshOne: end');
        console.timeEnd('handleTransactionRefreshOne');
    }

    // @UseInterceptors(ContextInterceptor)
    // @MessagePattern([FormVersionTopicEnum.ACTIVE_FORM_VERSION_TOPIC], Transport.KAFKA)
    // async handleFormVersionRelease(@Payload() eventData: CommonEvent, @Ctx() context: KafkaContext): Promise<void> {
    //     const topic = context.getTopic();
    //     try {
    //         await context.getHeartbeat();
    //     } catch (error) {
    //         this._loggerService.error(`${topic} - error event id: ${eventData.id}. Follow is details:`);
    //         this._loggerService.error(error);
    //     }
    // }
}
