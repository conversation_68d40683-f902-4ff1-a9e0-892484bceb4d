import { Injectable } from '@nestjs/common';
import { KafkaContext } from '@nestjs/microservices';
import { cloneDeep, compact, uniq } from 'lodash';
import { DataRegisterTransactionFieldOverrideEntity } from 'src/database/src/entities/public/data-register-transaction-field-override.public.entity';
import { DataRegisterTransactionFieldOverrideTenancyEntity } from 'src/database/src/entities/tenancy/data-register-transaction-field-override.tenancy.entity';
import { DataSource, In, Not, Repository } from 'typeorm';
import { LoggerService } from '../../../common/src';
import { AP_TARGET_ITSELF_FIELD_ID, EXTERNAL_DATA_SOURCES } from '../../../common/src/constant/field';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { DataPopulateEventEnum } from '../../../common/src/modules/shared/enums/event-driven/data-populate-event.enum';
import { DataRegisterTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/data-register-topic.enum';
import { DataProviderTypes } from '../../../database/src/constants/data-providers';
import { DataRegisterAdditionalFieldEntity } from '../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { DataRegisterAutoPopulateContextEntity } from '../../../database/src/entities/public/data-register-auto-populate-contexts.public.entity';
import { DataRegisterFieldEntity } from '../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldStyleEntity } from '../../../database/src/entities/public/data-register-transaction-field-style.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterAutoPopulateContextTenancyEntity } from '../../../database/src/entities/tenancy/data-register-auto-populate-contexts.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldStyleTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field-style.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { AutoPopulateDataSourceTypeEnum } from '../../../database/src/shared/enums/ap-data-source-type.enum';
import { DataRegisterEventEnum } from '../../../database/src/shared/enums/automation-event.enum';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldRequest, TransactionRequest } from '../../../shared/common/dto/get-populated-field-values.request';
import { GeneralAutoPopulateService } from '../../../shared/services';
import { CreateExtraDataRegisterService } from '../../data-register/services/data/create-extra-register.service';
import { DataRegisterTransactionDataService } from '../../data-register/services/data/data-register-transaction.data.service';
import { HandleActionResult } from '../action-handlers/common/action-types';
import { DataPopulateDto } from '../dtos/data-populate.dto';

@Injectable()
export class DataRegisterPopulateHandler {
    constructor(
        private readonly _logger: LoggerService,
        private readonly _dataSourceService: DataSourceService,
        private readonly _eventDrivenService: EventDrivenService,
    ) {}

    public async handleAction(actionDto: DataPopulateDto, context: KafkaContext): Promise<HandleActionResult> {
        const { metadata, payload } = actionDto ?? {};
        const transactionFields = payload?.transaction?.transactionFields ?? [];
        if (!transactionFields?.length) {
            this._logger.info(`Successfully triggered ${actionDto.contextId} but no settings were found`);
            return { type: 'TriggeredActionSuccess' };
        }

        try {
            const dataRepositories = await this.getRepositories(metadata?.tenantId);
            const fieldIds = compact(uniq([...transactionFields?.map((f) => f.fieldId), AP_TARGET_ITSELF_FIELD_ID]));
            const formSettings = await dataRepositories.formSettingRepository.find({
                where: { dataSourceId: actionDto.contextId, builderId: Not(actionDto.contextId), targetFieldId: In(fieldIds) },
            });

            return this.doAction(actionDto, context, formSettings, dataRepositories);
        } catch (error) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] ${payload.actionId} error ${error}`,
            };
        }
    }

    public async handleExternalAction(actionDto: DataPopulateDto, context: KafkaContext): Promise<HandleActionResult> {
        const { metadata, payload } = actionDto ?? {};
        try {
            const dataRepositories = await this.getRepositories(metadata?.tenantId);
            const filterDatasourceCodes = actionDto.contextSource ? [actionDto.contextSource] : [];
            if ([EXTERNAL_DATA_SOURCES.LLI_VESSEL].includes(actionDto.contextSource)) {
                actionDto.contextSource = 'vessel';
                filterDatasourceCodes.push('vessel');
            }
            const contextType = actionDto.contextType || actionDto.payload?.contextType;

            let formSettings = [];
            if (contextType === 'MDS') {
                formSettings = await dataRepositories.formSettingRepository.find({
                    where: {
                        dataSourceType: contextType as AutoPopulateDataSourceTypeEnum,
                        builderId: actionDto.payload?.dataRegisterId,
                    },
                });
            } else {
                formSettings = filterDatasourceCodes?.length
                    ? await dataRepositories.formSettingRepository.find({
                          where: {
                              dataSourceCode: filterDatasourceCodes?.length ? In(filterDatasourceCodes) : null,
                              dataSourceType: contextType as AutoPopulateDataSourceTypeEnum,
                          },
                      })
                    : [];
            }

            return this.doAction(actionDto, context, formSettings, dataRepositories, true);
        } catch (error) {
            return {
                type: 'TriggeredActionErrorGoTerminate',
                message: `[ACTION] ${payload.actionId} error ${error}`,
            };
        }
    }

    private async getRepositories(tenantId: string) {
        const dataSource = await this._dataSourceService.createAccountDataSource(tenantId);

        const dataRegisterTransactionRepository = tenantId
            ? dataSource.getRepository(DataRegisterTransactionTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionEntity);
        const formSettingRepository = tenantId
            ? dataSource.getRepository(GeneralAutoPopulateSettingTenancyEntity)
            : dataSource.getRepository(GeneralAutoPopulateSettingEntity);
        const dataRegisterRepository = tenantId
            ? dataSource.getRepository(DataRegisterTenancyEntity)
            : dataSource.getRepository(DataRegisterEntity);
        const drTranFieldRepository = tenantId
            ? dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionFieldEntity);
        const dataRegisterFieldRepository = tenantId
            ? dataSource.getRepository(DataRegisterFieldTenancyEntity)
            : dataSource.getRepository(DataRegisterFieldEntity);

        const dataRegisterAdditionalFieldRepository = tenantId
            ? dataSource.getRepository(DataRegisterAdditionalFieldTenancyEntity)
            : dataSource.getRepository(DataRegisterAdditionalFieldEntity);

        const dataRegisterAutoPopulateContextRepository = tenantId
            ? dataSource.getRepository(DataRegisterAutoPopulateContextTenancyEntity)
            : dataSource.getRepository(DataRegisterAutoPopulateContextEntity);

        const dataRegisterTransactionFieldStyleRepository = tenantId
            ? dataSource.getRepository(DataRegisterTransactionFieldStyleTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionFieldStyleEntity);

        const dataRegisterVersionRepository = tenantId
            ? dataSource.getRepository(DataRegisterVersionTenancyEntity)
            : dataSource.getRepository(DataRegisterVersionEntity);

        const dataRegisterTransactionFieldOverrideRepository = tenantId
            ? dataSource.getRepository(DataRegisterTransactionFieldOverrideTenancyEntity)
            : dataSource.getRepository(DataRegisterTransactionFieldOverrideEntity);

        const generalAutoPopulateSettingRepository = tenantId
            ? dataSource.getRepository(GeneralAutoPopulateSettingTenancyEntity)
            : dataSource.getRepository(GeneralAutoPopulateSettingEntity);

        return {
            dataSource,
            dataRegisterTransactionRepository,
            formSettingRepository,
            dataRegisterRepository,
            drTranFieldRepository,
            dataRegisterFieldRepository,
            dataRegisterAdditionalFieldRepository,
            dataRegisterAutoPopulateContextRepository,
            dataRegisterTransactionFieldStyleRepository,
            dataRegisterVersionRepository,
            dataRegisterTransactionFieldOverrideRepository,
            generalAutoPopulateSettingRepository,
        };
    }
    private async doAction(
        actionDto: DataPopulateDto,
        context: KafkaContext,
        settings: GeneralAutoPopulateSettingTenancyEntity[],
        dataRepositories: {
            dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>;
            formSettingRepository: Repository<GeneralAutoPopulateSettingTenancyEntity>;
            dataRegisterRepository: Repository<DataRegisterTenancyEntity>;
            drTranFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity>;
            dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity>;
            dataSource: DataSource;
            dataRegisterAdditionalFieldRepository: Repository<DataRegisterAdditionalFieldTenancyEntity>;
            dataRegisterAutoPopulateContextRepository: Repository<DataRegisterAutoPopulateContextTenancyEntity>;
            dataRegisterTransactionFieldStyleRepository: Repository<DataRegisterTransactionFieldStyleTenancyEntity>;
            dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity>;
            dataRegisterTransactionFieldOverrideRepository: Repository<DataRegisterTransactionFieldOverrideTenancyEntity>;
            generalAutoPopulateSettingRepository: Repository<GeneralAutoPopulateSettingTenancyEntity>;
        },
        isExternal = false,
    ): Promise<HandleActionResult> {
        const { metadata, payload } = actionDto ?? {};
        let transactionFields = payload?.transaction?.transactionFields ?? [];

        if (isExternal) {
            transactionFields = Object.keys(payload?.transaction ?? {}).map((fieldId) => {
                return {
                    fieldId: fieldId,
                    fieldValue: payload?.transaction[fieldId],
                };
            });
        }

        const fieldIds: string[] = [];
        const tranFieldIds = transactionFields.map((field) => field.fieldId);
        settings.forEach((item) => {
            if (tranFieldIds.includes(item.targetFieldId) || item.targetFieldId === AP_TARGET_ITSELF_FIELD_ID) {
                fieldIds.push(item.fieldId);
            }
        });
        const { drTranFieldRepository, dataRegisterRepository, dataRegisterTransactionRepository } = dataRepositories;
        if (!settings?.length) {
            this._logger.info(`Successfully triggered ${actionDto.contextId} but no settings were found`);
            return { type: 'TriggeredActionSuccess' };
        }

        let updatedResult: DataRegisterTransactionTenancyEntity[] = [];
        const builderIds = uniq(settings.map((item) => item.builderId));
        const dataRegisters = await dataRegisterRepository.findBy({ id: In(builderIds) });

        let dataRegisterTransactions = [];
        if (isExternal) {
            dataRegisterTransactions = await dataRegisterTransactionRepository.find({
                where: { dataRegisterId: In(builderIds), externalId: actionDto.contextId },
            });
        } else {
            dataRegisterTransactions = await dataRegisterTransactionRepository.find({
                where: { dataRegisterId: In(builderIds) },
            });
        }

        const tranIds = dataRegisterTransactions?.map((item) => item.id);
        if (tranIds?.length) {
            const tranFields = await drTranFieldRepository.findBy({ dataRegisterTransactionId: In(tranIds) });
            dataRegisterTransactions.forEach((item) => {
                item.transactionFields = tranFields?.filter((t) => t.dataRegisterTransactionId === item.id) ?? [];
            });

            if (!isExternal) {
                dataRegisterTransactions = dataRegisterTransactions.filter((item) => {
                    if (item.transactionFields?.length) {
                        return item.transactionFields.some((t) => t?.fieldOptionIds?.includes(payload.contextTransactionId));
                    }
                    return false;
                });
            }
        }

        const previousResult = cloneDeep(dataRegisterTransactions);

        for (const dataRegister of dataRegisters) {
            const transactions = dataRegisterTransactions.filter((tran) => tran.dataRegisterId === dataRegister.id);
            if (!transactions.length) {
                continue;
            }

            if (!dataRegister.activeVersionId) {
                continue;
            }

            const registerFields = await dataRepositories.dataRegisterFieldRepository.find({
                where: { dataRegisterVersionId: dataRegister.activeVersionId },
                select: ['id', 'fieldId', 'configuration', 'type', 'lookupTargetId', 'label'],
            });

            const result = await this.processDrTransaction({
                tenantId: metadata?.tenantId,
                dataRegister,
                dataRegisterTransactions: transactions,
                changedFieldIds: uniq(fieldIds),
                isExternal,
                registerFields,
                dataRepositories: dataRepositories,
            });
            updatedResult = [...updatedResult, ...(result ?? [])];
        }

        if (!updatedResult.length) {
            this._logger.info(`Successfully triggered ${actionDto.contextId} but no transaction updated`);
            return { type: 'TriggeredActionSuccess' };
        }

        await this.processNextActions(updatedResult, previousResult, context, metadata?.tenantId);

        this._logger.info(`Successfully triggered ${actionDto.contextId} but no transaction updated`);
        return { type: 'TriggeredActionSuccess' };
    }

    private async processDrTransaction({
        tenantId,
        dataRegister,
        dataRegisterTransactions,
        changedFieldIds,
        isExternal,
        registerFields,
        dataRepositories,
    }: {
        tenantId: string;
        dataRegister: DataRegisterTenancyEntity;
        dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
        changedFieldIds: string[];
        isExternal: boolean;
        registerFields: Pick<DataRegisterFieldTenancyEntity, 'id' | 'fieldId' | 'configuration' | 'type' | 'lookupTargetId' | 'label'>[];
        dataRepositories: {
            dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>;
            formSettingRepository: Repository<GeneralAutoPopulateSettingTenancyEntity>;
            dataRegisterRepository: Repository<DataRegisterTenancyEntity>;
            drTranFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity>;
            dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity>;
            dataSource: DataSource;
            dataRegisterAdditionalFieldRepository: Repository<DataRegisterAdditionalFieldTenancyEntity>;
            dataRegisterAutoPopulateContextRepository: Repository<DataRegisterAutoPopulateContextTenancyEntity>;
            dataRegisterTransactionFieldStyleRepository: Repository<DataRegisterTransactionFieldStyleTenancyEntity>;
            dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity>;
            dataRegisterTransactionFieldOverrideRepository: Repository<DataRegisterTransactionFieldOverrideTenancyEntity>;
            generalAutoPopulateSettingRepository: Repository<GeneralAutoPopulateSettingTenancyEntity>;
        };
    }): Promise<DataRegisterTransactionTenancyEntity[]> {
        const activeVersionId = dataRegister?.activeVersionId;
        const populateService = await this._dataSourceService.resolveService<GeneralAutoPopulateService>(
            tenantId,
            GeneralAutoPopulateService,
        );

        const createtExtraRegisterService = await this._dataSourceService.resolveService<CreateExtraDataRegisterService>(
            tenantId,
            CreateExtraDataRegisterService,
        );

        const drTransactionDataService = await this._dataSourceService.resolveService<DataRegisterTransactionDataService>(
            tenantId,
            DataRegisterTransactionDataService,
        );

        const registerFieldMap = (registerFields || []).reduce((acc, item) => {
            acc.set(item.fieldId, item);
            return acc;
        }, new Map<string, (typeof registerFields)[number]>());

        const updatedTrans: DataRegisterTransactionTenancyEntity[] = [];
        const mdsIdToRegisterIdMap = new Map<string, string>();

        for (const tran of dataRegisterTransactions) {
            const transactionFields = tran.transactionFields;
            const mdsId = tran.externalId;

            const data = await populateService.getPopulatedDataRegisterFieldValues({
                formVersionId: activeVersionId,
                fieldChangeId: '',
                transactionId: tran.id,
                transaction: {
                    mdsId: mdsId,
                    transactionFields: transactionFields?.map((item) => {
                        return {
                            fieldId: item.fieldId,
                            fieldValue: item.fieldValue,
                            fieldType: item.fieldType,
                            fieldOptionIds: item.fieldOptionIds,
                        } as TransactionFieldRequest;
                    }),
                } as TransactionRequest,
                isLabelingAsValue: true,
                internalUpdate: !isExternal,
            });

            const updatedTranFields: DataRegisterTransactionFieldTenancyEntity[] = [];

            const metadataResult = data?.metadataResult;
            const lookupExternalFields = (metadataResult?.populatedExternalFields || []).filter((f) => {
                const registerField = registerFieldMap.get(f.fieldId);
                return registerField?.type === FormFieldTypeEnum.Lookup;
            });

            for (const [fieldId, fieldValue] of Object.entries((data?.['populateValues'] ?? {}) as Record<string, any>)) {
                if (!changedFieldIds.includes(fieldId)) {
                    continue;
                }

                const versionTranField = transactionFields.find((field) => field.fieldId === fieldId);
                const lookupExternalField = lookupExternalFields.find((item) => item.fieldId === fieldId);

                if (isExternal && versionTranField && lookupExternalField) {
                    const mdsId = lookupExternalField.fieldValue;
                    const registerField = registerFieldMap.get(fieldId);
                    const targetRegisterId = registerField?.configuration?.targetId || registerField.lookupTargetId;
                    if (targetRegisterId && mdsId && !mdsIdToRegisterIdMap.has(mdsId)) {
                        const register = await dataRepositories.dataRegisterTransactionRepository.findOne({
                            where: {
                                dataRegisterId: targetRegisterId,
                                externalId: mdsId,
                            },
                        });

                        if (!register) {
                            const dr = await dataRepositories.dataRegisterRepository.findOne({
                                where: {
                                    id: targetRegisterId,
                                },
                                select: ['id', 'type'],
                            });

                            const registerType = dr?.type;
                            if (DataProviderTypes.includes(registerType)) {
                                const newRegisterRequest = await createtExtraRegisterService.create({
                                    registerId: targetRegisterId,
                                    externalId: mdsId,
                                    dataRegisterRepository: dataRepositories.dataRegisterRepository,
                                    dataRegisterFieldRepository: dataRepositories.dataRegisterFieldRepository,
                                });

                                if (newRegisterRequest) {
                                    const newRegisterId = await drTransactionDataService.create({
                                        request: newRegisterRequest,
                                        dataRegisterRepository: dataRepositories.dataRegisterRepository,
                                        dataRegisterTransactionRepository: dataRepositories.dataRegisterTransactionRepository,
                                        dataRegisterTransactionFieldRepository: dataRepositories.drTranFieldRepository,
                                        dataRegisterAdditionalFieldRepository: dataRepositories.dataRegisterAdditionalFieldRepository,
                                        dataRegisterAutoPopulateContextRepository:
                                            dataRepositories.dataRegisterAutoPopulateContextRepository,
                                        dataRegisterTransactionFieldStyleRepository:
                                            dataRepositories.dataRegisterTransactionFieldStyleRepository,
                                        dataRegisterFieldRepository: dataRepositories.dataRegisterFieldRepository,
                                        accountId: tenantId,
                                        dataSource: dataRepositories.dataSource,
                                        sourceOfChange: SourceOfChangeType.AUTO_POPULATE,
                                        dataRegisterVersionRepository: dataRepositories.dataRegisterVersionRepository,
                                        transactionFieldOverrideRepository: dataRepositories.dataRegisterTransactionFieldOverrideRepository,
                                        generalAutoPopulateSettingRepo: dataRepositories.generalAutoPopulateSettingRepository,
                                    });

                                    if (newRegisterId && typeof newRegisterId === 'string') {
                                        mdsIdToRegisterIdMap.set(mdsId, newRegisterId as string);
                                    }
                                }
                            }
                        } else {
                            mdsIdToRegisterIdMap.set(mdsId, register.id);
                        }
                    }

                    if (mdsIdToRegisterIdMap.has(mdsId)) {
                        const registerValue = mdsIdToRegisterIdMap.get(mdsId);
                        if (versionTranField.fieldValue != registerValue) {
                            if (versionTranField.fieldOptionIds?.length) {
                                const registerValueOptionIds = registerValue?.split(',') || [];

                                const isOriginIncludedInTarget = registerValueOptionIds.every((id) =>
                                    versionTranField.fieldOptionIds.includes(id),
                                );
                                const isTargetIncludedInOrigin = versionTranField.fieldOptionIds.every((id) =>
                                    registerValueOptionIds.includes(id),
                                );
                                const check = isOriginIncludedInTarget || isTargetIncludedInOrigin;

                                if (check) {
                                    continue;
                                }

                                versionTranField.fieldOptionIds = registerValueOptionIds;
                                versionTranField.fieldValue = registerValue as string;
                                updatedTranFields.push(versionTranField);
                            } else {
                                versionTranField.fieldValue = registerValue as string;
                                updatedTranFields.push(versionTranField);
                            }
                        }
                    }

                    continue;
                }

                if (versionTranField.fieldValue != fieldValue.fieldValue) {
                    if (fieldValue?.fieldOptionIds?.length) {
                        versionTranField.fieldOptionIds = fieldValue.fieldOptionIds;
                    }
                    versionTranField.fieldValue = fieldValue?.fieldValue as string;
                    updatedTranFields.push(versionTranField);
                }
            }

            if (updatedTranFields?.length) {
                updatedTrans.push({
                    id: tran.id,
                    dataRegisterId: tran.dataRegisterId,
                    dataRegisterVersionId: tran.dataRegisterVersionId,
                    transactionFields: updatedTranFields,
                });
            }
        }

        if (!updatedTrans.length) {
            return [];
        }
        const updateTasks = [];
        updatedTrans.forEach((item) => {
            item.transactionFields.forEach((field) => {
                updateTasks.push(dataRepositories.drTranFieldRepository.update({ id: field.id }, field));
            });
        });
        const _ = await Promise.all(updateTasks);
        return updatedTrans;
    }

    private async processNextActions(
        data: DataRegisterTransactionTenancyEntity[],
        previousData: DataRegisterTransactionTenancyEntity[],
        context: KafkaContext,
        tenantId: string,
    ) {
        for (const updatedTran of data) {
            await context.getHeartbeat();
            await this.triggerDataRegisterChanged(updatedTran, tenantId);
            await this.triggerAutomation(updatedTran, tenantId);
            const preTransaction = previousData?.find((item) => item.id === updatedTran.id);
            await this.triggerLogs(preTransaction, updatedTran, tenantId);
        }
    }

    private async triggerDataRegisterChanged(data: DataRegisterTransactionTenancyEntity, tenantId: string) {
        if (data?.transactionFields?.length) {
            const dataPopulateMessage = EventDrivenService.createCommonEvent({
                payload: {
                    contextId: data.dataRegisterId,
                    contextVersionId: data.dataRegisterVersionId,
                    contextTransactionId: data.id,
                    transaction: data,
                },
                aggregateId: data.id,
                tenantId: tenantId,
                type: DataPopulateEventEnum.DATA_REGISTER_FIELD_CHANGED,
                name: DataPopulateEventEnum.DATA_REGISTER_FIELD_CHANGED,
            });
            await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC, dataPopulateMessage);
        }
    }

    private async triggerAutomation(data: DataRegisterTransactionTenancyEntity, tenantId: string) {
        const message = EventDrivenService.createCommonEvent({
            payload: data,
            aggregateId: data.id,
            tenantId: tenantId,
            type: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
            name: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
        });
        await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC, message);
    }

    private async triggerLogs(
        previousData: DataRegisterTransactionTenancyEntity,
        currentData: DataRegisterTransactionTenancyEntity,
        tenantId: string,
    ) {
        const message = EventDrivenService.createCommonEvent({
            payload: {
                previous: previousData ?? {},
                current: currentData ?? {},
                sourceOfChange: SourceOfChangeType.AUTO_POPULATE,
            },
            aggregateId: currentData.id,
            tenantId: tenantId,
            type: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
            name: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
        });
        await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_CHANGE_LOG_TOPIC, message);
    }
}
