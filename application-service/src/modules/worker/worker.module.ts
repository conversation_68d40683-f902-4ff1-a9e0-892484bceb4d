import { HttpModule } from '@nestjs/axios';
import { Mo<PERSON><PERSON>, Provider } from '@nestjs/common';

import { KafkaProcessor } from './processors/kafka.processor';
import { ActionDataService } from './services/action.data.service';
import { AutomationDataService } from './services/automation.data.service';
import { DataRegisterDataService } from './services/data-register.data.service';
import { TransactionAutoCreationListener } from './services/related-transaction-auto-creation.listener';
import { TransactionCreationService } from './services/transaction-creation.service';

import { SharedModule } from '../../shared/shared.module';
import { DataRegisterModule } from '../data-register/data-register.module';

import { JwtService } from '@nestjs/jwt';
import { ActionLogModule, EventDrivenModule } from '../../common/src';
import { DataSourceService } from '../../database/src/services/connection-util.service';
import { BullMQModule } from '../bull-mq/bull.module';
import { ScheduleWfsKpiStatusService } from '../bull-mq/services';
import { CrontabModule } from '../crontab/crontab.module';
import { DocumentModule } from '../document/document.module';
import { FileModule } from '../file/file.module';
import { FormModule } from '../form/form.module';
import { MailerModule } from '../mailer';
import { DataRegisterPopulateHandler } from './action-handlers';
import { ActionTriggerHandler } from './action-handlers/common/action-trigger.handler';
import { Q88HVPQActionHandler } from './action-handlers/external-actions/data-pipeline/q88-hvpq-action.handler';
import { ExportPdfActionHandler } from './action-handlers/external-actions/export-pdf-action.handler';
import { ExternalDocumentActionHandler } from './action-handlers/external-actions/external-document.handler';
import { WebhookActionHandler } from './action-handlers/external-actions/send-webhook.handler';
import { SendEmailActionHandler } from './action-handlers/external-actions/sent-mail.handler';
import { TransactionFromExternalDocumentActionHandler } from './action-handlers/external-actions/transaction-from-external-document.handler';
import { CreateOrUpdateRegisterActionHandler } from './action-handlers/register-actions/create-or-update-register.handler';
import { CreateRegisterRecordActionHandler } from './action-handlers/register-actions/create-register-record.handler';
import { CreateTransactionFromDatasourceActionHandler } from './action-handlers/register-actions/create-transaction-from-datasource.handler';
import { RollupRegisterRecordsActionHandler } from './action-handlers/register-actions/rollup-register-records.handler';
import { UpdateRegisterRecordActionHandler } from './action-handlers/register-actions/update-register-record.handler';
import { ChangeTransactionWFSActionHandler } from './action-handlers/transaction-actions/change-transaction-wfs.handler';
import { CreateRelatedTransactionActionHandler } from './action-handlers/transaction-actions/create-related-transaction.handler';
import { CreateTransactionActionHandler } from './action-handlers/transaction-actions/create-transaction.handler';
import { DocPdfExtractionHandler } from './action-handlers/transaction-actions/doc-pdf-extraction.handler';
import { FormTransactionRefreshCollectionFieldsService } from './action-handlers/transaction-actions/form-transaction-refresh-collection-fields.handler';
import { FormTransactionRefreshFormFieldsService } from './action-handlers/transaction-actions/form-transaction-refresh-form-fields.handler';
import { FormTransactionRefreshService } from './action-handlers/transaction-actions/form-transaction-refresh.handler';
import { PopulatePurpleTRACHandler } from './action-handlers/transaction-actions/populate-purpletrac.handler';
import { PopulateQ88Handler } from './action-handlers/transaction-actions/populate-Q88.handler';
import { RefreshDataRegisterDisplayValueHandler } from './action-handlers/transaction-actions/refresh-data-register-display-value.handler';
import { RollupTransactionsActionHandler } from './action-handlers/transaction-actions/rollup-transactions.handler';
import { RunLLMActionHandler } from './action-handlers/transaction-actions/run-llm.handler';
import { ScoringSystemHandler } from './action-handlers/transaction-actions/scoring-system.handler';
import { UpdateCollectionFieldActionHandler } from './action-handlers/transaction-actions/update-collection.handler';
import { UpdateTransactionActionHandler } from './action-handlers/transaction-actions/update-transaction.handler';
import { DataPopulateDataService, DocCmidDataService } from './services';
import { DocCrewDataService } from './services/doc-crew.data.service';
import { DocOvidViqDataService } from './services/doc-ovid-viq.service';
import { DocQ88DataService } from './services/doc-q88.service';
import { DocSire2ViqDataService } from './services/doc-sire2-viq.service';
import { ExternalDocumentDataService } from './services/external-document.data.service';
import { NoRegisteredUserService } from './services/no-register-user.data.service';
import { OpenAIService } from './services/pdf-extraction/openAi-service';
import { PdfExtractionService } from './services/pdf-extraction/pdf-extraction.data.service';
import { PurpleTRACDataService } from './services/purpleTRAC.data.service';
import { RegisterActionService } from './services/register-action.service';
import { RollupTransactionsDataService } from './services/rollup-transactions.data.service';
import { TransactionCommittedService } from './services/transaction-committed.service';
import { UpdateCollectionService } from './services/update-collection.service';
import { RefreshTransactionQueueHandlerService } from './services/refresh-transaction-queue-handler.service';
import { ProcessRefreshTransactionService } from './services/process-refresh-transaction.service';
import { RefreshTransactionQueueService } from './services/refresh-transaction-queue.service';

const repositories: Provider[] = [] as const;

const services: Provider[] = [
    DataRegisterDataService,
    TransactionAutoCreationListener,
    AutomationDataService,
    ActionDataService,
    TransactionCreationService,
    RegisterActionService,
    DataPopulateDataService,
    TransactionCommittedService,
    NoRegisteredUserService,
    FormTransactionRefreshService,
    FormTransactionRefreshFormFieldsService,
    FormTransactionRefreshCollectionFieldsService,
    OpenAIService,
    PdfExtractionService,
    DocCrewDataService,
    DocOvidViqDataService,
    PurpleTRACDataService,
    DocSire2ViqDataService,
    DocQ88DataService,
    UpdateCollectionService,
    RefreshDataRegisterDisplayValueHandler,
    RollupTransactionsDataService,
    ExternalDocumentDataService,
    DocCmidDataService,
    ProcessRefreshTransactionService,
    RefreshTransactionQueueHandlerService,
    RefreshTransactionQueueService,
] as const;

const actionHandlers: Provider[] = [
    ActionTriggerHandler,
    ChangeTransactionWFSActionHandler,
    CreateTransactionActionHandler,
    SendEmailActionHandler,
    UpdateTransactionActionHandler,
    CreateRegisterRecordActionHandler,
    UpdateRegisterRecordActionHandler,
    ExportPdfActionHandler,
    DataRegisterPopulateHandler,
    ExternalDocumentActionHandler,
    CreateOrUpdateRegisterActionHandler,
    DocPdfExtractionHandler,
    CreateRelatedTransactionActionHandler,
    UpdateCollectionFieldActionHandler,
    PopulatePurpleTRACHandler,
    ScoringSystemHandler,
    RollupTransactionsActionHandler,
    RollupRegisterRecordsActionHandler,
    CreateTransactionFromDatasourceActionHandler,
    TransactionFromExternalDocumentActionHandler,
    PopulateQ88Handler,
    Q88HVPQActionHandler,
    WebhookActionHandler,
    ScheduleWfsKpiStatusService,
    RunLLMActionHandler,
] as const;

const processors: Provider[] = [] as const;

@Module({
    imports: [
        HttpModule,
        FileModule,
        SharedModule,
        FormModule,
        DataRegisterModule,
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
        ActionLogModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
        MailerModule,
        DocumentModule,
        CrontabModule,
        BullMQModule,
    ],
    controllers: [KafkaProcessor],
    providers: [...processors, ...services, ...actionHandlers, ...repositories, JwtService],
})
export class WorkerModule {}
