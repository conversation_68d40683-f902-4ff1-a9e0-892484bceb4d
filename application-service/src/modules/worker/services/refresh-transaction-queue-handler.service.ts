import { Injectable } from '@nestjs/common';
import { RefreshTransactionQueueService } from './refresh-transaction-queue.service';
import { ParsedActionDto } from '../dtos/action.dto';
import { ConfigService, LoggerService } from '../../../common/src';
import { ProcessRefreshTransactionService } from './process-refresh-transaction.service';
import { DataSource } from 'typeorm';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { TENANT_HEADER } from '../../../common/src/modules/auth/constants/tenancy.constant';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { Cron, SchedulerRegistry } from '@nestjs/schedule';
import { JobConfig } from '../../crontab/dto/job.dto';
import { <PERSON>ronJob } from 'cron';
import { AccountEntity } from '../../../database/src/entities/public/account.public.entity';

@Injectable()
export class RefreshTransactionQueueHandlerService {
    private _registeredAccounts: string[] = [];

    constructor(
        private readonly _refreshQueue: RefreshTransactionQueueService,
        private readonly _loggerService: LoggerService,
        private readonly _refreshTransactionQueueService: RefreshTransactionQueueService,
        private readonly _processRefreshTransactionService: ProcessRefreshTransactionService,
        private readonly _moduleRef: ModuleRef,
        private readonly _schedulerRegistry: SchedulerRegistry,
        private readonly _configService: ConfigService,
    ) {}

    async onApplicationBootstrap() {
        await this._registerAccounts();
    }

    private async _registerAccounts() {
        const publicDataSource = await this._getDataSource();
        const accountRepo = publicDataSource.getRepository(AccountEntity);
        const accounts = await accountRepo.find();
        for (const account of accounts) {
            if (this._registeredAccounts.includes(account.id)) continue;
            await this._registerJob(account.id, 'form_fields');
            await this._registerJob(account.id, 'collection_fields');
            this._registeredAccounts.push(account.id);
        }
    }

    @Cron('*/30 * * * *') // Every 30 minutes
    private async registerNewAccounts() {
        await this._registerAccounts();
    }

    private _registerJob(accountId: string, prefix: string) {
        try {
            const time = this._configService.refreshTransaction?.coldIntervalTime ?? 5;
            const queueName = this._refreshTransactionQueueService.queueName(accountId, prefix);
            const jobConfig: JobConfig = {
                id: new Date().getTime(),
                job_name: `job_${queueName}`,
                cron_expression: `*/${time} * * * *`,
                context: {
                    queueName: queueName,
                },
            };
            const job = new CronJob(jobConfig.cron_expression, async () => {
                await this._start(accountId, prefix);
            });
            this._schedulerRegistry.addCronJob(jobConfig.job_name, job);
            job.start();
            this._loggerService.log(`Registered job: ${jobConfig.job_name}`);
        } catch (error) {
            this._loggerService.error(`Error registering job: ${error}`);
        }
    }

    private async _start(accountId: string, prefix: string) {
        const task = await this._refreshQueue.getNextTask(accountId, prefix);
        if (!task) return;
        this._loggerService.log(`RefreshTransactionQueueService started for account ${accountId}`);
        await this._processTask(task);
    }

    private async _processTask(task: Record<string, unknown>) {
        const { actionDto, transactionIds, user, refreshFor } = task;
        this._loggerService.log(`RefreshTransactionQueueService started for task ${JSON.stringify(transactionIds)}`);
        await this._processRefreshTransactionService.doRefresh(
            actionDto as ParsedActionDto,
            transactionIds as string[],
            user as Record<string, unknown>,
            refreshFor as 'form_fields' | 'collection_fields' | 'all',
        );
    }

    private async _getDataSource(accountId?: string): Promise<DataSource> {
        if (accountId) {
            const contextId = ContextIdFactory.create();
            this._moduleRef.registerRequestByContextId(
                {
                    headers: {
                        [TENANT_HEADER]: accountId,
                    },
                },
                contextId,
            );
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.TENANT_CONNECTION, contextId, { strict: false });
        } else {
            const contextId = ContextIdFactory.create();
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.DATA_SOURCE, contextId, { strict: false });
        }
    }
}
