import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { randomUUID } from 'crypto';
import { isEmpty } from 'lodash';
import { lastValueFrom } from 'rxjs';
import { DataSource, In } from 'typeorm';
import { CacheService, LoggerService, UtilsService } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { COMPANY_FIELD_ID, MDS_PACK_FIELDS, VESSEL_FIELD_ID } from '../../../common/src/constant/field';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { CommonTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { DataRegisterEventEnum } from '../../../common/src/modules/shared/enums/event-driven/data-register-event';
import { AUTO_POPULATE_USER_ID, AUTOMATION_USER_ID } from '../../../database/src/constants/change-log';
import { TENANT_HEADER } from '../../../database/src/constants/database-option.constant';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { AccountEntity } from '../../../database/src/entities/public/account.public.entity';
import { DataRegisterFieldEntity } from '../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldOverrideEntity } from '../../../database/src/entities/public/data-register-transaction-field-override.public.entity';
import { DataRegisterTransactionFieldStyleEntity } from '../../../database/src/entities/public/data-register-transaction-field-style.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { TransactionChangeLogEntity } from '../../../database/src/entities/public/transaction-change-log.public.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldOverrideTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field-override.tenancy.entity';
import { DataRegisterTransactionFieldStyleTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field-style.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { TransactionChangeLogTenancyEntity } from '../../../database/src/entities/tenancy/transaction-change-log.tenancy.entity';
import { ChangeLogActionType, SourceOfChangeType, TransactionDataType } from '../../../database/src/shared/enums/change-log.type.enum';
import { DataRegisterTypeEnum } from '../../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { DataRegisterWithActiveVersionService } from '../../../shared/services/data-register-with-active-version.service';
import { TransactionRecordChangeLogDto } from '../../data-register/dtos/data-register-transaction-change-log.dto';
import { DataRegisterTransactionDataService } from '../../data-register/services/data/data-register-transaction.data.service';
import { DataRegisterJobDto, TransactionRecordChangeLogMessageDto } from '../dtos/data-register.job.dto';
const FIELD_IDS = [VESSEL_FIELD_ID, COMPANY_FIELD_ID, ...Object.values(MDS_PACK_FIELDS)] as const;

@Injectable()
export class DataRegisterDataService {
    constructor(
        private readonly _loggerService: LoggerService,
        private readonly _httpService: HttpService,
        private readonly _moduleRef: ModuleRef,
        private readonly _dataService: DataRegisterTransactionDataService,
        private readonly _dataRegisterWithActiveVersionService: DataRegisterWithActiveVersionService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _cacheService: CacheService,
    ) {}

    private async _getDataSource(accountId?: string | null): Promise<DataSource> {
        if (accountId) {
            const contextId = ContextIdFactory.create();
            this._moduleRef.registerRequestByContextId(
                {
                    headers: {
                        [TENANT_HEADER]: accountId,
                    },
                },
                contextId,
            );
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.TENANT_CONNECTION, contextId, { strict: false });
        } else {
            const contextId = ContextIdFactory.create();
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.DATA_SOURCE, contextId, { strict: false });
        }
    }

    public _getRepositories(dataSource: DataSource, accountId?: string | null) {
        if (accountId) {
            return {
                dRRepo: dataSource.getRepository(DataRegisterTenancyEntity),
                dRFieldRepo: dataSource.getRepository(DataRegisterFieldTenancyEntity),
                dRFieldStyleRepo: dataSource.getRepository(DataRegisterTransactionFieldStyleTenancyEntity),
                dRTransactionRepo: dataSource.getRepository(DataRegisterTransactionTenancyEntity),
                dRTransactionFieldRepo: dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity),
                dRTransactionChangeLogRepo: dataSource.getRepository(TransactionChangeLogTenancyEntity),
                dRVersionRepo: dataSource.getRepository(DataRegisterVersionTenancyEntity),
                generalAutoPopulateSettingRepo: dataSource.getRepository(GeneralAutoPopulateSettingTenancyEntity),
            };
        } else {
            return {
                dRRepo: dataSource.getRepository(DataRegisterEntity),
                dRFieldRepo: dataSource.getRepository(DataRegisterFieldEntity),
                dRFieldStyleRepo: dataSource.getRepository(DataRegisterTransactionFieldStyleEntity),
                dRTransactionRepo: dataSource.getRepository(DataRegisterTransactionEntity),
                dRTransactionFieldRepo: dataSource.getRepository(DataRegisterTransactionFieldEntity),
                dRTransactionChangeLogRepo: dataSource.getRepository(TransactionChangeLogEntity),
                dRVersionRepo: dataSource.getRepository(DataRegisterVersionEntity),
                generalAutoPopulateSettingRepo: dataSource.getRepository(GeneralAutoPopulateSettingEntity),
            };
        }
    }

    public async processReleaseDataRegisterVersion(data: DataRegisterJobDto): Promise<void> {
        this._loggerService.info('Processing release data register version');

        const dataSource = await this._getDataSource(data.accountId);
        const { dRRepo, dRFieldRepo, dRFieldStyleRepo, dRTransactionRepo, dRTransactionFieldRepo, dRVersionRepo } = this._getRepositories(
            dataSource,
            data.accountId,
        );

        const queryRunner = dataSource.createQueryRunner();
        let flStartTransaction = false;

        try {
            const dataRegister = await dRRepo.findOne({
                where: {
                    id: data.id,
                },
            });
            if (!dataRegister) {
                this._loggerService.error('Data register not found');
                return;
            }
            const activeFields = await dRFieldRepo.find({
                where: { dataRegisterVersionId: dataRegister.activeVersionId },
            });
            if (activeFields.length === 0) {
                this._loggerService.error('No active fields found');
                return;
            }

            const dataRecords = await dRTransactionRepo.find({
                where: { dataRegisterId: data.id },
            });
            if (dataRecords.length !== 0) {
                const dataTxFields = {};

                const activeFieldIds = activeFields.map((field) => field.fieldId);
                const rollupFieldIds = activeFields
                    .filter((field) => field.type === FormFieldTypeEnum.Rollup)
                    .map((field) => field.fieldId);

                const addDataRegisterFields: Record<
                    string,
                    {
                        addedDataFields: (DataRegisterFieldEntity | DataRegisterFieldTenancyEntity)[];
                        removedDataFields: (DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity)[];
                    }
                > = {};

                for (let i = 0, iMax = dataRecords.length; i < iMax; i++) {
                    const tranId = dataRecords[i].id;
                    const tmp = await dRTransactionFieldRepo.find({
                        where: { dataRegisterTransactionId: tranId },
                    });
                    dataTxFields[tranId] = tmp;

                    const dataFieldIds = tmp.map((field) => field.fieldId);

                    const removedDataFields = tmp.filter(
                        (field) => !activeFieldIds.includes(field.fieldId) && !FIELD_IDS.includes(field.fieldId),
                    );

                    const addedDataFields = activeFields.filter((field) => !dataFieldIds.includes(field.fieldId));

                    addDataRegisterFields[tranId] = {
                        addedDataFields: addedDataFields,
                        removedDataFields: removedDataFields,
                    };
                }

                await queryRunner.connect();
                await queryRunner.startTransaction();
                flStartTransaction = true;

                for (let i = 0, iMax = dataRecords.length; i < iMax; i++) {
                    const dataFieldsStyle = [];
                    const tranId = dataRecords[i].id;

                    let externalData = null;
                    if (dataRecords[i].externalId !== null) {
                        externalData = await this._getFieldValueFromDataLake(
                            RequestContextService.authorization,
                            dataRecords[i].id,
                            data.id,
                            dataRecords[i].externalId,
                        );
                    }

                    const updateFields = [];
                    const addedDataFields = addDataRegisterFields?.[tranId]?.addedDataFields ?? [];
                    const removedDataFields = addDataRegisterFields?.[tranId]?.removedDataFields ?? [];

                    if (removedDataFields.length > 0) {
                        await queryRunner.manager.delete(
                            data?.accountId ? DataRegisterTransactionFieldStyleTenancyEntity : DataRegisterTransactionFieldStyleEntity,
                            {
                                id: In(removedDataFields.map((field) => field.id)),
                            },
                        );

                        await queryRunner.manager.remove(
                            data?.accountId ? DataRegisterTransactionFieldTenancyEntity : DataRegisterTransactionFieldEntity,
                            removedDataFields,
                        );
                    }

                    if (addedDataFields.length > 0) {
                        let flag = false;
                        let newFields = [];
                        for (let j = 0, jMax = addedDataFields.length; j < jMax; j++) {
                            const field = addedDataFields[j];
                            let value = externalData?.[field.fieldId] || null;
                            let fieldValue;
                            let fieldOptionIds;

                            if (field.type === FormFieldTypeEnum.Lookup && value) {
                                const drTran = await dRTransactionRepo.findOne({
                                    where: {
                                        externalId: value,
                                    },
                                });

                                if (!drTran) {
                                    // TODO: Use logic from src/modules/data-register/services/data/data-register-transaction.data.service.ts:495 to create new record of data register transaction
                                    continue;
                                }

                                fieldValue = drTran?.id;
                                flag = true;
                            } else if (field.type === FormFieldTypeEnum.Select) {
                                // Not happend in case updated field - always no value from external
                                // Just add field with no value
                            } else {
                                fieldValue = value || undefined;
                            }

                            newFields.push({
                                dataRegisterTransactionId: dataRecords[i].id,
                                fieldId: field.fieldId,
                                fieldType: field.type,
                                fieldValue,
                                fieldOptionIds,
                            });
                        }

                        newFields = await queryRunner.manager.save(
                            data?.accountId ? DataRegisterTransactionFieldTenancyEntity : DataRegisterTransactionFieldEntity,
                            newFields,
                        );

                        if (flag) {
                            const forms = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                                dataRegisterIds: [dataRegister.id],
                                dataRegisterRepository: dRRepo,
                                dataRegisterVersionRepository: dRVersionRepo,
                                dataRegisterFieldRepository: dRFieldRepo,
                            });
                            const form = forms[0];
                            const formVersion = form.dataRegisterVersions[0];
                            const relatedLookupData = await this._dataService.getRelatedLookupData({
                                formVersion: formVersion,
                                transactionFields: newFields,
                                dataRegisterRepository: dRRepo,
                                dataRegisterTransactionRepository: dRTransactionRepo,
                            });

                            newFields = newFields.map((field, index) => {
                                if (field.fieldType === FormFieldTypeEnum.Lookup) {
                                    const { fieldOptionIds, fieldValue } = this._dataService.formatLookupValue({
                                        formField: addedDataFields[index],
                                        transactionField: field,
                                        dataRegisterVersions: (relatedLookupData.dataRegisters ?? [])
                                            .filter((dr) => dr.dataRegisterVersions?.length)
                                            .map((dr) => dr.dataRegisterVersions[0]),
                                        dataRegisterTransactions: relatedLookupData.dataRegisterTransactions,
                                    });
                                    field.fieldValue = fieldValue;
                                    field.fieldOptionIds = fieldOptionIds;
                                }

                                return field;
                            });

                            newFields = await queryRunner.manager.save(
                                data?.accountId ? DataRegisterTransactionFieldTenancyEntity : DataRegisterTransactionFieldEntity,
                                newFields,
                            );
                        }

                        updateFields.push(...newFields);
                    }

                    const updatedDataFields = dataTxFields[dataRecords[i].id].filter((field) => activeFieldIds.includes(field.fieldId));
                    if (updatedDataFields.length > 0) {
                        let updated = [];
                        let flag = false;
                        for (let j = 0, jMax = updatedDataFields.length; j < jMax; j++) {
                            const dataField: DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity =
                                updatedDataFields[j];
                            if (externalData && externalData[dataField.fieldId]) {
                                let field: DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity = dataField;
                                const value = externalData[dataField.fieldId];

                                if (dataField.fieldType === FormFieldTypeEnum.Lookup) {
                                    const drTran = await dRTransactionRepo.findOne({
                                        where: {
                                            externalId: value,
                                        },
                                    });

                                    if (!drTran) {
                                        // TODO: Use logic from src/modules/data-register/services/data/data-register-transaction.data.service.ts:495 to create new record of data register transaction
                                        continue;
                                    }

                                    if (dataField.fieldOptionIds.includes(drTran?.id) || dataField.fieldOptionIds.includes(value)) {
                                        continue;
                                    }

                                    field.fieldValue = drTran?.id;
                                    flag = true;
                                } else if (dataField.fieldType === FormFieldTypeEnum.Select) {
                                    // Not happend in case updated field - always no value from external
                                    continue;
                                } else if (dataField.fieldValue !== externalData[dataField.fieldId]) {
                                    field.fieldValue = value;
                                } else {
                                    continue;
                                }

                                // const tmp = await queryRunner.manager.save(
                                //     data?.accountId ? DataRegisterTransactionFieldTenancyEntity : DataRegisterFieldEntity,
                                //     field,
                                // );

                                updated.push(field);
                            } else {
                                updateFields.push(dataField);
                            }
                        }

                        if (flag) {
                            const forms = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                                dataRegisterIds: [dataRegister.id],
                                dataRegisterRepository: dRRepo,
                                dataRegisterVersionRepository: dRVersionRepo,
                                dataRegisterFieldRepository: dRFieldRepo,
                            });
                            const form = forms[0];
                            const formVersion = form.dataRegisterVersions[0];
                            const relatedLookupData = await this._dataService.getRelatedLookupData({
                                formVersion: formVersion,
                                transactionFields: updated,
                                dataRegisterRepository: dRRepo,
                                dataRegisterTransactionRepository: dRTransactionRepo,
                            });
                            updated = updated.map((field) => {
                                if (field.fieldType === FormFieldTypeEnum.Lookup) {
                                    const { fieldOptionIds, fieldValue } = this._dataService.formatLookupValue({
                                        formField: activeFields.find((f) => f.fieldId === field.fieldId),
                                        transactionField: field,
                                        dataRegisterVersions: (relatedLookupData.dataRegisters ?? [])
                                            .filter((dr) => dr.dataRegisterVersions?.length)
                                            .map((dr) => dr.dataRegisterVersions[0]),
                                        dataRegisterTransactions: relatedLookupData.dataRegisterTransactions,
                                    });
                                    field.fieldValue = fieldValue;
                                    field.fieldOptionIds = fieldOptionIds;
                                }

                                return field;
                            });
                            // updated = await queryRunner.manager.save(
                            //     data?.accountId ? DataRegisterTransactionFieldTenancyEntity : DataRegisterTransactionFieldEntity,
                            //     updated,
                            // );
                        }

                        updateFields.push(...updated);
                    }

                    if (updateFields.length > 0) {
                        const tmpStyles = this._dataService.pubGetTransactionFieldStyles(tranId, activeFields, updateFields);
                        dataFieldsStyle.push(...tmpStyles);

                        const rollupFields = updateFields.filter((field) => rollupFieldIds.includes(field.fieldId));

                        if (rollupFields.length > 0) {
                            const rollupFieldIds = rollupFields.map((field) => field.id).filter(Boolean);

                            if (rollupFieldIds.length > 0) {
                                const rollupFieldStyles = await this._dataService.getFieldStylesByDatRegisterFieldIds(
                                    rollupFieldIds,
                                    data?.accountId ? dRFieldStyleRepo : dRFieldStyleRepo,
                                );

                                if (rollupFieldStyles?.length) {
                                    // Create a Map for O(1) lookup performance
                                    const rollupFieldsMap = new Map(rollupFields.map((field) => [field.id, field]));

                                    // Separate styles that need configuration updates from those that don't
                                    const updatedStyles = [];
                                    const unchangedStyles = [];

                                    dataFieldsStyle.forEach((style) => {
                                        const rollupField = rollupFieldsMap.get(style.id);
                                        if (rollupField && isEmpty(style.configuration['icon'])) {
                                            style.configuration = rollupField.configuration;
                                            updatedStyles.push(style);
                                        } else {
                                            unchangedStyles.push(style);
                                        }
                                    });

                                    // Replace the original array with optimized result
                                    dataFieldsStyle.length = 0;
                                    dataFieldsStyle.push(...updatedStyles, ...unchangedStyles);
                                }
                            }
                        }

                        updateFields.forEach((field) => {
                            const style = (dataFieldsStyle ?? []).find((style) => style?.id === field?.id);
                            field.validationValue = style?.configuration?.['icon'] ?? null;
                        });
                    }

                    if (updateFields?.length) {
                        await queryRunner.manager.save(
                            data?.accountId ? DataRegisterTransactionFieldTenancyEntity : DataRegisterTransactionFieldEntity,
                            updateFields,
                        );
                    }

                    if (dataFieldsStyle.length > 0) {
                        const updatedStyles = [];
                        const removedOverride = [];

                        for (let j = 0, jMax = dataFieldsStyle.length; j < jMax; j++) {
                            const style = dataFieldsStyle[j];
                            const existedStyle = await dRFieldStyleRepo.findOne({
                                where: {
                                    transactionId: tranId,
                                    fieldId: style.fieldId,
                                },
                            });

                            if (existedStyle) {
                                if (existedStyle?.configuration?.['icon'] !== style?.configuration?.['icon']) {
                                    updatedStyles.push(style);
                                    removedOverride.push(style.id);
                                }
                            } else {
                                updatedStyles.push(style);
                            }
                        }

                        if (updatedStyles.length > 0) {
                            await queryRunner.manager.save(
                                data?.accountId ? DataRegisterTransactionFieldStyleTenancyEntity : DataRegisterTransactionFieldStyleEntity,
                                updatedStyles,
                            );
                        }

                        if (removedOverride.length > 0) {
                            await queryRunner.manager.delete(
                                data?.accountId
                                    ? DataRegisterTransactionFieldOverrideTenancyEntity
                                    : DataRegisterTransactionFieldOverrideEntity,
                                {
                                    dataRegisterTransactionFieldId: In(removedOverride),
                                },
                            );
                        }
                    }
                }
            }

            if (flStartTransaction) {
                await queryRunner.commitTransaction();
            }
        } catch (error) {
            this._loggerService.error(error);
            if (flStartTransaction) {
                await queryRunner.rollbackTransaction();
            }
            throw error;
        } finally {
            if (flStartTransaction) {
                await queryRunner.release();
            }
        }
    }

    public async cacheActiveDataRegisterVersionBuilder(data: DataRegisterJobDto): Promise<void> {
        if (!data.accountId || !data.id) return;
        this._loggerService.info(`Start caching active data register version for data register ${data.id} of account ${data.accountId}`);

        const dataSource = await this._getDataSource(data.accountId);
        const { dRRepo, dRFieldRepo, dRVersionRepo } = this._getRepositories(dataSource, data.accountId);

        const dataRegister = await dRRepo.findOne({
            where: {
                id: data.id,
            },
            select: ['id', 'activeVersionId'],
        });

        const dataRegisterVersion = await dRVersionRepo.findOne({
            where: {
                id: dataRegister.activeVersionId,
            },
            select: ['id', 'type', 'view', 'displayAttributes', 'config', 'associateField', 'dataRegisterId'],
        });

        const activeFields = await dRFieldRepo.find({
            where: { dataRegisterVersionId: dataRegister.activeVersionId },
        });

        const formatDrVersion = UtilsService.format<typeof dataRegisterVersion>([dataRegisterVersion])[0];
        const formatFields = UtilsService.format<(typeof activeFields)[number]>(activeFields);

        formatDrVersion.fields = formatFields;

        const { drVersionKey, drVersionFolder } =
            UtilsService.getActiveDrVersionCacheKeys({
                accountId: data.accountId,
                drId: data.id,
                drVersionId: dataRegister.activeVersionId,
            }) ?? {};

        this._loggerService.info(`Start caching active data register version with cache key ${drVersionKey}`);
        await this._cacheService.deleteByPrefix(drVersionFolder);
        await this._cacheService.set(drVersionKey, JSON.stringify(formatDrVersion));
        this._loggerService.info(`End caching active data register version with cache key ${drVersionKey}`);
    }

    public async addTransactionRecordChangeLog({
        data,
        accountId,
        objectType,
    }: {
        data: TransactionRecordChangeLogMessageDto;
        objectType: TransactionDataType;
        accountId?: string;
    }): Promise<void> {
        if (isEmpty(data) || isEmpty(data.actionType) || isEmpty(data.current)) {
            throw new Error('Invalid data register record change log');
        }

        try {
            const dataSource = await this._getDataSource(accountId);
            const { dRTransactionChangeLogRepo } = this._getRepositories(dataSource, accountId);
            const { sourceOfChange, actionType, current, ...rest } = data;
            const changeLogData: TransactionRecordChangeLogDto = {
                ...data,
                objectType,
                objectId: current.id,
            };
            let newChangeLog = dRTransactionChangeLogRepo.create(changeLogData);

            switch (actionType) {
                case ChangeLogActionType.ADDED:
                    {
                        newChangeLog.createdAt = current.createdAt;
                        newChangeLog.updatedAt = current.createdAt;
                        newChangeLog.createdBy = current.createdBy;
                        newChangeLog.createdByUser = current?.createdByUser;
                    }
                    break;
                case ChangeLogActionType.DELETED:
                    {
                        newChangeLog.createdAt = current.deletedAt;
                        newChangeLog.updatedAt = current.deletedAt;
                        newChangeLog.createdBy = current.deletedBy;
                        newChangeLog.createdByUser = current?.deletedByUser;
                    }
                    break;

                default: {
                    //default: ChangeLogActionType.UPDATED
                    newChangeLog.createdAt = current.updatedAt;
                    newChangeLog.updatedAt = current.updatedAt;
                    newChangeLog.createdBy = current.updatedBy;
                    newChangeLog.createdByUser = current.updatedByUser;
                    break;
                }
            }

            switch (sourceOfChange) {
                case SourceOfChangeType.AUTO_POPULATE:
                    {
                        newChangeLog.createdBy = AUTO_POPULATE_USER_ID;
                        newChangeLog.createdByUser = SourceOfChangeType.AUTO_POPULATE.replace('_', ' ');
                    }

                    break;
                case SourceOfChangeType.AUTOMATION:
                    {
                        newChangeLog.createdBy = AUTOMATION_USER_ID;
                        newChangeLog.createdByUser = SourceOfChangeType.AUTOMATION.replace('_', ' ');
                    }
                    break;

                default:
                    break;
            }

            await dRTransactionChangeLogRepo.save(newChangeLog);
        } catch (error) {
            this._loggerService.error(error);
        }
    }

    public async refreshDataRegister(eventData: any): Promise<void> {
        this._loggerService.info(`Start refreshing data register ${eventData?.id} of account ${eventData?.accountId}`);

        const accountId = eventData.metadata?.tenantId;
        const dataRegisterId = eventData.payload?.dataRegisterId;

        if (!accountId) {
            const dataSource = await this._getDataSource();
            const accounts = await dataSource.getRepository(AccountEntity).find({});

            for (const account of accounts) {
                await this._eventDrivenService.publishMessage(CommonTopicEnum.CATALOG_REFRESH_TOPIC, {
                    id: randomUUID(),
                    aggregateId: account.id,
                    payload: {
                        accountId: account.id,
                    },
                    metadata: {
                        tenantId: account.id,
                        correlationId: eventData?.metadata?.correlationId,
                        version: 0,
                        source: '',
                        timestamp: new Date().getTime(),
                        name: DataRegisterEventEnum.DATA_REGISTER_REFRESH_DATA,
                        type: DataRegisterEventEnum.DATA_REGISTER_REFRESH_DATA,
                    },
                });
            }
        } else if (accountId && !dataRegisterId) {
            const dataSource = await this._getDataSource(accountId);
            const { dRRepo } = this._getRepositories(dataSource, accountId);
            const dataRegisters = await dRRepo.findBy({ type: In([DataRegisterTypeEnum.Vessel, DataRegisterTypeEnum.Company]) });

            for (const dataRegister of dataRegisters) {
                await this._eventDrivenService.publishMessage(CommonTopicEnum.CATALOG_REFRESH_TOPIC, {
                    id: randomUUID(),
                    aggregateId: eventData?.aggregateId,
                    payload: {
                        accountId: accountId,
                        dataRegisterId: dataRegister.id,
                    },
                    metadata: {
                        tenantId: accountId,
                        correlationId: eventData?.metadata?.correlationId,
                        version: 0,
                        source: '',
                        timestamp: new Date().getTime(),
                        name: DataRegisterEventEnum.DATA_REGISTER_REFRESH_DATA,
                        type: DataRegisterEventEnum.DATA_REGISTER_REFRESH_DATA,
                    },
                });
            }
        } else if (accountId && dataRegisterId) {
            const dataSource = await this._getDataSource(accountId);
            const { dRRepo, dRTransactionRepo, generalAutoPopulateSettingRepo } = this._getRepositories(dataSource, accountId);

            await this._dataService.refreshDataRegisterTransaction({
                dataRegisterId,
                dataRegisterRepository: dRRepo,
                dataRegisterTransactionRepository: dRTransactionRepo,
                generalAutoPopulateSettingRepository: generalAutoPopulateSettingRepo,
            });
        }
    }

    private async _getFieldValueFromDataLake(
        token: string,
        dataRegisterTxId: string,
        dataRegisterId: string,
        ctxId: string,
    ): Promise<Map<string, any>> {
        const url = `${process.env.DATA_LAKE_SERVICE_URL}/ctx-properties/v1/data-register-transaction/${dataRegisterTxId}/data-register/${dataRegisterId}/context/${ctxId}`;
        const response = await lastValueFrom(
            this._httpService.get(url, {
                headers: {
                    Authorization: token,
                },
            }),
        );
        return response.data;
    }
}
