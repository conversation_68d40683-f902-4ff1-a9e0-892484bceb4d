import { Injectable } from '@nestjs/common';
import { keyBy, toNumber, uniqBy } from 'lodash';
import { DataSource, In } from 'typeorm';
import { LoggerService } from '../../../common/src';
import { OperatorType } from '../../../common/src/modules/shared/enums/operator.enum';
import { PREFIX } from '../../../common/src/modules/shared/enums/prefix.enum';
import { DATA_REGISTER_RECORD_ID } from '../../../constant';
import { AutomationActionTenancyEntity } from '../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { EditDataRegisterTransactionRequest } from '../../data-register/dtos/requests/create-data-register-transaction.request';
import { DataRegisterTransactionTenancyService } from '../../data-register/services/data-register-transaction.tenancy.service';
import { FormTransactionTenancyService } from '../../form/services/form-transaction.tenancy.service';

const RollupOperations = {
    [FormFieldTypeEnum.Number]: ['SUM', 'AVERAGE', 'MIN', 'MAX', 'COUNT'],
    [FormFieldTypeEnum.Rollup]: ['SUM', 'AVERAGE', 'MIN', 'MAX', 'COUNT'],
};

@Injectable()
export class RollupTransactionsDataService {
    constructor(
        private readonly _dataSourceService: DataSourceService,
        private readonly _loggerService: LoggerService,
    ) {}

    async handleService(actionResult: Record<string, any>) {
        const accountId = actionResult?.accountId;
        const contextTransactionId = actionResult?.contextTransactionId;
        if (!accountId || !contextTransactionId) {
            return;
        }
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            accountId,
            FormTransactionTenancyService,
        );

        const drService = await this._dataSourceService.resolveService<DataRegisterTransactionTenancyService>(
            accountId,
            DataRegisterTransactionTenancyService,
        );
        const dataSource = await this._dataSourceService.createAccountDataSource(accountId);
        const {
            automationActionRepository,
            transactionFieldRepository,
            formFieldTenancyRepository,
            formRepository,
            transactionRepository,
            dataRegisterFieldRepository,
            dataRegisterRepository,
            dataRegisterTransactionFieldRepository,
        } = this._getRepositories(dataSource);
        const actionId = actionResult.actionId;
        const targetType = actionResult.targetType;
        const action = await automationActionRepository.findOne({
            where: {
                id: actionId,
            },
        });
        if (!action) {
            return;
        }
        const { id, configuration } = action;

        const { formId, conditions, contextMappingFields, mappingFields, actionFields, targetFormId, targetRegisterId, registerId } =
            configuration ?? {};

        const souceType = configuration?.sourceType || 'form';

        if (!contextMappingFields?.length) {
            this._loggerService.error(`Context mapping fields are required for action ${id}`);
            return;
        }

        if (!mappingFields?.length || !actionFields?.length) {
            this._loggerService.error(`Mapping fields or action fields are required for action ${id}`);
            return;
        }

        if (souceType === 'form') {
            if (!formId && !targetFormId) {
                this._loggerService.error(`Form ID is required for action ${id}`);
                return;
            }

            // Get context transaction data from mapping fields
            const contextTransactionFields = await transactionFieldRepository.findBy({
                transactionId: contextTransactionId,
                dependFieldId: null,
            });
            if (!contextTransactionFields?.length) {
                this._loggerService.info(`No context transaction data found for action ${id}`);
                return;
            }

            let rollupSourceTransactionFilters = [
                {
                    field: 'formId',
                    operator: OperatorType.equals,
                    value: formId,
                    queryToOptionIds: null,
                },
            ];
            const contextMappingFieldsMap = keyBy(contextMappingFields, 'fromFieldId');
            contextTransactionFields?.forEach((field) => {
                const mapping = contextMappingFieldsMap[field.fieldId];
                if (mapping) {
                    rollupSourceTransactionFilters.push({
                        field: `${PREFIX.DATA_REGISTER_FIELD}_${mapping.toFieldId}`,
                        operator: OperatorType.equals,
                        value: field.fieldOptionIds?.[0],
                        queryToOptionIds: true,
                    });
                }
            });
            rollupSourceTransactionFilters = uniqBy(rollupSourceTransactionFilters, 'field');
            if (!rollupSourceTransactionFilters.length) {
                this._loggerService.info(`No rollup transaction found for action ${id}`);
                return true;
            }

            const rollupForm = await formRepository.findOne({
                where: {
                    id: formId,
                },
                select: {
                    activeVersionId: true,
                    id: true,
                },
            });
            if (!rollupForm) {
                this._loggerService.info(`No form found for action ${id}`);
                return false;
            }

            const rollupSourceIds = await service.getList({
                take: 0,
                skip: 0,
                conditions,
                filters: rollupSourceTransactionFilters,
                onlyGetTransactionIds: true,
            });
            if (!rollupSourceIds.data?.length) {
                this._loggerService.info(`No context transaction data found for action ${id}`);
                return true;
            }

            const rollupFormFields = await formFieldTenancyRepository.find({
                where: {
                    formVersionId: rollupForm.activeVersionId,
                },
            });

            const supportedFieldIds = rollupFormFields
                .filter((field) =>
                    [FormFieldTypeEnum.Calculation, FormFieldTypeEnum.Number, FormFieldTypeEnum.Rollup, FormFieldTypeEnum.Lookup].includes(
                        field.type as FormFieldTypeEnum,
                    ),
                )
                .map((field) => field.fieldId);
            const rollupTransactionFields = await transactionFieldRepository.find({
                where: {
                    transactionId: In(rollupSourceIds.data),
                    fieldId: In(supportedFieldIds),
                },
            });

            if (targetType === 'form') {
                await this._updateTargetTransaction(rollupTransactionFields, targetFormId, mappingFields, actionFields, service);
                return true;
            } else if (targetType === 'register') {
                await this._updateTargetDrRecords(rollupTransactionFields, targetRegisterId, mappingFields, actionFields, drService);
                return true;
            }
            return false;
        } else if (souceType === 'register') {
            if (!registerId && !targetRegisterId) {
                this._loggerService.error(`Register ID is required for action ${id}`);
                return;
            }
            // Get context transaction data from mapping fields
            const contextTransactionFields = await transactionFieldRepository.findBy({
                transactionId: contextTransactionId,
                dependFieldId: null,
            });

            if (!contextTransactionFields?.length) {
                this._loggerService.info(`No context transaction data found for action ${id}`);
                return;
            }

            let rollupSourceTransactionFilters = [
                {
                    field: 'dataRegisterId',
                    operator: OperatorType.equals,
                    value: registerId,
                    queryToOptionIds: null,
                },
            ];

            const contextMappingFieldsMap = keyBy(contextMappingFields, 'fromFieldId');
            contextTransactionFields?.forEach((field) => {
                const mapping = contextMappingFieldsMap[field.fieldId];
                if (mapping) {
                    rollupSourceTransactionFilters.push({
                        field: `${PREFIX.DATA_REGISTER_FIELD}_${mapping.toFieldId}`,
                        operator: OperatorType.equals,
                        value: field.fieldOptionIds?.[0],
                        queryToOptionIds: true,
                    });
                }
            });
            rollupSourceTransactionFilters = uniqBy(rollupSourceTransactionFilters, 'field');
            if (!rollupSourceTransactionFilters.length) {
                this._loggerService.info(`No rollup transaction found for action ${id}`);
                return true;
            }

            const rollupRegister = await dataRegisterRepository.findOne({
                where: {
                    id: registerId,
                },
                select: {
                    activeVersionId: true,
                    id: true,
                },
            });

            if (!rollupRegister) {
                this._loggerService.info(`No register found for action ${id}`);
                return false;
            }

            const rollupSourceIds = await drService.getList({
                take: 0,
                skip: 0,
                conditions,
                filters: rollupSourceTransactionFilters,
            });

            if (!rollupSourceIds.data?.length) {
                this._loggerService.info(`No context transaction data found for action ${id}`);
                return true;
            }

            const rollupRegisterFields = await dataRegisterFieldRepository.find({
                where: {
                    dataRegisterVersionId: rollupRegister.activeVersionId,
                },
            });

            const supportedFieldIds = rollupRegisterFields
                .filter((field) =>
                    [FormFieldTypeEnum.Calculation, FormFieldTypeEnum.Number, FormFieldTypeEnum.Rollup, FormFieldTypeEnum.Lookup].includes(
                        field.type as FormFieldTypeEnum,
                    ),
                )
                .map((field) => field.fieldId);

            const rollupTransactionFields = await dataRegisterTransactionFieldRepository.find({
                where: {
                    dataRegisterTransactionId: In(rollupSourceIds.data?.map((item) => item.id)),
                    fieldId: In(supportedFieldIds),
                },
            });

            console.log('rollupTransactionFields: ', rollupTransactionFields);

            if (targetType === 'form') {
                await this._updateTargetTransaction(rollupTransactionFields, targetFormId, mappingFields, actionFields, service);
                return true;
            } else if (targetType === 'register') {
                await this._updateTargetDrRecords(rollupTransactionFields, targetRegisterId, mappingFields, actionFields, drService);
                return true;
            }
            return false;
        }

        return false;
    }

    private async _updateTargetTransaction(
        rollupSourceTransactionFields: TransactionFieldEntity[] | DataRegisterTransactionFieldTenancyEntity[],
        targetFormId: string,
        mappingFields: Record<string, any>,
        actionFields: Record<string, any>,
        service: FormTransactionTenancyService,
    ) {
        // Get target transaction data from context mapping fields
        let targetTransactionFilters = [];
        const mappingFieldsMap = keyBy(mappingFields, 'fromFieldId');
        rollupSourceTransactionFields?.forEach((tf) => {
            const mapping = mappingFieldsMap[tf.fieldId];
            if (mapping) {
                targetTransactionFilters.push({
                    field: `${PREFIX.DATA_REGISTER_FIELD}_${mapping.toFieldId}`,
                    operator: OperatorType.equals,
                    value: tf.fieldOptionIds?.[0],
                    queryToOptionIds: true,
                });
            }
        });
        targetTransactionFilters = uniqBy(targetTransactionFilters, 'field');
        if (!targetTransactionFilters.length) {
            this._loggerService.info(`No transactions found for action`);
            return true;
        }

        targetTransactionFilters.push({
            field: 'formId',
            operator: OperatorType.equals,
            value: targetFormId,
        });

        const targetTransactionsResponse = await service.getList({
            take: 0,
            skip: 0,
            filters: targetTransactionFilters,
        });
        if (!targetTransactionsResponse.data?.length) {
            this._loggerService.info(`No transactions found for action`);
            return true;
        }

        // Do rollup source transaction data
        const rollupResult: Record<string, any> = {};
        actionFields?.forEach((field) => {
            const { rollupFromFieldId, rollupToFieldId, operation, dataType } = field;
            const transactionFields = rollupSourceTransactionFields.filter((tranField) => tranField.fieldId === rollupFromFieldId);
            const result = this._getRollupFields(transactionFields, operation, dataType);
            rollupResult[rollupToFieldId] = result;
        });

        // Update target transaction data
        const targetTransactions = targetTransactionsResponse.data;
        targetTransactions.forEach((targetTransaction) => {
            const { id, transactionFields } = targetTransaction;
            const updatedTransactionFields = [];
            //TODO: check rollup validate field here  & update it

            transactionFields.forEach((field) => {
                const { fieldId } = field;
                const rollupField = rollupResult[fieldId];
                if (rollupField !== undefined && rollupField !== null) {
                    field.fieldValue = rollupField;
                    updatedTransactionFields.push(field);
                }
            });

            service.update(id, {
                transactionFields: updatedTransactionFields as any,
                transactionId: id,
                formId: targetFormId,
            });
        });
    }

    private async _updateTargetDrRecords(
        rollupSourceTransactionFields: TransactionFieldEntity[] | DataRegisterTransactionFieldTenancyEntity[],
        targetDrId: string,
        mappingFields: Record<string, any>,
        actionFields: Record<string, any>,
        service: DataRegisterTransactionTenancyService,
    ) {
        // Get target transaction data from context mapping fields
        let targetTransactionFilters = [];
        const mappingFieldsMap = keyBy(mappingFields, 'fromFieldId');
        rollupSourceTransactionFields?.forEach((tf) => {
            const mapping = mappingFieldsMap[tf.fieldId];
            if (!mapping) return;
            const field = mapping.toFieldId === DATA_REGISTER_RECORD_ID ? 'id' : `${PREFIX.DATA_REGISTER_FIELD}_${mapping.toFieldId}`;
            if (targetTransactionFilters.some((f) => f.field === field)) {
                return;
            }
            targetTransactionFilters.push({
                field,
                operator: OperatorType.equals,
                value: tf.fieldOptionIds?.[0],
                queryToOptionIds: true,
            });
        });
        targetTransactionFilters = uniqBy(targetTransactionFilters, 'field');
        if (!targetTransactionFilters.length) {
            this._loggerService.info(`No transactions found for action`);
            return true;
        }

        targetTransactionFilters.push({
            field: 'dataRegisterId',
            operator: OperatorType.equals,
            value: targetDrId,
        });

        const targetTransactionsResponse = await service.getList({
            take: 0,
            skip: 0,
            filters: targetTransactionFilters,
        });
        if (!targetTransactionsResponse.data?.length) {
            this._loggerService.info(`No transactions found for action`);
            return true;
        }

        // Do rollup source transaction data
        const rollupResult: Record<string, any> = {};
        actionFields?.forEach((field) => {
            const { rollupFromFieldId, rollupToFieldId, operation, dataType } = field;
            const transactionFields = rollupSourceTransactionFields.filter((tranField) => tranField.fieldId === rollupFromFieldId);
            const result = this._getRollupFields(transactionFields, operation, dataType);
            rollupResult[rollupToFieldId] = result;
        });

        // Update target transaction data
        const targetTransactions = targetTransactionsResponse.data;
        for (const targetTransaction of targetTransactions) {
            const { id, transactionFields } = targetTransaction;
            let updatedTransactionFields = [];
            let rollupValidateField = [];

            transactionFields.forEach((field) => {
                const { fieldId } = field;
                const rollupField = rollupResult[fieldId];
                if (rollupField !== undefined && rollupField !== null) {
                    field.fieldValue = rollupField;
                    updatedTransactionFields.push(field);
                }
            });

            rollupValidateField = actionFields
                .filter((field) => field.dataType === 'validationResult')
                .map((field) => {
                    const value = rollupResult[field.rollupToFieldId];
                    return {
                        ...field,
                        fieldId: field.rollupToFieldId,
                        fieldValue: value,
                        fieldOptionIds: [],
                        type: FormFieldTypeEnum.ValidationResult,
                    };
                });

            if (rollupValidateField.length) {
                const rollupValidateFieldIDS = rollupValidateField.map((field) => field.fieldId);
                updatedTransactionFields = updatedTransactionFields.filter((field) => !rollupValidateFieldIDS.includes(field.fieldId));
                service.updateRollupValidation(
                    id,
                    {
                        transactionFields: rollupValidateField as any,
                        dataRegisterId: targetDrId,
                        dataRegisterTransactionId: id,
                    } as EditDataRegisterTransactionRequest,
                    SourceOfChangeType.AUTOMATION,
                );
            }

            if (updatedTransactionFields?.length) {
                service.update(
                    id,
                    {
                        transactionFields: updatedTransactionFields as any,
                        dataRegisterId: targetDrId,
                        dataRegisterTransactionId: id,
                    } as EditDataRegisterTransactionRequest,
                    SourceOfChangeType.AUTOMATION,
                );
            }
        }
    }

    private _getRepositories(dataSource: DataSource) {
        const automationActionRepository = dataSource.getRepository(AutomationActionTenancyEntity);
        const formRepository = dataSource.getRepository(FormTenancyEntity);
        const registerRepo = dataSource.getRepository(DataRegisterTransactionTenancyEntity);
        const transactionFieldRepository = dataSource.getRepository(TransactionFieldEntity);
        const formFieldTenancyRepository = dataSource.getRepository(FormFieldTenancyEntity);
        const transactionRepository = dataSource.getRepository(TransactionEntity);
        const dataRegisterFieldRepository = dataSource.getRepository(DataRegisterFieldTenancyEntity);
        const dataRegisterRepository = dataSource.getRepository(DataRegisterTenancyEntity);
        const dataRegisterTransactionFieldRepository = dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity);

        return {
            automationActionRepository,
            formRepository,
            registerRepo,
            transactionFieldRepository,
            formFieldTenancyRepository,
            transactionRepository,
            dataRegisterFieldRepository,
            dataRegisterRepository,
            dataRegisterTransactionFieldRepository,
        };
    }

    private _getRollupFields(
        transactionFields: (TransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity)[],
        operation: string,
        datatype: string,
    ) {
        try {
            const values = transactionFields.map((item) => item.fieldValue);
            let result;
            if (datatype !== 'validationResult') {
                switch (operation) {
                    case 'SUM':
                        result = values.reduce((sum, val) => sum + this._getFieldNumberValue(val), 0);
                        break;
                    case 'AVG':
                        result = values.length ? values.reduce((sum, val) => sum + this._getFieldNumberValue(val), 0) / values.length : 0;
                        break;
                    case 'COUNT':
                        result = values.length;
                        break;
                    case 'MIN':
                        result = values.length ? Math.min(...values.map((value) => this._getFieldNumberValue(value))) : 0;
                        break;
                    case 'MAX':
                        result = values.length ? Math.max(...values.map((value) => this._getFieldNumberValue(value))) : 0;
                        break;
                    default:
                        result = null;
                }
                result = result.toFixed(2);
            } else {
                result = this._getFieldValidationResultValue(values);
            }

            return result;
        } catch (error) {
            this._loggerService.error(`Error rolling up fields: ${error}`);
            return null;
        }
    }

    private _getFieldNumberValue(value: string) {
        if (!value) {
            return 0;
        }
        const numberValue = toNumber(value);
        if (isNaN(numberValue)) {
            return 0;
        }
        return numberValue;
    }

    /**
     * Converts an array of string-represented numbers, finds the maximum numeric value,
     * and returns that value as a string.
     *
     * @param values - An array of strings, where each string is expected to be a number.
     * @returns The highest number from the array, converted back to a string.
     * Returns '0' if the array is empty or contains no valid numbers.
     */
    private _getFieldValidationResultValue(values: string[]): string {
        // 1. Convert the array of strings to an array of numbers.
        // The `map(Number)` function is a concise way to call the Number constructor
        // on each element. We also filter out any potential `NaN` values that
        // might arise from invalid number strings (e.g., ['100', 'apple']).
        const numbers = values.map((str) => parseInt(str, 10)).filter((num) => !isNaN(num));

        // If the resulting array of numbers is empty (either because the input
        // was empty or contained no valid numbers), we can return a default value.
        if (numbers.length === 0) {
            return '0';
        }

        // 2. Find the maximum value from the array of numbers.
        // The spread operator (...) passes all elements of the array as individual
        // arguments to Math.max().
        const maxValue = Math.max(...numbers);

        // 3. Convert the resulting maximum number back to a string and return it.
        return maxValue.toString();
    }
}
