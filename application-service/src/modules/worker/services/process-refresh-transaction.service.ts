import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../../common/src';
import { ParsedActionDto } from '../dtos/action.dto';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { FormTransactionTenancyService } from '../../form/services/form-transaction.tenancy.service';
import { FormTransactionDataService } from '../../form/services/data/form-transaction.data.service';
import { PopulateTransactionFieldService } from '../../form/services/populate-transaction-field.service';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { EXTERNAL_ORIGINAL_VERSION_ID } from '../../../common/src/constant/field';
import { chunk, uniq } from 'lodash';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { UpdateTransactionOption } from '../../form/services/data/types/update-transaction-option';

@Injectable()
export class ProcessRefreshTransactionService {
    constructor(
        private readonly _loggerService: LoggerService,
        private readonly _dataSourceService: DataSourceService,
    ) {}

    public async doRefresh(
        actionDto: ParsedActionDto,
        transactionIds: string[],
        user?: Record<string, unknown>,
        refreshFor: 'form_fields' | 'collection_fields' | 'all' = 'form_fields',
    ) {
        if (refreshFor === 'form_fields') {
            await this._processFormFieldsTransaction(actionDto, transactionIds, user);
        } else if (refreshFor === 'collection_fields') {
            await this._processCollectionTransaction(actionDto, transactionIds, user);
        } else if (refreshFor === 'all') {
            // await this._processFormTransaction(actionDto, transactionIds, user);
        }
    }

    private async _processFormFieldBeforeUpdate(
        {
            actionDto,
            transaction,
            tranFields,
            externalOriginReportReportField,
        }: {
            actionDto: ParsedActionDto;
            transaction: TransactionEntity;
            tranFields: TransactionFieldEntity[];
            externalOriginReportReportField?: TransactionFieldEntity;
            refreshFor: 'form_fields' | 'collection_fields' | 'all';
        },
        resolvedServices: {
            formTransactionDataService: FormTransactionDataService;
            populateTransactionFieldService: PopulateTransactionFieldService;
            formTransactionTenancyService: FormTransactionTenancyService;
        },
        user?: Record<string, unknown>,
    ) {
        const { formTransactionDataService, populateTransactionFieldService, formTransactionTenancyService } = resolvedServices;
        const formValues = {};
        const formEntity = await formTransactionDataService.getFormWithActiveVersion({
            formId: transaction.formId,
            formVersionId: transaction.formVersionId,
        });
        const formVersion = formEntity?.formVersions[0];
        const fieldTypeMaps = {};
        formVersion.fields.map((f) => (fieldTypeMaps[f.fieldId] = f.type));
        const transactionFields = tranFields.filter((item) => item.contextType !== 'collection');

        // find lookup field change in form transaction
        const fieldIdChanged = formVersion?.fields.find((item) => item.lookupTargetId === actionDto.payload.external.dataRegisterId);

        const tranFieldsChanged = (transactionFields || []).filter((tf) => tf.fieldId === fieldIdChanged?.fieldId);

        (tranFieldsChanged || []).forEach((f) => {
            if (SELECTABLE_FIELD_TYPES.includes(fieldTypeMaps[f.fieldId])) {
                f.fieldValue = f.fieldOptionIds?.length ? (f.fieldOptionIds as any) : f.fieldValue;
            }
        });

        const targetFieldChangeIds = actionDto?.payload?.external?.fieldChanged?.length
            ? actionDto?.payload?.external?.fieldChanged?.map((item) => item.fieldId)
            : [];
        const targetFieldChangeFromRegisterId = actionDto?.payload?.external?.dataRegisterId;

        const populatedTransactionFields = tranFieldsChanged?.length
            ? await populateTransactionFieldService.populateTransactionFields(
                  {
                      formVersionFields: formVersion.fields,
                      transactionFields: tranFieldsChanged,
                  },
                  transaction.id,
                  {
                      targetFieldChangeIds: targetFieldChangeIds,
                      payloadDocuments: externalOriginReportReportField?.data?.report,
                  },
              )
            : [];

        tranFieldsChanged.forEach((f) => {
            const existed = populatedTransactionFields.find((pf) => pf.fieldId === f.fieldId);
            if (existed) {
                f.fieldOptionIds = existed.fieldOptionIds;
                f.fieldValue = existed.fieldValue;
            }
        });

        tranFieldsChanged.forEach((item) => {
            if (SELECTABLE_FIELD_TYPES.includes(fieldTypeMaps[item.fieldId])) {
                formValues[item.fieldId] = item.fieldOptionIds?.[0];
                item.fieldValue = item.fieldOptionIds?.[0];
            } else {
                formValues[item.fieldId] = item.fieldValue;
            }
        });

        const stage = await formTransactionTenancyService.getStageInfo(transaction.stageId);
        if (stage?.config.type == 'END') {
            this._loggerService.log('Transaction ' + transaction.id + ' is END stage, skip refresh');
            return { type: 'TriggeredActionSuccess', metadata: { message: `Transaction ${transaction.id} is END stage, skip refresh` } };
        }

        this._loggerService.debug('refresh transaction fields for transactionID: ' + transaction.id);

        if (tranFieldsChanged?.length) {
            tranFieldsChanged.forEach((field) => {
                targetFieldChangeIds.push(field.fieldId);
            });
        }

        const options: UpdateTransactionOption = {
            targetFieldChangeIds: uniq(targetFieldChangeIds),
            shouldRunPopulateFormFields: true,
            captureExistLookupField: true,
            forceRunAutoPopulate: false,
            ignoreAutoPopulateCollection: true,
            targetFieldChangeFromRegisterId: targetFieldChangeFromRegisterId,
        };

        await formTransactionDataService.update({
            id: transaction.id,
            request: {
                transactionId: transaction.id,
                formId: transaction.formId,
                transactionFields: tranFieldsChanged,
                formValues: formValues,
            },
            option: options,
            user: RequestContextService.currentUser() ?? user,
        });
    }

    private async _processFormFieldsTransaction(actionDto: ParsedActionDto, transactionIds: string[], user?: Record<string, unknown>) {
        const { formTransactionTenancyService, formTransactionDataService, populateTransactionFieldService } = await this._resolveServices(
            actionDto.metadata.tenantId,
            user,
        );
        const { transactions, transactionFields: tranFields } = await formTransactionTenancyService.getWithFieldsByIds(transactionIds);
        const newTransactionFields = actionDto.payload.external?.transactionFields ?? [];

        if (!tranFields.length || !newTransactionFields?.length) {
            return { type: 'TriggeredActionSuccess', metadata: { message: 'No transaction to be refreshed' } };
        }

        const updatedTrans = [];
        let externalOriginReportReportFields: TransactionFieldEntity[] = [];

        for (let j = 0; j < tranFields.length; j++) {
            const formTransactionField = tranFields[j];
            const newTransactionField = newTransactionFields.find(
                (newTransactionField) => newTransactionField.fieldId === formTransactionField.fieldId,
            );
            if (newTransactionField) {
                if (
                    newTransactionField.fieldValue != formTransactionField.fieldValue &&
                    !updatedTrans.includes(formTransactionField.transactionId)
                ) {
                    formTransactionField.fieldValue = newTransactionField.fieldValue;
                    updatedTrans.push(formTransactionField.transactionId);
                }
            }

            if (formTransactionField.fieldId === EXTERNAL_ORIGINAL_VERSION_ID) {
                externalOriginReportReportFields.push(formTransactionField);
            }
        }

        for (const transactionId of updatedTrans) {
            const processingTran = transactions.find((t) => t.id === transactionId);
            const transactionFields = tranFields.filter((tf) => tf.transactionId === transactionId);
            const externalOriginReportReportField = externalOriginReportReportFields.find((tf) => transactionId === tf.transactionId);
            await this._processFormFieldBeforeUpdate(
                {
                    actionDto,
                    transaction: processingTran,
                    tranFields: transactionFields,
                    externalOriginReportReportField,
                    refreshFor: 'form_fields',
                },
                {
                    formTransactionDataService,
                    populateTransactionFieldService,
                    formTransactionTenancyService,
                },
                user,
            );
        }
    }

    private async _processCollectionTransaction(actionDto: ParsedActionDto, transactionIds: string[], user?: Record<string, unknown>) {
        const { formTransactionDataService, formTransactionTenancyService } = await this._resolveServices(
            actionDto.metadata.tenantId,
            user,
        );

        for (const transactionId of transactionIds) {
            const transaction = await formTransactionDataService.getById(transactionId);
            const targetFieldChangeIds = actionDto?.payload?.external?.fieldChanged?.length
                ? actionDto.payload.external.fieldChanged.map((item) => item.fieldId)
                : [];
            const stage = await formTransactionTenancyService.getStageInfo(transaction.stageId);
            if (stage?.config.type == 'END') {
                this._loggerService.log('Transaction ' + transaction.id + ' is END stage, skip refresh');
                continue;
            }
            await formTransactionDataService.update({
                id: transaction.id,
                request: {
                    transactionId: transaction.id,
                    formId: transaction.formId,
                    transactionFields: [],
                },
                option: {
                    shouldRunPopulateFormFields: true,
                    forceRunAutoPopulate: true,
                    forceUsingTransactionFieldValues: true,
                    onlyUpdateIfRelevantRegisterRecordIds: [actionDto.payload.external.id],
                    targetFieldChangeIds: targetFieldChangeIds,
                },
                user: RequestContextService.currentUser(),
            });
            await formTransactionDataService.update({
                id: transaction.id,
                request: {
                    transactionId: transaction.id,
                    formId: transaction.formId,
                    transactionFields: [],
                },
                option: {
                    shouldRunPopulateFormFields: true,
                    forceRunAutoPopulate: true,
                    forceUsingTransactionFieldValues: true,
                    onlyUpdateIfRelevantRegisterRecordIds: [actionDto.payload.external.id],
                    targetFieldChangeIds: targetFieldChangeIds,
                },
                user: RequestContextService.currentUser(),
            });
        }
    }

    private async _resolveServices(accountId: string, user?: Record<string, unknown>) {
        const formTransactionTenancyService = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            accountId,
            FormTransactionTenancyService,
            undefined,
            {
                user: user ?? null,
            },
        );
        const formTransactionDataService = await this._dataSourceService.resolveService<FormTransactionDataService>(
            accountId,
            FormTransactionDataService,
            undefined,
            {
                user: user ?? null,
            },
        );
        const populateTransactionFieldService = await this._dataSourceService.resolveService<PopulateTransactionFieldService>(
            accountId,
            PopulateTransactionFieldService,
            undefined,
            {
                user: user ?? null,
            },
        );
        return {
            formTransactionTenancyService,
            formTransactionDataService,
            populateTransactionFieldService,
        };
    }
}
