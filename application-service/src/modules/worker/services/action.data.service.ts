import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';

import { CacheService, LoggerService } from '../../../common/src';
import { CommonEvent } from '../../../common/src/modules/shared/domain/events';
import { ActionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/action-event.enum';
import { AutomationContextType } from '../../../database/src/shared/enums/automation.enum';

import { ActionTriggerHandler } from '../action-handlers/common/action-trigger.handler';
import { Q88HVPQActionHandler } from '../action-handlers/external-actions/data-pipeline/q88-hvpq-action.handler';
import { ExportPdfActionHandler } from '../action-handlers/external-actions/export-pdf-action.handler';
import { ExternalDocumentActionHandler } from '../action-handlers/external-actions/external-document.handler';
import { WebhookActionHandler } from '../action-handlers/external-actions/send-webhook.handler';
import { SendEmailActionHandler } from '../action-handlers/external-actions/sent-mail.handler';
import { CreateOrUpdateRegisterActionHandler } from '../action-handlers/register-actions/create-or-update-register.handler';
import { CreateRegisterRecordActionHandler } from '../action-handlers/register-actions/create-register-record.handler';
import { CreateTransactionFromDatasourceActionHandler } from '../action-handlers/register-actions/create-transaction-from-datasource.handler';
import { RollupRegisterRecordsActionHandler } from '../action-handlers/register-actions/rollup-register-records.handler';
import { UpdateRegisterRecordActionHandler } from '../action-handlers/register-actions/update-register-record.handler';
import { ChangeTransactionWFSActionHandler } from '../action-handlers/transaction-actions/change-transaction-wfs.handler';
import { CreateRelatedTransactionActionHandler } from '../action-handlers/transaction-actions/create-related-transaction.handler';
import { CreateTransactionActionHandler } from '../action-handlers/transaction-actions/create-transaction.handler';
import { DocPdfExtractionHandler } from '../action-handlers/transaction-actions/doc-pdf-extraction.handler';
import { FormTransactionRefreshCollectionFieldsService } from '../action-handlers/transaction-actions/form-transaction-refresh-collection-fields.handler';
import { FormTransactionRefreshFormFieldsService } from '../action-handlers/transaction-actions/form-transaction-refresh-form-fields.handler';
import { FormTransactionRefreshService } from '../action-handlers/transaction-actions/form-transaction-refresh.handler';
import { PopulatePurpleTRACHandler } from '../action-handlers/transaction-actions/populate-purpletrac.handler';
import { PopulateQ88Handler } from '../action-handlers/transaction-actions/populate-Q88.handler';
import { RefreshDataRegisterDisplayValueHandler } from '../action-handlers/transaction-actions/refresh-data-register-display-value.handler';
import { RollupTransactionsActionHandler } from '../action-handlers/transaction-actions/rollup-transactions.handler';
import { RunLLMActionHandler } from '../action-handlers/transaction-actions/run-llm.handler';
import { ScoringSystemHandler } from '../action-handlers/transaction-actions/scoring-system.handler';
import { UpdateCollectionFieldActionHandler } from '../action-handlers/transaction-actions/update-collection.handler';
import { UpdateTransactionActionHandler } from '../action-handlers/transaction-actions/update-transaction.handler';
import { ParsedActionDto } from '../dtos/action.dto';

@Injectable()
export class ActionDataService {
    constructor(
        private readonly _loggerService: LoggerService,
        private readonly _moduleRef: ModuleRef,

        private readonly _cacheService: CacheService,

        private readonly _changeTransactionWFSActionHandler: ChangeTransactionWFSActionHandler,
        private readonly _createTransactionActionHandler: CreateTransactionActionHandler,
        private readonly _emailActionHandler: SendEmailActionHandler,
        private readonly _updateTransactionActionHandler: UpdateTransactionActionHandler,
        private readonly _createRegisterRecordActionHandler: CreateRegisterRecordActionHandler,
        private readonly _updateRegisterRecordActionHandler: UpdateRegisterRecordActionHandler,
        private readonly _exportPdfActionHandler: ExportPdfActionHandler,
        private readonly _externalDocumentActionHandler: ExternalDocumentActionHandler,
        private readonly _createUpdateStatusActionHandler: CreateOrUpdateRegisterActionHandler,
        private readonly _formTransactionRefreshService: FormTransactionRefreshService,
        private readonly _formTransactionRefreshOneService: FormTransactionRefreshFormFieldsService,
        private readonly _formTransactionRefreshCollectionFieldsService: FormTransactionRefreshCollectionFieldsService,
        private readonly _updateCollectionFieldActionHandler: UpdateCollectionFieldActionHandler,
        private readonly _createTransactionFromDatasource: CreateTransactionFromDatasourceActionHandler,

        private readonly _docPdfExtractionHandler: DocPdfExtractionHandler,

        private readonly _createRelatedTransactionActionHandler: CreateRelatedTransactionActionHandler,

        private _actionTriggerHandler: ActionTriggerHandler,

        private readonly _populatePurpleTRACHandler: PopulatePurpleTRACHandler,

        private readonly _refreshDataRegisterDisplayValueHandler: RefreshDataRegisterDisplayValueHandler,
        private readonly _scoringSystemHandler: ScoringSystemHandler,
        private readonly _rollupTransactionsActionHandler: RollupTransactionsActionHandler,
        private readonly _rollupRegisterRecordsActionHandler: RollupRegisterRecordsActionHandler,
        private readonly _populateQ88Handler: PopulateQ88Handler,
        private readonly _q88HVPQActionHandler: Q88HVPQActionHandler,
        private readonly _webhookActionHandler: WebhookActionHandler,
        private readonly _runLLMActionHandler: RunLLMActionHandler,
    ) {
        this.initActionHandlers();
    }

    private async initActionHandlers(): Promise<void> {
        this._actionTriggerHandler.registerActionHandlers([
            {
                type: ActionEventEnum.CHANGE_TRANSACTION_WORKFLOW_STAGE,
                handler: this._changeTransactionWFSActionHandler,
            },
            {
                type: ActionEventEnum.CREATE_TRANSACTION,
                handler: this._createTransactionActionHandler,
            },
            {
                type: ActionEventEnum.EMAIL,
                handler: this._emailActionHandler,
            },
            {
                type: ActionEventEnum.UPDATE_TRANSACTION_FIELD,
                handler: this._updateTransactionActionHandler,
            },
            {
                type: ActionEventEnum.CREATE_REGISTER,
                handler: this._createRegisterRecordActionHandler,
            },
            {
                type: ActionEventEnum.UPDATE_REGISTER_FIELD,
                handler: this._updateRegisterRecordActionHandler,
            },
            {
                type: ActionEventEnum.EXPORT_PDF,
                handler: this._exportPdfActionHandler,
            },
            {
                type: ActionEventEnum.CREATE_TRANSACTION_BY_EXTERNAL,
                handler: this._externalDocumentActionHandler,
            },
            {
                type: ActionEventEnum.CREATE_UPDATE_REGISTER,
                handler: this._createUpdateStatusActionHandler,
            },
            {
                type: ActionEventEnum.REFRESH_FROM_TRANSACTION_DATA,
                handler: this._formTransactionRefreshService,
            },
            {
                type: ActionEventEnum.DOC_PDF_EXTRACTION,
                handler: this._docPdfExtractionHandler,
            },
            {
                type: ActionEventEnum.REFRESH_FROM_TRANSACTION_FIELDS_DATA,
                handler: this._formTransactionRefreshOneService,
            },
            {
                type: ActionEventEnum.REFRESH_FROM_TRANSACTION_COLLECTIONS_DATA,
                handler: this._formTransactionRefreshCollectionFieldsService,
            },
            {
                type: ActionEventEnum.CREATE_RELATED_TRANSACTION,
                handler: this._createRelatedTransactionActionHandler,
            },
            {
                type: ActionEventEnum.UPDATE_COLLECTION_FIELD,
                handler: this._updateCollectionFieldActionHandler,
            },
            {
                type: ActionEventEnum.POPULATE_PURPLETRAC,
                handler: this._populatePurpleTRACHandler,
            },
            {
                type: ActionEventEnum.REFRESH_DATA_REGISTER_DISPLAY_VALUE,
                handler: this._refreshDataRegisterDisplayValueHandler,
            },
            {
                type: ActionEventEnum.CALCULATE_SYSTEM_SCORE,
                handler: this._scoringSystemHandler,
            },
            {
                type: ActionEventEnum.ROLLUP_FORM_TRANSACTIONS,
                handler: this._rollupTransactionsActionHandler,
            },
            {
                type: ActionEventEnum.ROLLUP_DATA_REGISTER_TRANSACTIONS,
                handler: this._rollupRegisterRecordsActionHandler,
            },
            {
                type: ActionEventEnum.CREATE_TRANSACTION_BY_DATASOURCE,
                handler: this._createTransactionFromDatasource,
            },
            {
                type: ActionEventEnum.POPULATE_Q88,
                handler: this._populateQ88Handler,
            },
            {
                type: ActionEventEnum.Q88_HVPQ_DATA_POPULATE,
                handler: this._q88HVPQActionHandler,
            },
            {
                type: ActionEventEnum.WEBHOOK,
                handler: this._webhookActionHandler,
            },
            {
                type: ActionEventEnum.RUN_LLM,
                handler: this._runLLMActionHandler,
            },
        ]);
    }

    public async handleEvent(event: CommonEvent): Promise<void> {
        // Step 1: Parse event
        const parsedEvent = ActionDataService.parseAllEvent(event);

        // Step 2: Check loop
        const alreadyProcessed = await this._cacheService.get(`${event.metadata.correlationId}:${event.payload.actionId}`);
        if (alreadyProcessed) {
            this._loggerService.warn(
                `[ACTION] ${parsedEvent.type} Event ${event.metadata.correlationId} already processed (loop detected)!`,
            );
            return;
        }

        // Step 2.1: Check if correlation is rollback
        const alreadyRollback = await this._cacheService.get(`${event.metadata.correlationId}:rollback`);
        if (alreadyRollback) {
            this._loggerService.warn(
                `[ACTION] ${parsedEvent.type} Event ${event.metadata.correlationId} can't be processed (rollback detected)!`,
            );
            return;
        }

        if (!parsedEvent.type) return;

        // Step 3: Execute action based on Event Type
        await this._actionTriggerHandler.triggerAction(parsedEvent.type, { event });

        // Step 4: Cache correlationId for checking loop
        await this._cacheService.set(`${event.metadata.correlationId}:${event.payload.actionId}`, '1', 1000 * 60 * 5); // 5 min
    }

    public async handleRollbackEvent(event: CommonEvent): Promise<void> {
        const alreadyProcessed = await this._cacheService.get(`${event.metadata.correlationId}:rollback`);
        if (alreadyProcessed) {
            this._loggerService.warn(`[ROLLBACK ACTION] ${event.metadata.type} Event ${event.metadata.correlationId} already processed!`);
            return;
        }

        await this._actionTriggerHandler.triggerSagaRollback({ event });

        await this._cacheService.set(`${event.metadata.correlationId}:rollback`, '1', 1000 * 60 * 60); // 60 min
    }

    public static parseAllEvent(event: CommonEvent): ParsedActionDto {
        const parsedEvent: Record<string, any> = {};
        const type = event.metadata.type;

        if (!type) {
            throw new Error('Event type is required');
        }

        if (type.startsWith('ACTION.')) {
            parsedEvent.type = type as ActionEventEnum;
            parsedEvent.contextType = event.payload?.context?.type;
            parsedEvent.contextId = event.payload?.context?.id;
            parsedEvent.contextVersionId = event.payload?.context?.versionId;
            parsedEvent.contextTransactionId = event.payload?.context?.transactionId;
        } else {
            throw new Error('Event type is not supported');
        }

        return {
            type: parsedEvent.type,
            contextType: parsedEvent.contextType as AutomationContextType,
            contextId: parsedEvent.contextId,
            contextVersionId: parsedEvent.contextVersionId,
            contextTransactionId: parsedEvent.contextTransactionId,
        };
    }
}
