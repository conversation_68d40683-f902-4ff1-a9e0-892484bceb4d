import { Injectable } from '@nestjs/common';
import { Core<PERSON>onfig, JsonTree, Utils as QbUtils } from '@react-awesome-query-builder/core';
import { apply } from 'json-logic-js';
import { groupBy } from 'lodash';
import { DataSource } from 'typeorm';
import { LoggerService } from '../../../common/src/modules/shared';
import { SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { DataRegisterFieldEntity } from '../../../database/src/entities/public/data-register-fields.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../../database/src/entities/public/form-field.public.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { getFieldValue, getFieldValueBasedOnPicker } from '../../../utils/format-data';
import { FormTransactionDto, FormTransactionFieldDto } from '../../form/dtos';
import { EditFormTransactionRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { FormTransactionTenancyService } from '../../form/services/form-transaction.tenancy.service';
import { ActionContext } from '../action-handlers/common/action-types';
import { TransactionUpdateDto } from '../dtos/transaction-creation.dto';

@Injectable()
export class UpdateCollectionService {
    // validateRequiredActionParams(parsedEvent: ParsedActionDto): ValidateEventResult {
    //     const formId = parsedEvent.contextTransactionId;
    //     if (!formId) {
    //         return {
    //             type: 'EventValidatedFailed',
    //             message: `[ACTION] ${parsedEvent.type} Form with id ${formId} not found in configuration!`,
    //         };
    //     }

    //     const updateFields = parsedEvent.payload?.configuration?.updateFields;

    //     if (!updateFields?.length) {
    //         return {
    //             type: 'EventValidatedFailed',
    //             message: `[ACTION] ${parsedEvent.type} Update fields not found in configuration!`,
    //         };
    //     }

    //     return {
    //         type: 'EventValidatedSuccess',
    //     };
    // }

    // async handleAction(parsedEvent: ParsedActionDto): Promise<HandleActionResult> {
    //     try {
    //         // const result = await this._transCreationService.updateCollectionField({
    //         //     transactionId: parsedEvent.contextTransactionId,
    //         //     parsedEvent,
    //         // });
    //         return { type: 'TriggeredActionSuccess', metadata: null };
    //     } catch (error) {
    //         Logger.error(error);
    //         return {
    //             type: 'TriggeredActionErrorGoRetry',
    //             message: error.message,
    //         };
    //     }
    // }

    constructor(
        private readonly _dataSourceService: DataSourceService,
        private readonly _loggerService: LoggerService,
    ) {}

    public async updateCollectionField({ transactionId, parsedEvent }: TransactionUpdateDto): Promise<any> {
        const transaction = await this._getTransaction(transactionId, parsedEvent.metadata.tenantId);

        // Purpose for Testing Step on Form Builder
        if (!transaction || (!transaction.isTest && transaction.deletedAt)) {
            return;
        }

        const collectionId = parsedEvent.payload?.configuration?.collectionId;
        const conditions: JsonTree = parsedEvent.payload?.configuration?.conditions;
        const updateFields = parsedEvent.payload?.configuration?.updateFields;

        const collectionFields = transaction.transactionFields.filter(
            (field) => field.contextType === TransactionFieldContextTypeEnum.COLLECTION && field.collectionId === collectionId,
        );

        //TODO: filter collectionFields by conditions
        const jsonLogic = conditions?.children1?.length
            ? QbUtils.jsonLogicFormat(QbUtils.loadTree(conditions as JsonTree), CoreConfig)
            : null;

        const collectionGroups = groupBy(collectionFields, 'rowKey');

        const variables = jsonLogic ? (this.extractVars(jsonLogic.logic) as string[]) : [];

        const collectionFieldsQuery = [];
        Object.entries(collectionGroups).forEach(([rowKey, data]) => {
            const _fields = {
                rowKey: rowKey,
            };

            for (const variable of variables) {
                const field = data.find((f) => f.fieldId === variable);
                if (field) {
                    _fields[variable] = field?.fieldOptionIds?.length
                        ? field?.fieldOptionIds.sort((a, b) => a.localeCompare(b)).join(',')
                        : (field?.fieldValue ?? '');
                } else {
                    _fields[variable] = '';
                }
            }

            collectionFieldsQuery.push(_fields);
        });

        // Apply JSON Logic filter to collection fields
        const _collectionFields = collectionFieldsQuery.filter((item) => apply(jsonLogic?.logic, item));

        if (!_collectionFields?.length) {
            return;
        }

        const accountId = parsedEvent?.metadata?.tenantId;

        const dataSource = await this._dataSourceService.createAccountDataSource(accountId);

        const { dataRegisterFieldRepo, formFieldRepo, formCollectionRepo } = this._getRepositories(dataSource, !!accountId);

        const formCollection = await formCollectionRepo.findOne({
            where: {
                identityId: collectionId,
                formVersionId: transaction.formVersionId,
            },
        });

        const tasks = [
            formFieldRepo.find({
                where: {
                    formVersionId: transaction.formVersionId,
                },
            }),
        ];

        if (formCollection?.dataRegisterVersionId) {
            tasks.push(
                dataRegisterFieldRepo.find({
                    where: {
                        dataRegisterVersionId: formCollection.dataRegisterVersionId,
                    },
                }),
            );
        }

        const [formFields, dataRegisterFields] = (await Promise.all(tasks)) as [FormFieldEntity[], DataRegisterFieldEntity[]];

        const transactionFields: FormTransactionFieldDto[] = [];
        _collectionFields.forEach((collectionField) => {
            const fields = collectionGroups?.[collectionField.rowKey];

            updateFields.forEach((uf) => {
                const field = fields.find((f) => f.fieldId === uf.fieldId);
                const dataRegisterField = dataRegisterFields.find((df) => df.fieldId === uf.fieldId);
                const fieldType = dataRegisterField?.type;

                let newField;

                if (field) {
                    newField = { ...field, fieldOptionIds: [] };
                } else {
                    newField = {
                        ...fields?.[0],
                        fieldId: uf.fieldId,
                        fieldType: dataRegisterField?.type,
                        data: {
                            defaultValue: dataRegisterField?.configuration?.defaultValue,
                            defaultFieldType: dataRegisterField?.type,
                        },
                        fieldOptionIds: [],
                    };
                }

                if (uf.fieldValueSource === 'field') {
                    switch (uf.typeName) {
                        case 'form':
                            {
                                const transactionField = transaction.transactionFields.find(
                                    (f) => f.fieldId === uf.fieldValue,
                                ) as TransactionFieldEntity;
                                const value = getFieldValue(dataRegisterField, transactionField);

                                newField.fieldValue = value;
                            }
                            break;
                        case 'collection_item':
                            break;
                    }
                } else {
                    const value = getFieldValueBasedOnPicker({
                        pickerType: uf?.pickerType,
                        value: uf?.defaultValue,
                        fieldValue: uf?.fieldValue,
                        fieldType: dataRegisterField.type,
                    });

                    newField.fieldValue = value;
                }

                if (fieldType && SELECTABLE_FIELD_TYPES.includes(fieldType)) {
                    newField.fieldOptionIds = Array.isArray(newField.fieldValue)
                        ? newField.fieldValue
                        : (newField.fieldValue ?? '').split(',');
                }

                transactionFields.push(newField);
            });
        });

        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            parsedEvent.metadata.tenantId,
            FormTransactionTenancyService,
        );
        const stageEntity = await service.getStageInfo(transaction.stageId);

        const request: EditFormTransactionRequest = {
            transactionId: transactionId,
            transactionFields,
            formId: transaction.formId,
            formValues: {},
            isTest: transaction.isTest,
        };

        await service.update(transactionId, request, {
            updatingCollectionFields: [],
            isModify: true,
            ignoreAutoPopulateCollection: true,
            shouldRunPopulateFormFields: false,
        });

        return {
            transactionId: transaction.id,
            formId: transaction.formId,
            activeStageId: stageEntity.id,
            activeStageIdentityId: stageEntity.identityId,
            stageId: stageEntity.id,
            stageIdentityId: stageEntity.identityId,
            transactionFields: [],
        };
    }

    private async _getTransaction(transactionId: string, tenantId: string): Promise<FormTransactionDto> {
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            tenantId,
            FormTransactionTenancyService,
        );

        try {
            // Purpose for Testing Step on Form Builder
            return await service.get(transactionId, {
                isTest: true,
                includeOriginTransactionIds: false,
                includeRelatedTransactionIds: false,
            });
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    async rollbackUpdateTransaction(context: ActionContext, actionResult: Record<string, any>) {
        const metadata = actionResult?.metadata;
        const transactionFields = metadata?.transactionFields.map((field) => {
            return { fieldId: field.fieldId, fieldValue: field.currentValue, fieldType: field.type };
        });

        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            context?.event?.metadata?.tenantId,
            FormTransactionTenancyService,
        );

        const request: EditFormTransactionRequest = {
            transactionId: metadata?.transactionId,
            transactionFields,
            formId: metadata?.formId,
        };
        try {
            await service.update(metadata?.transactionId, request);
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    private _getRepositories(dataSource: DataSource, isAccount: boolean) {
        const dataRegisterFieldRepo = isAccount
            ? dataSource.getRepository(DataRegisterFieldTenancyEntity)
            : dataSource.getRepository(DataRegisterFieldEntity);

        const formFieldRepo = isAccount ? dataSource.getRepository(FormFieldTenancyEntity) : dataSource.getRepository(FormFieldEntity);

        const formCollectionRepo = isAccount
            ? dataSource.getRepository(FormCollectionTenancyEntity)
            : dataSource.getRepository(FormCollectionEntity);

        return {
            dataRegisterFieldRepo,
            formFieldRepo,
            formCollectionRepo,
        };
    }

    private extractVars(logic: Record<string, any>) {
        let vars = [];
        if (logic.var) {
            vars.push(logic.var);
        } else {
            for (let key in logic) {
                if (Array.isArray(logic[key])) {
                    logic[key].forEach((item) => {
                        vars = vars.concat(this.extractVars(item));
                    });
                }
            }
        }
        return vars;
    }
}
