import { Injectable } from '@nestjs/common';
import { LoggerService, UtilsService } from '../../../common/src';
import { DATA_REGISTER_RECORD_ID, DEFAULT_STAGE_KPI_FIELD_ID } from '../../../constant';
import { SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { AutomationContextType } from '../../../database/src/shared/enums/automation.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { FormTransactionDto, FormTransactionFieldDto } from '../../../modules/form/dtos';
import {
    EditFormTransactionFieldRequest,
    EditFormTransactionRequest,
} from '../../../modules/form/dtos/requests/create-form-transaction.request';
import { DatePickerTypeEnum } from '../../../shared/enums/date-picker.enum';
import { GeneralAutoPopulateService } from '../../../shared/services';
import { convertDateTimeValueToDate, convertDateTimeValueToDateTime, convertDateTimeValueToMinutes } from '../../../utils';
import { DataRegisterTransactionTenancyService } from '../../data-register/services/data-register-transaction.tenancy.service';
import { FormTransactionTenancyService } from '../../form/services/form-transaction.tenancy.service';
import { PopulateTransactionFieldService } from '../../form/services/populate-transaction-field.service';
import { ActionContext } from '../action-handlers/common/action-types';
import { ParsedActionDto } from '../dtos/action.dto';
import {
    RunGeneralAutoPopulateDto,
    TransactionCreationDto,
    TransactionUpdateAfterCreateDto,
    TransactionUpdateDto,
    TransactionUpdateWithFieldsDto,
} from '../dtos/transaction-creation.dto';

function isValidValue(value) {
    return value !== undefined && value !== null && value !== '' && !Number.isNaN(value);
}
@Injectable()
export class TransactionCreationService {
    constructor(
        private readonly _dataSourceService: DataSourceService,
        private readonly _loggerService: LoggerService,
    ) {}

    async createEmptyTransaction(eventDto: TransactionCreationDto): Promise<TransactionEntity> {
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            eventDto.accountId,
            FormTransactionTenancyService,
        );
        try {
            const transaction = (await service.createEmptyTransaction({
                formId: eventDto.formId,
                formVersionId: eventDto.formVersionId,
                timezone: eventDto.timezone,
                isReturnTrans: true,
            })) as TransactionEntity;
            return transaction;
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    calculationFieldValue(field: FormTransactionFieldDto, config: Record<string, any>) {
        let value: any;
        let type = field?.type ?? '';
        switch (type) {
            case FormFieldTypeEnum.DatetimePicker: {
                const pickerType = config.pickerType;
                let intiValue: any;
                if (
                    [
                        DatePickerTypeEnum.FIXED,
                        DatePickerTypeEnum.NOW,
                        DatePickerTypeEnum.FOLLOWING_DURATION,
                        DatePickerTypeEnum.PRECEDING_DURATION,
                    ].includes(pickerType)
                ) {
                    intiValue = config.defaultValue ?? config.fieldValue;
                } else if ([DatePickerTypeEnum.SET_NULL].includes(pickerType)) {
                    intiValue = null;
                }
                value = convertDateTimeValueToDateTime({
                    pickerType: config.pickerType,
                    value: field.fieldValue ?? intiValue,
                    timezone: config.timezone,
                });
                value = value?.toString();
                break;
            }
            case FormFieldTypeEnum.TimePicker: {
                if (field.fieldValue) {
                    value = field.fieldValue;
                } else {
                    const pickerType = config.pickerType;
                    let intiValue: any;
                    if (
                        [
                            DatePickerTypeEnum.FIXED,
                            DatePickerTypeEnum.NOW,
                            DatePickerTypeEnum.NEXT_HOURS,
                            DatePickerTypeEnum.PREVIOUS_HOURS,
                        ].includes(pickerType)
                    ) {
                        intiValue = config.defaultValue ?? config.fieldValue;
                    } else if ([DatePickerTypeEnum.SET_NULL].includes(pickerType)) {
                        intiValue = null;
                    }
                    value = convertDateTimeValueToMinutes({
                        pickerType: config.pickerType,
                        value: intiValue,
                        timezone: config.timezone,
                    });
                }

                break;
            }
            case FormFieldTypeEnum.DatePicker: {
                const pickerType = config.pickerType;
                let intiValue: any;
                if ([DatePickerTypeEnum.FIXED, DatePickerTypeEnum.FOLLOWING_DAYS, DatePickerTypeEnum.PRECEDING_DAYS].includes(pickerType)) {
                    intiValue = config.defaultValue ?? config.fieldValue;
                } else if ([DatePickerTypeEnum.SET_NULL].includes(pickerType)) {
                    intiValue = null;
                }
                value = convertDateTimeValueToDate({
                    pickerType: config.pickerType,
                    value: field.fieldValue ?? intiValue,
                    timezone: config.timezone,
                });
                value = value?.toString();
                break;
            }
            default: {
                if (isValidValue(config.fieldValue)) value = config.fieldValue;
                else if (isValidValue(field.fieldValue)) value = field.fieldValue;
                else value = config?.fieldDefaultValue;
            }
        }

        return value;
    }

    async getTransaction(transactionId: string, tenantId: string): Promise<FormTransactionDto> {
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            tenantId,
            FormTransactionTenancyService,
        );
        try {
            return await service.get(transactionId, {
                isTest: true,
                includeOriginTransactionIds: false,
                includeRelatedTransactionIds: false,
            });
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    async updateTransaction({ transactionId, parsedEvent }: TransactionUpdateDto): Promise<any> {
        const transaction = await this.getTransaction(transactionId, parsedEvent.metadata.tenantId);

        // Purpose for Testing Step on Form Builder
        if (!transaction || (!transaction.isTest && transaction.deletedAt)) {
            return;
        }

        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            parsedEvent.metadata.tenantId,
            FormTransactionTenancyService,
        );
        const stageEntity = await service.getStageInfo(transaction.stageId);
        // field's value mapping
        const fieldIds = parsedEvent.payload.configuration.formFields.map((f) => f.fieldId);
        const fromTransactions = await service.getFields(parsedEvent.payload.context.transactionId, fieldIds, transaction.isTest);
        const fieldConfiguration = await service.getFieldConfiguration({ fieldIds, formVersionId: fromTransactions.formVersionId });
        const fieldsMap = {};
        fieldConfiguration.map((f) => (fieldsMap[f.fieldId] = f));
        fromTransactions.transactionFields.map((f) => {
            const existedField = fieldsMap[f.fieldId] || {};
            fieldsMap[f.fieldId] = { ...existedField, ...f, transactionFieldId: existedField.id };
        });
        const result = {
            transactionId: transaction.id,
            formId: transaction.formId,
            activeStageId: stageEntity.id,
            activeStageIdentityId: stageEntity.identityId,
            stageId: stageEntity.id,
            stageIdentityId: stageEntity.identityId,
            transactionFields: [],
        };

        const transactionFields = parsedEvent.payload.configuration.formFields.map((config) => {
            if (config?.fieldId === DEFAULT_STAGE_KPI_FIELD_ID) {
                result.transactionFields.push({
                    currentValue: null,
                    newValue: config.fieldValue,
                    fieldId: config.fieldId,
                    fieldType: FormFieldTypeEnum.StageKpi,
                    validationValue: config.validationValue,
                });
                return {
                    fieldValue: config.fieldValue,
                    fieldId: config.fieldId,
                    fieldType: FormFieldTypeEnum.StageKpi,
                    contextType: TransactionFieldContextTypeEnum.FORM,
                    validationValue: config.validationValue,
                };
            }
            const field = fieldsMap[config?.fieldId];
            const fieldValue = this.calculationFieldValue(field, config);
            result.transactionFields.push({
                currentValue: field.fieldValue,
                newValue: fieldValue,
                fieldId: field.fieldId,
                fieldType: field.type,
            });
            return {
                fieldId: field.fieldId,
                fieldValue: fieldValue,
                fieldType: field.type,
                contextType: field?.contextType ?? TransactionFieldContextTypeEnum.FORM,
            };
        });
        // done field's value mapping

        const request: EditFormTransactionRequest = {
            transactionId: transaction.id,
            transactionFields,
            formId: transaction.formId,
            isTest: transaction.isTest,
        };
        try {
            await service.update(transaction.id, request);
            return result;
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    async rollbackUpdateTransaction(context: ActionContext, actionResult: Record<string, any>) {
        const metadata = actionResult?.metadata;
        const transactionFields = metadata?.transactionFields.map((field) => {
            // Rollback to old value
            return { fieldId: field.fieldId, fieldValue: field.currentValue, fieldType: field.type };
        });

        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            context?.event?.metadata?.tenantId,
            FormTransactionTenancyService,
        );

        const request: EditFormTransactionRequest = {
            transactionId: metadata?.transactionId,
            transactionFields,
            formId: metadata?.formId,
        };
        try {
            await service.update(metadata?.transactionId, request);
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    private _getFromFieldMappings = async ({
        tranService,
        parsedEvent,
    }: {
        tranService: FormTransactionTenancyService;
        parsedEvent: ParsedActionDto;
    }) => {
        const fromFieldsMap = {};
        const fromFieldIds = parsedEvent.payload.configuration.mappingFields.map((f) => f.fromFieldId);

        if (parsedEvent.contextType === AutomationContextType.FormTransaction) {
            const fromFormTransactions = await tranService.getFieldsByFormFieldIds(parsedEvent.payload.context.transactionId, fromFieldIds);
            fromFormTransactions.transactionFields.map((f) => (fromFieldsMap[f.fieldId] = f));
        }

        if (parsedEvent.contextType === AutomationContextType.DataRegister) {
            const registerService = await this._dataSourceService.resolveService<DataRegisterTransactionTenancyService>(
                parsedEvent.metadata.tenantId,
                DataRegisterTransactionTenancyService,
            );

            const fromRegisterTransactions = await registerService.get(parsedEvent.payload.context.transactionId);
            fromRegisterTransactions.transactionFields.map((f) => {
                if (fromFieldIds?.includes(f.fieldId)) fromFieldsMap[f.fieldId] = f;
            });
        }

        return fromFieldsMap;
    };

    async updateTransactionAfterCreate({ transaction, parsedEvent, formFields = [] }: TransactionUpdateAfterCreateDto): Promise<any> {
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            parsedEvent.metadata.tenantId,
            FormTransactionTenancyService,
        );
        try {
            const stageEntity = await service.getStageInfo(transaction.stageId);
            // field's value mapping

            // from field
            const fromFieldsMap = await this._getFromFieldMappings({
                tranService: service,
                parsedEvent,
            });

            // to field
            const toFieldIds = parsedEvent.payload.configuration.mappingFields.map((f) => f.toFieldId);
            const toFieldConfiguration = await service.getFieldConfiguration({
                fieldIds: toFieldIds,
                formVersionId: transaction.formVersionId,
            });
            const toFieldsMap = {};
            toFieldConfiguration.map((f) => (toFieldsMap[f.fieldId] = f));

            const lookupFields: Record<string, any> = {};
            const transactionFields: EditFormTransactionFieldRequest[] = parsedEvent.payload.configuration.mappingFields.map((config) => {
                const fromField = fromFieldsMap[config?.fromFieldId];
                const toField = toFieldsMap[config?.toFieldId];
                if (
                    !fromField &&
                    config?.fromFieldId === DATA_REGISTER_RECORD_ID &&
                    parsedEvent.contextType === AutomationContextType.DataRegister &&
                    parsedEvent.contextTransactionId
                ) {
                    toField.fieldValue = `${parsedEvent.contextTransactionId}`;
                } else {
                    toField.fieldValue = SELECTABLE_FIELD_TYPES.includes(fromField.type)
                        ? (fromField.fieldOptionIds || []).join(',')
                        : (fromField.fieldValue ?? null);
                }

                const fieldValue = this.calculationFieldValue(toField, { ...toField.configuration, ...config });

                if (toField.type === FormFieldTypeEnum.Lookup) {
                    lookupFields[config.toFieldId] = fieldValue;
                }

                return {
                    fieldId: config.toFieldId,
                    fieldValue,
                    fieldOptionIds: SELECTABLE_FIELD_TYPES.includes(toField.type)
                        ? fieldValue?.split(',')?.map((f) => f?.trim()) || []
                        : [],
                    fieldType: toField.type,
                    contextType: toField?.contextType ?? TransactionFieldContextTypeEnum.FORM,
                    transactionId: transaction.id,
                } as EditFormTransactionFieldRequest;
            });
            // done field's value mapping
            const formValues = (transactionFields || []).reduce(
                (prev, tf) => {
                    switch (tf.contextType) {
                        case TransactionFieldContextTypeEnum.FORM:
                            prev[tf.fieldId] = tf.fieldOptionIds ? tf.fieldOptionIds : tf.fieldValue;
                            break;
                        case TransactionFieldContextTypeEnum.COLLECTION:
                            const key = UtilsService.combineCollectionKeys({
                                collectionIdentityId: tf.collectionId,
                                collectionItemIdentityId: tf.collectionItemId,
                                fieldId: '',
                                collectionItemId: '',
                                collectionItemKey: tf.rowKey ?? '',
                                fieldIdentityId: tf.fieldId,
                            });
                            prev[key] = tf.fieldOptionIds ? tf.fieldOptionIds : tf.fieldValue;
                            break;
                    }

                    return prev;
                },
                {} as Record<string, string | string[]>,
            );

            const populatedFields = await this.getAutoPopulateValues({
                accountId: parsedEvent.metadata.tenantId,
                transactionId: transaction.id,
                formFields: formFields,
                transactionFields: transactionFields,
            });

            // const populateFieldService = await this._dataSourceService.resolveService<PopulateTransactionFieldService>(
            //     parsedEvent.metadata.tenantId,
            //     PopulateTransactionFieldService,
            // );

            // try {
            //     const populateFields = await populateFieldService.populateTransactionFields(
            //         {
            //             formVersionFields: formFields,
            //             transactionFields: (transactionFields || []).map((rf) => {
            //                 return {
            //                     id: rf.id,
            //                     fieldId: rf.fieldId,
            //                     fieldValue: rf.fieldOptionIds?.length ? rf.fieldOptionIds.join(',') : rf.fieldValue,
            //                     fieldOptionIds: rf.fieldOptionIds,
            //                     fieldType: rf.fieldType,
            //                     collectionId: rf.collectionId,
            //                     collectionItemId: rf.collectionItemId,
            //                     contextType: rf.contextType,
            //                     transactionId: rf.transactionId,
            //                     rowKey: rf.rowKey,
            //                 } as TransactionFieldEntity;
            //             }),
            //         },
            //         transaction.id,
            //     );

            //     const newTranFields = (populateFields || [])
            //         .filter((pf) => !(transactionFields || []).some((tf) => tf.fieldId === pf.fieldId))
            //         .map((pf) => {
            //             if (pf.fieldOptionIds?.length) {
            //                 pf.fieldValue = pf.fieldOptionIds.join(',');
            //             }
            //             return pf;
            //         });
            //     transactionFields.push(...newTranFields);
            // } catch (err) {
            //     this._loggerService.error(`Error populate transaction fields on automation for creation transaction: ${err}`);
            // }

            const request: EditFormTransactionRequest = {
                transactionId: transaction.id,
                transactionFields: [...(populatedFields ?? []), ...transactionFields],
                formId: transaction.formId,
                formValues,
            };

            await service.update(transaction.id, request);
        } catch (err) {
            await service.delete(transaction.id);
            this._loggerService.error(err);
        }
    }

    async updateTransactionWithFields({
        parsedEvent,
        transactionFields: transactionFieldParams,
        transactionStageId,
        transactionId,
        formId,
        updatingCollectionFields = [],
        isModify,
        payloadDocuments,
    }: TransactionUpdateWithFieldsDto & { isModify?: boolean; payloadDocuments?: Record<string, string> }): Promise<any> {
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            parsedEvent.metadata.tenantId,
            FormTransactionTenancyService,
        );
        try {
            const stageEntity = await service.getStageInfo(transactionStageId);
            const transactionFields: EditFormTransactionFieldRequest[] = [];

            if (transactionFieldParams?.length) {
                transactionFieldParams.forEach((item) => {
                    const transactionField = (transactionFields || []).find((field) => field.fieldId === item.fieldId);
                    if (transactionField) {
                        transactionField.fieldValue = item.fieldValue;
                        if (item.data) {
                            transactionField.data = {
                                ...(transactionField.data ?? {}),
                                ...item.data,
                            };
                        }
                    } else {
                        transactionFields.push({
                            fieldId: item.fieldId,
                            fieldValue: item.fieldValue,
                            fieldType: item.fieldType,
                            data: item.data,
                            contextType: TransactionFieldContextTypeEnum.FORM,
                        });
                    }
                });
            }
            // done field's value mapping

            const formValues = transactionFields.reduce(
                (prev, curr) => {
                    return { ...prev, [curr.fieldId]: curr.fieldValue };
                },
                {} as Record<string, string>,
            );

            const request: EditFormTransactionRequest = {
                transactionId: transactionId,
                transactionFields,
                formId: formId,
                formValues,
                payloadDocuments,
            };

            await service.update(transactionId, request, {
                updatingCollectionFields,
                isModify,
            });
        } catch (err) {
            await service.delete(transactionId);
            this._loggerService.error(err);
        }
    }

    async runGeneralPopulate({ parsedEvent, transaction, fieldChangeId, externalValues, payloadDocuments }: RunGeneralAutoPopulateDto) {
        const service = await this._dataSourceService.resolveService<GeneralAutoPopulateService>(
            parsedEvent.metadata.tenantId,
            GeneralAutoPopulateService,
        );

        const result = await service.getPopulatedFormFieldValues({
            formVersionId: transaction.formVersionId,
            transaction,
            fieldChangeId,
            externalValues,
            payloadDocuments,
        });

        return result;
    }

    // Open it later when needed
    // private async getLookupFormValues(fields: Record<string, string>, accountId: string) {
    //     // getFieldsByTransactionIds
    //     const registerService = await this._dataSourceService.resolveService<DataRegisterTransactionTenancyService>(
    //         accountId,
    //         DataRegisterTransactionTenancyService,
    //     );
    //     const drTranIds = Object.values(fields);
    //     const tranFields = await registerService.getFieldsByTransactionIds(drTranIds, accountId);
    //     if (!tranFields?.length) {
    //         return null;
    //     }
    //     const result: Record<string, any> = {};
    //     const service = await this._dataSourceService.resolveService<GeneralAutoPopulateService>(
    //         accountId,
    //         GeneralAutoPopulateService,
    //     );
    //     Object.entries(fields).forEach(async item => {
    //         const fieldId = item[0];
    //         const drTranId = item[1];

    //         const records = tranFields?.filter(f => f.dataRegisterTransactionId === drTranId);
    //         if (!records?.length) {
    //             return;
    //         }
    //         records.forEach(r => {
    //             const key = `${fieldId}--${r.fieldId}`;
    //             result[key] = r.fieldValue;
    //         });
    //     });
    //     return result;
    // }

    private async getAutoPopulateValues({
        accountId,
        transactionId,
        formFields,
        transactionFields,
    }: {
        accountId: string;
        transactionId: string;
        formFields: FormFieldTenancyEntity[];
        transactionFields: any;
    }) {
        const populateFieldService = await this._dataSourceService.resolveService<PopulateTransactionFieldService>(
            accountId,
            PopulateTransactionFieldService,
        );

        try {
            const populateFields = await populateFieldService.populateTransactionFields(
                {
                    formVersionFields: formFields,
                    transactionFields: (transactionFields || []).map((rf) => {
                        return {
                            id: rf.id,
                            fieldId: rf.fieldId,
                            fieldValue: rf.fieldOptionIds?.length ? rf.fieldOptionIds.join(',') : rf.fieldValue,
                            fieldOptionIds: rf.fieldOptionIds,
                            fieldType: rf.fieldType,
                            collectionId: rf.collectionId,
                            collectionItemId: rf.collectionItemId,
                            contextType: rf.contextType,
                            transactionId: transactionId,
                            rowKey: rf.rowKey,
                        } as TransactionFieldEntity;
                    }),
                },
                transactionId,
            );

            const newTranFields = (populateFields || [])
                .filter((pf) => !(transactionFields || []).some((tf) => tf.fieldId === pf.fieldId))
                .map((pf) => {
                    if (pf.fieldOptionIds?.length) {
                        pf.fieldValue = pf.fieldOptionIds.join(',');
                    }
                    return pf;
                });
            return newTranFields;
        } catch (err) {
            this._loggerService.error(`Error populate transaction fields on automation for creation transaction: ${err}`);
        }
        return [];
    }
}
