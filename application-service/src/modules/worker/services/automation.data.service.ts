import { Injectable } from '@nestjs/common';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { DataSource, FindOptionsWhere, In, IsNull, Not, Repository } from 'typeorm';

import { CoreConfig, JsonTree, Utils as QbUtils } from '@react-awesome-query-builder/core';
import { LogicEngine } from 'json-logic-engine';
import { validate as isUUID, v4 } from 'uuid';

import { DEFAULT_STAGE_KPI_FIELD_ID, FROM_STAGE_FIELD, TO_STAGE_FIELD } from '../../../constant';

import { ParsedEventDto } from '../dtos/event.dto';

// Submodules
import { KafkaContext } from '@nestjs/microservices';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { LoggerService } from '../../../common/src';
import { EXTERNAL_DATA_SOURCE__SEPARATE_MARK, EXTERNAL_DATA_SOURCE_MAPPING } from '../../../common/src/constant/field';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { CommonEvent } from '../../../common/src/modules/shared/domain/events';
import { ActionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/action-event.enum';
import { AutomationEventEnum } from '../../../common/src/modules/shared/enums/event-driven/automation-event.enum';
import { AutomationTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/automation-topic.enum';
import { CommonTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { TENANT_HEADER } from '../../../database/src/constants/database-option.constant';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { AutomationActionEntity } from '../../../database/src/entities/public/automation-action.public.entity';
import { AutomationRuleEntity } from '../../../database/src/entities/public/automation-rule.public.entity';
import { AutomationVersionEntity } from '../../../database/src/entities/public/automation-version.public.entity';
import { AutomationEntity } from '../../../database/src/entities/public/automation.public.entity';
import { AutomationActionTenancyEntity } from '../../../database/src/entities/tenancy/automation-action.tenancy.entity';
import { AutomationRuleTenancyEntity } from '../../../database/src/entities/tenancy/automation-rule.tenancy.entity';
import { AutomationVersionTenancyEntity } from '../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { AutomationTenancyEntity } from '../../../database/src/entities/tenancy/automation.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { DataRegisterEventEnum, TransactionEventEnum } from '../../../database/src/shared/enums/automation-event.enum';
import {
    AutomationActionFunctionType,
    AutomationActionRunModeEnum,
    AutomationContextType,
    AutomationVersionStatus,
} from '../../../database/src/shared/enums/automation.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { formatCondition } from '../../../utils/formatConditional';
import { PopulateTransactionFieldService } from '../../../modules/form/services/populate-transaction-field.service';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { JsonLogicUtils } from '../../form/services/data/util/run-json-logic.util';

function _getBoolean(value: string | boolean | number): boolean {
    switch (value) {
        case true:
        case 'true':
        case 1:
        case '1':
        case 'on':
        case 'yes':
            return true;
        default:
            return false;
    }
}

@Injectable()
export class AutomationDataService {
    private readonly _engine = new LogicEngine();

    constructor(
        private readonly _loggerService: LoggerService,
        private readonly _moduleRef: ModuleRef,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _dataSourceService: DataSourceService,
    ) {}

    private _runJsonLogic(visibilityConditions: JsonTree, formValues: Record<string, string>) {
        if (!visibilityConditions) {
            return false;
        }
        const formatConditions = formatCondition(visibilityConditions) as any;

        const jsonLogic = QbUtils.jsonLogicFormat(QbUtils.loadTree(formatConditions as JsonTree), CoreConfig);

        const evalResult = this._engine.run(jsonLogic.logic, formValues);

        return evalResult ?? true;
    }

    private async _getDataSource(accountId?: string | null): Promise<DataSource> {
        if (accountId) {
            const contextId = ContextIdFactory.create();
            this._moduleRef.registerRequestByContextId(
                {
                    headers: {
                        [TENANT_HEADER]: accountId,
                    },
                },
                contextId,
            );
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.TENANT_CONNECTION, contextId, { strict: false });
        } else {
            const contextId = ContextIdFactory.create();
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.DATA_SOURCE, contextId, { strict: false });
        }
    }

    private _getRepositories(
        dataSource: DataSource,
        accountId?: string | null,
    ): {
        autoRepo: Repository<AutomationTenancyEntity>;
        autoVerRepo: Repository<AutomationVersionTenancyEntity>;
        autoRuleRepo: Repository<AutomationRuleTenancyEntity>;
        autoActionRepo: Repository<AutomationActionTenancyEntity>;
    } {
        if (accountId) {
            return {
                autoRepo: dataSource.getRepository(AutomationTenancyEntity),
                autoVerRepo: dataSource.getRepository(AutomationVersionTenancyEntity),
                autoRuleRepo: dataSource.getRepository(AutomationRuleTenancyEntity),
                autoActionRepo: dataSource.getRepository(AutomationActionTenancyEntity),
            };
        } else {
            return {
                autoRepo: dataSource.getRepository(AutomationEntity),
                autoVerRepo: dataSource.getRepository(AutomationVersionEntity),
                autoRuleRepo: dataSource.getRepository(AutomationRuleEntity),
                autoActionRepo: dataSource.getRepository(AutomationActionEntity),
            };
        }
    }

    public async handleEvent(event: CommonEvent): Promise<void> {
        // Step 1: Parse event
        const parsedEvent = AutomationDataService.parseAllEvent(event);

        if (!parsedEvent.type || !parsedEvent.contextId || !parsedEvent.contextVersionId) return;

        // Step 2: Get automation
        const dataSource = await this._getDataSource(event.metadata.tenantId);
        const { autoRepo, autoVerRepo, autoActionRepo } = this._getRepositories(dataSource, event.metadata.tenantId);

        let automationVersionId;

        if (
            parsedEvent.type === TransactionEventEnum.FORM_TRANSACTION_SCHEDULED ||
            parsedEvent.type === DataRegisterEventEnum.DATA_REGISTER_SCHEDULED ||
            parsedEvent.type === TransactionEventEnum.FORM_TRANSACTION_MANUAL
        ) {
            automationVersionId = parsedEvent?.payload?.automationVersionId;
        }

        let autoVersions: AutomationVersionTenancyEntity[] = [];

        if (parsedEvent.contextType === AutomationContextType.External) {
            const autos = await autoRepo.find({
                where: {
                    activeVersionId: Not(IsNull()),
                    contextType: AutomationContextType.External,
                },
                select: ['id', 'activeVersionId'],
            });

            if (autos?.length) {
                autoVersions = await autoVerRepo.find({
                    where: {
                        id: In(autos.map((a) => a.activeVersionId)),
                        isEnable: true,
                    },
                    relations: ['rules'],
                    select: ['id', 'automationId', 'contextVersionId', 'rules'],
                });
                autoVersions = (autoVersions || []).filter((item) => item.contextVersionId === parsedEvent.contextVersionId);
            }
        } else {
            const where: FindOptionsWhere<AutomationVersionTenancyEntity> = {
                eventType: parsedEvent.type,
                contextVersionId: parsedEvent.contextVersionId,
                isEnable: true,
                id: automationVersionId,
            };

            if (!parsedEvent?.payload?.isTest) {
                // In real transaction, only published version will be executed
                where.status = AutomationVersionStatus.Published;
            }

            autoVersions = await autoVerRepo.find({
                where: {
                    ...where,
                },
                relations: ['rules'],
            });
        }

        if (!autoVersions || autoVersions.length === 0) {
            return;
        }

        // Step 3: Get rules
        const rules = autoVersions.flatMap((v) => v.rules).filter((r) => r.isEnable);

        if (rules.length === 0) {
            return;
        }

        // Step 4: Execute rules
        const passedRules = [];

        // Build form values
        const formValues: Record<string, any> = {};
        const extraMetadata: Record<string, any> = {};

        if (parsedEvent.contextType === AutomationContextType.FormTransaction) {
            const tranFieldRepo = dataSource.getRepository(TransactionFieldEntity);
            const stageRepo = dataSource.getRepository(StageTenancyEntity);
            const fieldIds: Set<string> = new Set();
            const stageIds: Map<string, string> = new Map();
            for (let i = 0, iMax = rules.length; i < iMax; i++) {
                const rule = rules[i];
                const conditions = rule.configuration?.node?.data?.configuration?.conditions;

                (conditions?.children1 ?? []).forEach((children) => {
                    const fieldId = children?.properties?.field;
                    if (fieldId && isUUID(fieldId) && formValues[fieldId] === undefined) {
                        if (fieldId === FROM_STAGE_FIELD && event?.payload?.previousStageId) {
                            stageIds.set(fieldId, event?.payload?.previousStageId);
                        } else if (fieldId === TO_STAGE_FIELD && event?.payload?.stageId) {
                            stageIds.set(fieldId, event?.payload?.stageId);
                        } else if (fieldId === DEFAULT_STAGE_KPI_FIELD_ID) {
                            formValues[fieldId] = event?.payload?.kpiStatusId;
                        } else {
                            fieldIds.add(fieldId);
                        }
                    }
                });
            }
            if (stageIds.size > 0) {
                const stages = await stageRepo.find({
                    where: {
                        id: In(Array.from(stageIds.values())),
                    },
                });

                stages.forEach((stage) => {
                    stageIds.forEach((value, key) => {
                        if (value === stage.id) {
                            formValues[key] = stage.identityId;
                        }
                    });
                });
            }
            if (fieldIds.size > 0) {
                const fields = await tranFieldRepo.find({
                    where: {
                        fieldId: In(Array.from(fieldIds)),
                        transactionId: parsedEvent.contextTransactionId,
                    },
                });

                fields.forEach((field) => {
                    switch (field.fieldType) {
                        case FormFieldTypeEnum.Lookup:
                        case FormFieldTypeEnum.Select:
                        case FormFieldTypeEnum.MultiSelect:
                            formValues[field.fieldId] = JsonLogicUtils.convertVariableValue(
                                field.fieldType,
                                field.fieldValue,
                                field.fieldOptionIds,
                            );
                            break;
                        case FormFieldTypeEnum.Checkbox:
                            formValues[field.fieldId] = _getBoolean(field.fieldValue);
                            break;
                        default:
                            formValues[field.fieldId] = JsonLogicUtils.convertVariableValue(
                                field.fieldType,
                                field.fieldValue,
                                field.fieldOptionIds,
                            );
                    }
                });
            }
        } else if (parsedEvent.contextType === AutomationContextType.DataRegister) {
            const registerFieldRepo:
                | Repository<DataRegisterTransactionFieldTenancyEntity>
                | Repository<DataRegisterTransactionFieldEntity> = RequestContextService.accountId
                ? dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity)
                : dataSource.getRepository(DataRegisterTransactionFieldEntity);
            const fieldIds: Set<string> = new Set();

            for (let i = 0, iMax = rules.length; i < iMax; i++) {
                const rule = rules[i];
                const conditions = rule.configuration?.node?.data?.configuration?.conditions;

                (conditions?.children1 ?? []).forEach((children) => {
                    const fieldId = children?.properties?.field;
                    if (fieldId && isUUID(fieldId) && formValues[fieldId] === undefined) {
                        fieldIds.add(fieldId);
                    }
                });
            }
            if (fieldIds.size > 0) {
                const fields = await registerFieldRepo.find({
                    where: {
                        fieldId: In(Array.from(fieldIds)),
                        dataRegisterTransactionId: parsedEvent.contextTransactionId,
                    },
                });

                fields.forEach((field) => {
                    switch (field.fieldType) {
                        case FormFieldTypeEnum.Lookup:
                        case FormFieldTypeEnum.Select:
                        case FormFieldTypeEnum.MultiSelect:
                            formValues[field.fieldId] = JsonLogicUtils.convertVariableValue(
                                field.fieldType,
                                field.fieldValue,
                                field.fieldOptionIds,
                            );
                            break;
                        case FormFieldTypeEnum.Checkbox:
                            formValues[field.fieldId] = _getBoolean(field.fieldValue);
                            break;
                        default:
                            formValues[field.fieldId] = JsonLogicUtils.convertVariableValue(
                                field.fieldType,
                                field.fieldValue,
                                field.fieldOptionIds,
                            );
                    }
                });
            }
        } else if (parsedEvent.contextType === AutomationContextType.External) {
            const fields = parsedEvent.payload.fields ?? {};
            Object.keys(fields).forEach((fieldId) => {
                formValues[fieldId] = fields[fieldId];
            });
        }
        //

        for (let i = 0, iMax = rules.length; i < iMax; i++) {
            const _populateTransactionFieldService = await this._dataSourceService.resolveService(
                event?.metadata?.tenantId,
                PopulateTransactionFieldService,
            );

            const tranFieldRepo = dataSource.getRepository(TransactionFieldEntity);
            const formFieldRepo = dataSource.getRepository(FormFieldTenancyEntity);

            const formFields = await formFieldRepo.find({
                where: {
                    formVersionId: parsedEvent.contextVersionId,
                },
            });
            const tranFields = await tranFieldRepo.find({
                where: {
                    transactionId: parsedEvent.contextTransactionId,
                },
            });
            const availableFields: Array<{ fieldId: string; value: string }> = tranFields.map((field) => ({
                fieldId: field.fieldId,
                value: field.fieldValue,
            }));

            const rule = rules[i];
            const conditions = rule.configuration?.node?.data?.configuration?.conditions;
            const dataSources = rule.configuration?.node?.data?.configuration?.dataSources;

            let isValid = true;
            if (dataSources && dataSources.length > 0) {
                // check if data source rule is valid
                for (let j = 0, jMax = dataSources.length; j < jMax; j++) {
                    if (!isValid) {
                        break;
                    }

                    const {
                        dataSource: dataSourceId,
                        dataSourceType,
                        extraConfigurations,
                        conditions: dataSourceConditions,
                    } = dataSources[j];
                    const fieldSetting: GeneralAutoPopulateSettingTenancyEntity = new GeneralAutoPopulateSettingTenancyEntity();
                    fieldSetting.dataSourceId = dataSourceId;
                    fieldSetting.dataSourceType = dataSourceType;
                    fieldSetting.extraConfigurations = extraConfigurations?.map((config) => ({
                        ...config,
                        fixedValue: config.toFixedValue,
                        formFieldType: config.toFixedFieldType,
                    }));

                    const data = await _populateTransactionFieldService.execute({
                        fieldSetting,
                        availableFields,
                        formFields,
                    });

                    if (!data) {
                        isValid = false;
                        break;
                    }

                    // Check if data is valid
                    const dataSourceFormValues = await this._getFormValues(dataSource, AutomationContextType.DataRegister, data.id, [
                        dataSourceConditions,
                    ]);
                    isValid = this._runJsonLogic(dataSourceConditions as JsonTree, dataSourceFormValues);
                }
            }

            if (!isValid) {
                continue;
            }

            let passed = false;

            if (!conditions || conditions?.children1?.length === 0) {
                passed = true;
            } else {
                // Execute rule
                passed = this._runJsonLogic(conditions as JsonTree, formValues);
            }

            // Check if rule passed

            if (
                [TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED, DataRegisterEventEnum.DATA_REGISTER_RECORD_FIELD_UPDATED].includes(
                    parsedEvent.type,
                )
            ) {
                const triggerFields = rule.configuration?.node?.data?.configuration?.triggerFields ?? [];
                console.log('__parsedEvent__', parsedEvent);
                console.log('__triggerFields__', triggerFields);
                const changedFields = (parsedEvent?.payload?.fields ?? []).map((field) => field.fieldId);
                const hasTriggerField = changedFields.some((fieldId) => triggerFields.includes(fieldId));

                if (passed && hasTriggerField) passedRules.push(rule);
                // Check Collection transaction changed
                else if (parsedEvent.type === TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED) {
                    const selectedCollectionIdentityIds: string[] =
                        rule.configuration?.node?.data?.configuration?.selectedCollectionIds ?? [];
                    const collectionsMap: Record<string, { fieldIds: string[] }> =
                        rule.configuration?.node?.data?.configuration?.collections ?? {};
                    console.log('__selectedCollectionIds__', selectedCollectionIdentityIds);
                    console.log('__collectionsMap__', collectionsMap);
                    const triggerCollectionFields = selectedCollectionIdentityIds.flatMap(
                        (collectionIdentityId) => collectionsMap[collectionIdentityId]?.fieldIds ?? [],
                    );
                    const changedCollectionFields: { fieldId: string; collectionIdentityId: string }[] = (
                        parsedEvent?.payload?.fields ?? []
                    ).map((field) => ({
                        fieldId: field.fieldId,
                        collectionIdentityId: field.collectionId,
                    }));
                    const hasTriggerCollectionField = changedCollectionFields.some(
                        (field) =>
                            triggerCollectionFields.includes(field.fieldId) &&
                            selectedCollectionIdentityIds.includes(field.collectionIdentityId),
                    );

                    if (passed && hasTriggerCollectionField) passedRules.push(rule);
                }
            } else {
                if (passed) passedRules.push(rule);
            }
        }

        const allActions = [];

        for (let i = 0, iMax = passedRules.length; i < iMax; i++) {
            const rule = passedRules[i];
            const actions = await autoActionRepo.find({
                where: {
                    ruleId: rule.id,
                },
                order: {
                    order: 'ASC',
                },
            });

            allActions.push(...actions);

            //const sameAutomationActions = manualActions?.filter((action) => action.automationVersionId === rule.automationVersionId);
            const runMode: AutomationActionRunModeEnum = rule.configuration?.node?.data?.configuration
                ?.runMode as AutomationActionRunModeEnum;
            const isSequence = runMode === AutomationActionRunModeEnum.ORDERING;

            for (let j = 0, jMax = actions.length; j < jMax; j++) {
                const action = actions[j];
                // TODO: open it when need to filer action need to be triggered
                // if (sameAutomationActions?.length) {
                //     const mAction = sameAutomationActions?.find((ac) => ManualActionMapping[ac.actionType] === action.functionType);
                //     if (!mAction) {
                //         continue;
                //     }
                // }
                // Execute action
                // Step 6: Produce message - Action event trigger
                const automationId = autoVersions.find((v) => v.id === action.automationVersionId)?.automationId;
                delete action.configuration?.scoringSettings;
                let key = undefined;
                if ((action.functionType?.toString() as ActionEventEnum) === ActionEventEnum.CREATE_RELATED_TRANSACTION) {
                    key = action.id;
                }

                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        actionId: action.id,
                        functionType: action.functionType,
                        configuration: action.configuration,
                        context: {
                            type: parsedEvent.contextType,
                            id: parsedEvent.contextId,
                            versionId: parsedEvent.contextVersionId,
                            transactionId: parsedEvent.contextTransactionId,
                            automationId: automationId,
                            automationVersionId: action.automationVersionId,
                        },
                        browserTabId: parsedEvent.payload?.browserTabId,
                        //external
                        external: {
                            ...(parsedEvent.payload ?? {}),
                        },
                        actionName: action.name,
                    },
                    aggregateId: event.aggregateId,
                    tenantId: RequestContextService.accountId,
                    type: action.functionType.toString() as ActionEventEnum,
                    name: action.functionType.toString() as ActionEventEnum,
                });

                // Using rule id as key to ensure that the message is processed in order
                await this._eventDrivenService.publishMessage(
                    CommonTopicEnum.ACTION_TOPIC,
                    message,
                    key ?? (isSequence ? rule.id : undefined),
                );
            }
        }

        for (let i = 0, iMax = autoVersions.length; i < iMax; i++) {
            const autoVersion = autoVersions[i];

            const versionRules = rules.filter((r) => r.automationVersionId === autoVersion.id).map((r) => r.id);
            const versionPassedRules = passedRules.filter((r) => r.automationVersionId === autoVersion.id).map((r) => r.id);
            const versionActions = allActions.filter((a) => versionRules.includes(a.ruleId)).map((a) => a.id);

            const message = EventDrivenService.createCommonEvent({
                payload: {
                    id: autoVersion.id,
                    automationId: autoVersion.automationId,
                    automationVersionId: autoVersion.id,
                    context: {
                        type: parsedEvent.contextType,
                        id: parsedEvent.contextId,
                        versionId: parsedEvent.contextVersionId,
                        transactionId: parsedEvent.contextTransactionId,
                    },
                    triggerEvent: event,
                    execution: {
                        passedRules: versionPassedRules,
                        rules: versionRules,
                        actions: versionActions,
                    },
                },
                tenantId: RequestContextService.accountId,
                aggregateId: autoVersion.id,
                type: AutomationEventEnum.AUTOMATION_TRIGGERED,
                name: AutomationEventEnum.AUTOMATION_TRIGGERED,
                ...extraMetadata,
            });
            await this._eventDrivenService.publishMessage(AutomationTopicEnum.AUTOMATION_TOPIC, message);
        }
    }

    public async handleScheduledEvent(event: CommonEvent, context: KafkaContext): Promise<void> {
        // TODO: Implement schedule handling
        // Step 1: Get all transactions that need to be executed
        // Step 2: Execute automation rules

        // Step 1: Parse event
        const parsedEvent = AutomationDataService.parseAllEvent(event);

        if (!parsedEvent.type || !parsedEvent.contextType) return;

        const dataSource = await this._getDataSource(event.metadata.tenantId);
        const { autoVerRepo } = this._getRepositories(dataSource, event.metadata.tenantId);

        const autoVersion = await autoVerRepo.findOne({
            where: {
                id: event.aggregateId,
            },
        });

        if (!autoVersion) return;

        if (parsedEvent.contextType === AutomationContextType.FormTransaction) {
            if (!event.metadata.tenantId) {
                return;
            }

            // Form transaction
            const formVersionId = autoVersion.contextVersionId;
            const tranRepo = dataSource.getRepository(TransactionEntity);

            const transactions = await tranRepo.find({
                where: {
                    formVersionId,
                },
            });

            for (let i = 0, iMax = transactions.length; i < iMax; i++) {
                const transaction = transactions[i];
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        formId: transaction.formId,
                        formVersionId: transaction.formVersionId,
                        automationVersionId: autoVersion.id,
                    },
                    tenantId: RequestContextService.accountId,
                    aggregateId: transaction.id,
                    type: parsedEvent.type,
                    name: parsedEvent.type,
                    correlationId: v4(), // Generate new correlationId for each transaction
                });

                await context.getHeartbeat();
                await this._eventDrivenService.publishMessage(CommonTopicEnum.POPULATED_SCHEDULE_TOPIC, message);
            }
        } else if (parsedEvent.contextType === AutomationContextType.DataRegister) {
            // Data register
            const dataRegisterVersionId = autoVersion.contextVersionId;
            const tranRepo: Repository<DataRegisterTransactionEntity> | Repository<DataRegisterTransactionTenancyEntity> = event.metadata
                .tenantId
                ? dataSource.getRepository(DataRegisterTransactionTenancyEntity)
                : dataSource.getRepository(DataRegisterTransactionEntity);

            const transactions = await tranRepo.find({
                where: {
                    dataRegisterVersionId,
                },
            });

            for (let i = 0, iMax = transactions.length; i < iMax; i++) {
                const transaction = transactions[i];
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        dataRegisterId: transaction.dataRegisterId,
                        dataRegisterVersionId: transaction.dataRegisterVersionId,
                        automationVersionId: autoVersion.id,
                    },
                    aggregateId: transaction.id,
                    tenantId: RequestContextService.accountId,
                    type: parsedEvent.type,
                    name: parsedEvent.type,
                    correlationId: v4(), // Generate new correlationId for each transaction
                });

                await context.getHeartbeat();
                await this._eventDrivenService.publishMessage(CommonTopicEnum.POPULATED_SCHEDULE_TOPIC, message);
            }
        }
    }

    private async _getFormValues(
        dataSource: DataSource,
        contextType: AutomationContextType,
        contextTransactionId: string,
        ruleConditions: Array<any>,
        event?: CommonEvent,
    ) {
        const formValues: Record<string, any> = {};

        if (contextType === AutomationContextType.FormTransaction) {
            const tranFieldRepo = dataSource.getRepository(TransactionFieldEntity);
            const stageRepo = dataSource.getRepository(StageTenancyEntity);
            const fieldIds: Set<string> = new Set();
            const stageIds: Map<string, string> = new Map();
            for (let i = 0, iMax = ruleConditions.length; i < iMax; i++) {
                const conditions = ruleConditions[i];

                (conditions?.children1 ?? []).forEach((children) => {
                    const fieldId = children?.properties?.field;
                    if (fieldId && isUUID(fieldId) && formValues[fieldId] === undefined) {
                        if (fieldId === FROM_STAGE_FIELD && event?.payload?.previousStageId) {
                            stageIds.set(fieldId, event?.payload?.previousStageId);
                        } else if (fieldId === TO_STAGE_FIELD && event?.payload?.stageId) {
                            stageIds.set(fieldId, event?.payload?.stageId);
                        } else {
                            fieldIds.add(fieldId);
                        }
                    }
                });
            }
            if (stageIds.size > 0) {
                const stages = await stageRepo.find({
                    where: {
                        id: In(Array.from(stageIds.values())),
                    },
                });

                stages.forEach((stage) => {
                    stageIds.forEach((value, key) => {
                        if (value === stage.id) {
                            formValues[key] = stage.identityId;
                        }
                    });
                });
            }
            if (fieldIds.size > 0) {
                const fields = await tranFieldRepo.find({
                    where: {
                        fieldId: In(Array.from(fieldIds)),
                        transactionId: contextTransactionId,
                    },
                });

                fields.forEach((field) => {
                    switch (field.fieldType) {
                        case FormFieldTypeEnum.Lookup:
                        case FormFieldTypeEnum.Select:
                        case FormFieldTypeEnum.MultiSelect:
                            formValues[field.fieldId] = field.fieldOptionIds;
                            break;
                        case FormFieldTypeEnum.Checkbox:
                            formValues[field.fieldId] = _getBoolean(field.fieldValue);
                            break;
                        default:
                            formValues[field.fieldId] = field.fieldValue;
                    }
                });
            }
        } else if (contextType === AutomationContextType.DataRegister) {
            const registerFieldRepo:
                | Repository<DataRegisterTransactionFieldTenancyEntity>
                | Repository<DataRegisterTransactionFieldEntity> = RequestContextService.accountId
                ? dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity)
                : dataSource.getRepository(DataRegisterTransactionFieldEntity);
            const fieldIds: Set<string> = new Set();

            for (let i = 0, iMax = ruleConditions.length; i < iMax; i++) {
                const conditions = ruleConditions[i];

                (conditions?.children1 ?? []).forEach((children) => {
                    const fieldId = children?.properties?.field;
                    if (fieldId && isUUID(fieldId) && formValues[fieldId] === undefined) {
                        fieldIds.add(fieldId);
                    }
                });
            }
            if (fieldIds.size > 0) {
                const fields = await registerFieldRepo.find({
                    where: {
                        fieldId: In(Array.from(fieldIds)),
                        dataRegisterTransactionId: contextTransactionId,
                    },
                });

                fields.forEach((field) => {
                    switch (field.fieldType) {
                        case FormFieldTypeEnum.Lookup:
                        case FormFieldTypeEnum.Select:
                        case FormFieldTypeEnum.MultiSelect:
                            formValues[field.fieldId] = field.fieldOptionIds;
                            break;
                        case FormFieldTypeEnum.Checkbox:
                            formValues[field.fieldId] = _getBoolean(field.fieldValue);
                            break;
                        default:
                            formValues[field.fieldId] = field.fieldValue;
                    }
                });
            }
        }

        return formValues;
    }

    public static parseAllEvent(event: CommonEvent): ParsedEventDto {
        const parsedEvent: Record<string, any> = {};
        const type = event.metadata.type;

        if (!type) {
            throw new Error('Event type is required');
        }

        if (type.startsWith('FORM_TRANSACTION.')) {
            parsedEvent.type = type as TransactionEventEnum;
            parsedEvent.contextType = AutomationContextType.FormTransaction;
            parsedEvent.contextId = event.payload.formId;
            parsedEvent.contextVersionId = event.payload.formVersionId;
            parsedEvent.payload = event.payload;
        } else if (type.startsWith('DATA_REGISTER.')) {
            parsedEvent.type = type as DataRegisterEventEnum;
            parsedEvent.contextType = AutomationContextType.DataRegister;
            parsedEvent.contextId = event.payload.dataRegisterId;
            parsedEvent.contextVersionId = event.payload.dataRegisterVersionId;
            parsedEvent.payload = event.payload;
        } else if (type.startsWith('EXTERNAL_NEW_DOCUMENT.')) {
            parsedEvent.type = type as DataRegisterEventEnum;
            parsedEvent.contextType = AutomationContextType.External;
            parsedEvent.payload = event.payload;

            const eventDataSource = event.payload.datasource;
            const externalDataSourceKey = Object.keys(EXTERNAL_DATA_SOURCE_MAPPING).find((key) => {
                const [_, dataSource] = key.split(EXTERNAL_DATA_SOURCE__SEPARATE_MARK);

                return dataSource === eventDataSource;
            });

            parsedEvent.contextId = externalDataSourceKey ? EXTERNAL_DATA_SOURCE_MAPPING[externalDataSourceKey] : null;
            parsedEvent.contextVersionId = externalDataSourceKey ? EXTERNAL_DATA_SOURCE_MAPPING[externalDataSourceKey] : null;
        }

        parsedEvent.contextTransactionId = event.aggregateId;

        return {
            type: parsedEvent.type,
            contextType: parsedEvent.contextType as AutomationContextType,
            contextId: parsedEvent.contextId,
            contextVersionId: parsedEvent.contextVersionId,
            contextTransactionId: parsedEvent.contextTransactionId,
            payload: parsedEvent.payload ?? {},
        };
    }
}
