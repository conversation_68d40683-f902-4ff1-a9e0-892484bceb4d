import { Injectable } from '@nestjs/common';
import { CacheService, LoggerService } from '../../../common/src';
import { v4 } from 'uuid';

const QUEUE_NAME = 'RefreshTransactionQueue';

@Injectable()
export class RefreshTransactionQueueService {
    constructor(
        private readonly _cacheService: CacheService,
        private readonly _loggerService: LoggerService,
    ) {}

    public queueName(accountId: string, prefix: string) {
        return `${QUEUE_NAME}:${accountId}:${prefix}`;
    }

    public async addTask(task, accountId: string, prefix: string): Promise<boolean> {
        try {
            const taskWithId = { ...task, id: task.id || v4() };
            await this._cacheService.zAdd(this.queueName(accountId, prefix), Date.now(), JSON.stringify(taskWithId));
            return true;
        } catch (error) {
            this._loggerService.error(error);
            return false;
        }
    }

    public async getNextTask(accountId: string, prefix: string): Promise<Record<string, unknown> | null> {
        try {
            const items = await this._cacheService.zRange(this.queueName(accountId, prefix), 0, 0); // get the earliest task
            if (items.length === 0) return null;

            const task = JSON.parse(items[0]);
            await this._cacheService.zRem(this.queueName(accountId, prefix), 0, 0); // remove it after fetching
            return task;
        } catch (error) {
            this._loggerService.error(error);
            return null;
        }
    }

    public async hasTasks(accountId: string, prefix: string): Promise<boolean> {
        try {
            const count = await this._cacheService.zCard(this.queueName(accountId, prefix));
            return count > 0;
        } catch (error) {
            this._loggerService.error(error);
            return false;
        }
    }
}
