import { Modu<PERSON> } from '@nestjs/common';
import { LogController } from './controllers/log.controller';
import { LogService } from './services/log.service';
import { LogTenancyController } from './controllers/log.tenancy.controller';
import { LogTenancyService } from './services/log.tenancy.service';

const controllers = process.env.MODULE !== 'api' ? [LogController, LogTenancyController] : [];
@Module({
    imports: [],
    controllers: [...controllers],
    providers: [LogController, LogService, LogTenancyService, LogTenancyController],
    exports: [],
})
export class LogModule {}
