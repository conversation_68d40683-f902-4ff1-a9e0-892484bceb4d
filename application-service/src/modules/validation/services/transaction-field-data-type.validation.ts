import * as dayjs from 'dayjs';
import { every, includes, isArray, isNumber, isString, toNumber, uniq } from 'lodash';
import { In, Repository } from 'typeorm';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { UserStatus } from '../../../database/src/shared/enums/user-status.enum';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { TransactionValidationError } from '../enums/transaction-validation-errors.enum';
import { FieldDataTypeError, TransactionValidationErrorResponse } from '../types/transaction-validation-error';

export class TransactionFieldDataTypeValidation {
    public async validateTransactionFieldsDataType({
        fields,
        fieldsConfigurations,
        dataRegisterTransactionRepository,
        formTransactionFieldRepository,
        userRepository,
        transactionId,
        availableRoles,
    }: {
        fieldsConfigurations: FormFieldTenancyEntity[];
        fields: EditFormTransactionFieldRequest[];
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>;
        formTransactionFieldRepository: Repository<TransactionFieldEntity>;
        userRepository: Repository<UserTenancyEntity>;
        transactionId: string;
        availableRoles: string[];
    }): Promise<TransactionValidationErrorResponse<FieldDataTypeError[]>> {
        const errorFieldDataTypes: FieldDataTypeError[] = [];

        const lookupValues: { fieldId: string; value: string[]; type: string; label: string }[] = [];
        const roleLookupValues: { fieldId: string; value: string[]; type: string; label: string }[] = [];
        const userLookupValues: { fieldId: string; value: string[]; type: string; label: string }[] = [];

        for (const field of fields) {
            if (field.fieldValue === null || field.fieldValue === undefined) continue;

            const formField = (fieldsConfigurations || []).find((fc) => fc.fieldId === field.fieldId);
            if (!formField) continue;

            const type = formField?.type;
            const value = field.fieldValue;

            switch (type) {
                case FormFieldTypeEnum.Number:
                case FormFieldTypeEnum.TimePicker:
                case FormFieldTypeEnum.Duration:
                    !isNumber(toNumber(value)) &&
                        errorFieldDataTypes.push({ fieldId: field.fieldId, type: formField.type, label: formField.label });
                    break;

                case FormFieldTypeEnum.Select:
                    // TODO: Need to improve field value of select first
                    const fieldOptions = (formField.configuration?.options as Array<{ label: string; value: string }>) || [];

                    if (!fieldOptions.some((option) => option.value === value)) {
                        errorFieldDataTypes.push({ fieldId: field.fieldId, type: FormFieldTypeEnum.Select, label: formField.label });
                    }
                    break;

                case FormFieldTypeEnum.DatePicker:
                case FormFieldTypeEnum.DatetimePicker:
                    const isValid = dayjs(value, formField.configuration?.format).isValid();
                    !isValid &&
                        errorFieldDataTypes.push({
                            fieldId: field.fieldId,
                            type: formField.type,
                            label: formField.label,
                        });
                    break;

                case FormFieldTypeEnum.Lookup:
                    if (value) {
                        lookupValues.push({
                            fieldId: field.fieldId,
                            value: isArray(value) ? value : value.split(',') || [],
                            label: formField.label,
                            type: FormFieldTypeEnum.Lookup,
                        });
                    }
                    break;

                case FormFieldTypeEnum.RoleLookup:
                    if (value) {
                        roleLookupValues.push({
                            fieldId: field.fieldId,
                            value: isArray(value) ? value : value.split(',') || [],
                            label: formField.label,
                            type: FormFieldTypeEnum.RoleLookup,
                        });
                    }
                    break;

                case FormFieldTypeEnum.UserLookup:
                    if (value) {
                        userLookupValues.push({
                            fieldId: field.fieldId,
                            value: isArray(value) ? value : value.split(',') || [],
                            label: formField.label,
                            type: FormFieldTypeEnum.UserLookup,
                        });
                    }
                    break;

                //TODO
                case FormFieldTypeEnum.Calculation:
                    break;

                default:
                    !isString(value) && errorFieldDataTypes.push({ fieldId: field.fieldId, type: formField.type, label: formField.label });
                    break;
            }
        }

        if (lookupValues.length) {
            const lookupValueIds = lookupValues.flatMap((v) => v.value);
            const existedLookupValues = await dataRegisterTransactionRepository.findBy({
                id: In(uniq(lookupValueIds)),
            });

            const unExistedValues = lookupValues.filter((item) =>
                (item.value || []).some((value) => !existedLookupValues.some((e) => e.id === value)),
            );
            if (unExistedValues.length) {
                for (const item of unExistedValues) {
                    try {
                        let formFieldData: TransactionFieldEntity;

                        if (transactionId) {
                            formFieldData = await formTransactionFieldRepository.findOne({
                                where: {
                                    fieldId: item.fieldId,
                                    transactionId: transactionId,
                                },
                            });
                        }

                        const existedLookupValuesIds = existedLookupValues.map((el) => el.id);
                        const unExistedValueIds = item.value.filter((id) => !existedLookupValuesIds.includes(id));

                        if (!formFieldData && unExistedValueIds.length) {
                            errorFieldDataTypes.push({
                                fieldId: item.fieldId,
                                type: item.type,
                                label: item.label,
                            });
                        } else if (formFieldData && every(unExistedValueIds, (item) => includes(formFieldData.fieldOptionIds, item))) {
                            continue;
                        } else {
                            errorFieldDataTypes.push({
                                fieldId: item.fieldId,
                                type: item.type,
                                label: item.label,
                            });
                        }
                    } catch (error) {
                        console.error(`Error fetching field data for fieldId ${item.fieldId}:`, error);
                    }
                }
            }
        }

        if (roleLookupValues.length && availableRoles.length) {
            roleLookupValues.forEach((item) => {
                item.value.forEach((value) => {
                    if (!availableRoles.includes(value)) {
                        errorFieldDataTypes.push({
                            fieldId: item.fieldId,
                            type: item.type,
                            label: item.label,
                        });
                    }
                });
            });
        }

        if (userLookupValues.length) {
            const existingUsers = await userRepository.findBy({
                status: UserStatus.Active,
            });

            const existingUserIds = existingUsers.map((user) => user.id);

            userLookupValues.forEach((item) => {
                item.value.forEach((value) => {
                    if (!existingUserIds.includes(value)) {
                        errorFieldDataTypes.push({
                            fieldId: item.fieldId,
                            type: item.type,
                            label: item.label,
                        });
                    }
                });
            });
        }

        if (errorFieldDataTypes.length) {
            return {
                message: TransactionValidationError.FieldDataTypeError,
                data: errorFieldDataTypes,
            };
        }

        return null;
    }
}
