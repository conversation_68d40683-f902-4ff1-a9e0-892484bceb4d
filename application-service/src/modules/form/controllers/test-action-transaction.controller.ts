import { Controller, HttpCode, HttpStatus, Post, Version } from '@nestjs/common';
import { ApiOperation, ApiProperty, ApiTags } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { v4 } from 'uuid';
import { EXTERNAL_DATA_SOURCE_TYPE, EXTERNAL_DATA_SOURCES } from '../../../common/src/constant/field';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { DataLakeTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/datalake-topic.enum';
import { TransactionEventEnum } from '../../../database/src/shared/enums/automation-event.enum';
import { BaseController } from '../../../shared/common/base.controller';
import { CommonTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/common-topic.enum';

export class SQuery {
    @IsString()
    @ApiProperty()
    correlationId: string;
}

@Controller({
    path: 'test-action',
})
@ApiTags('Test Action')
export class TestActionController extends BaseController {
    constructor(private readonly _event: EventDrivenService) {
        super();
    }

    //#region POST
    //#region POST
    @ApiOperation({ summary: 'Create TMSA transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('sire-tmsa')
    public async testTMSA(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_TMSA,
                datasourceType: 'OCIMF',
                mdsId: '08376063-fad9-4559-a617-fc180d81690b', // field value of mdsId field
                report: {
                    ctxPropertyId: '414091b5-2a0d-4531-91a8-a0a26cb18486',
                    value: 'b4888feb-7731-448b-a173-62001df117a6',
                },
                originVersionId: 'OCIMF_SIRE_TMSA_08376063-fad9-4559-a617-fc180d81690b_WUEO-3373-7075-5642_5002',
                sourceId: '08376063-fad9-4559-a617-fc180d81690b',
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create TMSA transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('sire-tmsa/modify')
    public async testModifyTMSA(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_TMSA,
                datasourceType: 'OCIMF',
                mdsId: '08376063-fad9-4559-a617-fc180d81690b', // field value of mdsId field
                report: {
                    ctxPropertyId: '414091b5-2a0d-4531-91a8-a0a26cb18486',
                    value: 'b4888feb-7731-448b-a173-62001df117a6',
                },
                originVersionId: 'OCIMF_SIRE_TMSA_08376063-fad9-4559-a617-fc180d81690b_WUEO-3373-7075-5642_5002',
                isModify: true,
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create Ovid Crew transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('ovid-crew')
    public async testOVIDCrew(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_OVID_CREW,
                datasourceType: 'OCIMF',
                mdsId: '452fb889-88b4-4e15-83b0-4a185a897940', // field value of mdsId field
                report: {
                    ctxPropertyId: 'f864ac1f-405f-431e-8fa4-f10f5909a2eb',
                    value: 'c6f643cf-99db-485e-a5ce-5ecbe0c47783',
                },
                fields: {
                    'f864ac1f-405f-431e-8fa4-f10f5909a2eb': 'c6f643cf-99db-485e-a5ce-5ecbe0c47783',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create Sire Crew transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('sire-crew')
    public async testSireCrew(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_CREW,
                datasourceType: 'OCIMF',
                mdsId: '1ed6a86d-7309-4ed4-870f-6dd29abcc043', // SOLA
                report: {
                    ctxPropertyId: '91e4ee2a-bbe7-4a8b-bd42-4e12a4d6fc62',
                    value: 'b0b8e859-13fa-4f2e-a70f-9175d6af6fb2',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create Sire Crew transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('sire-crew/modify')
    public async testSireCrewModify(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_CREW,
                datasourceType: 'OCIMF',
                mdsId: '9d45794a-ea8b-4d48-ad5e-ba749afd68f4', // ASIA VISION
                report: {
                    ctxPropertyId: '91e4ee2a-bbe7-4a8b-bd42-4e12a4d6fc62',
                    value: '7f1decfb-3907-4f2c-9d3c-8f8a76c3a976',
                },
                originVersionId: 'OCIMF_SIRE_CREW_9d45794a-ea8b-4d48-ad5e-ba749afd68f4_0C998052-4462-44D3-9EAD-97A638410848_5100',
                isModify: true,
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create VIQ transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('sire-viq')
    public async testVIQCrew(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_VIQ,
                datasourceType: 'OCIMF',
                mdsId: '1ed6a86d-7309-4ed4-870f-6dd29abcc043', // field value of mdsId field
                report: {
                    ctxPropertyId: '14690d60-07a3-4a5c-ad06-778f151e13e9',
                    value: 'd51541ef-092f-4b81-8ecc-8595e83b3220',
                },
                fields: {
                    '14690d60-07a3-4a5c-ad06-778f151e13e9': 'd51541ef-092f-4b81-8ecc-8595e83b3220',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create VIQ transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('sire-incident')
    public async testSireIncident(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_SIRE_INCIDENT,
                datasourceType: 'OCIMF',
                mdsId: '523506d9-cbde-4a55-9da5-08cf5f39027f', // field value of mdsId field
                report: {
                    ctxPropertyId: '70726ae7-327a-4cf3-a098-3b7e611c69b1',
                    value: '8ee414ac-569f-43fb-a16b-f8c0ab1c2049',
                },
                fields: {
                    '70726ae7-327a-4cf3-a098-3b7e611c69b1': '8ee414ac-569f-43fb-a16b-f8c0ab1c2049',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create Ovid OVMSA transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('ovid-ovmsa')
    public async testOvidOVMSA(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.OCIMF_OVID_OVMSA,
                datasourceType: 'OCIMF',
                mdsId: '11fb5151-2f95-49ef-8044-1222fcc0dc5c', // field value of mdsId field
                report: {
                    ctxPropertyId: '20f33e3f-7b2b-4ff6-b298-7f206ac1a5b6',
                    value: '97fbb5b5-a3f8-4504-b61d-164adb13c454',
                },
                fields: {
                    '20f33e3f-7b2b-4ff6-b298-7f206ac1a5b6': '97fbb5b5-a3f8-4504-b61d-164adb13c454',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create empty transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('lli-vessel')
    public async testLLIVessel(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.LLI_VESSEL,
                datasourceType: EXTERNAL_DATA_SOURCE_TYPE.LLI,
                mdsId: 'e92968cd-10f2-4721-ae12-aff396091696',
                // report: {
                //     ctxPropertyId: '2a97c135-9f67-4783-9f1f-780d7117bf15',
                //     value: 'a2077826-f979-4d96-9f86-28b981306a41',
                // },
                fields: {
                    // 'f6d822b4-1bd2-411a-853d-21404e421431': 10,
                    // 'fef97bff-053e-4f45-b120-5ad40a269cd7': '2017-04-03T01:47:55.960Z',
                    // '39689ef1-9257-4a00-a2f6-78639fe42734': 'Tsakos Shipping 1',
                    // 'bb25a7d7-f501-43a9-b94c-09e70c144b18': '2024-12-23T06:00:47.453Z',
                    // '2a97c135-9f67-4783-9f1f-780d7117bf15': 'a2077826-f979-4d96-9f86-28b981306a41',
                    // '2180396d-1845-4334-be7a-eab63e0306a3': 'BB Supporter Test',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: '80faab76-8fee-4295-a754-d14a2284eb76',
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Create empty transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('lli-casualty')
    public async testLLICasualty(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.LLI_CASUALTY,
                datasourceType: EXTERNAL_DATA_SOURCE_TYPE.LLI,
                mdsId: 'e92968cd-10f2-4721-ae12-aff396091696',
                // report: {
                //     ctxPropertyId: '2a97c135-9f67-4783-9f1f-780d7117bf15',
                //     value: 'a2077826-f979-4d96-9f86-28b981306a41',
                // },
                fields: {
                    // 'f6d822b4-1bd2-411a-853d-21404e421431': 10,
                    // 'fef97bff-053e-4f45-b120-5ad40a269cd7': '2017-04-03T01:47:55.960Z',
                    // '39689ef1-9257-4a00-a2f6-78639fe42734': 'Tsakos Shipping 1',
                    // 'bb25a7d7-f501-43a9-b94c-09e70c144b18': '2024-12-23T06:00:47.453Z',
                    // '2a97c135-9f67-4783-9f1f-780d7117bf15': 'a2077826-f979-4d96-9f86-28b981306a41',
                    // '2180396d-1845-4334-be7a-eab63e0306a3': 'BB Supporter Test',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: '80faab76-8fee-4295-a754-d14a2284eb76',
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    //#region POST
    //#region POST
    @ApiOperation({ summary: 'Create Paris mou transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('paris-mou')
    public async testParisMou(): Promise<boolean> {
        this._event.publishMessage(DataLakeTopicEnum.EXTERNAL_DOCUMENT_CHANGE, {
            payload: {
                datasource: EXTERNAL_DATA_SOURCES.PARIS_MOU_PSC,
                datasourceType: 'PARIS_MOU',
                mdsId: '1ed6a86d-7309-4ed4-870f-6dd29abcc043', // field value of mdsId field
                // report: {
                //     ctxPropertyId: '414091b5-2a0d-4531-91a8-a0a26cb18486',
                //     value: 'e42364ee-b522-4611-84c5-762c061e6b7e',
                // },
                fields: {
                    // 'f6d822b4-1bd2-411a-853d-21404e421431': 10,
                    // 'fef97bff-053e-4f45-b120-5ad40a269cd7': '2017-04-03T01:47:55.960Z',
                    // '39689ef1-9257-4a00-a2f6-78639fe42734': 'Tsakos Shipping 1',
                    // 'bb25a7d7-f501-43a9-b94c-09e70c144b18': '2024-12-23T06:00:47.453Z',
                    // '414091b5-2a0d-4531-91a8-a0a26cb18486': 'e42364ee-b522-4611-84c5-762c061e6b7e',
                    // '2180396d-1845-4334-be7a-eab63e0306a3': 'BB Supporter Test',
                    '00d19b35-c0ff-40e7-a956-8d5e5b0fa22c': '123213',
                },
            },
            id: v4(),
            aggregateId: v4(),
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                type: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                name: TransactionEventEnum.EXTERNAL_NEW_DOCUMENT_CREATED,
                version: 1,
                source: 'External',
                timestamp: Date.now(),
            },
        });
        return true;
    }

    @ApiOperation({ summary: 'Data lake field populate' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('test-datalake-populate')
    public async testDatalakePopulate(): Promise<boolean> {
        this._event.publishMessage(CommonTopicEnum.DATA_POPULATE_TOPIC, {
            id: v4(),
            payload: {
                contextId: '8107a827-66d9-4c44-b393-dfdbf2dfb9c9',
                contextSourceType: 'OFAC',
                contextSource: 'OFAC_COMPANY_SANCTION',
                transaction: {
                    '6ad71fa3-f951-4932-b0ab-f82c7748b399': '2025-04-10T14:33:10.923Z',
                },
            },
            aggregateId: v4(),
            tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
            type: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            name: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
                timestamp: 1744254297719,
                version: 1,
                source: 'External',
                type: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
                name: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            },
        } as any);
        return true;
    }

    @ApiOperation({ summary: 'Data populate sire vpq' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('data-populate/sire-vpq')
    public async testDataPopulate(): Promise<boolean> {
        this._event.publishMessage(CommonTopicEnum.DATA_POPULATE_TOPIC, {
            id: v4(),
            payload: {
                contextId: '1ed6a86d-7309-4ed4-870f-6dd29abcc043',
                // contextType: 'LLI',
                // contextSource: 'LLI_VESSEL',
                // contextType: 'OCIMF',
                // contextSource: 'OCIMF_SIRE_VPQ',
                transaction: {
                    '7bb98cbb-d75b-46f0-bb02-f6ae5f0080e0': '',
                    '45ecd5cb-b5b9-43b9-83c6-762eb0f99c2b': '',
                    '2dc83ff3-9a79-4863-928b-62c3876e686f': '',
                    '513915c7-ed7d-4a91-930b-e5dd4b15f8f8': '',
                },
            },
            aggregateId: v4(),
            tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
            type: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            name: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
                timestamp: new Date().getTime(),
                version: 1,
                source: 'External',
                type: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
                name: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            },
        } as any);
        return true;
    }

    @ApiOperation({ summary: 'Data populate sire vpq' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('populate/lli-vessel')
    public async populateLLIVessel(): Promise<boolean> {
        this._event.publishMessage(CommonTopicEnum.DATA_POPULATE_TOPIC, {
            id: v4(),
            payload: {
                contextId: 'ff6b6dde-bf6a-40ab-b6b9-37c5532042ff',
                contextType: 'Q88',
                contextSource: 'Q88_VHPQ',
                transaction: {
                    '93d937f4-321f-417e-8891-b17d1e75a7fc': 'Winning Faith',
                    'e32c1167-956d-42d6-ba40-e25fdc9e0497': '9314076',
                    '2c91c23d-2e0d-4854-861d-4be31ac25b9e': '566963000',
                    'ebf07cae-680f-40d7-b702-c93986cb3e06': 55695,
                    'dd0dbabf-6b2a-44e5-b347-5a6ae61d29ad': 45,
                    '6b4c9384-36ae-4c5d-881d-d96deaf8bcf8': 6530,
                    'f401971a-4de3-4f2b-890c-43a7f4e88f1b': 24.7,
                    '81191dc2-d9e1-42d1-a399-469d401292e4': 288.93,
                    'caf95d94-bfc3-4871-9f46-dcdde4df3950': 55695,
                    '9530c2b6-3eb3-4908-ae82-3a3a3c0727b9': 18.17,
                    '6f9278cc-d352-4750-a83c-177bdca36bbf': 45,
                    '89ef4cc9-7da4-41ed-a3be-0231c1a721f7': null,
                    '768e36b1-a954-4c09-9392-b5a932b70341': null,
                    'bc06ef83-e70a-4b5a-b8e6-f456e399f3b4': null,
                    '7d9d68f4-0a22-4f73-9489-807f6ef6cc3a': null,
                    '4772261b-ae27-4917-b860-bfac98c4e009': null,
                    'a79c2e16-cdb8-4372-bad9-405ce775ea4c': 'FO 5,985.00  FW 529.00',
                    'e5b1c260-58e2-44c3-88d0-bf6cf052a4a3': null,
                    'ebaff252-d1f7-43ae-ae05-f9ac11638f21': null,
                    '44c9737d-c94f-4fbf-9be7-d264268f838f': null,
                    'fdd008dd-f499-4817-b989-ed483888cb1b': null,
                    '221ed170-6ae9-4721-a959-5b29922a93f5': null,
                    '41ae80c2-2222-4b01-b41b-3b319d29721d': null,
                    'a8d3ec48-b56e-41c2-9c43-129c4fc56c84': null,
                    '04fce2cc-08f6-48e9-8600-828edf301b61': null,
                    '544bca10-45a2-42e1-801f-f70f5cbb9cba': null,
                    'a57781e5-c5aa-4cae-8a1f-b87165d0abf3': '35fe0d3e-4ea9-4518-8402-055edf4a04bb',
                    '6685d220-7ef7-476b-a1f6-d49950c5e107': 'dd8897fa-9b33-4b8d-8e7a-7fc901121b2f',
                    '09d1bb2e-1544-4103-bafe-871bf4670944': 'bd8574e6-8119-498c-8c58-bf95a5d5f0e1',
                    '2dc83ff3-9a79-4863-928b-62c3876e686f': '8cde03ea-f594-4d9d-ba05-364ac7247e0f',
                    '3690f2b4-4635-4c46-8882-b0dcdabc8b15': [
                        {
                            id: '6968071e-95d9-48a0-881e-20dd7ee3be5c',
                            value: null,
                        },
                    ],
                    '7762329f-1f6b-4c2f-be65-d27a396252d4': [
                        {
                            id: '6968071e-95d9-48a0-881e-20dd7ee3be5c',
                            value: true,
                        },
                    ],
                    '24d1dc40-3655-4983-948f-25ff6c1f51bc': [
                        {
                            id: '6968071e-95d9-48a0-881e-20dd7ee3be5c',
                            value: null,
                        },
                    ],
                    '8afa0618-2633-4f1a-8216-e2cba09240c6': [
                        {
                            id: '6968071e-95d9-48a0-881e-20dd7ee3be5c',
                            value: null,
                        },
                    ],
                    'ce42e2e7-0892-4731-b7df-5303782bdf5c': [
                        {
                            id: '5e770535-5cd6-4783-a59b-69a13ed554ca',
                            value: '1',
                        },
                        {
                            id: 'dc12088f-802d-4b88-a5d5-2637bca8c493',
                            value: '8',
                        },
                    ],
                    '413d8c80-2c3b-4ba5-b0cb-a37c67fece89': [
                        {
                            id: '5e770535-5cd6-4783-a59b-69a13ed554ca',
                            value: '17.6',
                        },
                        {
                            id: 'dc12088f-802d-4b88-a5d5-2637bca8c493',
                            value: '20.8',
                        },
                    ],
                    '88ca600a-73d5-4711-8eda-ce87f1bed0f7': [
                        {
                            id: '5e770535-5cd6-4783-a59b-69a13ed554ca',
                            value: '15.66',
                        },
                        {
                            id: 'dc12088f-802d-4b88-a5d5-2637bca8c493',
                            value: '15.66',
                        },
                    ],
                    '065c1eba-d21d-49e3-96b9-d8ef245fa44a': [
                        {
                            id: '5e770535-5cd6-4783-a59b-69a13ed554ca',
                            value: '65763',
                        },
                        {
                            id: 'dc12088f-802d-4b88-a5d5-2637bca8c493',
                            value: '53061',
                        },
                    ],
                    '4fa600c8-5469-4c36-87ec-e896b74ddbc0': 137773,
                    'f1a54350-79f7-4650-b692-d3687ec193d4': [
                        {
                            id: 'eb74c15a-3a0e-429e-bb50-dbb7da461eb5',
                            value: null,
                        },
                    ],
                    '088dd026-25d9-4b95-ac3d-12b2ca42e84a': [
                        {
                            id: 'eb74c15a-3a0e-429e-bb50-dbb7da461eb5',
                            value: '23516',
                        },
                    ],
                    '7832e180-19e3-43f9-8b95-cfcdc5f6b1f6': [
                        {
                            id: 'eb74c15a-3a0e-429e-bb50-dbb7da461eb5',
                            value: null,
                        },
                    ],
                    'a47f7101-575c-440a-bb00-1fec03b8c184': [
                        {
                            id: 'eb74c15a-3a0e-429e-bb50-dbb7da461eb5',
                            value: '9',
                        },
                    ],
                    'd7b76139-8b95-4855-ad30-1f36765575cd': [
                        {
                            id: 'eb74c15a-3a0e-429e-bb50-dbb7da461eb5',
                            value: null,
                        },
                    ],
                    '3f3e64dc-6d7b-48aa-a089-b052aa90d829': [
                        {
                            id: '6968071e-95d9-48a0-881e-20dd7ee3be5c',
                            value: true,
                        },
                    ],
                    '5c79c0ff-8296-4de9-a29b-4d15a8b23830':
                        'LLI_VESSEL_ff6b6dde-bf6a-40ab-b6b9-37c5532042ff_bd09d8ee-e9d5-4d02-a1ff-be30660ef65b',
                },
            },
            aggregateId: '912fbb92-7451-4f82-9a9a-45bab478c365',
            tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
            type: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            name: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'caaea928-5e06-41df-b48a-3ecf7471b9ef',
                timestamp: 1747361761879,
                version: 1,
                source: 'External',
                type: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
                name: 'DATA_POPULATE.EXTERNAL_DATA_FIELD_CHANGED',
            },
        } as any);
        return true;
    }

    @ApiOperation({ summary: 'Data populate Q88 HVPQ' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('populate/q88-hvpq')
    public async testQ88Handler(): Promise<boolean> {
        this._event.publishMessage(CommonTopicEnum.MDS_DATA_CALLBACK_TOPIC, {
            id: v4(),
            payload: {
                context: {
                    type: 'data_register',
                    transactionId: '1ee81cdf-f95c-4c95-9b61-706c79549157',
                    mdsId: 'e609973f-d5f1-445c-9571-43a6df3a7285',
                    transaction: {
                        '210e0478-c9fc-44a8-b926-a0b000bf9f37': '123123',
                    },
                },
                datasource: 'Q88_VPQ',
                datasourceType: 'Q88',
            },
            aggregateId: '912fbb92-7451-4f82-9a9a-45bab478c365',
            tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
            type: 'ACTION.Q88_HVPQ_DATA_POPULATE',
            name: 'ACTION.Q88_HVPQ_DATA_POPULATE',
            metadata: {
                correlationId: v4(),
                causationId: v4(),
                tenantId: 'd6280172-114e-4190-a1a6-eed179d06f68',
                timestamp: 1747361761879,
                version: 1,
                source: 'External',
                type: 'ACTION.Q88_HVPQ_DATA_POPULATE',
                name: 'ACTION.Q88_HVPQ_DATA_POPULATE',
            },
        } as any);
        return true;
    }
}
