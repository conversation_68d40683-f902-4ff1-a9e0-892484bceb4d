import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { FormTransactionFieldDto } from '../dtos';
import { EditFormTransactionFieldRequest } from '../dtos/requests/create-form-transaction.request';
import { FilterCollectionTransactionRequestDto } from '../dtos/requests/FilterCollectionTransaction.request.dto';
import {
    CreateTransactionFieldRequest,
    DeleteCollectionRowRequest,
    OverrideTransactionFieldRequest,
    UnOverrideValidationFieldRequest,
} from '../dtos/requests/transaction-field.request';
import { TransactionMetadata } from '../guards/api-metadata.decorator';
import { OverrideTransactionFieldValidationGuard } from '../guards/override-transaction-field-validation.guard';
import { TransactionFieldGuard } from '../guards/transaction-field.guard';
import { TransactionGuard } from '../guards/transaction.guard';
import { FormTransactionFieldTenancyService } from '../services/form-transaction-field.tenancy.service';
import { DeleteCollectionRowGuard } from '../guards/delete-collection-row.guard';
import { CreateCollectionRowGuard } from '../guards/create-collection-row.guard';

@Controller({
    path: 'test-form-transaction-fields',
})
@ApiTags('Form Transaction Fields')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class FormTransactionFieldTestController extends BaseController {
    constructor(private readonly service: FormTransactionFieldTenancyService) {
        super();
    }

    @ApiOperation({
        summary: 'Create transaction fields',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post()
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(CreateCollectionRowGuard)
    public async createTransactionFields(@Body() request: CreateTransactionFieldRequest): Promise<ResponseDto<FormTransactionFieldDto[]>> {
        const result = await this.service.createTransactionFields(request);
        return this.getResponse<FormTransactionFieldDto[]>(!!result, result, []);
    }

    @ApiOperation({ summary: 'Get form collection transaction field list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post(':transactionId/:collectionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Param', key: 'transactionId' })
    @UseGuards(TransactionGuard)
    public async getList(
        @Body() body: FilterCollectionTransactionRequestDto,
        @Param('transactionId') transactionId: string,
        @Param('collectionId') collectionId: string,
    ): Promise<ResponseDto<FormTransactionFieldDto[]>> {
        const result = await this.service.getListCollectionTransField(body, transactionId, collectionId, true);
        return this.getResponse<FormTransactionFieldDto[]>(!!result, result, []);
    }

    @ApiOperation({
        summary: 'Get transaction field detail',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get(':transactionFieldId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Param', key: 'transactionFieldId' })
    @UseGuards(TransactionFieldGuard)
    public async getTransactionFieldDetail(
        @Param('transactionFieldId') transactionFieldId: string,
    ): Promise<ResponseDto<FormTransactionFieldDto>> {
        const result = await this.service.getTransactionFieldDetail(transactionFieldId);
        return this.getResponse<FormTransactionFieldDto>(!!result, result, []);
    }

    @ApiOperation({
        summary: 'Update transaction field',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Put()
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Body', key: 'transactionId' })
    @UseGuards(TransactionGuard)
    public async updateTransactionFields(@Body() request: EditFormTransactionFieldRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.updateTransactionField(request);
        return this.getResponse<boolean>(!!result, result, []);
    }

    @ApiOperation({
        summary: 'Delete transaction fields',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Delete('collection-row')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(DeleteCollectionRowGuard)
    public async deleteTransactionFields(@Body() request: DeleteCollectionRowRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.deleteCollectionRow(request);
        return this.getResponse<boolean>(!!result, result, []);
    }

    @ApiOperation({
        summary: 'Override transaction field',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('override-validation-field')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Body', key: 'transactionId' })
    @UseGuards(OverrideTransactionFieldValidationGuard, TransactionGuard)
    public async overrideTransactionField(@Body() request: OverrideTransactionFieldRequest): Promise<ResponseDto<FormTransactionFieldDto>> {
        const result = await this.service.overrideTransactionField({ ...request, isTest: true });
        return this.getResponse<FormTransactionFieldDto>(!!result, result, []);
    }

    @ApiOperation({
        summary: 'Un override validation value for transaction field',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Put('un-override-validation-field')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Body', key: 'transactionId' })
    @UseGuards(TransactionFieldGuard)
    public async deleteTransactionFieldOverrideById(
        @Body() body: UnOverrideValidationFieldRequest,
    ): Promise<ResponseDto<FormTransactionFieldDto>> {
        const result = await this.service.unOverrideValidationValue({ ...body, isTest: true });
        return this.getResponse<FormTransactionFieldDto>(!!result, result, []);
    }
}
