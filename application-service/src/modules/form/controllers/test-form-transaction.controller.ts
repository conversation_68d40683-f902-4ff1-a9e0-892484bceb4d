import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    Post,
    Put,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    Version,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Api<PERSON>earerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Dayjs } from 'dayjs';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { Duration } from '../../../common/src/modules/shared/types/duration';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { FormTransactionListInterceptor } from '../../../interceptors/form-transaction-list.interceptor';
import { FormTransactionDetailInterceptor } from '../../../interceptors/form-transactions-detail.interceptor';
import { TransactionChangeLogInterceptor } from '../../../interceptors/transaction-change-log.interceptor';
import { BaseController } from '../../../shared/common/base.controller';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { TransactionActionLogDto } from '../../../shared/common/dto/transaction-action-log.dto';
import { FormTransactionDto, FormTransactionFieldDto } from '../dtos';
import { TransactionRecordChangeLogDto } from '../dtos/form-transaction-change-log.dto';
import { ExecuteDecisionRequestDto } from '../dtos/requests';
import { CalculateFieldRequest } from '../dtos/requests/calculate-field.request';
import { ChangeStageRequestDto } from '../dtos/requests/change-stage.request';
import { CreateEmptyTransactionRequest, EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';
import { FilterFormTransactionRequestDto } from '../dtos/requests/filter-form-transaction.request';
import { UploadFileForRelatedFormRequest } from '../dtos/requests/transaction-field.request';
import { GetTransactionInformationResponse } from '../dtos/responses';
import { TransactionMetadata } from '../guards/api-metadata.decorator';
import { CreateTransactionGuard } from '../guards/create-transaction.guard';
import { DenyAnonymous } from '../guards/deny-anonymous.guard';
import { EditDecisionFormTransactionGuard } from '../guards/edit-decision-transaction.guard';
import { EditFieldsTransactionGuard } from '../guards/edit-field-transaction.guard';
import { TransactionStageTransitionGuard } from '../guards/transaction-stage-transition.guard';
import { TransactionGuard } from '../guards/transaction.guard';
import { PreventUpdateFieldInterceptor } from '../interceptors/prevent-update-field.interceptor';
import { ChangeTransactionStageService } from '../services/change-transaction-stage.service';
import { ConditionalRequired, ConditionalRequireService } from '../services/conditional-required.service';
import { FormTransactionChangeLogTenancyService } from '../services/form-transaction-change-log.tenancy.service';
import { FormTransactionTenancyService } from '../services/form-transaction.tenancy.service';
import { MigrateTransactionVersionService } from '../services/migrate-transaction-version.service';

@ApiTags('Form Transaction')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
@Controller({
    path: 'test-form-transactions',
})
export class FormTransactionTestController extends BaseController {
    constructor(
        private readonly service: FormTransactionTenancyService,
        private readonly migrateTransactionVersionService: MigrateTransactionVersionService,
        private readonly _changeLogService: FormTransactionChangeLogTenancyService,
        private readonly _conditionalRequiredService: ConditionalRequireService,
        private readonly _changeStageService: ChangeTransactionStageService,
    ) {
        super();
    }

    //#region GET
    @ApiOperation({ summary: 'Get form transaction change log list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/change-logs/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(TransactionChangeLogInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async getListChangeLog(
        @Param('id') id: string,
        @Query() query: MultipleFilterRequestDto,
    ): Promise<PaginationResponseDto<TransactionRecordChangeLogDto>> {
        const data = await this._changeLogService.getList(id, query);
        return data;
    }

    @ApiOperation({ summary: 'Filter form transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('filter')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionListInterceptor)
    public async filterTransactions(@Body() body: FilterFormTransactionRequestDto): Promise<PaginationResponseDto<FormTransactionDto>> {
        const data = await this.service.getList(body);
        return data;
    }

    @ApiOperation({ summary: 'Get transactions information' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('information/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async getTransactionInformation(@Param('formId') formId: string): Promise<ResponseDto<GetTransactionInformationResponse[]>> {
        const data = await this.migrateTransactionVersionService.getTransactionInformation(formId);
        return this.getResponse<GetTransactionInformationResponse[]>(!!data, data, []);
    }

    @ApiOperation({ summary: 'Get form transaction detail' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async get(
        @Param('id') id: string,
        @Query('includeRelatedTransactionIds') includeRelatedTransactionIds: boolean,
    ): Promise<ResponseDto<FormTransactionDto>> {
        const data = await this.service.get(id, {
            includeRelatedTransactionIds,
            includeOriginTransactionIds: true,
            includeFieldContextTypes: [TransactionFieldContextTypeEnum.FORM],
            isTest: true,
        });
        return this.getResponse<FormTransactionDto>(!!data, data, []);
    }

    // @ApiOperation({ summary: 'Get form transaction relation tree' })
    // @Version('1')
    // @HttpCode(HttpStatus.OK)
    // @Post('relation-transaction-tree/:id')
    // @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    // public async getRelationTransactionTree(@Param('id') id: string): Promise<ResponseDto<GetRelationTransactionResponse[]>> {
    //     const accountId = this.claimService.accountId;
    //     if (!accountId) {
    //         throw new ForbiddenException();
    //     }
    //     const data = await this.service.getRelationTransactionTree(accountId, id);
    //     return this.getResponse<GetRelationTransactionResponse[]>(!!data, data, []);
    // }

    @ApiOperation({ summary: 'Get form transaction fields value' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('fields-value/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async getFieldsValue(
        @Param('id') id: string,
        @Query('fieldIds') fieldIds: string,
    ): Promise<ResponseDto<FormTransactionFieldDto[]>> {
        const ids = fieldIds ? fieldIds.split(',') : [];
        const data = await this.service.getFields(id, ids, true);
        return this.getResponse<FormTransactionFieldDto[]>(!!data, data.transactionFields, []);
    }

    @ApiOperation({ summary: 'Get form transaction fields value' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('fields-value/collection/:id/:collectionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @TransactionMetadata({ source: '@Param', key: 'collectionId' })
    @UseGuards(TransactionGuard)
    public async getFieldsCollection(
        @Param('id') id: string,
        @Param('collectionId') collectionId: string,
    ): Promise<ResponseDto<FormTransactionFieldDto[]>> {
        const data = await this.service.getTransactionCollectionFields(id, collectionId);
        return this.getResponse<FormTransactionFieldDto[]>(!!data, data.transactionFields, []);
    }

    //#region GET
    @ApiOperation({ summary: 'Get form transaction action log list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/action-logs/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(TransactionChangeLogInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async getListActionLog(
        @Param('id') id: string,
        @Query() query: MultipleFilterRequestDto,
    ): Promise<PaginationResponseDto<TransactionActionLogDto>> {
        const data = await this._changeLogService.getActionLogs(id, query);
        return data;
    }

    //#endregion GET

    //#region POST
    @ApiOperation({ summary: 'Execute stage decision' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('decisions')
    @UseGuards(EditDecisionFormTransactionGuard)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async executeStageDecision(@Body() request: ExecuteDecisionRequestDto): Promise<ResponseDto<boolean | ConditionalRequired>> {
        const data = await this.service.executeDecision(request);
        return this.getResponse(!!data, data, []);
    }

    @ApiOperation({ summary: 'Change Stage' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('stages')
    @UseGuards(TransactionStageTransitionGuard)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async changeStage(@Body() request: ChangeStageRequestDto): Promise<ResponseDto<ConditionalRequired | boolean>> {
        const required = await this._conditionalRequiredService.verify({
            transactionId: request.transactionId,
            targetStageId: request.targetStageId,
            isTest: true,
        });

        if (!required.canChangeStage) {
            return this.getResponse(false, required, []);
        }

        const data = await this._changeStageService.handleTransitionAction({
            targetStageId: request.targetStageId,
            transactionId: request.transactionId,
            isTest: true,
        });

        return this.getResponse(!!data, !!data, []);
    }

    @ApiOperation({ summary: 'Create empty transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('empty')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(CreateTransactionGuard)
    public async createEmptyTransaction(
        @Body() request: CreateEmptyTransactionRequest,
    ): Promise<ResponseDto<Pick<TransactionEntity, 'id' | 'formVersionId'> | string>> {
        const result = await this.service.create({ ...request, isTest: true });
        return this.getResponse<Pick<TransactionEntity, 'id' | 'formVersionId'> | string>(
            !!result,
            result as Pick<TransactionEntity, 'id' | 'formVersionId'> | string,
            [],
        );
    }

    @ApiOperation({ summary: 'Calculate calculation field from transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('calculate')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async calculateField(
        @Body() request: CalculateFieldRequest,
    ): Promise<ResponseDto<Record<string, string | number | Date | Duration | Dayjs | null>>> {
        const data = await this.service.calculateFields(request);
        return this.getResponse(true, data, null);
    }

    @ApiOperation({
        summary: 'Upload file for related form',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('upload-file-related-form')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FileInterceptor('file'))
    public async uploadFileRelatedForm(@Body() request: UploadFileForRelatedFormRequest, @UploadedFile() file: Express.Multer.File) {
        const result = await this.service.uploadFileRelatedForm(request, file);
        return this.getResponse(true, result);
    }

    //#endregion POST

    //#region PUT
    @ApiOperation({ summary: 'Update form transaction' })
    @Version('1')
    @HttpCode(HttpStatus.ACCEPTED)
    @Put(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(EditFieldsTransactionGuard, TransactionGuard)
    @UseInterceptors(PreventUpdateFieldInterceptor)
    public async update(@Param('id') id: string, @Body() request: EditFormTransactionRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.update(
            id,
            { ...request, isTest: true },
            {
                shouldRunPopulateFormFields: true,
            },
        );
        return this.getResponse<boolean>(result);
    }

    //#endregion PUT

    //#region Delete
    @ApiOperation({ summary: 'Delete form transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Delete(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(DenyAnonymous)
    public async delete(@Param('id') id: string): Promise<ResponseDto<boolean>> {
        const result = await this.service.delete(id);
        return this.getResponse<boolean>(!!result, result, []);
    }
    //#endregion Delete
}
