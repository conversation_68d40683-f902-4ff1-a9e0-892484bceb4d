import {
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    HttpCode,
    HttpStatus,
    Inject,
    Param,
    Post,
    Put,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    Version,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Dayjs } from 'dayjs';
import { ClaimService, USER_CLAIMS } from '../../../common/src';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { Duration } from '../../../common/src/modules/shared/types/duration';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { FormTransactionListInterceptor } from '../../../interceptors/form-transaction-list.interceptor';
import { FormTransactionDetailInterceptor } from '../../../interceptors/form-transactions-detail.interceptor';
import { TransactionChangeLogInterceptor } from '../../../interceptors/transaction-change-log.interceptor';
import { BaseController } from '../../../shared/common/base.controller';
import { GetPopulatedFieldValueRequest } from '../../../shared/common/dto/get-populated-field-values.request';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { TransactionActionLogDto } from '../../../shared/common/dto/transaction-action-log.dto';
import { GeneralAutoPopulateService } from '../../../shared/services';
import { FormTransactionDto, FormTransactionFieldDto } from '../dtos';
import { TransactionRecordChangeLogDto } from '../dtos/form-transaction-change-log.dto';
import { ExecuteDecisionRequestDto, MigrateTransactionVersionRequest } from '../dtos/requests';
import { CalculateFieldRequest } from '../dtos/requests/calculate-field.request';
import { ChangeStageRequestDto } from '../dtos/requests/change-stage.request';
import { CheckCriteriaValidationRequest } from '../dtos/requests/check-criteria-validation.reqest';
import { CreateEmptyTransactionRequest, EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';
import { FilterFormTransactionRequestDto } from '../dtos/requests/filter-form-transaction.request';
import { GetListFormTransactionRequestDto } from '../dtos/requests/get-list-form-transaction.request';
import { PopulateCollectionRequest } from '../dtos/requests/populate-collection.request';
import { UploadFileForRelatedFormRequest } from '../dtos/requests/transaction-field.request';
import { GetTransactionInformationResponse } from '../dtos/responses';
import { GetRelationTransactionResponse } from '../dtos/responses/get-relation-transaction.response';
import { TransactionMetadata } from '../guards/api-metadata.decorator';
import { DeleteTransactionGuard } from '../guards/delete-transaction.guard';
import { DenyAnonymous } from '../guards/deny-anonymous.guard';
import { EditFieldsTransactionGuard } from '../guards/edit-field-transaction.guard';
import { TransactionStageDecisionGuard } from '../guards/transaction-stage-decision.guard';
import { TransactionStageTransitionGuard } from '../guards/transaction-stage-transition.guard';
import { TransactionGuard } from '../guards/transaction.guard';
import { PreventAutoPopulateFieldInterceptor } from '../interceptors/prevent-auto-populate-field.interceptor';
import { PreventUpdateFieldInterceptor } from '../interceptors/prevent-update-field.interceptor';
import { ChangeTransactionStageService } from '../services/change-transaction-stage.service';
import { CheckCriteriaValidationService } from '../services/check-criteria-validation.service';
import { ConditionalRequired, ConditionalRequireService } from '../services/conditional-required.service';
import { FormTransactionChangeLogTenancyService } from '../services/form-transaction-change-log.tenancy.service';
import { FormTransactionTenancyService } from '../services/form-transaction.tenancy.service';
import { MigrateTransactionVersionService } from '../services/migrate-transaction-version.service';
import { PopulateTransactionFieldService } from '../services/populate-transaction-field.service';

@ApiTags('Form Transaction')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
@Controller({
    path: 'form-transactions',
})
export class FormTransactionController extends BaseController {
    constructor(
        private readonly service: FormTransactionTenancyService,
        private readonly _changeStageService: ChangeTransactionStageService,
        private readonly generalAutoPopulateService: GeneralAutoPopulateService,
        private readonly populateTransactionFieldService: PopulateTransactionFieldService,
        private readonly migrateTransactionVersionService: MigrateTransactionVersionService,
        private readonly _checkCriteriaValidationService: CheckCriteriaValidationService,
        private readonly _changeLogService: FormTransactionChangeLogTenancyService,
        @Inject(USER_CLAIMS) private readonly claimService: ClaimService,

        private readonly _conditionalRequiredService: ConditionalRequireService,
    ) {
        super();
    }

    //#region GET
    @ApiOperation({ summary: 'Get form transaction change log list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/change-logs/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(TransactionChangeLogInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async getListChangeLog(
        @Param('id') id: string,
        @Query() query: MultipleFilterRequestDto,
    ): Promise<PaginationResponseDto<TransactionRecordChangeLogDto>> {
        const data = await this._changeLogService.getList(id, query);
        return data;
    }

    //#region GET
    @ApiOperation({ summary: 'Get form transaction kanban list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/kanban')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionListInterceptor)
    @UseGuards(DenyAnonymous)
    public async getKanbanList(@Query() query: GetListFormTransactionRequestDto): Promise<PaginationResponseDto<FormTransactionDto>> {
        const data = await this.service.getKanbanList(query);
        return data;
    }

    @ApiOperation({ summary: 'Get form transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get()
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionListInterceptor)
    public async getList(@Query() query: GetListFormTransactionRequestDto): Promise<PaginationResponseDto<FormTransactionDto>> {
        const data = await this.service.getList(query);
        return data;
    }

    @ApiOperation({ summary: 'Filter form transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('filter')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionListInterceptor)
    public async filterTransactions(@Body() body: FilterFormTransactionRequestDto): Promise<PaginationResponseDto<FormTransactionDto>> {
        const data = await this.service.getList(body);
        return data;
    }

    @ApiOperation({ summary: 'Filter form transaction list with pagination' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('filter-pagination')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionListInterceptor)
    public async filterTransactionsWithPagination(@Body() body: any): Promise<any> {
        const data = await this.service.getListPagination(body);
        return data;
    }

    @ApiOperation({ summary: 'Get transactions information' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('information/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async getTransactionInformation(@Param('formId') formId: string): Promise<ResponseDto<GetTransactionInformationResponse[]>> {
        const data = await this.migrateTransactionVersionService.getTransactionInformation(formId);
        return this.getResponse<GetTransactionInformationResponse[]>(!!data, data, []);
    }

    @ApiOperation({ summary: 'Get form transaction detail' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async get(
        @Param('id') id: string,
        @Query('includeRelatedTransactionIds') includeRelatedTransactionIds: boolean,
    ): Promise<ResponseDto<FormTransactionDto>> {
        const data = await this.service.get(id, {
            includeRelatedTransactionIds,
            includeOriginTransactionIds: true,
            includeFieldContextTypes: [TransactionFieldContextTypeEnum.FORM],
        });
        return this.getResponse<FormTransactionDto>(!!data, data, []);
    }

    @ApiOperation({ summary: 'Get form transaction relation tree' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('relation-transaction-tree/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async getRelationTransactionTree(@Param('id') id: string): Promise<ResponseDto<GetRelationTransactionResponse[]>> {
        const accountId = this.claimService.accountId;
        if (!accountId) {
            throw new ForbiddenException();
        }
        const data = await this.service.getRelationTransactionTree(accountId, id);
        return this.getResponse<GetRelationTransactionResponse[]>(!!data, data, []);
    }

    @ApiOperation({ summary: 'Get form transaction fields value' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('fields-value/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async getFieldsValue(
        @Param('id') id: string,
        @Query('fieldIds') fieldIds: string,
    ): Promise<ResponseDto<FormTransactionFieldDto[]>> {
        const ids = fieldIds ? fieldIds.split(',') : [];
        const data = await this.service.getFields(id, ids);
        return this.getResponse<FormTransactionFieldDto[]>(!!data, data.transactionFields, []);
    }

    @ApiOperation({ summary: 'Get form transaction fields value' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('fields-value/collection/:id/:collectionId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @TransactionMetadata({ source: '@Param', key: 'collectionId' })
    @UseGuards(TransactionGuard)
    public async getFieldsCollection(
        @Param('id') id: string,
        @Param('collectionId') collectionId: string,
    ): Promise<ResponseDto<FormTransactionFieldDto[]>> {
        const data = await this.service.getTransactionCollectionFields(id, collectionId);
        return this.getResponse<FormTransactionFieldDto[]>(!!data, data.transactionFields, []);
    }

    @ApiOperation({ summary: 'Check criteria validation' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('check-validation')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async checkValidation(@Body() request: CheckCriteriaValidationRequest) {
        const data = await this._checkCriteriaValidationService.checkCriteriaValidation(request);
        return this.getResponse(true, data);
    }

    //#region GET
    @ApiOperation({ summary: 'Get form transaction action log list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/action-logs/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(TransactionChangeLogInterceptor)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(TransactionGuard)
    public async getListActionLog(
        @Param('id') id: string,
        @Query() query: MultipleFilterRequestDto,
    ): Promise<PaginationResponseDto<TransactionActionLogDto>> {
        const data = await this._changeLogService.getActionLogs(id, query);
        return data;
    }

    //#endregion GET

    //#region POST
    @ApiOperation({ summary: 'Create empty transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('empty')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    // @UseGuards(CreateTransactionGuard)
    public async createEmptyTransaction(
        @Body() request: CreateEmptyTransactionRequest,
    ): Promise<ResponseDto<Pick<TransactionEntity, 'id' | 'formVersionId'> | string>> {
        const result = await this.service.create(request);
        return this.getResponse<Pick<TransactionEntity, 'id' | 'formVersionId'> | string>(
            !!result,
            result as Pick<TransactionEntity, 'id' | 'formVersionId'> | string,
            [],
        );
    }

    @ApiOperation({ summary: 'Execute stage decision' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('decisions')
    @UseGuards(TransactionStageDecisionGuard)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async executeStageDecision(@Body() request: ExecuteDecisionRequestDto): Promise<ResponseDto<ConditionalRequired | boolean>> {
        const data = await this.service.executeDecision(request);
        return this.getResponse(typeof data === 'boolean', data, []);
    }

    @ApiOperation({ summary: 'Change Stage' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('stages')
    @UseGuards(TransactionStageTransitionGuard)
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async changeStage(@Body() request: ChangeStageRequestDto): Promise<ResponseDto<ConditionalRequired | boolean>> {
        const required = await this._conditionalRequiredService.verify({
            transactionId: request.transactionId,
            targetStageId: request.targetStageId,
        });

        if (!required.canChangeStage) {
            return this.getResponse(false, required, []);
        }

        const data = await this._changeStageService.handleTransitionAction({
            targetStageId: request.targetStageId,
            transactionId: request.transactionId,
        });

        return this.getResponse(!!data, !!data, []);
    }

    @ApiOperation({ summary: 'Calculate calculation field from transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('calculate')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async calculateField(
        @Body() request: CalculateFieldRequest,
    ): Promise<ResponseDto<Record<string, string | number | Date | Duration | Dayjs | null>>> {
        const data = await this.service.calculateFields(request);
        return this.getResponse(true, data, null);
    }

    @ApiOperation({ summary: 'General auto populate field from transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('general-auto-populate')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(PreventAutoPopulateFieldInterceptor)
    public async generalAutoPopulateField(@Body() request: GetPopulatedFieldValueRequest): Promise<ResponseDto<Record<string, any>>> {
        const data = await this.generalAutoPopulateService.getPopulatedFormFieldValues(request);
        return this.getResponse(true, data, null);
    }

    @ApiOperation({ summary: 'Migrate transaction version' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('migrate-transaction-version')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(DenyAnonymous)
    public async migrateTransactionVersion(@Body() request: MigrateTransactionVersionRequest) {
        const data = await this.migrateTransactionVersionService.migrateTransactionsVersion(request);
        return this.getResponse(true, data, null);
    }

    @ApiOperation({
        summary: 'Upload file for related form',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('upload-file-related-form')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(FileInterceptor('file'))
    public async uploadFileRelatedForm(@Body() request: UploadFileForRelatedFormRequest, @UploadedFile() file: Express.Multer.File) {
        const result = await this.service.uploadFileRelatedForm(request, file);
        return this.getResponse(true, result);
    }

    //#endregion POST

    //#region PUT
    @ApiOperation({ summary: 'Update form transaction' })
    @Version('1')
    @HttpCode(HttpStatus.ACCEPTED)
    @Put(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Param', key: 'id' })
    @UseGuards(EditFieldsTransactionGuard, TransactionGuard)
    @UseInterceptors(PreventUpdateFieldInterceptor)
    public async update(@Param('id') id: string, @Body() request: EditFormTransactionRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.update(id, request, {
            shouldRunPopulateFormFields: true,
        });
        return this.getResponse<boolean>(result);
    }

    //#endregion PUT

    //#region Delete
    @ApiOperation({ summary: 'Delete form transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Delete(':id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(DenyAnonymous, DeleteTransactionGuard)
    public async delete(@Param('id') id: string): Promise<ResponseDto<boolean>> {
        const result = await this.service.delete(id);
        return this.getResponse<boolean>(!!result, result, []);
    }
    //#endregion Delete
}
