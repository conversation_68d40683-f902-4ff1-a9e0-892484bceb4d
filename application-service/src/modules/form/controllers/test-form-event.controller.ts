import { Body, Controller, HttpCode, HttpStatus, Post, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { ManualEventTriggerRequestDto } from '../dtos/requests/manual-event-trigger.request';
import { FormEventTenancyService } from '../services/form-event.service';
import { TransactionMetadata } from '../guards/api-metadata.decorator';
import { TransactionGuard } from '../guards/transaction.guard';

@Controller({
    path: 'test-form-transaction-event',
})
@ApiTags('Test Form Transaction Event')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class TestFormEventController extends BaseController {
    constructor(private readonly _formEventService: FormEventTenancyService) {
        super();
    }

    @ApiOperation({ summary: 'Trigger form manual event' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('manual')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @TransactionMetadata({ source: '@Body', key: 'transactionId' })
    @UseGuards(TransactionGuard)
    public async trigger(@Body() request: ManualEventTriggerRequestDto) {
        const data = await this._formEventService.handleManualEvent({ ...request, isTest: true });
        return this.getResponse(true, data);
    }
}
