import { AutoMap } from '@automapper/classes';
import { AbstractDto } from '../../../shared/common/dto/abstract.dto';
import { FormTransactionFieldDto } from './form-transaction-field.dto';
import { EditUser } from '../../../shared/common/dto/edit-user.dto';

export class FormTransactionDto extends AbstractDto {
    @AutoMap()
    formId: string;

    @AutoMap()
    stageId: string;

    @AutoMap()
    stageName?: string;

    @AutoMap()
    formName?: string;

    @AutoMap()
    previousStageName?: string;

    stageIdentityId?: string;

    @AutoMap()
    formVersionId: string;

    @AutoMap()
    formVersion: number;

    @AutoMap()
    previousStageId?: string;

    @AutoMap(() => [FormTransactionFieldDto])
    transactionFields?: FormTransactionFieldDto[];

    @AutoMap(() => [String])
    relatedTransactionIds?: string[];

    @AutoMap(() => [String])
    originTransactionIds?: string[];

    @AutoMap()
    updated?: EditUser;

    @AutoMap()
    created?: EditUser;

    isEmptyCollectionFields?: boolean;

    label?: string;

    @AutoMap()
    rollupStatus?: string;

    @AutoMap()
    autoCreationStatus?: string;

    @AutoMap()
    isTest?: boolean;

    stageEnteredAt?: Date;
}
