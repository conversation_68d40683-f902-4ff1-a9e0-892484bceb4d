import { SourceOfChangeType } from '../../../../database/src/shared/enums/change-log.type.enum';

export class RollUpCalculationEvent {
    accountId: string;
    transactionId: string;
    formId: string;
    formVersionId: string;
    fields: string[];
    causationTransId?: string;
    sourceOfChange?: SourceOfChangeType;
    isTest?: boolean;

    constructor(data: IRollUpCalculationEvent) {
        this.accountId = data.accountId;
        this.transactionId = data.transactionId;
        this.formId = data.formId;
        this.formVersionId = data.formVersionId;
        this.fields = data.fields;
        this.causationTransId = data.causationTransId;
        this.sourceOfChange = data.sourceOfChange;
        this.isTest = data.isTest;
    }
}

export interface IRollUpCalculationEvent {
    accountId: string;
    transactionId: string;
    formId: string;
    formVersionId: string;
    fields: string[];
    causationTransId?: string;
    sourceOfChange?: SourceOfChangeType;
    isTest?: boolean;
}
