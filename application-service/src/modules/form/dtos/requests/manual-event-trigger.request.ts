import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { ManualEventSourceType } from '../../../../database/src/shared/enums/form-manual-event.enum';

export class ManualEventTriggerRequestDto {
    @IsUUID()
    @AutoMap()
    formVersionId: string;

    @IsUUID()
    @AutoMap()
    transactionId: string;

    @ApiProperty({
        enum: ManualEventSourceType,
    })
    @AutoMap()
    @IsEnum(ManualEventSourceType)
    type: ManualEventSourceType;

    @IsUUID()
    @AutoMap()
    @IsOptional()
    sourceId?: string;

    @IsUUID()
    @AutoMap()
    @IsOptional()
    eventId?: string;

    @IsUUID()
    @AutoMap()
    eventVersionId?: string;

    @IsUUID()
    @AutoMap()
    stageId: string;

    @IsUUID()
    @AutoMap()
    roleId: string;

    @IsString()
    @AutoMap()
    browserTabId?: string;

    validRoleIds: string[];

    @IsBoolean()
    @AutoMap()
    @IsOptional()
    isTest?: boolean;
}
