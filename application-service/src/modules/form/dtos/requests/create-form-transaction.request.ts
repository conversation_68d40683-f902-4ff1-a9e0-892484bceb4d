import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsObject, IsOptional, IsString, IsUUID } from 'class-validator';

import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';

export class TransactionFieldDataConfig {
    @AutoMap()
    @IsOptional()
    filePath?: string;

    @AutoMap()
    @IsOptional()
    collectionItemExternalId?: string;

    transactions?: Record<string, unknown>;
}

export class EditFormTransactionFieldRequest {
    @IsUUID()
    @IsOptional()
    @ApiPropertyOptional()
    @AutoMap()
    id?: string;

    @IsUUID()
    @IsOptional()
    @ApiPropertyOptional()
    @AutoMap()
    transactionId?: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    fieldId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    fieldValue?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    fieldLabelValue?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    fieldFormula?: string;

    @ApiPropertyOptional({
        enum: FormFieldTypeEnum,
    })
    @IsOptional()
    @IsEnum(FormFieldTypeEnum)
    @AutoMap()
    fieldType?: FormFieldTypeEnum;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    defaultValue?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    collectionId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    collectionItemId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    parentId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    pairId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    contextType?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    rowKey?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap(() => [String])
    fieldOptionIds?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsObject()
    @AutoMap(() => TransactionFieldDataConfig)
    data?: TransactionFieldDataConfig;

    @ApiPropertyOptional()
    @IsOptional()
    @IsObject()
    @AutoMap()
    formValues?: Record<string, string>;

    @AutoMap()
    registerRecordId?: string;

    displayAttributeFieldIds?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    @AutoMap()
    isTest?: boolean;
}

export class EditFormTransactionRequest {
    @IsUUID()
    @ApiProperty()
    @AutoMap()
    transactionId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    formId: string;

    @ApiPropertyOptional()
    @AutoMap()
    @IsUUID()
    @IsOptional()
    originalTransactionId?: string;

    @ApiProperty({
        type: [EditFormTransactionFieldRequest],
    })
    @IsArray()
    @AutoMap(() => [EditFormTransactionFieldRequest])
    transactionFields: EditFormTransactionFieldRequest[];

    @IsObject()
    @IsOptional()
    @AutoMap()
    @ApiPropertyOptional()
    formValues?: Record<string, any>;

    @IsArray()
    @IsOptional()
    @AutoMap()
    @ApiPropertyOptional()
    fieldChangeIds?: string[];

    @IsUUID()
    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    roleId?: string;

    @ApiProperty()
    @IsObject()
    @IsOptional()
    payloadDocuments?: Record<string, string>;

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    isTest?: boolean;
}

export class CreateEmptyTransactionRequest {
    @IsUUID()
    @ApiProperty()
    @AutoMap()
    formId: string;

    @IsUUID()
    @ApiPropertyOptional()
    @AutoMap()
    @IsOptional()
    originalTransactionId?: string;

    @ApiProperty()
    @AutoMap()
    @IsString()
    timezone: string;

    @ApiProperty()
    @AutoMap()
    @IsBoolean()
    @IsOptional()
    isReturnTrans?: boolean;

    @ApiProperty()
    @IsBoolean()
    @IsOptional()
    skipRollup?: boolean;

    @ApiProperty()
    @IsBoolean()
    @IsOptional()
    isReturnObjectResult?: boolean;

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    isTest?: boolean;

    //form version is not passed from http request
    formVersionId?: string;

    skipPopulate?: boolean;
    skipValidation?: boolean;
    skipMapDependLookupField?: boolean;
}

export class ChangeStageRequest {
    @IsUUID()
    @ApiProperty()
    @AutoMap()
    activeStageId: string;
}
