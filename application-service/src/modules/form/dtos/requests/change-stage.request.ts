import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';

export class ChangeStageRequestDto {
    @IsUUID()
    @ApiProperty()
    @AutoMap()
    targetStageId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    transactionId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    roleId: string;

    @IsString()
    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    browserTabId: string;

    @IsBoolean()
    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    isTest: boolean;
}
