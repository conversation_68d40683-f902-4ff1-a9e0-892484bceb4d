import { AutoMap } from '@automapper/classes';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';
import { AutoPopulateDataLakeResponse } from '../../../../shared/common/dto/autopopulate-datalake.dto';
import { DataRegisterTransactionFieldEntity } from '../../../../database/src/entities/public/data-register-transaction-field.public.entity';

export class PopulateCollectionRequest {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    formVersionId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    formValues: Record<string, any>;

    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    triggerField?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    @IsArray()
    triggerFields?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    transactionId: string;

    @ApiPropertyOptional()
    @IsOptional()
    cachedFormVersion?: any;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    targetFieldChangeIds?: string[];
    targetFieldChangeFromRegisterId?: string;

    payloadDocuments: Record<string, string>;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isTest: boolean;
}
