import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';

export class ExecuteDecisionRequestDto {
    @IsUUID()
    @ApiProperty()
    @AutoMap()
    formId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    stageId: string; //using for validate acl

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    decisionIdentityId: string; //using for validate acl

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    decisionId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    transactionId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    formVersionId: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    roleId: string;

    @IsString()
    @ApiProperty()
    @AutoMap()
    browserTabId: string;

    @IsBoolean()
    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    isTest: boolean;
}
