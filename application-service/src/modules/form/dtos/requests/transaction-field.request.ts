import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNumber, IsObject, IsOptional, IsString, IsUUID } from 'class-validator';
import { EditFormTransactionFieldRequest } from './create-form-transaction.request';

export class CreateTransactionFieldRequest {
    @ApiProperty()
    @IsUUID()
    transactionId: string;

    @ApiProperty()
    @IsUUID()
    formId: string;

    @ApiProperty()
    @IsUUID()
    collectionIdentityId: string;

    @ApiProperty()
    @IsArray()
    fields: EditFormTransactionFieldRequest[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isTest?: boolean;

    @ApiProperty()
    @IsUUID()
    activeRoleId: string;
}

export class DeleteCollectionRowRequest {
    @ApiProperty()
    @IsUUID()
    rowKey: string;

    @ApiProperty()
    @IsUUID()
    transactionId: string;

    @ApiProperty()
    @IsUUID()
    collectionIdentityId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isTest?: boolean;

    @ApiProperty()
    @IsUUID()
    activeRoleId: string;
}

export class FormCollectionTransactionFieldRequest {
    @ApiProperty()
    @IsString()
    formCollectionIdentityId: string;

    @ApiProperty()
    @IsString()
    formVersionId: string;
}

export class OverrideTransactionFieldRequest {
    @ApiProperty()
    @IsString()
    transactionId: string;

    @ApiProperty()
    @IsString()
    transactionFieldId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsNumber()
    validationValue?: number;

    @ApiProperty()
    @IsObject()
    dependencyValues: Record<string, string>;

    @ApiProperty()
    @IsString()
    comment: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isTest?: boolean;
}

export class UnOverrideValidationFieldRequest {
    @ApiProperty()
    @IsUUID()
    transactionFieldId: string;

    @ApiProperty()
    @IsString()
    comment: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isTest?: boolean;
}

export class AutoPopulateAndCheckValidateFieldRequest {
    @ApiProperty()
    @IsString()
    transactionFieldId: string;

    @ApiProperty()
    @IsString()
    formVersionId: string;

    @ApiProperty()
    @IsObject()
    formValues: Record<string, any>;
}

export class UploadFileForRelatedFormRequest {
    @IsUUID()
    @ApiProperty()
    @AutoMap()
    formId: string;

    @IsUUID()
    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    formVersionId?: string;

    @IsUUID()
    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap()
    originalTransactionId?: string;

    @ApiProperty()
    @AutoMap()
    @IsString()
    timezone: string;

    @ApiProperty()
    @AutoMap()
    @IsString()
    documentFieldId: string;

    @ApiPropertyOptional()
    @IsBoolean()
    @IsOptional()
    isTest?: boolean;
}
