import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsObject, IsOptional, IsString } from 'class-validator';
import { FormFieldRequest } from './form-field.request';

export class CalculateFieldRequest {
    @ApiProperty()
    @IsString()
    @AutoMap()
    transactionId: string;

    @ApiProperty()
    @IsString()
    @AutoMap()
    fieldChangeId: string;

    @ApiProperty({
        type: [FormFieldRequest],
    })
    @IsArray()
    @AutoMap()
    calculationFields: FormFieldRequest[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsObject()
    @AutoMap()
    variableValues: { [fieldId: string]: any };

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    @AutoMap()
    isTest?: boolean;
}
