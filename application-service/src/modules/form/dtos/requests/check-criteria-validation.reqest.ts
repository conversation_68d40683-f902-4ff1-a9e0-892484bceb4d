import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsObject, IsOptional, IsString } from 'class-validator';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { DataRegisterTypeEnum } from '../../../../database/src/shared/enums/data-register-type.enum';
import { CaptureActiveFormVersionType } from '../../../../database/src/shared/providers/capture-active-form-version.provider';

export class CheckCriteriaValidationRequest {
    @ApiProperty()
    @IsString()
    formVersionId: string;

    @ApiProperty()
    @IsString()
    transactionId: string;

    @ApiProperty()
    @IsObject()
    formValues: Record<string, any>;

    @ApiProperty({
        type: 'enum',
        enum: [DataRegisterTypeEnum.Collection, DataRegisterTypeEnum.Criteria],
    })
    @IsEnum(DataRegisterTypeEnum)
    collectionType: DataRegisterTypeEnum;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    triggerFieldId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    transactionFields?: TransactionFieldEntity[];

    @ApiPropertyOptional()
    @IsOptional()
    cachedFormVersion?: CaptureActiveFormVersionType;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isTest?: boolean;
}
