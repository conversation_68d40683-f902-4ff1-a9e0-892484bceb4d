import { forwardRef, Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { EventDrivenModule } from '../../common/src';
import { FormCollectionCacheService } from '../../common/src/modules/shared/services/cache/form-collection.cache.service';
import { FormulaService } from '../../common/src/modules/shared/services/formula.service';
import { DataSourceService } from '../../database/src/services/connection-util.service';
import { FileUploadConfigService } from '../../fileValidations/file-upload-validation';
import { CalculationService } from '../../shared/services/calculation.service';
import { SharedModule } from '../../shared/shared.module';
import { BullMQModule } from '../bull-mq/bull.module';
import { ScheduleWfsKpiStatusService } from '../bull-mq/services';
import { FileModule } from '../file/file.module';
import { ValidationFormFieldModule } from '../validation/validation.module';
import { TransactionAutoCreationListener } from '../worker/services/related-transaction-auto-creation.listener';
import { FormCollectionActionController } from './controllers/form-collection-action.controller';
import { FormCollectionItemTenancyController } from './controllers/form-collection-transaction-field-tenancy.controller';
import { FormCollectionItemController } from './controllers/form-collection-transaction-field.controller';
import { FormEventController } from './controllers/form-event.controller';
import { FormTransactionFieldController } from './controllers/form-transaction-field.controller';
import { FormTransactionController } from './controllers/form-transaction.controller';
import { TestFormEventController } from './controllers/test-form-event.controller';
import { FormTransactionFieldTestController } from './controllers/test-form-transaction-field.controller';
import { FormTransactionTestController } from './controllers/test-form-transaction.controller';
import { formProviders } from './form-provider';
import { RelatedFieldChangeListener } from './listeners/related-field-change.listener';
import { TransactionCollectionCreationListener } from './listeners/related-transaction-collection-creation.listener';
import { TransactionFieldEntityListener } from './listeners/transaction-field-entity.listener';
import { FormTransactionFieldMappingProfile, FormTransactionMappingProfile } from './mapping-profiles';
import { FormCollectionProfile } from './mapping-profiles/form-collection.profile';
import { UpdateFormTransactionPipe } from './pipes/update-form-transaction.pipe';
import { AutoCreateCollectionTenancyService } from './services/auto-creation-collection.tenancy.service';
import { AutoCreationTransactionService } from './services/auto-creation.tenancy.service';
import { AutoPopulateTransactionFieldService } from './services/auto-populate.tenancy.service';
import { ChangeTransactionStageService } from './services/change-transaction-stage.service';
import { CheckCriteriaValidationService } from './services/check-criteria-validation.service';
import { CollectionFieldMappingService } from './services/collection-field-mapping.service';
import { ConditionalRequireService } from './services/conditional-required.service';
import { AutoPopulateDataService } from './services/data/auto-populate.data.service';
import { FormCollectionTransactionFieldDataService } from './services/data/form-collection-transaction-field.service';
import { FormEventDataService } from './services/data/form-event.data.service';
import { FormTransactionChangeLogDataService } from './services/data/form-transaction-change-log.data.service';
import { FormTransactionFieldDataService } from './services/data/form-transaction-field.data.service';
import { FormTransactionDataService } from './services/data/form-transaction.data.service';
import { FormatTransactionFieldService } from './services/data/format/transaction-field.service';
import { GetFormTransactionDataService } from './services/data/get-form-transaction.data.service';
import { RelatedLookupDataService } from './services/data/related-lookup-data.service';
import { DefaultTransactionFieldService } from './services/default-transaction-field.service';
import { FormCollectionActionService } from './services/form-collection-action.service';
import { FormCollectionTransactionFieldTenancyService } from './services/form-collection-transaction-field-tenancy.service';
import { FormCollectionTransactionFieldService } from './services/form-collection-transaction-field.service';
import { FormCollectionDataService } from './services/form-collection.data.service';
import { FormEventTenancyService } from './services/form-event.service';
import { FormTransactionChangeLogTenancyService } from './services/form-transaction-change-log.tenancy.service';
import { FormTransactionFieldTenancyService } from './services/form-transaction-field.tenancy.service';
import { FormTransactionStatusTenancyService } from './services/form-transaction-status.tenancy.service';
import { FormTransactionTenancyService } from './services/form-transaction.tenancy.service';
import { MigrateTransactionVersionService } from './services/migrate-transaction-version.service';
import { PopulateTransactionFieldService } from './services/populate-transaction-field.service';
import { RollUpCalculationService } from './services/rollup-calculation.service';
import { ScoringSystemService } from './services/scoring-system.service';

const listeners = [
    RelatedFieldChangeListener,
    TransactionAutoCreationListener,
    TransactionFieldEntityListener,
    TransactionCollectionCreationListener,
];

const providers = [
    FormTransactionMappingProfile,
    FormTransactionFieldMappingProfile,
    FormulaService,
    FormTransactionDataService,
    FormTransactionTenancyService,
    RollUpCalculationService,
    FormTransactionFieldTenancyService,
    FormTransactionFieldDataService,
    AutoPopulateTransactionFieldService,
    FormCollectionTransactionFieldService,
    FormCollectionTransactionFieldTenancyService,
    FormCollectionTransactionFieldDataService,
    AutoCreationTransactionService,
    FormCollectionProfile,
    DefaultTransactionFieldService,
    PopulateTransactionFieldService,
    CheckCriteriaValidationService,
    UpdateFormTransactionPipe,
    CalculationService,
    MigrateTransactionVersionService,
    CollectionFieldMappingService,
    AutoPopulateDataService,
    FormatTransactionFieldService,
    AutoCreateCollectionTenancyService,
    RelatedLookupDataService,
    GetFormTransactionDataService,
    FormEventDataService,
    FormEventTenancyService,
    FormTransactionStatusTenancyService,
    FormTransactionChangeLogTenancyService,
    FormTransactionChangeLogDataService,
    FormCollectionActionService,
    FormCollectionCacheService,
    ScoringSystemService,
    FormCollectionDataService,
    ConditionalRequireService,
    ChangeTransactionStageService,
    ScheduleWfsKpiStatusService,
    ...listeners,
    ...formProviders,
] as const;

const controllers =
    process.env.MODULE !== 'api'
        ? ([
              FormTransactionController,
              FormTransactionFieldController,
              FormCollectionItemController,
              FormCollectionItemTenancyController,
              FormEventController,
              // TestActionController,
              FormCollectionActionController,
              FormTransactionTestController,
              FormTransactionFieldTestController,
              TestFormEventController,
          ] as const)
        : [];

@Module({
    imports: [
        forwardRef(() => SharedModule),
        ValidationFormFieldModule,
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
        MulterModule.registerAsync({
            useClass: FileUploadConfigService,
        }),
        FileModule,
        BullMQModule,
    ],
    controllers: [...controllers],
    providers: [...providers, FileUploadConfigService],
    exports: [
        PopulateTransactionFieldService,
        CheckCriteriaValidationService,
        FormTransactionDataService,
        FormTransactionTenancyService,
        ScoringSystemService,
        FormCollectionTransactionFieldService,
        DefaultTransactionFieldService,
        FormTransactionFieldTenancyService,
        FormCollectionTransactionFieldTenancyService,
        FormCollectionDataService,
    ],
})
export class FormModule {}
