import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { TransactionEntity } from 'src/database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSource } from 'typeorm';
import { CacheService, UtilsService } from '../../../common/src';
import {
    IStageTransitionRoleACL,
    IStageTransitionRoleACLProvider,
} from '../../../common/src/modules/acl/interfaces/stage-transition-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { StageTransitionTenancyEntity } from '../../../database/src/entities/tenancy/stage-transition.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';

type BodyBase = {
    roleId: string;
    transactionId: string;
    targetStageId: string;
    isTest?: boolean;
};

@Injectable()
export class TransactionStageTransitionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageTransitionRoleACL)
        private readonly _stageRoleTransitionACL: IStageTransitionRoleACLProvider,

        private readonly _cacheService: CacheService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: BodyBase; user: { roles: { id: string }[]; accountId: string } };

        const { targetStageId, transactionId, roleId, isTest } = body;

        const transaction = await this._dataSource.getRepository(TransactionEntity).findOne({
            where: {
                id: transactionId,
            },
            withDeleted: !!isTest,
        });

        console.log('🐛 ~ TransactionStageTransitionGuard ~ canActivate ~ transaction:', transaction);

        if (!transaction) {
            throw new ForbiddenException('transaction_not_found');
        }

        if (transaction.stageId === targetStageId) {
            throw new ForbiddenException('already_in_this_stage');
        }

        const userRoles = user?.roles || [];
        if (!userRoles.some((ur) => ur.id === roleId)) {
            throw new ForbiddenException('user_not_in_role');
        }

        const stageRepo = this._dataSource.getRepository(StageTenancyEntity);
        const stageTransitionRepo = this._dataSource.getRepository(StageTransitionTenancyEntity);

        const cacheKey = UtilsService.getActiveFormVersionCacheKeys({
            accountId: user.accountId,
            formId: transaction.formId,
            formVersionId: transaction.formVersionId,
        });
        const cacheFormVersion: {
            stages: StageTenancyEntity[];
            stageTransitions: StageTransitionTenancyEntity[];
        } = cacheKey ? await this._cacheService.jsonGet(cacheKey.formVersionKey) : null;

        const stages = cacheFormVersion?.stages || [];

        const cachedStage = stages.find((s) => s.id === targetStageId);
        //find required upload files to change stage
        const stage = cachedStage
            ? cachedStage
            : await stageRepo.findOne({
                  where: { id: targetStageId },
                  select: ['id', 'name', 'config'],
              });

        if (!stage) {
            throw new ForbiddenException('stage_not_found');
        }

        const fromStageId = transaction.stageId;

        const stageRoleACLs = await this._stageRoleTransitionACL.get({
            stageTransitionRepo,
            formVersionId: transaction.formVersionId,
            roleId: roleId,
        });

        const stageTransition = stageRoleACLs.find((sra) => sra.sourceId === fromStageId && sra.targetId === targetStageId);
        if (!stageTransition?.canTransition) {
            throw new ForbiddenException('stage_transition_not_allowed');
        }

        return true;
    }
}
