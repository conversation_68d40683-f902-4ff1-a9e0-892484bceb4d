import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionValidationError } from '../../validation/enums/transaction-validation-errors.enum';
import { StageDecisionACLValidation } from '../../validation/services/stage-decisions-acl.validation';
import { ExecuteDecisionRequestDto } from '../dtos/requests';

@Injectable()
export class EditDecisionFormTransactionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
        private readonly stageDecisionValidation: StageDecisionACLValidation,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: ExecuteDecisionRequestDto; user: { roles: { id: string }[] } };

        const { formVersionId, roleId, stageId, decisionIdentityId, isTest } = body;

        if (!roleId) {
            throw new ForbiddenException('role_id_empty');
        }

        if (!decisionIdentityId) {
            throw new ForbiddenException('decision_id_empty');
        }

        const userRoles = user?.roles || [];
        if (!userRoles.some((ur) => ur.id === roleId)) {
            throw new ForbiddenException('user_not_in_role');
        }

        const [stageRoleACLs] = await Promise.all([
            this._stageRoleACL.getRoleStageACLs({
                request: {
                    formVersionId,
                    stageRoles: [
                        {
                            roleId,
                            stageId,
                        },
                    ],
                },
                stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
                stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
                ignoreCache: !!isTest,
            }),
        ]);

        const stageRoleACL = (stageRoleACLs || []).find((item) => item.roleId === roleId && item.stageId === stageId);
        if (!stageRoleACL) {
            throw new ForbiddenException('role_can_not_edit_stage');
        }

        const stageDecisionValidation = this.stageDecisionValidation.validateStageDecisionACL({
            stageRoleACL,
            decisionId: decisionIdentityId,
        });

        const canEdit = !stageDecisionValidation;
        if (!canEdit) {
            const combineErrorData = [];
            const combineErrorMessages = [];

            if (stageDecisionValidation) {
                combineErrorData.push({
                    type: TransactionValidationError.FieldACLError,
                    items: stageDecisionValidation.data,
                });
                combineErrorMessages.push(stageDecisionValidation.message);
            }

            throw new ForbiddenException({
                message: combineErrorMessages,
                data: combineErrorData,
            });
        }

        return canEdit;
    }
}
