import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { uniq } from 'lodash';
import { DataSource } from 'typeorm';
import { TRANSACTION_FIELD_ID } from '../../../common/src/constant/field';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { TransactionValidationError } from '../../validation/enums/transaction-validation-errors.enum';
import { TransactionFieldACLValidation } from '../../validation/services/transaction-field-acl.validation';
import { TransactionFieldDataTypeValidation } from '../../validation/services/transaction-field-data-type.validation';
import { EditFormTransactionFieldRequest } from '../dtos/requests/create-form-transaction.request';
import { TransactionEntity } from 'src/database/src/entities/tenancy/transaction.tenancy.entity';

type BodyBase = {
    roleId: string;
    stageId: string;
    formVersionId: string;
    transactionFields: EditFormTransactionFieldRequest[];
    transactionId: string;
    formId: string;
    originalTransactionId?: string;
    activeStageId: string;
    stageIdentityId?: string;
    activeStageIdentityId: string;
    formValues?: Record<string, any>;
    fieldChangeIds?: string[];
    isTest?: boolean;
};

@Injectable()
export class EditFieldsTransactionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,

        private readonly tfValidation: TransactionFieldACLValidation,
        private readonly tfDataTypeValidation: TransactionFieldDataTypeValidation,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: BodyBase; user: { roles: { id: string }[] } };

        const { formVersionId, roleId, transactionFields = [], fieldChangeIds, transactionId, isTest } = body;
        let activeStageId = body.activeStageId;

        if (!roleId) {
            throw new ForbiddenException('role_id_empty');
        }

        if (!activeStageId) {
            // throw new ForbiddenException('stage_id_empty');
            const transaction = await this._dataSource.getRepository(TransactionEntity).findOne({
                where: { id: transactionId },
                select: ['stageId'],
                withDeleted: !!isTest,
            });
            activeStageId = transaction?.stageId;
        }

        const userRoles = user?.roles || [];
        if (!userRoles.some((ur) => ur.id === roleId)) {
            throw new ForbiddenException('user_not_in_role');
        }

        const stageRoles = await this._dataSource.getRepository(StageRoleTenancyEntity).find({
            where: {
                formVersionId,
                stageId: activeStageId,
            },
        });

        const stageRoleACLs = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId,
                stageRoles: [
                    {
                        roleId,
                        stageId: activeStageId,
                    },
                ],
                includeFieldConfig: true,
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
            ignoreCache: !!isTest,
        });

        const stageRoleACL = (stageRoleACLs || []).find((item) => item.roleId === roleId && item.stageId === activeStageId);
        if (!stageRoleACL) {
            throw new ForbiddenException('stage_role_not_in_form_version');
        }

        const formFields = (stageRoleACL.fieldsConfigurations || []) as FormFieldTenancyEntity[];
        const transactionFormFields = (transactionFields || [])
            .filter((field) => (fieldChangeIds || []).includes(field.fieldId))
            .filter((field) => [TransactionFieldContextTypeEnum.FORM].includes(field.contextType as TransactionFieldContextTypeEnum))
            .filter((field) => field.fieldId !== TRANSACTION_FIELD_ID);

        const fieldErrorValidation = this.tfValidation.validateTransactionFieldsACL({
            stageRoleACL,
            transactionFields: transactionFormFields,
            fieldsConfigurations: formFields,
        });

        const fieldDataTypeValidation = await this.tfDataTypeValidation.validateTransactionFieldsDataType({
            fields: transactionFormFields,
            fieldsConfigurations: formFields,
            dataRegisterTransactionRepository: this._dataSource.getRepository(DataRegisterTransactionTenancyEntity),
            formTransactionFieldRepository: this._dataSource.getRepository(TransactionFieldEntity),
            userRepository: this._dataSource.getRepository(UserTenancyEntity),
            transactionId: body.transactionId,
            availableRoles: uniq(stageRoles.map((sr) => sr.roleId)),
        });

        const canEdit = !fieldErrorValidation && !fieldDataTypeValidation;

        if (!canEdit) {
            if (fieldErrorValidation) {
                throw new ForbiddenException(TransactionValidationError.FieldACLError);
            }

            if (fieldDataTypeValidation) {
                throw new ForbiddenException(TransactionValidationError.FieldDataTypeError);
            }
        }

        return canEdit;
    }
}
