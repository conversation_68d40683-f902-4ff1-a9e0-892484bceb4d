import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { FormRoleStageACLDto } from 'src/common/src/modules/acl/dto/form-role-stage-acl.dto';
import { TransactionEntity } from 'src/database/src/entities/tenancy/transaction.tenancy.entity';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { CreateTransactionFieldRequest } from '../dtos/requests/transaction-field.request';

@Injectable()
export class CreateCollectionRowGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body } = request as { body: CreateTransactionFieldRequest };

        const { transactionId, collectionIdentityId, isTest, activeRoleId } = body;

        const transaction = await this._dataSource.getRepository(TransactionEntity).findOne({
            where: {
                id: transactionId,
            },
            withDeleted: !!isTest,
        });

        if (!transaction) {
            throw new ForbiddenException('transaction_not_found');
        }

        const stageRoles = [
            {
                roleId: activeRoleId,
                stageId: transaction.stageId,
            },
        ];

        const stageRoleACLs: FormRoleStageACLDto[] = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: transaction.formVersionId,
                stageRoles,
                includeFieldConfig: true,
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
            ignoreCache: !!isTest,
        });

        const stageRoleACL = stageRoleACLs?.find((item) => item.roleId === activeRoleId && item.stageId === transaction.stageId);

        const aclRecord = stageRoleACL?.createDeleteCollection?.[collectionIdentityId];

        // *NOTE: If no record, that means this form is created before the create collection row feature is implemented
        // * So we allow to create collection row to make sure the old form still work
        if (!aclRecord) return true;

        if (aclRecord.canCreate?.toString() === 'false') {
            throw new ForbiddenException('user_has_no_permission_on_create_collection_row');
        }

        return true;
    }
}
