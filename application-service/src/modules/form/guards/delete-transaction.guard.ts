import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionEntity } from 'src/database/src/entities/tenancy/transaction.tenancy.entity';

@Injectable()
export class DeleteTransactionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { user } = request ?? {};

        const { id: transactionId } = request.params;

        const roles = user?.roles || [];
        if (!roles?.length) {
            throw new ForbiddenException('user_has_not_roles');
        }

        const transaction = await this._dataSource.getRepository(TransactionEntity).findOne({
            where: {
                id: transactionId,
            },
        });

        if (!transaction) {
            throw new ForbiddenException('transaction_not_found');
        }

        const acls = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: transaction.formVersionId,
                stageRoles: roles.map((r) => {
                    return {
                        roleId: r.id ?? (r as string),
                        stageId: transaction.stageId,
                    };
                }),
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
        });

        const canDelete = acls.some((acl) => acl.canDelete?.toString() === 'true');

        if (!canDelete) {
            throw new ForbiddenException('user_has_no_permission_on_delete_transaction');
        }

        return true;
    }
}
