import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { FormRoleStageACLDto } from '../../../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { CommonService } from '../../../shared/services';
import { OverrideTransactionFieldRequest } from '../dtos/requests/transaction-field.request';

@Injectable()
export class OverrideTransactionFieldValidationGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,

        private readonly commonService: CommonService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: OverrideTransactionFieldRequest; user: { roles: { id: string }[] } };

        const { transactionFieldId, isTest } = body;

        const transactionFieldRepo = this.commonService.getFormTransactionFieldRepository();
        const transactionFieldEntity = await transactionFieldRepo.findOne({
            where: {
                id: transactionFieldId,
            },
            // relations: ['transaction'],
        });

        const transactionRepo = this.commonService.getFormTransactionRepository();

        const transaction = await transactionRepo.findOne({
            where: {
                id: transactionFieldEntity.transactionId,
                isTest: !!isTest,
            },
            withDeleted: !!isTest,
            select: {
                id: true,
                formVersionId: true,
                stageId: true,
            },
        });

        if (!transactionFieldEntity || !transaction) {
            throw new ForbiddenException('transaction_field_not_found');
        }

        const { formVersionId, stageId } = transaction;

        const roles = user.roles || [];
        if (!roles) {
            throw new ForbiddenException('user_has_not_roles');
        }

        const acls: FormRoleStageACLDto[] = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId,
                stageRoles: roles.map((r) => {
                    return {
                        roleId: r.id,
                        stageId,
                    };
                }),
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            ignoreCache: !!isTest,
        });

        if (!acls?.length) {
            throw new ForbiddenException('no_override_validation_value_permission');
        }

        const { fieldId, collectionItemId } = transactionFieldEntity;
        const fieldType: 'FormField' | 'CollectionField' = collectionItemId ? 'CollectionField' : 'FormField';

        for (const acl of acls) {
            switch (fieldType) {
                case 'FormField': {
                    const fieldAcls = acl.field;
                    const fieldAcl = fieldAcls[transactionFieldEntity.fieldId];
                    if (fieldAcl.override?.toString() === 'true') return true;
                    break;
                }

                case 'CollectionField': {
                    const collectionItemAcls = acl.collection;
                    const collectionItemAcl = collectionItemAcls[`${fieldId}_${collectionItemId}`];
                    if (collectionItemAcl.override?.toString() === 'true') return true;
                    break;
                }
            }
        }

        return false;
    }
}
