import { CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { CreateEmptyTransactionRequest } from '../dtos/requests/create-form-transaction.request';

@Injectable()
export class CreateTransactionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: CreateEmptyTransactionRequest; user: { roles: { id: string }[] } };

        const { formId, originalTransactionId, isTest } = body;

        const roles = user.roles || [];
        if (!roles) {
            throw new ForbiddenException('user_has_not_roles');
        }

        const stageEntities = await this._stageRoleACL.getFormStages<StageTenancyEntity>({
            formId,
            formRepo: this._dataSource.getRepository(FormTenancyEntity),
            stageRepo: this._dataSource.getRepository(StageTenancyEntity),
            select: ['id', 'config', 'formVersionId'],
            isTest,
        });

        const startStage = (stageEntities || []).find((stage) => stage.config.type === 'START');
        if (!startStage) throw new ForbiddenException('start_stage_not_found');

        const acls = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: startStage.formVersionId,
                stageRoles: roles.map((r) => {
                    return {
                        roleId: r.id,
                        stageId: startStage.id,
                    };
                }),
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            ignoreCache: !!isTest,
        });

        const canCreate = acls.some((acl) => acl.canCreate?.toString() === 'true');

        if (!canCreate) {
            throw new ForbiddenException('user_has_no_permission_on_start_stage');
        }

        return true;
    }
}
