import { <PERSON><PERSON>, Mapping<PERSON>rofile, createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { TransactionChangeLogTenancyEntity } from '../../../database/src/entities/tenancy/transaction-change-log.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormTransactionDto } from '../dtos';
import { TransactionRecordChangeLogDto } from '../dtos/form-transaction-change-log.dto';
import { EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';
import { TransactionActionLogTenancyEntity } from '../../../database/src/entities/tenancy/transaction-action-log.tenancy.entity';
import { TransactionActionLogDto } from '../../../shared/common/dto/transaction-action-log.dto';
import { TransactionActionLogEntity } from '../../../database/src/entities/public/transaction-action-log.public.entity';
// import { TransactionActionLogTenancyEntity } from '../../../database/src/entities/tenancy/transaction-action-log.tenancy.entity';
// import { TransactionActionLogDto } from '../../../shared/common/dto/transaction-action-log.dto';
// import { TransactionActionLogEntity } from '../../../database/src/entities/public/transaction-action-log.public.entity';

@Injectable()
export class FormTransactionMappingProfile extends AutomapperProfile {
    constructor(@InjectMapper() mapper: Mapper) {
        super(mapper);
    }

    get profile(): MappingProfile {
        return (mapper: Mapper) => {
            createMap(
                mapper,
                TransactionEntity,
                FormTransactionDto,
                forMember(
                    (dest) => dest.formVersion,
                    mapFrom((source) => {
                        return source.formVersion?.version;
                    }),
                ),
                forMember(
                    (dest) => dest.rollupStatus,
                    mapFrom((source) => {
                        return source.rollupStatus?.status;
                    }),
                ),
                forMember(
                    (dest) => dest.autoCreationStatus,
                    mapFrom((source) => {
                        return source.autoCreationStatus?.status;
                    }),
                ),
                forMember(
                    (dest) => dest.isTest,
                    mapFrom((source) => {
                        return source.isTest;
                    }),
                ),
                // forMember(
                //     (dest) => dest.transactionFields,
                //     mapFrom((source) => {
                //         const views = source.dataRegisterVersion?.view;
                //         if (!views?.length) {
                //             return mapper.mapArray(
                //                 source.transactionFields || [],
                //                 DataRegisterTransactionFieldTenancyEntity,
                //                 FormTransactionFieldDto,
                //             );
                //         }
                //         const visibleFieldIds = views?.map((v) => v?.fieldId);
                //         const fields = source.transactionFields?.filter((f) => visibleFieldIds?.includes(f.fieldId));
                //         return mapper.mapArray(fields || [], DataRegisterTransactionFieldTenancyEntity, DataRegisterTransactionFieldDto);
                //     }),
                // ),
            );
            createMap(mapper, EditFormTransactionRequest, TransactionEntity);
            createMap(
                mapper,
                TransactionChangeLogTenancyEntity,
                TransactionRecordChangeLogDto,
                forMember(
                    (dest) => dest.current,
                    mapFrom((source) => {
                        return source.current;
                    }),
                ),
                forMember(
                    (dest) => dest.previous,
                    mapFrom((source) => {
                        return source.previous;
                    }),
                ),
            );
            createMap(
                mapper,
                TransactionActionLogTenancyEntity,
                TransactionActionLogDto,
                forMember(
                    (dest) => dest.actionData,
                    mapFrom((source) => {
                        return source.actionData;
                    }),
                ),
            );
            createMap(
                mapper,
                TransactionActionLogEntity,
                TransactionActionLogDto,
                forMember(
                    (dest) => dest.actionData,
                    mapFrom((source) => {
                        return source.actionData;
                    }),
                ),
                forMember(
                    (dest) => dest.actionLog,
                    mapFrom((source) => {
                        return source.actionLog;
                    }),
                ),
            );
        };
    }
}
