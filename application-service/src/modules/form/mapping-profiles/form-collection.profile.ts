import { createMap, for<PERSON><PERSON><PERSON>, map<PERSON>rom, Mapper, MappingProfile } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CollectionTransactionEntity } from '../../../database/src/entities/public/collection-transaction.public.entity';
import { CollectionTransactionTenancyEntity } from '../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { FormCollectionItemTransactionFieldDto } from '../dtos';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterAutoPopulateContextTenancyEntity } from '../../../database/src/entities/tenancy/data-register-auto-populate-contexts.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionAutoPopulateContextTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-auto-populate-contexts.tenancy.entity';

@Injectable()
export class FormCollectionProfile extends AutomapperProfile {
    constructor(@InjectMapper() mapper: Mapper) {
        super(mapper);
    }

    get profile(): MappingProfile {
        return (mapper: Mapper) => {
            createMap(
                mapper,
                CollectionTransactionEntity,
                FormCollectionItemTransactionFieldDto,
                forMember(
                    (s) => s.fieldOptionIds,
                    mapFrom((d) => (d.fieldOptionIds?.length ? d.fieldOptionIds.filter((id) => !!id) : [])),
                ),
            );
            createMap(
                mapper,
                CollectionTransactionTenancyEntity,
                FormCollectionItemTransactionFieldDto,
                forMember(
                    (s) => s.fieldOptionIds,
                    mapFrom((d) => (d.fieldOptionIds?.length ? d.fieldOptionIds.filter((id) => !!id) : [])),
                ),
            );

            createMap(
                mapper,
                DataRegisterAutoPopulateContextTenancyEntity,
                FormCollectionAutoPopulateContextTenancyEntity,
                forMember(
                    (d) => d.configuration,
                    mapFrom((s) => s.configuration),
                ),
            );

            createMap(
                mapper,
                DataRegisterAdditionalFieldTenancyEntity,
                FormCollectionAdditionalFieldTenancyEntity,
                forMember(
                    (d) => d.autoPopulateContexts,
                    mapFrom((s) =>
                        s?.autoPopulateContexts?.length
                            ? this.mapper.mapArray(
                                  s?.autoPopulateContexts,
                                  DataRegisterAutoPopulateContextTenancyEntity,
                                  FormCollectionAutoPopulateContextTenancyEntity,
                              )
                            : [],
                    ),
                ),
                forMember(
                    (d) => d.configuration,
                    mapFrom((s) => s?.configuration),
                ),
            );
        };
    }
}
