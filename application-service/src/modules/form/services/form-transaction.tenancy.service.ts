import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { DataSource, In, Repository } from 'typeorm';

import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { FormTransactionDto, FormTransactionFieldDto } from '../dtos';
import { CreateEmptyTransactionRequest, EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { JsonTree } from '@react-awesome-query-builder/core';
import { Dayjs } from 'dayjs';
import { isNil, sortBy } from 'lodash';
import { TransactionFieldContextTypeEnum } from 'src/database/src/shared/enums/transaction-field.enum';
import { validate as isUUID, v4 } from 'uuid';
import { CacheService, ClaimService, LoggerService, USER_CLAIMS, UtilsService } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { TRANSACTION_FIELD_ID } from '../../../common/src/constant/field';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { Duration } from '../../../common/src/modules/shared/types/duration';
import { DATA_PASSED_CODE, DEFAULT_STAGE_KPI_FIELD_ID, DEFAULT_TRANSACTION_FIELD_ID } from '../../../constant';
import { OBJECT_SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { StageDecisionTenancyEntity } from '../../../database/src/entities/tenancy/stage-decision.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DecisionTypeEnum } from '../../../database/src/enums';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { ManualEventSourceType } from '../../../database/src/shared/enums/form-manual-event.enum';
import { CaptureActiveFormVersionType } from '../../../database/src/shared/providers/capture-active-form-version.provider';
import { AutoPopulateDataLakeResponse } from '../../../shared/common/dto/autopopulate-datalake.dto';
import { CalculationFieldDataService } from '../../../shared/services/calculation-fields.data.service';
import { CalculationService } from '../../../shared/services/calculation.service';
import { FileService } from '../../../shared/services/file-service.service';
import { SequenceService } from '../../../shared/services/sequence.service';
import { UpdateCollectionFieldActionParam } from '../../../shared/types/auto-populate-and-check-validation';
import { executeConditions, mergeTransactionFields } from '../../../utils';
import { ExecuteDecisionRequestDto } from '../dtos/requests';
import { CalculateFieldRequest } from '../dtos/requests/calculate-field.request';
import { FilterFormTransactionRequestDto } from '../dtos/requests/filter-form-transaction.request';
import { GetListFormTransactionRequestDto } from '../dtos/requests/get-list-form-transaction.request';
import { UploadFileForRelatedFormRequest } from '../dtos/requests/transaction-field.request';
import { GetRelationTransactionResponse } from '../dtos/responses/get-relation-transaction.response';
import { ChangeTransactionStageService } from './change-transaction-stage.service';
import { ConditionalRequired, ConditionalRequireService } from './conditional-required.service';
import { AutoPopulateDataService } from './data/auto-populate.data.service';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { GetFormTransactionDataService } from './data/get-form-transaction.data.service';
import { captureCollectionItemDefaultValues } from './data/util/capture-collection-item-default-values';
import { DefaultTransactionFieldService } from './default-transaction-field.service';
import { FormEventTenancyService } from './form-event.service';
import { PopulateTransactionFieldService } from './populate-transaction-field.service';
import { ScheduleWfsKpiStatusService } from 'src/modules/bull-mq/services';

@Injectable()
export class FormTransactionTenancyService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TENANCY_REPOSITORY)
        private readonly _formRepository: Repository<FormTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly _formVersionRepository: Repository<FormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly formTransactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private readonly formFieldRepository: Repository<FormFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_DECISION_TENANCY_REPOSITORY)
        private readonly stageDecisionRepository: Repository<StageDecisionTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly stageRepository: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly transactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _autoPopulateRepository: Repository<FormAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_TENANCY_REPOSITORY)
        private readonly _stageRoleRepository: Repository<StageRoleTenancyEntity>,

        private readonly _dataService: FormTransactionDataService,
        private readonly _getTransactionDataService: GetFormTransactionDataService,
        private readonly _sequenceService: SequenceService,
        private readonly _defaultTransactionFieldService: DefaultTransactionFieldService,
        private readonly _populateTransactionFieldService: PopulateTransactionFieldService,
        private readonly _calculationService: CalculationService,
        private readonly _calculationFieldService: CalculationFieldDataService,
        private readonly _autoPopulateDataService: AutoPopulateDataService,
        private readonly _formEventService: FormEventTenancyService,
        @InjectMapper() readonly _mapper: Mapper,

        private readonly _eventDrivenService: EventDrivenService,
        private readonly _dataSourceService: DataSourceService,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
        @Inject(IStageRoleACL) private readonly _stageRoleACL: IStageRoleACLProvider,

        private _loggerService: LoggerService,
        private readonly _cacheService: CacheService,
        private readonly _changeStageService: ChangeTransactionStageService,

        private readonly _conditionalRequiredService: ConditionalRequireService,

        private readonly _scheduleWfsKpiStatusService: ScheduleWfsKpiStatusService,
    ) {}

    //#region GET
    public async getList(query: FilterFormTransactionRequestDto): Promise<PaginationResponseDto<FormTransactionDto>> {
        const result = await this._getTransactionDataService.getList({
            query,
        });

        if (query?.onlyGetTransactionIds) {
            return result;
        }

        result.data.forEach((transaction) => {
            const transactionFieldIdField = transaction.transactionFields.find((field) => field.fieldId === TRANSACTION_FIELD_ID);
            transaction.label = transactionFieldIdField?.fieldValue;
        });

        return result;
    }

    public async getListPagination(query: any): Promise<any> {
        const result = await this._getTransactionDataService.getListPagination({
            query,
        });

        result.data.forEach((transaction) => {
            const transactionFieldIdField = transaction.transactionFields.find((field) => field.fieldId === TRANSACTION_FIELD_ID);
            transaction.label = transactionFieldIdField?.fieldValue;
        });

        return result;
    }

    public async getKanbanList(query: GetListFormTransactionRequestDto): Promise<PaginationResponseDto<FormTransactionDto>> {
        const result = await this._getTransactionDataService.getKanban({
            query,
        });

        result.data.forEach((transaction) => {
            const transactionFieldIdField = transaction.transactionFields.find((field) => field.fieldId === TRANSACTION_FIELD_ID);
            transaction.label = transactionFieldIdField?.fieldValue;
        });

        return result;
    }

    public async getRelationTransactionTree(accountId: string, contextId: string): Promise<GetRelationTransactionResponse[]> {
        const result = await this._dataService.getRelationTransactionTree(accountId, contextId);

        return result;
    }

    public async get(
        id: string,
        request?: {
            includeRelatedTransactionIds: boolean;
            includeOriginTransactionIds: boolean;
            includeFieldContextTypes?: TransactionFieldContextTypeEnum[];
            isTest?: boolean;
        },
    ): Promise<FormTransactionDto> {
        const transaction = await this._getTransactionDataService.get({
            id,
            request,
        });
        if (!transaction) {
            throw new NotFoundException('transaction_not_found');
        }
        this.cacheRecentTransaction(transaction.id);
        return transaction;
    }

    public getFields(id: string, fieldIds: string[], isTest?: boolean): Promise<FormTransactionDto> {
        return this._getTransactionDataService.getFields({
            id,
            fieldIds,
            isTest,
        });
    }

    public getFieldsByFormFieldIds(id: string, fieldIds: string[]): Promise<FormTransactionDto> {
        return this._getTransactionDataService.getByFormFieldIds({
            id,
            fieldIds,
        });
    }

    public getTransactionCollectionFields(id: string, collectionId: string): Promise<FormTransactionDto> {
        return this._getTransactionDataService.getFields({
            id,
            fieldIds: [],
            collectionId,
        });
    }

    public getFieldConfiguration({ fieldIds, formVersionId }: { fieldIds: string[]; formVersionId: string }) {
        return this._getTransactionDataService._getFieldConfiguration({ fieldIds, formVersionId });
    }

    public getRelatedTransactions(id: string): Promise<FormTransactionDto[]> {
        return this._getTransactionDataService.getRelatedTransactions({
            transactionId: id,
            transactionRepo: this.formTransactionRepository,
        });
    }

    public async calculateFields(
        request: CalculateFieldRequest,
    ): Promise<Record<string, string | number | Date | Duration | Dayjs | null>> {
        if (!request?.calculationFields?.length) {
            return {};
        }

        const transaction = await this._getTransactionDataService.get({
            id: request.transactionId,
            request: {
                isTest: request.isTest,
            },
        });

        if (!transaction) {
            throw new NotFoundException('transaction_not_found');
        }

        const stackValues: Record<string, any> = {};

        //if current filed change is calculation field, ignore it in recalculating list
        //because user can change value of calculation field
        const calculatingFields = request.calculationFields.filter((f) => f.fieldId !== request.fieldChangeId);

        const priorities = this._calculationFieldService.getPriority({
            formFields: (calculatingFields || []).map((f) => ({
                configuration: {
                    calculationFormula: f.calculationFormula,
                },
                fieldId: f.fieldId,
                type: FormFieldTypeEnum.Calculation,
            })),
            fieldChangeId: request.fieldChangeId,
        });

        const sortedCalcFields = sortBy(calculatingFields || [], (field) =>
            (priorities || []).findIndex((p) => p.fieldId === field.fieldId),
        );

        //set null value for calculation field to requestValues for recalculating all calc fields
        //ignore if field change is calc field
        const requestValues = request.variableValues ?? {};
        sortedCalcFields.forEach((field) => {
            if (field.fieldId !== request.fieldChangeId) {
                requestValues[field.fieldId] = null;
            }
        });

        for (const field of sortedCalcFields) {
            const fieldStackValues: any[] = [];

            const transactionFields = this._mapper
                .mapArray(transaction.transactionFields ?? [], FormTransactionFieldDto, TransactionFieldEntity)
                ?.filter(({ inVisible }) => !inVisible);

            const formFieldIds = Object.keys(requestValues).filter(isUUID);
            const formFields = await this.formFieldRepository.find({
                where: { fieldId: In(formFieldIds), formVersionId: transaction.formVersionId },
            });

            const transactionFormFields = transactionFields.filter(({ contextType }) => contextType !== 'collection');

            Object.entries(requestValues).forEach(([key, value]) => {
                if (!isUUID(key)) return;

                const existingField = transactionFormFields.find((f) => f.fieldId === key);
                if (existingField) {
                    existingField.fieldValue = value;
                } else {
                    const fieldMeta = formFields.find((f) => f.fieldId === key);
                    transactionFormFields.push({
                        fieldId: key,
                        fieldValue: value,
                        fieldType: fieldMeta?.type,
                        fieldOptionIds: [],
                        transactionId: transaction.id,
                    });
                }
            });

            const fieldWithCvf = this._dataService.executeConditionValueField({
                transactionFields: transactionFormFields,
                formFieldEntities: formFields,
                formCollectionFieldEntities: [],
            });

            const conditionFormFields = this._dataService.mergeWithCfvFields({
                transactionFields: transactionFormFields,
                fieldWithCvf,
            });

            const visibleFormFields = conditionFormFields.filter(({ inVisible }) => !inVisible);
            const convertRequestValues = Object.fromEntries(
                Object.entries(requestValues).filter(([key]) => isUUID(key) && visibleFormFields.some(({ fieldId }) => fieldId === key)),
            );

            this._calculationService.calculateCalculationField({
                calculationField: {
                    ...field,
                    dataType: field.dataType || field.calculationFormula?.dataType,
                },
                requestValues: convertRequestValues,
                transactionFields,
                stackValues,
                calculationFormFields: sortedCalcFields,
                fieldStackValues,
            });

            const calculatedValue = stackValues?.[field.fieldId];
            if (!isNil(calculatedValue)) {
                requestValues[field.fieldId] = calculatedValue;
            }
        }

        return stackValues;
    }

    public async getStageInfo(stageId: string) {
        try {
            const stage = await this.stageRepository.findOne({
                where: {
                    id: stageId,
                },
            });
            return stage;
        } catch (error) {
            this._loggerService.error('Error in getFormUseRegister:', error);
            throw error;
        }
    }

    //#endregion GET

    //TODO: method unused, check and reopen when needed
    //#region POST
    // public async create(request: EditFormTransactionRequest): Promise<string> {
    //     const result = await this._dataService.create({
    //         request,
    //     });
    //     return result;
    // }

    public async create(
        request: CreateEmptyTransactionRequest,
        options?: {
            ignoreFirstUpdate?: boolean;
            returnTransaction?: boolean;
        },
    ) {
        try {
            const isTest = request.isTest;

            if (!request.formVersionId) {
                const { activeVersionId, latestVersionId } = await this._formRepository.findOne({
                    where: {
                        id: request.formId,
                    },
                    select: ['id', 'activeVersionId', 'latestVersionId'],
                });

                request.formVersionId = isTest ? latestVersionId : activeVersionId;
            }

            const cacheKeys = UtilsService.getActiveFormVersionCacheKeys({
                accountId: this._claims.accountId,
                formId: request.formId,
                formVersionId: request.formVersionId,
            });

            const cachedFormVersion = await this._cacheService.jsonGet<CaptureActiveFormVersionType>(cacheKeys.formVersionKey);

            const result = await this.createEmptyTransaction({
                ...request,
                isReturnTrans: true,
                skipRollup: true,
                skipPopulate: true,
                skipValidation: true,
                cachedFormVersion,
            });

            if (result instanceof TransactionEntity) {
                const newTransactionFields = result?.transactionFields || [];
                const formValues = newTransactionFields.reduce((prev, field) => {
                    if (field.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                        const collectionKey = UtilsService.combineCollectionKeys({
                            collectionIdentityId: field.collectionId,
                            collectionItemIdentityId: field.collectionItemId,
                            fieldIdentityId: field.fieldId,
                            collectionItemId: '',
                            fieldId: '',
                            collectionItemKey: field.rowKey,
                        });
                        prev[collectionKey] = field.fieldValue;
                        return prev;
                    }

                    prev[field.fieldId] = field.fieldValue;
                    return prev;
                }, {});

                if (!options?.ignoreFirstUpdate) {
                    await this.update(result.id, { ...result, formValues } as any, {
                        shouldRunPopulateFormFields: true,
                    });
                }

                if (isTest) {
                    await this.initTestTransaction(result.id, result.formVersionId);
                }

                if (request.isReturnObjectResult) {
                    return {
                        id: result.id,
                        formVersionId: result.formVersionId,
                    };
                } else if (options?.returnTransaction) {
                    return result;
                } else {
                    return result.id;
                }
            }

            if (isTest) {
                await this.initTestTransaction(result, request.formVersionId);
            }

            return result;
        } catch (error) {
            this._loggerService.error(error);
            // delete transaction was created
            throw error;
        }
    }

    private async initTestTransaction(transactionId: string, formVersionId: string) {
        await Promise.all([
            this.formTransactionRepository.update(transactionId, {
                deletedAt: new Date(),
                deletedBy: this._claims.accountId,
            }),
            this._formVersionRepository.update(formVersionId, {
                testTransactionId: transactionId,
            }),
        ]);
    }

    public async createEmptyTransaction({
        formId,
        formVersionId,
        originalTransactionId,
        timezone,
        cachedFormVersion,
        isReturnTrans = false,
        skipRollup = false,
        skipPopulate = false,
        skipValidation = false,
        skipMapDependLookupField = false,
        isTest = false,
    }: CreateEmptyTransactionRequest & { cachedFormVersion?: CaptureActiveFormVersionType }): Promise<string | TransactionEntity> {
        let versionId: string = '';
        let formVersion: FormVersionTenancyEntity = null;
        let cachedCollections: FormCollectionTenancyEntity[] = [];
        let cachedCapturedAdditionalFields: FormCollectionAdditionalFieldTenancyEntity[] = [];
        if (cachedFormVersion) {
            versionId = formVersionId;
            formVersion = cachedFormVersion;
            cachedCollections = cachedFormVersion?.formCollections || [];
            cachedCapturedAdditionalFields = cachedFormVersion?.collectionAdditionalFields || [];
        } else {
            const formEntity = await this._dataService.getFormWithActiveVersion({
                formId,
                formVersionId,
            });

            versionId = !formVersionId ? (formEntity.formVersions.length ? formEntity.formVersions[0].id : formVersionId) : formVersionId;
            formVersion = formEntity.formVersions.length ? formEntity.formVersions[0] : null;
        }

        let stageEntities: StageTenancyEntity[] = cachedFormVersion?.stages?.length
            ? cachedFormVersion.stages
            : await this.stageRepository.find({
                  where: {
                      formVersionId: versionId,
                  },
              });

        const startStage = stageEntities.find((stage) => stage.config.type === 'START');

        if (!startStage) throw new NotFoundException('start_stage_not_found');

        const transaction = new TransactionEntity();
        transaction.formId = formId;
        transaction.formVersionId = formVersionId;
        transaction.stageId = startStage.id;
        transaction.stageName = startStage.name;
        transaction.isTest = isTest;
        const transactionId = v4();
        transaction.id = transactionId;

        await this.formTransactionRepository.insert(transaction);

        try {
            let cachedFormFields: FormFieldTenancyEntity[] = cachedFormVersion?.fields || [];

            //currently, use metadata to store collections default values in transactions
            let isUpdateTransaction = false;
            const metadata: { collectionDefaultValues: Record<string, any> } = {
                collectionDefaultValues: {},
            };

            const transactionFormField = cachedFormFields?.length
                ? cachedFormFields.find((f) => f.fieldId === DEFAULT_TRANSACTION_FIELD_ID)
                : await this.formFieldRepository.findOneBy({
                      formVersionId: versionId,
                      fieldId: DEFAULT_TRANSACTION_FIELD_ID,
                  });

            const fieldTransactionId = await this.generateTransactionIdValue({
                formId,
                formField: transactionFormField,
                isTest,
            });

            let transactionIncrementField = new TransactionFieldEntity();
            transactionIncrementField.transactionId = transactionId;
            transactionIncrementField.fieldId = DEFAULT_TRANSACTION_FIELD_ID;
            transactionIncrementField.fieldValue = fieldTransactionId;

            let transactionStageKpiField = new TransactionFieldEntity();
            transactionStageKpiField.transactionId = transactionId;
            transactionStageKpiField.fieldId = DEFAULT_STAGE_KPI_FIELD_ID;
            transactionStageKpiField.fieldValue = 'OK';
            transactionStageKpiField.validationValue = DATA_PASSED_CODE;
            transactionStageKpiField.fieldType = FormFieldTypeEnum.StageKpi;

            const { defaultTransactionFields, transactionLookupFields } =
                await this._defaultTransactionFieldService.getDefaultTransactionFields({
                    transaction,
                    timezone,
                    formFields: formVersion.fields,
                    stages: stageEntities,
                });

            const { collectionTransactionFields: collectionFields, hideDefaultValuesCollectionIds } =
                await this._defaultTransactionFieldService.getDefaultCollectionFields({
                    transaction,
                    formVersionId: formVersion.id,
                    timezone,
                    stages: stageEntities,
                    cachedCapturedAdditionalFields: cachedCapturedAdditionalFields || [],
                    isTest,
                });

            let filterEmptyFormField = false; //just filter form fields
            let dfCollectionTranFields: TransactionFieldEntity[] = [];

            if (collectionFields?.length) {
                const collectionDefaultValues = captureCollectionItemDefaultValues({ collectionFields });
                metadata.collectionDefaultValues = collectionDefaultValues;
                if (hideDefaultValuesCollectionIds?.length) {
                    dfCollectionTranFields = collectionFields.filter((cf) => !hideDefaultValuesCollectionIds.includes(cf.collectionId));
                } else {
                    dfCollectionTranFields = collectionFields;
                }

                isUpdateTransaction = true;
            }

            let transactionFields = [...(defaultTransactionFields || []), ...(dfCollectionTranFields || [])];

            if (transactionLookupFields?.length) {
                let lookups = transactionLookupFields ?? [];

                if (skipMapDependLookupField) {
                    lookups = lookups.filter((lookup) => !lookup.dependFieldId);
                }

                transactionFields = mergeTransactionFields(transactionFields, lookups);
            }

            if (originalTransactionId) {
                const relatedTransactionFields = await this._createRelatedTransactions(
                    originalTransactionId,
                    transactionId,
                    formId,
                    formVersion,
                );

                if (relatedTransactionFields?.length) {
                    transactionFields = mergeTransactionFields(transactionFields, relatedTransactionFields);
                    filterEmptyFormField = true;
                }
            }

            if (!skipPopulate) {
                const populatedTransactionFields = await this._populateTransactionFieldService.populateTransactionFields(
                    {
                        formVersionFields: formVersion.fields,
                        transactionFields,
                    },
                    transactionId,
                );
                transactionFields = mergeTransactionFields(transactionFields, populatedTransactionFields);
            }

            await this.formTransactionFieldRepository.insert(
                [transactionIncrementField, transactionStageKpiField, ...(transactionFields || [])].map((f) => {
                    f.transactionId = transactionId;
                    return f;
                }),
            );

            if (!skipRollup) {
                await this._dataService.publishUpdatedFieldEventForRollup({
                    accountId: this._claims.accountId,
                    transactionId,
                    formId,
                    formVersionId: formVersion.id,
                    fields: transactionFields,
                    stageId: startStage.id,
                });
            }

            const message = EventDrivenService.createCommonEvent({
                payload: transaction,
                aggregateId: transaction.id,
                tenantId: RequestContextService.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_CREATED,
                name: TransactionEventEnum.FORM_TRANSACTION_CREATED,
            });

            this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
            if (originalTransactionId) {
                this._dataService.publishMQTTFormChangedEvent({
                    accountId: RequestContextService.accountId,
                    formId: formId,
                    transactionId: originalTransactionId,
                });
            }

            if (isUpdateTransaction) {
                await this.formTransactionRepository.update(transaction.id, { metadata });
            }

            if (transaction.id) {
                transaction.transactionFields = await this.transactionFieldRepository.find({
                    where: { transactionId },
                    select: [
                        'id',
                        'fieldId',
                        'fieldValue',
                        'fieldOptionIds',
                        'collectionId',
                        'collectionItemId',
                        'rowKey',
                        'parentId',
                        'pairId',
                        'dependFieldId',
                        'data',
                        'contextType',
                        'fieldType',
                    ],
                });

                //This is to handle the case when the fieldOptionIds is not an array -> re-assign value to fieldValue
                //Currently, just handle for form fields and collection fields cuz if existed depend field of loookup form field
                //They shouldn't be assigned to fieldValue
                transaction.transactionFields.forEach((f) => {
                    if (f.dependFieldId) return;

                    const formFields = formVersion.fields || [];
                    const formField = formFields.find((ff) => ff.fieldId === f.fieldId);
                    const fieldOptionIds = f.fieldOptionIds;

                    //form fields
                    if (formField && fieldOptionIds?.length) {
                        f.fieldValue = fieldOptionIds.length > 1 ? (fieldOptionIds as any)?.join(',') : fieldOptionIds[0];
                    }

                    //collection fields
                    if (f.collectionId && fieldOptionIds?.length) {
                        f.fieldValue = fieldOptionIds.length > 1 ? (fieldOptionIds as any)?.join(',') : fieldOptionIds[0];
                    }
                });

                if (filterEmptyFormField) {
                    transaction.transactionFields = (transaction.transactionFields || []).filter((f) => {
                        if (!f.collectionId) {
                            return f.fieldValue !== null && f.fieldValue !== undefined;
                        }
                        return true;
                    });
                }

                // CREATE SCHEDULE FOR WFS KPI STATUS
                const currentStageConfig = formVersion.stages.find((item) => item.id === transaction.stageId);
                if (currentStageConfig) {
                    const delays = UtilsService.getWfsKpiSetting(currentStageConfig.config);
                    if (delays.length) {
                        await this._scheduleWfsKpiStatusService.createJobs(
                            {
                                tenantId: RequestContextService.accountId,
                                source: 'automation',
                                transactionId: transaction.id,
                                formId: transaction.formId,
                                formVersionId: transaction.formVersionId,
                            },
                            delays,
                        );
                    }
                }
            }

            return isReturnTrans ? transaction : transactionId;
        } catch (error) {
            this._loggerService.error(error);

            // delete transaction was created
            await this.formTransactionRepository.delete(transactionId);
            throw error;
        }
    }

    public async executeDecision({
        decisionId,
        transactionId,
        formVersionId,
        stageId,
        roleId,
        browserTabId,
        isTest,
    }: ExecuteDecisionRequestDto): Promise<ConditionalRequired | boolean> {
        const decisionEntity = await this.stageDecisionRepository.findOneBy({ id: decisionId });
        if (!decisionEntity) throw new NotFoundException('decision_not_found');

        const stageRoles = await this._stageRoleRepository.findBy({ stageId: stageId, formVersionId: formVersionId });

        const decisionCondition = decisionEntity.config?.condition;
        if (decisionCondition) {
            const validateDecisionCondition = await this.validateDecisionCondition(formVersionId, transactionId, decisionCondition);
            if (!validateDecisionCondition) {
                throw new BadRequestException('decision_condition_failed');
            }
        }

        const transitionAction = (decisionEntity.config?.actions || []).find((a) => a?.decisionType === DecisionTypeEnum.Transition);

        if (transitionAction) {
            const conditionRequired = await this._conditionalRequiredService.verify({
                targetStageId: transitionAction.targetId,
                transactionId,
                isTest: !!isTest,
            });

            if (!conditionRequired.canChangeStage) {
                return conditionRequired;
            }
        }

        const tasks = [];

        decisionEntity.config?.actions?.forEach((action) => {
            switch (action.decisionType) {
                case DecisionTypeEnum.Transition:
                    if (!action.targetId) throw new BadRequestException('targetId_not_found');
                    tasks.push(
                        this._changeStageService.handleTransitionAction({
                            targetStageId: action.targetId,
                            transactionId,
                            isTest: !!isTest,
                        }),
                    );
                    break;
            }
        });

        tasks.push(
            this._formEventService.handleManualEvent({
                formVersionId: formVersionId,
                transactionId: transactionId,
                stageId: stageId,
                roleId: roleId,
                sourceId: decisionEntity.identityId,
                type: ManualEventSourceType.DecisionStage,
                validRoleIds: stageRoles?.map((role) => role.roleId),
                browserTabId,
                isTest,
            }),
        );

        await Promise.all(tasks);

        return true;
    }

    private async isAllowUploadFileRelatedForm(request: UploadFileForRelatedFormRequest, transaction: TransactionEntity) {
        try {
            const { formVersionId, documentFieldId } = request;
            const userRoles = RequestContextService.currentUser()?.roles ?? [];

            const stageRoles = await this._dataSource.getRepository(StageRoleTenancyEntity).find({
                where: {
                    formVersionId,
                    stageId: transaction.stageId,
                },
            });
            const stageRoleIds = stageRoles.map((role) => role.roleId);
            const roleId = userRoles.map((ur) => ur.id).find((urId) => stageRoleIds.includes(urId));
            const stageRoleACLs = await this._stageRoleACL.getRoleStageACLs({
                request: {
                    formVersionId: transaction.formVersionId,
                    stageRoles: [
                        {
                            roleId: roleId,
                            stageId: transaction.stageId,
                        },
                    ],
                },
                stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
                stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
                fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
            });
            const transactionAcl = (stageRoleACLs || []).find((item) => item.roleId === roleId && item.stageId === transaction.stageId);
            const fieldAcl = transactionAcl?.field ? transactionAcl.field[documentFieldId] : {};

            return !!(fieldAcl?.editable?.toString()?.toLowerCase() === 'true');
        } catch (err) {
            return false;
        }
    }

    public async uploadFileRelatedForm(request: UploadFileForRelatedFormRequest, file: Express.Multer.File) {
        const { formVersionId, formId, documentFieldId, originalTransactionId } = request;

        const formEntity = await this._dataService.getFormWithActiveVersion({
            formId: request.formId,
            formVersionId: formVersionId,
        });

        const versionId = !formVersionId ? (formEntity.formVersions.length ? formEntity.formVersions[0].id : formVersionId) : formVersionId;

        const documentFormField = await this.formFieldRepository.findOne({
            where: {
                formVersionId: versionId,
                fieldId: documentFieldId,
                type: FormFieldTypeEnum.Document,
            },
            select: ['id', 'fieldId'],
        });

        if (!documentFormField) {
            throw new NotFoundException('document_field_invalid');
        }

        let transactionId;

        try {
            const transaction = (await this.createEmptyTransaction({
                formId,
                formVersionId: versionId,
                originalTransactionId,
                timezone: request.timezone,
                isReturnTrans: true,
            })) as TransactionEntity;

            transactionId = transaction.id;

            const isAllowToUpload = await this.isAllowUploadFileRelatedForm(request, transaction);

            if (!isAllowToUpload) {
                return { message: 'not_permission_upload_file' };
            }
            const fileName = file.originalname;
            const fileBuffer = file.buffer;

            //folder: transactions/<form-id>/<transaction-id>
            const folderPath = `transactions/${formEntity.id}/${transaction.id}`;
            const _fileService = await this._dataSourceService.resolveService<FileService>(RequestContextService.accountId, FileService);
            const fileUploaded = await _fileService.uploadFile(fileName, fileBuffer, folderPath);

            const newTransactionFields = (transaction?.transactionFields || []).filter((field) => {
                if (!field.collectionId) {
                    return field.fieldValue !== null && field.fieldValue !== undefined;
                }

                return true;
            });

            const docField = await this.transactionFieldRepository.findOneBy({
                transactionId: transaction.id,
                fieldId: documentFormField.fieldId,
            });

            if (docField) {
                docField.data = {
                    filePath: fileUploaded.filePath,
                    docFieldType: 'upload',
                };
                docField.fieldType = FormFieldTypeEnum.Document;
                docField.fieldValue = fileName;
                docField.contextType = TransactionFieldContextTypeEnum.FORM;

                newTransactionFields.push(docField);
            }

            if (transaction) {
                const formValues = newTransactionFields.reduce((prev, field) => {
                    if (field.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                        const collectionKey = UtilsService.combineCollectionKeys({
                            collectionIdentityId: field.collectionId,
                            collectionItemIdentityId: field.collectionItemId,
                            fieldIdentityId: field.fieldId,
                            collectionItemId: '',
                            fieldId: '',
                            collectionItemKey: field.rowKey,
                        });
                        prev[collectionKey] = field.fieldValue;
                        return prev;
                    }

                    prev[field.fieldId] = field.fieldValue;
                    return prev;
                }, {});

                const updateRequest: EditFormTransactionRequest = {
                    transactionId: transaction.id,
                    transactionFields: newTransactionFields,
                    formId: transaction.formId,
                    formValues,
                };

                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        id: transactionId,
                        request: updateRequest,
                        user: RequestContextService.currentUser(),
                        fromRequest: RequestContextService.source === 'user',
                        option: {
                            shouldRunPopulateFormFields: true,
                        },
                    },
                    tenantId: RequestContextService.accountId,
                    aggregateId: transactionId,
                    type: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                    name: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                });

                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_TOPIC, message, transactionId);
            }

            return { message: 'success' };
        } catch (error) {
            this._loggerService.error(error);

            if (transactionId) {
                await this.formTransactionRepository.delete(transactionId as string);
            }
            throw error;
        }
    }
    //#endregion POST

    //#region PUT
    public async update(
        id: string,
        request: EditFormTransactionRequest,
        option?: {
            forceUsingTransactionFieldValues?: boolean;
            forceRunAutoPopulate?: boolean;
            shouldRunPopulateFormFields?: boolean;
            updatingCollectionFields?: Array<UpdateCollectionFieldActionParam>;
            purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>;
            isModify?: boolean;
            ignoreAutoPopulateCollection?: boolean;
        },
    ) {
        try {
            // const result = await this._dataService.update({
            //     id,
            //     request,
            //     user: RequestContextService.currentUser(),
            //     option,
            // });

            // return result;

            const message = EventDrivenService.createCommonEvent({
                payload: {
                    id,
                    request,
                    user: RequestContextService.currentUser(),
                    option,
                    fromRequest: RequestContextService.source === 'user',
                },
                aggregateId: id,
                tenantId: RequestContextService.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                name: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
            });

            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_TOPIC, message, id);

            return true;
        } catch (error) {
            this._loggerService.error(error);
        }

        return false;
    }

    //#endregion PUT

    //#region DELETE
    public async delete(id: string) {
        const result = await this._dataService.delete({
            id,
        });

        return result;
    }
    //#endregion DELETE

    //#region private methods

    public async generateTransactionIdValue({
        formField,
        formId,
        repository,
        isTest,
    }: {
        formId: string;
        formField: FormFieldTenancyEntity;
        repository?: Repository<FormFieldTenancyEntity>;
        isTest?: boolean;
    }) {
        // Ensure the sequence exists
        // Check if sequence of form are used in public schema => keep
        // Other wise recreate sequence to tenancy schema
        const sequenceName = isTest ? `form_test_seq` : `form_${formId.replaceAll('-', '_')}_seq`;
        const fieldRepo = repository ?? this.formFieldRepository;

        const _accountId = RequestContextService.accountId;
        let schema = _accountId ? _accountId : 'public';

        let isExistSequence = false;

        const isExistSequenceInPublic = await this._sequenceService.sequenceExists({
            sequenceName,
            repository: fieldRepo,
            schema: 'public',
        });

        if (isExistSequenceInPublic) {
            schema = 'public';
            isExistSequence = true;
        } else {
            isExistSequence = await this._sequenceService.sequenceExists({
                sequenceName,
                repository: fieldRepo,
                schema,
            });
        }

        if (!isExistSequence) {
            await this._sequenceService.createSequence({
                sequenceName,
                repository: fieldRepo,
                schema,
            });
        }

        const nextValue = await this._sequenceService.getNextVal({
            sequenceName,
            repository: fieldRepo,
            schema,
        });

        let transactionId = '';
        if (formField?.configuration?.prefix?.length > 0) {
            transactionId = `${formField.configuration?.prefix}-`;
        }
        transactionId += UtilsService.padNumber(nextValue, formField?.configuration?.leadingZeros ?? '');

        return transactionId;
    }

    private getRelatedFormFieldValue = async (
        originFormTransaction: FormTransactionDto,
        activeVersion: FormVersionTenancyEntity,
    ): Promise<{ fieldId: string; fieldValue: string; fieldOptionsIds: string[]; targetFieldId: string; displayValue: string }[]> => {
        const relatedFormFields: {
            fieldId: string;
            fieldValue: string;
            fieldOptionsIds: string[];
            targetFieldId: string;
            displayValue: string;
        }[] = [];

        if (!activeVersion) {
            return relatedFormFields;
        }

        if (!originFormTransaction) {
            return relatedFormFields;
        }

        const settings = await this._autoPopulateDataService.getAutoPopulateFields(
            [activeVersion.formId],
            [activeVersion.id],
            this._autoPopulateRepository,
        );

        activeVersion.fields?.forEach((field) => {
            // const autoPopulateSettings = this._autoPopulateDataService.getGeneralAutoPopulatesFromConfigs(
            //     activeVersion?.id,
            //     activeVersion?.formId,
            //     field,
            // );

            const autoPopulateSettings = settings.filter((s) => s.originFieldId === field.fieldId);
            // const autoPopulate = autoPopulateSettings?.find(
            //     (item) =>
            //         item.targetFormId === originFormTransaction.formId &&
            //         item.dataSourceType === AutoPopulateDataSourceTypeEnum.RelatedForm,
            // );

            const autoPopulate = autoPopulateSettings?.find((item) => item.targetFormId === originFormTransaction.formId);

            if (autoPopulate) {
                const tranField = originFormTransaction.transactionFields.find((item) => item.fieldId === autoPopulate.targetFieldId);
                if (!tranField) {
                    return;
                }
                relatedFormFields.push({
                    fieldId: field.fieldId,
                    fieldValue: tranField.fieldValue,
                    fieldOptionsIds: tranField.fieldOptionIds,
                    targetFieldId: autoPopulate.targetFieldId,
                    displayValue: tranField.displayValue,
                });
            }
        });

        return relatedFormFields;
    };

    private async _createRelatedTransactions(
        originalTranId: string,
        tranId: string,
        currentFormId: string,
        activeFormVersion: FormVersionTenancyEntity,
    ) {
        await this._dataService.saveRelatedTransaction(originalTranId, tranId, currentFormId);

        const originFormTransaction = await this._getTransactionDataService.get({
            id: originalTranId,
            request: {
                includeRelatedTransactionIds: false,
            },
        });

        const relatedFields = await this.getRelatedFormFieldValue(originFormTransaction, activeFormVersion);

        const fieldIds = relatedFields.map((item) => item.targetFieldId);

        const originFormFields = await this.formFieldRepository.findBy({
            fieldId: In(fieldIds),
            formVersionId: originFormTransaction?.formVersionId,
        });

        // get form fields configurations
        if (relatedFields?.length) {
            const newRelatedFields = [];
            relatedFields.forEach((item) => {
                const transactionField = new TransactionFieldEntity();
                transactionField.transactionId = tranId;
                transactionField.fieldId = item.fieldId;
                transactionField.contextType = TransactionFieldContextTypeEnum.FORM;
                const originField = originFormFields.find((f) => f.fieldId === item.targetFieldId);
                const targetField = activeFormVersion.fields?.find((f) => f.fieldId === item.fieldId);
                if (!targetField || !originField || originField?.type !== targetField?.type) {
                    return;
                }

                if (OBJECT_SELECTABLE_FIELD_TYPES.includes(targetField?.type)) {
                    if (targetField?.configuration?.mode !== originField?.configuration?.mode) {
                        return;
                    }
                    transactionField.fieldOptionIds = item.fieldOptionsIds;
                    transactionField.fieldValue = item.fieldValue;
                } else {
                    transactionField.fieldValue = item.fieldValue;
                }

                newRelatedFields.push(transactionField);

                const lookupFieldsCapture = originFormTransaction?.transactionFields?.filter((f) => f.dependFieldId === item.targetFieldId);
                lookupFieldsCapture?.forEach((f) => {
                    const tranField = this._mapper.map(f, FormTransactionFieldDto, TransactionFieldEntity);
                    tranField.transactionId = tranId;
                    tranField.dependFieldId = targetField.fieldId;

                    delete tranField.id;
                    newRelatedFields.push(tranField);
                });
            });

            return newRelatedFields;
        }
    }

    private async validateDecisionCondition(
        formVersionId: string,
        transactionId: string,
        decisionCondition: JsonTree | undefined,
    ): Promise<boolean> {
        if (!decisionCondition || !Object.keys(decisionCondition)?.length) {
            return true;
        }
        const formFields = await this.formFieldRepository.findBy({ formVersionId: formVersionId });
        if (!formFields?.length) {
            return false;
        }
        const fieldIds = formFields.map((f) => f.fieldId);
        const transactionFields = await this.transactionFieldRepository.findBy({ transactionId: transactionId });
        const watchedValues: any[] = fieldIds.map((f) => {
            const tf = transactionFields.find((tf) => tf.fieldId === f);
            if (!tf) return null;
            if (
                [FormFieldTypeEnum.Select, FormFieldTypeEnum.Lookup, FormFieldTypeEnum.UserLookup, FormFieldTypeEnum.RoleLookup].includes(
                    tf.fieldType,
                )
            ) {
                return tf.fieldOptionIds;
            }
            return tf.fieldValue;
        });
        const result = executeConditions(decisionCondition, fieldIds, watchedValues, true);
        return result;
    }
    //#endregion private methods
    public async getTransactionUseRegisterRecord(registerRecordId: string) {
        if (!registerRecordId) {
            return [];
        }
        try {
            const transactions_ = await this.transactionFieldRepository
                .createQueryBuilder('transactionField')
                .select('DISTINCT transactionField.transactionId', 'transactionId') // Use DISTINCT instead of GROUP BY
                .where('transactionField.contextType = :contextType', { contextType: TransactionFieldContextTypeEnum.FORM })
                .andWhere(`jsonb_exists("transactionField"."field_option_ids"::jsonb, :recordId)`, { recordId: registerRecordId })
                .getRawMany();
            const transactionsIds = transactions_.map((transaction) => transaction.transactionId);
            const transactions = await this.formTransactionRepository.find({
                where: {
                    id: In(transactionsIds),
                },
                order: {
                    updatedAt: 'DESC',
                },
                // relations: {
                //     transactionFields: true,
                // },
            });

            // filter transaction is END stage
            const stageIds = transactions.map((trans) => trans.stageId);
            const stages = await this.stageRepository.find({
                where: {
                    id: In(stageIds),
                },
            });
            const endStageIds = stages.filter((s) => s.config.type == 'END').map((s) => s.id);

            const filteredTransactions = transactions.filter((trans) => !endStageIds.includes(trans.stageId));

            return filteredTransactions;
        } catch (error) {
            console.log('error: ', error);
            this._loggerService.error('Error in getFormUseRegister:', error);
            throw error;
        }
    }

    public async getTransactionUseRegisterRecordInCollection(registerRecordId: string) {
        if (!registerRecordId) {
            return null;
        }
        try {
            const transactions_ = await this.transactionFieldRepository
                .createQueryBuilder('transactionField')
                .select('DISTINCT transactionField.transactionId', 'transactionId') // Use DISTINCT instead of GROUP BY
                .where('transactionField.registerRecordId = :registerRecordId', { registerRecordId })
                .getRawMany();
            const transactionIds = transactions_.map((trans) => trans.transactionId);
            const transactions = await this.formTransactionRepository.find({
                where: {
                    id: In(transactionIds),
                },
                order: {
                    updatedAt: 'DESC',
                },
            });

            // filter transaction is END stage
            const stageIds = transactions.map((trans) => trans.stageId);
            const stages = await this.stageRepository.find({
                where: {
                    id: In(stageIds),
                },
            });
            const endStageIds = stages.filter((s) => s.config.type == 'END').map((s) => s.id);

            const filteredTransactions = transactions.filter((trans) => !endStageIds.includes(trans.stageId));

            return filteredTransactions;
        } catch (error) {
            console.log('error: ', error);
            this._loggerService.error('Error in getFormUseRegister:', error);
            throw error;
        }
    }

    public async getTransactionByFormVersion(fromVersionIds: string[]) {
        try {
            const transactions = await this.formTransactionRepository.find({
                select: ['id'],
                where: {
                    formVersionId: In(fromVersionIds),
                },
            });
            return transactions;
        } catch (error) {
            this._loggerService.error('Error in getFormUseRegister:', error);
            throw error;
        }
    }

    public async cacheRecentTransaction(transactionId: string) {
        try {
            const cacheKey = `tenant:${RequestContextService.accountId}:recent_transaction_ids`;
            const now = new Date();
            const maxTransactions = 100;
            // Add transaction ID with queryTime as the score
            await this._cacheService.zAdd(cacheKey, now.getTime(), transactionId);
            // Trim to keep only the latest 'maxTransactions'
            await this._cacheService.zRemRangeByRank(cacheKey, 0, -(maxTransactions + 1));
        } catch (error) {
            this._loggerService.error('Error in cacheRecentTransaction:', error);
        }
    }

    public async getRecentTransaction() {
        try {
            const cacheKey = `tenant:${RequestContextService.accountId}:recent_transaction_ids`;
            const recentTransactionIds = await this._cacheService.zRevRange(cacheKey, 0, -1);
            return recentTransactionIds;
        } catch (error) {
            this._loggerService.error('Error in getRecentTransaction:', error);
        }
    }

    public async getOneTransactionWithFields(transactionId: string) {
        try {
            const transaction = await this.formTransactionRepository.findOne({
                where: {
                    id: transactionId,
                },
                relations: {
                    transactionFields: true,
                },
            });
            return transaction;
        } catch (error) {
            this._loggerService.error('Error in getRecentTransaction:', error);
        }
    }

    public async getTransactionFields(transactionId: string) {
        try {
            const transactionFields = await this.transactionFieldRepository.find({
                where: {
                    transactionId: transactionId,
                },
            });
            return transactionFields;
        } catch (error) {
            this._loggerService.error('Error in getRecentTransaction:', error);
        }
    }

    public async getWithFieldsByIds(transactionIds: string[]) {
        const transactions = await this.formTransactionRepository.find({
            where: {
                id: In(transactionIds),
            },
        });
        const transactionFields = await this.transactionFieldRepository.find({
            where: {
                transactionId: In(transactionIds),
            },
        });
        return { transactions, transactionFields };
    }
}
