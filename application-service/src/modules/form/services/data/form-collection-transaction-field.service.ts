import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import * as _ from 'lodash';
import { In, Repository } from 'typeorm';
import { validate } from 'uuid';
import { CacheService, ClaimService, LoggerService, USER_CLAIMS, UtilsService } from '../../../../common/src';
import { FormCollectionCacheService } from '../../../../common/src/modules/shared/services/cache/form-collection.cache.service';
import { OBJECT_SELECTABLE_FIELD_TYPES } from '../../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { CollectionTransactionEntity } from '../../../../database/src/entities/public/collection-transaction.public.entity';
import { DataRegisterAdditionalFieldEntity } from '../../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { FormCollectionItemEntity } from '../../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../../database/src/entities/public/form-collection.public.entity';
import { CollectionTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { CaptureActiveFormVersionType } from '../../../../database/src/shared/providers/capture-active-form-version.provider';
import { FormCollectionItemTransactionFieldDto } from '../../dtos';
import { EditFormTransactionFieldRequest } from '../../dtos/requests/create-form-transaction.request';
import { FormCollectionTransactionFieldRequest } from '../../dtos/requests/transaction-field.request';
import { FormatCollectionDataType } from './format/collection-field.service';
import { FormCollectionDataService } from '../form-collection.data.service';

@Injectable()
export class FormCollectionTransactionFieldDataService {
    constructor(
        @InjectMapper()
        private readonly _mapper: Mapper,
        private readonly _loggerService: LoggerService,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _formCollectionAdditionalFieldTenancyRepo: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemTenancyRepo: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTenancyRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly _dataRegisterTransactionTenancyRepo: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly _dataRegisterTransactionFieldTenancyRepo: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_TENANCY_REPOSITORY)
        private readonly _dataRegisterVersionTenancyRepo: Repository<DataRegisterVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly _dataRegisterTenancyRepo: Repository<DataRegisterTenancyEntity>,

        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,

        private readonly _cacheService: CacheService,
        private readonly _formCollectionCacheService: FormCollectionCacheService,

        private readonly _collectionDataService: FormCollectionDataService,
    ) {}

    public async getFormCollectionTransactionFields({
        query,
        formCollectionItemTransactionFieldsRepo,
        dataRegisterAdditionalFieldRepo,
    }: {
        query: FormCollectionTransactionFieldRequest;
        formCollectionRepo: Repository<FormCollectionEntity | FormCollectionTenancyEntity>;
        formCollectionItemRepo: Repository<FormCollectionItemEntity | FormCollectionItemTenancyEntity> | any;
        formCollectionItemTransactionFieldsRepo: Repository<CollectionTransactionEntity | CollectionTransactionTenancyEntity>;
        dataRegisterAdditionalFieldRepo: Repository<DataRegisterAdditionalFieldEntity | DataRegisterAdditionalFieldTenancyEntity>;
    }): Promise<FormCollectionItemTransactionFieldDto[]> {
        try {
            let formCollectionItemTransactionFields: (CollectionTransactionEntity | CollectionTransactionTenancyEntity)[] = [];

            const cachedData = await this._formCollectionCacheService.getCollectionTransactionFields<
                CollectionTransactionEntity | CollectionTransactionTenancyEntity
            >(query.formVersionId);

            if (cachedData?.length) {
                formCollectionItemTransactionFields = cachedData;
            } else {
                formCollectionItemTransactionFields = await formCollectionItemTransactionFieldsRepo.find({
                    where: {
                        formVersionId: query.formVersionId,
                    },
                });

                await this._formCollectionCacheService.setCollectionTransactionFields(
                    query.formVersionId,
                    formCollectionItemTransactionFields,
                );
            }

            const dto = this._mapper.mapArray(
                formCollectionItemTransactionFields,
                CollectionTransactionEntity,
                FormCollectionItemTransactionFieldDto,
            );

            const dataRegisterTransactionIds = _.uniq(formCollectionItemTransactionFields.map((field) => field.dataRegisterTransactionId));

            const additionFields = await dataRegisterAdditionalFieldRepo.find({
                where: {
                    transactionId: In(dataRegisterTransactionIds),
                },
                select: ['transactionId', 'configuration', 'fieldId'],
            });

            if (additionFields.length) {
                dto.forEach((value) => {
                    const configuration = additionFields?.find(
                        (field) => field.transactionId === value.dataRegisterTransactionId && field.fieldId === value.fieldId,
                    )?.configuration;

                    value.additionalFieldConfiguration = configuration ?? {};
                });
            }

            return dto;
        } catch (err) {
            this._loggerService.error(err);
            throw new InternalServerErrorException(err);
        }
    }

    /**
     * @description
     * Pre-process for collection field
     * - Collection criteria answer, question
     * - Collection select, lookup
     * - Populate data register fields to collection field
     * @param {formVersionId: string, requestTransactionFields: EditFormTransactionFieldRequest[]} param0
     * @returns {Promise<void>}
     * @memberof FormCollectionTransactionFieldDataService
     */
    public async preProcess({
        requestTransactionFields,
        formVersionId,
        cachedFormVersion,
        isTest,
    }: {
        formVersionId: string;
        requestTransactionFields: EditFormTransactionFieldRequest[];
        cachedFormVersion: CaptureActiveFormVersionType;
        isTest: boolean;
    }): Promise<FormatCollectionDataType> {
        const result: FormatCollectionDataType = {
            additionalFields: [],
            collectionItems: [],
            registers: [],
            registerTransactions: [],
            registerVersions: [],
            fieldValueRegisterVersions: [],
            fieldValueTransactions: [],
            fieldValueRegisters: [],
        };

        const lookupTransactionFieldSet: Set<string> = new Set();

        const collectionFields = (requestTransactionFields || [])
            .filter((f) => f.contextType === TransactionFieldContextTypeEnum.COLLECTION)
            .filter(Boolean);

        if (!collectionFields.length) {
            return result;
        }

        //get all form collection additional fields
        const additionalFields = isTest
            ? await this._collectionDataService.getTestAdditionalFields({
                  formVersionId,
                  formCollectionItems: [],
              })
            : cachedFormVersion?.collectionAdditionalFields?.length
              ? cachedFormVersion?.collectionAdditionalFields
              : await this._formCollectionAdditionalFieldTenancyRepo.findBy({
                    formVersionId,
                });

        const additionalFieldsMap = additionalFields.reduce((acc, af) => {
            const key = `${af.fieldId}-${af.formCollectionItemIdentityId}`;
            acc[key] = af;
            return acc;
        }, {});

        //get criteria field has type map to  lookup to get transaction id
        collectionFields.forEach((cf) => {
            const additionalField = additionalFieldsMap[`${cf.fieldId}-${cf.collectionItemId}`];

            if ([FormFieldTypeEnum.Lookup].includes(additionalField?.type)) {
                if (cf.fieldValue) {
                    let fieldValues = _.isArray(cf.fieldValue) ? cf.fieldValue : [cf.fieldValue];

                    if (typeof cf.fieldValue === 'string') {
                        fieldValues = cf.fieldValue?.split(',');
                    }

                    (fieldValues || []).forEach((fv) => lookupTransactionFieldSet.add(fv));
                }
            }
        });

        result.additionalFields = additionalFields;

        //collection select, lookup
        const selectableCollectionFields = collectionFields.filter((f) => OBJECT_SELECTABLE_FIELD_TYPES.includes(f.fieldType));

        const collectionItemIdentityIds = _.uniq(collectionFields.map((f) => f.collectionItemId).filter(Boolean));
        //get form field of populate fields: register's fields
        const { collectionItems, registerTransactions, registers, registerVersions } = await this.getCollectionItemRegisters({
            collectionItemIdentityIds,
            formVersionId,
            cachedFormVersion,
        });

        selectableCollectionFields.forEach((item) => {
            if ([FormFieldTypeEnum.Lookup].includes(item.fieldType)) {
                if (item.fieldValue) {
                    const fieldValues = _.isArray(item.fieldValue) ? item.fieldValue : [item.fieldValue];
                    (fieldValues || []).forEach((fv) => lookupTransactionFieldSet.add(fv));
                }
            }
        });

        if (lookupTransactionFieldSet.size) {
            const lookupTransactionFieldIds = Array.from(lookupTransactionFieldSet);
            const lookupCollectionFieldValues = await this.getRegisters(
                _.uniq(_.compact(lookupTransactionFieldIds).filter((fieldId) => validate(fieldId))),
            );
            result.fieldValueRegisterVersions = lookupCollectionFieldValues.registerVersions || [];
            result.fieldValueTransactions = lookupCollectionFieldValues.registerTransactions || [];
            result.fieldValueRegisters = lookupCollectionFieldValues.registers || [];
        }

        result.collectionItems = collectionItems;
        result.registerTransactions = registerTransactions;
        result.registers = registers;
        result.registerVersions = registerVersions;

        return result;
    }

    /**
     *
     * get form fields of populated fields from data register
     * @param {string[]} collectionIdentityIds
     */
    public async getCollectionItemRegisters({
        collectionItemIdentityIds,
        formVersionId,
        registerIds,
        cachedFormVersion,
    }: {
        collectionItemIdentityIds: string[];
        formVersionId: string;
        registerIds?: string[];
        cachedFormVersion?: CaptureActiveFormVersionType;
    }) {
        const result: {
            collectionItems: FormCollectionItemTenancyEntity[];
            registerTransactions: DataRegisterTransactionTenancyEntity[];
            registers: DataRegisterTenancyEntity[];
            registerVersions: DataRegisterVersionTenancyEntity[];
        } = {
            collectionItems: [],
            registerTransactions: [],
            registers: [],
            registerVersions: [],
        };

        if (!collectionItemIdentityIds.length) {
            return result;
        }

        const collectionItemIds: string[] = [];
        const collections = cachedFormVersion?.formCollections?.length
            ? cachedFormVersion?.formCollections
            : await this._formCollectionTenancyRepo.findBy({
                  formVersionId,
              });

        const cachedCollectionItems = collections?.flatMap((c) => c.formCollectionItems || []);

        const collectionIds = collections.map((c) => c.id);
        if (collectionIds.length) {
            const collectionItems = cachedCollectionItems.length
                ? cachedCollectionItems
                : await this._formCollectionItemTenancyRepo.findBy({
                      formCollectionId: In(collectionIds),
                  });

            const filterCollectionItems = collectionItems.filter((item) => collectionItemIdentityIds.includes(item.identityId));
            collectionItemIds.push(..._.compact(filterCollectionItems.map((item) => item.id)));
        }

        const collectionItems = cachedCollectionItems?.length
            ? cachedCollectionItems.filter((item) => collectionItemIds.includes(item.id))
            : await this._formCollectionItemTenancyRepo.findBy({
                  id: In(collectionItemIds),
              });

        result.collectionItems = collectionItems;

        const dataRegisterTransactionIds = _.uniq(collectionItems.map((item) => item.dataRegisterTransactionId));
        if (!dataRegisterTransactionIds.length) {
            return result;
        }

        const collectionResult = await this.getRegisters(dataRegisterTransactionIds, registerIds);

        result.registerTransactions = collectionResult.registerTransactions;
        result.registers = collectionResult.registers;
        result.registerVersions = collectionResult.registerVersions;

        return result;
    }

    public async getRegisters(
        dataRegisterTransactionIds: string[],
        registerIds?: string[],
    ): Promise<Pick<FormatCollectionDataType, 'registerTransactions' | 'registers' | 'registerVersions'>> {
        const result: Pick<FormatCollectionDataType, 'registerTransactions' | 'registers' | 'registerVersions'> = {
            registers: [],
            registerTransactions: [],
            registerVersions: [],
        };

        const registerTransactions = await this._dataRegisterTransactionTenancyRepo.find({
            where: {
                id: In(dataRegisterTransactionIds),
            },
            // relations: {
            //     transactionFields: true,
            // },
        });

        const regsiterTransactionFields = await this._dataRegisterTransactionFieldTenancyRepo.find({
            where: {
                dataRegisterTransactionId: In(dataRegisterTransactionIds),
            },
        });

        const groupByRegisterTransactionId = _.groupBy(regsiterTransactionFields, 'dataRegisterTransactionId');

        registerTransactions.forEach((item) => {
            const registerTransactionFields = groupByRegisterTransactionId[item.id] || [];
            item.transactionFields = registerTransactionFields;
        });

        result.registerTransactions = registerTransactions;

        let drIds = _.uniq(registerTransactions.map((item) => item.dataRegisterId));

        if (registerIds?.length) {
            drIds = _.uniq([...drIds, ...registerIds]);
        }

        if (!drIds.length) {
            return result;
        }

        const { registers, registerVersions } = await this.getRegisterWithVersion(drIds);

        result.registers = registers;
        result.registerVersions = registerVersions;

        return result;
    }

    public async getRegisterWithVersion(registerIds: string[]): Promise<Pick<FormatCollectionDataType, 'registers' | 'registerVersions'>> {
        const result: Pick<FormatCollectionDataType, 'registers' | 'registerVersions'> = {
            registers: [],
            registerVersions: [],
        };

        if (!registerIds.length) {
            return result;
        }

        const registers = await this._dataRegisterTenancyRepo.findBy({
            id: In(registerIds),
        });
        result.registers = registers;

        const activeVersionIds = registers.map((dr) => dr.activeVersionId).filter(Boolean);
        if (!activeVersionIds.length) {
            return result;
        }

        const registerCacheKeys = _.compact(
            registers.map(
                (dr) =>
                    UtilsService.getActiveDrVersionCacheKeys({
                        accountId: this._claims.accountId,
                        drId: dr.id,
                        drVersionId: dr.activeVersionId,
                    })?.drVersionKey,
            ),
        );

        const cachedRegisterVersions = await this._cacheService.getByKeys<DataRegisterVersionTenancyEntity>(registerCacheKeys);

        const cachedVersionIds = _.uniq(cachedRegisterVersions.map((item) => item.id));

        const notCachedVersionIds = activeVersionIds.filter((id) => !cachedVersionIds.includes(id));

        if (notCachedVersionIds.length) {
            const registerVersions = await this._dataRegisterVersionTenancyRepo.find({
                where: {
                    id: In(notCachedVersionIds),
                },
                relations: ['fields'],
            });

            result.registerVersions = [...cachedRegisterVersions, ...registerVersions];
        } else {
            result.registerVersions = cachedRegisterVersions;
        }

        return result;
    }
}
