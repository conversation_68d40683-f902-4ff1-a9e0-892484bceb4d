import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JsonItem, JsonTree } from '@react-awesome-query-builder/core';
import * as _ from 'lodash';
import { chunk, compact, isEmpty, uniq } from 'lodash';
import { DataSource, In, IsNull, Not, Repository } from 'typeorm';
import { validate as isUUID, v4, validate } from 'uuid';
import { CacheService, ClaimService, LoggerService, MqttService, USER_CLAIMS, UtilsService } from '../../../../common/src';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';
import { EXTERNAL_ORIGINAL_VERSION_ID, ORIGIN_DATA, TRANSACTION_FIELD_ID } from '../../../../common/src/constant/field';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';
import { DataFieldDto } from '../../../../common/src/modules/shared/dtos/data-field.dto';
import { FormulaDto } from '../../../../common/src/modules/shared/dtos/formula.dto';
import { DurationFormatEnum } from '../../../../common/src/modules/shared/enums/duration-format-type.enum';
import { ActionEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/action-event.enum';
import { TransactionEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { TransactionTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { WidgetMQTTTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/widget-event-topic.enum';
import { CalculationFormula, FormulaSettingType, VariableMapping } from '../../../../common/src/modules/shared/formula-calculator/types';
import { FormulaService } from '../../../../common/src/modules/shared/services/formula.service';
import { Duration } from '../../../../common/src/modules/shared/types/duration';
import { EVENT } from '../../../../constant/event';
import { AUTO_POPULATE_USER_ID, AUTOMATION_USER_ID } from '../../../../database/src/constants/change-log';
import { TENANT_HEADER } from '../../../../database/src/constants/database-option.constant';
import { OBJECT_SELECTABLE_FIELD_TYPES, SELECTABLE_FIELD_TYPES } from '../../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { FormCollectionItemEntity } from '../../../../database/src/entities/public/form-collection-item.public.entity';
import { FormFieldEntity } from '../../../../database/src/entities/public/form-field.public.entity';
import { FormVersionEntity } from '../../../../database/src/entities/public/form-version.public.entity';
import { TransactionChangeLogEntity } from '../../../../database/src/entities/public/transaction-change-log.public.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { RelationTransactionEntity } from '../../../../database/src/entities/tenancy/relation-transaction.tenancy.entity';
import { RoleTenancyEntity } from '../../../../database/src/entities/tenancy/role.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionChangeLogTenancyEntity } from '../../../../database/src/entities/tenancy/transaction-change-log.tenancy.entity';
import { TransactionFieldOverrideEntity } from '../../../../database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { TransactionFieldStyleEntity } from '../../../../database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { UserTenancyEntity } from '../../../../database/src/entities/tenancy/user.tenancy.entity';
import { ChangeLogActionType, SourceOfChangeType, TransactionDataType } from '../../../../database/src/shared/enums/change-log.type.enum';
import { DataRegisterTypeEnum } from '../../../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { OverrideStatusEnum } from '../../../../database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from '../../../../database/src/shared/enums/override-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { CaptureActiveFormVersionType } from '../../../../database/src/shared/providers/capture-active-form-version.provider';
import { DataValidationResult } from '../../../../shared/enums/data-validation-result.enum';
import { FileTypeEnum } from '../../../../shared/enums/file-type.enum';
import { GeneralAutoPopulateService } from '../../../../shared/services';
import { AutoPopulateAndCheckValidationCollectionFieldService } from '../../../../shared/services/auto-populate-check-validation-collection-field.service';
import { CalculationService } from '../../../../shared/services/calculation.service';
import {
    AfterPopulateCollectionResult,
    AutoPopulateAndCheckValidation,
    AutoPopulateFormFieldParam,
    AutoPopulateFormFieldsResult,
    CheckValidationPayload,
    ProcessPopulateCollectionResult,
    UpdateCollectionFieldActionParam,
    UpdateFieldActionParam,
    UpdatePopulateCollectionParam,
} from '../../../../shared/types/auto-populate-and-check-validation';
import { FieldConfiguration } from '../../../../types';
import {
    addSystemOverrideRecords,
    executeConditions,
    getOverrideRecord,
    getRelatedFields,
    updateActiveOverrideRecords,
} from '../../../../utils';
import { StyleConditionType } from '../../../../utils/get-style-conditions';
import { TransactionRecordChangeLogDto } from '../../../data-register/dtos/data-register-transaction-change-log.dto';
import { CheckValidationResponseType, FormRuleType } from '../../../validation/types';
import { TransactionRecordChangeLogMessageDto } from '../../../worker/dtos/data-register.job.dto';
import { PopulateCollectionField } from '../../dtos/populate-collection-field.dto';
import { CheckCriteriaValidationRequest } from '../../dtos/requests';
// import { AutoCreationEventDto } from '../../dtos/requests/auto-creation.request';
import { CoreConfig, Utils as QbUtils } from '@react-awesome-query-builder/core';
import { LogicEngine } from 'json-logic-engine';
import {
    softDeleteRelationTransactionQuery,
    softDeleteTransactionFieldOverridesQuery,
    softDeleteTransactionFieldsQuery,
    softDeleteTransactionFieldStylesQuery,
} from 'src/raw-queries/soft-delete-transaction-fields.query';
import { getRelationTransactionFromOriginQuery } from '../../../../raw-queries/get-relation-transactions-from-origin.query';
import { getRelationTransactionFromTargetQuery } from '../../../../raw-queries/get-relation-transactions-from-target.query';
import { AutoPopulateDataLakeResponse } from '../../../../shared/common/dto/autopopulate-datalake.dto';
import { formatCondition } from '../../../../utils/formatConditional';
import { EditFormTransactionFieldRequest, EditFormTransactionRequest } from '../../dtos/requests/create-form-transaction.request';
import { RelatedFieldChangeEventDto } from '../../dtos/requests/related-field-change-event.dto';
import { GetRelationTransactionResponse } from '../../dtos/responses/get-relation-transaction.response';
import { CheckCriteriaValidationService } from '../check-criteria-validation.service';
import { AutoPopulateDataService } from './auto-populate.data.service';
import { FormCollectionTransactionFieldDataService } from './form-collection-transaction-field.service';
import { FormatTransactionFieldService } from './format/transaction-field.service';
import { RelatedLookupDataService } from './related-lookup-data.service';
import { JsonLogicUtils } from './util/run-json-logic.util';
import { StyleAndOverrideFields } from 'src/modules/data-register/dtos/requests/create-data-register-transaction.request';
import { UpdateTransactionOption } from './types/update-transaction-option';
import { StageAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';

const PREVIOUS_STAGE = 'PREVIOUS_STAGE';
@Injectable()
export class FormTransactionDataService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,
        private readonly _formulaService: FormulaService,
        private readonly _eventEmitter: EventEmitter2,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly stageRepo: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TENANCY_REPOSITORY)
        private readonly formRepository: Repository<FormTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly formVersionRepository: Repository<FormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private readonly formRelationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly transactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_STYLE_REPOSITORY)
        private readonly transactionFieldStyleRepository: Repository<TransactionFieldStyleEntity>,

        @Inject(PROVIDER_KEYS.USER_TENANCY_REPOSITORY)
        private readonly userTenancyRepository: Repository<UserTenancyEntity>,

        @Inject(PROVIDER_KEYS.ROLE_TENANCY_REPOSITORY)
        private readonly roleTenancyRepository: Repository<RoleTenancyEntity>,

        private readonly _calculationService: CalculationService,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private readonly _relationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionRepository: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY)
        private readonly _stageAccessControlRepository: Repository<StageAccessControlTenancyEntity>,

        private readonly _checkCriteriaValidationService: CheckCriteriaValidationService,

        private readonly _autoPopulateDataService: AutoPopulateDataService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly _transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>,

        private readonly _formatTransactionFieldService: FormatTransactionFieldService,
        private readonly _formCollectionTransactionFieldService: FormCollectionTransactionFieldDataService,

        @Inject(PROVIDER_KEYS.AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _autoPopulateRepository: Repository<FormAutoPopulateSettingTenancyEntity>,

        private readonly _relatedLookupDataService: RelatedLookupDataService,
        @Inject(forwardRef(() => AutoPopulateAndCheckValidationCollectionFieldService))
        private readonly _autoPopulateAndCheckValidationCollectionFieldService: AutoPopulateAndCheckValidationCollectionFieldService,

        private readonly _generalAutoPopulateService: GeneralAutoPopulateService,

        private readonly _eventDrivenService: EventDrivenService,
        private readonly _moduleRef: ModuleRef,
        private readonly _cacheService: CacheService,
        private readonly _mqttService: MqttService,
    ) {}

    private async _getDataSource(accountId?: string | null): Promise<DataSource> {
        if (accountId) {
            const contextId = ContextIdFactory.create();
            this._moduleRef.registerRequestByContextId(
                {
                    headers: {
                        [TENANT_HEADER]: accountId,
                    },
                },
                contextId,
            );
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.TENANT_CONNECTION, contextId, { strict: false });
        } else {
            const contextId = ContextIdFactory.create();
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.DATA_SOURCE, contextId, { strict: false });
        }
    }

    public _getRepositories(dataSource: DataSource, accountId?: string | null) {
        if (accountId) {
            return {
                transChangeLogRepo: dataSource.getRepository(TransactionChangeLogTenancyEntity),
            };
        } else {
            return {
                transChangeLogRepo: dataSource.getRepository(TransactionChangeLogEntity),
            };
        }
    }

    //#region GET
    public async getByFormVersionId({ formVersionId }: { formVersionId: string }) {
        const transactions = await this.formTransactionRepository.findOne({
            where: {
                formVersionId: formVersionId,
            },
            relations: ['transactionFields'],
        });
        return transactions;
    }

    public async getById(id: string, relations: string[] = []) {
        const transactions = await this.formTransactionRepository.findOne({
            where: {
                id: id,
            },
            relations,
        });
        return transactions;
    }

    //#endregion GET

    //#region POST
    public async handleTransitionAction(props: { transactionId: string; targetStage: string; isTest?: boolean }) {
        const { targetStage, transactionId, isTest } = props;
        const transaction = await this.formTransactionRepository.findOne({
            where: { id: transactionId },
            withDeleted: !!isTest,
        });
        const stage = await this.stageRepo.findOneBy({ id: targetStage });

        if (!transaction) {
            throw new NotFoundException('transaction_not_found');
        }

        switch (targetStage) {
            case PREVIOUS_STAGE:
                const previousStageId = transaction.previousStageId;
                if (!previousStageId) {
                    throw new BadRequestException('previous_stage_not_found');
                }

                const previousStage = await this.stageRepo.findOneBy({ id: previousStageId });
                transaction.previousStageId = transaction.stageId;
                transaction.previousStageName = transaction.stageName;

                transaction.stageId = previousStage.id;
                transaction.stageName = previousStage.name;
                break;

            default:
                if (targetStage === transaction.stageId) {
                    throw new BadRequestException('already_in_this_stage');
                }

                transaction.previousStageName = transaction.stageName;
                transaction.previousStageId = transaction.stageId;

                transaction.stageId = stage.id;
                transaction.stageName = stage.name;
                break;
        }

        try {
            const result = await this.formTransactionRepository.save(transaction);

            //TODO: Remove this after calling via API
            const decisionStageKey = `decision_stage_${transaction.id}`;
            await this._cacheService.delete(decisionStageKey);
            return result;
        } catch (err) {
            this._logger.error(err);
            return false;
        }
    }

    async getRelationTransactionTree(accountId: string, targetTransactionId: string): Promise<GetRelationTransactionResponse[]> {
        const queryOrigin = getRelationTransactionFromOriginQuery(accountId);
        const resultOrigin = await this._relationTransactionRepository.query(queryOrigin, [targetTransactionId]);

        const queryTarget = getRelationTransactionFromTargetQuery(accountId);
        const resultTarget = await this._relationTransactionRepository.query(queryTarget, [targetTransactionId]);

        const mappedResult: GetRelationTransactionResponse[] = [...resultOrigin, ...resultTarget].map(
            (item: { id: string; form_id: string; form_version_id: string }) => {
                return {
                    id: item.id,
                    formId: item.form_id,
                    formVersionId: item.form_version_id,
                };
            },
        );
        return mappedResult;
    }

    //#endregion POST

    //#region PUT

    /**
     * Formats the field value based on display attribute fields
     * @param field The field to format
     * @param transactionFields The transaction fields to use for formatting
     * @returns The formatted field value or empty string if no valid values found
     */
    private _formatFieldValueWithDisplayAttributes(
        field: { displayAttributeFieldIds?: string[]; fieldValue?: string },
        transactionFields: TransactionFieldEntity[],
    ): string {
        // Early return if no display attributes or transaction fields
        if (!field?.displayAttributeFieldIds?.length || !transactionFields?.length) {
            return field?.fieldValue || '';
        }

        // Create a map for O(1) lookup of field values
        const fieldValueMap = new Map(
            transactionFields.filter((tf) => tf?.fieldId && tf?.fieldValue).map((tf) => [tf.fieldId, tf.fieldValue]),
        );

        // Get values in the order of displayAttributeFieldIds
        const values = field.displayAttributeFieldIds.map((fieldId) => fieldValueMap.get(fieldId)).filter(Boolean);

        // Return empty string if no valid values found
        return values.length ? values.join(' - ') : '';
    }

    private async _updateRelatedDataRegisterFields({
        reqTransactionFields,
        relatedLookupDRTransactions,
        transactionId,
        captureExistLookupField,
        updateFields,
    }: {
        reqTransactionFields: EditFormTransactionFieldRequest[];
        relatedLookupDRTransactions: DataRegisterTransactionTenancyEntity[];
        transactionId: string;
        captureExistLookupField?: boolean;
        updateFields?: TransactionFieldEntity[];
    }) {
        try {
            console.time('updateRelatedDataRegisterFields');
            const lookupFields = reqTransactionFields.filter((f) => f.fieldType === FormFieldTypeEnum.Lookup);
            if (!lookupFields?.length) return;
            const [hasValue, noValue] = _.partition(lookupFields, (f) => !!f.fieldValue);

            const hasValueIds = hasValue.map((f) => f.fieldId);
            const noValueIds = noValue.map((f) => f.fieldId);

            const deleteTasks = [];

            if (noValueIds.length) {
                deleteTasks.push(
                    this.transactionFieldRepository.softDelete({
                        dependFieldId: In(noValueIds),
                        sourceFieldId: IsNull(),
                        transactionId: transactionId,
                    }),
                );
            }

            const updateTasks = [];

            if (hasValueIds.length) {
                const existTransactionFields = await this.transactionFieldRepository.find({
                    where: {
                        dependFieldId: In(hasValueIds),
                        sourceFieldId: IsNull(),
                        transactionId: transactionId,
                    },
                    select: ['id', 'fieldId', 'fieldOptionIds', 'dependFieldId'],
                });

                const existTransactionFieldsMap = _.groupBy(existTransactionFields, 'dependFieldId');

                hasValue.forEach((f) => {
                    const fieldOptionIds = typeof f.fieldValue === 'string' ? f.fieldValue.split(',') : f.fieldValue;

                    const existFields = existTransactionFieldsMap?.[f.fieldId] ?? [];

                    const [updateTransactionFields, deleteTransactionFields] = existFields.reduce(
                        (acc, field) => {
                            fieldOptionIds.includes(field.fieldOptionIds?.[0]) ? acc?.[0].push(field) : acc?.[1].push(field);
                            return acc;
                        },
                        [[], []],
                    );

                    let transactionFields = this._formatTransactionFieldService.captureDataRegisterFieldsForLookup({
                        selectMode: fieldOptionIds?.length > 1 ? 'multiple' : 'single',
                        lookupFieldId: f.fieldId,
                        dataRegisterTransactions: relatedLookupDRTransactions,
                        transactionId: transactionId,
                        fieldValue: f.fieldValue,
                    });

                    const existFieldMap = new Map(updateTransactionFields.map((f) => [`${f.fieldId}-${f.fieldOptionIds[0]}`, f]));
                    if (!captureExistLookupField) {
                        transactionFields = transactionFields.filter((transField) => {
                            const key = `${transField.fieldId}-${transField.fieldOptionIds[0]}`;
                            return !existFieldMap.has(key);
                        });
                    } else {
                        transactionFields.forEach((transField) => {
                            const key = `${transField.fieldId}-${transField.fieldOptionIds[0]}`;
                            if (existFieldMap.has(key)) {
                                transField.id = existFieldMap.get(key).id;
                            }
                        });

                        if (f.displayAttributeFieldIds?.length && transactionFields?.length && updateFields?.length) {
                            const newFieldValue = this._formatFieldValueWithDisplayAttributes(f, transactionFields);

                            for (const transactionField of updateFields) {
                                if (transactionField.fieldId === f.fieldId) {
                                    transactionField.fieldValue = newFieldValue;
                                    break;
                                }
                            }
                        }
                    }

                    transactionFields = transactionFields.filter((f) => !_.isEmpty(f.fieldValue));

                    updateTasks.push(this.transactionFieldRepository.upsert(transactionFields, ['id']));

                    if (deleteTransactionFields?.length > 0) {
                        deleteTasks.push(
                            this.transactionFieldRepository.softDelete({
                                id: In(deleteTransactionFields.map((transField) => transField.id)),
                            }),
                        );
                    }
                });
            }

            if (!deleteTasks.length && !updateTasks.length) {
                return;
            }

            await Promise.all([...deleteTasks, ...updateTasks]);
            console.timeEnd('updateRelatedDataRegisterFields');
        } catch (e) {
            this._logger.error(e);
        }
    }

    public async update({
        id,
        request,
        user,
        option,
    }: {
        id: string;
        request: EditFormTransactionRequest;
        user?: any;
        // option?: {
        //     forceUsingTransactionFieldValues?: boolean; // If true, ignore field values from request
        //     onlyUpdateIfRelevantRegisterRecordIds?: string[]; // Contains list of register record IDs that need for refresh worker
        //     updatingFormFields?: Array<UpdateFieldActionParam>; // Fields of form transaction that need to be used to format transaction fields
        //     updatingCollectionFields?: Array<UpdateCollectionFieldActionParam>; // Collection field of transaction that need to be used to format transaction fields
        //     purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>; // Additional data for PurpleTRAC

        //     isModify?: boolean; // TODO: need to be remove, unused in current code base
        //     captureExistLookupField?: boolean;  // If false filter out lookup captured fields
        //     targetFieldChangeIds?: string[]; // Contains list of field IDs that need to be refreshed
        //     targetFieldChangeFromRegisterId?: string; //support to determine which register targetFieldChangeIds come from

        //     forceRunAutoPopulate?: boolean; // If true, force run auto populate without field changed
        //     shouldRunPopulateFormFields?: boolean; // If true, run auto populate for form fields
        //     ignoreAutoPopulateCollection?: boolean; //!if set this option true, please make sure not forceRunAutoPopulate
        // };
        option?: UpdateTransactionOption;
    }) {
        try {
            const isTest = !!request.isTest;

            console.time('getTransaction');
            const transaction = await this._getTransaction(id, isTest);

            console.timeEnd('getTransaction');
            if (!transaction) {
                throw new NotFoundException('transaction_not_found');
            }

            const cacheKey = UtilsService.getActiveFormVersionCacheKeys({
                accountId: this._claims.accountId,
                formId: request.formId,
                formVersionId: transaction.formVersionId,
            });

            let cachedFormVersion: CaptureActiveFormVersionType;

            if (cacheKey) {
                cachedFormVersion = await this._cacheService.jsonGet<CaptureActiveFormVersionType>(cacheKey.formVersionKey);
            }

            const formVersion =
                cachedFormVersion ||
                (
                    await this.getFormWithActiveVersion({
                        formId: request.formId,
                        formVersionId: transaction.formVersionId,
                    })
                )?.formVersions?.[0];

            if (!formVersion) {
                throw new BadRequestException('form_not_found');
            }

            // Get stage access controls
            const stageAccessControls = formVersion?.stageAccessControls
                ? formVersion?.stageAccessControls?.filter((f) => f.stageId === transaction.stageId) || []
                : (await this._stageAccessControlRepository.find({
                      where: {
                          formVersionId: transaction.formVersionId,
                          stageId: transaction.stageId,
                      },
                  })) || [];
            const lockedFieldIds = stageAccessControls.filter((control) => control.config?.locked).map((control) => control.targetId);

            //filter transaction id field
            request.transactionFields = (request.transactionFields || []).filter((f) => f.fieldId !== TRANSACTION_FIELD_ID);

            // Filter out locked fields from the request
            if (stageAccessControls?.length && Array.isArray(request.transactionFields)) {
                const filterLockFields: typeof request.transactionFields = [];
                request.transactionFields?.forEach((field) => {
                    const fieldControl = stageAccessControls.find((control) => control.targetId === field.fieldId) as any;
                    if (fieldControl?.config?.locked?.toString() === 'true') {
                        // locked
                        return;
                    }
                    filterLockFields.push(field);
                });

                request.transactionFields = filterLockFields;
            }

            const formCollections =
                formVersion?.formCollections ||
                (await this._formCollectionRepository.find({
                    where: {
                        formVersionId: transaction.formVersionId,
                    },
                    relations: ['formCollectionItems'],
                }));

            transaction.formId = formVersion.formId;
            transaction.formVersionId = formVersion.id;

            let transactionFields = transaction.transactionFields || [];
            const cloneTransactionFields = _.cloneDeep(transactionFields);
            let requestTransactionFields = request.transactionFields || [];
            const metadata = transaction.metadata ?? {};
            const defaultCollectionValuesOnCreation = metadata.collectionDefaultValues ?? {};

            if (!request.payloadDocuments) {
                const externalOriginReportField = cloneTransactionFields.find((f) => f.fieldId === EXTERNAL_ORIGINAL_VERSION_ID);
                const payloadDocuments = externalOriginReportField?.data?.[ORIGIN_DATA.REPORT];
                request.payloadDocuments = payloadDocuments;
            }

            // Auto populate for form fields
            let cloneFieldAfterAutoPopulate: TransactionFieldEntity[] = cloneTransactionFields;
            let populatedFormFieldIds: string[] = [];

            let styleAndOverrideFields: StyleAndOverrideFields = {};

            console.time('runAutoPopulateFormFields');
            if (option?.shouldRunPopulateFormFields) {
                const {
                    updatedRequestTransactionFields,
                    updatedTransactionFields,
                    formValues,
                    isChanged,
                    populatedFormFieldIds: newPopulatedFormFieldIds,
                    styleAndOverrideFields: newStyleAndOverrideFields,
                } = await this.runAutoPopulateForFormFields({
                    requestTransactionFields: _.cloneDeep(requestTransactionFields),
                    cloneTransactionFields: _.cloneDeep(cloneTransactionFields),
                    transaction,
                    formVersion,
                    transactionFields: _.cloneDeep(transactionFields),
                    formValues: _.cloneDeep(request.formValues),
                    updatingFormFields: option?.updatingFormFields,
                    purpleTRACPopulatedData: option?.purpleTRACPopulatedData,
                    forceRun: option?.forceRunAutoPopulate,
                    payloadDocuments: _.cloneDeep(request.payloadDocuments),
                    targetFieldChangeIds: option?.targetFieldChangeIds,
                    targetFieldChangeFromRegisterId: option?.targetFieldChangeFromRegisterId,
                });

                styleAndOverrideFields = {
                    ...(newStyleAndOverrideFields ?? {}),
                };

                if (isChanged) {
                    cloneFieldAfterAutoPopulate = _.cloneDeep(updatedTransactionFields);

                    request.formValues = formValues;
                    requestTransactionFields = updatedRequestTransactionFields.map((f) => {
                        if (OBJECT_SELECTABLE_FIELD_TYPES.includes(f.fieldType)) {
                            f.fieldValue = f.fieldOptionIds?.join(',') || f.fieldValue;
                        }
                        return f;
                    });
                }

                if (newPopulatedFormFieldIds?.length) {
                    populatedFormFieldIds = newPopulatedFormFieldIds;
                }
            }
            console.timeEnd('runAutoPopulateFormFields');

            console.time('runAutoPopulateAndValidation');

            let populatedTranFields: TransactionFieldEntity[] = cloneFieldAfterAutoPopulate;
            let populateResult;

            if (!option?.ignoreAutoPopulateCollection) {
                populateResult = await this.runAutoPopulateForCollectionFields({
                    formVersionId: formVersion.id,
                    transactionId: transaction.id,
                    reqTransactionFields: requestTransactionFields,
                    transactionFields: cloneFieldAfterAutoPopulate,
                    formValues: request.formValues,
                    forceRun: option?.forceRunAutoPopulate,
                    lockedFieldIds,
                    onlyUpdateIfRelevantRegisterRecordIds: option?.onlyUpdateIfRelevantRegisterRecordIds,
                    cachedFormVersion,
                    updatingCollectionFields: option?.updatingCollectionFields,
                    isModify: option?.isModify,
                    targetFieldChangeIds: option?.targetFieldChangeIds,
                    targetFieldChangeFromRegisterId: option?.targetFieldChangeFromRegisterId,
                    purpleTRACPopulatedData: option?.purpleTRACPopulatedData ?? [],
                    payloadDocuments: request.payloadDocuments,
                    defaultCollectionValuesOnCreation,
                    isTest,
                });

                console.timeEnd('runAutoPopulateAndValidation');

                if (populateResult?.styleAndOverrideFields) {
                    styleAndOverrideFields = {
                        ...(styleAndOverrideFields ?? {}),
                        ...(populateResult?.styleAndOverrideFields ?? {}),
                    };
                }

                console.time('reGetTransactionFields');
                populatedTranFields = populateResult?.updatedTransactionFields || [];

                if (populateResult?.deletedCollectionFieldIds?.size) {
                    requestTransactionFields = (requestTransactionFields || []).filter((f) => {
                        if (!f.rowKey) return true;
                        const key = `${f.collectionId}_${f.collectionItemId}_${f.rowKey}_${f.fieldId}`;
                        return !populateResult.deletedCollectionFieldIds.has(key);
                    });
                }

                if (populateResult?.collectionChangeFields?.length) {
                    const requestTransactionFieldsMap = (requestTransactionFields || []).reduce((acc, field) => {
                        const key = this._formatTransactionFieldService.getTransactionFieldKey(field);
                        acc.set(key, field);
                        return acc;
                    }, new Map());

                    for (const field of populateResult.collectionChangeFields) {
                        const key = this._formatTransactionFieldService.getTransactionFieldKey(field);
                        const existedRequestField = requestTransactionFieldsMap.get(key);
                        if (existedRequestField) {
                            existedRequestField.fieldValue = field.fieldValue;
                            existedRequestField.fieldOptionIds = field.fieldOptionIds;
                            existedRequestField.registerRecordId = field.registerRecordId;
                        } else {
                            requestTransactionFields.push(field);
                        }
                    }
                }

                if (populatedTranFields?.length) {
                    transactionFields = populatedTranFields;
                    request.formValues =
                        await this._autoPopulateAndCheckValidationCollectionFieldService.convertTransactionFieldsToFormValues({
                            transactionId: transaction.id,
                            transactionFieldParam: transactionFields,
                        });
                }

                console.timeEnd('reGetTransactionFields');
            }

            await this.copyOverrideRecords({
                styleAndOverrideFields,
                transaction,
                transactionFieldOverrideRepository: this._transactionFieldOverrideRepo,
            });

            const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum; border?: number }> = {};

            transactionFields.forEach((field) => {
                originTransactionFieldOverrides[field.id] = {
                    value: field.validationValue,
                    type: OverrideTypeEnum.System,
                    border: field.transactionFieldStyle?.configuration?.border,
                };
            });

            const { combinedTranFields, externalDataDict, currentFieldDict, shouldCheckValidationRows } = this._mergeTransactionFields({
                currentTranFields: transactionFields,
                requestTranFields: requestTransactionFields,
                option,
            });

            console.time('getRelatedLookupData');
            const relatedLookupData = await this._relatedLookupDataService.getRelatedLookupData({
                formVersion,
                allTransactionFields: combinedTranFields,
            });
            console.timeEnd('getRelatedLookupData');

            console.time('preProcessDataFields');
            await this.preProcessDataFields(formVersion.fields, requestTransactionFields, relatedLookupData);
            console.timeEnd('preProcessDataFields');

            //!must: format value of select/lookup field of collection
            console.time('preProcessCollectionData');
            const relatedCollectionData = await this._formCollectionTransactionFieldService.preProcess({
                requestTransactionFields: combinedTranFields,
                formVersionId: transaction.formVersionId,
                cachedFormVersion,
                isTest,
            });
            console.timeEnd('preProcessCollectionData');

            // formatUpdatedFields does not format value of select/lookup field of collection
            let updateFields = this._formatTransactionFieldService.formatUpdatedFields(
                requestTransactionFields,
                transactionFields,
                transaction,
                null,
                formVersion,
                relatedLookupData,
                relatedCollectionData,
            );

            let formCollectionItems: FormCollectionItemTenancyEntity[] = (formCollections || []).flatMap(
                (fc) => fc.formCollectionItems || [],
            );

            let validationResult = this.validateTransactionData(formVersion.fields, combinedTranFields, relatedLookupData);

            const _mappingTransactionFields = transactionFields;
            const updateFieldsMap = new Map();

            updateFields.forEach((field) => {
                const key = this._formatTransactionFieldService.getTransactionFieldKey(field);
                if (!currentFieldDict.has(key)) {
                    _mappingTransactionFields.push(field);
                } else {
                    let existedField = currentFieldDict.get(key);
                    existedField.fieldValue = field.fieldValue;
                    existedField.fieldOptionIds = field.fieldOptionIds;
                }
                updateFieldsMap.set(key, field);
                return;
            });

            const fieldWithCvf = this.executeConditionValueField({
                formFieldEntities: formVersion.fields,
                transactionFields: _mappingTransactionFields,
                formCollectionFieldEntities: formCollectionItems,
            });

            updateFields = this.mergeWithCfvFields({
                transactionFields: updateFields,
                fieldWithCvf: fieldWithCvf,
            });

            const fieldStyles = this.getFieldStyles(formVersion.fields, updateFields, combinedTranFields, relatedLookupData);

            console.time('getCollectionStyles');
            let collectionStyleDict = new Map();

            //transactionFields were updated values corresponding to request fields so can be used to run validation
            const shouldValidationCollectionFields = (transactionFields || []).filter((tf) => shouldCheckValidationRows.has(tf.rowKey));
            if (shouldValidationCollectionFields?.length || option?.forceRunAutoPopulate) {
                const styleRequest: CheckValidationPayload = {
                    activeFormVersionId: formVersion.id,
                    transactionId: transaction.id,
                    ignoreSave: true,
                    formValues: request.formValues,
                    lockedFieldIds,
                    transactionFields: shouldValidationCollectionFields,
                    cachedFormVersion,
                    isTest,
                };

                const shouldValidationCollectionFieldsMap = shouldValidationCollectionFields.reduce((prev, curr) => {
                    const key = this._formatTransactionFieldService.getTransactionCollectionFieldKey({
                        collectionItemId: curr.collectionItemId,
                        rowKey: curr.rowKey,
                        fieldId: curr.fieldId,
                    });
                    key && prev.set(key, curr);
                    return prev;
                }, new Map());

                const checkValidationResult =
                    await this._autoPopulateAndCheckValidationCollectionFieldService.checkValidation(styleRequest);

                const collectionStyles = (checkValidationResult?.collectionValidation || []).map((c) => {
                    const key = this._formatTransactionFieldService.getTransactionCollectionFieldKey({
                        collectionItemId: c.collectionItemId,
                        rowKey: c.rowKey,
                        fieldId: c.fieldId,
                    });
                    const tranField = shouldValidationCollectionFieldsMap.get(key);
                    if (tranField) {
                        if (updateFieldsMap.has(key)) {
                            updateFieldsMap.get(key).validationValue = c.validationValue;
                        } else {
                            tranField.validationValue = c.validationValue;
                            updateFields.push(tranField);
                            updateFieldsMap.set(key, tranField);
                        }
                    }

                    return {
                        code: c.validationValue,
                        label: c.label,
                        rowKey: c.rowKey,
                        collectionId: c.collectionId,
                        collectionItemIdentityId: c.collectionItemId,
                        fieldId: c.fieldId,
                    };
                });

                validationResult.push(...collectionStyles);

                collectionStyleDict = collectionStyles.reduce((prev, curr) => {
                    const key = this._formatTransactionFieldService.getTransactionCollectionFieldKey({
                        collectionItemId: curr.collectionItemIdentityId,
                        rowKey: curr.rowKey,
                        fieldId: curr.fieldId,
                    });
                    prev.set(key, curr);
                    return prev;
                }, new Map());
            }

            console.timeEnd('getCollectionStyles');
            const updatedFieldStyles: Array<TransactionFieldStyleEntity & { rowKey?: string }> = this.getUpdatedFieldStyles({
                validationResult,
                combinedTranFields,
                transaction,
                updateFields,
                fieldStyles,
                updateFieldsMap,
                styleAndOverrideFields,
            });

            // Update related data register fields for lookup fields
            await this._updateRelatedDataRegisterFields({
                reqTransactionFields: requestTransactionFields,
                relatedLookupDRTransactions: relatedLookupData.dataRegisterTransactions,
                transactionId: transaction.id,
                captureExistLookupField: option?.captureExistLookupField,
                updateFields,
            });

            updateFields.forEach((field) => {
                if (!field.id) field.id = v4();

                const key = this._formatTransactionFieldService.getTransactionFieldKey(field);

                const collectionFieldWithStyle = collectionStyleDict.get(key);
                if (collectionFieldWithStyle) {
                    field.validationValue = !!collectionFieldWithStyle?.code ? collectionFieldWithStyle.code : null;

                    const isExternalInput = externalDataDict.has(`${field.collectionItemId}_${field.rowKey}`);
                    if (isExternalInput) {
                        updatedFieldStyles.push({
                            id: field.id,
                            fieldId: field.fieldId,
                            configuration: {
                                icon: collectionFieldWithStyle?.code,
                                label: collectionFieldWithStyle?.label,
                            },
                            transactionId: transaction.id,
                        });
                    }
                }
            });

            const fieldIds = updateFields.map((field) => field.id);
            console.time('getOverrideValues');
            const overrideValues = await this._transactionFieldOverrideRepo.find({
                where: {
                    transactionFieldId: In(fieldIds),
                    type: OverrideTypeEnum.User,
                    status: Not(OverrideStatusEnum.UnOverride),
                },
            });

            const afterUpdatedFields = this._mergeTransactionAfterUpdate({
                currentTransactionFields: combinedTranFields,
                updateTransactionFields: updateFields,
            });
            // Check Override validation value
            const activeOverrides: TransactionFieldOverrideEntity[] = this.combineValidationValue({
                originTransactionFieldOverrides,
                overrideValues,
                updatedFieldStyles,
                updateFields: afterUpdatedFields,
            });
            console.timeEnd('getOverrideValues');

            const updatedResult = await this.updateTransactionData(
                transaction,
                updateFields,
                updatedFieldStyles,
                isTest,
                styleAndOverrideFields,
            );

            this.detectAndPublishFieldChangedEvent({
                oldFields: cloneTransactionFields,
                newFields: updatedResult.updatedFields,
                aggregateId: transaction.id,
                updatedTransaction: updatedResult.updatedTran,
                stageId: transaction.stageId,
                formVersion,
                populatedTranFields: (populatedTranFields || []).filter(
                    (f) => !f.collectionId && !populatedFormFieldIds.includes(f.fieldId),
                ),
                isTest,
            }).catch(console.error);

            console.time('update override');
            await updateActiveOverrideRecords({ activeOverrides, transactionFieldOverrideRepo: this._transactionFieldOverrideRepo });
            await addSystemOverrideRecords({
                originTransactionFieldOverrides,
                updateFields,
                transactionFieldOverrideRepo: this._transactionFieldOverrideRepo,
            });
            console.timeEnd('update override');

            if (populateResult?.populatedCollectionIds?.length) {
                const result = _.groupBy(updatedResult?.updatedFields || [], 'collectionId');
                const collectionIds = Object.keys(result);
                const uniqCollectionIds = Array.from(
                    new Set([...(collectionIds || []), ...(populateResult.populatedCollectionIds || [])]),
                ).filter(Boolean);

                for (const collectionId of uniqCollectionIds) {
                    if (validate(collectionId)) {
                        const message = EventDrivenService.createCommonEvent({
                            payload: {
                                id: transaction.id,
                                collectionId,
                                sourceOfChangeType: SourceOfChangeType.AUTO_POPULATE,
                            },
                            aggregateId: transaction.id,
                            tenantId: RequestContextService.accountId,
                            type: TransactionEventEnum.FORM_TRANSACTION_COLLECTION_AUTO_POPULATED,
                            name: TransactionEventEnum.FORM_TRANSACTION_COLLECTION_AUTO_POPULATED,
                        });
                        this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_COLLECTION_TOPIC, message);
                    }
                }
            }

            request.transactionFields = updatedResult?.updatedFields || [];
            this.handleTransactionUpdated(formVersion, updatedResult, transaction);

            // Push MQTT Form Updated event for Widget
            this.publishMQTTFormChangedEvent({
                accountId: this._claims.accountId,
                formId: request.formId,
                transactionId: transaction.id,
            });

            if (request?.payloadDocuments) {
                // Lastly, save hidden field to not track changes or saved to log
                await this._handlePayloadDocuments({
                    payloadDocuments: request?.payloadDocuments,
                    transactionFields: updatedResult?.updatedFields,
                    transactionId: transaction.id,
                });
            }

            return true;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    private async copyOverrideRecords(params: {
        styleAndOverrideFields?: StyleAndOverrideFields;
        transaction: TransactionEntity;
        transactionFieldOverrideRepository: Repository<TransactionFieldOverrideEntity>;
    }) {
        const { styleAndOverrideFields, transaction, transactionFieldOverrideRepository } = params;
        if (styleAndOverrideFields) {
            const overrideRecords: TransactionFieldOverrideEntity[] = [];
            Object.entries(styleAndOverrideFields).forEach(([fieldKey, { override, style }]) => {
                if (override) {
                    let transactionField: TransactionFieldEntity;

                    // Is form field transaction
                    if (isUUID(fieldKey)) {
                        transactionField = transaction.transactionFields.find((field) => field.fieldId === fieldKey);
                    }
                    // Is collection field transaction
                    else {
                        const [collectionIdentityId, collectionItemIdentityId, fieldId] = fieldKey.split('_');

                        transactionField = transaction.transactionFields.find(
                            (field) => field.collectionItemId === collectionItemIdentityId && field.fieldId === fieldId,
                        );

                        if (transactionField) {
                            const collectionKey = UtilsService.combineCollectionKeys({
                                collectionIdentityId: transactionField.collectionId,
                                collectionItemIdentityId: transactionField.collectionItemId,
                                fieldIdentityId: transactionField.fieldId,
                                collectionItemId: '',
                                fieldId: '',
                                collectionItemKey: transactionField.rowKey,
                            });
                            override.dependencyValues = {
                                [collectionKey]: override.dependencyValues[transactionField.fieldId],
                            };
                        }
                    }
                    if (!transactionField) return;

                    overrideRecords.push(
                        transactionFieldOverrideRepository.create({
                            comment: override.comment,
                            createdBy: override.createdBy,
                            createdByUser: override.createdByUser,
                            dependencyValues: override.dependencyValues,
                            fromValue: override.fromValue,
                            validationValue: override.validationValue,
                            transactionFieldId: transactionField.id,
                            transactionId: transaction.id,
                            status: OverrideStatusEnum.Active,
                            type: OverrideTypeEnum.User,
                        }),
                    );
                }
            });
            await transactionFieldOverrideRepository.update(
                {
                    transactionFieldId: In(overrideRecords.map((record) => record.transactionFieldId)),
                    status: OverrideStatusEnum.Active,
                },
                {
                    status: OverrideStatusEnum.Inactive,
                },
            );

            await transactionFieldOverrideRepository.save(overrideRecords);
        }
    }

    private copyFieldStyles(params: {
        styleAndOverrideFields?: StyleAndOverrideFields;
        transactionFieldStyles: TransactionFieldStyleEntity[];
        updatedFields: TransactionFieldEntity[];
    }) {
        const { styleAndOverrideFields, transactionFieldStyles, updatedFields } = params;
        if (styleAndOverrideFields) {
            Object.entries(styleAndOverrideFields).forEach(([fieldKey, { style, validationValue }]) => {
                let transactionField: TransactionFieldEntity;
                if (style) {
                    if (isUUID(fieldKey)) {
                        transactionField = updatedFields.find((field) => field.fieldId === fieldKey);
                    } else {
                        const [collectionIdentityId, collectionItemIdentityId, fieldId] = fieldKey.split('_');
                        transactionField = updatedFields.find(
                            (field) => field.collectionItemId === collectionItemIdentityId && field.fieldId === fieldId,
                        );
                    }

                    if (!transactionField) return;
                    const transactionFieldStyle = transactionFieldStyles.find((style) => style.id === transactionField.id);
                    if (transactionFieldStyle) {
                        transactionFieldStyle.configuration = style.style;
                    } else {
                        transactionFieldStyles.push({
                            fieldId: transactionField.fieldId,
                            configuration: style.style,
                            transactionId: transactionField.transactionId,
                            id: transactionField.id,
                        });
                    }
                }
                if (validationValue) {
                    transactionField.validationValue = validationValue;
                }
            });
        }
    }

    public detectAndPublishFieldChangedEvent = async ({
        oldFields,
        newFields,
        aggregateId,
        updatedTransaction,
        stageId,
        formVersion,
        populatedTranFields,
        isTest,
    }: {
        oldFields: TransactionFieldEntity[];
        newFields: TransactionFieldEntity[];
        aggregateId: string;
        updatedTransaction: TransactionEntity;
        stageId: string;
        formVersion: FormVersionTenancyEntity;
        populatedTranFields: TransactionFieldEntity[];
        isTest: boolean;
    }) => {
        const previous = [];
        const changes: (TransactionFieldEntity & { prevFieldValue: string; stageId?: string; prevStageId?: string })[] = [];
        const oldFieldMap = new Map(oldFields.map((item) => [item.id, item]));

        for (const newField of newFields) {
            const oldField = oldFieldMap.get(newField.id) || { fieldValue: '', validationValue: null };
            oldField.fieldValue = oldField?.fieldValue?.toString() ?? '';
            newField.fieldValue = newField?.fieldValue?.toString() ?? '';

            if (newField.dependFieldId || (oldField as any)?.dependFieldId) {
                continue;
            }

            if (oldField?.fieldValue !== newField.fieldValue || oldField.validationValue !== newField.validationValue) {
                if (oldFieldMap.get(newField.id)) {
                    previous.push(oldField);
                }
                changes.push({ ...newField, prevFieldValue: oldField.fieldValue });
            }
        }

        if (changes.length > 0) {
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...updatedTransaction,
                    fields: changes,
                    populatedFields: populatedTranFields.map((f) => f.id),
                    previous,
                    sourceFunction: 'detectAndPublishFieldChangedEvent',
                    isTest,
                },
                aggregateId,
                tenantId: RequestContextService.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
                name: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);

            await this.publishUpdatedFieldEventForRollup({
                accountId: this._claims.accountId,
                transactionId: updatedTransaction.id,
                formId: updatedTransaction.formId,
                formVersionId: updatedTransaction.formVersionId,
                fields: newFields, // send full new fields list to ensure rollup calculation logic
                stageId,
                isTest,
            });
            // TODO: check for test transaction
            await this.triggerDocPdfExtraction(updatedTransaction.id, changes, formVersion);
        } else {
            const message = EventDrivenService.createCommonEvent({
                payload: { ...updatedTransaction },
                aggregateId,
                tenantId: RequestContextService.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_UPDATED,
                name: TransactionEventEnum.FORM_TRANSACTION_UPDATED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
        }
    };

    public publishMQTTFormChangedEvent({ accountId, formId, transactionId }: { accountId: string; formId: string; transactionId: string }) {
        const widgetTransactionDatasourceTopic = `${accountId}/${WidgetMQTTTopicEnum.TRANSACTION_DATASOURCE_UPDATE}`;
        const widgetTransactionDatasourceMsg = {
            dataSourceId: formId,
            transactionId,
        };
        this._mqttService.publish(widgetTransactionDatasourceTopic, widgetTransactionDatasourceMsg, { qos: 1 }).catch((error) => {
            console.error(`Failed to publish MQTT message: ${error}`);
        });
    }

    public combineValidationValue = (params: {
        updateFields: TransactionFieldEntity[];
        updatedFieldStyles: TransactionFieldStyleEntity[];
        overrideValues: TransactionFieldOverrideEntity[];
        originTransactionFieldOverrides: Record<string, any>;
    }) => {
        const { originTransactionFieldOverrides, overrideValues, updateFields, updatedFieldStyles } = params;
        const activeOverrides: TransactionFieldOverrideEntity[] = [];

        updateFields.forEach((transactionField) => {
            const fieldStyle = updatedFieldStyles.find((fs) => fs.id === transactionField.id);
            if (!fieldStyle) return;

            const overrides = overrideValues.filter((ov) => ov.transactionFieldId === transactionField.id);
            if (!overrides.length) return;
            const overrideRecord = getOverrideRecord(updateFields, overrides);
            if (!overrideRecord) return;
            activeOverrides.push(overrideRecord as TransactionFieldOverrideEntity);

            originTransactionFieldOverrides[transactionField.id].type = OverrideTypeEnum.User;

            transactionField.validationValue = overrideRecord.validationValue;

            fieldStyle.configuration = {
                icon: overrideRecord.validationValue,
                label: overrideRecord.comment,
                border: fieldStyle.configuration.icon,
            };
        });

        return activeOverrides;
    };

    //#endregion PUT

    //#region DELETE
    public async delete({ id }: { id: string }) {
        if (!id) {
            return false;
        }

        try {
            const transaction = await this.formTransactionRepository.findOne({ where: { id }, withDeleted: true });
            if (!transaction || (!transaction.isTest && transaction.deletedAt)) {
                throw new NotFoundException('transaction_not_found');
            }

            const isTest = transaction.isTest;

            const fields = await this.transactionFieldRepository.find({ where: { transactionId: id } });

            const softDeleteFieldsQuery = softDeleteTransactionFieldsQuery(this._claims.accountId);
            const softDeleteFieldStylesQuery = softDeleteTransactionFieldStylesQuery(this._claims.accountId);
            const softDeleteFieldOverridesQuery = softDeleteTransactionFieldOverridesQuery(this._claims.accountId);
            const softDeleteRelationQuery = softDeleteRelationTransactionQuery(this._claims.accountId);

            this.formTransactionRepository.query(softDeleteFieldsQuery, [this._claims.userId, id]).catch((e) => {
                console.error(e);
            });
            this.formTransactionRepository.query(softDeleteFieldStylesQuery, [this._claims.userId, id]).catch((e) => {
                console.error(e);
            });
            this.formTransactionRepository.query(softDeleteFieldOverridesQuery, [this._claims.userId, id]).catch((e) => {
                console.error(e);
            });
            this.formTransactionRepository.query(softDeleteRelationQuery, [this._claims.userId, id]).catch((e) => {
                console.error(e);
            });

            if (isTest) {
                await this.formTransactionRepository.update(id, {
                    isTest: false,
                });
                await this.formVersionRepository.update(transaction.formVersionId, {
                    testTransactionId: null,
                });

                return true;
            } else {
                const result = await this.formTransactionRepository.softRemove(transaction);
                await this.publishUpdatedFieldEventForRollup({
                    accountId: this._claims.accountId,
                    transactionId: id,
                    formId: transaction.formId,
                    formVersionId: transaction.formVersionId,
                    fields: fields,
                    stageId: transaction.stageId,
                });

                // Push MQTT Form Updated event for Widget
                this.publishMQTTFormChangedEvent({
                    accountId: this._claims.accountId,
                    formId: transaction.formId,
                    transactionId: transaction.id,
                });

                const message = EventDrivenService.createCommonEvent({
                    payload: result,
                    aggregateId: result.id,
                    tenantId: RequestContextService.accountId,
                    type: TransactionEventEnum.FORM_TRANSACTION_DELETED,
                    name: TransactionEventEnum.FORM_TRANSACTION_DELETED,
                });
                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);

                return !!result;
            }
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    //#endregion DELETE

    public async getFormWithActiveVersion({
        formId,
        formVersionId,
        version,
    }: {
        formId: string;
        formVersionId?: string;
        version?: number;
    }): Promise<FormTenancyEntity> {
        const builder = await this.formRepository
            .createQueryBuilder('form')
            .leftJoinAndSelect('form.formVersions', 'formVersion')
            .leftJoinAndSelect('formVersion.fields', 'field')
            .leftJoinAndSelect('formVersion.stages', 'stages')
            .where({
                id: formId,
            });

        if (formVersionId) {
            builder.andWhere('formVersion.id = :formVersionId', { formVersionId });
        } else if (version) {
            builder.andWhere('formVersion.version = :version', { version });
        } else {
            builder.andWhere('formVersion.id = form.activeVersionId');
        }
        // console.log(builder.getQueryAndParameters());
        const result = await builder.getOne();

        return result;
    }

    public async getFormByVersion({ formVersionId }: { formVersionId: string }): Promise<FormVersionTenancyEntity> {
        const builder = await this.formVersionRepository
            .createQueryBuilder('formVersion')
            .leftJoinAndSelect('formVersion.fields', 'field')
            .leftJoinAndSelect('formVersion.stages', 'stage')
            .where({
                id: formVersionId,
            });

        const result = await builder.getOne();
        return result;
    }

    public async preProcessDataFields(
        registerFields: FormFieldTenancyEntity[],
        transactionFields: EditFormTransactionFieldRequest[],
        relatedLookupData: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ) {
        if (!transactionFields) {
            return;
        }

        const userData = await this._getUserLookupData(transactionFields, registerFields);
        const roleData = await this._getRoleLookupData(transactionFields, registerFields);

        transactionFields.forEach((field) => {
            const registerField = registerFields?.find((item) => item.fieldId === field.fieldId);
            if (!registerField) {
                return;
            }

            try {
                switch (registerField.type) {
                    case FormFieldTypeEnum.Calculation:
                        if (!field.fieldValue) {
                            this._processCalculationField(field, registerField, transactionFields, relatedLookupData);
                        }
                        break;
                    case FormFieldTypeEnum.UserLookup:
                        const user = userData?.find((user) => user.id === field.fieldValue);
                        if (user) {
                            field.fieldLabelValue = `${user.firstName} ${user.secondName}`;
                        }
                        break;
                    case FormFieldTypeEnum.RoleLookup:
                        const role = roleData?.find((role) => role.id === field.fieldValue);
                        if (role) {
                            field.fieldLabelValue = role.name;
                        }
                        break;
                    case FormFieldTypeEnum.Text:
                        field.fieldValue = field?.fieldValue ?? '';
                        break;
                    default:
                        break;
                }
            } catch (error) {
                console.error(error);
            }
        });
    }

    public getMainFormula(formulas: CalculationFormula[], value: DataFieldDto[]) {
        const watchingConditionFields: string[] = [];
        const values: any[] = [];
        formulas?.forEach((formula: any) => {
            const rules = formula?.conditions?.children1;
            if (rules) {
                getRelatedFields((rules as JsonItem[]) || [], watchingConditionFields);
            }
        });

        watchingConditionFields.map((field) => {
            values.push(value?.find((item) => item.id === field)?.value);
        });

        return this.getSatisfiedFormula(formulas ?? [], watchingConditionFields, values);
    }

    public async saveRelatedTransaction(originalTransactionId: string, transactionId: string, transactionFormId: string) {
        if (!originalTransactionId || !transactionId) {
            return null;
        }

        const originTransaction = await this.formTransactionRepository.findOne({ where: { id: originalTransactionId } });

        return this.formRelationTransactionRepository.save({
            originTransactionId: originTransaction.id,
            originFormId: originTransaction.formId,
            targetTransactionId: transactionId,
            targetFormId: transactionFormId,
            type: 'related',
        });
    }

    public async getParentTransactionId(transactionId: string) {
        const parentTransactions = await this.formRelationTransactionRepository.find({
            where: { targetTransactionId: transactionId },
        });
        return parentTransactions;
    }

    public async processRelatedFieldsChange(
        formVersion: FormVersionEntity | FormVersionTenancyEntity,
        transactionId: string,
        tranFields: TransactionFieldEntity[],
        stageId: string,
    ): Promise<void> {
        if (!formVersion || !formVersion.fields?.length || !tranFields?.length) {
            return;
        }

        let onChangeFields = [];
        const dtos: RelatedFieldChangeEventDto = {
            accountId: this._claims.accountId,
            formId: formVersion.formId,
            formVersionId: formVersion.id,
            transactionId: transactionId,
            tranFields: [],
            stageId: stageId,
        };

        // get list pull register
        const autoPopulateSettings = await this._autoPopulateDataService.getAllAutoPopulateFields(
            formVersion?.formId,
            this._autoPopulateRepository,
        );

        formVersion.fields.forEach((item: FormFieldTenancyEntity) => {
            const fieldIds = autoPopulateSettings?.filter((f) => f.originFieldId === item.fieldId || f.targetFieldId === item.fieldId);
            onChangeFields = [...onChangeFields, ...(fieldIds ?? [])];
        });
        if (onChangeFields?.length) {
            dtos.tranFields = tranFields;
            this._eventEmitter.emit(EVENT.RELATED_FIELD_CHANGE, dtos);
        }
    }

    //#region PRIVATE METHODS

    private _processCalculationField(
        tranField: EditFormTransactionFieldRequest,
        registerField: FormFieldTenancyEntity,
        transactionFields: EditFormTransactionFieldRequest[],
        relatedLookupData: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ) {
        const formulaObject = registerField?.configuration?.['calculationFormula'] as FormulaSettingType;
        if (!formulaObject) {
            return;
        }
        const variableValue = transactionFields.map((item) => {
            return {
                id: item.fieldId,
                type: registerField.type,
                value: item.fieldValue,
                name: registerField.label,
            } as DataFieldDto;
        });
        const mainFormula = this._calculationService.getMainFormula(formulaObject?.formulas ?? [], variableValue);
        if (!mainFormula) {
            return;
        }
        const { variableMappings } = mainFormula;
        // get related field for formula
        const values: any = [];
        variableMappings.forEach((item: VariableMapping) => {
            if (!item?.field) {
                return;
            }
            let value = variableValue?.find((v) => v.id === item.field?.id)?.value;
            if (value && FormFieldTypeEnum.Lookup === item.field.type && item.field.mode != 'multiple') {
                const lookupFieldData = this.getLookupData(
                    value,
                    item?.lookupData?.field?.id ?? '',
                    item?.lookupData?.field?.type,
                    relatedLookupData,
                );
                value = lookupFieldData;
            }
            values.push(value);
        });

        if (!tranField.transactionId) return null;

        console.log(
            'Start calculation for: ',
            `tranId:${tranField.transactionId}-fieldId:${tranField.id}-formVerId:${registerField?.formVersionId}`,
        );
        const value = this._formulaService.calculate(formulaObject.dataType, tranField.defaultValue, mainFormula as FormulaDto, values);
        console.log(
            'End calculation for: ',
            `tranId:${tranField.transactionId}-fieldId:${tranField.id}-formVerId:${registerField?.formVersionId}`,
        );
        tranField.fieldValue = value ? value.toString() : null;
    }

    private async _getUserLookupData(tranFields: EditFormTransactionFieldRequest[], registerFields: FormFieldTenancyEntity[]) {
        const userLookupFieldIds = [];
        tranFields?.forEach((field) => {
            const registerField = registerFields?.find((f) => f.fieldId === field.fieldId);
            if (registerField?.type !== FormFieldTypeEnum.UserLookup || !field.fieldValue) {
                return;
            }
            userLookupFieldIds.push(field.fieldValue);
        });

        if (!userLookupFieldIds) {
            return [];
        }
        const users = await this.userTenancyRepository.findBy({
            id: In(userLookupFieldIds),
        });
        return users;
    }

    private async _getRoleLookupData(tranFields: EditFormTransactionFieldRequest[], registerFields: FormFieldTenancyEntity[]) {
        const rolesLookupFieldIds = [];
        tranFields?.forEach((field) => {
            const registerField = registerFields?.find((f) => f.fieldId === field.fieldId);
            if (registerField?.type !== FormFieldTypeEnum.RoleLookup || !field.fieldValue) {
                return;
            }
            rolesLookupFieldIds.push(field.fieldValue);
        });

        if (!rolesLookupFieldIds) {
            return [];
        }
        const roles = await this.roleTenancyRepository.findBy({
            id: In(rolesLookupFieldIds),
        });
        return roles;
    }

    private getSatisfiedFormula(formulas: CalculationFormula[], watchingConditionFields: string[], watchedConditionFieldValues: any[]) {
        if (!formulas?.length) {
            return null;
        }
        const activeFormulas: CalculationFormula[] = [];
        formulas.forEach((formula) => {
            const condition = formula.conditions;
            if (!condition?.children1?.length) {
                activeFormulas.push(formula);
                return;
            }
            const satisfyCondition = executeConditions(condition, watchingConditionFields, watchedConditionFieldValues, false);
            if (satisfyCondition) {
                activeFormulas.push(formula);
            }
        });
        // if there are multiple satisfied conditions then return first formula from the list
        return activeFormulas[0];
    }

    private getLookupData(
        lookupRecordId: string,
        lookupFieldId: string,
        lookupFieldType: FormFieldTypeEnum,
        relatedLookupData: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ) {
        if (!relatedLookupData?.dataRegisterTransactions) {
            return null;
        }
        const record = relatedLookupData.dataRegisterTransactions?.find((item) => item.id === lookupRecordId);
        if (!record) {
            return null;
        }
        const data = record.transactionFields?.find((item: any) => item.fieldId === lookupFieldId)?.fieldValue;
        if (!data) {
            return data;
        }
        return this.convertVariableValue(lookupFieldType, data);
    }

    private convertVariableValue(type: FormFieldTypeEnum | undefined, value: any) {
        if (!type) {
            return null;
        }

        let formattedValue: string | number | null | Duration | Date = '';

        switch (type) {
            case FormFieldTypeEnum.DatePicker:
                formattedValue = value ? new Date(value) : null;
                break;
            case FormFieldTypeEnum.TimePicker:
                formattedValue = UtilsService.convertTimeStringToDate(value as string);
                break;

            case FormFieldTypeEnum.Duration:
                formattedValue = UtilsService.convertStringToDuration(
                    UtilsService.convertMinutesToDuration({ value: value, format: DurationFormatEnum.DHM }),
                );
                break;

            case FormFieldTypeEnum.Number:
                formattedValue = Number(value);
                break;

            default:
                formattedValue = value;
                break;
        }
        return formattedValue;
    }

    public async publishUpdatedFieldEventForRollup({
        accountId,
        fields,
        formId,
        formVersionId,
        transactionId,
        stageId,
        isTest,
    }: {
        accountId: string;
        transactionId: string;
        formId: string;
        formVersionId: string;
        fields: Array<TransactionFieldEntity>;
        stageId?: string;
        isTest?: boolean;
    }): Promise<void> {
        const fieldIds = compact(uniq(fields.map((field) => field.fieldId)));

        const message = EventDrivenService.createCommonEvent({
            payload: {
                accountId: accountId,
                transactionId: transactionId,
                formId: formId,
                formVersionId: formVersionId,
                fields: fieldIds,
                sourceOfChange: RequestContextService.source === 'user' ? SourceOfChangeType.MANUAL : SourceOfChangeType.AUTOMATION,
                stageId: stageId,
                isTest: isTest,
            },
            aggregateId: transactionId,
            tenantId: accountId,
            type: TransactionEventEnum.FORM_TRANSACTION_ROLLUP,
            name: TransactionEventEnum.FORM_TRANSACTION_ROLLUP,
        });
        await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_ROLLUP_TOPIC, message);
    }

    private _setDefaultRoleLookupField(
        formVersion: FormVersionEntity,
        transaction: TransactionEntity,
        request: EditFormTransactionRequest,
    ) {
        const roleLookupField = formVersion.fields.find((f) => f.type === FormFieldTypeEnum.RoleLookup);
        if (!roleLookupField) return;

        const requestField = request.transactionFields?.find((field) => field.fieldId === roleLookupField.fieldId);
        if (!requestField || requestField.fieldValue) {
            return;
        }

        const { defaultRoles } = (roleLookupField.configuration ?? {}) as FieldConfiguration;
        if (!defaultRoles?.length) return;

        let defaultRoleTransactionField = transaction.transactionFields?.find((f) => f.fieldId === roleLookupField.fieldId);
        if (!defaultRoleTransactionField) {
            defaultRoleTransactionField = new TransactionFieldEntity();
            defaultRoleTransactionField.fieldId = roleLookupField.fieldId;
            defaultRoleTransactionField.transactionId = transaction.id;
        }

        const role = defaultRoles.find((r) => r.stageId === transaction.stageId); // NEED TO CHANGE TO IDENTITY ID
        if (!role) return;

        defaultRoleTransactionField.fieldOptionIds = [role.roleId];
        defaultRoleTransactionField.fieldValue = role.roleName;

        return defaultRoleTransactionField;
    }

    private _mergeTransactionFields({
        requestTranFields,
        currentTranFields,
        option,
    }: {
        currentTranFields: TransactionFieldEntity[];
        requestTranFields: EditFormTransactionFieldRequest[];
        option?: { forceUsingTransactionFieldValues?: boolean };
    }) {
        const existedItemDict = new Map();
        const externalCollectionItemRequestFields: typeof requestTranFields = [];
        const collectionItemExternalDict = new Map();

        const requestFieldDict = new Map();
        const externalDataDict = new Map();

        const shouldCheckValidationRows = new Map();

        requestTranFields.forEach((rf) => {
            const key = this._formatTransactionFieldService.getTransactionFieldKey(rf);
            requestFieldDict.set(key, rf);

            if (rf.data?.collectionItemExternalId) {
                externalCollectionItemRequestFields.push(rf);
                existedItemDict.set(rf.collectionItemId, 1);
                collectionItemExternalDict.set(rf.data?.collectionItemExternalId, 1);
            }
        });

        // let removeTranFields: TransactionFieldEntity[] = [];
        // let removeTranFieldDict = new Map();

        // if (externalCollectionItemRequestFields?.length) {
        //     const ctxIdDict = currentTranFields.reduce((prev, tf) => {
        //         if (collectionItemExternalDict.has(tf.data?.collectionItemExternalId)) {
        //             prev.set(tf?.data?.externalContextId, 1);
        //         }

        //         return prev;
        //     }, new Map());

        //     removeTranFields = currentTranFields.filter((f) => {
        //         if (!f.data?.collectionItemExternalId) {
        //             const isRm = existedItemDict.has(f.collectionItemId);
        //             if (isRm) {
        //                 removeTranFieldDict.set(f.id, 1);
        //             }

        //             return isRm;
        //         }

        //         const isRm = f.data?.collectionItemExternalId && !ctxIdDict.has(f.data?.externalContextId);
        //         if (isRm) {
        //             removeTranFieldDict.set(f.id, 1);
        //         }
        //         return isRm;
        //     });
        // }

        // if (removeTranFields?.length) currentTranFields = currentTranFields.filter((f) => !removeTranFieldDict.has(f.id));

        const currentFieldDict = new Map();
        const updateFieldDict = new Map();

        for (let field of currentTranFields) {
            const tranFieldKey = this._formatTransactionFieldService.getTransactionFieldKey(field);
            const updatedField = requestFieldDict.get(tranFieldKey);
            if (updatedField && !option?.forceUsingTransactionFieldValues) {
                const isSelectableField = OBJECT_SELECTABLE_FIELD_TYPES.includes(updatedField.fieldType);

                if (isSelectableField) {
                    const splittedValue = updatedField.fieldValue?.split(',');

                    const isLookup = splittedValue?.every((v) => isUUID(v)) && updatedField.fieldType === FormFieldTypeEnum.Lookup;
                    const isSelect = [FormFieldTypeEnum.Select, FormFieldTypeEnum.MultiSelect].includes(updatedField.fieldType);

                    if (splittedValue?.length && (isLookup || isSelect)) {
                        field.fieldOptionIds = splittedValue;
                        if (!updatedField.fieldOptionIds?.length) {
                            updatedField.fieldOptionIds = splittedValue;
                        }
                    }
                    field.fieldValue = updatedField.fieldValue;
                } else {
                    if (!updatedField.fieldValue && updatedField.fieldType === FormFieldTypeEnum.Document) {
                        updatedField.fieldValue = '';
                        if (updatedField.data) {
                            (updatedField.data as Record<string, string>).docId = null;
                        }

                        if (field.data) {
                            (field.data as Record<string, string>).docId = null;
                        }
                    }
                    field.fieldValue = updatedField.fieldValue;
                }
                field.fieldOptionIds = updatedField.fieldOptionIds;
            }

            updateFieldDict.set(tranFieldKey, field);
            currentFieldDict.set(tranFieldKey, field);

            if (field.collectionId && field.collectionItemId && field.rowKey && field.data?.collectionItemExternalId) {
                externalDataDict.set(`${field.collectionItemId}_${field.rowKey}`, {
                    collectionItemExternalId: field.data?.collectionItemExternalId,
                    externalContextId: field.data?.externalContextId,
                    externalOrder: field.data?.externalOrder,
                });
            }
        }

        requestTranFields.forEach((field) => {
            const key = this._formatTransactionFieldService.getTransactionFieldKey(field);

            if (field.rowKey) {
                shouldCheckValidationRows.set(field.rowKey, true);
            }

            const isExistCurrent = currentFieldDict.has(key);
            if (isExistCurrent) {
                if (option?.forceUsingTransactionFieldValues) {
                    const tranField = currentFieldDict.get(key);
                    field.fieldValue = tranField.fieldValue;
                    field.fieldOptionIds = tranField.fieldOptionIds;
                }
                return;
            }

            const isSelectableField = OBJECT_SELECTABLE_FIELD_TYPES.includes(field.fieldType);
            const entity = this._mapper.map(field, EditFormTransactionFieldRequest, TransactionFieldEntity);
            entity.transactionId = currentTranFields[0]?.transactionId;
            if (isSelectableField) {
                const splittedValue = field.fieldValue?.split(',');
                if (splittedValue?.length) {
                    entity.fieldOptionIds = splittedValue;
                }
            }

            const dataKey = `${field.collectionItemId}_${field.rowKey}`;
            if (externalDataDict.has(dataKey)) {
                entity.data = { ...(entity.data ?? {}), ...externalDataDict.get(dataKey) };
            }

            updateFieldDict.set(key, entity);
        });

        return {
            combinedTranFields: Array.from(updateFieldDict.values()),
            // removeTranFields,
            currentFieldDict,
            externalDataDict,
            shouldCheckValidationRows,
        };
    }

    private _mergeTransactionAfterUpdate(params: {
        currentTransactionFields: TransactionFieldEntity[];
        updateTransactionFields: TransactionFieldEntity[];
    }): TransactionFieldEntity[] {
        const { currentTransactionFields = [], updateTransactionFields = [] } = params;

        return currentTransactionFields.map((currentField) => {
            const updatedField = updateTransactionFields.find((f) => f.id === currentField.id);
            return updatedField || currentField;
        });
    }

    public validateTransactionData(
        fields: FormFieldTenancyEntity[],
        tranFields: TransactionFieldEntity[],
        lookupValues: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ): {
        fieldId: string;
        code: DataValidationResult;
        label: string;
        rowKey?: string;
    }[] {
        if (!fields?.length || !tranFields?.length) {
            return null;
        }
        const result = [];
        //just run for transaction field
        for (let tranField of tranFields) {
            if (tranField.collectionId) continue;

            const field = fields.find((item) => item.fieldId === tranField.fieldId);
            if (!field) {
                continue;
            }

            const configs = field.configuration['ruleConfigs']?.[FormRuleType.DataValidation];
            if (!configs) {
                continue;
            }
            const fieldValidationResult = this.executeDataValidationConditions(configs, tranFields, lookupValues, fields);
            if (fieldValidationResult && !result.some((f) => f.fieldId === tranField.fieldId)) {
                result.push({
                    fieldId: tranField.fieldId,
                    code: fieldValidationResult.code,
                    label: fieldValidationResult.label,
                });
            }
        }
        return result;
    }

    public getFieldStyles(
        fields: FormFieldTenancyEntity[],
        updatedFields: TransactionFieldEntity[],
        tranFields: TransactionFieldEntity[],
        lookupValues: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ): {
        fieldId: string;
        style: { color: string; backgroundColor: string };
    }[] {
        if (!fields?.length || !tranFields?.length) {
            return null;
        }
        const result = [];
        for (let tranField of tranFields) {
            const field = fields.find((item) => item.fieldId === tranField.fieldId);
            if (!field) {
                continue;
            }

            const configs = field.configuration['styleConditions'] as StyleConditionType[];
            if (!configs) {
                if (field.configuration.style) {
                    result.push({ fieldId: tranField.fieldId, style: field.configuration.style });
                } else {
                    continue;
                }
            }
            const fieldStyles = this.executeStyleConditions(configs, updatedFields, tranFields, lookupValues);
            if (fieldStyles) {
                result.push({ fieldId: tranField.fieldId, style: fieldStyles });
            } else {
                if (field.configuration.style) {
                    result.push({ fieldId: tranField.fieldId, style: field.configuration.style });
                }
            }
        }
        return result;
    }

    private executeDataValidationConditions(
        conditions: any | undefined,
        tranFields: TransactionFieldEntity[],
        lookupValues: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
        fields: FormFieldTenancyEntity[],
    ) {
        if (!conditions?.length) return null;

        let watchingConditionFields: string[] = [];
        const watchingConditionFieldValues: any[] = [];
        conditions?.forEach((formula: any) => {
            const rules = formula?.condition?.children1;
            if (rules) {
                getRelatedFields((rules as JsonItem[]) || [], watchingConditionFields);
            }
        });

        watchingConditionFields = uniq(watchingConditionFields);

        const separator = '--';
        const conditionFieldIds: string[] = [];
        watchingConditionFields.map((field) => {
            let fieldValue: any = null;
            let lookupField: any = null;
            const fieldComparators = !field.includes(separator) ? [null, field] : field.split(separator);

            conditionFieldIds.push(fieldComparators[1]);

            lookupValues?.dataRegisterTransactions?.forEach((drTran) => {
                const fields = drTran.transactionFields;
                const tranField = fields?.find((f) => f.fieldId === fieldComparators[1]);
                if (tranField) {
                    lookupField = tranField;
                }
            });

            const transactionField = tranFields?.find(
                (item) => item.fieldId === fieldComparators[1] && (!fieldComparators[0] || item.dependFieldId === fieldComparators[0]),
            );

            let fieldConfig: any = null;
            if (isUUID(fieldComparators[0])) {
                const formField = fields?.find((item) => item.fieldId === fieldComparators[0]);
                if (!formField) {
                    return;
                }
                const register = lookupValues.dataRegisters.find((r) => r.id === formField.configuration.targetId);
                fieldConfig = register?.dataRegisterVersions?.[0]?.fields?.find((field) => field.fieldId === fieldComparators[1]);
            } else {
                fieldConfig = fields.find((field) => field.fieldId === transactionField?.fieldId);
            }

            const convertVariableRequest: {
                type: FormFieldTypeEnum;
                value: any;
                fieldOptionIds?: any;
            } = {
                type: fieldConfig?.type,
                value: transactionField?.fieldValue,
                fieldOptionIds: transactionField?.fieldOptionIds,
            };

            if (lookupField) {
                convertVariableRequest.value = lookupField?.fieldValue;
                convertVariableRequest.fieldOptionIds = lookupField?.optionIds?.[0];

                if (!(fieldComparators?.length === 2 && isUUID(fieldComparators[1]))) {
                    convertVariableRequest.type = FormFieldTypeEnum.Lookup;
                }
            }

            fieldValue = JsonLogicUtils.convertVariableValue(
                convertVariableRequest.type,
                convertVariableRequest.value,
                convertVariableRequest.fieldOptionIds,
            );

            watchingConditionFieldValues.push(fieldValue);
        });

        for (let index = 0; index < conditions?.length; index++) {
            const { condition, icon = {} } = conditions[index];

            const satisfyCondition = executeConditions(condition, conditionFieldIds, watchingConditionFieldValues, false);

            if (satisfyCondition) {
                return {
                    code: icon.name,
                    label: icon.label,
                };
            }
        }
        return {
            code: null,
            label: null,
        };
    }

    private executeStyleConditions(
        conditions: any | undefined,
        updatedFields: TransactionFieldEntity[],
        tranFields: EditFormTransactionFieldRequest[],
        lookupValues: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ) {
        if (!conditions?.length) return null;

        let watchingConditionFields: string[] = [];
        const watchingConditionFieldValues: any[] = [];
        conditions?.forEach((formula: any) => {
            const rules = formula?.condition?.children1;
            if (rules) {
                getRelatedFields((rules as JsonItem[]) || [], watchingConditionFields);
            }
        });

        if (!updatedFields.some((f) => watchingConditionFields?.includes(f.fieldId))) {
            return;
        }
        watchingConditionFields = uniq(watchingConditionFields);

        watchingConditionFields.map((field) => {
            let fieldValue: any = null;
            let lookupField: any = null;
            lookupValues?.dataRegisterTransactions?.forEach((drTran) => {
                const fields = drTran.transactionFields;
                const tranField = fields?.find((f) => f.fieldId === field);
                if (tranField) {
                    lookupField = tranField;
                }
            });
            fieldValue =
                lookupField?.fieldValue ?? lookupField?.optionIds?.[0] ?? tranFields?.find((item) => item.fieldId === field)?.fieldValue;
            watchingConditionFieldValues.push(fieldValue);
        });

        for (let index = 0; index < conditions?.length; index++) {
            const { condition, style = {} } = conditions[index];
            const satisfyCondition = executeConditions(condition, watchingConditionFields, watchingConditionFieldValues, false);

            if (satisfyCondition) {
                return style;
            }
        }
        return null;
    }

    public getUpdatedFieldStyles({
        validationResult,
        combinedTranFields,
        transaction,
        updateFields,
        updateFieldsMap,
        fieldStyles,
        styleAndOverrideFields,
    }: {
        validationResult: { fieldId: string; code: DataValidationResult; label: string; border?: number; rowKey?: string }[];
        combinedTranFields: TransactionFieldEntity[];
        transaction: TransactionEntity;
        updateFields: TransactionFieldEntity[];
        updateFieldsMap: Map<string, TransactionFieldEntity>;
        fieldStyles: { fieldId: string; style: { color: string; backgroundColor: string } }[];
        styleAndOverrideFields?: StyleAndOverrideFields;
    }) {
        const updatedFieldStyles: Array<TransactionFieldStyleEntity & { rowKey?: string }> = [];

        if (validationResult?.length || styleAndOverrideFields) {
            const validationResultMap = (validationResult || []).reduce((acc, item) => {
                const key = item.rowKey ? `${item.rowKey}_${item.fieldId}` : item.fieldId;
                if (acc.has(key)) {
                    return acc;
                }

                acc.set(key, item);
                return acc;
            }, new Map());

            combinedTranFields?.forEach((field) => {
                field.transactionId = transaction.id;

                const updateStyleField: TransactionFieldStyleEntity & {
                    rowKey?: string;
                } = {
                    id: field.id,
                    fieldId: field.fieldId,
                    rowKey: field.rowKey,
                    transactionId: field.transactionId,
                };

                let isUpdate = false;
                let validationValue: number = null;

                if (styleAndOverrideFields) {
                    const fieldKey = field.collectionId
                        ? `${field.collectionId}_${field.collectionItemId}_${field.fieldId}`
                        : field.fieldId;

                    if (styleAndOverrideFields[fieldKey]) {
                        const populateValidation = styleAndOverrideFields[fieldKey] ?? {};
                        const style = populateValidation.style;

                        if (style) {
                            updateStyleField.configuration = {
                                icon: style.style?.icon,
                                label: style.style?.label,
                                color: style.style?.color,
                                backgroundColor: style?.style?.backgroundColor,
                                border: style.style?.border,
                            };

                            validationValue = style.style?.icon;
                            isUpdate = true;
                        }
                    }
                }

                if (validationResult?.length) {
                    const styles = fieldStyles?.find((item) => item.fieldId === field.fieldId);
                    const key = field.rowKey ? `${field.rowKey}_${field.fieldId}` : field.fieldId;
                    const fieldValidation = validationResultMap.get(key);
                    if (fieldValidation) {
                        updateStyleField.configuration = {
                            icon: fieldValidation.code,
                            label: fieldValidation.label,
                            color: styles?.style?.color,
                            backgroundColor: styles?.style?.backgroundColor,
                            border: fieldValidation.border,
                        };

                        updateStyleField.rowKey = fieldValidation.rowKey;
                        validationValue = fieldValidation.code;
                        isUpdate = true;
                    }
                }

                if (isUpdate) {
                    updatedFieldStyles.push(updateStyleField);

                    const updateFieldKey = this._formatTransactionFieldService.getTransactionFieldKey(field);
                    const updatedField = updateFieldsMap.get(updateFieldKey);

                    if (updatedField) {
                        updatedField.validationValue = validationValue;
                    } else {
                        updateFields.push(field);
                    }
                }
            });
        }

        return updatedFieldStyles;
    }

    /**
     * Executes the condition value field logic for the provided form and transaction fields.
     *
     * @param params - The parameters for the function.
     * @param params.updatedFields - An optional array of need to update transaction field entities.
     * @param params.transactionFields - An array of all transaction field entities.
     * @param params.formFieldEntities - An array of form field entities, which can be either tenancy or non-tenancy entities.
     * @param params.formCollectionFieldEntities - An array of form collection item entities, which can be either tenancy or non-tenancy entities.
     * @returns The processed transaction fields after applying the condition value logic.
     */
    public executeConditionValueField({
        transactionFields,
        formFieldEntities,
        formCollectionFieldEntities,
    }: {
        transactionFields: TransactionFieldEntity[];
        formFieldEntities: FormFieldTenancyEntity[] | FormFieldEntity[];
        formCollectionFieldEntities: FormCollectionItemTenancyEntity[] | FormCollectionItemEntity[];
    }) {
        const conditionFormFields = formFieldEntities.filter((f) => f.configuration?.visibilityConditions?.children1?.length);
        const formCollectionConditionFields = formCollectionFieldEntities.filter((f) => f.setting?.visibility?.children1?.length);

        if (!conditionFormFields.length && !formCollectionConditionFields.length) {
            return [];
        }

        const formValues = this._processConditionFormFields(conditionFormFields, transactionFields, formFieldEntities);

        const formCollectionValues = this._processCollectionConditionFields(formCollectionConditionFields, transactionFields);

        return [...formValues, ...formCollectionValues];
    }

    public mergeWithCfvFields({
        transactionFields,
        fieldWithCvf,
    }: {
        transactionFields: TransactionFieldEntity[];
        fieldWithCvf: TransactionFieldEntity[];
    }) {
        const updateFieldMap = new Map<string, number>();
        const contextFields = new Map<string, TransactionFieldEntity>();

        // Build initial maps in single pass
        for (let i = 0; i < transactionFields.length; i++) {
            const field = transactionFields[i];
            if (!field.contextType) continue;

            const key =
                field.contextType === TransactionFieldContextTypeEnum.COLLECTION
                    ? `${field.fieldId}_${field.collectionItemId}`
                    : field.fieldId;

            updateFieldMap.set(key, i);
            contextFields.set(key, field);
        }

        // Process fields in single pass
        fieldWithCvf.forEach((field) => {
            const key =
                field.contextType === TransactionFieldContextTypeEnum.COLLECTION
                    ? `${field.fieldId}_${field.collectionItemId}`
                    : field.fieldId;

            const index = updateFieldMap.get(key);

            if (index !== undefined) {
                transactionFields[index] = field;
            } else {
                transactionFields.push(field);
            }
        });

        return transactionFields;
    }

    private _processConditionFormFields(
        conditionFormFields: (FormFieldTenancyEntity | FormFieldEntity)[],
        transactionFields: TransactionFieldEntity[],
        formFieldEntities: FormFieldTenancyEntity[] | FormFieldEntity[],
    ) {
        const formValues = {};

        const transactionFieldMap: Record<string, TransactionFieldEntity> = transactionFields.reduce((prev, curr) => {
            prev[curr.fieldId] = curr;
            return prev;
        }, {});

        const formFieldEntitiesMap: Record<string, FormFieldTenancyEntity | FormFieldEntity> = formFieldEntities.reduce((prev, curr) => {
            prev[curr.fieldId] = curr;
            return prev;
        }, {});

        const result: Array<TransactionFieldEntity> = [];

        for (const field of conditionFormFields) {
            const { fieldId, configuration } = field;
            const transactionField = transactionFieldMap[fieldId];

            if (!transactionField) continue;

            for (const child of configuration.visibilityConditions?.children1) {
                const fieldKey = (child as any)?.properties?.field;

                const _transactionField = transactionFieldMap[fieldKey];

                const field = formFieldEntitiesMap[fieldKey];

                formValues[fieldKey] = JsonLogicUtils.convertVariableValue(
                    field?.type,
                    _transactionField?.fieldValue,
                    _transactionField?.fieldOptionIds,
                );
            }

            this._processDependencyFields(configuration.visibilityConditions.children1, transactionFields, formValues);

            transactionField.inVisible = !this._evaluateConditions(configuration.visibilityConditions, formValues);

            result.push(transactionField);
        }

        return result;
    }

    private _processCollectionConditionFields(
        formCollectionConditionFields: (FormCollectionItemTenancyEntity | FormCollectionItemEntity)[],
        transactionFields: TransactionFieldEntity[],
    ) {
        const formCollectionValues = {};
        const separator = '--';

        const transactionFieldMap: Record<string, TransactionFieldEntity> = {};

        const groupByCollectionItem: Record<string, Array<TransactionFieldEntity>> = {};

        transactionFields.forEach((field) => {
            const mapKey = `${field.fieldId}_${field.collectionItemId ?? null}`;
            if (!transactionFieldMap[mapKey]) {
                transactionFieldMap[mapKey] = field;
            }

            if (!field.collectionItemId) {
                return;
            }

            if (!groupByCollectionItem[field.collectionItemId]) {
                groupByCollectionItem[field.collectionItemId] = [];
            }

            groupByCollectionItem[field.collectionItemId].push(field);
        });

        const result: Array<TransactionFieldEntity> = [];

        for (const field of formCollectionConditionFields) {
            for (const child of field.setting?.visibility?.children1) {
                const fieldKey = (child as any)?.properties?.field;
                const [collectionItemIdentityId, fieldId] = fieldKey.includes(separator) ? fieldKey.split(separator) : [null, fieldKey];

                // const transactionField = transactionFields.find(
                //     (f) => f.fieldId === fieldId && (f.collectionItemId ?? null) === collectionItemIdentityId,
                // );

                const transactionField = transactionFieldMap[`${fieldId}_${collectionItemIdentityId}`];

                const fieldType = (child as any).properties?.valueType?.[0];

                if (transactionField) {
                    formCollectionValues[fieldKey] = JsonLogicUtils.convertVariableValue(
                        transactionField?.fieldType ?? fieldType,
                        transactionField.fieldValue,
                        transactionField.fieldOptionIds,
                    );
                }

                this._processDependencyFields([child], transactionFields, formCollectionValues);
            }

            //TODO: convert visibility value
            const inVisible = !this._evaluateConditions(field.setting?.visibility as any, formCollectionValues);

            const collectionItemFields = groupByCollectionItem[field.identityId] ?? [];

            collectionItemFields.forEach((tf) => {
                tf.inVisible = inVisible;
                result.push(tf);
            });
        }

        return result;
    }

    private _processDependencyFields(children: any[], transactionFields: TransactionFieldEntity[], values: Record<string, any>) {
        for (const child of children) {
            const dependencyFieldId = child?.properties?.value?.[0];
            if (isUUID(dependencyFieldId)) {
                const dependField = transactionFields.find((f) => f.fieldId === dependencyFieldId);
                const fieldType = child.properties?.valueType?.[0];
                if (dependField) {
                    values[dependencyFieldId] = JsonLogicUtils.convertVariableValue(
                        fieldType,
                        dependField.fieldValue,
                        dependField.fieldOptionIds,
                    );
                }
            }
        }
    }

    private _evaluateConditions(conditions: JsonTree, values: Record<string, any>) {
        const engine = new LogicEngine();

        const formatConditions = formatCondition(conditions) as any;
        const jsonLogic = QbUtils.jsonLogicFormat(QbUtils.loadTree(formatConditions as JsonTree), CoreConfig);
        if (jsonLogic.logic) {
            const evalResult = engine.run(jsonLogic.logic, values);
            return evalResult;
        }

        return true;
    }

    private async updateTransactionData(
        transaction: TransactionEntity,
        updateFields: TransactionFieldEntity[],
        updateFieldStyles: Array<TransactionFieldStyleEntity & { rowKey?: string }>,
        isTest: boolean,
        styleAndOverrideFields?: StyleAndOverrideFields,
    ) {
        console.time('updateTransactionData');
        transaction.transactionFields = undefined;
        transaction.transactionFieldStyles = undefined;

        updateFields?.forEach((field) => {
            if (typeof field.fieldValue === 'object' && !!field?.fieldValue && 'inspectionId' in field?.fieldValue) {
                field.fieldValue = 'Sire2Answer';
            }
        });

        console.time('save transsss');

        //TODO: Need to check reason why duplicate field when upsert
        updateFields = updateFields?.filter((item, index, self) => index === self.findIndex((t) => t.id === item.id));
        updateFields = updateFields.map((f) => {
            // Get TypeORM metadata for TransactionFieldEntity to identify UUID columns
            const metadata = this.transactionFieldRepository.metadata;
            const uuidColumnPropertyNames = metadata.columns
                .filter(
                    (col) =>
                        // Check if the column is a primary generated UUID or explicitly typed as UUID
                        col.generationStrategy === 'uuid' || (typeof col.type === 'string' && col.type.toLowerCase() === 'uuid'),
                )
                .map((col) => col.propertyName); // Get the corresponding property name in the entity class

            // Iterate through the identified UUID property names for the current field object 'f'
            uuidColumnPropertyNames.forEach((columnName) => {
                // Check if the field object 'f' has this UUID property and if its value is the string "null"
                if (f.hasOwnProperty(columnName) && f[columnName] === 'null') {
                    // Set the value to actual null
                    f[columnName] = null;
                }
            });

            // Return the potentially modified field object
            return f;
        });

        const chunkSize = 50;
        const updateFieldChunks = _.cloneDeep(chunk(updateFields, chunkSize));

        const updateTransactionPayload = {
            ...transaction,
            transactionFields: undefined,
        };
        await this.formTransactionRepository.update(transaction.id, updateTransactionPayload);

        await Promise.all(
            updateFieldChunks.map((fieldsChunk) => this.transactionFieldRepository.upsert(fieldsChunk, { conflictPaths: ['id'] })),
        );

        const updatedTran = await this.formTransactionRepository.findOne({
            where: { id: transaction.id },
            withDeleted: isTest,
        });
        console.timeEnd('save transsss');

        let updatedFieldStyles = [];

        const [collectionFields, formFields] = _.partition(
            updateFields,
            (f) => f.contextType === TransactionFieldContextTypeEnum.COLLECTION,
        );

        const collectionFieldsMap = collectionFields.reduce(
            (prev, curr) => {
                prev[`${curr.rowKey}_${curr.fieldId}`] = curr;
                return prev;
            },
            {} as Record<string, TransactionFieldEntity>,
        );

        if (updateFieldStyles?.length) {
            const uniqUpdateFieldStyleDict = new Map<string, TransactionFieldStyleEntity>();

            console.time('save styles');
            updateFieldStyles.forEach((item) => {
                let tranField = formFields.find((f) => f.fieldId === item.fieldId && !item.rowKey);
                if (tranField) {
                    item.id = tranField.id;
                } else {
                    tranField = collectionFieldsMap[`${item.rowKey}_${item.fieldId}`];
                    if (tranField) {
                        item.id = tranField.id;
                    }
                }

                if (!item.id || !tranField) return;

                if (!uniqUpdateFieldStyleDict.has(item.id)) {
                    const { rowKey, ...fieldStyle } = item;
                    uniqUpdateFieldStyleDict.set(item.id, fieldStyle);
                } else {
                    const field = uniqUpdateFieldStyleDict.get(item.id);
                    if (field.configuration) {
                        field.configuration.icon = item.configuration.icon;
                        field.configuration.label = item.configuration.label;
                    } else {
                        field.configuration = {
                            icon: item.configuration.icon,
                            label: item.configuration.label,
                        };
                    }
                }
            });

            const fieldsToUpdate = Array.from(uniqUpdateFieldStyleDict.values());

            this.copyFieldStyles({
                transactionFieldStyles: fieldsToUpdate,
                updatedFields: updateFields,
                styleAndOverrideFields: styleAndOverrideFields,
            });

            const chunkSize = 100;
            const fieldsToUpdateChunks = chunk(fieldsToUpdate, chunkSize);

            await Promise.all(fieldsToUpdateChunks.map((chunk) => this.transactionFieldStyleRepository.upsert(chunk, ['id'])));

            updatedFieldStyles = await this.transactionFieldStyleRepository.find({
                where: {
                    id: In(fieldsToUpdate.map((f) => f.id)),
                },
            });
            console.timeEnd('save styles');
        }

        console.time('get after save trans');
        const newFields = await this.transactionFieldRepository.find({
            where: {
                transactionId: transaction.id,
            },
        });
        console.timeEnd('get after save trans');
        console.timeEnd('updateTransactionData');

        return { updatedTran, updatedFields: newFields, updatedFieldStyles };
    }

    private async handleTransactionUpdated(
        formVersion: FormVersionTenancyEntity,
        updatedResult: { updatedTran: TransactionEntity; updatedFields: TransactionFieldEntity[]; updatedFieldStyles: any[] },
        transaction: TransactionEntity,
    ) {
        const stage = formVersion.stages.find((s) => s.id === transaction.stageId);
        if (!stage) {
            console.error('STAGE_NOT_FOUND');
            return;
        }
        this.processRelatedFieldsChange(formVersion, transaction.id, updatedResult.updatedFields, stage.identityId);
    }

    private async _processPopulateAndValidateCollections({
        activeFormVersionId,
        transactionId,
        formValues,
        triggerFields,
        lockedFieldIds = [],
        onlyUpdateIfRelevantRegisterRecordIds = [],
        cachedFormVersion,
        transactionFields,
        updatingCollectionFields,
        isModify,
        purpleTRACPopulatedData,
        targetFieldChangeIds,
        targetFieldChangeFromRegisterId,
        payloadDocuments,
        defaultCollectionValuesOnCreation,
        isTest,
    }: {
        activeFormVersionId: string;
        transactionId: string;
        formValues: Record<string, any>;
        triggerFields?: string[];
        lockedFieldIds?: string[];
        onlyUpdateIfRelevantRegisterRecordIds?: string[];
        cachedFormVersion?: any;
        transactionFields?: TransactionFieldEntity[];
        updatingCollectionFields?: UpdatePopulateCollectionParam['updatingCollectionFields'];
        purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>;
        isModify?: boolean;
        targetFieldChangeIds?: string[];
        targetFieldChangeFromRegisterId?: string;
        payloadDocuments?: Record<string, string>;
        defaultCollectionValuesOnCreation: Record<string, string>;
        isTest: boolean;
    }): Promise<ProcessPopulateCollectionResult> {
        const request: AutoPopulateAndCheckValidation = {
            activeFormVersionId,
            transactionId,
            ignoreSave: true,
            formValues,
            triggerFields,
            lockedFieldIds,
            cachedFormVersion,
            purpleTRACPopulatedData,
            targetFieldChangeIds,
            targetFieldChangeFromRegisterId,
            payloadDocuments,
            isTest,
        };

        //run for all fields in form
        console.time('_process.PopulateAndValidateCollections_autoPopulateFields');
        const {
            populateCollectionFields: autoPopulateFields,
            runCollectionIds,
            styleAndOverrideFields,
        } = await this._autoPopulateAndCheckValidationCollectionFieldService.autoPopulateCollectionFields(request);
        console.timeEnd('_process.PopulateAndValidateCollections_autoPopulateFields');

        // save to db
        console.time('_process.PopulateAndValidateCollections_updatePopulateCollectionFields');
        let updateResult = await this._updatePopulateCollectionFields({
            populateCollectionFields: autoPopulateFields,
            transactionId,
            runCollectionIds,
            lockedFieldIds,
            onlyUpdateIfRelevantRegisterRecordIds,
            transactionFields,
            updatingCollectionFields, // Add the missing parameter
            isModify,
            defaultCollectionValuesOnCreation,
        });
        console.timeEnd('_process.PopulateAndValidateCollections_updatePopulateCollectionFields');

        return {
            transactionFields: updateResult?.updatedTransactionFields || [],
            collectionChangeFields: updateResult?.collectionChangeFields || [],
            populatedCollectionIds: runCollectionIds,
            deletedCollectionFieldIds: updateResult?.deletedCollectionFieldIds || new Set(),
            styleAndOverrideFields: styleAndOverrideFields || {},
        };
    }

    private async _updatePopulateCollectionFields({
        populateCollectionFields,
        transactionId,
        runCollectionIds,
        lockedFieldIds,
        onlyUpdateIfRelevantRegisterRecordIds,
        //current transaction fields, required to not re get from db
        transactionFields: transactionFieldsParams,
        updatingCollectionFields,
        defaultCollectionValuesOnCreation,
        isModify,
    }: UpdatePopulateCollectionParam & { isModify?: boolean }): Promise<AfterPopulateCollectionResult> {
        const query = {
            collectionIds: [],
            collectionItemIds: [],
            fieldIds: [],
        };

        populateCollectionFields.forEach((field) => {
            query.collectionIds.push(field.collectionId);
            query.collectionItemIds.push(field.collectionItemId);
            query.fieldIds.push(field.fieldId);
        });

        const cloneTransactionFields = _.cloneDeep(transactionFieldsParams || []);
        const collectionFieldsParam = (transactionFieldsParams || []).filter(
            (f) => f.contextType === TransactionFieldContextTypeEnum.COLLECTION,
        );

        const transactionFields = collectionFieldsParam || [];

        const normalCollectionFields: Partial<TransactionFieldEntity>[] = [];

        const { externalCollectionTransactionFields, normalMap } = (populateCollectionFields || []).reduce(
            (prev, field) => {
                const key = `${field.collectionId}_${field.collectionItemId}_${field.fieldId}`;
                if (field.data?.collectionItemExternalId) {
                    prev.externalCollectionTransactionFields.push(field);
                    prev.externalMap.set(key, field);
                } else {
                    prev.normalCollectionTransactionFields.push(field);
                    prev.normalMap.set(key, field);
                }
                return prev;
            },
            {
                externalCollectionTransactionFields: [] as PopulateCollectionField[],
                normalCollectionTransactionFields: [] as PopulateCollectionField[],
                normalMap: new Map(),
                externalMap: new Map(),
            },
        );

        const collectionFieldMap = new Map<string, TransactionFieldEntity[]>();
        const collectionFieldOnRowMap = new Map<string, TransactionFieldEntity[]>();
        const externalCollectionItemSet = new Set<string>();
        (transactionFields || []).forEach((field) => {
            if (field.contextType === 'collection') {
                const cItemKey = `${field.collectionId}_${field.collectionItemId}`;
                const cItemRowKey = `${field.collectionId}_${field.collectionItemId}_${field.rowKey}`;

                if (!collectionFieldMap.has(cItemKey)) {
                    collectionFieldMap.set(cItemKey, []);
                }

                if (!collectionFieldOnRowMap.has(cItemRowKey)) {
                    collectionFieldOnRowMap.set(cItemRowKey, []);
                }

                collectionFieldMap.get(cItemKey).push(field);
                collectionFieldOnRowMap.get(cItemRowKey).push(field);
            }

            if (field.data?.collectionItemExternalId && field.collectionItemId) {
                externalCollectionItemSet.add(field.collectionItemId);
            }

            const key = `${field.collectionId}_${field.collectionItemId}_${field.fieldId}`;
            const transactionField = normalMap.get(key);

            if (!field.data?.collectionItemExternalId) {
                if (transactionField) {
                    field.fieldValue = transactionField.fieldValue as string;
                    normalCollectionFields.push({
                        id: field.id,
                        transactionId: field.transactionId,
                        fieldId: transactionField.fieldId,
                        fieldValue: transactionField.fieldValue as string,
                        fieldOptionIds: transactionField.fieldOptionIds || [],
                        registerRecordId: transactionField.registerRecordId,
                        collectionId: transactionField.collectionId,
                        collectionItemId: transactionField.collectionItemId,
                        rowKey: field.rowKey || transactionField.rowKey,
                        pairId: field.pairId || transactionField.pairId,
                        parentId: field.parentId || transactionField.parentId,
                        fieldType: transactionField?.fieldType,
                        contextType: field.contextType,
                    });
                } else if (updatingCollectionFields?.length) {
                    const updateCollectionField = updatingCollectionFields.find(
                        (f) =>
                            f.collectionId === field.collectionId &&
                            f.collectionItemId === field.collectionItemId &&
                            f.fieldId === field.fieldId,
                    );

                    if (updateCollectionField) {
                        normalCollectionFields.push({
                            id: field.id,
                            transactionId: field.transactionId,
                            fieldId: updateCollectionField.fieldId,
                            fieldValue: updateCollectionField.fieldValue as string,
                            fieldOptionIds: SELECTABLE_FIELD_TYPES.includes(updateCollectionField.fieldType)
                                ? (updateCollectionField.fieldValue ?? '').split(',')
                                : [],
                            registerRecordId: field.registerRecordId,
                            collectionId: field.collectionId,
                            collectionItemId: field.collectionItemId,
                            rowKey: field.rowKey,
                            pairId: field.pairId,
                            parentId: field.parentId,
                            contextType: field.contextType,
                        });
                    }
                }
            }
        });

        let removeExternalTranFields: TransactionFieldEntity[] = [];
        const saveExternalTranFields: TransactionFieldEntity[] = [];

        const mapParentObj = (transactionFields || []).reduce(
            (prev, curr) => {
                if (curr.collectionId && curr.collectionItemId) {
                    prev[`${curr.collectionId}_${curr.collectionItemId}`] = {
                        pairId: curr.pairId,
                        parentId: curr.parentId,
                    };
                }
                return prev;
            },
            {} as Record<string, { pairId: string; parentId: string }>,
        );

        const mutateExternalCollectionFields: {
            [collectionId: string]: {
                changes: {
                    [rowKey: string]: TransactionFieldEntity[];
                };
            };
        } = {};

        const retainCollectionItemRowMap = new Map<string, Set<string>>();
        const collectionItemParentMap: Map<string, { parentId: string; pairId: string }> = new Map();
        if (externalCollectionTransactionFields?.length) {
            const externalCollectionGroup = _.groupBy(externalCollectionTransactionFields, 'collectionId') || [];
            Object.entries(externalCollectionGroup ?? {}).forEach(([collectionId, rows]) => {
                const externalCollectionItemGroup = _.groupBy(rows as PopulateCollectionField[], 'collectionItemId');
                mutateExternalCollectionFields[collectionId] = {
                    changes: {},
                };

                const changeFields: TransactionFieldEntity[] = [];

                Object.entries(externalCollectionItemGroup ?? {}).forEach(([collectionItemId, populateFields]) => {
                    const { existedRowKeys, populateFieldsMap } = (populateFields || []).reduce(
                        (prev, item) => {
                            const key = `${item.fieldId}_${item.data?.collectionItemExternalId}`;
                            prev.populateFieldsMap.set(key, item);
                            prev.existedRowKeys.set(item.rowKey, true);
                            return prev;
                        },
                        {
                            populateFieldsMap: new Map(),
                            existedRowKeys: new Map(),
                        },
                    );

                    const collectionItemTranFields = collectionFieldMap.get(`${collectionId}_${collectionItemId}`) || [];

                    const retainCollectionItemRowKeysSet = new Set();
                    const removeCollectionItemTranFields = (collectionItemTranFields || []).filter((f) => {
                        const rowKey = f.rowKey;
                        const collectionItemRowKey = `${collectionId}_${collectionItemId}`;
                        if (!retainCollectionItemRowMap.has(collectionItemRowKey)) {
                            retainCollectionItemRowMap.set(collectionItemRowKey, new Set());
                        }

                        if (!f.data?.collectionItemExternalId) {
                            const isRemoved = !existedRowKeys.has(rowKey);
                            if (!isRemoved) {
                                retainCollectionItemRowKeysSet.add(rowKey);
                                retainCollectionItemRowMap.get(collectionItemRowKey).add(rowKey);
                            }
                            return isRemoved;
                        }

                        const key = `${f.fieldId}_${f.data?.collectionItemExternalId}`;
                        const isRemoved = !populateFieldsMap.has(key);
                        if (!isRemoved) {
                            retainCollectionItemRowKeysSet.add(rowKey);
                            retainCollectionItemRowMap.get(collectionItemRowKey).add(rowKey);
                        }
                        return isRemoved;
                    });

                    removeExternalTranFields.push(...removeCollectionItemTranFields);

                    const spawnRowKeys = new Set();
                    // const rowDataMap: Map<string, any> = new Map();
                    const populatedFieldIdMap: Map<string, boolean> = new Map();

                    const saveCollectionItemTranFields = populateFields
                        .filter((e) => {
                            return !removeCollectionItemTranFields.some(
                                (f) => e.fieldId === f.fieldId && e.data?.collectionItemExternalId === f.data?.collectionItemExternalId,
                            );
                        })
                        .map((e) => {
                            const mapParent = mapParentObj[`${collectionId}_${collectionItemId}`];
                            const existed = collectionItemTranFields.find(
                                (f) => f.fieldId === e.fieldId && f.data?.collectionItemExternalId === e.data?.collectionItemExternalId,
                            );

                            if (!retainCollectionItemRowKeysSet.has(e.rowKey)) {
                                //just need add for new row key
                                spawnRowKeys.add(e.rowKey);
                            }

                            collectionItemParentMap.set(collectionItemId, {
                                parentId: mapParent?.parentId,
                                pairId: mapParent?.pairId,
                            });
                            populatedFieldIdMap.set(e.fieldId, true);

                            const data = {
                                collectionItemExternalId: e.data?.collectionItemExternalId,
                                externalContextId: e.data?.externalContextId,
                                externalOrder: e.data?.externalOrder,
                                fieldValue: e.data?.fieldValue,
                            };
                            // rowDataMap.set(e.rowKey, data);

                            const result = {
                                id: existed?.id || v4(),
                                collectionId,
                                collectionItemId,
                                fieldId: e.fieldId,
                                fieldValue: e.fieldValue,
                                fieldOptionIds: e.fieldOptionIds,
                                rowKey: e.rowKey,
                                transactionId,
                                contextType: TransactionFieldContextTypeEnum.COLLECTION,
                                data,
                                parentId: mapParent?.parentId,
                                pairId: mapParent?.pairId,
                            } satisfies TransactionFieldEntity;

                            let existedValue = existed?.fieldValue === 'Sire2Answer' ? existed.data?.fieldValue : existed?.fieldValue;
                            if (typeof existedValue === 'object') {
                                existedValue = JSON.stringify(existedValue);
                            }

                            let poplatedValue = e?.fieldValue === 'Sire2Answer' ? e.data?.fieldValue : e?.fieldValue;
                            if (typeof poplatedValue === 'object') {
                                poplatedValue = JSON.stringify(poplatedValue);
                            }

                            if (!existed || (existed && existedValue !== poplatedValue)) {
                                changeFields.push(result);
                            }

                            return result;
                        });

                    saveExternalTranFields.push(...saveCollectionItemTranFields);

                    const collectionItemDefaultFields =
                        (defaultCollectionValuesOnCreation?.[collectionId]?.[collectionItemId] as TransactionFieldEntity[]) || [];
                    if (spawnRowKeys?.size && collectionItemDefaultFields?.length) {
                        Array.from(spawnRowKeys).forEach((r: string) => {
                            if (!collectionItemDefaultFields.length) {
                                return;
                            }
                            const newCollectionItemDefaultFields: TransactionFieldEntity[] = [];
                            collectionItemDefaultFields.forEach((df) => {
                                if (populatedFieldIdMap.has(df.fieldId)) {
                                    return;
                                }
                                // const externalRowData = rowDataMap.get(r) ?? {};
                                const newField = {
                                    id: v4(),
                                    collectionId,
                                    collectionItemId,
                                    fieldId: df.fieldId,
                                    fieldValue: df.fieldValue,
                                    fieldOptionIds: df.fieldOptionIds,
                                    rowKey: r,
                                    transactionId,
                                    contextType: 'collection',
                                    parentId: collectionItemParentMap.get(collectionItemId)?.parentId,
                                    pairId: collectionItemParentMap.get(collectionItemId)?.pairId,
                                    fieldType: df.fieldType,
                                } as TransactionFieldEntity & Partial<{ fieldType?: FormFieldTypeEnum }>;

                                newCollectionItemDefaultFields.push(newField);

                                const collectionRowKey = `${collectionId}_${collectionItemId}_${r}`;
                                if (!collectionFieldOnRowMap.has(collectionRowKey)) {
                                    collectionFieldOnRowMap.set(collectionRowKey, []);
                                }
                                collectionFieldOnRowMap.get(collectionRowKey).push(newField);
                            });
                            saveExternalTranFields.push(...newCollectionItemDefaultFields);
                        });
                    }
                });

                if (changeFields.length) {
                    mutateExternalCollectionFields[collectionId].changes = {
                        ..._.groupBy(changeFields, 'rowKey'),
                    };
                }
            });
        } else {
            //remove all external collection fields
            removeExternalTranFields = (transactionFields || []).filter(
                (tf) =>
                    runCollectionIds.includes(tf.collectionId) && tf.collectionItemId && externalCollectionItemSet.has(tf.collectionItemId),
            );
        }

        if (!_.isEmpty(mutateExternalCollectionFields) && updatingCollectionFields?.length && collectionFieldOnRowMap?.size) {
            updatingCollectionFields.forEach((mapping) => {
                const { collectionId, collectionItemId, fieldId, fieldType, fieldValue } = mapping;
                const byCollection = mutateExternalCollectionFields[collectionId];
                if (!byCollection || isEmpty(byCollection?.changes)) return;

                const rowChanges = byCollection.changes;

                const changedRowKeys = new Set(Object.keys(rowChanges));

                const needUpdateFields: TransactionFieldEntity[] = [];

                for (const rowKey of changedRowKeys) {
                    const key = `${collectionId}_${collectionItemId}_${rowKey}`;
                    const collectionFields = (collectionFieldOnRowMap.get(key) || []).filter((f) => f.fieldId === fieldId);

                    if (collectionFields.length) {
                        needUpdateFields.push(...collectionFields);
                    } else {
                        const collectionItemRowKey = `${collectionId}_${collectionItemId}`;
                        if (!retainCollectionItemRowMap.get(collectionItemRowKey)?.has(rowKey)) {
                            const parentMap = collectionItemParentMap.get(collectionItemId);
                            needUpdateFields.push({
                                id: v4(),
                                collectionId,
                                collectionItemId,
                                fieldId,
                                fieldValue,
                                fieldOptionIds: fieldValue ? fieldValue.split(',') : [],
                                rowKey,
                                transactionId,
                                contextType: TransactionFieldContextTypeEnum.COLLECTION,
                                parentId: parentMap?.parentId,
                                pairId: parentMap?.pairId,
                                fieldType,
                            });
                        }
                    }
                }

                if (!needUpdateFields.length) {
                    return;
                }

                needUpdateFields.forEach((nuf) => {
                    if (!nuf.fieldType) {
                        nuf.fieldType = fieldType;
                    }
                    if (SELECTABLE_FIELD_TYPES.includes(fieldType)) {
                        nuf.fieldOptionIds = fieldValue ? fieldValue.split(',') : [];
                        nuf.fieldValue = fieldValue;
                    } else {
                        nuf.fieldValue = fieldValue;
                    }
                });

                saveExternalTranFields.push(...needUpdateFields);
            });
        }

        try {
            const collectionChangeFields: TransactionFieldEntity[] = [];
            const normalFieldMap = new Map<string, TransactionFieldEntity>();
            if (normalCollectionFields?.length) {
                // await this.transactionFieldRepository.upsert(tasks, ['id']);
                normalCollectionFields.forEach((task) => {
                    if (task.id) {
                        normalFieldMap.set(task.id, task as TransactionFieldEntity);
                        collectionChangeFields.push(task as TransactionFieldEntity);
                    }
                });
            }

            const chunkSize = 200;
            const now = new Date();
            const filterLockedFields = (saveExternalTranFields || []).filter((f) => !lockedFieldIds.includes(f.fieldId));
            let filteredFields = filterLockedFields;
            if (onlyUpdateIfRelevantRegisterRecordIds?.length) {
                filteredFields = filterLockedFields.filter((f) => onlyUpdateIfRelevantRegisterRecordIds.includes(f.registerRecordId));
            }

            /**
             * Each key is combine of  collectionId_collectionItemId_rowKey_fieldId
             */
            let removeFieldIdSet = new Set<string>();
            if (removeExternalTranFields.length) {
                removeFieldIdSet = new Set(
                    removeExternalTranFields
                        .map((f) => (f.rowKey ? `${f.collectionId}_${f.collectionItemId}_${f.rowKey}_${f.fieldId}` : null))
                        .filter(Boolean),
                );

                const removeChunks = chunk(removeExternalTranFields, chunkSize);
                for (const removeChunk of removeChunks) {
                    const entities = removeChunk.map((field) => {
                        return {
                            id: field.id,
                            fieldId: field.fieldId,
                            transactionId,
                            deletedAt: now,
                        };
                    });
                    await this.transactionFieldRepository.upsert(entities, ['id']);
                }
            }

            let result = cloneTransactionFields;
            if (removeFieldIdSet?.size) {
                result = result.filter((field) => {
                    if (!field.rowKey) return true;
                    const key = `${field.collectionId}_${field.collectionItemId}_${field.rowKey}_${field.fieldId}`;
                    return !removeFieldIdSet.has(key);
                });
            }

            if (normalFieldMap?.size) {
                result = result.map((field) => {
                    const normalField = normalFieldMap.get(field.id);
                    if (normalField) {
                        field.fieldValue = normalField.fieldValue;
                        field.fieldOptionIds = normalField.fieldOptionIds;
                        field.registerRecordId = normalField.registerRecordId;
                    }
                    return field;
                });
            }

            const resultMap = result?.reduce(
                (prev, curr) => {
                    prev[`${curr.rowKey}_${curr.fieldId}`] = curr;
                    return prev;
                },
                {} as Record<string, TransactionFieldEntity>,
            );

            if (filteredFields?.length) {
                filteredFields.forEach((field) => {
                    const key = `${field.rowKey}_${field.fieldId}`;
                    const existed = resultMap[key];

                    field.id = field.id || v4();

                    if (!existed) {
                        result.push(field);
                    } else {
                        existed.fieldValue = field.fieldValue;
                        existed.fieldOptionIds = field.fieldOptionIds;
                    }

                    collectionChangeFields.push(field);
                });
            }
            return { updatedTransactionFields: result, collectionChangeFields, deletedCollectionFieldIds: removeFieldIdSet };
        } catch (err) {
            this._logger.error(err);
            return { updatedTransactionFields: transactionFields, collectionChangeFields: [], deletedCollectionFieldIds: new Set() };
        }
    }

    private async _updateCollectionValidation({
        criteriaValidation,
        collectionValidation,
        transactionFields,
        transactionId,
    }: {
        criteriaValidation: CheckValidationResponseType[];
        collectionValidation: CheckValidationResponseType[];
        transactionFields: TransactionFieldEntity[];
        transactionId: string;
    }) {
        const fieldStyle: TransactionFieldStyleEntity[] = [];
        const transactionFieldIds: string[] = [];
        const fieldMap = new Map<string, CheckValidationResponseType>();

        criteriaValidation?.forEach((field) => {
            transactionFieldIds.push(field.transactionFieldStyleId);
            fieldMap.set(field.transactionFieldStyleId, field);

            fieldStyle.push({
                id: field.transactionFieldStyleId,
                configuration: field.configuration,
                fieldId: field.fieldId,
                transactionId: transactionId,
            });
        });

        collectionValidation?.forEach((field) => {
            transactionFieldIds.push(field.transactionFieldStyleId);
            fieldMap.set(field.transactionFieldStyleId, field);

            fieldStyle.push({
                id: field.transactionFieldStyleId,
                configuration: field.configuration,
                fieldId: field.fieldId,
                transactionId: transactionId,
            });
        });

        if (!transactionFieldIds?.length) {
            return;
        }

        if (transactionFields?.length) {
            const tasks = [];
            const styleTasks = [];

            transactionFields.forEach((entity) => {
                const field = fieldMap.get(entity.id);
                if (field) {
                    tasks.push({
                        id: entity.id,
                        fieldId: field.fieldId,
                        validationValue: field.validationValue,
                        transactionId: entity.transactionId,
                    });

                    styleTasks.push({
                        id: entity.id,
                        fieldId: entity.fieldId,
                        transactionId: entity.transactionId,
                        configuration: field.configuration,
                    });
                }
            });

            if (tasks?.length) {
                await this.transactionFieldRepository.upsert(tasks, ['id']);
            }

            if (styleTasks?.length) {
                await this.transactionFieldStyleRepository.upsert(styleTasks, ['id']);
            }
        }
    }

    // TODO: LS-2280
    private async getCollectionStyles({
        formVersionId,
        transactionId,
        tranFields,
        requestTransactionFields = [],
        additionalFields = [],
        cachedFormVersion,
    }: {
        formVersionId: string;
        transactionId: string;
        tranFields: TransactionFieldEntity[];
        requestTransactionFields: EditFormTransactionFieldRequest[];
        additionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
        cachedFormVersion: any;
    }): Promise<
        {
            fieldId: string;
            code: DataValidationResult;
            label: string;
            rowKey: string;
            collectionId: string;
            collectionItemIdentityId: string;
        }[]
    > {
        if (!tranFields?.length || !formVersionId) {
            return null;
        }
        const cloneTranFields = _.cloneDeep(tranFields || []);

        const requestTransactionFieldMap = requestTransactionFields.reduce(
            (prev, curr) => {
                prev[`${curr.fieldId}_${curr.rowKey}`] = curr;
                return prev;
            },
            {} as Record<string, EditFormTransactionFieldRequest>,
        );

        const additionalFieldMap = additionalFields.reduce(
            (prev, curr) => {
                prev[`${curr.fieldId}`] = curr;
                return prev;
            },
            {} as Record<string, FormCollectionAdditionalFieldTenancyEntity>,
        );

        cloneTranFields.forEach((item) => {
            const requestTranField = requestTransactionFieldMap[`${item.fieldId}_${item.rowKey}`];
            if (!requestTranField) return;
            let itemFieldType = requestTranField.fieldType;
            if ([FormFieldTypeEnum.Answer, FormFieldTypeEnum.Comparison, FormFieldTypeEnum.Definable].includes(itemFieldType)) {
                const additionalField = additionalFieldMap[`${item.fieldId}`];
                if (additionalField) {
                    itemFieldType = additionalField?.type;
                }
            }
            if (OBJECT_SELECTABLE_FIELD_TYPES.includes(itemFieldType)) {
                const value = item.fieldValue;
                // if all element of fieldValue separated by comma is uuid
                if (value && value.includes(',')) {
                    item.fieldValue = value.split(',') as any;
                }
            }
        });

        // Can be collection or criteria
        const collectionTypeFormFields = cloneTranFields.filter((f) => f.contextType === TransactionFieldContextTypeEnum.COLLECTION);

        const collectionItemIds = _.compact(uniq(collectionTypeFormFields.map((f) => f.collectionItemId)));

        if (!collectionItemIds?.length) {
            return [];
        }

        const formCollections: FormCollectionTenancyEntity[] = cachedFormVersion?.formCollections?.length
            ? cachedFormVersion.formCollections
            : await this._formCollectionRepository
                  .createQueryBuilder('collections')
                  .leftJoinAndSelect('collections.formCollectionItems', 'formCollectionItems')
                  .where('collections.formVersionId = :formVersionId', { formVersionId: formVersionId })
                  .andWhere('formCollectionItems.identityId IN (:...collectionItemIds)', {
                      collectionItemIds: collectionItemIds,
                  })
                  .getMany();

        const collectionFormFields = collectionTypeFormFields;

        const validationResultWithStyles: {
            fieldId: string;
            code: DataValidationResult;
            label: string;
            border?: number;
            rowKey: string;
            collectionId: string;
            collectionItemIdentityId: string;
        }[] = [];

        // COLLECTIONS
        if (!collectionFormFields?.length) {
            return validationResultWithStyles;
        }

        const collectionFormFieldIds = _.compact(_.uniq(collectionFormFields.map((f) => f.fieldId)));

        const dataRegisterVersionIds = _.compact(
            _.uniq(formCollections.filter((c) => c.type === DataRegisterTypeEnum.Collection)?.flatMap((c) => c.dataRegisterVersionId)),
        );

        const fieldValidations = await this._checkCriteriaValidationService._getDataRegisterFields({
            fieldIds: collectionFormFieldIds,
            dataRegisterVersionIds,
            formVersionId,
            additionalFields: cachedFormVersion?.collectionAdditionalFields || [],
        });

        if (!fieldValidations?.normalFields?.length && !fieldValidations?.additionalFields?.length) {
            return validationResultWithStyles;
        }

        const validateResult = await this._getCollectionValidateResult({
            transactionId,
            formVersionId,
            collectionType: DataRegisterTypeEnum.Collection,
            tranFields: cloneTranFields,
            collectionTransFields: collectionFormFields,
            formCollections,
            cachedFormVersion: cachedFormVersion,
        });

        if (validateResult?.length) {
            validateResult.forEach((item) => {
                validationResultWithStyles.push({
                    fieldId: item.fieldId,
                    code: item.validationValue,
                    label: item.label,
                    border: item.border,
                    rowKey: item.rowKey,
                    collectionId: item.collectionId,
                    collectionItemIdentityId: item.collectionItemId,
                });
            });
        }

        return validationResultWithStyles;
    }

    private async _getCollectionValidateResult({
        transactionId,
        formVersionId,
        collectionType,
        tranFields,
        collectionTransFields,
        formCollections,
        cachedFormVersion,
    }: {
        transactionId: string;
        formVersionId: string;
        collectionType: DataRegisterTypeEnum;
        tranFields: TransactionFieldEntity[];
        collectionTransFields: TransactionFieldEntity[];
        formCollections: FormCollectionTenancyEntity[];
        cachedFormVersion: any;
    }) {
        if (!collectionTransFields?.length) return [];
        const request: CheckCriteriaValidationRequest = {
            formValues: tranFields.reduce((prev, curr) => {
                if (curr.contextType === TransactionFieldContextTypeEnum.FORM) {
                    prev[curr.fieldId] = curr.fieldValue;
                } else {
                    const collection = formCollections.find((c) => c.identityId === curr.collectionId);
                    if (!collection) return prev;
                    const collectionItemId = collection?.formCollectionItems?.find((ci) => ci.identityId === curr.collectionItemId)?.id;
                    if (!collectionItemId) return prev;

                    const key = UtilsService.combineCollectionKeys({
                        collectionIdentityId: curr.collectionId,
                        collectionItemId: collectionItemId,
                        collectionItemIdentityId: curr.collectionItemId,
                        collectionItemKey: curr.rowKey,
                        fieldId: curr.id,
                        fieldIdentityId: curr.fieldId,
                    });
                    prev[key] = curr.fieldOptionIds?.length ? curr.fieldOptionIds : curr.fieldValue;
                }
                return prev;
            }, {}),
            formVersionId: formVersionId,
            collectionType: collectionType,
            transactionId: transactionId,
            cachedFormVersion: cachedFormVersion,
        };

        const checkValidationResult = await this._checkCriteriaValidationService.checkCriteriaValidation(request);
        return checkValidationResult;
    }

    public async runAutoPopulateForCollectionFields({
        reqTransactionFields,
        transactionFields,
        formVersionId,
        transactionId,
        formValues,
        forceRun,
        lockedFieldIds,
        onlyUpdateIfRelevantRegisterRecordIds,
        cachedFormVersion,
        updatingCollectionFields,
        purpleTRACPopulatedData,
        isModify,
        targetFieldChangeIds,
        targetFieldChangeFromRegisterId,
        payloadDocuments,
        defaultCollectionValuesOnCreation,
        isTest,
    }: {
        reqTransactionFields: EditFormTransactionFieldRequest[];
        transactionFields: TransactionFieldEntity[];
        formVersionId: string;
        transactionId: string;
        formValues: Record<string, any>;
        forceRun?: boolean;
        lockedFieldIds: string[];
        onlyUpdateIfRelevantRegisterRecordIds?: string[];
        cachedFormVersion?: any;
        updatingCollectionFields?: Array<UpdateCollectionFieldActionParam>;
        purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>;
        isModify?: boolean;
        payloadDocuments?: Record<string, string>;
        targetFieldChangeIds?: string[];
        targetFieldChangeFromRegisterId?: string;
        defaultCollectionValuesOnCreation: Record<string, any>;
        isTest: boolean;
    }) {
        try {
            let isEmitRollupEvent = false;
            const fieldValueChanges = reqTransactionFields?.filter(
                (reqField) =>
                    reqField.contextType !== TransactionFieldContextTypeEnum.COLLECTION && reqField.fieldId !== TRANSACTION_FIELD_ID,
            );

            const fieldValueChangeDict = fieldValueChanges.reduce((prev, curr) => {
                prev.set(curr.fieldId, curr);
                return prev;
            }, new Map());

            const formValueKeysDict = Object.keys(formValues ?? {}).reduce((prev, curr) => {
                prev[curr] = true;
                return prev;
            }, new Map());

            if (fieldValueChanges?.length || forceRun) {
                const cloneFormValues = _.cloneDeep(formValues ?? {});
                const tranFormFields = transactionFields.filter((f) => f.contextType !== TransactionFieldContextTypeEnum.COLLECTION);
                tranFormFields.forEach((tranField) => {
                    if (fieldValueChangeDict.has(tranField.fieldId)) return;
                    if (formValueKeysDict.has(tranField.fieldId)) return;
                    if (tranField.dependFieldId) {
                        return;
                    }

                    cloneFormValues[tranField.fieldId] = tranField?.fieldOptionIds?.length
                        ? tranField?.fieldOptionIds
                        : tranField.fieldValue;
                });

                const {
                    transactionFields: updatedTransactionFields,
                    collectionChangeFields = [],
                    populatedCollectionIds,
                    deletedCollectionFieldIds,
                    styleAndOverrideFields,
                } = await this._processPopulateAndValidateCollections({
                    activeFormVersionId: formVersionId,
                    transactionId: transactionId,
                    formValues: cloneFormValues,
                    triggerFields: forceRun ? [] : fieldValueChanges.map((f) => f.fieldId),
                    lockedFieldIds: lockedFieldIds,
                    onlyUpdateIfRelevantRegisterRecordIds,
                    cachedFormVersion: cachedFormVersion,
                    transactionFields: transactionFields,
                    updatingCollectionFields: updatingCollectionFields,
                    purpleTRACPopulatedData: purpleTRACPopulatedData,
                    isModify,
                    targetFieldChangeIds,
                    targetFieldChangeFromRegisterId,
                    payloadDocuments,
                    defaultCollectionValuesOnCreation,
                    isTest,
                });
                isEmitRollupEvent = !!updatedTransactionFields;

                return {
                    isEmitRollupEvent,
                    updatedTransactionFields,
                    populatedCollectionIds,
                    collectionChangeFields,
                    deletedCollectionFieldIds,
                    isChanged: true,
                    styleAndOverrideFields: styleAndOverrideFields || {},
                };
            }

            return { isEmitRollupEvent, isChanged: false, styleAndOverrideFields: {} };
        } catch (err) {
            this._logger.error(err);
            return null;
        }
    }

    public async runAutoPopulateForFormFields({
        requestTransactionFields,
        cloneTransactionFields,
        transaction,
        formVersion,
        transactionFields,
        formValues,
        updatingFormFields,
        purpleTRACPopulatedData,
        forceRun,
        payloadDocuments,
        targetFieldChangeIds,
    }: AutoPopulateFormFieldParam): Promise<AutoPopulateFormFieldsResult> {
        if (updatingFormFields?.length) {
            const requestFieldMap = _.reduce(
                requestTransactionFields,
                (prev, curr) => {
                    prev[curr.fieldId] = curr;
                    return prev;
                },
                {} as Record<string, EditFormTransactionFieldRequest>,
            );

            updatingFormFields.forEach((field) => {
                const requestField = requestFieldMap[field.fieldId];
                if (requestField) {
                    if (SELECTABLE_FIELD_TYPES.includes(field.fieldType)) {
                        requestField.fieldOptionIds = field.fieldValue;
                    } else {
                        requestField.fieldValue = field.fieldValue;
                    }
                } else {
                    requestFieldMap[field.fieldId] = field;
                }
            });

            requestTransactionFields = Object.values(requestFieldMap);
        }

        const fieldChangeIds =
            requestTransactionFields
                ?.filter((f) => f.contextType !== TransactionFieldContextTypeEnum.COLLECTION)
                ?.map((field) => field.fieldId) || [];

        if (!fieldChangeIds?.length && !forceRun) {
            return {
                updatedRequestTransactionFields: requestTransactionFields,
                updatedTransactionFields: transactionFields,
                formValues: formValues,
                isChanged: false,
            };
        }

        const formTransactionFields = _.cloneDeep(cloneTransactionFields).filter(
            (f) => f.contextType !== TransactionFieldContextTypeEnum.COLLECTION,
        );
        const requestTransactionFieldMap = _.groupBy(requestTransactionFields, 'fieldId');

        const requestTransactionFieldIdSet = new Set(Object.keys(requestTransactionFieldMap));

        const mergedTransactionFields = formTransactionFields.map((f) => {
            if (requestTransactionFieldMap[f.fieldId]) {
                f.fieldValue = requestTransactionFieldMap[f.fieldId]?.[0]?.fieldValue;
                f.fieldOptionIds = requestTransactionFieldMap[f.fieldId]?.[0]?.fieldOptionIds;
            }
            return f;
        });

        const { transactionFields: populatedResult, styleAndOverrideFields } =
            await this._generalAutoPopulateService.getPopulatedFormFieldValues({
                formVersionId: transaction.formVersionId,
                transaction: {
                    transactionFields: mergedTransactionFields?.map((f) => {
                        if (f.fieldOptionIds?.length) {
                            f.fieldValue = f.fieldOptionIds?.[0];
                        }
                        return f;
                    }) as any,
                },
                cachedFormVersion: formVersion,
                transactionId: transaction.id,
                activeStageId: transaction.stageId,
                fieldChangeIds,
                purpleTRACPopulatedData: purpleTRACPopulatedData,
                payloadDocuments,
                targetFieldChangeIds,
            });

        const populateValues = (populatedResult as any)?.populateValues;

        if (isEmpty(populateValues)) {
            return {
                updatedRequestTransactionFields: requestTransactionFields,
                updatedTransactionFields: transactionFields,
                formValues: formValues,
                isChanged: false,
            };
        }

        const metadata = (populatedResult as any)?.metadata;

        const formFields = formVersion.fields;
        const populatedFormFieldIds = Object.keys(populateValues ?? {});

        if (!formValues) {
            formValues = {};
        }

        Object.keys(populateValues ?? {}).forEach((fieldId) => {
            const fieldValueObj = populateValues[fieldId];
            const fieldMetadata = metadata?.find((f) => f.fieldId === fieldId);
            if (!fieldValueObj || Array.isArray(fieldValueObj?.fieldValue ?? '')) return;
            const formField = formFields.find((f) => f.fieldId === fieldId);
            const transactionField = transactionFields?.find((item) => item.fieldId === fieldId);
            if (transactionField) {
                transactionField.fieldValue = fieldValueObj?.fieldValue;
                transactionField.fieldOptionIds = fieldValueObj?.fieldOptionIds || [];
                transactionField.fieldType = formField?.type as FormFieldTypeEnum;
                if (formField.type === FormFieldTypeEnum.Document) {
                    transactionField.data = {
                        docId: fieldMetadata?.docId,
                        dataSourceType: fieldMetadata?.dataSourceType,
                        docFieldType: fieldMetadata?.docFieldType,
                        filePath: fieldMetadata?.filePath,
                    };
                }

                if (!requestTransactionFieldIdSet.has(fieldId)) {
                    requestTransactionFields.push(transactionField);
                    formValues[fieldId] = fieldValueObj?.fieldOptionIds ? fieldValueObj?.fieldOptionIds : fieldValueObj?.fieldValue;
                }
            } else {
                const populateField = {
                    id: v4(),
                    transactionId: transaction.id,
                    fieldOptionIds: fieldValueObj?.fieldOptionIds,
                    fieldId,
                    fieldValue: fieldValueObj?.value,
                    fieldType: formField?.type as FormFieldTypeEnum,
                    data: fieldMetadata
                        ? {
                              docId: fieldMetadata?.docId,
                              dataSourceType: fieldMetadata?.dataSourceType,
                              docFieldType: fieldMetadata?.docFieldType,
                              filePath: fieldMetadata?.filePath,
                          }
                        : undefined,
                } satisfies TransactionFieldEntity;

                transactionFields.push(populateField);

                if (!requestTransactionFieldIdSet.has(fieldId)) {
                    requestTransactionFields.push(populateField);
                    formValues[fieldId] = fieldValueObj?.fieldOptionIds ? fieldValueObj?.fieldOptionIds : fieldValueObj?.fieldValue;
                }
            }
        });
        return {
            updatedRequestTransactionFields: requestTransactionFields,
            updatedTransactionFields: transactionFields,
            formValues: formValues,
            isChanged: true,
            populatedFormFieldIds,
            styleAndOverrideFields: styleAndOverrideFields,
        };
    }

    //#endregion PRIVATE METHODS

    public async addTransactionRecordChangeLog({
        data,
        accountId,
        objectType,
        subObjectType,
        metadata,
    }: {
        data: TransactionRecordChangeLogMessageDto;
        objectType: TransactionDataType;
        accountId?: string;
        subObjectType?: TransactionDataType;
        metadata?: Record<string, any>;
    }): Promise<void> {
        if (isEmpty(data) || isEmpty(data.actionType) || isEmpty(data.current)) {
            throw new Error('Invalid data register record change log');
        }

        try {
            const dataSource = await this._getDataSource(accountId);
            const { transChangeLogRepo } = this._getRepositories(dataSource, accountId);
            const { sourceOfChange, actionType, current, previous } = data;

            // Ignore dependent fields in change log
            current.fields = current?.fields?.filter((f) => !f.dependFieldId);
            if (previous) {
                previous.fields = previous?.fields?.filter((f) => !f.dependFieldId);
            }

            const changeLogData: TransactionRecordChangeLogDto = {
                current,
                previous,
                sourceOfChange,
                actionType,
                objectType,
                objectId: current.id,
                subObjectType,
            };
            let newChangeLog = transChangeLogRepo.create(changeLogData);

            switch (actionType) {
                case ChangeLogActionType.ADDED:
                    {
                        newChangeLog.createdAt = metadata.createdAt;
                        newChangeLog.updatedAt = metadata.createdAt;
                        newChangeLog.createdBy = metadata.createdBy;
                        newChangeLog.createdByUser = metadata?.createdByUser;
                    }
                    break;
                case ChangeLogActionType.DELETED:
                    {
                        newChangeLog.createdAt = metadata.deletedAt;
                        newChangeLog.updatedAt = metadata.deletedAt;
                        newChangeLog.createdBy = metadata.deletedBy;
                        newChangeLog.createdByUser = metadata?.deletedByUser;
                    }
                    break;

                default: {
                    //default: ChangeLogActionType.UPDATED
                    newChangeLog.createdAt = metadata.updatedAt;
                    newChangeLog.updatedAt = metadata.updatedAt;
                    newChangeLog.createdBy = metadata.updatedBy;
                    newChangeLog.createdByUser = metadata.updatedByUser;
                    break;
                }
            }

            switch (sourceOfChange) {
                case SourceOfChangeType.AUTO_POPULATE:
                    {
                        newChangeLog.createdBy = AUTO_POPULATE_USER_ID;
                        newChangeLog.createdByUser = null;
                    }

                    break;
                case SourceOfChangeType.AUTOMATION:
                    {
                        newChangeLog.createdBy = AUTOMATION_USER_ID;
                        newChangeLog.createdByUser = null;
                    }
                    break;

                default:
                    break;
            }

            await transChangeLogRepo.save(newChangeLog);
        } catch (error) {
            this._logger.error(error);
        }
    }

    public async updateStageEnteredAt(transactionId: string) {
        await this.formTransactionRepository.update(transactionId, { stageEnteredAt: new Date() });
    }

    public async triggerDocPdfExtraction(
        transactionId: string,
        transactionFields: TransactionFieldEntity[],
        formVersion: FormVersionTenancyEntity,
    ) {
        const docField = transactionFields?.find((item) => item.fieldType === FormFieldTypeEnum.Document);
        if (!docField?.data?.filePath) {
            return;
        }
        const formField = formVersion?.fields?.find((item) => item.fieldId === docField.fieldId);
        if (!formField) {
            return;
        }

        const { configuration } = formField;
        const extractionSupportTypes = [
            FileTypeEnum.SIRE_CREW,
            FileTypeEnum.OVID_VIQ,
            FileTypeEnum.SIRE2_VIQ,
            FileTypeEnum.Q88,
            FileTypeEnum.CMID,
        ];
        if (!configuration?.isLLM || !extractionSupportTypes.includes(configuration?.fileType)) {
            return;
        }

        const message = EventDrivenService.createCommonEvent({
            payload: {
                transactionId: transactionId,
                transactionFields: [docField],
                fileType: configuration?.fileType,
            },

            tenantId: this._claims.accountId,
            aggregateId: transactionId,
            type: ActionEventEnum.DOC_PDF_EXTRACTION,
            name: ActionEventEnum.DOC_PDF_EXTRACTION,
        });
        await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_PDF_DOC_EXTRACTION_TOPIC, message);
    }

    private async _getTransaction(transactionId: string, isTest?: boolean): Promise<TransactionEntity> {
        const transaction = await this.formTransactionRepository.findOne({
            where: {
                id: transactionId,
            },
            withDeleted: true,
        });

        if (!transaction?.isTest && transaction.deletedAt) {
            throw new NotFoundException('transaction_not_found');
        }

        const tFields = await this.transactionFieldRepository.find({
            where: {
                transactionId,
            },
        });

        const tFieldStyles = await this.transactionFieldStyleRepository.find({
            where: {
                transactionId: transaction.id,
            },
        });

        const tFieldStylesMap = tFieldStyles.reduce((acc, tFieldStyle) => {
            acc[tFieldStyle.id] = tFieldStyle;
            return acc;
        }, {});

        transaction.transactionFields = tFields.map((tField) => {
            tField.transactionFieldStyle = tFieldStylesMap[tField.id];
            return tField;
        });

        return transaction;
    }

    private async _handlePayloadDocuments({
        payloadDocuments,
        transactionFields,
        transactionId,
    }: {
        payloadDocuments: Record<string, string>;
        transactionFields: TransactionFieldEntity[];
        transactionId: string;
    }) {
        if (payloadDocuments) {
            // Update original version id of external source, not need to track change in transaction fields
            const originalVersionIdField = (transactionFields || []).find(
                (f) => !f.collectionId && f.fieldId === EXTERNAL_ORIGINAL_VERSION_ID,
            );

            const parsedPayloadDocumenmts = Object.entries(payloadDocuments ?? {}).reduce((prev, [dataSource, originalVersionId]) => {
                if (dataSource && originalVersionId) {
                    prev.push({
                        dataSource,
                        originalVersionId,
                    });
                }

                return prev;
            }, []);

            const report: Record<string, string> = {};
            const filterOptionIds: string[] = [];
            parsedPayloadDocumenmts.forEach((item) => {
                report[item.dataSource] = item.originalVersionId;
                filterOptionIds.push(item.originalVersionId);
            });

            if (originalVersionIdField) {
                if (!originalVersionIdField.data) {
                    originalVersionIdField.data = {};
                }

                const currentReport: Record<string, string> = originalVersionIdField.data[ORIGIN_DATA.REPORT] || {};
                const currentFilterOptions: string[] = originalVersionIdField.data[ORIGIN_DATA.FILTER_OPTIONS] || [];
                currentFilterOptions.push(...filterOptionIds);

                originalVersionIdField.data[ORIGIN_DATA.REPORT] = { ...currentReport, ...report };
                originalVersionIdField.data[ORIGIN_DATA.FILTER_OPTIONS] = Array.from(new Set(currentFilterOptions));
                await this.transactionFieldRepository.save(originalVersionIdField);
            } else {
                const originVersionField = new TransactionFieldEntity();
                originVersionField.transactionId = transactionId;
                originVersionField.fieldId = EXTERNAL_ORIGINAL_VERSION_ID;
                originVersionField.fieldValue = null;
                originVersionField.fieldOptionIds = [];
                originVersionField.contextType = TransactionFieldContextTypeEnum.FORM;
                originVersionField.fieldType = FormFieldTypeEnum.Text;
                originVersionField.data = {
                    [ORIGIN_DATA.REPORT]: report,
                    [ORIGIN_DATA.FILTER_OPTIONS]: filterOptionIds,
                };
                await this.transactionFieldRepository.save(originVersionField);
            }
        }
    }
}
