import { CoreConfig, JsonTree, Utils as QbUtils } from '@react-awesome-query-builder/core';
import * as dayjs from 'dayjs';
import { LogicEngine } from 'json-logic-engine';
import { isArray, isEmpty } from 'lodash';
import { UtilsService } from '../../../../../common/src';
import { DurationFormatEnum } from '../../../../../common/src/modules/shared/enums/duration-format-type.enum';
import { Duration } from '../../../../../common/src/modules/shared/types/duration';
import { YYYYMMDD, YYYYMMDDHHmm } from '../../../../../constant/date';
import { FormFieldTypeEnum } from '../../../../../database/src/shared/enums/form-field-type.enum';
import { convertMinutesToHHMM } from '../../../../../utils';
import { formatCondition } from '../../../../../utils/formatConditional';

export class JsonLogicUtils {
    public static runJsonLogic(condition: JsonTree, formValues: Record<string, any>) {
        if (!condition) {
            return false;
        }

        const engine = new LogicEngine();
        const formatConditions = formatCondition(condition) as any;

        const jsonLogic = QbUtils.jsonLogicFormat(QbUtils.loadTree(formatConditions as JsonTree), CoreConfig);

        // for sire2Answer
        const evalResultArray = [];
        Object.entries(formValues).forEach(([key, value]) => {
            const formValuesCopy = JSON.parse(JSON.stringify(formValues));
            if (Array.isArray(value)) {
                value.forEach((item) => {
                    formValuesCopy[key] = item;
                    const evalResult = engine.run(jsonLogic.logic, formValuesCopy);
                    evalResultArray.push(evalResult);
                });
            }
        });
        const evalResult = engine.run(jsonLogic.logic, formValues);

        return evalResult || evalResultArray.some((result) => result === true) || false;
    }

    // From x minutes to HH:mm
    public static convertMinutesToHHmm(value: string | number): string {
        if (!value) {
            return null;
        }

        let numberValue = Number(value);
        return convertMinutesToHHMM(numberValue);
    }

    public static convertVariableValue = (type: FormFieldTypeEnum | undefined | 'boolean', value: any, fieldOptionIds?: any) => {
        if (isEmpty(value?.toString()) && isEmpty(fieldOptionIds)) {
            return null;
        }

        let formattedValue: string | number | null | Duration | Date | boolean | string[] = '';

        switch (type) {
            case FormFieldTypeEnum.DatePicker:
                // Convert to query builder format: YYYYMMDD
                formattedValue = value ? dayjs(value).format(YYYYMMDD) : null;
                break;
            case FormFieldTypeEnum.DatetimePicker:
                formattedValue = value ? dayjs(value).format(YYYYMMDDHHmm) : null;
                break;
            case FormFieldTypeEnum.TimePicker:
                formattedValue = this.convertMinutesToHHmm(value as string | number);
                break;

            case FormFieldTypeEnum.Duration:
                formattedValue = UtilsService.convertStringToDuration(
                    UtilsService.convertMinutesToDuration({ value: value, format: DurationFormatEnum.DHM }),
                );
                break;

            case FormFieldTypeEnum.Number:
                formattedValue = Number(value?.toString());
                break;

            case FormFieldTypeEnum.Checkbox:
            case 'boolean':
                formattedValue = value === 'true' || value === true;
                break;

            case FormFieldTypeEnum.Select:
            case FormFieldTypeEnum.Lookup:
                formattedValue = isArray(fieldOptionIds) ? fieldOptionIds : null;
                break;
            case FormFieldTypeEnum.Text:
                return value;

            default:
                formattedValue = fieldOptionIds?.length ? fieldOptionIds : value;
                break;
        }
        return formattedValue;
    };
}
