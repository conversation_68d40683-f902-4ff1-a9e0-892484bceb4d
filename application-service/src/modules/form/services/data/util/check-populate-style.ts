import { FormFieldTypeEnum } from '../../../../../database/src/shared/enums/form-field-type.enum';

export const isPopulateFieldStyle = ({
    includeValidationValue,
    type,
}: {
    includeValidationValue?: string | boolean;
    type?: FormFieldTypeEnum;
}) => {
    /**
     * Keep separate checking to clear why need to export field style in populate
     * Bad practice, but clearance takes priority
     */
    let populateFieldStyle = false;
    if (includeValidationValue?.toString() === 'true') {
        populateFieldStyle = true;
    }

    // https://maritime-ds.atlassian.net/browse/LS-3328?atlOrigin=eyJpIjoiY2I3MTllMWNiNmJlNGQwN2JlYWM2MDhjZDY0MDAxNzQiLCJwIjoiaiJ9
    if (type === FormFieldTypeEnum.Rollup) {
        populateFieldStyle = true;
    }

    return populateFieldStyle;
};
