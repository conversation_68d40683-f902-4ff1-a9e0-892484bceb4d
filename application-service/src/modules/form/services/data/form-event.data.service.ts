import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { In, Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../../common/src';
import { FormulaService } from '../../../../common/src/modules/shared/services/formula.service';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { FormManualEventTenancyEntity } from '../../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';

@Injectable()
export class FormEventDataService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,
        private readonly _formulaService: FormulaService,
        private readonly _eventEmitter: EventEmitter2,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly _formTransactionRepository: Repository<TransactionEntity>,
        @Inject(PROVIDER_KEYS.STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY)
        private readonly _stageAccessControlRepo: Repository<StageAccessControlTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_TENANCY_REPOSITORY)
        private readonly _stageRoleRepo: Repository<StageRoleTenancyEntity>,
        @Inject(PROVIDER_KEYS.FORM_MANUAL_EVENT_TENANCY_REPOSITORY)
        private readonly _formManualEventRepo: Repository<FormManualEventTenancyEntity>,
    ) {}

    public async validateManualEvent({
        formVersionId,
        stageId,
        roleId,
        validRoleIds,
        transactionId,
        manualEventIds,
        isTest,
    }: {
        formVersionId: string;
        manualEventIds: string[];
        stageId: string;
        roleId: string;
        validRoleIds: string[];
        transactionId: string;
        isTest?: boolean;
    }): Promise<{ data: { manualEvents: FormManualEventTenancyEntity[]; transaction: TransactionEntity }; errors: string[] }> {
        const result: { data: { manualEvents: FormManualEventTenancyEntity[]; transaction: TransactionEntity }; errors: string[] } = {
            data: { manualEvents: [], transaction: null },
            errors: [],
        };
        if (!roleId || !validRoleIds?.includes(roleId)) {
            result.errors.push('no_access');
        }
        if (!manualEventIds.length) {
            result.errors.push('no_actions');
            return result;
        }
        const manualEvents = await this._formManualEventRepo.find({
            where: {
                id: In(manualEventIds),
            },
        });

        const eventIds = manualEvents.map((item) => item.automationId);

        const stageRole = await this._stageRoleRepo.find({
            where: { stageId: stageId, formVersionId: formVersionId, roleId: roleId },
            relations: ['accessControls'],
        });
        if (!this.hasPermission(stageRole, eventIds)) {
            result.errors.push('no_access');
        }

        result.data.manualEvents = manualEvents;

        const transaction = await this._formTransactionRepository.findOne({
            where: { id: transactionId },
            withDeleted: !!isTest,
        });
        if (!transaction) {
            result.errors.push('no_transaction');
        }
        result.data.transaction = transaction;

        return result;
    }

    private hasPermission(stageRoles: StageRoleTenancyEntity[], eventIds: string[]) {
        return stageRoles.some((role) => {
            return role?.accessControls?.some((ac) => ac.type === 'automation' && ac.config.enable && eventIds.includes(ac.targetId));
        });
    }
}
