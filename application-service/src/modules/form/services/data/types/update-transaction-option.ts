import { AutoPopulateDataLakeResponse } from '../../../../../shared/common/dto/autopopulate-datalake.dto';
import { UpdateCollectionFieldActionParam, UpdateFieldActionParam } from '../../../../../shared/types/auto-populate-and-check-validation';

export type UpdateTransactionOption = {
    forceUsingTransactionFieldValues?: boolean; // If true, ignore field values from request
    onlyUpdateIfRelevantRegisterRecordIds?: string[]; // Contains list of register record IDs that need for refresh worker
    updatingFormFields?: Array<UpdateFieldActionParam>; // Fields of form transaction that need to be used to format transaction fields
    updatingCollectionFields?: Array<UpdateCollectionFieldActionParam>; // Collection field of transaction that need to be used to format transaction fields
    purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>; // Additional data for PurpleTRAC

    isModify?: boolean; // TODO: need to be remove, unused in current code base
    captureExistLookupField?: boolean; // If false filter out lookup captured fields
    targetFieldChangeIds?: string[]; // Contains list of field IDs that need to be refreshed
    targetFieldChangeFromRegisterId?: string; //support to determine which register targetFieldChangeIds come from

    forceRunAutoPopulate?: boolean; // If true, force run auto populate without field changed
    shouldRunPopulateFormFields?: boolean; // If true, run auto populate for form fields
    ignoreAutoPopulateCollection?: boolean; //!if set this option true, please make sure not forceRunAutoPopulate
};
