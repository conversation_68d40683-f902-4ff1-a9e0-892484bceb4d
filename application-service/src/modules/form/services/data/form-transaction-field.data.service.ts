import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { JsonTree } from '@react-awesome-query-builder/core';
import { compact, groupBy, uniq } from 'lodash';
import { DataSource, In, Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS, UtilsService } from '../../../../common/src';
import { FilterOptionDto } from '../../../../common/src/modules/shared/dtos/filter-option.dto';
import { OrderOptionDto } from '../../../../common/src/modules/shared/dtos/order-option.dto';
import { CONDITION_OVERRIDE_FIELD, CONDITION_VALIDATION_FIELD } from '../../../../constant';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { DataRegisterFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { TransactionFieldOverrideEntity } from '../../../../database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { TransactionFieldStyleEntity } from '../../../../database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { ValidationValueEnum } from '../../../../database/src/shared/enums/validation-value.enum';
import { FormTransactionFieldDto } from '../../dtos';
import { EditFormTransactionFieldRequest } from '../../dtos/requests/create-form-transaction.request';
import {
    CreateTransactionFieldRequest,
    DeleteCollectionRowRequest,
    OverrideTransactionFieldRequest,
    UnOverrideValidationFieldRequest,
} from '../../dtos/requests/transaction-field.request';
import { FormTransactionDataService } from './form-transaction.data.service';
import { FormatTransactionFieldService } from './format/transaction-field.service';
import { JsonLogicUtils } from './util/run-json-logic.util';

import * as _ from 'lodash';
import { CollectionTransactionTenancyEntity } from 'src/database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { checkRowConditionFilters } from 'src/utils/check-row-condition-filters';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';
import { TransactionEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { TransactionTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { SELECTABLE_FIELD_TYPES } from '../../../../database/src/constants/field';
import { FormCollectionAdditionalFieldEntity } from '../../../../database/src/entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { SourceOfChangeType } from '../../../../database/src/shared/enums/change-log.type.enum';
import { FormCollectionItemTypeEnum } from '../../../../database/src/shared/enums/form-collection-item-type.enum';
import { OverrideStatusEnum } from '../../../../database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from '../../../../database/src/shared/enums/override-type.enum';
import { AutoPopulateAndCheckValidationCollectionFieldService } from '../../../../shared/services/auto-populate-check-validation-collection-field.service';
import { FilterCollectionTransactionRequestDto } from '../../dtos/requests/FilterCollectionTransaction.request.dto';
import { TransactionFieldOverrideDto } from '../../dtos/transaction-field-override.dto';
import { DefaultTransactionFieldService } from '../default-transaction-field.service';
import { FormCollectionDataService } from '../form-collection.data.service';
import { FormTransactionTenancyService } from '../form-transaction.tenancy.service';
import { captureCollectionItemDefaultValues } from './util/capture-collection-item-default-values';
import { formatSire2Answer } from './util/format-sire2-answer';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';

@Injectable()
export class FormTransactionFieldDataService {
    constructor(
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _collectionRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemRepo: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_TRANSACTION_TENANCY_REPOSITORY)
        private readonly _collectionTransactionRepo: Repository<CollectionTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly _drFieldRepo: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _transactionFieldRepo: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_STYLE_REPOSITORY)
        private readonly _transactionFieldStyleRepo: Repository<TransactionFieldStyleEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly _transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _formAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly stageRepository: Repository<StageTenancyEntity>,

        private readonly _formTransactionDataService: FormTransactionDataService,

        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        private readonly _eventDrivenService: EventDrivenService,
        private readonly _formatField: FormatTransactionFieldService,
        private readonly _autoPopulateAndCheckValidationCollectionFieldService: AutoPopulateAndCheckValidationCollectionFieldService,
        private readonly _defaultTransactionFieldService: DefaultTransactionFieldService,
        private readonly _formTransactionTenancyService: FormTransactionTenancyService,

        private readonly _formCollectionDataService: FormCollectionDataService,
    ) {}

    //#region GET
    public async getListCollectionTransField(
        query: FilterCollectionTransactionRequestDto,
        transactionId: string,
        formCollectionIdentityId: string,
        isTest?: boolean,
    ): Promise<FormTransactionFieldDto[]> {
        try {
            if (!transactionId) return [];
            const transaction = await this.formTransactionRepository.findOne({
                where: { id: transactionId },
                withDeleted: !!isTest,
            });
            const transFields = await this._transactionFieldRepo.find({
                select: {
                    id: true,
                    fieldId: true,
                    fieldValue: true,
                    fieldOptionIds: true,
                    contextType: true,
                    rowKey: true,
                    pairId: true,
                    dependFieldId: true,
                    sourceFieldId: true,
                    inVisible: true,
                    data: true,
                    collectionId: true,
                    collectionItemId: true,
                    parentId: true,
                    displayAttributeFieldIds: true,
                    validationValue: true,
                    fieldType: true,
                    transactionId: true,
                    transactionFieldStyle: {
                        id: true,
                        transactionId: true,
                        fieldId: true,
                        configuration: true,
                    },
                },
                where: {
                    transactionId: transactionId,
                    collectionId: formCollectionIdentityId,
                    contextType: TransactionFieldContextTypeEnum.COLLECTION,
                },
                relations: {
                    transactionFieldStyle: true,
                },
            });

            const transactionFieldOverrides = await this._transactionFieldOverrideRepo.find({
                where: {
                    transactionId: transactionId,
                },
            });

            const drFields = await this._getRelatedDataRegisterFields({
                formFields: transFields,
                formCollectionIdentityId,
                formVersionId: transaction.formVersionId,
            });

            if (transactionFieldOverrides?.length) {
                transFields.forEach((dto) => {
                    const _transactionFieldOverrides = transactionFieldOverrides.filter((tfo) => tfo.transactionFieldId === dto.id);
                    dto.transactionFieldOverrides = _transactionFieldOverrides?.length
                        ? this._mapper.mapArray(_transactionFieldOverrides, TransactionFieldOverrideEntity, TransactionFieldOverrideDto)
                        : [];
                });
            }

            const { filters, sorters, collectionFilters } = query;
            if (!filters?.length && !collectionFilters?.length) {
                if (!sorters?.length) {
                    const dto = this._mapper.mapArray(transFields, TransactionFieldEntity, FormTransactionFieldDto);
                    this._formatField.formatFieldConfiguration({
                        formFields: drFields,
                        transactionFields: dto,
                    });

                    return dto;
                }

                const sortedDto = this._mapper.mapArray(
                    this._sortResult(transFields, sorters),
                    TransactionFieldEntity,
                    FormTransactionFieldDto,
                );
                this._formatField.formatFieldConfiguration({
                    formFields: drFields,
                    transactionFields: sortedDto,
                });

                return sortedDto;
            }

            const groupByRowKey = groupBy(transFields, 'rowKey');

            const result: TransactionFieldEntity[] = [];

            for (const rowKey in groupByRowKey) {
                const fieldsOfRow = groupByRowKey[rowKey];
                const checkFieldOfRowResult = filters.map((filter) => this._checkRowConditionFilter({ filter, fieldsOfRow, drFields }));
                const checkCollectionFilterResult = checkRowConditionFilters({ fieldsOfRow, drFields, collectionFilters });
                if (checkFieldOfRowResult.every((r) => r) && checkCollectionFilterResult) {
                    result.push(...fieldsOfRow);
                }
            }

            if (!sorters?.length) {
                const dto = this._mapper.mapArray(result, TransactionFieldEntity, FormTransactionFieldDto);
                this._formatField.formatFieldConfiguration({
                    formFields: drFields,
                    transactionFields: dto,
                });

                return dto;
            }

            const sortedResult = this._sortResult(result, sorters);
            const sortedDto = this._mapper.mapArray(sortedResult, TransactionFieldEntity, FormTransactionFieldDto);

            const sortedDto_ = formatSire2Answer(sortedDto);
            return sortedDto_;
        } catch (err) {
            this._logger.error(err);
            return [];
        }
    }

    public async getTransactionFieldDetail(transactionFieldId: string): Promise<FormTransactionFieldDto> {
        //TODO: removing joining field overrides
        const transactionFieldEntity = await this._transactionFieldRepo.findOne({
            where: {
                id: transactionFieldId,
            },
            // relations: {
            //     transactionFieldStyle: true,
            //     transactionFieldOverrides: true,
            // },
        });
        if (transactionFieldEntity) {
            const transactionFieldStyles = await this._transactionFieldStyleRepo.findOneBy({ id: transactionFieldId });
            const transactionFieldOVerrides = await this._transactionFieldOverrideRepo.findBy({ transactionFieldId: transactionFieldId });
            transactionFieldEntity.transactionFieldStyle = transactionFieldStyles;
            transactionFieldEntity.transactionFieldOverrides = transactionFieldOVerrides;
        }

        const dto = this._mapper.map(transactionFieldEntity, TransactionFieldEntity, FormTransactionFieldDto);
        return dto;
    }

    private _sortResult(data: TransactionFieldEntity[], sorters: OrderOptionDto[]) {
        let result = data;

        sorters.forEach((sorter) => {
            const { field } = sorter;
            const order = (sorter.order as unknown as 'asc' | 'desc') === 'asc' ? 'asc' : 'desc';

            const groupByRowKey = groupBy(result, 'rowKey');

            const sortedKeys = _.chain(groupByRowKey)
                .toPairs()
                .orderBy((pair) => pair[1].find((i) => i.fieldId === field)?.fieldValue, [order])
                .map(0)
                .value();

            result = uniq(compact(sortedKeys)).reduce((acc, key) => {
                return acc.concat(groupByRowKey[key]);
            }, []);
        });
        return result;
    }

    private async _getRelatedDataRegisterFields({
        formFields,
        formCollectionIdentityId,
        formVersionId,
    }: {
        formFields: TransactionFieldEntity[];
        formCollectionIdentityId: string;
        formVersionId: string;
    }): Promise<DataRegisterFieldTenancyEntity[]> {
        const collection = await this._collectionRepo.findOne({
            where: {
                identityId: formCollectionIdentityId,
                formVersionId,
            },
        });

        if (!collection?.dataRegisterVersionId) return [];

        const dataRegisterVersionId = collection.dataRegisterVersionId;

        const fieldIds = uniq(compact(formFields.map((f) => f.fieldId)));

        if (!fieldIds?.length) return [];

        const drFields = await this._drFieldRepo.find({
            where: {
                fieldId: In(fieldIds),
                dataRegisterVersionId: dataRegisterVersionId,
            },
        });

        const additionalFields = await this._formAdditionalFieldRepo.findBy({
            formVersionId,
        });

        if (additionalFields.length) {
            additionalFields.forEach((field) => {
                const drField = drFields.find((f) => f.fieldId === field.fieldId);
                if (!drField) {
                    return;
                }

                if (drField.configuration) {
                    drField.configuration.mode = field.configuration?.mode || drField.configuration.mode;
                    drField.configuration.type = field.configuration?.type || drField.configuration.type;
                    drField.configuration.format = field.configuration?.format || drField.configuration.format;
                } else {
                    drField.configuration = {
                        mode: field.configuration?.mode,
                        type: field.configuration?.type,
                        format: field.configuration?.format,
                    };
                }
            });
        }

        return drFields;
    }

    private _checkRowConditionFilter({
        filter,
        fieldsOfRow,
        drFields,
    }: {
        filter: FilterOptionDto;
        fieldsOfRow: TransactionFieldEntity[];
        drFields: DataRegisterFieldTenancyEntity[];
    }): boolean {
        const filterTerm = filter.value;

        if (!filterTerm) return true;
        const transFieldId = filter.field;

        const condition = JSON.parse(filterTerm as string) as JsonTree;

        const conditionFieldIds: string[] = [];
        UtilsService.loadConditionFieldIds({
            ruleOrGroup: condition,
            fieldIds: conditionFieldIds,
        });

        if (!conditionFieldIds?.length) return true;

        const valueMapping = {};
        conditionFieldIds.forEach((fieldId) => {
            const rowField = fieldsOfRow.find((f) => f.fieldId === transFieldId);
            const drField = drFields.find((f) => f.fieldId === fieldId);

            let type = drField?.type;

            if (fieldId === transFieldId) {
                if (FormFieldTypeEnum.Calculation === type) {
                    type = drField?.configuration?.calculationFormula?.dataType;
                }
                valueMapping[fieldId] = JsonLogicUtils.convertVariableValue(type, rowField?.fieldValue, rowField?.fieldOptionIds);
            } else if (fieldId === CONDITION_VALIDATION_FIELD) {
                valueMapping[fieldId] = rowField?.validationValue;
            } else if (fieldId === CONDITION_OVERRIDE_FIELD) {
                valueMapping[fieldId] = !!rowField?.transactionFieldStyle?.configuration?.border;
            } else if (fieldId.includes(transFieldId)) {
                // sire2Answer
                const [fieldId_, _] = fieldId.split('.');
                // getSire2AnswerFromCategory(rowField?.data?.fieldValue?.complexResponses, category);
                valueMapping[fieldId_] = rowField?.data?.fieldValue?.complexResponses;
            }
        });

        const matched = JsonLogicUtils.runJsonLogic(condition, valueMapping);

        return !!matched;
    }

    //#endregion GET

    public async create({
        request,
        isTest,
    }: {
        request: CreateTransactionFieldRequest;
        isTest?: boolean;
    }): Promise<FormTransactionFieldDto[]> {
        const transaction = await this.formTransactionRepository.findOne({
            where: {
                id: request.transactionId,
                isTest: !!isTest,
            },
            withDeleted: !!isTest,
        });

        if (!transaction) {
            throw new NotFoundException('transaction_not_found');
        }

        let formCollectionTransactions: CollectionTransactionTenancyEntity[] = [];

        if (isTest) {
            const formCollections = await this._collectionRepo.find({
                where: {
                    formVersionId: transaction.formVersionId,
                },
                select: {
                    id: true,
                },
            });

            const collectionIds = formCollections.map((f) => f.id);

            if (collectionIds?.length) {
                const formCollectionItems = await this._formCollectionItemRepo.find({
                    where: {
                        formCollectionId: In(collectionIds),
                    },
                    select: {
                        id: true,
                    },
                });

                const collectionItemIds = formCollectionItems.map((f) => f.id);

                if (collectionItemIds?.length) {
                    formCollectionTransactions =
                        (await this._formCollectionDataService.getTestCollectionTransaction({
                            formVersionId: transaction.formVersionId,
                            formCollectionItemIds: collectionItemIds,
                        })) ?? [];
                }
            }
        } else {
            formCollectionTransactions = await this._collectionTransactionRepo.find({
                where: {
                    formVersionId: transaction.formVersionId,
                },
            });
        }

        const entityMapper = this._mapper.mapArray(request.fields, EditFormTransactionFieldRequest, TransactionFieldEntity);

        const { collectionIdentityIds, collectionItemIdentityIds } = entityMapper.reduce(
            (acc, mapperField) => {
                acc.collectionIdentityIds.add(mapperField.collectionId);
                acc.collectionItemIdentityIds.add(mapperField.collectionItemId);
                return acc;
            },
            { collectionIdentityIds: new Set(), collectionItemIdentityIds: new Set() },
        );

        const collectionIdentityIdsArray = Array.from(collectionIdentityIds) as string[];
        const collectionItemIdentityIdsArray = Array.from(collectionItemIdentityIds) as string[];

        let collectionDefaultValues = [];
        let capturedCollectionDefaultValues: ReturnType<typeof captureCollectionItemDefaultValues> = null;

        if (collectionIdentityIdsArray?.length && collectionItemIdentityIdsArray?.length) {
            const stageEntities: StageTenancyEntity[] = await this.stageRepository.find({
                where: {
                    formVersionId: transaction.formVersionId,
                },
            });

            const { collectionTransactionFields } = await this._defaultTransactionFieldService.getDefaultCollectionFields({
                formVersionId: transaction.formVersionId,
                transaction: transaction,
                stages: stageEntities,
                option: {
                    collectionIdentityIds: collectionIdentityIdsArray,
                    collectionItemIdentityIds: collectionItemIdentityIdsArray,
                    collectionTypes: [FormCollectionItemTypeEnum.DEFAULT, FormCollectionItemTypeEnum.OPTION],
                },
            });

            collectionDefaultValues = collectionTransactionFields || [];
            capturedCollectionDefaultValues = captureCollectionItemDefaultValues({ collectionFields: collectionTransactionFields });
        }

        try {
            const formValues = {};
            const newFields = entityMapper.map((mapperField) => {
                const capturedField = formCollectionTransactions.find(
                    (f) => f.fieldId === mapperField.fieldId && mapperField.collectionItemId === f.formCollectionItemIdentityId,
                );
                mapperField.fieldValue = capturedField?.fieldValue;
                mapperField.fieldOptionIds = capturedField?.fieldOptionIds;
                mapperField.transactionId = transaction.id;

                if (!mapperField.fieldValue && collectionDefaultValues?.length) {
                    const defaultField = collectionDefaultValues.find(
                        (f) => f.fieldId === mapperField.fieldId && f.collectionItemId === mapperField.collectionItemId,
                    );

                    if (defaultField?.fieldValue) {
                        mapperField.fieldValue = defaultField.fieldValue;
                        mapperField.fieldOptionIds = defaultField.fieldOptionIds;
                    }
                }

                if (SELECTABLE_FIELD_TYPES.includes(mapperField.fieldType)) {
                    formValues[mapperField.fieldId] = mapperField.fieldOptionIds?.[0];
                    mapperField.fieldValue = mapperField.fieldOptionIds?.[0];
                } else {
                    formValues[mapperField.fieldId] = mapperField.fieldValue;
                }

                return mapperField;
            });

            const stage = await this._formTransactionTenancyService.getStageInfo(transaction.stageId);

            const request = {
                id: transaction.id,
                request: {
                    transactionId: transaction.id,
                    formId: transaction.formId,
                    stageId: transaction.stageId,
                    activeStageId: transaction.stageId,
                    transactionFields: newFields,
                    stageIdentityId: stage.identityId,
                    activeStageIdentityId: stage.identityId,
                    formValues: formValues,
                    isTest: !!isTest,
                },
            };

            await this._formTransactionDataService.update(request);

            if (capturedCollectionDefaultValues) {
                if (!transaction.metadata)
                    transaction.metadata = {
                        collectionDefaultValues: {},
                    };

                const metadata = transaction.metadata;

                if (!metadata.collectionDefaultValues) {
                    metadata.collectionDefaultValues = {};
                }

                Object.entries(capturedCollectionDefaultValues).forEach(([collectionId, collectionItems]) => {
                    if (!metadata.collectionDefaultValues[collectionId]) metadata.collectionDefaultValues[collectionId] = {};

                    Object.entries(collectionItems).forEach(([collectionItemId, collectionFields]) => {
                        metadata.collectionDefaultValues[collectionId][collectionItemId] = collectionFields;
                    });
                });

                await this.formTransactionRepository.update(
                    { id: transaction.id },
                    {
                        metadata,
                    },
                );
            }

            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...transaction,
                    fields: newFields,
                    sourceOfChange: SourceOfChangeType.MANUAL,
                    isTest: transaction.isTest,
                },
                tenantId: RequestContextService.accountId,
                aggregateId: transaction.id,
                type: TransactionEventEnum.FORM_TRANSACTION_FIELD_CREATED,
                name: TransactionEventEnum.FORM_TRANSACTION_FIELD_CREATED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);

            return this._mapper.mapArray(newFields, TransactionFieldEntity, FormTransactionFieldDto);
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async updateField({
        request,
        formTransactionRepository,
        formTransactionFieldRepository,
    }: {
        request: EditFormTransactionFieldRequest;
        formTransactionRepository: Repository<TransactionEntity>;
        formTransactionFieldRepository: Repository<TransactionFieldEntity>;
    }): Promise<boolean> {
        try {
            const isTest = !!request.isTest;
            const transactionId = request.transactionId;
            const transaction = await formTransactionRepository.findOne({
                where: {
                    id: transactionId,
                    isTest: !!isTest,
                },
                withDeleted: !!isTest,
            });

            if (!transaction) {
                throw new BadRequestException('transaction_not_found');
            }

            const entity = this._mapper.map(request, EditFormTransactionFieldRequest, TransactionFieldEntity);

            const transactionFields = await formTransactionFieldRepository.find({
                where: {
                    transactionId: transactionId,
                    rowKey: entity.rowKey,
                },
            });

            const transactionField = transactionFields.find((field) => field.fieldId === entity.fieldId);

            if (transactionField) {
                entity.id = transactionField.id;
                entity.pairId = transactionField.pairId;
                entity.parentId = transactionField.parentId;
                entity.rowKey = transactionField.rowKey;
                if (request.fieldOptionIds) entity.fieldOptionIds = request.fieldOptionIds;

                console.time('updateField.checkCollectionValidation');
                const validation = await this._autoPopulateAndCheckValidationCollectionFieldService.checkCollectionValidation({
                    activeFormVersionId: transaction.formVersionId,
                    transactionId,
                    formValues: request.formValues,
                    triggerField: transactionField,
                    transactionFields: transactionFields,
                    isTest,
                });
                console.timeEnd('updateField.checkCollectionValidation');

                const validationResults = validation?.validations;
                const combineValidationResults = validationResults || [];
                const validationField = combineValidationResults.find((f) => f.fieldId === entity.fieldId && f.rowKey === entity.rowKey);
                if (validationField) {
                    entity.validationValue = validationField.validationValue;
                }

                const result = await formTransactionFieldRepository.save(entity);

                if (result) {
                    await this._saveConditionCollectionValueFields({
                        formVersionId: transaction.formVersionId,
                        transactionId: transactionId,
                        formTransactionFieldRepository,
                    });
                }

                await this._publishUpdatedFieldEventForRollup({
                    accountId: this._claims.accountId,
                    transactionId: entity.transactionId,
                    formId: transaction.formId,
                    formVersionId: transaction.formVersionId,
                    fields: [entity],
                    isTest: transaction.isTest,
                });

                if (
                    transactionField?.fieldValue !== result?.fieldValue ||
                    (transactionField.fieldOptionIds || []).join(',') !== (result.fieldOptionIds || []).join(',')
                ) {
                    const message = EventDrivenService.createCommonEvent({
                        payload: {
                            ...transaction,
                            fields: [result],
                            sourceOfChange: SourceOfChangeType.MANUAL,
                            previous: [transactionField],
                            isTest: transaction.isTest,
                        },
                        tenantId: RequestContextService.accountId,
                        aggregateId: transaction.id,
                        type: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
                        name: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
                    });
                    await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);
                }
            }

            return true;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async deleteCollectionRow({
        request,
        formTransactionFieldRepository,
        isTest,
    }: {
        request: DeleteCollectionRowRequest;
        formTransactionFieldRepository: Repository<TransactionFieldEntity>;
        isTest: boolean;
    }) {
        const { rowKey, transactionId, collectionIdentityId } = request;
        const transactionFields = await formTransactionFieldRepository.find({
            where: {
                transactionId,
                rowKey,
                collectionId: collectionIdentityId,
            },
        });

        if (!transactionFields?.length) return false;

        const transactionFieldIds = transactionFields.map((field) => field.id);

        try {
            const entitiesToDelete = await formTransactionFieldRepository.find({
                where: [
                    {
                        id: In(transactionFieldIds),
                    },
                    {
                        parentId: In(transactionFieldIds),
                    },
                ],
            });

            const transaction = await this.formTransactionRepository.findOne({
                where: {
                    id: transactionId,
                    isTest: !!isTest,
                },
                withDeleted: !!isTest,
            });
            const previous = _.cloneDeep(entitiesToDelete);
            const result = await formTransactionFieldRepository.softRemove(entitiesToDelete);

            await this._publishUpdatedFieldEventForRollup({
                accountId: this._claims.accountId,
                transactionId: transactionId,
                formId: transaction.formId,
                formVersionId: transaction.formVersionId,
                fields: entitiesToDelete,
                isTest: transaction.isTest,
            });

            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...transaction,
                    fields: result,
                    sourceOfChange: SourceOfChangeType.MANUAL,
                    previous,
                    isTest: transaction.isTest,
                },
                tenantId: RequestContextService.accountId,
                aggregateId: transaction.id,
                type: TransactionEventEnum.FORM_TRANSACTION_FIELD_DELETED,
                name: TransactionEventEnum.FORM_TRANSACTION_FIELD_DELETED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);

            return !!result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async overrideTransactionField({
        request,
        repository,
    }: {
        request: OverrideTransactionFieldRequest;
        repository: DataSource;
    }): Promise<FormTransactionFieldDto> {
        const validationValue = request.validationValue ?? ValidationValueEnum.PASS;

        const isTest = !!request.isTest;

        const transactionField = await repository.transaction(async (manager) => {
            const transaction = await manager.getRepository(TransactionEntity).findOne({
                where: {
                    id: request.transactionId,
                    isTest: !!isTest,
                },
                relations: {
                    transactionFields: {
                        transactionFieldStyle: true,
                    },
                },
                withDeleted: !!isTest,
            });

            const currentTransactionField = transaction.transactionFields.find((tf) => tf.id === request.transactionFieldId);

            const transactionFieldStyle = currentTransactionField?.transactionFieldStyle;

            if (!currentTransactionField || !transactionFieldStyle) {
                throw new BadRequestException('transaction_field_style_not_found');
            }

            const fromValue = currentTransactionField.validationValue;

            currentTransactionField.validationValue = validationValue;

            transactionFieldStyle.configuration = {
                icon: validationValue,
                label: request.comment,
                border: transactionFieldStyle.configuration.icon,
            };

            await manager.getRepository(TransactionFieldOverrideEntity).update(
                {
                    transactionFieldId: currentTransactionField.id,
                    status: OverrideStatusEnum.Active,
                },
                { status: OverrideStatusEnum.Inactive, transactionId: request.transactionId },
            );

            let overrideEntity = new TransactionFieldOverrideEntity();

            overrideEntity.validationValue = validationValue;
            overrideEntity.comment = request.comment;
            overrideEntity.transactionFieldId = currentTransactionField.id;
            overrideEntity.dependencyValues = request.dependencyValues;
            overrideEntity.type = OverrideTypeEnum.User;
            overrideEntity.status = OverrideStatusEnum.Active;
            overrideEntity.fromValue = fromValue;
            overrideEntity.transactionId = request.transactionId;

            await Promise.all([
                manager.getRepository(TransactionFieldEntity).save(currentTransactionField),
                manager.getRepository(TransactionFieldStyleEntity).save(transactionFieldStyle),
                manager.getRepository(TransactionFieldOverrideEntity).save(overrideEntity),
            ]);

            await this._publishUpdatedFieldEventForRollup({
                accountId: this._claims.accountId,
                transactionId: transaction.id,
                formId: transaction.formId,
                formVersionId: transaction.formVersionId,
                fields: [currentTransactionField],
                isTest: transaction.isTest,
            });

            return currentTransactionField;
        });

        const transactionFieldDto = await this.getTransactionFieldDetail(transactionField.id);

        return transactionFieldDto;
    }

    public async unOverrideValidationValue({ request, dataSource }: { request: UnOverrideValidationFieldRequest; dataSource: DataSource }) {
        const { comment, transactionFieldId, isTest } = request;
        await dataSource.transaction(async (manager) => {
            const transactionFieldOverride = await manager.getRepository(TransactionFieldOverrideEntity).findOne({
                where: {
                    transactionFieldId,
                    type: OverrideTypeEnum.User,
                    status: OverrideStatusEnum.Active,
                },
                relations: {
                    transactionField: {
                        // transaction: true,
                        transactionFieldStyle: true,
                    },
                },
                withDeleted: !!isTest,
            });
            const transactionField = transactionFieldOverride.transactionField;

            const transaction = await manager.getRepository(TransactionEntity).findOne({
                where: {
                    id: transactionField.transactionId,
                    isTest: !!isTest,
                },
                withDeleted: !!isTest,
                select: {
                    id: true,
                },
            });

            if (!transaction) {
                throw new NotFoundException('transaction_field_override_not_found');
            }

            const transactionFieldStyle = transactionField.transactionFieldStyle;

            transactionFieldOverride.status = OverrideStatusEnum.UnOverride;
            const unOverrideEntity = new TransactionFieldOverrideEntity();
            unOverrideEntity.transactionFieldId = transactionFieldId;
            unOverrideEntity.status = OverrideStatusEnum.UnOverride;
            unOverrideEntity.type = OverrideTypeEnum.User;
            unOverrideEntity.comment = comment;
            unOverrideEntity.fromValue = transactionFieldOverride.validationValue;
            unOverrideEntity.validationValue = transactionFieldOverride.fromValue;
            unOverrideEntity.transactionId = transaction.id;

            transactionField.validationValue = unOverrideEntity.validationValue;

            transactionFieldStyle.configuration = {
                label: comment,
                icon: unOverrideEntity.validationValue,
            };

            await Promise.all([
                manager.getRepository(TransactionFieldEntity).save(transactionField),
                manager.getRepository(TransactionFieldStyleEntity).save(transactionFieldStyle),
                manager.getRepository(TransactionFieldOverrideEntity).save([transactionFieldOverride, unOverrideEntity]),
            ]);

            await this._publishUpdatedFieldEventForRollup({
                accountId: this._claims.accountId,
                transactionId: transaction.id,
                formId: transaction.formId,
                formVersionId: transaction.formVersionId,
                fields: [transactionField],
                isTest: request.isTest,
            });
        });

        const transactionFieldDto = await this.getTransactionFieldDetail(transactionFieldId);
        return transactionFieldDto;
    }

    // private _getTransactionFieldDto = async (transactionFieldId: string): Promise<FormTransactionFieldDto> => {
    //     //TODO: removing joining field overrides
    //     const transactionFieldEntity = await this._transactionFieldRepo.findOne({
    //         where: {
    //             id: transactionFieldId,
    //         },
    //         relations: {
    //             transactionFieldStyle: true,
    //             transactionFieldOverrides: true,
    //         },
    //     });

    //     const transactionFieldDto = this._mapper.map(transactionFieldEntity, TransactionFieldEntity, FormTransactionFieldDto);
    //     transactionFieldDto.style = transactionFieldEntity.transactionFieldStyle;
    //     return transactionFieldDto;
    // };

    private async _publishUpdatedFieldEventForRollup({
        accountId,
        fields,
        formId,
        formVersionId,
        transactionId,
        isTest,
    }: {
        accountId: string;
        transactionId: string;
        formId: string;
        formVersionId: string;
        fields: Array<TransactionFieldEntity>;
        isTest: boolean;
    }): Promise<void> {
        console.log('_publishUpdatedFieldEventForRollup');
        const fieldIds = compact(uniq(fields.map((field) => field.fieldId)));
        const message = EventDrivenService.createCommonEvent({
            payload: {
                accountId: accountId,
                transactionId: transactionId,
                formId: formId,
                formVersionId: formVersionId,
                fields: fieldIds,
                sourceOfChange: SourceOfChangeType.MANUAL,
                isTest: isTest,
            },
            aggregateId: transactionId,
            tenantId: accountId,
            type: TransactionEventEnum.FORM_TRANSACTION_ROLLUP,
            name: TransactionEventEnum.FORM_TRANSACTION_ROLLUP,
        });
        await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_ROLLUP_TOPIC, message);
    }

    private async _getConditionCollectionField(formVersionId: string) {
        const formCollections = await this._collectionRepo.find({
            where: {
                formVersionId,
            },
            relations: {
                formCollectionItems: true,
            },
        });

        if (!formCollections) {
            return [];
        }

        let formCollectionItems = [];

        for (const collection of formCollections) {
            if (collection.formCollectionItems?.length) {
                formCollectionItems = formCollectionItems.concat(collection.formCollectionItems);
            }
        }

        return formCollectionItems;
    }

    private async _saveConditionCollectionValueFields({
        formVersionId,
        transactionId,
        formTransactionFieldRepository,
    }: {
        formVersionId: string;
        transactionId: string;
        formTransactionFieldRepository: Repository<TransactionFieldEntity>;
    }) {
        const chunkSize = 50;

        const [formCollectionItems, transactionFields] = await Promise.all([
            this._getConditionCollectionField(formVersionId),
            formTransactionFieldRepository.find({
                where: {
                    transactionId: transactionId,
                },
            }),
        ]);

        const updateEntities = this._formTransactionDataService.executeConditionValueField({
            formFieldEntities: [],
            transactionFields: transactionFields,
            formCollectionFieldEntities: formCollectionItems,
        });

        for (let i = 0; i < updateEntities.length; i += chunkSize) {
            const chunk = updateEntities.slice(i, i + chunkSize);
            await formTransactionFieldRepository.save(chunk);
        }
    }
}
