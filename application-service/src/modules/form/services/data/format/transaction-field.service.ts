import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { compact, isArray, uniq } from 'lodash';
import { DataRegisterTransactionTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { TransactionFieldEntity } from '../../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormFieldTypeEnum } from '../../../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../../database/src/shared/enums/transaction-field.enum';
import { LookupFieldDataSource } from '../../../../../shared/enums/lookup-field-datasource.enum';
import { EditFormTransactionFieldRequest } from '../../../dtos/requests/create-form-transaction.request';
import { TransactionFieldUtil } from '../util/transaction-field.util';
import { FormatCollectionDataType, FormatCollectionTransactionFieldService } from './collection-field.service';

import { DEFAULT_DATE_FORMAT, DEFAULT_DATE_TIME_FORMAT } from '../../../../../constant/date';
import { OBJECT_SELECTABLE_FIELD_TYPES, SELECTABLE_FIELD_TYPES } from '../../../../../database/src/constants/field';
import { convertDurationToMinutes } from '../../../../../utils';
import { FormTransactionFieldDto } from '../../../dtos';

@Injectable()
export class FormatTransactionFieldService {
    constructor(@InjectMapper() readonly _mapper: Mapper) {}

    public formatUpdatedFields(
        requestTransactionFields: EditFormTransactionFieldRequest[],
        transactionFields: TransactionFieldEntity[],
        transaction: TransactionEntity,
        defaultTransactionField: TransactionFieldEntity,
        formVersion: FormVersionTenancyEntity,
        relatedLookupData: { dataRegisters: DataRegisterTenancyEntity[]; dataRegisterTransactions: DataRegisterTransactionTenancyEntity[] },
        relatedCollectionData?: FormatCollectionDataType,
    ) {
        return requestTransactionFields.map((requestField) => {
            if (requestField?.data?.transactions) {
                delete requestField.data.transactions;
            }

            const type = requestField?.fieldType;
            let mapperField: TransactionFieldEntity;
            if (typeof requestField.fieldValue === 'object') {
                requestField.fieldValue = JSON.stringify(requestField.fieldValue);
                mapperField = this._mapper.map(requestField, EditFormTransactionFieldRequest, TransactionFieldEntity);
                mapperField.fieldValue = JSON.parse(mapperField.fieldValue);
            } else {
                mapperField = this._mapper.map(requestField, EditFormTransactionFieldRequest, TransactionFieldEntity);
            }

            mapperField.fieldOptionIds = requestField.fieldOptionIds;

            const existedField =
                mapperField?.contextType === 'collection'
                    ? transactionFields.find((field) => field.fieldId === mapperField.fieldId && field.rowKey === mapperField.rowKey)
                    : transactionFields.find((field) => field.fieldId === mapperField.fieldId);

            mapperField.id = existedField?.id;
            mapperField.fieldId = existedField?.fieldId ?? mapperField.fieldId;
            mapperField.transactionId = transaction.id;
            mapperField.parentId = existedField?.parentId;
            mapperField.pairId = existedField?.pairId;
            if (Object.keys(requestField?.data ?? {}).length)
                mapperField.data = { ...(existedField?.data ?? {}), ...(requestField.data ?? {}) };

            if (defaultTransactionField?.fieldId === mapperField.fieldId) {
                mapperField.fieldOptionIds = defaultTransactionField.fieldOptionIds;
                mapperField.fieldValue = defaultTransactionField.fieldValue;
            } else {
                if (mapperField.contextType === 'collection' && relatedCollectionData) {
                    mapperField = this.formatCollectionField({
                        requestField,
                        mapperField,
                        relatedLookupData: {
                            dataRegisters: relatedCollectionData?.fieldValueRegisters?.map((dr) => {
                                dr.dataRegisterVersions = (relatedCollectionData?.fieldValueRegisterVersions || []).filter(
                                    (v) => v.dataRegisterId === dr.id,
                                );

                                return dr;
                            }),
                            dataRegisterTransactions: relatedCollectionData?.fieldValueTransactions,
                        },
                        relatedCollectionData,
                        transactionFields,
                    });
                }
                const formField = (formVersion.fields || []).find((f) => f.fieldId === mapperField.fieldId);
                if (!formField) return mapperField;

                mapperField = this.formatTransactionValueByType({
                    type: formField?.type ?? (type as FormFieldTypeEnum),
                    formField,
                    mapperField,
                    extra: {
                        relatedLookupData: relatedLookupData,
                    },
                    requestField,
                    transactionFields,
                });
            }

            return mapperField;
        });
    }

    public formatTransactionValueByType({
        type,
        formField,
        mapperField,
        extra,
        requestField,
        transactionFields,
        excludeTypes,
    }: {
        type: FormFieldTypeEnum;
        formField: FormFieldTenancyEntity;
        mapperField: TransactionFieldEntity;
        extra?: {
            relatedLookupData?: any;
        };
        requestField?: EditFormTransactionFieldRequest;
        transactionFields?: TransactionFieldEntity[];
        excludeTypes?: FormFieldTypeEnum[];
    }): any {
        if (excludeTypes?.includes(type)) return mapperField;
        switch (type) {
            case FormFieldTypeEnum.Select: {
                if (mapperField.fieldValue === undefined) {
                    break;
                }
                const { fieldOptionIds, fieldValue } = this.formatSelectValue({ formField, transactionField: mapperField });
                mapperField.fieldOptionIds = fieldOptionIds;
                mapperField.fieldValue = fieldValue;
                break;
            }
            case FormFieldTypeEnum.Lookup: {
                if (mapperField.fieldValue === undefined) {
                    break;
                }

                const { fieldOptionIds, fieldValue, displayAttributeFieldIds } = this.formatLookupValue({
                    formField,
                    transactionField: mapperField,
                    dataRegisterVersions: (extra?.relatedLookupData?.dataRegisters ?? [])
                        .filter((dr) => dr.dataRegisterVersions?.length)
                        .map((dr) => dr.dataRegisterVersions[0]),
                    dataRegisterTransactions: extra?.relatedLookupData?.dataRegisterTransactions || [],
                    transactionFields: transactionFields,
                });
                mapperField.fieldOptionIds = fieldOptionIds;
                mapperField.fieldValue = fieldValue;
                mapperField.displayAttributeFieldIds = displayAttributeFieldIds;

                break;
            }
            case FormFieldTypeEnum.TimePicker: {
                mapperField.fieldValue = mapperField.fieldValue ? mapperField.fieldValue.toString() : null;
                break;
            }

            case FormFieldTypeEnum.DatePicker:
                mapperField.fieldValue = mapperField.fieldValue ? dayjs(mapperField.fieldValue).format(DEFAULT_DATE_FORMAT) : null;
                break;
            case FormFieldTypeEnum.DatetimePicker:
                mapperField.fieldValue = mapperField.fieldValue ? dayjs(mapperField.fieldValue).format(DEFAULT_DATE_TIME_FORMAT) : null;
                break;

            case FormFieldTypeEnum.Number:
                mapperField.fieldValue = mapperField.fieldValue?.toString() || null;
                break;
            case FormFieldTypeEnum.Duration:
                //if send format is string convert to number
                if (mapperField.fieldValue && isNaN(+mapperField.fieldValue)) {
                    mapperField.fieldValue = convertDurationToMinutes(mapperField.fieldValue)?.toString();
                } else {
                    mapperField.fieldValue = mapperField.fieldValue?.toString() || null;
                }
                break;

            case FormFieldTypeEnum.UserLookup:
            case FormFieldTypeEnum.RoleLookup:
                mapperField.fieldOptionIds = [mapperField.fieldValue ?? ''];
                mapperField.fieldValue = requestField?.fieldLabelValue ?? '';
                break;
            case FormFieldTypeEnum.Calculation:
                if (formField?.configuration?.calculationFormula?.dataType === FormFieldTypeEnum.Duration) {
                    if (mapperField.fieldValue && isNaN(+mapperField.fieldValue)) {
                        mapperField.fieldValue = convertDurationToMinutes(mapperField.fieldValue)?.toString();
                    }
                }

                break;
            default:
                break;
        }
        return mapperField;
    }

    public formatSelectValue({
        formField,
        transactionField,
    }: {
        formField: FormFieldTenancyEntity;
        transactionField: TransactionFieldEntity;
    }): {
        fieldValue: string;
        fieldOptionIds: string[];
    } {
        const result = {
            fieldValue: null,
            fieldOptionIds: [],
        };
        switch (formField?.configuration?.mode) {
            case 'single':
                {
                    const fieldOptionId = transactionField.fieldValue; //id value
                    const option = ((formField?.configuration?.options as { label: string; value: string }[]) || []).find(
                        (o) => o.value === fieldOptionId,
                    );

                    if (option) {
                        result.fieldValue = option ? option.label : '';
                        result.fieldOptionIds = option ? compact([option.value]) : [];
                    } else if (transactionField.fieldOptionIds?.length) {
                        const optionId = transactionField.fieldOptionIds?.[0];
                        const option = optionId
                            ? ((formField?.configuration?.options as { label: string; value: string }[]) || []).find(
                                  (o) => o.value === optionId,
                              )
                            : null;
                        if (option) {
                            result.fieldValue = option ? option.label : '';
                            result.fieldOptionIds = option ? compact([option.value]) : [];
                        }
                    } else {
                        //field value is label
                        const option = ((formField?.configuration?.options as { label: string; value: string }[]) || []).find(
                            (o) => o.label === fieldOptionId,
                        );

                        if (option) {
                            result.fieldValue = option ? option.label : '';
                            result.fieldOptionIds = option ? compact([option.value]) : [];
                        }
                    }
                }
                break;
            case 'multiple':
                {
                    const fieldOptionIds = Array.isArray(transactionField.fieldValue)
                        ? transactionField.fieldValue
                        : compact(transactionField.fieldValue?.split(',') as string[]) || []; //id value
                    const options =
                        ((formField?.configuration?.options as { label: string; value: string }[]) || []).filter((o) =>
                            fieldOptionIds.includes(o.value),
                        ) || [];

                    if (options?.length) {
                        result.fieldValue = options.map((option) => option.label).join(',');
                        result.fieldOptionIds = fieldOptionIds;
                    } else if (transactionField.fieldOptionIds?.length) {
                        const optionIds = transactionField.fieldOptionIds || [];
                        const options =
                            ((formField?.configuration?.options as { label: string; value: string }[]) || []).filter((o) =>
                                optionIds.includes(o.value),
                            ) || [];

                        if (options?.length) {
                            result.fieldValue = options.map((option) => option.label).join(',');
                            result.fieldOptionIds = compact(options.map((o) => o.value));
                        }
                    } else {
                        const options =
                            ((formField?.configuration?.options as { label: string; value: string }[]) || []).filter((o) =>
                                fieldOptionIds.includes(o.label),
                            ) || [];

                        if (options?.length) {
                            result.fieldValue = options.map((option) => option.label).join(',');
                            result.fieldOptionIds = compact(options.map((o) => o.value));
                        }
                    }
                }
                break;
        }
        return result;
    }

    public formatLookupValue({
        formField,
        transactionField,
        dataRegisterVersions,
        dataRegisterTransactions,
        transactionFields,
    }: {
        formField: FormFieldTenancyEntity;
        transactionField: TransactionFieldEntity;
        dataRegisterVersions: DataRegisterVersionTenancyEntity[];
        dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
        transactionFields: TransactionFieldEntity[];
    }): {
        fieldValue: string;
        fieldOptionIds: string[];
        displayAttributeFieldIds: string[];
    } {
        const result = {
            fieldValue: null,
            fieldOptionIds: [],
            displayAttributeFieldIds: [],
        };

        if (!transactionField.fieldValue) {
            return result;
        }
        const fieldOptionIds = transactionField?.fieldOptionIds?.length
            ? transactionField?.fieldOptionIds
            : TransactionFieldUtil.getFieldOptionIds({
                  mode: formField?.configuration?.mode,
                  fieldValue: transactionField.fieldValue,
              });

        let displayLabels: string[] = [];
        switch (formField?.configuration?.dataset) {
            case LookupFieldDataSource.DATA_REGISTER:
            default: {
                displayLabels = compact(fieldOptionIds).map((id) => {
                    if (!id || typeof id !== 'string') id = '';

                    const transaction = dataRegisterTransactions.find((drt) => drt.id === id?.trim());
                    const dataRegisterVersion = dataRegisterVersions.find((dr) => dr.dataRegisterId === transaction?.dataRegisterId);
                    let displayFieldIds = dataRegisterVersion?.displayAttributes?.length
                        ? dataRegisterVersion.displayAttributes
                        : dataRegisterVersion?.fields?.filter((f) => f.isDefault)?.map((f) => f.fieldId) || [];

                    let transactionFieldValues = [];
                    displayFieldIds.forEach((fieldId) => {
                        const fieldData = transaction?.transactionFields.find((el) => el.fieldId === fieldId);
                        if (fieldData) {
                            transactionFieldValues.push(fieldData.fieldValue);
                        }
                    });

                    const fieldData = transactionFields.find((el) => el.id === transactionField.id);
                    // check if transactionFieldValues is updated get origin transactionFieldValues
                    if (displayFieldIds.length && fieldData) {
                        const { displayAttributeFieldIds = [] } = fieldData;
                        const _displayLabels = [];
                        displayAttributeFieldIds?.length &&
                            displayAttributeFieldIds?.forEach((el) => {
                                const fields = transactionFields.find((tf) => {
                                    return tf.fieldId === el && id === tf.fieldOptionIds?.[0];
                                });
                                fields && _displayLabels.push(fields?.fieldValue);
                            });
                        if (_displayLabels.length && transactionFieldValues.toString() !== _displayLabels.toString()) {
                            transactionFieldValues = _displayLabels;
                        }
                    }
                    // // else if dft deleted get fro, transaction fields
                    else if (!displayFieldIds.length && fieldData) {
                        const { displayAttributeFieldIds = [] } = fieldData;
                        displayFieldIds = displayAttributeFieldIds;
                        const _displayLabels = [];
                        displayAttributeFieldIds?.length &&
                            displayAttributeFieldIds?.forEach((el) => {
                                const fields = transactionFields.find((tf) => {
                                    return tf.fieldId === el && id === tf.fieldOptionIds?.[0];
                                });
                                fields && _displayLabels.push(fields?.fieldValue);
                            });
                        transactionFieldValues = _displayLabels;
                    }

                    result.displayAttributeFieldIds = displayFieldIds;
                    return TransactionFieldUtil.displayFieldLabels(transactionFieldValues);
                });
                break;
            }
        }

        const label = displayLabels.join(', ');
        result.fieldValue = label ? label : transactionField.fieldValue;
        result.fieldOptionIds = fieldOptionIds;
        return result;
    }

    public captureDataRegisterFieldsForLookup({
        selectMode,
        lookupFieldId,
        dataRegisterTransactions,
        transactionId,
        fieldValue,
    }: {
        selectMode: 'single' | 'multiple';
        lookupFieldId: string;
        dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
        transactionId: string;
        fieldValue: string;
    }): TransactionFieldEntity[] {
        let transactionFields: TransactionFieldEntity[] = [];

        if (selectMode === 'single') {
            fieldValue = fieldValue;
        } else if (selectMode === 'multiple') {
            fieldValue = isArray(fieldValue) ? fieldValue?.join(', ') : fieldValue;
        }

        const fieldOptionIds = TransactionFieldUtil.getFieldOptionIds({ mode: selectMode, fieldValue }) || [];

        fieldOptionIds.forEach((optionId) => {
            const transaction = dataRegisterTransactions.find((drt) => drt.id === optionId.trim());
            transaction?.transactionFields?.forEach((drTransField) => {
                let transactionField = new TransactionFieldEntity();
                transactionField.contextType = TransactionFieldContextTypeEnum.FORM;
                transactionField.transactionId = transactionId;
                transactionField.dependFieldId = lookupFieldId;
                transactionField.fieldValue = drTransField.fieldValue;
                transactionField.fieldId = drTransField.fieldId;
                transactionField.fieldOptionIds = [optionId];

                transactionFields.push(transactionField);
            });
        });

        return transactionFields;
    }

    public formatCollectionField({
        mapperField,
        requestField,
        relatedCollectionData,
        relatedLookupData,
        transactionFields,
    }: {
        mapperField: TransactionFieldEntity;
        requestField: EditFormTransactionFieldRequest;
        relatedCollectionData: Partial<FormatCollectionDataType>;
        relatedLookupData: {
            dataRegisters: DataRegisterTenancyEntity[];
            dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
        };
        transactionFields: TransactionFieldEntity[];
    }) {
        if (!requestField) return mapperField;
        const fieldId = requestField.fieldId;
        const collectionItemIdentityId = requestField.collectionItemId;
        const registerFormField = FormatCollectionTransactionFieldService.getFormField({
            fieldId,
            collectionItemIdentityId,
            collectionItems: relatedCollectionData.collectionItems || [],
            registerTransactions: relatedCollectionData.registerTransactions || [],
            registers: relatedCollectionData.registers || [],
            registerVersions: relatedCollectionData.registerVersions || [],
            additionalFields: relatedCollectionData.additionalFields || [],
        });

        if (!registerFormField) return mapperField;

        //format normal fields
        mapperField = this.formatTransactionValueByType({
            type: registerFormField.type,
            formField: registerFormField,
            mapperField,
            extra: {
                relatedLookupData,
            },
            requestField,
            transactionFields,
            excludeTypes: SELECTABLE_FIELD_TYPES,
        });

        //#region Criteria
        //criteria answer, question
        //TODO: LS-2280
        if ([FormFieldTypeEnum.Answer, FormFieldTypeEnum.Comparison, FormFieldTypeEnum.Definable].includes(registerFormField.type)) {
            const additionalField = (relatedCollectionData.additionalFields || []).find(
                (af) => af.fieldId === requestField.fieldId && af.formCollectionItemIdentityId === requestField.collectionItemId,
            );

            if (OBJECT_SELECTABLE_FIELD_TYPES.includes(additionalField?.type)) {
                mapperField = this.formatTransactionValueByType({
                    type: additionalField.type,
                    formField: additionalField,
                    mapperField,
                    extra: {
                        relatedLookupData,
                    },
                    requestField,
                    transactionFields,
                });

                //capture lookup fields transaction
                mapperField = this._captureCollectionLookupData({
                    formField: additionalField,
                    dataRegisterTransactions: relatedLookupData?.dataRegisterTransactions || [],
                    mapperField,
                });

                return mapperField;
            }
        }
        //#endregion Criteria

        //#region Collection
        //collection select, lookup
        if (OBJECT_SELECTABLE_FIELD_TYPES.includes(registerFormField.type)) {
            mapperField = this.formatTransactionValueByType({
                type: registerFormField.type,
                formField: registerFormField,
                mapperField,
                extra: {
                    relatedLookupData,
                },
                requestField,
                transactionFields,
            });

            //capture lookup fields transaction
            mapperField = this._captureCollectionLookupData({
                formField: registerFormField,
                dataRegisterTransactions: relatedLookupData?.dataRegisterTransactions || [],
                mapperField,
            });

            return mapperField;
        }
        //#endregion Collection

        return mapperField;
    }

    public formatFieldConfiguration({
        transactionFields = [],
        formFields = [],
    }: {
        transactionFields: FormTransactionFieldDto[];
        formFields: {
            type: FormFieldTypeEnum;
            fieldId: string;
            configuration?: Record<string, any>;
        }[];
    }) {
        transactionFields.forEach((tf) => {
            // Format sire2Answer
            const fieldData = tf?.data;
            if (fieldData?.fieldValue && 'inspectionId' in fieldData?.fieldValue) {
                tf.fieldValue = fieldData?.fieldValue as any;
            }

            const formField = formFields.find((f) => f.fieldId === tf.fieldId);
            if (!formField) {
                return;
            }

            // if (SELECTABLE_FIELD_TYPES.includes(formField.type)) {
            //     tf.displayValue = tf.fieldValue;
            //     switch (formField?.configuration?.mode) {
            //         case 'multiple':
            //             {
            //                 tf.fieldValue = tf.fieldOptionIds?.join(',') ?? tf.fieldValue;
            //             }
            //             break;
            //         default:
            //             {
            //                 tf.fieldValue = tf.fieldOptionIds?.[0] ?? tf.fieldValue;
            //             }
            //             break;
            //     }
            // }

            tf.type = formField.type;
            tf.mode = formField.configuration?.mode;
            tf.format = formField.configuration?.format;
        });
    }

    getTransactionFieldKey(field: { fieldId?: string; collectionItemId?: string; rowKey?: string; contextType?: string }): string {
        const key =
            field.contextType !== 'collection'
                ? this.getTransactionFormFieldKey({ fieldId: field.fieldId })
                : this.getTransactionCollectionFieldKey({
                      fieldId: field.fieldId,
                      collectionItemId: field.collectionItemId,
                      rowKey: field.rowKey,
                  });

        return key;
    }

    getTransactionFormFieldKey({ fieldId }: { fieldId: string }): string {
        return `form_${fieldId}`;
    }

    getTransactionCollectionFieldKey({
        fieldId,
        collectionItemId,
        rowKey,
    }: Partial<{
        fieldId: string;
        collectionItemId: string;
        rowKey: string;
    }>): string {
        return `collection_${collectionItemId}_${rowKey}_${fieldId}`;
    }

    private _captureCollectionLookupData({
        formField,
        mapperField,
        dataRegisterTransactions,
    }: {
        formField: FormFieldTenancyEntity;
        mapperField: TransactionFieldEntity;
        dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
    }) {
        if (formField?.type === FormFieldTypeEnum.Lookup && mapperField.fieldOptionIds?.length) {
            const transactionIds = uniq(mapperField.fieldOptionIds);
            const transactions = (dataRegisterTransactions || []).filter((drt) => transactionIds.includes(drt.id));
            mapperField.data = {
                ...(mapperField.data ?? {}),
                transactions,
            };
        }

        return mapperField;
    }
}
