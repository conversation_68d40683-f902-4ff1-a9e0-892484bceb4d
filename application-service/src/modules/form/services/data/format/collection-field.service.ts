import { DataRegisterFieldTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';

export type FormatCollectionDataType = {
    collectionItems: FormCollectionItemTenancyEntity[];
    registerTransactions: DataRegisterTransactionTenancyEntity[];
    registers: DataRegisterTenancyEntity[];
    registerVersions: DataRegisterVersionTenancyEntity[];
    additionalFields: FormCollectionAdditionalFieldTenancyEntity[];
    fieldValueTransactions: DataRegisterTransactionTenancyEntity[];
    fieldValueRegisterVersions: DataRegisterVersionTenancyEntity[];
    fieldValueRegisters: DataRegisterTenancyEntity[];
};

export class FormatCollectionTransactionFieldService {
    public static getFormField({
        fieldId,
        collectionItemIdentityId,
        collectionItems,
        registerTransactions,
        registers,
        registerVersions,
        additionalFields,
    }: {
        fieldId: string;
        collectionItemIdentityId: string;
        collectionItems: FormCollectionItemTenancyEntity[];
        registerTransactions: DataRegisterTransactionTenancyEntity[];
        registers: DataRegisterTenancyEntity[];
        registerVersions: DataRegisterVersionTenancyEntity[];
        additionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
    }) {
        const collectionItem = collectionItems.find((ci) => ci.identityId === collectionItemIdentityId);
        if (!collectionItem) return;
        const drTransaction = registerTransactions.find((t) => t.id === collectionItem.dataRegisterTransactionId);
        if (!drTransaction) return;
        const dr = registers.find((d) => d.id === drTransaction.dataRegisterId);
        if (!dr) return;
        const drVersion = registerVersions.find((v) => v.id === dr.activeVersionId);
        if (!drVersion) return;
        const field = (drVersion.fields || []).find((f) => f.fieldId === fieldId);
        if (field) return field;

        //optional, if not existed additional field, return undefined
        const additionalField = additionalFields?.find((af) => af.fieldId === fieldId);
        return additionalField as unknown as DataRegisterFieldTenancyEntity;
    }
}
