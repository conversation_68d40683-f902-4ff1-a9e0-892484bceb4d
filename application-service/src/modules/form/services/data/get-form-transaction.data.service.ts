import { map } from 'rxjs';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as dayjs from 'dayjs';
import { compact, flatten, isEmpty, partition, uniq, uniqBy, merge } from 'lodash';
import { Brackets, FindManyOptions, In, IsNull, Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../../common/src';
import { TRANSACTION_FIELD_ID } from '../../../../common/src/constant/field';
import { FilterOptionDto } from '../../../../common/src/modules/shared/dtos/filter-option.dto';
import { DurationFormatEnum } from '../../../../common/src/modules/shared/enums/duration-format-type.enum';
import { OperatorType } from '../../../../common/src/modules/shared/enums/operator.enum';
import { OrderType } from '../../../../common/src/modules/shared/enums/order.enum';
import { PREFIX } from '../../../../common/src/modules/shared/enums/prefix.enum';
import { FROM_STAGE_FIELD, PUBLISHED_FIELD_ID, VERSION_FIELD_ID } from '../../../../constant';
import { EVENT } from '../../../../constant/event';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { FormVersionEntity } from '../../../../database/src/entities/public/form-version.public.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormViewTenancyEntity } from '../../../../database/src/entities/tenancy/form-view.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { RelationTransactionEntity } from '../../../../database/src/entities/tenancy/relation-transaction.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldStyleEntity } from '../../../../database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { FormViewItemType } from '../../../../database/src/shared/enums/form-view.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { IFormViewType } from '../../../../database/src/types/subscription.type';
import { getRelatedTransactionQuery } from '../../../../raw-queries';
import { getRelatedTransactionFromOriginQuery } from '../../../../raw-queries/get-related-transactions-from-origin.query';
import { PaginationResponseDto } from '../../../../shared/common/dto/pagination-response.dto';
import { FormTransactionQueryBuilderService } from '../../../../shared/services/form-transaction-query-builder.service';
import { convertMinutesToDuration } from '../../../../utils';
import { FormTransactionDto, FormTransactionFieldDto } from '../../dtos';
import { GetListFormTransactionRequestDto } from '../../dtos/requests/get-list-form-transaction.request';
import { RelatedFieldChangeEventDto } from '../../dtos/requests/related-field-change-event.dto';
import { AutoPopulateDataService } from './auto-populate.data.service';
import { FormatTransactionFieldService } from './format/transaction-field.service';

import * as isBetween from 'dayjs/plugin/isBetween';
import * as isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import * as isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

import { JsonTree } from '@react-awesome-query-builder/core';
import { YYYYMMDD, YYYYMMDDHHmm } from '../../../../constant/date';
import { TransactionFieldOverrideEntity } from '../../../../database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { FilterFormTransactionRequestDto } from '../../dtos/requests/filter-form-transaction.request';
import { formatCondition } from 'src/utils/formatConditional';
import * as _ from 'lodash';
import { AnyARecord } from 'dns';

dayjs.extend(isBetween);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

@Injectable()
export class GetFormTransactionDataService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,
        private readonly _transQueryBuilder: FormTransactionQueryBuilderService,
        private readonly _eventEmitter: EventEmitter2,
        // private readonly _producerService: ProducerService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TENANCY_REPOSITORY)
        private readonly formRepository: Repository<FormTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly formVersionRepository: Repository<FormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VIEW_TENANCY_REPOSITORY)
        private readonly _formViewRepository: Repository<FormViewTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private readonly formRelationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private readonly formFieldRepository: Repository<FormFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly transactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly stageRepository: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_STYLE_REPOSITORY)
        private readonly _transactionFieldStyleRepo: Repository<TransactionFieldStyleEntity>,

        private readonly _autoPopulateDataService: AutoPopulateDataService,

        @Inject(PROVIDER_KEYS.AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _autoPopulateRepository: Repository<FormAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly _transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>,

        private readonly _formatField: FormatTransactionFieldService,
    ) {}

    //#region GET
    public async getList({ query }: { query: FilterFormTransactionRequestDto }): Promise<PaginationResponseDto<FormTransactionDto>> {
        query.conditions = formatCondition(query.conditions);
        const { order, filters, sort, sorters, skip, take, conditions, onlyGetTransactionIds } = query;

        const searchTerm = (filters || []).find((filter) => filter.field === 'searchTerm');
        const fieldFilters = (filters || []).filter((filter) => filter.field !== 'searchTerm') || [];
        const [fields, nonFields] = partition(fieldFilters, (filter) => filter.field.includes(PREFIX.DATA_REGISTER_FIELD));
        const formId = nonFields.find((filter) => filter.field === 'formId')?.value;
        if (searchTerm?.value) {
            fields.push({
                field: TRANSACTION_FIELD_ID,
                operator: searchTerm.operator ?? OperatorType.iLike,
                value: searchTerm.value,
            });
        }

        const normalizedFilters: FilterOptionDto[] = fields.map((filter) => ({
            ...filter,
            field: this._transQueryBuilder.withOutPrefix(filter.field),
        }));

        const normalizedFieldIds = normalizedFilters.map((filter) => filter.field);

        const [attributeSortFields, nonAttributeSortFields] = partition(sorters, (filter) =>
            filter.field.includes(PREFIX.DATA_REGISTER_FIELD),
        );
        const attributeSortField = sort && sort.includes(PREFIX.DATA_REGISTER_FIELD) ? this._transQueryBuilder.withOutPrefix(sort) : null;
        const sortField = sort && !sort.includes(PREFIX.DATA_REGISTER_FIELD) ? sort : null;

        const selectFields = compact([...normalizedFieldIds]);

        const { data, total } = await this.getListPagingIds({
            selectFields,
            standardFilters: nonFields,
            orphanedTransaction: query.orphanedTransaction,
            normalizedFilters,
            attributeSortFields: attributeSortFields
                ? attributeSortFields
                : [
                      {
                          field: attributeSortField,
                          order: order,
                      },
                  ],
            sorts: sortField
                ? [
                      {
                          field: sortField,
                          order: order,
                      },
                  ]
                : nonAttributeSortFields
                  ? nonAttributeSortFields
                  : [],
            skip,
            take,
            conditions,
        });

        if (onlyGetTransactionIds) {
            return { data: data as any[], total };
        }

        if (!total) {
            return {
                data: [],
                total: 0,
            };
        }

        try {
            // Bind TransactionData
            const ids = uniq(data);

            if (isEmpty(ids)) {
                return {
                    data: [],
                    total: total,
                };
            }

            const findCondition = {
                where: { id: In(ids) },
                select: [
                    'id',
                    'stageId',
                    'stageName',
                    'formVersionId',
                    'formId',
                    'rollupStatus',
                    'previousStageId',
                    'createdAt',
                    'createdByUser',
                    'updatedAt',
                    'updatedByUser',
                    'stageEnteredAt',
                ],
                relations: [],
            };
            if (query?.fieldIds?.includes(VERSION_FIELD_ID) || query?.fieldIds?.includes(PUBLISHED_FIELD_ID)) {
                findCondition.relations = ['formVersion'];
                findCondition.select.push('formVersion');
            }
            const formTransactionData = await this.formTransactionRepository.find(findCondition as FindManyOptions<TransactionEntity>);

            let trans = await this.bindTransactionFieldFilter({ formId, query, transactionIds: ids, formTransactionData });
            trans = await this.bindForms({ query, formTransactions: trans });

            return {
                data: trans,
                total: total,
            };
        } catch (err) {
            this._logger.error(err);
        }

        return {
            data: [],
            total: 0,
        };
    }

    public async getListPagination({ query }: { query: any }): Promise<any> {
        query.conditions = formatCondition(query.conditions);
        const { pageIndex, pageSize } = query[0];

        const rawQueries = query?.map((q) => {
            const rawQ = this.getRawFormQuery({ query: q });
            return rawQ;
        });

        const { data, total } = await this.getMergedTransactionsPaginatedFromRawQueries(rawQueries, pageIndex, pageSize);

        const ids = uniq(data.map((item) => item.transaction_id)) as any;
        if (isEmpty(ids)) {
            return {
                data: [],
                total: 0,
            };
        }

        const findCondition = {
            where: { id: In(ids) },
            select: [
                'id',
                'stageId',
                'stageName',
                'formVersionId',
                'formId',
                'rollupStatus',
                'previousStageId',
                'createdAt',
                'createdByUser',
                'updatedAt',
                'updatedByUser',
            ],
            relations: [],
        };

        const formTransactionData = await this.formTransactionRepository.find(findCondition as FindManyOptions<TransactionEntity>);

        const trans = await Promise.all(
            query.map(async (q: any) => {
                const formId = q.filters.find((filter) => filter.field === 'formId')?.value;
                return await this.bindTransactionFieldFilter({ formId, query: q, transactionIds: ids, formTransactionData });
            }),
        );

        // Flat the array and ensure uniqueness by id using Lodash
        const flattenedAndUniqueTrans = _.uniqBy(_.flatMap(trans), 'id');

        const finalTrans = await Promise.all(
            query.map(async (q: any) => {
                return await this.bindForms({ query: q, formTransactions: flattenedAndUniqueTrans });
            }),
        );

        const flattenFinals = _.uniqBy(_.flatMap(finalTrans), 'id');

        return {
            data: flattenFinals,
            total: total,
        };
    }

    public getRawFormQuery({ query }: { query: any }): any {
        query.conditions = formatCondition(query.conditions);
        const { order, filters, sort, sorters, skip, take, conditions, onlyGetTransactionIds } = query;

        const searchTerm = (filters || []).find((filter) => filter.field === 'searchTerm');
        const fieldFilters = (filters || []).filter((filter) => filter.field !== 'searchTerm') || [];
        const [fields, nonFields] = partition(fieldFilters, (filter) => filter.field.includes(PREFIX.DATA_REGISTER_FIELD));
        const formId = nonFields.find((filter) => filter.field === 'formId')?.value;
        if (searchTerm?.value) {
            fields.push({
                field: TRANSACTION_FIELD_ID,
                operator: searchTerm.operator ?? OperatorType.iLike,
                value: searchTerm.value,
            });
        }

        const normalizedFilters: FilterOptionDto[] = fields.map((filter) => ({
            ...filter,
            field: this._transQueryBuilder.withOutPrefix(filter.field),
        }));

        const normalizedFieldIds = normalizedFilters.map((filter) => filter.field);

        const [attributeSortFields, nonAttributeSortFields] = partition(sorters, (filter) =>
            filter.field.includes(PREFIX.DATA_REGISTER_FIELD),
        );
        const attributeSortField = sort && sort.includes(PREFIX.DATA_REGISTER_FIELD) ? this._transQueryBuilder.withOutPrefix(sort) : null;
        const sortField = sort && !sort.includes(PREFIX.DATA_REGISTER_FIELD) ? sort : null;

        const selectFields = compact([...normalizedFieldIds]);

        const { rawQuery, rawParams } = this._transQueryBuilder.getRawPaging({
            selectFields,
            standardFilters: nonFields,
            orphanedTransaction: query.orphanedTransaction,
            normalizedFilters,
            attributeSortFields: attributeSortFields
                ? attributeSortFields
                : [
                      {
                          field: attributeSortField,
                          order: order,
                      },
                  ],
            sorts: sortField
                ? [
                      {
                          field: sortField,
                          order: order,
                      },
                  ]
                : nonAttributeSortFields
                  ? nonAttributeSortFields
                  : [],
            skip,
            take,
            conditions,
        });

        return {
            rawQuery,
            rawParams,
        };
    }

    private async getMergedTransactionsPaginatedFromRawQueries(queriesData: any, page: number = 1, limit: number = 20): Promise<any> {
        // Updated return type
        const offset = (page - 1) * limit;

        const subQueries: string[] = [];
        let orderByClause: string = '"sub"."created_at" DESC';
        let hasCreatedAtInSelect = false;

        queriesData.forEach((queryDataItem, queryIndex) => {
            let currentRawQuery = queryDataItem.rawQuery;
            const currentRawParams = queryDataItem.rawParams;

            currentRawQuery = currentRawQuery.replace(/ eq /g, ' = ');

            for (const originalParamName in currentRawParams) {
                if (currentRawParams.hasOwnProperty(originalParamName)) {
                    let paramValue = currentRawParams[originalParamName];

                    if (typeof paramValue === 'string') {
                        paramValue = `'${paramValue.replace(/'/g, "''")}'`;
                    } else if (paramValue instanceof Date) {
                        paramValue = `'${paramValue.toISOString()}'`;
                    } else if (paramValue === null || paramValue === undefined) {
                        paramValue = 'NULL';
                    } else {
                        paramValue = String(paramValue);
                    }

                    currentRawQuery = currentRawQuery.replace(new RegExp(`:${originalParamName}\\b`, 'g'), paramValue);
                }
            }

            const orderByMatch = currentRawQuery.match(/ORDER BY\s+("tr"\."[^"]+"|"[^"]+")\s+(ASC|DESC)/i);
            if (orderByMatch && queryIndex === 0) {
                const originalColumnExpression = orderByMatch[1];
                const direction = orderByMatch[2];

                let actualColumnName = originalColumnExpression;
                if (originalColumnExpression.startsWith('"tr"."')) {
                    actualColumnName = originalColumnExpression.substring('"tr"'.length);
                }

                if (!actualColumnName.startsWith('.')) {
                    actualColumnName = `.${actualColumnName}`;
                }

                orderByClause = `"sub"${actualColumnName} ${direction}`;
            }

            if (!currentRawQuery.includes('"tr"."created_at" AS "created_at"')) {
                currentRawQuery = currentRawQuery.replace(
                    /SELECT\s+"tr"\."id"\s+AS\s+"transaction_id"/,
                    'SELECT "tr"."id" AS "transaction_id", "tr"."created_at" AS "created_at"',
                );
            } else {
                hasCreatedAtInSelect = true;
            }

            currentRawQuery = currentRawQuery.replace(/ORDER BY\s+("tr"\."[^"]+"|"[^"]+")\s+(ASC|DESC)/i, '');
            currentRawQuery = currentRawQuery.replace(/LIMIT\s+\d+/, '');

            subQueries.push(`(${currentRawQuery})`);
        });

        if (!hasCreatedAtInSelect && !orderByClause.includes('"created_at"')) {
            orderByClause = '"sub"."created_at" DESC';
        }

        const combinedSubqueriesSql = subQueries.join(' UNION ALL ');

        // Query for paginated data
        const dataQuery = `
            SELECT
                "sub"."transaction_id",
                "sub"."created_at"
            FROM
                (${combinedSubqueriesSql}) AS "sub"
            ORDER BY
                ${orderByClause}
            LIMIT ${limit} OFFSET ${offset}
        `;

        // Query for total count
        const countQuery = `
            SELECT
                COUNT(*) AS "total"
            FROM
                (${combinedSubqueriesSql}) AS "sub"
        `;

        try {
            // Execute both queries concurrently
            const [dataResults, countResults] = await Promise.all([
                this.formTransactionRepository.query(dataQuery),
                this.formTransactionRepository.query(countQuery),
            ]);

            const total = countResults[0] ? parseInt(countResults[0].total, 10) : 0;

            return {
                data: dataResults,
                total: total,
            };
        } catch (error) {
            console.error('Error executing merged queries:', error);
            throw error;
        }
    }

    private async bindTransactionFieldFilter({
        formId,
        query,
        transactionIds,
        formTransactionData,
    }: {
        formId: string | string[] | number;
        query: FilterFormTransactionRequestDto;
        transactionIds: string[];
        formTransactionData: TransactionEntity[];
    }) {
        const transIds = uniq(formTransactionData?.map((t) => t.id) ?? []);
        const viewType: IFormViewType = (query.viewType?.toString() as IFormViewType) || 'list';
        if (query.limitField && !query?.fieldIds?.length) {
            const formVersionId = formTransactionData[0]?.formVersionId;
            if (formVersionId) {
                const formViews = await this._formViewRepository.find({
                    where: {
                        formVersionId: formVersionId,
                        type: viewType as IFormViewType,
                    },
                });
                const formViewConfigs = uniqBy(flatten(formViews?.map((el) => el.config) || []), 'fieldId');
                query.fieldIds = formViewConfigs?.map((el) => el?.fieldId);
            }
        }

        const whereOptions = {
            collectionId: IsNull(),
            transactionId: In(transIds),
            ...((query?.fieldIds?.length && { fieldId: In(query?.fieldIds) }) || {}),
        };

        //TODO: removing joining field overrides
        const transactionFields = await this.transactionFieldRepository.find({
            where: whereOptions,
            // relations: {
            //     transactionFieldStyle: true,
            //     transactionFieldOverrides: true,
            // },
        });
        const transactionFieldStyles = await this._transactionFieldStyleRepo.findBy({ transactionId: In(transIds) });
        const transactionFieldOVerrides = await this._transactionFieldOverrideRepo.findBy({ transactionId: In(transIds) });

        formTransactionData.forEach((value) => {
            value.transactionFields = transactionFields.filter((field) => field.transactionId === value.id) || [];
            value.transactionFields.forEach((tranField) => {
                tranField.transactionFieldOverrides = transactionFieldOVerrides.filter(
                    (override) => override.transactionFieldId === tranField.id,
                );
                tranField.transactionFieldStyle = transactionFieldStyles.find((style) => style.id === tranField.id);
            });
        });

        let trans = this._mapper.mapArray(formTransactionData, TransactionEntity, FormTransactionDto);
        trans = await this._mapFieldConfiguration(trans, formId as string);

        const idIndexMap: Map<string, number> = new Map(transactionIds.map((id, index) => [id, index]));

        trans.sort((a, b) => {
            const indexA = idIndexMap.get(a.id) ?? -1; // Use -1 as a fallback value if id is not found
            const indexB = idIndexMap.get(b.id) ?? -1; // Use -1 as a fallback value if id is not found
            return indexA - indexB;
        });

        return trans;
    }

    private async bindForms({
        query,
        formTransactions,
    }: {
        query: FilterFormTransactionRequestDto;
        formTransactions: FormTransactionDto[];
    }) {
        if (query.includeForm?.toString() === 'true') {
            const formIds = uniq(compact(formTransactions.map((t) => t.formId)));
            const forms = await this.formRepository.findBy({ id: In(formIds) });
            formTransactions.forEach((t) => {
                const form = forms.find((s) => s.id === t.formId);
                t.formName = form?.name;
            });
        }

        return formTransactions;
    }

    public async getKanban({ query }: { query: GetListFormTransactionRequestDto }): Promise<PaginationResponseDto<FormTransactionDto>> {
        try {
            const formId = query.filters.find((item) => item.field === 'formId')?.value as string;
            const viewId = query.filters.find((item) => item.field === 'viewId')?.value as string;

            if (!formId || !viewId) {
                return {
                    data: [],
                    total: 0,
                };
            }

            const kanbanSearch = query.filters.find((item) => item.field === 'kanbanSearch');
            const filterString = query.filters.find((item) => item.field === 'filter')?.value as string;

            const fieldValue = kanbanSearch?.value;

            const formView = await this._formViewRepository.findOne({
                where: {
                    id: viewId,
                },
                relations: ['items'],
            });

            if (!formView) {
                return {
                    data: [],
                    total: 0,
                };
            }

            let filterFieldIds: string[] = [TRANSACTION_FIELD_ID];

            const columnConstraintId = formView?.columnConstraintId;

            const swimlaneConstraintIds = compact(
                uniq(
                    formView.items
                        ?.filter((item) => item.type === FormViewItemType.Swimlane)
                        ?.map((item) => item.config?.swimlaneConstraintId),
                )?.filter((id) => id !== FROM_STAGE_FIELD),
            );

            if (columnConstraintId && columnConstraintId !== FROM_STAGE_FIELD) {
                filterFieldIds.push(columnConstraintId);
            }

            if (swimlaneConstraintIds?.length) {
                filterFieldIds = [...filterFieldIds, ...swimlaneConstraintIds];
            }

            const layoutConfigColumns: Array<any> | undefined = formView?.items?.find((item) => item.type === FormViewItemType.CardLayout)
                ?.config?.cardLayoutConfig?.columns;

            const layoutFields = (layoutConfigColumns || [])?.flatMap((column) => column.layoutFields || []);

            if (layoutFields.length) {
                filterFieldIds.push(...uniq(compact(layoutFields.map((field) => field?.fieldId))));
            }

            const qb = this.formTransactionRepository
                .createQueryBuilder('transactions')
                .leftJoinAndSelect('transactions.formVersion', 'formVersion')
                .where('transactions.formId = :formId', { formId });

            if (fieldValue) {
                qb.leftJoin(TransactionFieldEntity, 'transactionFields', 'transactionFields.transactionId = transactions.id');
                if (filterFieldIds.length) {
                    qb.andWhere(
                        new Brackets((qb) => {
                            filterFieldIds.forEach((fieldId: string, index: number) => {
                                qb.orWhere(
                                    new Brackets((qb) => {
                                        qb.where(`transactionFields.field_id = :fieldId_${index}`, {
                                            [`fieldId_${index}`]: fieldId,
                                        }).andWhere(`transactionFields.fieldValue ILIKE :fieldValue`, {
                                            [`fieldValue`]: `%${fieldValue}%`,
                                        });
                                    }),
                                );
                            });
                        }),
                    );
                } else {
                    // search all if no filterFieldIds is configured
                    qb.andWhere('transactionFields.fieldValue ILIKE :fieldValue', { fieldValue: `%${fieldValue}%` });
                }
            }

            qb.orderBy('transactions.updatedAt', 'DESC');

            const transData = await qb.getMany();

            if (!transData.length) {
                return {
                    data: [],
                    total: 0,
                };
            }

            const transIds = transData.map((t) => t.id);

            const filterableFields = (formView?.extraConfig as any)?.filterConfig as string[];

            if (filterableFields?.length) {
                filterFieldIds.push(...filterableFields);
            }

            // Construct whereOptions dynamically
            const whereOptions = {
                transactionId: In(transIds),
                ...((query.limitField?.toString() === 'true' && filterFieldIds.length && { fieldId: In(uniq([...filterFieldIds])) }) || {}),
            };

            //TODO: removing joining field overrides
            const transactionFields = await this.transactionFieldRepository.find({
                where: whereOptions,
                // relations: {
                //     transactionFieldStyle: true,
                //     transactionFieldOverrides: true,
                // },
            });
            const transactionFieldStyles = await this._transactionFieldStyleRepo.findBy({ transactionId: In(transIds) });
            const transactionFieldOVerrides = await this._transactionFieldOverrideRepo.findBy({ transactionId: In(transIds) });

            transData.forEach((value) => {
                value.transactionFields = transactionFields.filter((field) => field.transactionId === value.id) || [];
                value.transactionFields.forEach((tranField) => {
                    tranField.transactionFieldOverrides = transactionFieldOVerrides.filter(
                        (override) => override.transactionFieldId === tranField.id,
                    );
                    tranField.transactionFieldStyle = transactionFieldStyles.find((style) => style.id === tranField.id);
                });
            });

            let trans = this._mapper.mapArray(transData, TransactionEntity, FormTransactionDto);

            if (query.includeStage?.toString() === 'true') {
                const stageIds = uniq(compact(trans.map((t) => t.stageId)));
                const stageEntities = await this.stageRepository.findBy({ id: In(stageIds) });
                trans.forEach((t) => {
                    const stage = stageEntities.find((s) => s.id === t.stageId);
                    t.stageName = stage?.name;
                    t.stageIdentityId = stage?.identityId;
                });
            }

            if (!filterString) {
                trans = await this._mapFieldConfiguration(trans, formId as string);
                return {
                    data: trans,
                    total: trans.length,
                };
            }

            const filterFields = JSON.parse(filterString) as Record<
                string,
                {
                    type: FormFieldTypeEnum | 'stage' | 'validationResult';
                    value: any;
                    fromValue?: any;
                    toValue?: any;
                }
            >;

            if (isEmpty(filterFields)) {
                return {
                    data: trans,
                    total: trans.length,
                };
            }
            let filteredTrans = this._filterKanbanData(trans, filterFields);
            filteredTrans = await this._mapFieldConfiguration(filteredTrans, formId as string);

            return {
                data: filteredTrans,
                total: filteredTrans.length,
            };
        } catch (err) {
            this._logger.error(err);
            return {
                data: [],
                total: 0,
            };
        }
    }

    public async getListPagingIds(options?: {
        orphanedTransaction: boolean;
        selectFields: string[];
        standardFilters: FilterOptionDto[];
        normalizedFilters: FilterOptionDto[];
        attributeSortFields: { field: string; order: OrderType }[];
        sorts: { field: string; order: OrderType }[];
        skip: number;
        take: number;
        conditions?: JsonTree;
        withRelationDeep?: boolean;
    }): Promise<{ data: string[]; total: number }> {
        const {
            selectFields,
            orphanedTransaction,
            standardFilters,
            normalizedFilters,
            attributeSortFields,
            sorts,
            skip,
            take,
            conditions,
        } = options;

        try {
            const { data: transactionIds, total } = await this._transQueryBuilder.getPaging({
                orphanedTransaction,
                selectFields,
                standardFilters,
                normalizedFilters,
                attributeSortFields,
                sorts,
                skip: options.withRelationDeep ? 0 : skip,
                take: options.withRelationDeep ? 99999 : take,
                conditions,
            });

            return {
                data: transactionIds,
                total: total,
            };
        } catch (err) {
            this._logger.error(err);
        }

        return {
            data: [],
            total: 0,
        };
    }

    public async getFields({
        id,
        fieldIds,
        collectionId,
        isTest,
    }: {
        id: string;
        fieldIds?: string[];
        collectionId?: string;
        isTest?: boolean;
    }): Promise<FormTransactionDto> {
        let data: TransactionEntity = await this._getTransactionByFieldsOrTransactionFields({
            id,
            fieldIds,
            collectionId,
            byTransactionFieldId: true,
            isTest,
        });
        return await this._fromTransactionFieldToDto(data);
    }

    public async getByFormFieldIds({
        id,
        fieldIds,
        collectionId,
    }: {
        id: string;
        fieldIds?: string[];
        collectionId?: string;
    }): Promise<FormTransactionDto> {
        let data: TransactionEntity = await this._getTransactionByFieldsOrTransactionFields({
            id,
            fieldIds,
            collectionId,
        });
        return await this._fromTransactionFieldToDto(data);
    }

    private async _fromTransactionFieldToDto(data: TransactionEntity): Promise<FormTransactionDto> {
        const isEmptyCollectionFields = data.transactionFields.some(
            (transactionField) => transactionField.contextType === TransactionFieldContextTypeEnum.COLLECTION,
        );

        const formTemplate = await this.getFormWithActiveVersion({
            formId: data.formId,
            formVersionId: data.formVersionId,
        });

        if (!formTemplate?.formVersions?.length) {
            throw new NotFoundException('form_template_not_found');
        }

        const formVersion = formTemplate.formVersions[0];

        const dto = this._mapper.map(data, TransactionEntity, FormTransactionDto);

        dto.formVersion = formTemplate.activeVersion;

        const fieldWithStyles = await this._getTransactionFieldStyle({
            transactionFields: dto.transactionFields,
        });

        dto.transactionFields = fieldWithStyles;

        //map option ids to field value for select fields
        this._formatField.formatFieldConfiguration({
            transactionFields: dto?.transactionFields || [],
            formFields: formVersion?.fields || [],
        });

        dto.isEmptyCollectionFields = isEmptyCollectionFields;
        return dto;
    }

    private async _getTransactionByFieldsOrTransactionFields({
        id,
        fieldIds,
        collectionId,
        byTransactionFieldId,
        isTest,
    }: {
        id: string;
        fieldIds?: string[];
        collectionId?: string;
        byTransactionFieldId?: boolean;
        isTest?: boolean;
    }) {
        let data: TransactionEntity;
        try {
            data = await this.formTransactionRepository.findOne({
                where: { id },
                withDeleted: !!isTest,
            });

            //TODO: removing joining field overrides
            if (fieldIds?.length) {
                const field = byTransactionFieldId ? 'id' : 'fieldId';
                data.transactionFields = await this.transactionFieldRepository.find({
                    where: {
                        transactionId: id,
                        [field]: In(fieldIds),
                    },
                    // relations: {
                    //     transactionFieldOverrides: true,
                    // },
                    withDeleted: false,
                });
            } else {
                //TODO: removing joining field overrides
                data.transactionFields = await this.transactionFieldRepository.find({
                    where: {
                        transactionId: id,
                        collectionId,
                    },
                    // relations: {
                    //     transactionFieldOverrides: true,
                    // },
                    withDeleted: false,
                });
            }
            const transactionFieldOVerrides = await this._transactionFieldOverrideRepo.findBy({ transactionId: id });

            data.transactionFields?.forEach((tranField) => {
                tranField.transactionFieldOverrides = transactionFieldOVerrides.filter(
                    (override) => override.transactionFieldId === tranField.id,
                );
            });
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
        return data;
    }

    public async get({
        id,
        request,
        fieldIds,
    }: {
        id: string;
        request?: {
            includeRelatedTransactionIds?: boolean;
            includeOriginTransactionIds?: boolean;
            includeFieldContextTypes?: TransactionFieldContextTypeEnum[];
            isTest?: boolean;
        };
        fieldIds?: string[];
    }): Promise<FormTransactionDto> {
        let data: TransactionEntity;
        try {
            data = await this.formTransactionRepository.findOne({
                where: { id },
                select: {
                    id: true,
                    formId: true,
                    formVersionId: true,
                    createdAt: true,
                    createdByUser: true,
                    updatedAt: true,
                    updatedByUser: true,
                    deletedAt: true,
                    stageId: true,
                    previousStageId: true,
                    rollupStatus: {
                        status: true,
                    },
                    autoCreationStatus: {
                        status: true,
                    },
                    isTest: true,
                    stageEnteredAt: true,
                },
                withDeleted: request?.isTest ?? false,
            });

            //TODO: removing joining field overrides
            const includeFieldContextTypes = request?.includeFieldContextTypes;
            let condition: Record<string, any> = { transactionId: id };
            if (includeFieldContextTypes?.length) {
                condition = [
                    { transactionId: id, contextType: In(includeFieldContextTypes) },
                    { transactionId: id, contextType: IsNull() },
                ];
            }
            console.time('find tran fields');
            const transactionFields = await this.transactionFieldRepository.find({
                where: condition,
                select: {
                    id: true,
                    fieldId: true,
                    fieldValue: true,
                    fieldOptionIds: true,
                    contextType: true,
                    rowKey: true,
                    pairId: true,
                    dependFieldId: true,
                    sourceFieldId: true,
                    inVisible: true,
                    data: true,
                    collectionId: true,
                    collectionItemId: true,
                    parentId: true,
                    displayAttributeFieldIds: true,
                    validationValue: true,
                    fieldType: true,
                    transactionId: true,
                    transactionFieldOverrides: {
                        id: true,
                        transactionFieldId: true,
                        validationValue: true,
                        fromValue: true,
                        type: true,
                        status: true,
                        comment: true,
                        dependencyValues: true,
                    },
                },
                // relations: {
                //     transactionFieldOverrides: true,
                // },
            });
            console.timeEnd('find tran fields');
            const transactionFieldOVerrides = await this._transactionFieldOverrideRepo.findBy({ transactionId: id });
            transactionFields?.forEach((tranField) => {
                tranField.transactionFieldOverrides = transactionFieldOVerrides.filter(
                    (override) => override.transactionFieldId === tranField.id,
                );
            });

            data.transactionFields = transactionFields;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }

        let isEmptyCollectionFields = true;

        const transactionFields: TransactionFieldEntity[] = [];
        data.transactionFields.forEach((transactionField) => {
            if (!transactionField.deletedAt && (fieldIds ? fieldIds.includes(transactionField.fieldId) : true)) {
                transactionFields.push(transactionField);
            }
            if (transactionField.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                isEmptyCollectionFields = false;
            }
        });

        data.transactionFields = transactionFields;

        const formVersion = await this.getFormVersion({ formVersionId: data.formVersionId });

        if (!formVersion) {
            throw new NotFoundException('form_template_not_found');
        }

        const dto = this._mapper.map(data, TransactionEntity, FormTransactionDto);
        dto.formVersion = formVersion.version;

        const fieldWithStyles = await this._getTransactionFieldStyle({
            transactionId: dto.id,
            transactionFields: dto.transactionFields,
        });

        dto.transactionFields = fieldWithStyles;

        //map option ids to field value for select fields
        this._formatField.formatFieldConfiguration({
            transactionFields: dto?.transactionFields || [],
            formFields: formVersion?.fields || [],
        });

        if (request?.includeRelatedTransactionIds) {
            const result = await this.formRelationTransactionRepository.find({
                where: {
                    originTransactionId: dto.id,
                },
            });

            dto.relatedTransactionIds = result ? result.map((r) => r.targetTransactionId) : [];
        }

        if (request?.includeOriginTransactionIds) {
            const result = await this.formRelationTransactionRepository.find({
                where: {
                    targetTransactionId: dto.id,
                },
            });

            dto.originTransactionIds = result ? result.map((r) => r.originTransactionId) : [];
        }

        dto.isEmptyCollectionFields = isEmptyCollectionFields;

        return dto;
    }

    public async getRelatedTransactions({
        transactionId,
        transactionRepo,
        accountId,
        excludeOriginTransaction,
        fromOrigin,
    }: {
        transactionId: string;
        transactionRepo: Repository<TransactionEntity>;
        accountId?: string;
        excludeOriginTransaction?: boolean;
        fromOrigin?: boolean;
    }) {
        if (!accountId && !this._claims.accountId) {
            return [];
        }
        const query = fromOrigin
            ? getRelatedTransactionFromOriginQuery(accountId || this._claims.accountId)
            : getRelatedTransactionQuery(accountId || this._claims.accountId);
        const transactionResults = await transactionRepo.query(query, [transactionId]);

        let transactionIds: string[] = transactionResults.map((raw) => raw.id);

        if (excludeOriginTransaction) {
            transactionIds = transactionIds.filter((t) => t !== transactionId);
        }

        const [transactionEntities, transactionFields] = await Promise.all([
            transactionRepo.find({
                where: {
                    id: In(transactionIds),
                },
            }),
            this.transactionFieldRepository.find({
                where: {
                    transactionId: In(transactionIds),
                },
            }),
        ]);

        transactionEntities.forEach((transaction) => {
            transaction.transactionFields = transactionFields.filter((field) => field.transactionId === transaction.id);
        });

        return this._mapper.mapArray(transactionEntities, TransactionEntity, FormTransactionDto);
    }

    //#endregion GET

    public getFormWithActiveVersion = async ({
        formId,
        formVersionId,
        version,
    }: {
        formId: string;
        formVersionId?: string;
        version?: number;
    }): Promise<FormTenancyEntity> => {
        const builder = await this.formRepository
            .createQueryBuilder('form')
            .leftJoinAndSelect('form.formVersions', 'formVersion')
            .leftJoinAndSelect('formVersion.fields', 'field')
            .leftJoinAndSelect('formVersion.stages', 'stages')
            .where({
                id: formId,
            });

        if (formVersionId) {
            builder.andWhere('formVersion.id = :formVersionId', { formVersionId });
        } else if (version) {
            builder.andWhere('formVersion.version = :version', { version });
        } else {
            builder.andWhere('formVersion.id = form.activeVersionId');
        }

        const result = await builder.getOne();

        return result;
    };

    public getFormVersion = async ({ formVersionId }: { formVersionId: string }): Promise<FormVersionTenancyEntity> => {
        const formVersion = await this.formVersionRepository.findOne({
            select: {
                id: true,
                version: true,
                formId: true,
                fields: {
                    id: true,
                    fieldId: true,
                    type: true,
                    formVersionId: true,
                    label: true,
                    isDefault: true,
                    configuration: true,
                    lookupDataset: true,
                    lookupTargetId: true,
                    rollupDependencies: true,
                },
            },
            where: {
                id: formVersionId,
            },
            relations: {
                fields: true,
            },
        });
        return formVersion;
    };

    public async processRelatedFieldsChange(
        formVersion: FormVersionEntity | FormVersionTenancyEntity,
        transactionId: string,
        tranFields: TransactionFieldEntity[],
        stageId: string,
    ): Promise<void> {
        if (!formVersion || !formVersion.fields?.length || !tranFields?.length) {
            return;
        }

        let onChangeFields = [];
        const dtos: RelatedFieldChangeEventDto = {
            accountId: this._claims.accountId,
            formId: formVersion.formId,
            formVersionId: formVersion.id,
            transactionId: transactionId,
            tranFields: [],
            stageId: stageId,
        };

        // get list pull register
        const autoPopulateSettings = await this._autoPopulateDataService.getAllAutoPopulateFields(
            formVersion?.formId,
            this._autoPopulateRepository,
        );

        formVersion.fields.forEach((item: FormFieldTenancyEntity) => {
            const fieldIds = autoPopulateSettings?.filter((f) => f.originFieldId === item.fieldId || f.targetFieldId === item.fieldId);
            onChangeFields = [...onChangeFields, ...(fieldIds ?? [])];
        });
        if (onChangeFields?.length) {
            dtos.tranFields = tranFields;
            this._eventEmitter.emit(EVENT.RELATED_FIELD_CHANGE, dtos);
        }
    }

    //#region PRIVATE METHODS

    public async _getFieldConfiguration({
        fieldIds,
        formVersionId,
    }: {
        fieldIds: string[];
        formVersionId: string;
    }): Promise<FormFieldTenancyEntity[]> {
        const data = await this.formFieldRepository.find({
            where: {
                fieldId: In(fieldIds),
                formVersionId,
            },
            select: ['id', 'fieldId', 'configuration', 'type'],
        });

        return data;
    }

    private _mapFieldConfiguration = async (data: Array<FormTransactionDto>, formId: string) => {
        let fieldIds = [];

        data?.forEach((value) => {
            value.transactionFields?.forEach((field) => {
                fieldIds.push(field.fieldId);
            });
        });

        if (fieldIds.length) {
            fieldIds = uniq(fieldIds);

            const form = await this.formRepository.findOneBy({ id: formId?.toString() });
            const fields = await this._getFieldConfiguration({
                fieldIds,
                formVersionId: form.activeVersionId,
            });

            data?.forEach((value) => {
                for (let field of value.transactionFields) {
                    const _field = fields.find((item) => item.fieldId === field.fieldId);
                    if (_field) {
                        field.format = _field.configuration?.format;
                        field.type = _field?.type;

                        //format duration field
                        const isCalculationDuration =
                            _field.type === FormFieldTypeEnum.Calculation &&
                            _field.configuration?.calculationFormula?.dataType === FormFieldTypeEnum.Duration;
                        const isRollupDuration =
                            _field.type === FormFieldTypeEnum.Rollup &&
                            _field.configuration?.rollup?.dataType === FormFieldTypeEnum.Duration;
                        const isRollupValidationResult =
                            _field.type === FormFieldTypeEnum.Rollup &&
                            _field.configuration?.rollup?.dataType === FormFieldTypeEnum.ValidationResult;
                        const isDuration = _field.type === FormFieldTypeEnum.Duration;
                        if (isCalculationDuration || isRollupDuration || isDuration) {
                            if (field?.fieldValue && !isNaN(+field.fieldValue)) {
                                field.fieldValue = convertMinutesToDuration({
                                    value: +field.fieldValue,
                                    format: (field.format as DurationFormatEnum) ?? DurationFormatEnum.DHM,
                                });
                            }
                        }

                        if (isRollupValidationResult) {
                            field.fieldValue = '';
                        }
                    }
                }
            });
        }

        return data;
    };

    private async _getTransactionFieldStyle({
        transactionId,
        transactionFields,
    }: {
        transactionId?: string;
        transactionFields: FormTransactionFieldDto[];
    }) {
        if (!transactionFields?.length && !transactionId) return [];

        let where: Record<string, any> = {};

        if (transactionId) {
            where = {
                transactionId,
            };
        } else if (transactionFields?.length) {
            where = {
                id: In(transactionFields.map((f) => f.id)),
            };
        }

        const styles = await this._transactionFieldStyleRepo.find({
            select: {
                id: true,
                transactionId: true,
                configuration: true,
                fieldId: true,
            },
            where,
        });

        if (styles.length) {
            const styleMap = new Map(styles.map((style) => [style.id, style]));

            transactionFields.forEach((field) => {
                const style = styleMap.get(field.id);
                if (style) {
                    field.style = style;
                }
            });
        }
        return transactionFields;
    }

    private _filterKanbanData = (
        data: FormTransactionDto[],
        filterFields: Record<
            string,
            {
                type: FormFieldTypeEnum | 'stage' | 'validationResult';
                value: any;
                fromValue?: any;
                toValue?: any;
            }
        >,
    ) => {
        Object.entries(filterFields).forEach(([fieldId, filter]) => {
            if ((!filter.value && !filter.fromValue && !filter.toValue) || (Array.isArray(filter.value) && !filter.value.length)) return;

            switch (filter.type) {
                case 'stage': {
                    data = data.filter((t) => t.stageIdentityId === filter.value);
                    break;
                }

                case 'validationResult': {
                    data = data.filter((t) => {
                        const field = t.transactionFields.find((f) => f.fieldId === fieldId);
                        if (!field) return false;
                        return field?.validationValue === filter.value;
                    });
                    break;
                }
                case FormFieldTypeEnum.Duration:
                case FormFieldTypeEnum.TimePicker:
                    {
                        data = data.filter((t) => {
                            const field = t.transactionFields.find((f) => f.fieldId === fieldId);
                            if (!field) return false;
                            const numberValue = field?.fieldValue ? +field.fieldValue : 0;
                            if (filter.fromValue && filter.toValue) {
                                return numberValue >= filter.fromValue && numberValue <= filter.toValue;
                            }

                            if (filter.fromValue) {
                                return numberValue >= filter.fromValue;
                            }

                            if (filter.toValue) {
                                return numberValue <= filter.toValue;
                            }
                        });
                    }
                    break;
                case FormFieldTypeEnum.DatePicker:
                case FormFieldTypeEnum.DatetimePicker:
                    {
                        const format = (filter.type as FormFieldTypeEnum) === FormFieldTypeEnum.DatePicker ? YYYYMMDD : YYYYMMDDHHmm;
                        data = data.filter((t) => {
                            const field = t.transactionFields.find((f) => f.fieldId === fieldId);
                            if (!field) return false;

                            if (!field.fieldValue) return false;

                            const dateValue = dayjs(field.fieldValue, format);

                            const fromDateValue = filter.fromValue ? dayjs(filter.fromValue, format) : null;
                            const toDateValue = filter.toValue ? dayjs(filter.toValue, format) : null;

                            if (fromDateValue && toDateValue) {
                                return dateValue.isBetween(fromDateValue, toDateValue, null, '[]');
                            }

                            if (fromDateValue) {
                                return dateValue.isSameOrAfter(fromDateValue);
                            }

                            if (toDateValue) {
                                return dateValue.isSameOrBefore(toDateValue);
                            }
                        });
                    }
                    break;
                case FormFieldTypeEnum.Select:
                case FormFieldTypeEnum.Lookup:
                case FormFieldTypeEnum.UserLookup:
                case FormFieldTypeEnum.RoleLookup:
                    {
                        data = data.filter((t) => {
                            const field = t.transactionFields.find((f) => f.fieldId === fieldId);
                            if (!field) return false;
                            const fieldOptionIds = field?.fieldOptionIds || [];

                            const formattedValue = Array.isArray(filter.value) ? filter.value : [filter.value];

                            return formattedValue.every((value) => fieldOptionIds.includes(value));
                        });
                    }
                    break;

                case FormFieldTypeEnum.Number:
                    {
                        data = data.filter((t) => {
                            const field = t.transactionFields.find((f) => f.fieldId === fieldId);
                            if (!field) return false;
                            const numberValue = field?.fieldValue ? +field.fieldValue : 0;
                            return numberValue === filter.value;
                        });
                    }
                    break;

                default:
                    {
                        data = data.filter((t) => {
                            const field = t.transactionFields.find((f) => f.fieldId === fieldId);
                            if (!field) return false;
                            return field.fieldValue?.toLowerCase() === filter.value?.toString()?.toLowerCase();
                        });
                    }
                    break;
            }
        });
        return data;
    };

    //#endregion PRIVATE METHODS
}
