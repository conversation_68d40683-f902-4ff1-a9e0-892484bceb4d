import { Inject, Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { Dictionary, compact, groupBy, isEmpty, keyBy, uniq } from 'lodash';
import { Brackets, In, IsNull, Not, Repository, WhereExpressionBuilder } from 'typeorm';
import { v4 as uuid } from 'uuid';
import { LoggerService } from '../../../common/src';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { MMDDYYYY, TRANSPORT_DATE_TIME_FORMAT } from '../../../constant/date';
import { DATABASE_DATE_TIME_FORMAT } from '../../../constant/datetime';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { RollupDependencyTenancyEntity } from '../../../database/src/entities/tenancy/roll-up-dependency.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { TransactionFieldOverrideEntity } from '../../../database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { TransactionFieldStyleEntity } from '../../../database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { StatusEnum } from '../../../database/src/enums/transaction-status.enum';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { OverrideTypeEnum } from '../../../database/src/shared/enums/override-type.enum';
import { RollUpSecondContextEnum, TargetTypeEnum } from '../../../database/src/shared/enums/roll-up-dependency.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { addSystemOverrideRecords } from '../../../utils';
import { FormTransactionDto } from '../dtos';
import { RollUpCalculationEvent } from '../dtos/events/rollup-calculation.event';
import { RollUpCalculationRequest } from '../dtos/requests/rollup-calculation.request';
import { RollupOperator } from '../enums/rollup-operator.enum';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { GetFormTransactionDataService } from './data/get-form-transaction.data.service';
import { RelatedLookupDataService } from './data/related-lookup-data.service';
import { RollupCalcUtils } from './data/util/rollup-calc.util';
import { FormTransactionStatusTenancyService } from './form-transaction-status.tenancy.service';

@Injectable()
export class RollUpCalculationService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private formFieldRepository: Repository<FormFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.ROLL_UP_DEPENDENCY_TENANCY_REPOSITORY)
        private rollUpDependencyRepository: Repository<RollupDependencyTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private transactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private transactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_STYLE_REPOSITORY)
        private transactionStyleRepository: Repository<TransactionFieldStyleEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly _transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>,

        private readonly getTransactionDataService: GetFormTransactionDataService,
        private readonly logger: LoggerService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly formTransactionStatusTenancyService: FormTransactionStatusTenancyService,

        @Inject(PROVIDER_KEYS.STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY)
        private stageAccessControlRepository: Repository<StageAccessControlTenancyEntity>,
        private readonly _relatedLookupDataService: RelatedLookupDataService,
        private readonly _formTransactionDataService: FormTransactionDataService,
    ) {}

    private async rollUpForOneTransaction(
        relationId: string,
        request: RollUpCalculationRequest,
        relatedRollupMap: Map<string, RollupDependencyTenancyEntity[]>,
        relatedTransactionFields: Array<TransactionFieldEntity> = [],
        transactionGroup: Record<string, { formId: string; formVersionId: string }>,
        updatedResult: {
            updatedFields: any[];
            updatedFieldStyles: any[];
            originalFields: Map<string, any>;
        },
    ) {
        // start rollup for parent transaction
        await this.formTransactionStatusTenancyService.setParentRollupStatus(
            request.transactionId,
            StatusEnum.Processing,
            relationId,
            request.isTest,
        );

        const isTest = !!request.isTest;

        const rollupDepsFields = relatedRollupMap.get(relationId);

        const originalTransaction = await this.transactionRepository.findOne({
            where: { id: relationId },
            withDeleted: isTest,
        });

        if (!originalTransaction) {
            // Deleted
            this.logger.warn(`No transaction found for transaction ${relationId}`);
            return;
        }

        if (!rollupDepsFields.length) {
            this.logger.info(`No rollup dependencies found for transaction with relationId ${relationId}`);
            return;
        }

        transactionGroup[originalTransaction.id] = {
            formId: originalTransaction.formId,
            formVersionId: originalTransaction.formVersionId,
        };

        this.logger.info(`Rollup calculation for transaction ${relationId}`);
        const formIds = uniq(compact(rollupDepsFields.map((fv) => fv.relatedFormId)));

        // Get all related transactions of this transaction
        const deepMatchingTransactions = await this.getTransactionDataService.getRelatedTransactions({
            transactionId: relationId,
            transactionRepo: this.transactionRepository,
            accountId: request.accountId,
            excludeOriginTransaction: true,
            fromOrigin: true,
        });

        const relationTransactions = deepMatchingTransactions.filter((mt) => formIds.includes(mt.formId));
        relationTransactions?.forEach((item) => {
            transactionGroup[item.id] = { formId: item.formId, formVersionId: item.formVersionId };
        });

        const relationTransactionGroups = groupBy<FormTransactionDto>(relationTransactions, 'formId');

        const targetGroups = groupBy(rollupDepsFields, 'fieldId');

        //TODO: remove locked fields

        const fieldIds = rollupDepsFields.map((tf) => tf.formFieldId);

        const fields = await this.formFieldRepository.find({
            select: ['configuration', 'fieldId'],
            where: {
                id: In(fieldIds),
            },
        });

        const fieldsByKey = keyBy(fields, 'fieldId');

        await Promise.all(
            Object.keys(targetGroups).map(async (formFieldId) => {
                const rollupTargetFields = targetGroups[formFieldId];

                if (fieldsByKey[formFieldId]) {
                    const rollupField = fieldsByKey[formFieldId];
                    const operator = rollupField?.configuration?.rollup?.operator;
                    const dataType = rollupField?.configuration?.rollup?.dataType;

                    const mapOperator = this._mapOperator(operator);

                    // Apply condition for rollup
                    const {
                        ableRollupRowKeysForCollectionFields,
                        childCollectionItemIdsChecked,
                        notQualifiedTransactionIdsPerDependCollectionItem,
                        notQualifiedTransactionIdsPerDependField,
                        rollupFieldsWithConditionChecked,
                        notQualifiedTransactionIdsPerDependChildCollectionItem,
                    } = await RollupCalcUtils.applyRollupCondition({
                        rollupField,
                        rollupTargetFields: rollupTargetFields,
                        originalTransactionId: originalTransaction.id,
                        relationTransactionGroups: relationTransactionGroups,
                        transactionFieldRepository: this.transactionFieldRepository,
                    });

                    if (rollupFieldsWithConditionChecked.length === 0) {
                        this.logger.info(`No rollup dependencies condition are matched for transaction ${originalTransaction.id}`);
                        if (rollupFieldsWithConditionChecked.length !== rollupTargetFields.length) {
                            const originalRollUpTransactionField = await this.transactionFieldRepository.findOneBy({
                                transactionId: originalTransaction.id,
                                fieldId: rollupField.fieldId,
                            });

                            if (
                                originalRollUpTransactionField &&
                                (originalRollUpTransactionField.fieldValue !== null || mapOperator === RollupOperator.COUNT)
                            ) {
                                const defaultVal = RollupCalcUtils.getRollupResultValue({ value: null, operator: mapOperator });
                                originalRollUpTransactionField.fieldValue = defaultVal;

                                const field: TransactionFieldEntity =
                                    await this.transactionFieldRepository.save(originalRollUpTransactionField);
                                updatedResult.originalFields.set(originalRollUpTransactionField.id, originalRollUpTransactionField);
                                updatedResult.updatedFields.push(field);

                                this._publishUpdatedFieldEventForRollup({
                                    fields: [originalRollUpTransactionField],
                                    accountId: request.accountId,
                                    causationTransId: request.transactionId,
                                    sourceOfChange: request.sourceOfChange,
                                });

                                this.logger.info(
                                    `Reset rollup value when no conditions are matched with transaction_field record id ${originalRollUpTransactionField.id}`,
                                );
                            }
                        }
                    } else {
                        const queryBuilder = this.transactionFieldRepository.createQueryBuilder('transactions').andWhere(
                            new Brackets((qbInner) => {
                                for (let field of rollupFieldsWithConditionChecked) {
                                    let notQualifiedTransactionIds = [];
                                    if (field.targetType === TargetTypeEnum.RELATED_COLLECTION) {
                                        notQualifiedTransactionIds =
                                            notQualifiedTransactionIdsPerDependCollectionItem[field?.collectionItemId] ?? [];
                                    }
                                    if (field.targetType === TargetTypeEnum.RELATED_FORM) {
                                        notQualifiedTransactionIds = notQualifiedTransactionIdsPerDependField[field?.fieldId] ?? [];
                                    }

                                    this._buildGetRollupDepsQuery({
                                        qb: qbInner,
                                        field,
                                        originalTransaction,
                                        relationTransactionGroups,
                                        childCollectionItemIds: childCollectionItemIdsChecked,
                                        notQualifiedTransactionIds,
                                        notQualifiedTransactionIdsPerDependChildCollectionItem,
                                        ableRollupRowKeysForCollectionFields,
                                    });
                                }
                            }),
                        );

                        if (dataType != FormFieldTypeEnum.ValidationResult || mapOperator === RollupOperator.COUNT) {
                            queryBuilder.andWhere({ fieldValue: Not(IsNull()) }).andWhere(`(transactions.field_value != '')`);
                        }

                        const rollupSelectQuery = this._buildRollupSelectQuery(dataType, mapOperator);
                        // if (!rollupSelectQuery) continue;
                        if (rollupSelectQuery) {
                            queryBuilder.select(rollupSelectQuery);

                            // if (formFieldId === '07691ae6-f6f6-4255-ae4a-ad0b8ce926ad') {
                            //     this.logger.info(`Query: ${queryBuilder.getSql()}`);
                            //     this.logger.info(`Query parameters: ${JSON.stringify(queryBuilder.getParameters())}`);
                            // }

                            if (!isEmpty(queryBuilder.getParameters())) {
                                queryBuilder.andWhere({ inVisible: false });

                                const result = await queryBuilder.getRawOne();

                                this.logger.info(`Calculation result of ${formFieldId}: ${result?.result}`);

                                const originalRollUpTransactionField = await this.transactionFieldRepository.findOneBy({
                                    transactionId: originalTransaction.id,
                                    fieldId: rollupField.fieldId,
                                });

                                // Update if rollup result is changed
                                if (originalRollUpTransactionField?.fieldValue?.toString() !== result?.result?.toString()) {
                                    switch (dataType) {
                                        case FormFieldTypeEnum.DatePicker:
                                            result.result = result.result
                                                ? dayjs.utc(result.result).format(MMDDYYYY)
                                                : (result.result ?? null);
                                            break;

                                        case FormFieldTypeEnum.DatetimePicker:
                                            result.result = result.result
                                                ? dayjs.utc(result.result).format(TRANSPORT_DATE_TIME_FORMAT)
                                                : (result.result ?? null);
                                            break;
                                    }

                                    const id = originalRollUpTransactionField?.id || uuid();

                                    relatedTransactionFields.push({
                                        id,
                                        transactionId: originalTransaction.id,
                                        fieldId: rollupField.fieldId,
                                        fieldValue: RollupCalcUtils.getRollupResultValue({
                                            value: result.result,
                                            operator: mapOperator,
                                        }),
                                        validationValue: dataType == FormFieldTypeEnum.ValidationResult ? result.result : null,
                                        contextType: TransactionFieldContextTypeEnum.FORM,
                                        fieldOptionIds: null,
                                    });
                                    updatedResult.originalFields.set(id, originalRollUpTransactionField);
                                    this.logger.info(`Do update rollup field ${id} with value ${result.result}`);
                                }
                            }
                        }
                    }
                }
            }),
        );

        if (relatedTransactionFields.length === 0) {
            await this.formTransactionStatusTenancyService.setParentRollupStatus(
                request.transactionId,
                StatusEnum.Completed,
                relationId,
                request.isTest,
            );
        }
    }

    private async publicEventForUpdatedTransaction(
        updatedResult: {
            updatedFields: any[];
            updatedFieldStyles: any[];
            originalFields: Map<string, any>;
        },
        request: RollUpCalculationRequest,
    ) {
        // publish event for transaction field updated
        const transactionsFieldsMaps = groupBy(updatedResult.updatedFields, 'transactionId');
        const updatedTransactionIds = Object.keys(transactionsFieldsMaps);
        const trans = await this.transactionRepository.find({
            where: { id: In(updatedTransactionIds) },
            withDeleted: true,
        });
        const transactionsMap = trans.reduce((acc, curr) => {
            acc[curr.id] = curr;
            return acc;
        }, {});
        if (updatedTransactionIds.length) {
            for (let transactionId in transactionsFieldsMaps) {
                const data = transactionsFieldsMaps[transactionId];
                const previous = [];
                data.forEach((field) => {
                    previous.push(updatedResult.originalFields.get(field.id));
                });

                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        ...transactionsMap[transactionId],
                        fields: data,
                        fieldStyles: updatedResult.updatedFieldStyles,
                        sourceOfChange: request.sourceOfChange,
                        previous,
                        isTest: transactionsMap[transactionId]?.isTest,
                    },
                    aggregateId: transactionId,
                    tenantId: request.accountId,
                    type: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
                    name: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
                });
                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);
            }
        }
    }

    public async calculateRollup(request: RollUpCalculationRequest) {
        console.time(`calculateRollup ${request.transactionId}:`);
        try {
            this.logger.info(`Start Rollup calculation from transaction ${request.transactionId}`);
            // Set the Rollup Status to Processing
            await this.formTransactionStatusTenancyService.setSelfRollupStatus(
                request.transactionId,
                StatusEnum.Processing,
                request.isTest,
            );
            const updatedResult = {
                updatedFields: [],
                updatedFieldStyles: [],
                originalFields: new Map<string, any>(),
            };
            // Check current field is related to rollup of current form
            // We don't include targetId here is because we want ot cache the query
            let rollupInCurrentForm = await this.rollUpDependencyRepository.find({
                select: {
                    fieldId: true,
                    targetId: true,
                },
                where: {
                    // targetId: In(updatedFieldIds),
                    formVersionId: request.formVersionId,
                    targetType: In([TargetTypeEnum.FORM, TargetTypeEnum.COLLECTION]),
                },
                cache: true,
            });
            // We filter out any rollup is not relevant to updatedFieldIds
            // Need to check if the updatedField is redundant
            const updatedFieldIds = request.fields;
            rollupInCurrentForm = rollupInCurrentForm.filter((field) => updatedFieldIds.includes(field.targetId));
            //TODO: remove locked fields from rollupInCurrentForm with targetID ===locked id
            const relatedRollupMap = new Map<string, RollupDependencyTenancyEntity[]>();
            let originalRollupFormTransaction: TransactionEntity, rollupDependencyFields: RollupDependencyTenancyEntity[];
            originalRollupFormTransaction = await this.transactionRepository.findOne({
                select: ['id', 'stageId', 'formVersionId'],
                where: {
                    id: request.transactionId,
                },
                withDeleted: !!request.isTest,
            });

            let lockedFields = [];
            if (originalRollupFormTransaction && originalRollupFormTransaction?.stageId && request?.formVersionId) {
                const stageAccessControls = await this.stageAccessControlRepository.find({
                    select: ['config', 'targetId'],
                    where: {
                        formVersionId: request?.formVersionId,
                        stageId: originalRollupFormTransaction.stageId,
                    },
                    cache: true,
                });

                //remove locked fields from rollupInCurrentForm
                lockedFields = stageAccessControls
                    ?.filter((stageAccessControl) => stageAccessControl?.config?.locked)
                    ?.map((field) => field.targetId);

                rollupInCurrentForm = rollupInCurrentForm.filter((field) => !lockedFields.includes(field.fieldId));
            }

            if (rollupInCurrentForm?.length > 0) {
                if (originalRollupFormTransaction) {
                    rollupDependencyFields = (
                        (await this.rollUpDependencyRepository.find({
                            select: [
                                'id',
                                'targetType',
                                'targetId',
                                'collectionId',
                                'collectionItemId',
                                'formFieldId',
                                'fieldId',
                                'formFieldId',
                            ],
                            where: { formVersionId: originalRollupFormTransaction.formVersionId },
                            cache: true,
                        })) || []
                    )?.filter((field) => !lockedFields.includes(field.fieldId));

                    relatedRollupMap.set(request.transactionId, rollupDependencyFields);
                }
            }

            //Hierarchical relation form
            const deepRelationTransactions =
                (await this.getTransactionDataService.getRelatedTransactions({
                    transactionId: request.transactionId,
                    transactionRepo: this.transactionRepository,
                    accountId: request.accountId,
                    excludeOriginTransaction: true,
                })) || [];

            if (deepRelationTransactions.length) {
                const formVersionIds = deepRelationTransactions.map((t) => t.formVersionId);
                const allRollupFields = await this.rollUpDependencyRepository.find({
                    select: [
                        'id',
                        'collectionItemId',
                        'relatedFormId',
                        'fieldId',
                        'targetId',
                        'targetType',
                        'collectionId',
                        'formFieldId',
                        'formVersionId',
                    ],
                    where: {
                        formVersionId: In(formVersionIds),
                    },
                    cache: true,
                });
                // Group by formVersionId for faster lookup
                const rollupFieldsByFormVersion = groupBy(allRollupFields, 'formVersionId');
                for (const related of deepRelationTransactions) {
                    const rollupFields = rollupFieldsByFormVersion[related.formVersionId] || [];
                    // Filter if the rollup field is related to the current form push to map
                    if (rollupFields?.some((rf) => rf.relatedFormId === request.formId)) {
                        relatedRollupMap.set(related.id, rollupFields);
                    }
                }
            }

            if (!relatedRollupMap.size) {
                // Rollup base case, there is no rollup for this transaction
                // set the Rollup Status to Completed
                await this.formTransactionStatusTenancyService.backTrackRollupStatus(
                    request.causationTransId,
                    request.transactionId,
                    StatusEnum.Completed,
                    request.isTest,
                );
                this.logger.warn(`No rollup dependencies found for transaction with transactionId ${request.transactionId}`);
                return;
            }

            let relatedTransactionFields: Array<TransactionFieldEntity> = [];
            const keys = Array.from(relatedRollupMap.keys());
            await this.formTransactionStatusTenancyService.initRollupStatus(
                request.transactionId,
                request.causationTransId,
                keys,
                request.isTest,
            );

            const transactionGroup: Record<string, { formId: string; formVersionId: string }> = {};
            // Process all transactions in parallel instead of sequentially
            await Promise.all(
                keys.map((relationId) =>
                    this.rollUpForOneTransaction(
                        relationId,
                        request,
                        relatedRollupMap,
                        relatedTransactionFields,
                        transactionGroup,
                        updatedResult,
                    ),
                ),
            );
            //TODO: remove locked fields from resRelatedTransactionFields
            // get stage access control by transaction id  & filter out locked fields
            const formVersionIds = deepRelationTransactions.map((trx) => trx.formVersionId);
            const stageIds = deepRelationTransactions.map((trx) => trx.stageId);
            const stageAccessControlsDeep = await this.stageAccessControlRepository.find({
                select: ['config', 'targetId'],
                where: {
                    formVersionId: In(formVersionIds),
                    stageId: In(stageIds),
                },
                cache: true,
            });

            //remove locked fields from rollup related form
            const lockedFieldsDeep = stageAccessControlsDeep
                ?.filter((stageAccessControl) => stageAccessControl?.config?.locked)
                ?.map((field) => field.targetId);
            relatedTransactionFields = relatedTransactionFields.filter((field) => !lockedFieldsDeep.includes(field.fieldId));

            // calculate fields validation values
            const validationResult = await this._getFieldValidations({
                transactionGroup: transactionGroup,
                updatedTranFields: relatedTransactionFields,
            });

            // set validation value for relatedTransactionFields
            relatedTransactionFields.forEach((rollup) => {
                const validationValue = validationResult?.find((item) => item.id === rollup.id);
                if (validationValue) {
                    rollup.validationValue = validationValue.configuration?.icon;
                }
            });

            // save changes including transaction fields and styles
            await this.transactionFieldRepository.upsert(relatedTransactionFields, ['id']);
            updatedResult.updatedFields.push(...relatedTransactionFields);

            if (validationResult?.length) {
                await this.transactionStyleRepository.upsert(validationResult, ['id']);
                updatedResult.updatedFieldStyles.push(...validationResult);
            }
            // add override logs
            await this._addSystemOverrideLogs(request.formVersionId, relatedTransactionFields);

            // publish event for transaction field updated
            this.publicEventForUpdatedTransaction(updatedResult, request);

            if (relatedTransactionFields.length === 0) {
                await this.formTransactionStatusTenancyService.backTrackRollupStatus(
                    request.causationTransId,
                    request.transactionId,
                    StatusEnum.Completed,
                    request.isTest,
                );
            }
            this._publishUpdatedFieldEventForRollup({
                fields: relatedTransactionFields,
                accountId: request.accountId,
                causationTransId: request.transactionId,
                sourceOfChange: request.sourceOfChange,
            });
            return relatedTransactionFields;
        } catch (error) {
            this.logger.error(error);
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...request,
                    id: request.transactionId,
                },
                aggregateId: request.transactionId,
                tenantId: request.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_ROLLUP_STATUS_FAILED,
                name: TransactionEventEnum.FORM_TRANSACTION_ROLLUP_STATUS_FAILED,
            });
            await this.formTransactionStatusTenancyService.backTrackRollupStatus(
                request.causationTransId,
                request.transactionId,
                StatusEnum.Failed,
                request.isTest,
            );
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
        } finally {
            console.timeEnd(`calculateRollup ${request.transactionId}:`);
        }
    }
    //#region Private methods

    private async _publishUpdatedFieldEventForRollup({
        fields,
        accountId,
        causationTransId,
        sourceOfChange,
    }: {
        fields: TransactionFieldEntity[];
        accountId: string;
        causationTransId: string;
        sourceOfChange?: SourceOfChangeType;
    }) {
        if (!fields.length) return;

        const groupedByTransaction = groupBy(fields, 'transactionId');

        for (let transactionId in groupedByTransaction) {
            let data = groupedByTransaction[transactionId];

            const transaction = await this.transactionRepository.findOne({
                where: { id: transactionId },
                withDeleted: true,
            });

            this.logger.info(`Emit Rollup calculation event for transaction ${transactionId}`);

            const fieldIds = compact(uniq(data.map((field) => field.fieldId)));
            const message = EventDrivenService.createCommonEvent({
                payload: new RollUpCalculationEvent({
                    accountId: accountId,
                    transactionId: transactionId,
                    formId: transaction.formId,
                    formVersionId: transaction.formVersionId,
                    fields: fieldIds,
                    causationTransId: causationTransId,
                    sourceOfChange,
                    isTest: transaction.isTest,
                }),
                aggregateId: transactionId,
                tenantId: accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_ROLLUP,
                name: TransactionEventEnum.FORM_TRANSACTION_ROLLUP,
            });
            // hot fix

            if (message.payload.fields.length === 0) {
                continue;
            }
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_ROLLUP_TOPIC, message);
        }
    }

    private _buildRollupSelectQuery(type: FormFieldTypeEnum, mapOperator: RollupOperator): string {
        if (!type) return '';
        let query = '';

        switch (type) {
            case FormFieldTypeEnum.Number:
            case FormFieldTypeEnum.Duration:
            case FormFieldTypeEnum.TimePicker:
                if (mapOperator === RollupOperator.COUNT) {
                    query = `${mapOperator}(transactions.field_value) as result`;
                } else {
                    query = `${mapOperator}(CAST(COALESCE(transactions.field_value, '0') AS numeric)) as result`;
                }
                break;
            case FormFieldTypeEnum.DatePicker:
            case FormFieldTypeEnum.DatetimePicker:
                query = `${mapOperator}(to_timestamp("transactions"."field_value", '${DATABASE_DATE_TIME_FORMAT}')) AS result`;
                break;
            case FormFieldTypeEnum.ValidationResult:
                query = `MAX(CAST(COALESCE(transactions.validation_value, '0') AS numeric)) as result`;
                break;
            default:
                break;
        }
        return query;
    }

    private _mapOperator(operator: RollupOperator): RollupOperator {
        if (operator === RollupOperator.LATEST) {
            operator = RollupOperator.MAX;
        } else if (operator === RollupOperator.EARLIEST) {
            operator = RollupOperator.MIN;
        } else if ((operator as string) === 'AVERAGE') {
            operator = RollupOperator.AVG;
        }
        return operator;
    }

    private _buildGetRollupDepsQuery({
        qb,
        field,
        childCollectionItemIds,
        originalTransaction,
        relationTransactionGroups,
        notQualifiedTransactionIds,
        notQualifiedTransactionIdsPerDependChildCollectionItem,
        ableRollupRowKeysForCollectionFields,
    }: {
        qb: WhereExpressionBuilder;
        field: RollupDependencyTenancyEntity & { secondContextType: RollUpSecondContextEnum; rowKey?: string };
        originalTransaction: TransactionEntity;
        relationTransactionGroups: Dictionary<FormTransactionDto[]>;
        childCollectionItemIds?: string[];
        notQualifiedTransactionIds?: string[];
        notQualifiedTransactionIdsPerDependChildCollectionItem?: Record<string, string[]>;
        ableRollupRowKeysForCollectionFields?: Record<string, string[]>;
    }): void {
        const targetType = field.targetType;
        const secondContextType = field.secondContextType;
        const normalizedId = field.id.replace(/-/g, '_');

        switch (targetType) {
            case TargetTypeEnum.FORM:
                qb.orWhere(
                    `(transactions.field_id = :fieldId_${normalizedId} AND transactions.transaction_id = :transactionId_${normalizedId})`,
                    {
                        [`fieldId_${normalizedId}`]: field.targetId,
                        [`transactionId_${normalizedId}`]: originalTransaction.id,
                    },
                );
                break;

            case TargetTypeEnum.RELATED_FORM: {
                const relatedTransactions = relationTransactionGroups[field.relatedFormId];

                relatedTransactions?.forEach((transaction, index) => {
                    if (!notQualifiedTransactionIds.includes(transaction.id)) {
                        const normalizedTransId = transaction.id.replace(/-/g, '_');
                        const suffix = `${normalizedTransId}_${normalizedId}_${index}`;

                        qb.orWhere(
                            `(transactions.field_id = :fieldId_${suffix} 
                            AND transactions.transaction_id = :transactionId_${suffix})`,
                            {
                                [`fieldId_${suffix}`]: field.targetId,
                                [`transactionId_${suffix}`]: transaction.id,
                            },
                        );
                    }
                });
                break;
            }

            case TargetTypeEnum.COLLECTION: {
                if (secondContextType === RollUpSecondContextEnum.COLLECTION_FIELD) {
                    // Rollup from collection field
                    const rowKeys = ableRollupRowKeysForCollectionFields?.[field.targetId] ?? [];
                    if (rowKeys.length > 0) {
                        rowKeys.forEach((rowKey) => {
                            const suffix = `${normalizedId}_${rowKey}`.replace(/-/g, '_');

                            qb.orWhere(
                                `(transactions.field_id = :fieldId_${suffix} 
                            AND transactions.transaction_id = :transactionId_${suffix}
                            AND transactions.row_key = :rowKey_${suffix})`,
                                {
                                    [`fieldId_${suffix}`]: field.targetId,
                                    [`transactionId_${suffix}`]: originalTransaction.id,
                                    [`rowKey_${suffix}`]: rowKey,
                                },
                            );
                        });
                    } else {
                        qb.orWhere(
                            `(transactions.field_id = :fieldId_${normalizedId} 
                        AND transactions.transaction_id = :transactionId_${normalizedId})`,
                            {
                                [`fieldId_${normalizedId}`]: field.targetId,
                                [`transactionId_${normalizedId}`]: originalTransaction.id,
                            },
                        );
                    }
                } else {
                    // Rollup from collection field value
                    if (secondContextType !== RollUpSecondContextEnum.COLLECTION_GROUP) {
                        const suffix = `${normalizedId}_${field.rowKey}`.replace(/-/g, '_');
                        qb.orWhere(
                            `(transactions.field_id = :fieldId_${suffix}  
                            AND transactions.transaction_id = :transactionId_${suffix} 
                            AND transactions.row_key = :rowKey_${suffix})`,
                            {
                                [`fieldId_${suffix}`]: field.targetId,
                                [`transactionId_${suffix}`]: originalTransaction.id,
                                [`rowKey_${suffix}`]: field.rowKey,
                            },
                        );
                    } else {
                        // Rollup from collection groups with child collection items
                        compact(uniq(childCollectionItemIds)).forEach((collectionItemId) => {
                            const normalizedCollectionId = collectionItemId.replace(/-/g, '_');
                            const normalizedTargetFieldId = field.targetId.replace(/-/g, '_');
                            const suffix = `${normalizedId}_${normalizedCollectionId}_${normalizedTargetFieldId}`;

                            qb.orWhere(
                                `(transactions.field_id = :fieldId_${suffix}  
                                AND transactions.transaction_id = :transactionId_${suffix} 
                                AND transactions.collection_item_id = :collectionItemId_${suffix})`,
                                {
                                    [`fieldId_${suffix}`]: field.targetId,
                                    [`transactionId_${suffix}`]: originalTransaction.id,
                                    [`collectionItemId_${suffix}`]: collectionItemId,
                                },
                            );
                        });
                    }
                }
                break;
            }
            case TargetTypeEnum.RELATED_COLLECTION: {
                const relatedTransactions = relationTransactionGroups[field.relatedFormId];

                if (secondContextType === RollUpSecondContextEnum.COLLECTION_FIELD) {
                    if (!isEmpty(ableRollupRowKeysForCollectionFields)) {
                        relatedTransactions?.forEach((transaction, index) => {
                            if (!notQualifiedTransactionIds.includes(transaction.id)) {
                                const rowKeys = ableRollupRowKeysForCollectionFields?.[`${field.targetId}_${transaction.id}`] ?? [];

                                rowKeys.forEach((rowKey) => {
                                    const suffix = `${normalizedId}_${rowKey}_${index}`.replace(/-/g, '_');

                                    qb.orWhere(
                                        `(transactions.field_id = :fieldId_${suffix} 
                                        AND transactions.transaction_id = :transactionId_${suffix}
                                         AND transactions.row_key = :rowKey_${suffix})`,
                                        {
                                            [`fieldId_${suffix}`]: field.targetId,
                                            [`transactionId_${suffix}`]: transaction.id,
                                            [`rowKey_${suffix}`]: rowKey,
                                        },
                                    );
                                });
                            }
                        });
                    } else {
                        relatedTransactions?.forEach((transaction, index) => {
                            if (!notQualifiedTransactionIds.includes(transaction.id)) {
                                const suffix = `${normalizedId}_${index}`;

                                qb.orWhere(
                                    `(transactions.field_id = :fieldId_${suffix} 
                                    AND transactions.transaction_id = :transactionId_${suffix})`,
                                    {
                                        [`fieldId_${suffix}`]: field.targetId,
                                        [`transactionId_${suffix}`]: transaction.id,
                                    },
                                );
                            }
                        });
                    }
                } else {
                    // Rollup from collection item
                    if (secondContextType !== RollUpSecondContextEnum.COLLECTION_GROUP) {
                        relatedTransactions?.forEach((transaction, index) => {
                            if (!notQualifiedTransactionIds.includes(transaction.id)) {
                                const normalizedTransId = transaction.id.replace(/-/g, '_');
                                const suffix = `${normalizedTransId}_${normalizedId}_${index}`;

                                qb.orWhere(
                                    `(transactions.field_id = :fieldId_${suffix}
                                AND transactions.transaction_id = :transactionId_${suffix}
                                AND transactions.collection_id = :collectionId_${suffix} 
                                AND transactions.collection_item_id = :collectionItemId_${suffix})`,
                                    {
                                        [`fieldId_${suffix}`]: field.targetId,
                                        [`transactionId_${suffix}`]: transaction.id,
                                        [`collectionId_${suffix}`]: field.collectionId,
                                        [`collectionItemId_${suffix}`]: field.collectionItemId,
                                    },
                                );
                            }
                        });
                    } else {
                        // Rollup from collection groups with child collection items
                        childCollectionItemIds.forEach((collectionItemId) => {
                            relatedTransactions?.forEach((transaction) => {
                                const notQualifiedTransIds = notQualifiedTransactionIdsPerDependChildCollectionItem[collectionItemId] ?? [];

                                if (!notQualifiedTransIds.includes(transaction.id)) {
                                    const normalizedCollectionId = collectionItemId.replace(/-/g, '_');
                                    const normalizedTransId = transaction.id.replace(/-/g, '_');
                                    const suffix = `${normalizedTransId}_${normalizedId}_${normalizedCollectionId}`;

                                    qb.orWhere(
                                        `(transactions.field_id = :fieldId_${suffix}
                                    AND transactions.transaction_id = :transactionId_${suffix}
                                    AND transactions.collection_id = :collectionId_${suffix}
                                    AND transactions.collection_item_id = :collectionItemId_${suffix})`,
                                        {
                                            [`fieldId_${suffix}`]: field.targetId,
                                            [`transactionId_${suffix}`]: transaction.id,
                                            [`collectionId_${suffix}`]: field.collectionId,
                                            [`collectionItemId_${suffix}`]: collectionItemId,
                                        },
                                    );
                                }
                            });
                        });
                    }
                }
                break;
            }
            default:
                break;
        }
    }

    private async _getFieldValidations({
        transactionGroup,
        updatedTranFields,
    }: {
        transactionGroup: Record<string, { formId: string; formVersionId: string }>;
        updatedTranFields: TransactionFieldEntity[];
    }) {
        let updatedFieldStyles = [];
        const groupByTransaction = groupBy(updatedTranFields, 'transactionId');

        const tasks = [];

        for (const transactionId in groupByTransaction) {
            const fields = groupByTransaction[transactionId];
            const form = transactionGroup?.[transactionId];
            if (!fields?.length || !form) {
                continue;
            }

            tasks.push(this._getTransactionFieldStyles(transactionId, form.formId, form.formVersionId, fields));
        }

        if (tasks?.length) {
            const result = (await Promise.all(tasks)) ?? [];

            result.forEach((styles) => {
                updatedFieldStyles = [...updatedFieldStyles, ...(styles ?? [])];
            });
        }

        return updatedFieldStyles;
    }

    private async _getTransactionFieldStyles(
        transactionId: string,
        formId: string,
        formVersionId: string,
        updatedTranFields: TransactionFieldEntity[],
    ) {
        const form = await this._formTransactionDataService.getFormWithActiveVersion({ formId: formId, formVersionId: formVersionId });
        const activeFormVersion = form?.formVersions?.[0];

        if (!activeFormVersion) {
            return null;
        }
        const updatedFieldStyles = [];
        let transactionFields = await this.transactionFieldRepository.findBy({ transactionId: transactionId });
        // Create a map for faster lookups
        const updatedFieldsMap = updatedTranFields.reduce((map, field) => {
            map[field.fieldId] = field;
            return map;
        }, {});
        for (const field of transactionFields) {
            const updatedField = updatedFieldsMap[field.fieldId];
            if (updatedField) {
                field.fieldValue = updatedField.fieldValue;
                field.fieldOptionIds = updatedField.fieldOptionIds;
            }
        }

        const relatedLookupData = await this._relatedLookupDataService.getRelatedLookupData({
            formVersion: activeFormVersion,
            allTransactionFields: transactionFields,
        });

        const validations = this._formTransactionDataService.validateTransactionData(
            activeFormVersion?.fields,
            transactionFields,
            relatedLookupData,
        );

        for (const field of transactionFields) {
            const formField = activeFormVersion.fields?.find((f) => f.fieldId === field.fieldId);
            if (updatedFieldStyles.some((item) => item.id === field.id)) {
                continue;
            }
            if (formField?.configuration?.rollup?.dataType === FormFieldTypeEnum.ValidationResult) {
                updatedFieldStyles.push({
                    id: field.id,
                    fieldId: field.fieldId,
                    configuration: {
                        icon: field.fieldValue,
                        label: '',
                    },
                    transactionId: field.transactionId,
                });
                continue;
            }
            if (!formField?.configuration?.ruleConfigs?.dataValidationConditions?.length) {
                continue;
            }
            const fieldValidation = validations?.find((f) => f.fieldId === field.fieldId);
            updatedFieldStyles.push({
                id: field.id,
                fieldId: field.fieldId,
                configuration: {
                    icon: fieldValidation?.code,
                    label: fieldValidation?.label ?? '',
                },
                transactionId: field.transactionId,
            });
        }
        return updatedFieldStyles;
    }

    private async _addSystemOverrideLogs(formVersionId: string, tranFields: TransactionFieldEntity[]) {
        if (!tranFields?.length) {
            return;
        }

        const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum; border?: number }> = {};
        tranFields.forEach((field) => {
            originTransactionFieldOverrides[field.id] = {
                value: field.validationValue,
                type: OverrideTypeEnum.System,
                border: field.transactionFieldStyle?.configuration?.border,
            };
        });

        await addSystemOverrideRecords({
            originTransactionFieldOverrides,
            updateFields: tranFields,
            transactionFieldOverrideRepo: this._transactionFieldOverrideRepo,
        });
    }

    //#endregion Private methods
}
