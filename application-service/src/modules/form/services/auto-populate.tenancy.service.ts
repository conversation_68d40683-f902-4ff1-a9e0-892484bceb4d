import { Inject, Injectable } from '@nestjs/common';
import { isEqual, sortBy, uniq } from 'lodash';
import { In, Repository } from 'typeorm';
import { v4 as uuid } from 'uuid';
import { ClaimService, USER_CLAIMS } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormAutoPopulateSettingEntity } from '../../../database/src/entities/public/form-auto-populate-setting.public.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { RelationTransactionEntity } from '../../../database/src/entities/tenancy/relation-transaction.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';
import { RelatedFieldChangeEventDto } from '../dtos/requests/related-field-change-event.dto';

@Injectable()
export class AutoPopulateTransactionFieldService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private relationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private autoPopulateRepository: Repository<FormAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private transactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private stageRepository: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private formVersionRepository: Repository<FormVersionTenancyEntity>,

        private readonly _eventDrivenService: EventDrivenService,
    ) {}

    public async handleChange(request: RelatedFieldChangeEventDto) {
        if (!request) {
            return;
        }
        const fieldIds = request.tranFields?.map((item) => item.fieldId);
        if (!fieldIds?.length) {
            return;
        }

        await this.handleChangeOnPush(request);
        await this.handleChangeOnPull(request);
    }

    public async handleChangeOnPush(request: RelatedFieldChangeEventDto) {
        const fieldIds = request.tranFields.map((item) => item.fieldId);
        const autoPopulatesSettings = await this.autoPopulateRepository.findBy({
            originFieldId: In(fieldIds),
            onPush: true,
            originFormId: request.formId,
            originFormVersionId: request.formVersionId,
        });

        if (!autoPopulatesSettings?.length) {
            return [];
        }

        await this.onPush(request, autoPopulatesSettings);
    }

    public async handleChangeOnPull(request: RelatedFieldChangeEventDto) {
        if (!request) {
            return;
        }
        const fieldIds = request.tranFields.map((item) => item.fieldId);
        if (!fieldIds?.length) {
            return;
        }

        // onPull, we check only stage of children form, not current form
        const autoPopulatesSettings = await this.autoPopulateRepository.findBy({
            targetFieldId: In(fieldIds),
            targetFormId: request.formId,
            onPull: true,
        });

        await this.onPull(request, autoPopulatesSettings);
    }

    private async onPush(
        request: RelatedFieldChangeEventDto,
        autoPopulatesSettings: (FormAutoPopulateSettingEntity | FormAutoPopulateSettingTenancyEntity)[],
    ): Promise<void> {
        const relationTransaction = await this.relationTransactionRepository.findBy({
            targetFormId: request.formId,
            targetTransactionId: request.transactionId,
        });

        if (!relationTransaction?.length) {
            return;
        }

        const stageIds = autoPopulatesSettings.map((item) => item.stageId);

        if (!stageIds?.length) {
            return;
        }

        const stages = await this.stageRepository.findBy({ identityId: In(uniq(stageIds)) });

        if (!stages?.length) {
            return;
        }

        for (let relation of relationTransaction) {
            const targetTransaction = await this.transactionRepository.findOne({
                where: { id: relation.originTransactionId },
                relations: ['transactionFields'],
            });

            const targetFormVersion = await this.formVersionRepository.findOne({
                where: { id: targetTransaction.formVersionId, formId: targetTransaction.formId },
                relations: ['fields'],
            });

            const originTransaction = await this.transactionRepository.findOne({
                where: { id: relation.targetTransactionId },
                relations: ['transactionFields'],
            });

            const mappingStage = stages.find((item) => item.id === originTransaction.stageId);
            if (!targetTransaction || !mappingStage) {
                continue;
            }

            const updatingTranFields: TransactionFieldEntity[] = [];
            const previous: TransactionFieldEntity[] = [];
            for (let field of autoPopulatesSettings) {
                const updatedField = request.tranFields.find((f) => f.fieldId === field.originFieldId);
                if (!updatedField) {
                    continue;
                }
                // check stage and field values
                const pushingTranField = targetTransaction?.transactionFields?.find((f) => f.fieldId === field.targetFieldId);
                const pullingFormField = targetFormVersion?.fields?.find((f) => f.fieldId === field.targetFieldId);

                if (this.isFieldValuesEqual(updatedField, pushingTranField)) {
                    continue;
                }

                previous.push(pushingTranField);

                const newTranField = this.formatTransactionField(
                    updatedField,
                    pushingTranField,
                    targetTransaction.id,
                    field.targetFieldId,
                    pullingFormField,
                );

                if (!newTranField) {
                    continue;
                }
                newTranField.id = pushingTranField?.id || uuid();
                updatingTranFields.push(newTranField);
            }

            if (updatingTranFields.length) {
                const transactionId = targetTransaction.id;

                const formValues = updatingTranFields.reduce((prev, field) => {
                    prev[field.fieldId] = field.fieldValue;
                    return prev;
                }, {});

                const updateRequest: EditFormTransactionRequest = {
                    transactionId: transactionId,
                    transactionFields: updatingTranFields,
                    formId: targetTransaction.formId,
                    formValues,
                };

                const updateMessage = EventDrivenService.createCommonEvent({
                    payload: {
                        id: transactionId,
                        request: updateRequest,
                        user: RequestContextService.currentUser(),
                        fromRequest: RequestContextService.source === 'user',
                        option: {
                            shouldRunPopulateFormFields: true,
                        },
                        sourceOfChange: SourceOfChangeType.AUTO_POPULATE,
                    },
                    tenantId: RequestContextService.accountId,
                    aggregateId: transactionId,
                    type: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                    name: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                });

                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_TOPIC, updateMessage, transactionId);
            }
        }
    }

    private isFieldValuesEqual(originField: TransactionFieldEntity, targetField: TransactionFieldEntity) {
        if (!originField || !targetField) {
            return false;
        }

        if (!originField?.fieldValue && !originField?.fieldOptionIds?.length) return true;

        // Handle fieldOptionIds comparison
        const originOptionIds = originField?.fieldOptionIds || [];
        const targetOptionIds = targetField?.fieldOptionIds || [];
        if (originOptionIds.length > 0 || targetOptionIds.length > 0) {
            // Check if originOptionIds is included in targetOptionIds or vice versa
            const isOriginIncludedInTarget = originOptionIds.every((id) => targetOptionIds.includes(id));
            const isTargetIncludedInOrigin = targetOptionIds.every((id) => originOptionIds.includes(id));
            const check = isOriginIncludedInTarget || isTargetIncludedInOrigin;

            if (check) {
                return true;
            }
        }

        if (originOptionIds.length > 0 || targetOptionIds.length > 0) {
            return isEqual(sortBy(originOptionIds), sortBy(targetOptionIds));
        }

        // Handle fieldValue comparison
        const originValue = originField?.fieldValue ?? '';
        const targetValue = targetField?.fieldValue ?? '';

        return originValue == targetValue;
    }

    private formatTransactionField(
        mappedTranField: TransactionFieldEntity,
        transactionField: TransactionFieldEntity,
        relationTransactionId: string,
        fieldId: string,
        formField: FormFieldTenancyEntity,
    ) {
        let fieldOptionIds = mappedTranField.fieldOptionIds;
        let newTranField = null;
        if (!fieldOptionIds?.length) {
            newTranField = {
                id: transactionField?.id,
                transactionId: relationTransactionId,
                fieldId: fieldId,
                fieldOptionIds: [],
                fieldValue: mappedTranField?.fieldValue,
            };
        } else {
            const fieldMode = formField?.configuration?.mode as string;

            if (fieldMode === 'single' && fieldOptionIds?.length > 1) {
                return null;
            }
            const tranFieldValues = fieldMode === 'multiple' ? (transactionField?.fieldValue?.split(',') ?? []) : [];
            const tranFieldOptions = fieldMode === 'multiple' ? (transactionField?.fieldOptionIds ?? []) : [];
            let newTranFieldValue = mappedTranField?.fieldValue.split(',') ?? [];
            newTranFieldValue = fieldMode === 'multiple' ? newTranFieldValue : newTranFieldValue[0] ? [newTranFieldValue[0]] : [];
            fieldOptionIds = fieldMode === 'multiple' ? fieldOptionIds : fieldOptionIds[0] ? [fieldOptionIds[0]] : [];

            const optionIds = uniq([...tranFieldOptions.filter(Boolean), ...(fieldOptionIds ?? [])]);
            const values = uniq([...tranFieldValues.filter(Boolean), ...newTranFieldValue.filter(Boolean)]).join(',');
            newTranField = {
                id: transactionField?.id || uuid(),
                transactionId: relationTransactionId,
                fieldId: fieldId,
                fieldOptionIds: optionIds,
                fieldValue: optionIds?.join(','),
            };
        }
        return newTranField;
    }

    private async onPull(
        request: RelatedFieldChangeEventDto,
        autoPopulatesSettings: (FormAutoPopulateSettingEntity | FormAutoPopulateSettingTenancyEntity)[],
    ): Promise<void> {
        const relationTransaction = await this.relationTransactionRepository.findBy({
            originFormId: request.formId,
            originTransactionId: request.transactionId,
        });

        if (!relationTransaction?.length) {
            return;
        }

        const stageIds = autoPopulatesSettings.map((item) => item.stageId);

        if (!stageIds?.length) {
            return;
        }

        const stages = await this.stageRepository.findBy({ identityId: In(uniq(stageIds)) });

        if (!stages?.length) {
            return;
        }

        for (let relation of relationTransaction) {
            const targetTransaction = await this.transactionRepository.findOne({
                where: { id: relation.targetTransactionId },
                relations: ['transactionFields'],
            });

            const mappingStage = stages.find((item) => item.id === targetTransaction.stageId);
            if (!targetTransaction || !mappingStage) {
                continue;
            }

            const targetFormVersion = await this.formVersionRepository.findOne({
                where: { id: targetTransaction.formVersionId, formId: targetTransaction.formId },
                relations: ['fields'],
            });

            const updatingTranFields: TransactionFieldEntity[] = [];
            const previous: TransactionFieldEntity[] = [];
            for (let field of autoPopulatesSettings) {
                const updatedField = request.tranFields.find((f) => f.fieldId === field.targetFieldId);
                if (!updatedField) {
                    continue;
                }
                // check stage and field values
                const pullingTranField = targetTransaction?.transactionFields?.find((f) => f.fieldId === field.originFieldId);
                const pullingFormField = targetFormVersion?.fields?.find((f) => f.fieldId === field.originFieldId);

                if (
                    updatingTranFields.some((f) => f.fieldId === pullingTranField.fieldId) ||
                    this.isFieldValuesEqual(updatedField, pullingTranField)
                ) {
                    continue;
                }

                previous.push(pullingTranField);

                const newTranField = this.formatTransactionField(
                    updatedField,
                    pullingTranField,
                    relation.targetTransactionId,
                    field.originFieldId,
                    pullingFormField,
                );

                if (!newTranField) {
                    continue;
                }
                newTranField.id = pullingTranField?.id || uuid();
                updatingTranFields.push(newTranField);
            }

            if (updatingTranFields.length) {
                const transactionId = relation.targetTransactionId;

                const formValues = updatingTranFields.reduce((prev, field) => {
                    prev[field.fieldId] = field.fieldValue;
                    return prev;
                }, {});

                const updateRequest: EditFormTransactionRequest = {
                    transactionId: transactionId,
                    transactionFields: updatingTranFields,
                    formId: targetTransaction.formId,
                    formValues,
                };

                const updateMessage = EventDrivenService.createCommonEvent({
                    payload: {
                        id: transactionId,
                        request: updateRequest,
                        user: RequestContextService.currentUser(),
                        fromRequest: RequestContextService.source === 'user',
                        option: {
                            shouldRunPopulateFormFields: true,
                        },
                        sourceOfChange: SourceOfChangeType.AUTO_POPULATE,
                    },
                    tenantId: RequestContextService.accountId,
                    aggregateId: transactionId,
                    type: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                    name: TransactionEventEnum.FORM_TRANSACTION_UPDATE,
                });

                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_TOPIC, updateMessage, transactionId);
            }
        }
    }
}
