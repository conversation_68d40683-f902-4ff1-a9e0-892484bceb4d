import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { v7 } from 'uuid';
import { CacheService, LoggerService } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { FROM_STAGE_FIELD } from '../../../constant';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';

const PREVIOUS_STAGE = 'PREVIOUS_STAGE';

@Injectable()
export class ChangeTransactionStageService {
    constructor(
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly stageRepo: Repository<StageTenancyEntity>,

        private readonly _eventDrivenService: EventDrivenService,

        private readonly _cacheService: CacheService,
    ) {}

    public async handleTransitionAction(props: { transactionId: string; targetStageId: string; isTest?: boolean }) {
        const { targetStageId, transactionId, isTest } = props;
        const transaction = await this.formTransactionRepository.findOne({ where: { id: transactionId }, withDeleted: !!isTest });

        const stage = await this.stageRepo.findOneBy({ id: targetStageId });

        if (!transaction) {
            throw new NotFoundException('transaction_not_found');
        }

        switch (targetStageId) {
            case PREVIOUS_STAGE:
                const previousStageId = transaction.previousStageId;
                if (!previousStageId) {
                    throw new BadRequestException('previous_stage_not_found');
                }

                const previousStage = await this.stageRepo.findOneBy({ id: previousStageId });
                transaction.previousStageId = transaction.stageId;
                transaction.previousStageName = transaction.stageName;

                transaction.stageId = previousStage.id;
                transaction.stageName = previousStage.name;
                break;

            default:
                if (targetStageId === transaction.stageId) {
                    throw new BadRequestException('already_in_this_stage');
                }

                transaction.previousStageName = transaction.stageName;
                transaction.previousStageId = transaction.stageId;

                transaction.stageId = stage.id;
                transaction.stageName = stage.name;
                break;
        }

        try {
            const result = await this.formTransactionRepository.update({ id: transaction.id }, { ...transaction });

            const updateTransactionPayload: Record<string, any> = {
                ...transaction,
                stageId: transaction.stageId,
                previousStageId: transaction.previousStageId,
                stageName: transaction.stageName,
                previousStageName: transaction.previousStageName,
            };

            const message = EventDrivenService.createCommonEvent({
                payload: updateTransactionPayload,
                aggregateId: transaction.id,
                tenantId: RequestContextService.accountId,
                userId: RequestContextService.userId,
                type: TransactionEventEnum.FORM_TRANSACTION_STAGE_UPDATED,
                name: TransactionEventEnum.FORM_TRANSACTION_STAGE_UPDATED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_STAGE_TOPIC, message);

            const previous = [];
            const changes: (TransactionFieldEntity & { prevFieldValue: string; stageId?: string; prevStageId?: string })[] = [];
            const id = v7();
            changes.push({
                id,
                fieldId: FROM_STAGE_FIELD,
                fieldValue: transaction.stageName,
                prevFieldValue: transaction.previousStageName,
                transactionId: transaction.id,
                stageId: transaction.stageId,
                prevStageId: transaction.previousStageId,
                fieldOptionIds: [],
            });

            previous.push({
                id,
                fieldId: FROM_STAGE_FIELD,
                fieldValue: transaction.previousStageName,
                prevFieldValue: null,
                transactionId: transaction.id,
                stageId: transaction.previousStageId,
                prevStageId: null,
                fieldOptionIds: [],
            });

            // const defaultTransactionField = this._setDefaultRoleLookupField(formVersion, transaction, request);

            const changeMessage = EventDrivenService.createCommonEvent({
                payload: {
                    ...transaction,
                    fields: changes,
                    populatedFields: [],
                    previous,
                    sourceFunction: 'detectAndPublishFieldChangedEvent',
                },
                aggregateId: transaction.id,
                tenantId: RequestContextService.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
                name: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, changeMessage);

            const decisionStageKey = `decision_stage_${transaction.id}`;
            await this._cacheService.delete(decisionStageKey);

            return result;
        } catch (err) {
            this._logger.error(err);
            return false;
        }
    }
}
