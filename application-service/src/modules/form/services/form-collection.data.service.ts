import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Inject, Injectable } from '@nestjs/common';
import { cloneDeep, compact, groupBy, uniq, uniqWith } from 'lodash';
import { In, Repository } from 'typeorm';
import { v4, v7 } from 'uuid';
import { EXTRA_CONFIGURATION_TYPES } from '../../../common/src/constant/auto-populate';
import {
    EXTERNAL_DATA_SOURCE_MAPPING,
    EXTERNAL_DATA_SOURCE_TYPE,
    EXTERNAL_DATA_SOURCE_TYPES,
    EXTERNAL_DATA_SOURCE__SEPARATE_MARK,
    MDS_PACK_FIELDS,
} from '../../../common/src/constant/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterVersionEntity } from '../../../database/src/entities/public/data-register-versions.public.entity';
import { FormCollectionAdditionalFieldEntity } from '../../../database/src/entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { GeneralAutoPopulateExtraConfigEntity } from '../../../database/src/entities/public/general-auto-populate-extra-config.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { CollectionTransactionTenancyEntity } from '../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormContextMappingTenancyEntity } from '../../../database/src/entities/tenancy/form-context-mapping.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-extra-config.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { AutoPopulateBuilderTypeEnum } from '../../../database/src/shared/enums/ap-builder-type.enum';
import { AutoPopulateDataSourceTypeEnum } from '../../../database/src/shared/enums/ap-data-source-type.enum';
import { AutoPopulateExtraConfigTypeEnum } from '../../../database/src/shared/enums/ap-extra-config-type.enum';
import { ContextMappingType } from '../../../database/src/shared/enums/context-mapping-type.enum';
import { DataRegisterTypeEnum } from '../../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';

export type FormCollectionItemFieldsAP = {
    [fieldId: string]: FormCollectionItemFieldAP[];
};

export type FormCollectionItemFieldAP = {
    id: string;
    priority: number;
    dataSource: string;
    dataSourceType: AutoPopulateDataSourceTypeEnum;
    targetFieldId: string;
    includeValidationValue: boolean;
    extraConfigurations?: FormCollectionItemFieldAPExtraConfig[];
    fieldId?: string;
    targetFieldType: FormFieldTypeEnum;
};

export type FormCollectionItemFieldAPExtraConfig = {
    fromFieldId: string;
    toFieldId: string;
    toLookupFieldId?: string;
    toFieldRegisterId?: string;
    toTransactionRegisterId?: string;
    id: string;
    extraType: (typeof EXTRA_CONFIGURATION_TYPES)[keyof typeof EXTRA_CONFIGURATION_TYPES];

    toContextId?: string;
    toFixedValue?: string;
    toFixedFieldType?: string;
    toContextFieldType?: string;
};

@Injectable()
export class FormCollectionDataService {
    constructor(
        @InjectMapper() readonly _mapper: Mapper,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _additionalFieldRepo: Repository<DataRegisterAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly _dataRegisterRepo: Repository<DataRegisterTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterFieldRepo: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly _dataRegisterTransactionRepo: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly _dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTenancyRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemRepo: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _generalAutoPopulateRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private readonly _formFieldRepo: Repository<FormFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_CONTEXT_MAPPING_TENANCY_REPOSITORY)
        private readonly _contextMappingRepo: Repository<FormContextMappingTenancyEntity>,
    ) {}

    public async getTestAdditionalFields({
        formVersionId,
        formCollectionItems,
    }: {
        formVersionId: string;
        formCollectionItems: FormCollectionItemEntity[] | FormCollectionItemTenancyEntity[];
    }): Promise<FormCollectionAdditionalFieldTenancyEntity[]> {
        if (!formCollectionItems?.length) {
            const collections = await this._formCollectionTenancyRepo.findBy({
                formVersionId,
            });
            if (collections.length) {
                const collectionItems = await this._formCollectionItemRepo.findBy({
                    formCollectionId: In(collections.map((c) => c.id)),
                });
                formCollectionItems = collectionItems;
            }
        }

        const dataTransactionIds = formCollectionItems?.map((item) => item.dataRegisterTransactionId);
        if (!dataTransactionIds?.length) {
            return [];
        }

        const additionalFieldsGroup = groupBy(formCollectionItems, 'dataRegisterTransactionId') ?? {};

        const additionalFields = await this._additionalFieldRepo.find({
            where: {
                transactionId: In(dataTransactionIds),
            },
            // relations: ['autoPopulateContexts'],
        });

        if (!additionalFields?.length) {
            return [];
        }

        const newAdditionalFields: (FormCollectionAdditionalFieldEntity | FormCollectionAdditionalFieldTenancyEntity)[] = [];

        additionalFields.forEach((field) => {
            const newField = this._mapper.map(field, DataRegisterAdditionalFieldTenancyEntity, FormCollectionAdditionalFieldTenancyEntity);

            newField.formVersionId = formVersionId;

            const items = additionalFieldsGroup?.[field.transactionId];
            if (items) {
                items.forEach((item: FormCollectionItemEntity | FormCollectionItemTenancyEntity) => {
                    newField.id = v4();
                    newField.formCollectionItemIdentityId = item.identityId;
                    newAdditionalFields.push(cloneDeep(newField));
                });
            }
        });

        return newAdditionalFields;
    }

    public async getTestCollectionTransaction({
        formCollectionItemIds,
        formCollectionItemEntities,
        formVersionId,
    }: {
        formCollectionItemIds: string[];
        formCollectionItemEntities?: FormCollectionItemEntity[] | FormCollectionItemTenancyEntity[];
        formVersionId?: string;
    }): Promise<CollectionTransactionTenancyEntity[]> {
        const uniqFormCollectionItemIds = uniq(formCollectionItemIds);

        if (!uniqFormCollectionItemIds?.length && !formCollectionItemEntities?.length) {
            return [];
        }

        const formCollectionItems = formCollectionItemEntities?.length
            ? formCollectionItemEntities
            : await this._formCollectionItemRepo.find({
                  where: {
                      id: In(uniqFormCollectionItemIds),
                  },
                  select: ['id', 'dataRegisterTransactionId', 'identityId', 'setting'],
              });

        if (!formCollectionItems.length) {
            return [];
        }

        const formCollectionItemsGroup = groupBy(formCollectionItems, 'dataRegisterTransactionId');
        const dataRegisterTransactionIds = Object.keys(formCollectionItemsGroup);

        const [dataTransactionFields, dataRegisterTransactions] = await Promise.all([
            this._dataRegisterTransactionFieldRepo.find({
                where: { dataRegisterTransactionId: In(dataRegisterTransactionIds) },
            }),
            dataRegisterTransactionIds?.length
                ? this._dataRegisterTransactionRepo.find({
                      where: { id: In(dataRegisterTransactionIds) },
                  })
                : Promise.resolve([]),
        ]);

        let dataRegisterVersionIds = [];
        let dataRegisterIds = [];

        for (const drt of dataRegisterTransactions) {
            dataRegisterIds.push(drt.dataRegisterId);
            dataRegisterVersionIds.push(drt.dataRegisterVersionId);
        }

        dataRegisterIds = uniq(dataRegisterIds);
        dataRegisterVersionIds = uniq(dataRegisterVersionIds);

        const dataRegisterFields = await this._getActiveFields({
            dataRegisterIds: dataRegisterIds,
        });

        const createFormCollectionItemTransactionFields: CollectionTransactionTenancyEntity[] = [];

        // await this._collectionTransactionRepo.softDelete({
        //     formCollectionItemId: In(uniqFormCollectionItemIds),
        // });

        formCollectionItems.forEach((formCollectionItem) => {
            if ((formCollectionItem?.setting as any)?.configuration?.hasGroup) {
                return;
            }

            const cloneTransactionFields: Array<
                (DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity) & {
                    configuration?: Record<string, any>;
                }
            > = cloneDeep(dataTransactionFields);

            if (dataRegisterFields.length) {
                dataRegisterFields.forEach((field) => {
                    const dataRegisterTransaction = dataRegisterTransactions.find(
                        (transaction) =>
                            transaction.dataRegisterVersionId === field.dataRegisterVersionId &&
                            formCollectionItem?.dataRegisterTransactionId === transaction.id,
                    );

                    const entity: DataRegisterTransactionFieldEntity & {
                        configuration?: Record<string, any>;
                    } = {
                        fieldId: field.fieldId,
                        fieldValue: null,
                        fieldOptionIds: [],
                        fieldType: field.type,
                        dataRegisterTransactionId: dataRegisterTransaction?.id ?? formCollectionItem?.dataRegisterTransactionId,
                        validationValue: null,
                        configuration: {
                            ...field?.configuration,
                            type: field?.type,
                        },
                    };

                    cloneTransactionFields.push(entity);
                });
            }

            let _formCollectionItemTransactionFields = cloneTransactionFields.filter(
                (field) => field.dataRegisterTransactionId === formCollectionItem.dataRegisterTransactionId,
            );

            _formCollectionItemTransactionFields = uniqWith(
                _formCollectionItemTransactionFields,
                (a, b) => a.fieldId === b.fieldId && a.dataRegisterTransactionId === b.dataRegisterTransactionId,
            );

            if (!_formCollectionItemTransactionFields.length) {
                return;
            }

            _formCollectionItemTransactionFields.forEach((field: any) => {
                createFormCollectionItemTransactionFields.push({
                    ...field,
                    id: v4(),
                    formCollectionItemIdentityId: formCollectionItem?.identityId,
                    formCollectionItemId: formCollectionItem?.id,
                    formVersionId: formVersionId,
                });
            });
        });

        return createFormCollectionItemTransactionFields ?? [];
    }

    public async getTestCollectionAutoPopulateSettings({
        formVersionId,
        formId,
        collections,
        formCollectionItems,
    }: {
        formVersionId: string;
        formId: string;
        formCollectionItems: FormCollectionItemTenancyEntity[];
        collections: FormCollectionTenancyEntity[];
    }): Promise<GeneralAutoPopulateSettingEntity[]> {
        const formFields = await this._formFieldRepo.findBy({
            formVersionId,
        });
        const formFieldRegisterIds = formFields.filter((f) => f.type === FormFieldTypeEnum.Lookup).map((f) => f.lookupTargetId);

        if (!collections?.length) {
            return [];
        }

        const collectionItems = formCollectionItems;
        if (!collectionItems?.length) return [];

        const collectionIdentityIds = collections.map((c) => c.identityId ?? c.id);
        const collectionsContextMappings = await this._contextMappingRepo.findBy({
            formVersionId,
            collectionIdentityId: In(collectionIdentityIds),
            type: ContextMappingType.POPULATE,
        });

        const collectionItemIdentityIds = collectionItems.map((c) => c.identityId ?? c.id);

        const generalAutoPopulateSettings = await this._generalAutoPopulateRepo.find({
            where: {
                builderVersionId: formVersionId,
                builderType: AutoPopulateBuilderTypeEnum.FormCollectionItem,
                itemIdentityId: In(collectionItemIdentityIds),
            },
            relations: {
                extraConfigurations: true,
            },
        });

        const transactionIds: string[] = collectionItems.map((item) => item.dataRegisterTransactionId);
        const registerAdditionalFields = await this._additionalFieldRepo.findBy({
            transactionId: In(uniq(compact(transactionIds))),
        });

        const additionalFields: {
            transactionId: string;
            settings: FormCollectionItemFieldAP[];
            fieldId: string;
            additionalType: string;
        }[] = registerAdditionalFields.map((item) => {
            return {
                transactionId: item.transactionId,
                fieldId: item.fieldId,
                settings:
                    (item.configuration?.setting?.autoPopulate?.general as FormCollectionItemFieldAP[]) ||
                    (item.configuration?.autoPopulate?.general as FormCollectionItemFieldAP[]) ||
                    [],
                additionalType: item.additionalType || item.configuration?.additionalType,
            };
        });

        const contextIds = collectionsContextMappings.map((cm) => cm.contextId);
        const filterRegisterIds = uniq(compact([...formFieldRegisterIds, ...contextIds]));
        const registers = await this._dataRegisterRepo.findBy({
            id: In(filterRegisterIds),
        });

        const updateApEntities: GeneralAutoPopulateSettingEntity[] = [];
        const updateExtraConfigs: GeneralAutoPopulateExtraConfigEntity[] = [];
        const removeExtraConfigIds: string[] = [];

        collectionItems.forEach((collectionItem) => {
            const collection = collections.find((c) => c.id === collectionItem.formCollectionId);
            const transactionId = collectionItem.dataRegisterTransactionId;
            const collectionItemAdditionalFields = additionalFields.filter((af) => af.transactionId === transactionId);
            for (const additionalField of collectionItemAdditionalFields) {
                const settings = additionalField?.settings || [];
                const itemIdentityId = collectionItem.identityId ?? collectionItem.id;
                const parentItemIdentityId = collection?.identityId ?? collection?.id;
                const existedCollectionItemSettings = generalAutoPopulateSettings.filter(
                    (item) => item.itemIdentityId === itemIdentityId && item.fieldId === additionalField.fieldId,
                );
                const updateCollectionItemSettings: GeneralAutoPopulateSettingEntity[] = [];
                const updateCollectionItemExtraSettings: GeneralAutoPopulateExtraConfigEntity[] = [];

                settings.forEach((setting) => {
                    let dataSourceId: string = setting.dataSource;
                    const isExternal = EXTERNAL_DATA_SOURCE_TYPES.includes(setting.dataSourceType);
                    let dataSourceCode: string = undefined;
                    if (isExternal) {
                        dataSourceCode = setting.dataSource;
                        dataSourceId = this._getSettingDataSource({
                            dataSource: setting.dataSource,
                            dataSourceType: setting.dataSourceType,
                        });
                    }

                    const existed = existedCollectionItemSettings.find(
                        (item) =>
                            item.dataSourceId === dataSourceId &&
                            item.fieldId === setting.fieldId &&
                            item.targetFieldId === setting.targetFieldId,
                    );

                    const updatedIds: string[] = [];

                    const isArray = setting?.targetFieldType?.includes('array');
                    const newSetting = {
                        id: existed?.id ?? v7(),
                        builderVersionId: formVersionId,
                        builderType: AutoPopulateBuilderTypeEnum.FormCollectionItem,
                        type: AutoPopulateExtraConfigTypeEnum.DataSource,
                        builderId: formId,
                        dataSourceType: setting.dataSourceType,
                        priority: setting.priority,
                        fieldId: setting.fieldId,
                        targetFieldId: setting.targetFieldId,
                        dataSourceId,
                        dataSourceCode,
                        itemIdentityId: collectionItem.identityId ?? collectionItem.id,
                        parentItemIdentityId,
                        targetFieldType: additionalField?.additionalType,
                        isArray,
                        includeValidationValue: setting.includeValidationValue,
                    } as GeneralAutoPopulateSettingEntity;

                    if (!setting?.extraConfigurations?.length) {
                        const mappingContext = collectionsContextMappings.find(
                            (rcm) =>
                                !rcm.sourceContextId && rcm.contextId === dataSourceId && rcm.collectionIdentityId === parentItemIdentityId,
                        );
                        const mappingFieldId = mappingContext?.fieldId;

                        if (mappingFieldId) {
                            updateCollectionItemSettings.push(newSetting);

                            const fromFieldRegister = registers.find((register) => register.id === mappingContext.contextId);
                            const extraConfigs = existed?.extraConfigurations || [];
                            const existedConfig = extraConfigs?.[0]; //just only have 1 condition

                            const toField = formFields.find((f) => f.fieldId === mappingFieldId);
                            const toFieldRegister = registers.find((register) => register.id === toField?.lookupTargetId);

                            const newConfig = {
                                generalAutoPopulateSettingId: newSetting.id,
                                builderId: formId,
                                builderVersionId: formVersionId,
                                type: AutoPopulateExtraConfigTypeEnum.DataSource,
                                builderType: AutoPopulateBuilderTypeEnum.FormCollectionItem,
                                dataSourceType: setting.dataSourceType,
                                itemIdentityId: collectionItem.identityId ?? collectionItem.id,
                                parentItemIdentityId,
                                fieldId: setting.fieldId,
                                fromFieldId: this.getMDSFieldId(fromFieldRegister?.type), //mds field id of context
                                toFieldId: mappingFieldId,
                                toLookupFieldId: this.getMDSFieldId(toFieldRegister?.type), //register
                                toFieldRegisterId: toField?.lookupTargetId, //register id
                                toTransactionRegisterId: additionalField.transactionId, //transaction ID
                                id: existedConfig?.id ?? v7(),
                            } as GeneralAutoPopulateExtraConfigEntity;
                            updateCollectionItemExtraSettings.push(newConfig);
                        } else if (
                            setting.dataSourceType === EXTERNAL_DATA_SOURCE_TYPE.PDF ||
                            setting.dataSourceType === EXTERNAL_DATA_SOURCE_TYPE.PURPLETRAC
                        ) {
                            updateCollectionItemSettings.push(newSetting);
                        }
                    } else {
                        updateCollectionItemSettings.push(newSetting);
                        const extraSettings = setting?.extraConfigurations || [];
                        const existedExtraConfigs = existed?.extraConfigurations || [];

                        extraSettings.forEach((extraSetting, idx) => {
                            const existedExtraConfig = idx < (existedExtraConfigs?.length ?? 0) ? existedExtraConfigs[idx] : undefined;
                            if (existedExtraConfig?.id) updatedIds.push(existedExtraConfig?.id);

                            const base: Partial<GeneralAutoPopulateExtraConfigEntity> = {
                                id: existedExtraConfig?.id,
                                generalAutoPopulateSettingId: newSetting.id,
                                builderId: formId,
                                builderVersionId: formVersionId,
                                type: AutoPopulateExtraConfigTypeEnum.DataSource,
                                builderType: AutoPopulateBuilderTypeEnum.FormCollectionItem,
                                dataSourceType: setting.dataSourceType,
                                itemIdentityId: collectionItem.identityId ?? collectionItem.id,
                                parentItemIdentityId,
                                extraType: extraSetting.extraType,
                                fieldId: setting.fieldId,
                                fromFieldId: extraSetting.fromFieldId,
                                toFieldId: extraSetting.toFieldId,
                                toTransactionRegisterId: additionalField.transactionId, //transaction ID
                                toLookupFieldId: extraSetting.toLookupFieldId,
                                toFieldRegisterId: extraSetting.toFieldRegisterId, //register id
                            };

                            switch (extraSetting.extraType) {
                                case EXTRA_CONFIGURATION_TYPES.FIXED_VALUE:
                                    {
                                        let fixedValue = Array.isArray(extraSetting.toFixedValue)
                                            ? extraSetting.toFixedValue.join(', ')
                                            : extraSetting.toFixedValue;

                                        if (!fixedValue) {
                                            if (extraSetting.toFixedFieldType === FormFieldTypeEnum.Checkbox) {
                                                fixedValue = 'false';
                                            } else {
                                                if (!extraSetting.fromFieldId) break;
                                            }
                                        }

                                        base.toFieldId = null;
                                        base.toLookupFieldId = null;
                                        base.toFieldRegisterId = null;
                                        base.fromFieldId = extraSetting.fromFieldId;
                                        base.fixedValue = fixedValue;
                                        base.formFieldType = extraSetting.toFixedFieldType;
                                        updateCollectionItemExtraSettings.push(base as GeneralAutoPopulateExtraConfigEntity);
                                    }
                                    break;

                                case EXTRA_CONFIGURATION_TYPES.CONTEXT:
                                    {
                                        if (!extraSetting.toContextId) break;
                                        const extraMappingContext = collectionsContextMappings.find(
                                            (rcm) =>
                                                rcm.sourceContextId === newSetting.dataSourceId &&
                                                rcm.contextId === extraSetting.toContextId &&
                                                rcm.collectionIdentityId === parentItemIdentityId,
                                        );

                                        if (!extraMappingContext) break;
                                        const toField = formFields.find((f) => f.fieldId === extraMappingContext.fieldId);
                                        const toFieldRegister = registers.find((register) => register.id === toField?.lookupTargetId);

                                        base.fromFieldId = extraSetting.fromFieldId;
                                        base.toFieldId = toField?.fieldId;
                                        base.toLookupFieldId = null;
                                        base.toFieldRegisterId = toFieldRegister?.id ?? null; //toFieldRegister may not exist if context is select type
                                        base.formFieldType = extraSetting.toContextFieldType;
                                        updateCollectionItemExtraSettings.push(base as GeneralAutoPopulateExtraConfigEntity);
                                    }
                                    break;

                                default: {
                                    const extraMappingContext = collectionsContextMappings.find(
                                        (rcm) =>
                                            rcm.contextId === newSetting.dataSourceId && rcm.collectionIdentityId === parentItemIdentityId,
                                    );

                                    if (extraMappingContext) {
                                        base.toFieldId = extraMappingContext.fieldId;
                                        base.fixedValue = extraSetting.toFixedValue;
                                        base.toLookupFieldId = extraSetting.toLookupFieldId ?? null;
                                        base.toFieldRegisterId = extraSetting.toFieldRegisterId ?? null;
                                        updateCollectionItemExtraSettings.push(base as GeneralAutoPopulateExtraConfigEntity);
                                    }
                                }
                            }
                        });

                        const removeIds = existedExtraConfigs.filter((item) => !updatedIds.includes(item.id)).map((item) => item.id);
                        removeExtraConfigIds.push(...removeIds);
                    }
                });

                updateApEntities.push(...updateCollectionItemSettings);
                updateExtraConfigs.push(...updateCollectionItemExtraSettings);
            }
        });

        updateApEntities.forEach((item) => {
            const extraConfigs = updateExtraConfigs.filter((e) => e.generalAutoPopulateSettingId === item.id);
            item.extraConfigurations = extraConfigs;
        });

        return updateApEntities;
    }

    private _getSettingDataSource(setting: { dataSourceType: string; dataSource: string }): string {
        if (EXTERNAL_DATA_SOURCE_TYPES.includes(setting.dataSourceType)) {
            const dataSource: string =
                EXTERNAL_DATA_SOURCE_MAPPING[
                    `${setting.dataSourceType?.toUpperCase()}${EXTERNAL_DATA_SOURCE__SEPARATE_MARK}${setting.dataSource?.toUpperCase()}`
                ];
            return dataSource ? dataSource : (setting.dataSource as string);
        }

        return setting.dataSource as string;
    }

    private getMDSFieldId(type: DataRegisterTypeEnum) {
        return MDS_PACK_FIELDS[type];
    }

    private async _getActiveFields({ dataRegisterIds }: { dataRegisterIds: string[] }) {
        const activeVersions = await this._dataRegisterRepo.find({
            where: { id: In(dataRegisterIds) },
            select: ['id', 'activeVersionId'],
        });

        const activeVersionIds = activeVersions.map(({ activeVersionId }) => activeVersionId);
        if (!activeVersionIds.length) return [];

        const newActiveFields =
            (await this._dataRegisterFieldRepo
                .createQueryBuilder('drf')
                .where('drf.dataRegisterVersionId IN (:...activeVersionIds)', { activeVersionIds })
                .getMany()) ?? [];

        newActiveFields.forEach((field) => {
            const activeVersion = activeVersions.find((version) => version.activeVersionId === field.dataRegisterVersionId);

            if (activeVersion) {
                field.dataRegisterVersion = Object.assign(new DataRegisterVersionEntity(), {
                    dataRegisterId: activeVersion.id,
                    id: field.dataRegisterVersionId,
                });
            }
        });

        return newActiveFields;
    }
}
