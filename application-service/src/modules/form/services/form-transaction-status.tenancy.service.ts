import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { CacheService } from '../../../common/src';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { StatusEnum } from '../../../database/src/enums/transaction-status.enum';
import { TransactionEventEnum } from '../../../database/src/shared/enums/automation-event.enum';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';

function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
@Injectable()
export class FormTransactionStatusTenancyService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepository: Repository<TransactionEntity>,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _cacheService: CacheService,
    ) {}

    public async initRollupStatus(transactionId: string, causationTransId: string, targetTransactionIds?: string[], isTest?: boolean) {
        // prevent self rollup
        if (transactionId === causationTransId) return;
        const filteredTargetTransactionIds = targetTransactionIds.filter((id) => id != transactionId);

        const transaction = await this.formTransactionRepository.findOne({
            where: { id: transactionId },
            withDeleted: !!isTest,
            // select: ['id', 'rollupStatus'],
        });
        const parentRollupStatus = {};
        for (const id of filteredTargetTransactionIds) parentRollupStatus[id] = { status: StatusEnum.Pending, causationTransId };
        transaction.rollupStatus.parentRollupStatus = parentRollupStatus;
        const resultOfTransaction = await this.formTransactionRepository.save(transaction);
        await this.sendTransactionUpdatedEvent(resultOfTransaction);
    }

    public checkRollupStatus(transaction: TransactionEntity) {
        const parentRollupStatus = transaction?.rollupStatus?.['parentRollupStatus'] || {};
        const allParentStatus = Object.values(parentRollupStatus).map((s: any) => s.status);
        return this.unionStatus(allParentStatus);
    }

    public unionStatus(allStatus: StatusEnum[]) {
        if (allStatus.every((status) => status === StatusEnum.Completed)) return StatusEnum.Completed;
        else if (allStatus.some((status) => status === StatusEnum.Failed)) return StatusEnum.Failed;
        else if (allStatus.some((status) => status === StatusEnum.Pending)) return StatusEnum.Pending;
        else if (allStatus.some((status) => status === StatusEnum.Processing)) return StatusEnum.Processing;

        return StatusEnum.Completed;
    }

    public async backTrackRollupStatus(
        causationTransId: string | null | undefined,
        transactionId: string,
        status: StatusEnum,
        isTest: boolean,
    ) {
        if (!transactionId) return;

        const transaction = await this.formTransactionRepository.findOne({
            where: { id: transactionId },
            withDeleted: !!isTest,
            // select: ['id', 'rollupStatus'],
        });

        if (!transaction) return;

        if (causationTransId === transactionId) {
            transaction.rollupStatus.status = status;
            const resultOfTransaction = await this.formTransactionRepository.save(transaction);
            await this.sendTransactionUpdatedEvent(resultOfTransaction, isTest);
            return;
        }

        const oldRollupStatus = transaction?.rollupStatus?.status;
        const newRollupStatus = this.unionStatus([this.checkRollupStatus(transaction), status]);
        // only update when status is changed
        if (oldRollupStatus != newRollupStatus) {
            if (!transaction.rollupStatus) {
                transaction.rollupStatus = { status: newRollupStatus };
            } else {
                transaction.rollupStatus.status = newRollupStatus;
            }
            const resultOfTransaction = await this.formTransactionRepository.save(transaction);
            await this.sendTransactionUpdatedEvent(resultOfTransaction, isTest);
            // update for causationTrans
            if (!causationTransId) return;
            const causationTrans = await this.formTransactionRepository.findOne({
                where: { id: causationTransId },
                withDeleted: true,
                // select: ['id', 'rollupStatus'],
            });
            const parentRollupStatus = causationTrans.rollupStatus['parentRollupStatus'] || {};
            parentRollupStatus[transactionId].status = newRollupStatus;
            causationTrans.rollupStatus['parentRollupStatus'] = parentRollupStatus;
            const resultOfCausationTrans = await this.formTransactionRepository.save(causationTrans);
            await this.sendTransactionUpdatedEvent(resultOfCausationTrans, causationTrans.isTest);
            // backtracking
            await this.backTrackRollupStatus(
                parentRollupStatus[transactionId].causationTransId,
                causationTransId,
                newRollupStatus,
                causationTrans.isTest,
            );
        }
    }
    public async setParentRollupStatus(transactionId: string, status: StatusEnum, parentId: string, isTest?: boolean) {
        if (transactionId === parentId) return;

        const transaction = await this.formTransactionRepository.findOne({
            where: { id: transactionId },
            withDeleted: !!isTest,
        });
        const parentRollupStatus = transaction.rollupStatus['parentRollupStatus'] || {};
        parentRollupStatus[parentId].status = status;
        transaction.rollupStatus['parentRollupStatus'] = parentRollupStatus;
        const resultOfTransaction = await this.formTransactionRepository.save(transaction);
        await this.sendTransactionUpdatedEvent(resultOfTransaction, isTest);
    }

    public async setSelfRollupStatus(transactionId: string, status: StatusEnum, isTest?: boolean) {
        await this.formTransactionRepository
            .createQueryBuilder()
            .update('transaction')
            .set({
                rollupStatus: () => `jsonb_set(COALESCE(rollup_status, '{}'::jsonb), '{status}', '"${status}"'::jsonb)`,
            })
            .where('id = :transactionId', { transactionId })
            .execute();
        await this.sendTransactionUpdatedEvent(transactionId, isTest);
    }

    public async setAutoCreationStatus(transactionId: string, status: StatusEnum) {
        await this.formTransactionRepository
            .createQueryBuilder()
            .update('transaction')
            .set({
                autoCreationStatus: () => `jsonb_set(COALESCE(auto_creation_status, '{}'::jsonb), '{status}', '"${status}"'::jsonb)`,
            })
            .where('id = :transactionId', { transactionId })
            .execute();
        await this.sendTransactionUpdatedEvent(transactionId);
    }

    public async sendTransactionUpdatedEvent(transactionInput: string | TransactionEntity, isTest?: boolean) {
        const { status, transaction } = await this.getTransactionStatus(transactionInput, isTest);

        if (!transaction) return;

        if (status === StatusEnum.Completed) {
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...transaction,
                    status,
                },
                tenantId: RequestContextService.accountId,
                aggregateId: transaction.id,
                type: TransactionEventEnum.FORM_TRANSACTION_UPDATED,
                name: TransactionEventEnum.FORM_TRANSACTION_UPDATED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
        }
    }

    public async getTransactionStatus(
        transactionInput: string | TransactionEntity,
        isTest?: boolean,
    ): Promise<{
        rollupStatus: StatusEnum | null;
        autoCreationStatus: StatusEnum | null;
        status: StatusEnum;
        transaction: TransactionEntity;
    }> {
        let transaction: TransactionEntity;
        if (typeof transactionInput === 'string') {
            transaction = await this.formTransactionRepository.findOne({
                where: { id: transactionInput },
                withDeleted: !!isTest,
            });
        } else {
            transaction = transactionInput;
        }
        if (!transaction)
            return {
                status: StatusEnum.Unknown,
                rollupStatus: StatusEnum.Unknown,
                autoCreationStatus: StatusEnum.Unknown,
                transaction: null,
            };
        const status = this.unionStatus(
            [
                transaction.rollupStatus?.status,
                // transaction.autoCreationStatus?.status
            ].filter(Boolean),
        );
        return {
            rollupStatus: transaction.rollupStatus?.status,
            autoCreationStatus: transaction.autoCreationStatus?.status,
            status,
            transaction,
        };
    }
}
