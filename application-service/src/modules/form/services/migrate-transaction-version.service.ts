import { BadRequestException, Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import * as _ from 'lodash';
import { groupBy, isNil } from 'lodash';
import { DataSource, In, Not, Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { TransactionFieldOverrideEntity } from '../../../database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { TransactionFieldStyleEntity } from '../../../database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { FormVersionStatus } from '../../../database/src/shared/enums/form-version-status.enum';
import { OverrideStatusEnum } from '../../../database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from '../../../database/src/shared/enums/override-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { AutoPopulateAndCheckValidationCollectionFieldService } from '../../../shared/services/auto-populate-check-validation-collection-field.service';
import { CalculationFieldDataService } from '../../../shared/services/calculation-fields.data.service';
import { CalculationService } from '../../../shared/services/calculation.service';
import { addSystemOverrideRecords, getOverrideRecord, mergeTransactionCollectionFields, mergeTransactionFields } from '../../../utils';
import { CheckValidationResponseType } from '../../validation/types';
import { MigrateTransactionVersionRequest, MigrateVersionType } from '../dtos/requests';
import { GetTransactionInformationResponse } from '../dtos/responses';
import { CollectionFieldMappingService } from './collection-field-mapping.service';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { FormatTransactionFieldService } from './data/format/transaction-field.service';
import { RelatedLookupDataService } from './data/related-lookup-data.service';
import { DefaultTransactionFieldService } from './default-transaction-field.service';
import { PopulateTransactionFieldService } from './populate-transaction-field.service';

@Injectable()
export class MigrateTransactionVersionService {
    constructor(
        private readonly _logger: LoggerService,
        private readonly _formTransactionDataService: FormTransactionDataService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly _transactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _transactionFieldRepository: Repository<TransactionFieldEntity>,

        private readonly _populateTransactionFieldService: PopulateTransactionFieldService,

        @Inject(PROVIDER_KEYS.FORM_VERSION_TENANCY_REPOSITORY)
        private readonly _formVersionRepository: Repository<FormVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        private readonly _calculationFieldService: CalculationFieldDataService,
        private readonly _calculationService: CalculationService,
        private readonly _collectionFieldMappingService: CollectionFieldMappingService,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionRepository: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly _transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>,

        @Inject(USER_CLAIMS) private readonly _claim: ClaimService,

        private readonly _apAndValidationCollectionFieldService: AutoPopulateAndCheckValidationCollectionFieldService,

        private readonly _relatedLookupDataService: RelatedLookupDataService,

        private readonly _defaultTransactionFieldService: DefaultTransactionFieldService,

        private readonly _formatTransactionFieldService: FormatTransactionFieldService,
    ) {}

    public migrateTransactionsVersion = async (request: MigrateTransactionVersionRequest) => {
        const queryRunner = this._dataSource.createQueryRunner();

        try {
            const { formId } = request;

            const { currentFormVersion, migratedFormVersion } = await this.getMigratedFormVersion(request);

            //TODO: log origin transaction before migrated to logs table
            const collections = await this._formCollectionRepository.find({
                where: {
                    formVersionId: migratedFormVersion.id,
                },
                relations: ['formCollectionItems'],
            });

            migratedFormVersion.formCollections = collections;

            let transactions = await this._transactionRepository.find({
                where: {
                    formId: formId,
                    formVersionId: currentFormVersion.id,
                },
                relations: ['transactionFields'],
            });

            if (!transactions.length) return;

            transactions = await this.updateTransactionVersion(transactions, currentFormVersion, migratedFormVersion);

            const changedTransactionFields: TransactionFieldEntity[] = [];
            await Promise.all(
                transactions.map(async (transaction) => {
                    const fields = await this.recalculateTransaction({
                        transactionFields: transaction.transactionFields,
                        originFormVersionFields: currentFormVersion.fields || [],
                        formVersion: migratedFormVersion,
                        transaction: transaction,
                    });
                    changedTransactionFields.push(...fields);
                }),
            );

            const relatedLookupData = await this._relatedLookupDataService.getRelatedLookupData({
                formVersion: migratedFormVersion,
                allTransactionFields: changedTransactionFields,
            });

            const overrideValues = await this.getOverrideValidationValues(changedTransactionFields.map((f) => f.id));

            //* re-calculate fields validation data (x)
            const { migratedFields, migratedFieldStyles } = await this.getFieldValidationResultAndStyles({
                transactions,
                transactionFields: changedTransactionFields,
                formFields: migratedFormVersion.fields,
                relatedLookupData,
            });

            //* migrate override validation data for fields
            const { updateOverrideFields, updateOverrideStyles, overrideFieldRecords, originTransactionFieldOverrides } =
                this.combineFieldOverrideValues({
                    migratedFields,
                    migratedFieldStyles,
                    overrideValues,
                });
            await queryRunner.connect();
            await queryRunner.startTransaction();
            const transactionRepo = queryRunner.manager.getRepository(TransactionEntity);
            const transactionFieldRepo = queryRunner.manager.getRepository(TransactionFieldEntity);
            const transactionFieldStyleRepo = queryRunner.manager.getRepository(TransactionFieldStyleEntity);
            const transactionFieldOverrideRepo = queryRunner.manager.getRepository(TransactionFieldOverrideEntity);

            const savedTransactions = await transactionRepo.save(transactions);
            const savedTransactionFields = await transactionFieldRepo.save(migratedFields);
            await transactionFieldStyleRepo.save(migratedFieldStyles);

            const transactionIds = savedTransactions.map((t) => t.id);

            // * auto populate collection & criteria fields
            const apResults = await this.autoPopulateCollectionFields({
                formVersionId: migratedFormVersion.id,
                transactionIds: transactionIds,
                transactionFieldRepository: transactionFieldRepo,
            });

            const afterUpdateFields = this.mergeCollectionFields({ originFields: savedTransactionFields, updatedFields: apResults });

            // * re-calculate collection & criteria fields validation data
            const { updateTransactionFieldStyles, updateTransactionFields } = await this.runValidationCollectionFields({
                formVersionId: migratedFormVersion.id,
                transactionIds: transactionIds,
                transactionFields: afterUpdateFields,
            });

            //* migrate override validation data for collection & criteria
            const { updateOverrideCollection, originTransactionCollectionOverrides } = this.combineCollectionFieldsOverrideValues({
                migratedFields: updateTransactionFields,
                migratedFieldStyles: updateTransactionFieldStyles,
                overrideValues,
            });

            // * save system override records
            const systemOverrideRecords = await this.getSystemOverrideRecords({
                transactionFieldOverrideRepo,
                updateOverrideCollection,
                updateOverrideFields,
                originTransactionCollectionOverrides,
                originTransactionFieldOverrides,
            });

            await Promise.all([
                transactionFieldRepo.save(updateTransactionFields),
                transactionFieldStyleRepo.save(updateTransactionFieldStyles),
                transactionFieldOverrideRepo.save(systemOverrideRecords),
            ]);

            await queryRunner.commitTransaction();

            // * re-calculate rollup for validation result
            this.publishRollUpCalculationEvent({
                accountId: this._claim.accountId,
                formId: migratedFormVersion.formId,
                formVersionId: migratedFormVersion.id,
                transactionFields: updateTransactionFields,
            });

            return true;
        } catch (err) {
            await queryRunner.rollbackTransaction();
            this._logger.error(err);
            throw new InternalServerErrorException(err);
        } finally {
            // Release the query runner
            await queryRunner.release();
        }
    };

    public getTransactionInformation = async (formId: string): Promise<GetTransactionInformationResponse[]> => {
        const transactions = await this._transactionRepository.find({
            where: {
                formId: formId,
            },
            select: ['id', 'formVersion', 'formVersionId'],
        });

        const formVersions = await this._formVersionRepository.find({
            where: {
                formId,
                status: FormVersionStatus.Published,
            },
            select: ['version', 'id'],
            order: { version: 'DESC' },
        });

        const transactionInformation = formVersions.map(({ version, id }) => {
            return {
                version,
                totalTransaction: transactions.filter((t) => t.formVersionId === id).length,
            };
        });

        return transactionInformation;
    };

    private recalculateTransaction = async ({
        transactionFields,
        originFormVersionFields,
        formVersion,
        transaction,
    }: {
        transactionFields: TransactionFieldEntity[];
        originFormVersionFields: FormFieldTenancyEntity[];
        formVersion: FormVersionTenancyEntity;
        transaction: TransactionEntity;
    }): Promise<TransactionFieldEntity[]> => {
        const { fields: formVersionFields, formCollections = [] } = formVersion;

        const collectionFields = formCollections.map((c) => c.formCollectionItems).flat();

        const transactionIds = _.uniq(transactionFields.map((f) => f.transactionId));

        const defaultCollectionFields = await this.getDefaultCollectionFields(transaction, formVersion, '');

        let updatedTransactionFields: TransactionFieldEntity[] = await this._populateTransactionFieldService.populateTransactionFields(
            {
                transactionFields,
                formVersionFields,
            },
            transaction.id,
        );

        const calcFieldValues = this.calculateTransactionFields({
            formFields: formVersionFields,
            transactionFields,
            originFormFields: originFormVersionFields,
        });

        updatedTransactionFields.forEach((field) => {
            const isCalcField = formVersionFields.find((item) => item?.fieldId === field.fieldId)?.type === FormFieldTypeEnum.Calculation;
            if (isCalcField) {
                field.fieldValue = calcFieldValues?.[field.fieldId];
            }
        });

        updatedTransactionFields = mergeTransactionCollectionFields(updatedTransactionFields, defaultCollectionFields);

        const transactionResults = await this._collectionFieldMappingService.mapFormFieldsToCollectionFields({
            collectionFields,
            formFields: formVersionFields,
            transactionFields: updatedTransactionFields,
        });

        updatedTransactionFields = mergeTransactionFields(updatedTransactionFields, transactionResults);

        return updatedTransactionFields;
    };

    private getDefaultCollectionFields = async (
        transaction: TransactionEntity,
        formVersion: FormVersionTenancyEntity,
        timezone: string,
    ) => {
        const { collectionTransactionFields } = await this._defaultTransactionFieldService.getDefaultCollectionFields({
            transaction,
            formVersionId: formVersion.id,
            timezone,
            stages: formVersion.stages || [],
        });
        return collectionTransactionFields || [];
    };

    private updateTransactionVersion = async (
        transactions: TransactionEntity[],
        currentFormVersion: FormVersionTenancyEntity,
        migratedFormVersion: FormVersionTenancyEntity,
    ): Promise<TransactionEntity[]> => {
        transactions.forEach((transaction) => {
            transaction.formVersionId = migratedFormVersion.id;
            const currentStage = currentFormVersion.stages.find((stage) => stage.id === transaction.stageId);
            const migratedStage = migratedFormVersion.stages.find((stage) => stage.identityId === currentStage.identityId);

            const endStage = migratedFormVersion.stages.find((stage) => stage.config.type === 'END');

            if (!migratedStage) {
                transaction.stageId = endStage.id;
                transaction.stageName = endStage.name;
            } else {
                transaction.stageId = migratedStage.id;
                transaction.stageName = migratedStage.name;
            }
        });
        return transactions;
    };

    public calculateTransactionFields({
        formFields,
        transactionFields,
        originFormFields,
    }: {
        transactionFields: TransactionFieldEntity[];
        formFields: FormFieldTenancyEntity[];
        originFormFields: FormFieldTenancyEntity[];
    }): Record<string, any> {
        try {
            const calcFormFields = (formFields || []).filter((field) => field.type === FormFieldTypeEnum.Calculation);
            const originCalcFields = calcFormFields.filter((field) => originFormFields?.some((f) => f.fieldId === field.fieldId));
            const unchangedConfigFields: FormFieldTenancyEntity[] = this._calculationFieldService.getChanges({
                currentFields: calcFormFields,
                originFields: originCalcFields,
            });

            const priorityFields = this._calculationFieldService.getPriority({
                formFields,
                unChangeFieldIds: unchangedConfigFields.map((f) => f.fieldId),
            });

            const stackValues: Record<string, any> = {};
            const requestValues: { [fieldId: string]: any } = (transactionFields || []).reduce(
                (prev, current) => {
                    return { ...prev, [current.fieldId]: current.fieldValue };
                },
                {} as { [fieldId: string]: any },
            );

            for (const field of priorityFields) {
                const fieldStackValues: Array<any> = [];
                this._calculationService.calculateCalculationField({
                    stackValues,
                    calculationField: {
                        ...field,
                        calculationFormula: field.configuration?.calculationFormula,
                    },
                    requestValues,
                    transactionFields,
                    calculationFormFields: calcFormFields.map((field) => ({
                        dataType: field.configuration?.calculationFormula?.dataType,
                        id: field.fieldId,
                        defaultValue: field.configuration?.defaultValue,
                        formulas: field.configuration?.calculationFormula?.formulas || [],
                        calculationFormula: field.configuration?.calculationFormula,
                    })),
                    fieldStackValues,
                });

                if (!isNil(fieldStackValues?.[field.fieldId])) {
                    requestValues[field.fieldId] = fieldStackValues?.[field.fieldId];
                }
            }

            return stackValues;
        } catch (err) {
            this._logger.error(err);
            return null;
        }
    }

    private getMigratedFormVersion = async (request: MigrateTransactionVersionRequest) => {
        const { currentVersion, formId, type } = request;

        const currentForm = await this._formTransactionDataService.getFormWithActiveVersion({
            formId,
            version: currentVersion,
        });

        //TODO: validate migrated type with current version (ver.1 cannot backward, last active ver cannot forward)
        const migratedForm = await this._formTransactionDataService.getFormWithActiveVersion({
            formId,
            version: type === MigrateVersionType.Backward ? currentVersion - 1 : currentVersion + 1,
        });

        if (!currentForm?.formVersions?.length || !migratedForm.formVersions?.length) {
            throw new BadRequestException('invalid_form_version');
        }

        const currentFormVersion = currentForm.formVersions[0];
        const migratedFormVersion = migratedForm.formVersions[0];

        return {
            currentFormVersion,
            migratedFormVersion,
        };
    };

    private getFieldValidationResultAndStyles = async ({
        transactions,
        transactionFields,
        relatedLookupData,
        formFields,
    }: {
        transactions: TransactionEntity[];
        transactionFields: TransactionFieldEntity[];
        relatedLookupData: {
            dataRegisters: DataRegisterTenancyEntity[];
            dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
        };
        formFields: FormFieldTenancyEntity[];
    }) => {
        const fieldResult: TransactionFieldEntity[] = [];
        const fieldStyleResult: TransactionFieldStyleEntity[] = [];

        const groupFieldByTransaction = groupBy(transactionFields, 'transactionId');
        for (const transactionId in groupFieldByTransaction) {
            const fields = groupFieldByTransaction[transactionId];

            const fieldValidationResult = await this._formTransactionDataService.validateTransactionData(
                formFields,
                fields,
                relatedLookupData,
            );

            const fieldStyles = this._formTransactionDataService.getFieldStyles(formFields, fields, fields, relatedLookupData);
            const transaction = transactions.find((t) => t.id === transactionId);

            const updateFieldsMap = (fields || []).reduce((prev, current) => {
                const key = this._formatTransactionFieldService.getTransactionFieldKey(current);
                prev.set(key, current);
                return prev;
            }, new Map());

            const updatedFieldStyles: TransactionFieldStyleEntity[] = this._formTransactionDataService.getUpdatedFieldStyles({
                validationResult: fieldValidationResult,
                combinedTranFields: fields,
                transaction,
                updateFields: fields,
                fieldStyles,
                updateFieldsMap,
            });

            const { fieldStylesToUpdate } = this.genFieldStyleRelation(fields, updatedFieldStyles);

            fieldResult.push(...fields);
            fieldStyleResult.push(...fieldStylesToUpdate);
        }

        return { migratedFields: fieldResult, migratedFieldStyles: fieldStyleResult };
    };

    private genFieldStyleRelation(
        updateFields: TransactionFieldEntity[],
        updateFieldStyles: TransactionFieldStyleEntity[],
    ): {
        fieldStylesToUpdate: TransactionFieldStyleEntity[];
    } {
        let updatedFieldStyles: TransactionFieldStyleEntity[] = [];

        const [formFields, collectionFields] = _.partition(updateFields, (f) => f.contextType === TransactionFieldContextTypeEnum.FORM);

        if (updateFieldStyles?.length) {
            updateFieldStyles.forEach((item) => {
                let tranField = formFields.find((f) => f.fieldId === item.fieldId);
                if (tranField) {
                    item.id = tranField.id;
                } else {
                    tranField = collectionFields.find((f) => f.fieldId === item.fieldId);

                    if (tranField && !item.id) {
                        item.id = tranField.id;
                    }
                }
            });
            updatedFieldStyles = updateFieldStyles.filter((f) => f.id);
        }
        return { fieldStylesToUpdate: updatedFieldStyles };
    }

    private autoPopulateCollectionFields = async ({
        formVersionId,
        transactionIds,
        transactionFieldRepository,
    }: {
        formVersionId: string;
        transactionIds: string[];
        transactionFieldRepository: Repository<TransactionFieldEntity>;
    }) => {
        const populateCollectionFields = await this.apCollectionFields({ formVersionId, transactionIds });

        const transformToUpdateParams = await Promise.all(
            transactionIds.map(async (transactionId) => {
                const populateCollectionFieldByTran = populateCollectionFields.filter((f) => f.transactionId === transactionId);
                return await this.transformPopulateCollectionFields(
                    populateCollectionFieldByTran,
                    transactionId,
                    transactionFieldRepository,
                );
            }),
        );

        const updatedFields = transformToUpdateParams.flat();

        await transactionFieldRepository.save(updatedFields);

        return updatedFields;
    };

    private apCollectionFields = async ({ formVersionId, transactionIds }: { formVersionId: string; transactionIds: string[] }) => {
        const apTasks = transactionIds.map((transactionId) => {
            return this._apAndValidationCollectionFieldService.autoPopulateCollectionFields({
                activeFormVersionId: formVersionId,
                transactionId,
                ignoreSave: true,
                isTest: false,
            });
        });

        const data = await Promise.all(apTasks);

        return (data || []).map((d) => d.populateCollectionFields).flat();
    };

    private async transformPopulateCollectionFields(
        populateCollectionFields: any[],
        transactionId: string,
        transactionFieldRepository: Repository<TransactionFieldEntity>,
    ): Promise<Array<Partial<TransactionFieldEntity>>> {
        const query = {
            collectionIds: [],
            collectionItemIds: [],
            fieldIds: [],
        };

        populateCollectionFields.forEach((field) => {
            query.collectionIds.push(field.collectionId);
            query.collectionItemIds.push(field.collectionItemId);
            query.fieldIds.push(field.fieldId);
        });

        const result: Array<Partial<TransactionFieldEntity>> = [];

        const transactionFields =
            (await transactionFieldRepository.find({
                where: {
                    collectionId: In(_.uniq(query.collectionIds)),
                    collectionItemId: In(_.uniq(query.collectionItemIds)),
                    fieldId: In(_.uniq(query.fieldIds)),
                    transactionId: transactionId,
                    // fieldValue: In([null, ' ', '']),
                },
                select: ['id', 'fieldId', 'collectionId', 'collectionItemId', 'fieldValue', 'transactionId'],
            })) ?? [];

        if (transactionFields.length) {
            transactionFields.forEach((field) => {
                const transactionField = populateCollectionFields.find(
                    (cField) =>
                        cField.collectionId === field.collectionId &&
                        cField.collectionItemId === field.collectionItemId &&
                        cField.fieldId === field.fieldId,
                );

                if (transactionField) {
                    result.push({
                        ...field,
                        id: field.id,
                        fieldId: transactionField.fieldId,
                        fieldValue: transactionField.fieldValue as string,
                    });
                }
            });
        }
        return result;
    }

    private runValidationCollectionFields = async ({
        formVersionId,
        transactionIds,
        transactionFields,
    }: {
        formVersionId: string;
        transactionIds: string[];
        transactionFields: TransactionFieldEntity[];
    }): Promise<{
        updateTransactionFields: Array<TransactionFieldEntity>;
        updateTransactionFieldStyles: TransactionFieldStyleEntity[];
    }> => {
        const groupByTransaction = groupBy(transactionFields, 'transactionId');

        const needUpdateFields: TransactionFieldEntity[] = [];
        const needUpdateFieldStyles: TransactionFieldStyleEntity[] = [];

        for (const transactionId of transactionIds) {
            const fields = groupByTransaction[transactionId];
            const validationData = await this._apAndValidationCollectionFieldService.checkValidation({
                activeFormVersionId: formVersionId,
                transactionId,
                ignoreSave: true,
                transactionFields,
                isTest: false,
            });

            const { collectionValidation } = validationData;

            const { updateTransactionFieldStyles, updateTransactionFields } = await this.transformCollectionValidation({
                validations: collectionValidation || [],
                transactionFields: fields,
                transactionId,
            });

            needUpdateFields.push(...updateTransactionFields);
            needUpdateFieldStyles.push(...updateTransactionFieldStyles);
        }

        return { updateTransactionFields: needUpdateFields, updateTransactionFieldStyles: needUpdateFieldStyles };
    };

    private async transformCollectionValidation({
        validations,
        transactionFields,
        transactionId,
    }: {
        validations: CheckValidationResponseType[];
        transactionFields: TransactionFieldEntity[];
        transactionId: string;
    }): Promise<{
        updateTransactionFields: Array<TransactionFieldEntity>;
        updateTransactionFieldStyles: TransactionFieldStyleEntity[];
    }> {
        const collectionFieldStyle: TransactionFieldStyleEntity[] = [];
        const transactionFieldIds: string[] = [];

        validations.forEach((field) => {
            transactionFieldIds.push(field.transactionFieldStyleId);

            collectionFieldStyle.push({
                id: field.transactionFieldStyleId,
                configuration: field.configuration,
                fieldId: field.fieldId,
                transactionId: transactionId,
            });
        });

        if (!transactionFieldIds.length) {
            return { updateTransactionFields: [], updateTransactionFieldStyles: [] };
        }

        const updateTransactionFields = [];
        const updateTransactionFieldStyles = [];

        if (transactionFields?.length) {
            transactionFields.forEach((entity) => {
                const collectionField = validations.find((validation) => validation.transactionFieldStyleId === entity.id);
                if (collectionField) {
                    updateTransactionFields.push({
                        ...entity,
                        id: entity.id,
                        fieldId: collectionField.fieldId,
                        validationValue: collectionField.validationValue,
                    });

                    updateTransactionFieldStyles.push({
                        id: entity.id,
                        fieldId: entity.fieldId,
                        transactionId: entity.transactionId,
                        configuration: collectionField.configuration,
                    });
                }
            });
        }

        return { updateTransactionFields, updateTransactionFieldStyles };
    }

    private mergeCollectionFields = ({
        originFields,
        updatedFields,
    }: {
        originFields: TransactionFieldEntity[];
        updatedFields: Array<Partial<TransactionFieldEntity>>;
    }) => {
        const groupByTransaction = groupBy(originFields, 'transactionId');
        const result: TransactionFieldEntity[] = [];

        for (const transactionId in groupByTransaction) {
            const fields = groupByTransaction[transactionId];
            const updateFields = updatedFields.filter((f) => f.transactionId === transactionId);

            fields.forEach((field) => {
                const updateField = updateFields.find(
                    (f) =>
                        f.fieldId === field.fieldId &&
                        f.collectionId === field.collectionId &&
                        f.collectionItemId === field.collectionItemId,
                );
                if (updateField) {
                    field = {
                        ...field,
                        ...updateField,
                    };
                }
            });
            result.push(...fields);
        }

        return result;
    };

    private getOverrideValidationValues = async (fieldIds: string[]) => {
        const overrideValues = await this._transactionFieldOverrideRepo.find({
            where: {
                transactionFieldId: In(fieldIds),
                status: Not(OverrideStatusEnum.UnOverride),
                type: OverrideTypeEnum.User,
            },
        });

        return overrideValues;
    };

    private combineFieldOverrideValues = ({
        migratedFieldStyles,
        migratedFields,
        overrideValues,
    }: {
        migratedFields: TransactionFieldEntity[];
        migratedFieldStyles: TransactionFieldStyleEntity[];
        overrideValues: TransactionFieldOverrideEntity[];
    }) => {
        const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum }> = {};

        migratedFields.forEach((field) => {
            originTransactionFieldOverrides[field.id] = {
                value: field.validationValue,
                type: OverrideTypeEnum.System,
            };
        });

        const overrideFieldRecords = this._formTransactionDataService.combineValidationValue({
            updateFields: migratedFields,
            updatedFieldStyles: migratedFieldStyles,
            overrideValues,
            originTransactionFieldOverrides,
        });

        return {
            overrideFieldRecords,
            updateOverrideFields: migratedFields,
            updateOverrideStyles: migratedFieldStyles,
            originTransactionFieldOverrides,
        };
    };

    private combineCollectionFieldsOverrideValues({
        migratedFieldStyles,
        migratedFields,
        overrideValues,
    }: {
        migratedFields: TransactionFieldEntity[];
        migratedFieldStyles: TransactionFieldStyleEntity[];
        overrideValues: TransactionFieldOverrideEntity[];
    }) {
        const activeOverrides: TransactionFieldOverrideEntity[] = [];
        const originTransactionCollectionOverrides: Record<string, { value: number; type: OverrideTypeEnum }> = {};

        migratedFields.forEach((transactionField) => {
            originTransactionCollectionOverrides[transactionField.id] = {
                type: OverrideTypeEnum.System,
                value: transactionField.validationValue,
            };
            const fieldStyle = migratedFieldStyles.find((fs) => fs.id === transactionField.id);
            if (!fieldStyle) return;

            const overrides = overrideValues.filter((ov) => ov.transactionFieldId === transactionField.id);
            const overrideRecord = getOverrideRecord(migratedFields, overrides);
            if (!overrideRecord) return;
            activeOverrides.push(overrideRecord as TransactionFieldOverrideEntity);

            fieldStyle.configuration = {
                icon: overrideRecord.validationValue,
                label: overrideRecord.comment,
                border: fieldStyle.configuration.icon,
            };

            // const responseItem = _result.find((i) => i.rowKey === transactionField.rowKey && i.fieldId === transactionField.fieldId);
            // if (responseItem) {
            //     responseItem.validationValue = overrideRecord.validationValue;
            //     responseItem.label = overrideRecord.comment;
            //     responseItem.border = fieldStyle.configuration.border;
            // }
        });

        return {
            collectionOverrideRecords: activeOverrides,
            updateOverrideCollection: migratedFields,
            updateOverrideCollectionStyles: migratedFieldStyles,
            originTransactionCollectionOverrides,
        };
    }

    private publishRollUpCalculationEvent = ({
        transactionFields,
        accountId,
        formId,
        formVersionId,
    }: {
        transactionFields: TransactionFieldEntity[];
        accountId: string;
        formId: string;
        formVersionId: string;
    }) => {
        if (!transactionFields.length) return;
        const groupByTransaction = groupBy(transactionFields, 'transactionId');

        for (const transactionId in groupByTransaction) {
            const fields = groupByTransaction[transactionId];

            this._formTransactionDataService.publishUpdatedFieldEventForRollup({
                accountId,
                fields,
                formId,
                formVersionId,
                transactionId,
                stageId: '',
            });
        }
    };

    private getSystemOverrideRecords = async ({
        originTransactionFieldOverrides,
        originTransactionCollectionOverrides,
        transactionFieldOverrideRepo,
        updateOverrideFields,
        updateOverrideCollection,
    }: {
        originTransactionFieldOverrides: Record<
            string,
            {
                value: number;
                type: OverrideTypeEnum;
            }
        >;
        originTransactionCollectionOverrides: Record<
            string,
            {
                value: number;
                type: OverrideTypeEnum;
            }
        >;
        transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>;
        updateOverrideFields: TransactionFieldEntity[];
        updateOverrideCollection: TransactionFieldEntity[];
    }) => {
        const [systemOverrideFieldRecords, systemCollectionOverrideFieldRecords] = await Promise.all([
            addSystemOverrideRecords({
                originTransactionFieldOverrides,
                transactionFieldOverrideRepo,
                updateFields: updateOverrideFields,
                ignoreSave: true,
            }),
            addSystemOverrideRecords({
                originTransactionFieldOverrides: originTransactionCollectionOverrides,
                transactionFieldOverrideRepo,
                updateFields: updateOverrideCollection,
                ignoreSave: true,
            }),
        ]);

        const systemOverrideRecords = [...systemOverrideFieldRecords, ...systemCollectionOverrideFieldRecords];
        return systemOverrideRecords;
    };

    //#endregion Private methods
}
