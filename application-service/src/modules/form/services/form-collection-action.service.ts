import { BadRequestException, ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common';

import { EventDrivenService } from 'src/common/src/modules/event-driven/event-driven.service';
import { FilterOptionDto } from 'src/common/src/modules/shared/dtos/filter-option.dto';
import { TransactionTopicEnum } from 'src/common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { OperatorType } from 'src/common/src/modules/shared/enums/operator.enum';
import { OrderType } from 'src/common/src/modules/shared/enums/order.enum';
import { AutomationVersionTenancyEntity } from 'src/database/src/entities/tenancy/automation-version.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from 'src/database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from 'src/database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from 'src/database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionAutomationMappingTenancyEntity } from 'src/database/src/entities/tenancy/form-collection-automation-mapping.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from 'src/database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { StageRoleTenancyEntity } from 'src/database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionFieldEntity } from 'src/database/src/entities/tenancy/transaction-field.tenancy.entity';
import { WorkspaceTenancyEntity } from 'src/database/src/entities/tenancy/workspace.tenancy.entity';
import { AutoPopulateBuilderTypeEnum } from 'src/database/src/shared/enums/ap-builder-type.enum';
import { AutoPopulateDataSourceTypeEnum } from 'src/database/src/shared/enums/ap-data-source-type.enum';
import { TransactionEventEnum } from 'src/database/src/shared/enums/automation-event.enum';
import { DataRegisterTypeEnum } from 'src/database/src/shared/enums/data-register-type.enum';
import { WorkspaceTypeEnum } from 'src/database/src/shared/enums/workspace-type.enum';
import { AutoPopulateAndCheckValidationCollectionFieldService } from 'src/shared/services/auto-populate-check-validation-collection-field.service';
import { FormTransactionQueryBuilderService } from 'src/shared/services/form-transaction-query-builder.service';
import { FindOptionsWhere, In, IsNull, Not, Repository } from 'typeorm';
import { ClaimService, USER_CLAIMS } from '../../../common/src';
import { SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormContextMappingTenancyEntity } from '../../../database/src/entities/tenancy/form-context-mapping.tenancy.entity';
import { ContextMappingType } from '../../../database/src/shared/enums/context-mapping-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import {
    GetFormTransactionRequestDto,
    GetRegisterRecordRequestDto,
    TriggerCollectionAutomationActionRequestDto,
} from '../dtos/requests/collection-action.dto';
import { FormEventDataService } from './data/form-event.data.service';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { FormEventTenancyService } from './form-event.service';
import { PopulateTransactionFieldService } from './populate-transaction-field.service';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';

@Injectable()
export class FormCollectionActionService {
    constructor(
        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _generalAutoPopulateSettingRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _transactionFieldRepo: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _formCollectionAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_AUTOMATION_MAPPING_TENANCY_REPOSITORY)
        private readonly _automationMappingRepo: Repository<FormCollectionAutomationMappingTenancyEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_VERSION_TENANCY_REPOSITORY)
        private readonly _automationVersionRepo: Repository<AutomationVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_ROLE_TENANCY_REPOSITORY)
        private readonly _stageRoleRepo: Repository<StageRoleTenancyEntity>,

        @Inject(PROVIDER_KEYS.WORKSPACE_TENANCY_REPOSITORY)
        private readonly _workspaceRepository: Repository<WorkspaceTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly _dataRegisterRepo: Repository<DataRegisterTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly _dataRegisterTransactionRepo: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_CONTEXT_MAPPING_TENANCY_REPOSITORY)
        private readonly _formContextMappingRepo: Repository<FormContextMappingTenancyEntity>,

        private readonly _dataService: FormTransactionDataService,

        private readonly _formEventService: FormEventTenancyService,
        private readonly _formEventDataService: FormEventDataService,
        private readonly _eventDrivenService: EventDrivenService,

        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        private readonly _autoPopulateAndCheckValidationCollectionFieldService: AutoPopulateAndCheckValidationCollectionFieldService,
        private readonly _populateTransactionFieldService: PopulateTransactionFieldService,
        private readonly _formTransactionQueryBuilderService: FormTransactionQueryBuilderService,
        // private readonly _logger: LoggerService,
    ) {}

    public async getRegisterRecord(request: GetRegisterRecordRequestDto) {
        const { fieldId, rowKey, transactionId } = request;

        const transactionField = await this._transactionFieldRepo.findOne({
            where: {
                fieldId,
                rowKey,
                transactionId,
            },
            relations: {
                transaction: true,
            },
        });

        const transaction = transactionField?.transaction;

        if (!transaction) {
            throw new NotFoundException('transaction_field_not_found');
        }

        const formVersionId = transaction.formVersionId;

        const autoPopulateSetting = await this._generalAutoPopulateSettingRepo.findOne({
            where: {
                builderType: AutoPopulateBuilderTypeEnum.FormCollectionItem,
                builderVersionId: formVersionId,
                fieldId,
                dataSourceType: AutoPopulateDataSourceTypeEnum.Register,
                itemIdentityId: transactionField.collectionItemId,
            },
            relations: {
                extraConfigurations: true,
            },
        });

        if (!autoPopulateSetting) {
            throw new NotFoundException('auto_populate_setting_not_found');
        }

        const formValues = await this._autoPopulateAndCheckValidationCollectionFieldService.convertTransactionFieldsToFormValues({
            transactionId: transaction.id,
        });

        const formVersion = await this._dataService.getFormByVersion({ formVersionId });
        const fields = formVersion?.fields;
        const fieldIds = Object.keys(formValues);

        const availableFields = fields
            .filter((f) => {
                return fieldIds.includes(f.fieldId) && !!formValues[f.fieldId];
            })
            .map((f) => ({
                ...f,
                value: formValues[f.fieldId],
            }));

        const registerRecord = await this._populateTransactionFieldService.execute({
            availableFields,
            fieldSetting: autoPopulateSetting,
            formFields: fields,
        });

        if (!registerRecord) return null;

        const additionalField = await this._formCollectionAdditionalFieldRepo.findOne({
            where: {
                fieldId,
                formVersionId: transaction.formVersionId,
                formCollectionItemIdentityId: transactionField.collectionItemId,
            },
        });

        if (!additionalField) {
            throw new NotFoundException('additional_field_not_found');
        }
        const openRegisterConfiguration = additionalField.configuration?.setting?.collectionActions?.openRegisterRecord;
        if (!openRegisterConfiguration) {
            throw new NotFoundException('open_register_configuration_not_found');
        }

        const openWith = openRegisterConfiguration.openWith;
        if (openWith?.toString() !== 'workspace') return registerRecord;

        const register = await this._dataRegisterRepo.findOne({
            where: {
                id: registerRecord.dataRegisterId,
            },
        });

        if (!register) {
            throw new NotFoundException('register_not_found');
        }

        const getWorkspaceType = () => {
            switch (register.type) {
                case DataRegisterTypeEnum.Vessel:
                    return WorkspaceTypeEnum.Vessel;
                case DataRegisterTypeEnum.Company:
                    return WorkspaceTypeEnum.Company;
                case DataRegisterTypeEnum.Berth:
                    return WorkspaceTypeEnum.Location;

                default:
                    return WorkspaceTypeEnum.Standard;
            }
        };

        const wsType = getWorkspaceType();

        const where: Record<string, any> = {
            type: wsType,
            activeVersionId: Not(IsNull()),
        };

        let mdsId: string | undefined;
        if (wsType === WorkspaceTypeEnum.Standard) {
            where.registerId = registerRecord.dataRegisterId;
        } else {
            const record = await this._dataRegisterTransactionRepo.findOne({
                where: {
                    id: registerRecord.id,
                },
            });

            if (record) {
                mdsId = record.externalId;
            }
        }

        const workspace = await this._workspaceRepository.findOne({
            where,
        });

        return {
            ...registerRecord,
            workspaceId: workspace?.id,
            workspaceType: wsType,
            mdsId,
        };
    }

    public async getFormTransaction(request: GetFormTransactionRequestDto) {
        const { fieldId, rowKey, transactionId } = request;
        const transactionField = await this._transactionFieldRepo.findOne({
            where: {
                fieldId,
                rowKey,
                transactionId,
            },
            relations: {
                transaction: true,
            },
        });

        if (!transactionField) {
            throw new NotFoundException('transaction_field_not_found');
        }

        const transaction = transactionField.transaction;

        const additionalField = await this._formCollectionAdditionalFieldRepo.findOne({
            where: {
                fieldId,
                formVersionId: transaction.formVersionId,
                formCollectionItemIdentityId: transactionField.collectionItemId,
            },
        });

        if (!additionalField) {
            throw new NotFoundException('additional_field_not_found');
        }

        const openFormTransactionConfiguration = additionalField.configuration?.setting?.collectionActions?.openFormTransaction;

        const isValidConfiguration = (): boolean => {
            if (!openFormTransactionConfiguration) {
                return false;
            }

            const { enable, formId } = openFormTransactionConfiguration;

            if (enable.toString() !== 'true' || !formId) {
                return false;
            }

            return true;
        };

        if (!isValidConfiguration()) {
            throw new BadRequestException('invalid_open_form_transaction_configuration');
        }

        const { formId, extraConfiguration = [] } = openFormTransactionConfiguration as {
            formId: string;
            extraConfiguration: {
                extraType: 'default' | 'fixed_value' | 'context';
                registerFieldId: string;
                dataSourceFieldId: string;
                toContextId?: string;
                toContextFieldType?: FormFieldTypeEnum;
                toFixedValue?: string;
                toFixedFieldType?: FormFieldTypeEnum;
            }[];
        };

        const contextFields = extraConfiguration.filter((item) => item.extraType === 'context');
        let contextMappings: FormContextMappingTenancyEntity[] = [];
        if (contextFields?.length) {
            const currentFormVersionId = transaction.formVersionId;
            if (currentFormVersionId) {
                contextMappings = await this._formContextMappingRepo.find({
                    select: {
                        fieldId: true,
                        contextId: true,
                        sourceContextId: true,
                    },
                    where: {
                        formVersionId: currentFormVersionId,
                        collectionIdentityId: transactionField.collectionId,
                        type: ContextMappingType.FORM_ACTION,
                    },
                });
            }
        }

        const filterFormTranFieldIds = extraConfiguration
            .filter((item) => item.dataSourceFieldId && !item.registerFieldId)
            .map((item) => {
                switch (item.extraType) {
                    case 'context':
                        const dataSourceFieldId = item.dataSourceFieldId;
                        return contextMappings.find((f) => f.contextId === dataSourceFieldId)?.fieldId;
                    case 'fixed_value':
                    case 'default':
                    default:
                        return undefined;
                }
            })
            .filter(Boolean);

        const whereCondition: FindOptionsWhere<TransactionFieldEntity>[] = [
            {
                transactionId: transaction.id,
                collectionItemId: transactionField.collectionItemId,
                rowKey: transactionField.rowKey,
            },
        ];

        if (filterFormTranFieldIds?.length) {
            whereCondition.push({
                fieldId: In(filterFormTranFieldIds),
                transactionId: transaction.id,
                contextType: TransactionFieldContextTypeEnum.FORM,
            });
        }

        const transactionFields = await this._transactionFieldRepo.find({
            where: whereCondition,
        });

        const filters: FilterOptionDto[] = [];
        const selectFields: string[] = [];

        extraConfiguration.forEach((item) => {
            let queryValue: string = undefined;
            let queryToOptionIds = false;

            switch (item.extraType) {
                case 'fixed_value':
                    queryValue = item.toFixedValue ?? '';
                    queryToOptionIds = SELECTABLE_FIELD_TYPES.includes(item.toFixedFieldType);
                    break;
                case 'context':
                    const fieldId = contextMappings.find((f) => f.contextId === item.dataSourceFieldId)?.fieldId;
                    const tranField = transactionFields.find((tf) => tf.fieldId === fieldId);
                    const isSelectable = SELECTABLE_FIELD_TYPES.includes(item.toContextFieldType);

                    // map value of select field base on labels
                    if (item.toContextFieldType === FormFieldTypeEnum.Select) {
                        queryValue = tranField?.fieldValue;
                        queryToOptionIds = false;
                        break;
                    }

                    queryValue = isSelectable ? (tranField?.fieldOptionIds || []).join(',') : (tranField?.fieldValue ?? '');
                    queryToOptionIds = isSelectable;
                    break;
                case 'default':
                default:
                    const collectionTranField = transactionFields
                        .filter((tf) => tf.collectionItemId === transactionField.collectionItemId)
                        .find((tf) => tf.fieldId === item.registerFieldId);
                    queryValue = collectionTranField?.fieldOptionIds?.length
                        ? (collectionTranField?.fieldOptionIds || []).join(',')
                        : (collectionTranField?.fieldValue ?? '');
                    queryToOptionIds = !!collectionTranField?.fieldOptionIds?.length;
                    break;
            }

            selectFields.push(item.dataSourceFieldId);

            const filter: FilterOptionDto = {
                field: item.dataSourceFieldId,
                operator: OperatorType.equals,
                value: queryValue,
                queryToOptionIds,
            };

            filters.push(filter);
        });

        const transactionIds = await this._formTransactionQueryBuilderService.getPaging({
            selectFields: selectFields,
            standardFilters: [
                {
                    field: 'formId',
                    operator: OperatorType.equals,
                    value: formId,
                },
            ],
            normalizedFilters: filters,
            skip: 0,
            take: 1,
            attributeSortFields: [],
            sorts: [
                {
                    field: 'createdAt',
                    order: OrderType.DESC,
                },
            ],
        });

        if (!transactionIds?.data?.length) {
            return null;
        }

        return {
            id: transactionIds.data[0],
            formId,
        };
    }

    public async triggerCollectionAutomationAction(request: TriggerCollectionAutomationActionRequestDto) {
        const { fieldId, rowKey, transactionId, browserTabId, actionId } = request;

        const transactionField = await this._transactionFieldRepo.findOne({
            where: {
                fieldId,
                rowKey,
                transactionId,
            },
            relations: {
                transaction: true,
            },
        });

        if (!transactionField) {
            throw new NotFoundException('transaction_field_not_found');
        }

        const transaction = transactionField.transaction;
        const formVersionId = transaction.formVersionId;

        const automationMapping = await this._automationMappingRepo.findOne({
            where: {
                actionId,
                formVersionId,
            },
        });

        if (!automationMapping?.automationId) {
            throw new NotFoundException('automation_mapping_not_found');
        }

        const { automationId } = automationMapping;

        const automationVersion = await this._automationVersionRepo.findOne({
            where: {
                automationId,
                contextVersionId: formVersionId,
                isEnable: true,
            },
        });

        if (!automationVersion) {
            throw new NotFoundException('automation_version_not_found');
        }

        const roleIds = this._claims.roles.map((r) => r.id);

        const stageRole = await this._stageRoleRepo.find({
            where: { stageId: transaction.stageId, formVersionId: formVersionId, roleId: In(roleIds) },
            relations: ['accessControls'],
        });

        const hasPermission = this.checkPermission(stageRole, [automationId]);

        if (!hasPermission) throw new ForbiddenException('no_permission');

        const message = EventDrivenService.createCommonEvent({
            payload: {
                ...transaction,
                automationVersionId: automationVersion.id,
                browserTabId: request.browserTabId,
            },
            tenantId: RequestContextService.accountId,
            aggregateId: request.transactionId,
            type: TransactionEventEnum.FORM_TRANSACTION_MANUAL,
            name: TransactionEventEnum.FORM_TRANSACTION_MANUAL,
        });
        await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_TOPIC, message, automationVersion.id);
    }

    private checkPermission(stageRoles: StageRoleTenancyEntity[], eventIds: string[]) {
        return stageRoles.some((role) => {
            return role?.accessControls?.some((ac) => ac.type === 'automation' && ac.config.enable && eventIds.includes(ac.targetId));
        });
    }
}
