import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';

import * as dayjs from 'dayjs';
import { compact, groupBy, isArray, isEmpty, isNil, partition, uniq } from 'lodash';
import { OverrideStatusEnum } from 'src/database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from 'src/database/src/shared/enums/override-type.enum';
import { StyleAndOverrideFields } from 'src/modules/data-register/dtos/requests/create-data-register-transaction.request';
import { Brackets, DataSource, FindOptionsWhere, In, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { EXTRA_CONFIGURATION_TYPES } from '../../../common/src/constant/auto-populate';
import {
    AP_TARGET_ITSELF_FIELD_ID,
    EXTERNAL_DATA_SOURCE_TYPE,
    EXTERNAL_DATA_SOURCE_TYPES,
    EXTERNAL_ORIGINAL_VERSION_ID,
} from '../../../common/src/constant/field';
import { DateFormat, DEFAULT_DATE_FORMAT } from '../../../constant/date';
import { OBJECT_SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { AutoPopulateBuilderTypeEnum } from '../../../database/src/shared/enums/ap-builder-type.enum';
import { DataRegisterTypeEnum } from '../../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { CaptureActiveFormVersionType } from '../../../database/src/shared/providers/capture-active-form-version.provider';
import { AutoPopulateDataLakeResponse } from '../../../shared/common/dto/autopopulate-datalake.dto';
import { GeneralAutoPopulateSettingDto } from '../../../shared/common/dto/general-auto-populate-setting.dto';
import { GeneralAutoPopulateService } from '../../../shared/services';
import { convertDurationToMinutes, convertHHmmToMinutes, getPriorityByDependentFields, PriorityType } from '../../../utils';
import { PopulateCollectionRequest } from '../dtos/requests/populate-collection.request';
import { FormCollectionTransactionFieldDataService } from './data/form-collection-transaction-field.service';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { FormatCollectionTransactionFieldService } from './data/format/collection-field.service';
import { isPopulateFieldStyle } from './data/util/check-populate-style';
import { FormCollectionDataService } from './form-collection.data.service';

@Injectable()
export class PopulateTransactionFieldService {
    constructor(
        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly formCollectionRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly formCollectionAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly _generalAutoPopulateSettingsRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _datasource: DataSource,

        @Inject(forwardRef(() => FormTransactionDataService))
        private readonly _dataService: FormTransactionDataService,
        private readonly _generalAutoPopulateService: GeneralAutoPopulateService,
        private readonly _collectionTransactionFieldService: FormCollectionTransactionFieldDataService,

        private readonly _collectionDataService: FormCollectionDataService,
    ) {}

    private TRANSACTION_FIELD_TABLE = 'data_register_transaction_fields';

    //This method should be called from backend and don't need send to frontend
    public populateTransactionFields = async (
        props: {
            formVersionFields: FormFieldTenancyEntity[];
            transactionFields: TransactionFieldEntity[];
        },
        transactionId: string,
        option?: {
            targetFieldChangeIds?: string[];
            payloadDocuments?: Record<string, string>;
        },
    ): Promise<TransactionFieldEntity[]> => {
        let { formVersionFields, transactionFields } = props;
        if (!formVersionFields.length) return transactionFields;
        const formVersionId = formVersionFields[0].formVersionId;

        let payloadDocuments = option?.payloadDocuments;

        if (!payloadDocuments) {
            const externalOriginVersionReportField = transactionFields.find((f) => f.fieldId === EXTERNAL_ORIGINAL_VERSION_ID);
            payloadDocuments = externalOriginVersionReportField?.data?.report;
        }

        const generalAutoPopulateSettings = await this._generalAutoPopulateSettingsRepo.find({
            where: {
                builderVersionId: formVersionId,
                builderType: AutoPopulateBuilderTypeEnum.FormField,
            },
            relations: {
                extraConfigurations: true,
            },
        });

        const groupedSettings = groupBy(generalAutoPopulateSettings, (s) => s.fieldId);

        const sortedFields = this.getPopulatePriority(formVersionFields);

        const formatTransactionFields: TransactionFieldEntity[] = transactionFields.map((item) => {
            return {
                ...item,
                fieldValue: item.fieldOptionIds?.length ? item.fieldOptionIds[0] : item.fieldValue,
            };
        });

        const dataLakeAutoPopulateValues = await this._generalAutoPopulateService.getDataLakeAutoPopulate({
            generalSettings: generalAutoPopulateSettings,
            transactionFields: formatTransactionFields || [],
            type: 'form',
            transactionId,
            payloadDocuments,
        });
        for (const field of sortedFields) {
            const populatedValueResult = await this._generalAutoPopulateService.getPopulatedValue({
                generalAutoPopulateSettings: groupedSettings[field.fieldId] as GeneralAutoPopulateSettingTenancyEntity[],
                transaction: {
                    transactionFields: formatTransactionFields,
                } as TransactionEntity,
                dataLakeValues: dataLakeAutoPopulateValues,
                fields: formVersionFields,
                isLabelingAsValue: true,
                targetFieldChangeIds: option?.targetFieldChangeIds,
                currentFieldType: field.type,
            });

            const { transactionField: populatedTransactionField } = populatedValueResult ?? {};

            if (!populatedTransactionField) continue;

            const check = await this._generalAutoPopulateService.checkPopulateNormalToLookup({
                field,
                transactionField: populatedTransactionField,
            });

            if (check.isContinue) {
                continue;
            }

            let transactionField = transactionFields.find((f) => f.fieldId === field.fieldId);
            if (!transactionField) {
                transactionField = new TransactionFieldEntity();
                transactionField.fieldId = field.fieldId;
                transactionField.contextType = TransactionFieldContextTypeEnum.FORM;
                transactionField.transactionId = formatTransactionFields[0].transactionId;

                if (field.type === FormFieldTypeEnum.Document) {
                    transactionField.data = {
                        docId: populatedTransactionField.fieldValue,
                        docFieldType: EXTERNAL_DATA_SOURCE_TYPES.includes(populatedTransactionField.dataSourceType)
                            ? 'external'
                            : 'internal',
                        dataSourceType: populatedTransactionField.dataSourceType,
                    };
                }

                transactionFields.push(transactionField);
            } else {
                if (field.type === FormFieldTypeEnum.Document) {
                    transactionField.data = {
                        ...(transactionField.data ?? {}),
                        docId: populatedTransactionField.fieldValue,
                        docFieldType: EXTERNAL_DATA_SOURCE_TYPES.includes(populatedTransactionField.dataSourceType)
                            ? 'external'
                            : 'internal',
                        dataSourceType: populatedTransactionField.dataSourceType,
                    };
                }
            }

            transactionField.fieldValue = populatedTransactionField.fieldValue;
            transactionField.fieldOptionIds = populatedTransactionField.fieldOptionIds;
        }
        return transactionFields;
    };

    private _format({
        formField,
        value,
        targetField,
    }: {
        formField?: {
            type: FormFieldTypeEnum;
            configuration: FormFieldTenancyEntity['configuration'];
        };
        value: string;
        targetField: DataRegisterTransactionFieldTenancyEntity;
    }) {
        if (!formField || !value) {
            return { value };
        }

        switch (formField.type) {
            case FormFieldTypeEnum.Select: {
                const sourceOptions: Array<{ label: string; value: string }> = formField.configuration?.options || [];
                const mode = formField.configuration?.mode || 'single';

                const rawValues = isArray(value) ? value : (value as string).split(',');
                const values = rawValues.filter((v) => !isNil(v)) as string[]; //is labels of options from target field

                //filter labels from options in target field that is existing in criteria field
                const finalOptions =
                    sourceOptions.filter((option) => values.some((v) => option.label?.toString()?.trim() === `${v}`.trim())) || [];

                const finalValues = finalOptions.map((fo) => fo.value);

                return {
                    value: mode === 'single' ? finalValues[0] : finalValues,
                    label: finalOptions?.length ? finalOptions.map((o) => o.label).join(', ') : value,
                    type: FormFieldTypeEnum.Select,
                };
            }
            case FormFieldTypeEnum.Lookup: {
                const mode = formField.configuration?.mode || 'single';

                const label = targetField?.fieldValue;
                if (!targetField?.fieldOptionIds?.length) {
                    return { value, label, type: FormFieldTypeEnum.Lookup };
                }
                return {
                    value: mode === 'single' ? targetField.fieldOptionIds[0] : targetField?.fieldOptionIds,
                    type: FormFieldTypeEnum.Lookup,
                    label,
                };
            }
            default:
                return { value };
        }
    }

    public populateCollectionFieldData = async (
        params: PopulateCollectionRequest,
    ): Promise<{
        result: {
            [formCollectionIdentityId: string]: {
                [formCollectionItemIdentityId: string]: {
                    [fieldId: string]: string;
                };
            };
        };
        registerRecordMap?: {
            [formCollectionIdentityId: string]: {
                [formCollectionItemIdentityId: string]: {
                    [fieldId: string]: string;
                };
            };
        };
        mappingFieldResult?: Record<string, { label: string; type: string; externalContextId?: string }>;
        runCollectionIds?: string[];
        styleAndOverrideFields: StyleAndOverrideFields;
    }> => {
        try {
            const {
                formValues,
                formVersionId,
                triggerField,
                triggerFields,
                cachedFormVersion,
                purpleTRACPopulatedData,
                targetFieldChangeIds,
                targetFieldChangeFromRegisterId,
                payloadDocuments,
                isTest,
            } = params;
            if (!formVersionId || isEmpty(formValues))
                return {
                    result: {},
                    styleAndOverrideFields: {},
                };

            const styleAndOverrideFields: StyleAndOverrideFields = {};

            const formVersion = cachedFormVersion || (await this._dataService.getFormByVersion({ formVersionId }));
            const formFields = formVersion?.fields;
            const fieldIds = Object.keys(formValues);

            const availableFields = formFields
                .filter((f) => {
                    return fieldIds.includes(f.fieldId) && !!formValues[f.fieldId];
                })
                .map((f) => ({
                    ...f,
                    value: formValues[f.fieldId],
                }));

            const formCollectionList = cachedFormVersion?.formCollections?.length
                ? cachedFormVersion.formCollections
                : await this.formCollectionRepo.find({
                      where: {
                          formVersionId: formVersion.id,
                          type: DataRegisterTypeEnum.Collection,
                      },
                      relations: ['formCollectionItems'],
                  });

            if (!formCollectionList.length)
                return {
                    result: {},
                    styleAndOverrideFields: {},
                };

            const result = formCollectionList.reduce((acc, formCollection) => {
                acc[formCollection.identityId] = {};
                return acc;
            }, {});
            const registerRecordMap: Record<string, any> = formCollectionList.reduce((acc, formCollection) => {
                acc[formCollection.identityId] = {};
                return acc;
            }, {});
            const mappingFieldResult: Record<string, any> = {};
            const runCollectionIds: string[] = [];

            for (const formCollection of formCollectionList) {
                const items = formCollection.formCollectionItems;

                const collectionItemIdentityIds: string[] = uniq(items.map((i) => i.identityId).filter(Boolean));
                if (!collectionItemIdentityIds.length) {
                    continue;
                }

                //#region new logic
                const generalAutoPopulateSettings: GeneralAutoPopulateSettingTenancyEntity[] = await this._getGeneralAutoPopulateSettings({
                    cachedFormVersion,
                    collectionItemIdentityIds,
                    isTest,
                    formVersionId,
                    formVersion,
                    items,
                    formCollection,
                });

                const triggerFieldList = uniq(compact([triggerField, ...(triggerFields || [])]));

                const dependencyAutoPopulateSettings = triggerFieldList?.length
                    ? generalAutoPopulateSettings.filter((setting) =>
                          (setting.extraConfigurations || []).some((ec) => triggerFieldList.includes(ec.toFieldId)),
                      )
                    : generalAutoPopulateSettings;

                if (!dependencyAutoPopulateSettings?.length) {
                    continue;
                }

                formCollection.identityId && runCollectionIds.push(formCollection.identityId);

                // Populate from data lake first
                const dataLakeAutoPopulateValues = await this._generalAutoPopulateService.getDataLakeAutoPopulate({
                    generalSettings: dependencyAutoPopulateSettings,
                    transactionFields: (availableFields || []).map((f) => ({
                        fieldId: f.fieldId,
                        fieldValue: f.value,
                        fieldType: f.type,
                    })),
                    type: 'form',
                    transactionId: params.transactionId,
                    payloadDocuments,
                });

                const additionalFields: FormCollectionAdditionalFieldTenancyEntity[] = await this._getCollectionAdditionalFields({
                    cachedFormVersion,
                    collectionItemIdentityIds,
                    isTest,
                    formVersionId,
                    formVersion,
                    items,
                });

                const lookupAdditionalFields = additionalFields.filter((f) => f.configuration?.type === FormFieldTypeEnum.Lookup);

                const lookupAdditionalFieldTargetIds = compact(uniq(lookupAdditionalFields.map((f) => f.configuration?.targetId)));

                dependencyAutoPopulateSettings.forEach((setting) => {
                    if (setting.targetFieldType === FormFieldTypeEnum.Definable) {
                        const additionalField = additionalFields.find(
                            (f) => f.fieldId === setting.fieldId && f.formCollectionItemIdentityId === setting.itemIdentityId,
                        );
                        if (additionalField) {
                            setting.targetFieldType = additionalField.configuration?.type;
                        }
                    }
                });

                const selectTargetFields = dependencyAutoPopulateSettings.filter((s) =>
                    OBJECT_SELECTABLE_FIELD_TYPES.includes(s.targetFieldType),
                );

                const selectCollectionItemIdentityIds = selectTargetFields.map((f) => f.itemIdentityId);

                const selectTypeResult = await this._collectionTransactionFieldService.getCollectionItemRegisters({
                    collectionItemIdentityIds: selectCollectionItemIdentityIds,
                    formVersionId,
                    registerIds: lookupAdditionalFieldTargetIds,
                });

                const groupedAutoPopulateSettingGroup = groupBy(dependencyAutoPopulateSettings, 'itemIdentityId');

                const externalContextMap = new Map();
                for (const identityId in groupedAutoPopulateSettingGroup) {
                    const groupedAutoPopulateSettings = groupedAutoPopulateSettingGroup[identityId] || [];

                    //group by field id
                    const groupedFieldAutoPopulateSettings = groupBy(groupedAutoPopulateSettings, 'fieldId');

                    for (const fieldId in groupedFieldAutoPopulateSettings) {
                        const fieldSettings = groupedFieldAutoPopulateSettings[fieldId];
                        //sort by priority
                        fieldSettings.sort((a, b) => a.priority - b.priority);

                        //run auto populate for each field setting
                        for (const fieldSetting of fieldSettings) {
                            if (!fieldSetting.dataSourceId) continue;

                            if (
                                targetFieldChangeIds?.length &&
                                !targetFieldChangeIds.includes(fieldSetting.targetFieldId) &&
                                targetFieldChangeFromRegisterId &&
                                targetFieldChangeFromRegisterId === fieldSetting?.dataSourceId
                            ) {
                                continue;
                            }

                            if (!result[formCollection.identityId]?.[identityId]) {
                                result[formCollection.identityId][identityId] = {};
                                registerRecordMap[formCollection.identityId][identityId] = {};
                            }

                            const additionalField = additionalFields.find(
                                (f) => f.fieldId === fieldSetting.fieldId && f.formCollectionItemIdentityId === fieldSetting.itemIdentityId,
                            );

                            const formField = FormatCollectionTransactionFieldService.getFormField({
                                fieldId: fieldSetting.fieldId,
                                collectionItemIdentityId: identityId,
                                registers: selectTypeResult.registers,
                                collectionItems: selectTypeResult.collectionItems,
                                registerTransactions: selectTypeResult.registerTransactions,
                                registerVersions: selectTypeResult.registerVersions,
                            });

                            const fieldConfig: {
                                type: FormFieldTypeEnum;
                                configuration: FormFieldTenancyEntity['configuration'];
                                lookupTargetId: string;
                            } = {
                                type:
                                    formField?.type && formField.type !== FormFieldTypeEnum.Definable
                                        ? formField?.type
                                        : additionalField?.type || additionalField.configuration?.additionalType,
                                configuration:
                                    formField?.type && formField.type !== FormFieldTypeEnum.Definable
                                        ? formField?.configuration
                                        : additionalField?.configuration,
                                lookupTargetId:
                                    formField?.lookupTargetId ||
                                    formField?.configuration?.targetId ||
                                    additionalField?.configuration?.targetId,
                            };

                            // Before running auto populate, check if the field is already populated by data lake
                            if (EXTERNAL_DATA_SOURCE_TYPES.includes(fieldSetting.dataSourceType)) {
                                if (formField || additionalField) {
                                    if (
                                        fieldSetting.dataSourceType === EXTERNAL_DATA_SOURCE_TYPE.PURPLETRAC &&
                                        purpleTRACPopulatedData?.length
                                    ) {
                                        /**Purple Track Populate */
                                        const { hasData } = await this._formatDataLakeValue({
                                            collectionIdentityId: formCollection.identityId,
                                            collectionItemIdentityId: identityId,
                                            dataLakeAutoPopulateValues: purpleTRACPopulatedData,
                                            fieldSetting,
                                            mappingFieldResult,
                                            result,
                                            formField: {
                                                ...fieldConfig,
                                            },
                                            externalContextMap,
                                            targetComparedFieldId: fieldSetting.targetComparedFieldId,
                                            targetComparedFieldType: fieldSetting.targetComparedFieldType,
                                        });
                                        if (!hasData) {
                                            continue;
                                        } else {
                                            break;
                                        }
                                    } else {
                                        /**Data Lake Populate */
                                        const { hasData } = await this._formatDataLakeValue({
                                            collectionIdentityId: formCollection.identityId,
                                            collectionItemIdentityId: identityId,
                                            dataLakeAutoPopulateValues,
                                            fieldSetting,
                                            mappingFieldResult,
                                            result,
                                            formField: {
                                                ...fieldConfig,
                                            },
                                            externalContextMap,
                                            targetComparedFieldId: fieldSetting.targetComparedFieldId,
                                            targetComparedFieldType: fieldSetting.targetComparedFieldType,
                                        });
                                        if (!hasData) {
                                            continue;
                                        } else {
                                            break;
                                        }
                                    }
                                }
                            }

                            /**Register Populate */
                            const data = await this.execute({
                                availableFields,
                                fieldSetting,
                                formFields,
                            });

                            if (!data) {
                                result[formCollection.identityId][identityId][fieldSetting.fieldId] = null;
                                registerRecordMap[formCollection.identityId][identityId][fieldSetting.fieldId] = null;
                                continue;
                            }

                            const fields = await this.dataRegisterTransactionFieldRepo.find({
                                where: {
                                    dataRegisterTransactionId: data.id,
                                },
                            });

                            const fieldKey = `${formCollection.identityId}_${identityId}_${fieldSetting.fieldId}`;

                            // #region get override and style for field
                            const isPopulateStyle = isPopulateFieldStyle({
                                includeValidationValue: fieldSetting.includeValidationValue,
                                type: fieldConfig.type,
                            });
                            // #endregion

                            if (isPopulateStyle) {
                                const tf = fields.find((f) => f.fieldId === fieldSetting.targetFieldId);
                                if (tf) {
                                    const transactionFieldEntity = await this.dataRegisterTransactionFieldRepo.findOne({
                                        where: {
                                            id: tf.id,
                                        },
                                        relations: {
                                            transactionFieldOverrides: true,
                                            transactionFieldStyle: true,
                                        },
                                    });

                                    const override = transactionFieldEntity.transactionFieldOverrides?.find(
                                        (o) => o.type === OverrideTypeEnum.User && o.status === OverrideStatusEnum.Active,
                                    );
                                    const style = transactionFieldEntity.transactionFieldStyle;

                                    styleAndOverrideFields[fieldKey] = {};
                                    if (override) {
                                        styleAndOverrideFields[fieldKey].override = {
                                            fieldId: fieldSetting.fieldId,
                                            dependencyValues: {
                                                [fieldSetting.fieldId]: transactionFieldEntity.fieldOptionIds?.length
                                                    ? transactionFieldEntity.fieldOptionIds
                                                    : transactionFieldEntity.fieldValue,
                                            },
                                            comment: override.comment,
                                            createdBy: override.createdBy,
                                            createdByUser: override.createdByUser,
                                            fromValue: override.fromValue,
                                            validationValue: override.validationValue,
                                            overrideValue: override.validationValue,
                                        };
                                    }

                                    if (style) {
                                        styleAndOverrideFields[fieldKey].style = {
                                            fieldId: fieldSetting.fieldId,
                                            style: style.configuration,
                                        };
                                    }
                                }
                            }

                            const targetFieldId = fieldSetting.targetFieldId;
                            if (targetFieldId === AP_TARGET_ITSELF_FIELD_ID) {
                                if (fields?.length) {
                                    result[formCollection.identityId][identityId][fieldSetting.fieldId] =
                                        fields[0]?.dataRegisterTransactionId ?? null;

                                    const drId = fieldConfig.configuration?.targetId;
                                    const drVersion = selectTypeResult.registerVersions.find((r) => r.dataRegisterId === drId);
                                    const displayAttributes = drVersion?.displayAttributes || [];

                                    const label = displayAttributes.reduce((acc, attr) => {
                                        const field = fields.find((f) => f.fieldId === attr);
                                        if (field) {
                                            acc.push(field.fieldValue);
                                        }
                                        return acc;
                                    }, []);

                                    mappingFieldResult[fieldKey] = {
                                        label: label.length ? label.join(' - ') : '',
                                        type: FormFieldTypeEnum.Lookup,
                                    };
                                }
                            } else {
                                const targetField = (fields || []).find((f) => f.fieldId === targetFieldId);
                                let targetValue = targetField?.fieldValue;

                                if (fieldSetting.targetComparedFieldId) {
                                    const targetComparedFieldId = fieldSetting.targetComparedFieldId;
                                    //LS-3367
                                    if (targetComparedFieldId && formField.type === FormFieldTypeEnum.Lookup) {
                                        const value = await this._generalAutoPopulateService.findRegisterRecordByComparedField({
                                            value: targetValue,
                                            comparedFieldId: targetComparedFieldId,
                                            registerId: formField.lookupTargetId,
                                            fieldId,
                                        });

                                        targetValue = value;
                                        targetField.fieldOptionIds = value ? value.split(',') : [];
                                    }
                                }

                                if (!result[formCollection.identityId]?.[identityId]) {
                                    result[formCollection.identityId][identityId] = {};
                                    registerRecordMap[formCollection.identityId][identityId] = {};
                                }

                                result[formCollection.identityId][identityId][fieldSetting.fieldId] = !targetValue ? null : targetValue;

                                registerRecordMap[formCollection.identityId][identityId][fieldSetting.fieldId] = data.id;

                                //format value for select, lookup fields
                                if (targetField?.fieldOptionIds?.length) {
                                    if (formField || additionalField) {
                                        //format value for select, lookup fields
                                        const {
                                            value: targetFieldValue,
                                            label,
                                            type,
                                        } = this._format({
                                            formField: {
                                                ...fieldConfig,
                                            },
                                            value: targetField?.fieldValue,
                                            targetField,
                                        });

                                        result[formCollection.identityId][identityId][fieldSetting.fieldId] = targetFieldValue;
                                        registerRecordMap[formCollection.identityId][identityId][fieldSetting.fieldId] = data.id;
                                        if (OBJECT_SELECTABLE_FIELD_TYPES.includes(type)) {
                                            mappingFieldResult[`${formCollection.identityId}_${identityId}_${fieldSetting.fieldId}`] = {
                                                label,
                                                type,
                                            };
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
                //#endregion
            }

            return { result, mappingFieldResult, runCollectionIds, registerRecordMap, styleAndOverrideFields };
        } catch (error) {
            throw new BadRequestException(error);
        }
    };

    public getPopulatePriority = (formVersionFields: FormFieldTenancyEntity[]): FormFieldTenancyEntity[] => {
        const populatedFields = formVersionFields.filter((f) => !!f.configuration?.autoPopulate?.general?.length);

        if (!populatedFields?.length) return [];
        let priorityList: PriorityType[] = [];
        populatedFields.forEach((field) => {
            const dependOnFieldIds: string[] = [];
            if (field.configuration?.autoPopulate?.general?.length) {
                const settings = field.configuration?.autoPopulate?.general as GeneralAutoPopulateSettingDto[];
                settings.forEach((setting) => {
                    const { extraConfigurations } = setting;
                    extraConfigurations?.forEach((config) => {
                        const { toFieldId } = config;
                        dependOnFieldIds.push(toFieldId);
                    });
                });
            }
            priorityList.push({ fieldId: field.fieldId, dependOnFieldIds });
        });

        const { sortedList } = getPriorityByDependentFields(priorityList);
        priorityList = sortedList;
        return priorityList.map((p) => p.fieldId).map((f) => formVersionFields.find((field) => field.fieldId === f));
    };

    private getEmptyDataByType = (type: FormFieldTypeEnum) => {
        switch (type) {
            case FormFieldTypeEnum.Checkbox:
                return false;
            default:
                return null;
        }
    };

    private _formatDataLakeValue = async ({
        result,
        dataLakeAutoPopulateValues = [],
        fieldSetting,
        mappingFieldResult,
        collectionItemIdentityId,
        collectionIdentityId,
        formField,
        externalContextMap,
    }: {
        dataLakeAutoPopulateValues: AutoPopulateDataLakeResponse[] | Partial<AutoPopulateDataLakeResponse>[];
        fieldSetting: {
            fieldId: string;
            targetFieldId: string;
            targetComparedFieldId?: string;
            isArray?: boolean;
        };
        result: Record<string, any>;
        mappingFieldResult: Record<string, any>;
        collectionItemIdentityId: string;
        collectionIdentityId: string;
        formField: {
            type: FormFieldTypeEnum;
            configuration: FormFieldTenancyEntity['configuration'];
            lookupTargetId: string;
        };
        externalContextMap: Map<string, string>;
        targetComparedFieldId?: string;
        targetComparedFieldType?: FormFieldTypeEnum;
    }) => {
        const isTargetFieldHasArrayType = !!fieldSetting.isArray;
        const fieldId = fieldSetting.fieldId;
        const targetFieldId = fieldSetting.targetFieldId;
        let dataLakeValue = dataLakeAutoPopulateValues.find((f) => f.fieldId === fieldId && f.ctxPropertyId === targetFieldId)?.value;

        const targetComparedFieldId = fieldSetting.targetComparedFieldId;

        //LS-3367
        if (targetComparedFieldId && formField.type === FormFieldTypeEnum.Lookup) {
            const externalSingleValue = Array.isArray(dataLakeValue) ? (dataLakeValue[0]?.value as string) : dataLakeValue;

            const value = await this._generalAutoPopulateService.findRegisterRecordByComparedField({
                value: externalSingleValue,
                comparedFieldId: targetComparedFieldId,
                registerId: formField.lookupTargetId,
                fieldId,
            });

            if (Array.isArray(dataLakeValue)) {
                (dataLakeValue || []).forEach((item) => (item.value = value));
            } else {
                dataLakeValue = value;
            }
        }

        if (dataLakeValue) {
            if (isTargetFieldHasArrayType || Array.isArray(dataLakeValue)) {
                //spawn collection items
                const spawnValues = (dataLakeValue || []) as Array<{ id: string; value: string }>;
                if (!externalContextMap.has(collectionItemIdentityId)) {
                    const id = v4();
                    externalContextMap.set(collectionItemIdentityId, id);
                }
                const externalContextId = externalContextMap.get(collectionItemIdentityId);

                spawnValues.forEach((item, index) => {
                    const { id, value } = item;
                    this._formatSinglePopulatedValue({
                        collectionIdentityId,
                        collectionItemIdentityId,
                        fieldId,
                        value,
                        formField,
                        mappingFieldResult,
                        result,
                        rowKey: id,
                        data: {
                            externalIdent: id,
                            externalContextId,
                            externalOrder: index + 1,
                        },
                    });
                });
            } else {
                this._formatSinglePopulatedValue({
                    collectionIdentityId,
                    collectionItemIdentityId,
                    fieldId,
                    value: dataLakeValue,
                    formField,
                    mappingFieldResult,
                    result,
                });
            }
        } else {
            result[collectionIdentityId][collectionItemIdentityId][fieldId] = this.getEmptyDataByType(formField?.type);
            return { hasData: false };
        }

        return { hasData: true };
    };

    private _formatSinglePopulatedValue = ({
        formField,
        fieldId,
        value,
        collectionIdentityId,
        collectionItemIdentityId,
        rowKey,
        result,
        mappingFieldResult,
        data,
    }: {
        formField?: {
            type: FormFieldTypeEnum;
            configuration: FormFieldTenancyEntity['configuration'];
        };
        fieldId: string;
        value: string;
        collectionIdentityId: string;
        collectionItemIdentityId: string;
        rowKey?: string;
        result: Record<string, any>;
        mappingFieldResult: Record<string, any>;
        data?: {
            externalIdent?: string;
            externalContextId?: string;
            externalOrder?: number;
        };
    }) => {
        let combineCollectionItemIdentityId = collectionItemIdentityId;
        if (rowKey) {
            combineCollectionItemIdentityId = `${collectionItemIdentityId}_${rowKey}`;
        }

        if (data?.externalOrder) {
            combineCollectionItemIdentityId = `${collectionItemIdentityId}_${rowKey}_${data.externalOrder}`;
        }

        //format value for select, lookup fields if need
        const {
            value: targetFieldValue,
            label,
            type,
        } = this._format({
            formField,
            value,
            targetField: {
                fieldId,
                fieldValue: value,
                fieldOptionIds: [],
            },
        });

        if (!result[collectionIdentityId][combineCollectionItemIdentityId]) {
            result[collectionIdentityId][combineCollectionItemIdentityId] = {};
        }

        result[collectionIdentityId][combineCollectionItemIdentityId][fieldId] = targetFieldValue;

        const keyFieldMapping = `${collectionIdentityId}_${combineCollectionItemIdentityId}_${fieldId}`;
        if (!mappingFieldResult[keyFieldMapping]) {
            mappingFieldResult[keyFieldMapping] = {};
        }

        if (data?.externalContextId) {
            mappingFieldResult[keyFieldMapping].externalContextId = data?.externalContextId;
        }

        if (OBJECT_SELECTABLE_FIELD_TYPES.includes(type)) {
            mappingFieldResult[keyFieldMapping].label = label;
            mappingFieldResult[keyFieldMapping].type = type;
        }
    };

    private _alias = (value: string, alias: string) => `${alias}_${value.split('-').join('_')}`;

    public async execute({
        availableFields,
        fieldSetting,
        formFields,
    }: {
        fieldSetting: GeneralAutoPopulateSettingTenancyEntity;
        availableFields: Array<{ fieldId: string; value: string }>;
        formFields: FormFieldTenancyEntity[];
    }) {
        const combineContexts: Array<{
            toFieldId: string;
            fromField: string;
            fieldValue: any;
            toFieldRegisterId?: string;
            toLookupFieldId?: string; //using when need compare !lookup from field = lookup to field -> !lookup to field
            toFixedValue?: string;
            extraType: string;
            toFromFieldType: string;
            toTransactionRegisterId: string;
        }> = (fieldSetting.extraConfigurations || []).map((config) => {
            const isToLookupField = !!config.toLookupFieldId;
            const availableField = availableFields.find((f) => f.fieldId === config.toFieldId);
            return {
                fromField: config.fromFieldId,
                toFieldId: isToLookupField ? config.toLookupFieldId : config.toFieldId,
                fieldValue: availableField?.value || config.toTransactionRegisterId,
                toFieldRegisterId: config.toFieldRegisterId,
                toLookupFieldId: config.toLookupFieldId,
                toFixedValue: config.fixedValue,
                toFromFieldType: config.formFieldType,
                extraType: config.extraType,
                toTransactionRegisterId: config.toTransactionRegisterId,
            };
        });

        const [fixedValues, nonFixedValuesContexts] = partition(
            combineContexts,
            (ctx) => ctx.extraType === EXTRA_CONFIGURATION_TYPES.FIXED_VALUE,
        );
        const [collectionFieldSubCtxs, subContexts] = partition(
            nonFixedValuesContexts,
            (ctx) => ctx.extraType === EXTRA_CONFIGURATION_TYPES.DEFAULT && !!ctx.toFixedValue,
        );

        let builder = this.dataRegisterTransactionRepository
            .createQueryBuilder('register_transaction')
            .select(['register_transaction.id', 'register_transaction.dataRegisterId'])
            .where('register_transaction.dataRegisterId = :dataRegisterId', {
                dataRegisterId: fieldSetting.dataSourceId,
            });

        const dataRegisterSubCtxs = subContexts.filter((ctx) => ctx.toLookupFieldId);

        //get transactions from registerIds
        if (dataRegisterSubCtxs?.length) {
            //get list field Value from data register transaction
            const drFieldValues = await this.dataRegisterTransactionFieldRepo
                .createQueryBuilder('rf')
                .andWhere(
                    new Brackets((qb) => {
                        dataRegisterSubCtxs.forEach((item, i) => {
                            const fieldValue = isArray(item.fieldValue) ? item.fieldValue[0] : item.fieldValue;
                            qb.orWhere(
                                new Brackets((qb1) => {
                                    qb1.andWhere(`rf.fieldId = :fieldId${i}`, {
                                        [`fieldId${i}`]: item.toFieldId,
                                    }).andWhere(`rf.data_register_transaction_id = :transactionId${i}`, {
                                        [`transactionId${i}`]: fieldValue,
                                    });
                                }),
                            );
                        });
                    }),
                )
                .getMany();

            const mappingValues = drFieldValues
                .map((drFieldValue) => ({
                    value: drFieldValue.fieldValue,
                    transactionId: drFieldValue.dataRegisterTransactionId,
                    fieldId: drFieldValue.fieldId,
                }))
                .reduce((prev, curr) => ({ ...prev, [`${curr.transactionId}_${curr.fieldId}`]: curr.value }), {});

            dataRegisterSubCtxs.forEach((item, index) => {
                const alias = this._alias(item.toFieldId, `f${index}`);

                builder
                    .innerJoin(this.TRANSACTION_FIELD_TABLE, alias, `${alias}.data_register_transaction_id = register_transaction.id`)
                    .andWhere(
                        new Brackets((subQb) => {
                            subQb.andWhere(`${alias}.field_id = :fieldId${index}`, {
                                [`fieldId${index}`]: item.fromField,
                            });

                            const value = mappingValues[`${item.fieldValue}_${item.toFieldId}`] ?? null;
                            if (value === null) {
                                subQb.andWhere(`${alias}.field_value is null`);
                            } else {
                                subQb.andWhere(`${alias}.field_value = :fieldValue${index}`, {
                                    [`fieldValue${index}`]: mappingValues[`${item.fieldValue}_${item.toFieldId}`],
                                });
                            }

                            return subQb;
                        }),
                    );
            });
        }

        const formFieldSubCtxs = subContexts.filter((ctx) => !ctx.toLookupFieldId);
        if (formFieldSubCtxs.length) {
            formFieldSubCtxs?.forEach((item, index: number) => {
                let findOperator = '';
                let findValue = '';
                let findColumn = '';

                if (item.toFieldRegisterId) {
                    findColumn = 'field_option_ids';
                    findOperator = `@> :formFieldValue${index}`;
                    findValue = !isNil(item.fieldValue) ? `["${item.fieldValue}"]` : null;
                } else if (item.toFromFieldType === FormFieldTypeEnum.Select) {
                    const formField = (formFields || []).find((f) => f.fieldId === item.toFieldId);
                    const formFieldOptions = (formField?.configuration?.options as Array<{ label: string; value: string }>) || [];
                    let searchValue = item.fieldValue;
                    const itemValues = Array.isArray(item.fieldValue)
                        ? item.fieldValue
                        : ((item.fieldValue as string) ?? '').split(',').map((i) => i.trim());

                    const valueMap = (itemValues || []).reduce((prev, v) => {
                        prev.set(v, true);
                        return prev;
                    }, new Map());

                    let labels = (formFieldOptions || []).filter((op) => valueMap.has(op.value));
                    if (!labels?.length) {
                        labels = (formFieldOptions || []).filter((op) => valueMap.has(op.label));
                    }

                    if (labels?.length) {
                        searchValue = (labels || []).map((item) => item.label ?? '').join(', ');
                    }

                    findColumn = 'field_value';
                    findOperator = `= :formFieldValue${index}`;
                    findValue = !isNil(searchValue) ? `${searchValue}` : null;
                } else {
                    findColumn = 'field_value';
                    findOperator = `= :formFieldValue${index}`;
                    findValue = !isNil(item.fieldValue) ? `${item.fieldValue}` : null;
                }

                const alias = this._alias(item.toFieldId, `ff${index}`);
                builder.innerJoin(
                    this.TRANSACTION_FIELD_TABLE,
                    alias,
                    `${alias}.data_register_transaction_id = register_transaction.id AND ${alias}.field_id = :formFieldId${index}`,
                    {
                        [`formFieldId${index}`]: item.fromField,
                    },
                );

                if (findValue === null) {
                    builder.andWhere(new Brackets((subQb) => subQb.andWhere(`${alias}.${findColumn} is null`)));
                } else {
                    builder.andWhere(
                        new Brackets((subQb) =>
                            subQb.andWhere(`${alias}.${findColumn} ${findOperator}`, {
                                [`formFieldValue${index}`]: findValue,
                            }),
                        ),
                    );
                }
            });
        }

        if (collectionFieldSubCtxs?.length) {
            const where: FindOptionsWhere<DataRegisterTransactionFieldTenancyEntity>[] = [];
            collectionFieldSubCtxs.forEach((item) => {
                if (item.toTransactionRegisterId && item.toFixedValue) {
                    where.push({
                        dataRegisterTransactionId: item.toTransactionRegisterId,
                        fieldId: item.toFixedValue,
                    });
                }
            });

            if (where.length) {
                const registerFields = await this.dataRegisterTransactionFieldRepo.find({
                    where,
                });
                collectionFieldSubCtxs.forEach((item, index) => {
                    const fieldValue = registerFields.find(
                        (rf) => rf.fieldId === item.toFixedValue && rf.dataRegisterTransactionId === item.toTransactionRegisterId,
                    );
                    item.fieldValue = fieldValue?.fieldValue;

                    let findOperator = '';
                    let findValue = '';
                    let findColumn = '';

                    findColumn = 'field_value';
                    findOperator = `= :collectionFieldValue${index}`;
                    findValue = !isNil(item.fieldValue) ? `${item.fieldValue}` : null;

                    const alias = this._alias(item.toFieldId, `cff${index}`);
                    builder.innerJoin(
                        this.TRANSACTION_FIELD_TABLE,
                        alias,
                        `${alias}.data_register_transaction_id = register_transaction.id AND ${alias}.field_id = :collectionFieldId${index}`,
                        {
                            [`collectionFieldId${index}`]: item.fromField,
                        },
                    );

                    if (findValue === null) {
                        builder.andWhere(new Brackets((subQb) => subQb.andWhere(`${alias}.${findColumn} is null`)));
                    } else {
                        builder.andWhere(
                            new Brackets((subQb) =>
                                subQb.andWhere(`${alias}.${findColumn} ${findOperator}`, {
                                    [`collectionFieldValue${index}`]: findValue,
                                }),
                            ),
                        );
                    }
                });
            }
        }

        if (fixedValues.length) {
            const registerId = fieldSetting.dataSourceId;
            const { activeVersionId } = await this._datasource.getRepository(DataRegisterTenancyEntity).findOne({
                where: { id: registerId },
                select: { activeVersionId: true, id: true },
            });

            if (activeVersionId) {
                const searchFieldIds =
                    fixedValues
                        ?.filter((item) => FormFieldTypeEnum.Select === item.toFromFieldType)
                        .map((f) => f.fromField)
                        .filter(Boolean) || [];

                const uniqFieldIds = Array.from(new Set(searchFieldIds));

                let builderFields: DataRegisterFieldTenancyEntity[] = [];
                if (uniqFieldIds?.length) {
                    builderFields = await this._datasource.getRepository(DataRegisterFieldTenancyEntity).find({
                        where: {
                            dataRegisterVersionId: activeVersionId,
                            fieldId: In(uniqFieldIds),
                        },
                        select: ['configuration', 'fieldId'],
                    });
                }

                fixedValues.forEach((item, index) => {
                    const alias = this._alias(item.fromField, `fixed${index}`);

                    let findOperator = '';
                    let findValue = '';
                    let findColumn = '';

                    switch (item.toFromFieldType) {
                        case FormFieldTypeEnum.Lookup: {
                            findColumn = 'field_option_ids';
                            findOperator = `@> :fixedValueOp${index}`;
                            findValue = !isNil(item.toFixedValue) ? `["${item.toFixedValue}"]` : null;
                            break;
                        }

                        case FormFieldTypeEnum.Select: {
                            const builderField = (builderFields || []).find((bf) => bf.fieldId === item.fromField);
                            const options = builderField?.configuration?.options || [];
                            let searchValue = item.toFixedValue;
                            const itemValues = Array.isArray(searchValue)
                                ? (searchValue as string[])
                                : ((searchValue as string) ?? '').split(',').map((i) => i.trim());

                            const valueMap = (itemValues || []).reduce((prev, v) => {
                                prev.set(v, true);
                                return prev;
                            }, new Map());

                            let labels = (options || []).filter((op) => valueMap.has(op.value));
                            if (!labels?.length) {
                                labels = (options || []).filter((op) => valueMap.has(op.label));
                            }

                            if (labels?.length) {
                                searchValue = (labels || []).map((item) => item.label ?? '').join(', ');
                            }

                            findColumn = 'field_value';
                            findOperator = `= :fixedValueOp${index}`;
                            findValue = !isNil(searchValue) ? `${searchValue}` : null;
                            break;
                        }

                        case FormFieldTypeEnum.DatePicker: {
                            findColumn = 'field_value';
                            findOperator = `= :fixedValueOp${index}`;
                            const searchValue = item.toFixedValue;
                            let date = searchValue ? dayjs(searchValue) : null;
                            let dateStr = null;
                            dateStr = date ? date.format(DEFAULT_DATE_FORMAT) : null;
                            findValue = dateStr;
                            break;
                        }

                        case FormFieldTypeEnum.DatetimePicker: {
                            findColumn = 'field_value';
                            findOperator = `:fixedValueOp${index}`;
                            const searchValue = item.toFixedValue;
                            let date = searchValue ? dayjs.utc(searchValue) : null;
                            let dateStr = null;
                            if (date) {
                                dateStr = date.format(DateFormat.YYYYMMDDTHHmm);
                            }

                            findValue = dateStr;
                            break;
                        }

                        case FormFieldTypeEnum.Duration: {
                            findColumn = 'field_value';
                            findOperator = `= :fixedValueOp${index}`;

                            const searchValue = item.toFixedValue;
                            let searchValueStr = Number(searchValue) ? searchValue : convertDurationToMinutes(searchValue);
                            const duration = searchValue ? searchValueStr : null;

                            findValue = !isNil(duration) ? `${duration}` : null;
                            break;
                        }

                        case FormFieldTypeEnum.TimePicker: {
                            findColumn = 'field_value';
                            findOperator = `= :fixedValueOp${index}`;

                            const searchValue = item.toFixedValue;
                            const minutes = searchValue ? convertHHmmToMinutes(searchValue) : null;

                            findValue = !isNil(minutes) ? `${minutes}` : null;
                            break;
                        }

                        case FormFieldTypeEnum.Checkbox: {
                            findColumn = 'field_value';
                            findOperator = `= :fixedValueOp${index}`;
                            break;
                        }

                        default: {
                            findColumn = 'field_value';
                            findOperator = `= :fixedValueOp${index}`;
                            findValue = !isNil(item.toFixedValue) ? `${item.toFixedValue}` : null;
                            break;
                        }
                    }

                    builder.innerJoin(
                        this.TRANSACTION_FIELD_TABLE,
                        alias,
                        `${alias}.data_register_transaction_id = register_transaction.id AND ${alias}.field_id = :fixedFieldId${index}`,
                        {
                            [`fixedFieldId${index}`]: item.fromField,
                        },
                    );

                    switch (item.toFromFieldType) {
                        case FormFieldTypeEnum.Checkbox: {
                            //format
                            const isChecked = item.toFixedValue === 'true';
                            if (isChecked) {
                                builder.andWhere(
                                    new Brackets((subQb) =>
                                        subQb.andWhere(`${alias}.${findColumn} ${findOperator}`, {
                                            [`fixedValueOp${index}`]: 'true',
                                        }),
                                    ),
                                );
                            } else {
                                builder.andWhere(
                                    new Brackets((subQb) =>
                                        subQb.andWhere(`${alias}.${findColumn} is null or ${alias}.${findColumn} ${findOperator}`, {
                                            [`fixedValueOp${index}`]: 'false',
                                        }),
                                    ),
                                );
                            }
                            break;
                        }

                        case FormFieldTypeEnum.DatetimePicker: {
                            if (findValue === null) {
                                builder.andWhere(new Brackets((subQb) => subQb.andWhere(`${alias}.${findColumn} is null`)));
                            } else {
                                builder.andWhere(new Brackets((subQb) => subQb.andWhere(`${alias}.${findColumn}  ilike '%${findValue}%'`)));
                            }
                            break;
                        }
                        default: {
                            if (findValue === null) {
                                builder.andWhere(new Brackets((subQb) => subQb.andWhere(`${alias}.${findColumn} is null`)));
                            } else {
                                builder.andWhere(
                                    new Brackets((subQb) =>
                                        subQb.andWhere(`${alias}.${findColumn} ${findOperator}`, {
                                            [`fixedValueOp${index}`]: findValue,
                                        }),
                                    ),
                                );
                            }
                            break;
                        }
                    }
                });
            }
        }

        const data = await builder.getOne();

        return data;
    }

    /**
     * Retrieves general auto-populate settings for collection items
     *
     * This method fetches auto-populate configuration settings for form collection items.
     * It handles both test and production scenarios, with caching support for improved performance.
     *
     * @param cachedFormVersion - Cached form version data containing pre-loaded settings
     * @param collectionItemIdentityIds - Array of collection item identity IDs to filter settings
     * @param isTest - Flag indicating if running in test mode
     * @param formVersionId - ID of the form version
     * @param formVersion - Form version entity containing form metadata
     * @param items - Array of form collection item entities
     * @param formCollection - Form collection entity containing collection metadata
     * @returns Promise resolving to array of general auto-populate setting entities
     */
    private async _getGeneralAutoPopulateSettings({
        cachedFormVersion,
        collectionItemIdentityIds,
        isTest,
        formVersionId,
        formVersion,
        items,
        formCollection,
    }: {
        cachedFormVersion: CaptureActiveFormVersionType;
        collectionItemIdentityIds: string[];
        isTest: boolean;
        formVersionId: string;
        formVersion: FormVersionTenancyEntity;
        items: FormCollectionItemTenancyEntity[];
        formCollection: FormCollectionTenancyEntity;
    }): Promise<GeneralAutoPopulateSettingTenancyEntity[]> {
        if (isTest) {
            return await this._collectionDataService.getTestCollectionAutoPopulateSettings({
                formVersionId,
                formId: formVersion.formId,
                formCollectionItems: items,
                collections: [formCollection],
            });
        }

        if (cachedFormVersion?.generalAutoPopulateSettings?.length) {
            return cachedFormVersion.generalAutoPopulateSettings.filter((i) => collectionItemIdentityIds.includes(i.itemIdentityId));
        }

        return await this._generalAutoPopulateSettingsRepo.find({
            where: {
                itemIdentityId: In(collectionItemIdentityIds),
                builderVersionId: formVersion.id,
            },
            relations: {
                extraConfigurations: true,
            },
        });
    }

    private async _getCollectionAdditionalFields({
        cachedFormVersion,
        collectionItemIdentityIds,
        isTest,
        formVersionId,
        formVersion,
        items,
    }: {
        cachedFormVersion: CaptureActiveFormVersionType;
        collectionItemIdentityIds: string[];
        isTest: boolean;
        formVersionId: string;
        formVersion: FormVersionTenancyEntity;
        items: FormCollectionItemTenancyEntity[];
    }) {
        const additionalFields: FormCollectionAdditionalFieldTenancyEntity[] = cachedFormVersion?.collectionAdditionalFields?.length
            ? cachedFormVersion?.collectionAdditionalFields.filter((i) =>
                  collectionItemIdentityIds.includes(i.formCollectionItemIdentityId),
              )
            : isTest
              ? await this._collectionDataService.getTestAdditionalFields({
                    formVersionId,
                    formCollectionItems: items,
                })
              : await this.formCollectionAdditionalFieldRepo.find({
                    where: {
                        formCollectionItemIdentityId: In(collectionItemIdentityIds),
                        formVersionId: formVersion.id,
                    },
                });

        return additionalFields;
    }
}
