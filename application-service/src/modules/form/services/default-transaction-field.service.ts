import { Inject, Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { In, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { CacheService, ClaimService, LoggerService, UtilsService } from '../../../common/src';
import { DEFAULT_STAGE_KPI_FIELD_ID, DEFAULT_TRANSACTION_FIELD_ID, USER_CLAIMS } from '../../../constant';
import { MMDDYYYY, TRANSPORT_DATE_TIME_FORMAT } from '../../../constant/date';
import { SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { CollectionTransactionTenancyEntity } from '../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormLayoutZoneFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-layout-zone-field.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../../database/src/entities/tenancy/form-layout.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormCollectionItemTypeEnum } from '../../../database/src/shared/enums/form-collection-item-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { DataRegisterWithActiveVersionService } from '../../../shared/services/data-register-with-active-version.service';
import { FieldConfiguration } from '../../../types';
import {
    convertDateTimeValueToDate,
    convertDateTimeValueToDateTime,
    convertDateTimeValueToString,
    convertHHmmToMinutes,
} from '../../../utils/format-date-time-value';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { FormatTransactionFieldService } from './data/format/transaction-field.service';
import { FormCollectionDataService } from './form-collection.data.service';

@Injectable()
export class DefaultTransactionFieldService {
    constructor(
        private readonly _formTransactionDataService: FormTransactionDataService,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly dataRegisterTransactionRepo: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly dataRegisterRepo: Repository<DataRegisterTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_TENANCY_REPOSITORY)
        private readonly dataRegisterVersionRepo: Repository<DataRegisterVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly dataRegisterFieldRepo: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTenancyRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemTenancyRepo: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.COLLECTION_TRANSACTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTransactionTenancyRepo: Repository<CollectionTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_LAYOUT_TENANCY_REPOSITORY)
        private readonly _formLayoutTenancyRepo: Repository<FormLayoutTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_LAYOUT_ZONE_FIELD_TENANCY_REPOSITORY)
        private readonly _formLayoutZoneFieldTenancyRepo: Repository<FormLayoutZoneFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _formCollectionAdditionalFieldRepo: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        private readonly _dataRegisterWithActiveVersionService: DataRegisterWithActiveVersionService,
        private readonly _loggerService: LoggerService,
        private readonly _formatTransactionFieldService: FormatTransactionFieldService,
        private readonly _cacheService: CacheService,
        private readonly _formCollectionDataService: FormCollectionDataService,
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,
    ) {}

    public getDefaultTransactionFields = async ({
        transaction,
        timezone,
        formFields,
        stages,
    }: {
        transaction: TransactionEntity;
        timezone: string;
        formFields: FormFieldTenancyEntity[];
        stages: StageTenancyEntity[];
    }): Promise<{ defaultTransactionFields: TransactionFieldEntity[]; transactionLookupFields: TransactionFieldEntity[] }> => {
        try {
            const fields = formFields || [];

            const [lookupFields, otherFields] = fields.reduce(
                (acc, field) => {
                    if (field.fieldId !== DEFAULT_TRANSACTION_FIELD_ID && field.fieldId !== DEFAULT_STAGE_KPI_FIELD_ID) {
                        field.type === FormFieldTypeEnum.Lookup ? acc?.[0]?.push(field) : acc?.[1]?.push(field);
                    }
                    return acc;
                },
                [[], []],
            );

            let defaultTransactionFields = otherFields?.map((item) =>
                this.getTransactionField({ field: item, transaction, timezone, stages: stages || [] }),
            );

            const defaultTranLookupFieldTasks = lookupFields.map((item) => this.getLookupTransactionFields(item, transaction));

            const defaultTransactionLookupFields = await Promise.all(defaultTranLookupFieldTasks);

            let transactionLookupFields = defaultTransactionLookupFields.flat(Infinity) as TransactionFieldEntity[];

            return {
                defaultTransactionFields,
                transactionLookupFields,
            };
        } catch (error) {
            console.log(error);
        }
    };

    public getDefaultCollectionFields = async ({
        transaction,
        timezone,
        stages,
        formVersionId,
        cachedCapturedAdditionalFields,
        cachedCollectionTransactions,
        option,
        isTest,
    }: {
        transaction: TransactionEntity;
        stages: StageTenancyEntity[];
        formVersionId: string;
        timezone?: string;
        cachedCapturedAdditionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
        cachedCollectionTransactions?: CollectionTransactionTenancyEntity[];
        option?: {
            collectionIdentityIds?: string[];
            collectionItemIdentityIds?: string[];
            collectionTypes?: FormCollectionItemTypeEnum[];
        };
        isTest?: boolean;
    }): Promise<{ collectionTransactionFields: TransactionFieldEntity[]; hideDefaultValuesCollectionIds: string[] }> => {
        let collectionTransactionFields: TransactionFieldEntity[] = [];

        const transactionId = transaction.id;

        const formCollectionBuilder = this._formCollectionTenancyRepo
            .createQueryBuilder('formCollection')
            .where('formCollection.formVersionId = :formVersionId', { formVersionId });

        if (option?.collectionIdentityIds?.length) {
            formCollectionBuilder.andWhere('formCollection.identityId IN (:...collectionIdentityIds)', {
                collectionIdentityIds: option?.collectionIdentityIds,
            });
        }

        let formCollections = await formCollectionBuilder
            .select([
                'formCollection.id',
                'formCollection.identityId',
                'formCollection.dataRegisterVersionId',
                'formCollection.displaySetting',
                'formCollection.dataRegisterId',
                'formCollection.dataRegisterVersionId',
            ])
            .getMany();

        if (!formCollections?.length)
            return {
                collectionTransactionFields: [],
                hideDefaultValuesCollectionIds: [],
            };

        if (isTest) {
            // sync active register version to form collection if in test mode
            formCollections = await this.syncDrVersionToFormCollection({
                collections: formCollections,
                repositories: {
                    dataRegisterRepo: this.dataRegisterRepo,
                    dataRegisterVersionRepo: this.dataRegisterVersionRepo,
                },
            });
        }

        const hideDefaultValuesCollectionIds: string[] = formCollections
            .filter((fc) => fc.displaySetting?.hideDefaultCollectionValuesWhenEmpty)
            .map((fc) => fc.identityId);

        let dataRegisterVersionIds = _.uniq(formCollections.map((collection) => collection.dataRegisterVersionId));
        const cacheDataRegisters = formCollections.map((collection) => {
            const { drVersionKey } =
                UtilsService.getActiveDrVersionCacheKeys({
                    accountId: this._claims.accountId,
                    drId: collection.dataRegisterId,
                    drVersionId: collection.dataRegisterVersionId,
                }) ?? {};
            return drVersionKey ? this._cacheService.get(drVersionKey) : Promise.resolve(null);
        });

        const dataRegisterVersions = [];
        const cacheDataRegisterEntities = ((await Promise.all(cacheDataRegisters)) as DataRegisterVersionTenancyEntity[]) ?? [];

        if (cacheDataRegisterEntities?.length) {
            const cacheDataRegisterVersionIds: string[] = [];
            for (const dr of cacheDataRegisterEntities) {
                if (!dr?.id) {
                    continue;
                }
                dataRegisterVersions.push(dr);
                cacheDataRegisterVersionIds.push(dr?.id);
            }
            dataRegisterVersionIds = dataRegisterVersionIds.filter((id) => !cacheDataRegisterVersionIds.includes(id));
        }
        if (dataRegisterVersionIds?.length) {
            const dataRegisterVersionEntities = await this.dataRegisterVersionRepo.find({
                where: {
                    id: In(dataRegisterVersionIds),
                },
                relations: ['fields'],
            });

            if (dataRegisterVersionEntities?.length) {
                dataRegisterVersions.push(...dataRegisterVersionEntities);
            }
        }

        const formCollectionItemBuilder = this._formCollectionItemTenancyRepo
            .createQueryBuilder('formCollectionItem')
            .where('formCollectionItem.formCollectionId IN (:...formCollectionIds)', {
                formCollectionIds: _.uniq(formCollections.map((collection) => collection.id)),
            });

        if (option?.collectionItemIdentityIds?.length) {
            formCollectionItemBuilder.andWhere('formCollectionItem.identityId IN (:...collectionItemIdentityIds)', {
                collectionItemIdentityIds: option?.collectionItemIdentityIds,
            });
        }

        const _formCollectionItems: FormCollectionItemTenancyEntity[] = await formCollectionItemBuilder
            .select(
                ['id', 'identityId', 'parentId', 'formCollectionId', 'name', 'setting', 'type', 'dataRegisterTransactionId'].map(
                    (item) => `formCollectionItem.${item}`,
                ),
            )
            .orderBy('formCollectionItem.parentId', 'ASC', 'NULLS FIRST')
            .getMany();

        if (!_formCollectionItems.length)
            return {
                collectionTransactionFields: [],
                hideDefaultValuesCollectionIds: [],
            };

        const formCollectionItems: FormCollectionItemTenancyEntity[] = [];

        let formCollectionOptionTypes = [FormCollectionItemTypeEnum.DEFAULT];

        if (option?.collectionTypes?.length) {
            formCollectionOptionTypes = option?.collectionTypes;
        }

        _formCollectionItems.forEach((item) => {
            if (formCollectionOptionTypes.includes(item.type)) {
                formCollectionItems.push(item);
            }

            if (item.parentId) {
                const parent = _formCollectionItems.find((_item) => _item.id === item.parentId || _item.identityId === item.parentId);

                if (formCollectionOptionTypes.includes(parent.type)) {
                    formCollectionItems.push(item);
                }
            }
        });

        let collectionTransactions: CollectionTransactionTenancyEntity[] = [];
        let capturedAdditionalFields: FormCollectionAdditionalFieldTenancyEntity[] = [];

        if (isTest) {
            const [testCollectionTransactions, testAdditionalFields] = await Promise.all([
                this._formCollectionDataService.getTestCollectionTransaction({
                    formCollectionItemIds: _.uniq(formCollectionItems.map((item) => item.id)),
                    formCollectionItemEntities: formCollectionItems,
                    formVersionId,
                }),

                this._formCollectionDataService.getTestAdditionalFields({
                    formVersionId,
                    formCollectionItems,
                }),
            ]);

            collectionTransactions = testCollectionTransactions;
            capturedAdditionalFields = testAdditionalFields;
        } else {
            collectionTransactions = cachedCollectionTransactions?.length
                ? cachedCollectionTransactions
                : await this._formCollectionTransactionTenancyRepo.find({
                      where: {
                          formCollectionItemId: In(_.uniq(formCollectionItems.map((item) => item.id))),
                          dataRegisterTransactionId: In(
                              _.uniq(_.compact(formCollectionItems.map((item) => item.dataRegisterTransactionId))),
                          ),
                      },
                  });

            capturedAdditionalFields = cachedCapturedAdditionalFields?.length
                ? cachedCapturedAdditionalFields
                : await this._formCollectionAdditionalFieldRepo.find({
                      where: {
                          formVersionId: formVersionId,
                          formCollectionItemIdentityId: In(_.uniq(formCollectionItems.map((item) => item.identityId))),
                      },
                  });
        }

        let additionalDataRegisters: DataRegisterTenancyEntity[] = [];

        const lookupAdditionalFields = capturedAdditionalFields?.filter(
            (field) => field.type === FormFieldTypeEnum.Lookup || field?.configuration?.type === FormFieldTypeEnum.Lookup,
        );

        if (lookupAdditionalFields?.length) {
            const dataRegisterIds = _.uniq(
                _.compact(lookupAdditionalFields.map((field) => field?.configuration?.lookupTargetId ?? field?.configuration?.targetId)),
            );

            const drs = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                dataRegisterIds,
                dataRegisterRepository: this.dataRegisterRepo,
                dataRegisterVersionRepository: this.dataRegisterVersionRepo,
                dataRegisterFieldRepository: this.dataRegisterFieldRepo,
            });

            additionalDataRegisters = drs?.filter((dr) => dr?.dataRegisterVersions?.length);
        }

        const transactionGroup = _.groupBy(collectionTransactions, 'formCollectionItemId');

        const capturedAdditionalFieldsGroup = _.groupBy(capturedAdditionalFields, 'formCollectionItemIdentityId');

        const calculationFields: FormCollectionItemTenancyEntity[] = [];

        try {
            for (const formCollectionItem of formCollectionItems) {
                const formCollection = formCollections.find((collection) => collection.id === formCollectionItem?.formCollectionId);

                const drFields = dataRegisterVersions.find((dr) => dr.id === formCollection?.dataRegisterVersionId)?.fields ?? [];

                const drFieldIdSet = new Set(drFields.map((f) => f.fieldId));

                const values = transactionGroup[formCollectionItem.id];

                const activeValues = values?.filter((v) => drFieldIdSet.has(v.fieldId));

                const capturedAdditionalFieldValues = capturedAdditionalFieldsGroup[formCollectionItem.identityId];

                if (formCollectionItem.setting?.calculation?.variableMappings?.length) {
                    calculationFields.push(_.cloneDeep(formCollectionItem));
                }

                if (activeValues?.length) {
                    for (const field of activeValues) {
                        let parentId = formCollectionItem.parentId;

                        if (parentId) {
                            const parent = collectionTransactionFields.find((field) => field.rowKey === formCollectionItem?.parentId);
                            parentId = parent?.id;
                        }

                        let value = field.fieldValue;
                        const fieldBuilder = drFields.find((f) => f.fieldId === field.fieldId);
                        const fieldType = fieldBuilder?.type;

                        const collectionTransactionField: TransactionFieldEntity = {
                            fieldId: field.fieldId,
                            id: v4(),
                            fieldValue: field.fieldValue,
                            fieldOptionIds: field.fieldOptionIds?.length ? _.compact(field.fieldOptionIds) : [],
                            transactionId: transactionId,
                            collectionId: formCollection.identityId,
                            collectionItemId: formCollectionItem.identityId,
                            parentId: parentId,
                            fieldType,
                            contextType: TransactionFieldContextTypeEnum.COLLECTION,
                            rowKey: formCollectionItem.id,
                        };

                        if (fieldBuilder?.configuration?.defaultValue || fieldBuilder?.configuration?.pickerType) {
                            const _field = this.getTransactionField({
                                field: {
                                    type: fieldType,
                                    configuration: fieldBuilder.configuration,
                                } as any,
                                transaction,
                                timezone,
                                stages: stages || [],
                            });

                            if (_field.fieldValue) {
                                collectionTransactionField.fieldValue = _field.fieldValue;
                                collectionTransactionField.fieldOptionIds = _field.fieldOptionIds;

                                if (SELECTABLE_FIELD_TYPES.includes(fieldType)) {
                                    collectionTransactionField.data = {
                                        defaultValue: _field.fieldOptionIds?.join(',') ?? '',
                                        defaultOptionIds: _field.fieldOptionIds,
                                        defaultFieldType: fieldType,
                                    };
                                } else {
                                    collectionTransactionField.data = {
                                        defaultValue: _field.fieldValue,
                                        defaultOptionIds: _field.fieldOptionIds,
                                        defaultFieldType: fieldType,
                                    };
                                }
                            }
                        }

                        // hot fix
                        const isExist = collectionTransactionFields.find(
                            (f) => f.fieldId === collectionTransactionField.fieldId && f.rowKey === collectionTransactionField.rowKey,
                        );

                        if (!isExist) {
                            collectionTransactionFields.push(collectionTransactionField);
                        }
                    }
                } else {
                    collectionTransactionFields.push({
                        id: v4(),
                        fieldId: formCollectionItem.identityId,
                        fieldValue: formCollectionItem.name,
                        fieldOptionIds: [],
                        transactionId: transactionId,
                        collectionId: formCollection.identityId,
                        collectionItemId: formCollectionItem?.identityId,
                        parentId: formCollectionItem?.parentId,
                        contextType: TransactionFieldContextTypeEnum.COLLECTION,
                        rowKey: formCollectionItem?.id,
                    });
                }

                if (capturedAdditionalFieldValues?.length) {
                    for (const field of capturedAdditionalFieldValues) {
                        const parentField = formCollectionItem.parentId
                            ? collectionTransactionFields.find((f) => f.rowKey === formCollectionItem.parentId)
                            : null;

                        const baseTransactionField: TransactionFieldEntity = {
                            id: v4(),
                            fieldId: field.fieldId,
                            fieldValue: field.configuration?.defaultValue ?? null,
                            fieldOptionIds: SELECTABLE_FIELD_TYPES.includes(field.type)
                                ? (field.configuration?.defaultValue ? field.configuration?.defaultValue : '')
                                      .split(',')
                                      .map((option) => option.trim())
                                : [],
                            fieldType: field.type,
                            transactionId,
                            collectionId: formCollection.identityId,
                            collectionItemId: formCollectionItem.identityId,
                            parentId: parentField?.id ?? null,
                            contextType: TransactionFieldContextTypeEnum.COLLECTION,
                            rowKey: formCollectionItem.id,
                        };

                        const fieldType = field.type === FormFieldTypeEnum.Definable ? field?.configuration?.type : field.type;

                        // Handle field value based on type
                        if (fieldType === FormFieldTypeEnum.Lookup) {
                            const lookupField = await this.getLookupCollectionTransactionFields(
                                field,
                                transactionId,
                                additionalDataRegisters,
                            );
                            if (lookupField) {
                                baseTransactionField.fieldValue = lookupField.fieldValue;
                                baseTransactionField.fieldOptionIds = lookupField.fieldOptionIds;
                                baseTransactionField.data = {
                                    defaultValue: (lookupField.fieldOptionIds || []).join(','),
                                    defaultOptionIds: lookupField.fieldOptionIds,
                                    defaultFieldType: fieldType,
                                };
                            }
                        } else if (fieldType === FormFieldTypeEnum.Select) {
                            const { options, mode, defaultValue } = field.configuration;
                            if (defaultValue && options?.length) {
                                const defaultOption = options.find((option) => option.value === defaultValue);
                                if (mode === 'single') {
                                    baseTransactionField.fieldValue = defaultOption?.label ?? '';
                                    baseTransactionField.fieldOptionIds = defaultOption ? [defaultValue] : [];
                                } else if (mode === 'multiple') {
                                    const defaultOptionIds = Array.isArray(defaultValue) ? defaultValue : defaultValue.split(',');
                                    const defaultOptionLabels = options
                                        .filter((o) => (defaultOptionIds || []).includes(o.value))
                                        .map((o) => o.label);
                                    baseTransactionField.fieldValue = defaultOptionLabels?.length ? defaultOptionLabels.join(',') : '';
                                    baseTransactionField.fieldOptionIds = defaultOptionLabels?.length ? defaultOptionIds : [];
                                }
                                baseTransactionField.data = {
                                    defaultValue: (baseTransactionField.fieldOptionIds || []).join(','),
                                    defaultOptionIds: baseTransactionField.fieldOptionIds,
                                    defaultFieldType: fieldType,
                                };
                            }
                        } else {
                            baseTransactionField.fieldValue =
                                this.getTransactionField({
                                    field: {
                                        type: fieldType,
                                        configuration: field.configuration,
                                    } as any,
                                    transaction,
                                    timezone,
                                    stages: stages || [],
                                })?.fieldValue ?? null;

                            baseTransactionField.data = {
                                defaultValue: baseTransactionField.fieldValue,
                                defaultFieldType: fieldType,
                            };
                        }

                        const existingIndex = collectionTransactionFields.findIndex(
                            (f) => f.fieldId === field.fieldId && f.collectionItemId === field.formCollectionItemIdentityId,
                        );

                        if (existingIndex !== -1) {
                            const existed = collectionTransactionFields[existingIndex];
                            existed.fieldValue = baseTransactionField.fieldValue;
                            existed.fieldOptionIds = baseTransactionField.fieldOptionIds;
                            continue;
                        } else {
                            collectionTransactionFields.push(baseTransactionField);
                        }
                    }
                }
            }

            if (calculationFields?.length) {
                calculationFields.forEach((calculationField) => {
                    const variableMapping = calculationField.setting.calculation.variableMappings;
                    const values = collectionTransactionFields?.filter((field) => field.collectionItemId === calculationField.identityId);

                    const _collectionTransactionFields = collectionTransactionFields?.filter(
                        (field) => field.collectionItemId === variableMapping?.[0]?.formCollectionItemId,
                    );

                    let pairId = _collectionTransactionFields?.[0]?.pairId ?? v4();

                    calculationField.setting.calculation.variableMappings.forEach((variable) => {
                        const variableField = variable.field as unknown as FormFieldTenancyEntity;

                        _collectionTransactionFields.push({
                            ..._collectionTransactionFields?.[0],
                            id: v4(),
                            fieldId: variableField.fieldId,
                            fieldValue: '',
                            fieldOptionIds: [],
                            pairId: pairId,
                        });
                    });

                    if (!_collectionTransactionFields?.[0]?.pairId) {
                        _collectionTransactionFields.forEach((field) => (field.pairId = pairId));
                    }

                    if (!values?.[0]?.pairId) {
                        values.forEach((field) => (field.pairId = pairId));
                    }

                    collectionTransactionFields = [...values, ..._collectionTransactionFields, ...collectionTransactionFields];
                });
            }

            const uniqueCollectionTransactionFields = _.uniqBy(collectionTransactionFields, 'id');
            return { collectionTransactionFields: uniqueCollectionTransactionFields, hideDefaultValuesCollectionIds };
        } catch (err) {
            this._loggerService.error(err);
        }
    };

    private getDataRegisterTransactionByIds = async (ids: string[]): Promise<DataRegisterTransactionTenancyEntity[]> => {
        return await this.dataRegisterTransactionRepo.find({
            where: {
                id: In(ids),
            },
            relations: ['transactionFields'],
        });
    };

    private getTransactionField = ({
        field,
        transaction,
        timezone,
        stages,
    }: {
        field: FormFieldTenancyEntity;
        transaction: TransactionEntity;
        timezone: string;
        stages: StageTenancyEntity[];
    }) => {
        let transactionField = new TransactionFieldEntity();
        transactionField.transactionId = transaction.id;
        transactionField.fieldId = field.fieldId;
        transactionField.contextType = TransactionFieldContextTypeEnum.FORM;

        const { type, configuration } = field;

        switch (type) {
            case FormFieldTypeEnum.Select: {
                const { options, mode, defaultValue } = configuration;
                if (mode === 'single') {
                    transactionField.fieldValue = options?.find((option) => option.value === defaultValue)?.label;
                    transactionField.fieldOptionIds = [defaultValue];
                    break;
                } else if (mode === 'multiple') {
                    transactionField.fieldValue = options
                        ?.filter((option) => defaultValue?.some((v) => v === option.value))
                        ?.map((o) => o.label)
                        .join(',');
                    transactionField.fieldOptionIds = defaultValue;
                    break;
                }
            }

            case FormFieldTypeEnum.DatePicker: {
                const { defaultValue, pickerType } = configuration;

                const value = convertDateTimeValueToDate({ pickerType, value: defaultValue, timezone });

                if (value) transactionField.fieldValue = value.format(MMDDYYYY);
                else transactionField.fieldValue = undefined;
                break;
            }

            case FormFieldTypeEnum.DatetimePicker: {
                const { defaultValue, pickerType } = configuration;

                const value = convertDateTimeValueToDateTime({ pickerType, value: defaultValue, timezone });

                if (value) transactionField.fieldValue = value.format(TRANSPORT_DATE_TIME_FORMAT);
                else transactionField.fieldValue = undefined;
                break;
            }

            case FormFieldTypeEnum.TimePicker: {
                const { defaultValue, pickerType } = configuration;
                const value = convertDateTimeValueToString({ pickerType, value: defaultValue, timezone });
                if (_.isEmpty(value)) break;

                const minutes = convertHHmmToMinutes(value.toString()).toString();
                transactionField.fieldValue = minutes;
                break;
            }
            case FormFieldTypeEnum.RoleLookup: {
                const { defaultRoles } = (configuration ?? {}) as FieldConfiguration;

                const stage = stages?.find((s) => s.config.type === 'START' && defaultRoles?.some((r) => r.stageId === s.identityId));
                if (!stage) break;

                const role = defaultRoles.find((r) => r.stageId === stage.identityId);
                if (!role) break;

                transactionField.fieldOptionIds = [role.roleId];
                transactionField.fieldValue = role.roleName;
                break;
            }
            case FormFieldTypeEnum.UserLookup: {
                const { defaultByCurrentUser } = configuration;
                if (defaultByCurrentUser) {
                    transactionField.fieldValue = this._claims.userFullName;
                    transactionField.fieldOptionIds = this._claims.userId ? [this._claims.userId] : [];
                }

                break;
            }
            case FormFieldTypeEnum.Lookup: {
                const { defaultValue } = configuration;
                if (defaultValue) {
                    transactionField.fieldOptionIds = [defaultValue];
                    transactionField.fieldValue = defaultValue;
                }
                break;
            }

            default:
                transactionField.fieldValue = configuration.defaultValue?.toString();
                break;
        }

        return transactionField;
    };

    private async getLookupTransactionFields(
        field: FormFieldTenancyEntity,
        transaction: TransactionEntity,
    ): Promise<TransactionFieldEntity[]> {
        const { configuration } = field;
        const { mode, defaultValue } = configuration;

        // if (_.isEmpty(defaultValue)) return [];

        const dataRegisterTransactions = await this.getDataRegisterTransactionByIds(
            _.isArray(defaultValue) ? defaultValue : defaultValue ? [defaultValue] : [],
        );
        const dataRegisters = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
            dataRegisterIds: [field.lookupTargetId],
            dataRegisterRepository: this.dataRegisterRepo,
            dataRegisterVersionRepository: this.dataRegisterVersionRepo,
            dataRegisterFieldRepository: this.dataRegisterFieldRepo,
        });

        if (!dataRegisters?.length || !dataRegisters[0]?.dataRegisterVersions?.length) return [];
        const dataRegister = dataRegisters[0];

        const transactionFields = this._formatTransactionFieldService.captureDataRegisterFieldsForLookup({
            selectMode: mode,
            lookupFieldId: field.fieldId,
            dataRegisterTransactions,
            transactionId: transaction.id,
            fieldValue: defaultValue,
        });

        // Handle transaction lookup field label values
        let transactionLookupField = new TransactionFieldEntity();
        transactionLookupField.transactionId = transaction.id;

        if (mode === 'single') {
            transactionLookupField.fieldOptionIds = defaultValue ? [defaultValue] : [];
            transactionLookupField.fieldValue = defaultValue;
        } else if (mode === 'multiple') {
            transactionLookupField.fieldOptionIds = typeof defaultValue === 'string' ? defaultValue.split(',') : defaultValue;
            transactionLookupField.fieldValue = typeof defaultValue === 'string' ? defaultValue : defaultValue?.join(', ');
        }

        transactionLookupField.contextType = TransactionFieldContextTypeEnum.FORM;

        const { fieldValue, fieldOptionIds, displayAttributeFieldIds } = this._formatTransactionFieldService.formatLookupValue({
            formField: field,
            transactionField: transactionLookupField,
            dataRegisterVersions: dataRegister.dataRegisterVersions,
            dataRegisterTransactions: dataRegisterTransactions || [],
            transactionFields,
        });

        transactionLookupField.fieldId = field.fieldId;
        transactionLookupField.fieldValue = fieldValue;
        transactionLookupField.fieldOptionIds = fieldOptionIds;
        transactionLookupField.displayAttributeFieldIds = displayAttributeFieldIds;

        return [...transactionFields, transactionLookupField];
    }

    private async getLookupCollectionTransactionFields(
        field: FormFieldTenancyEntity | FormCollectionAdditionalFieldTenancyEntity,
        transactionId: string,
        dataRegisters: DataRegisterTenancyEntity[],
    ): Promise<TransactionFieldEntity> {
        const { configuration } = field;
        const { mode, defaultValue, lookupTargetId, targetId } = configuration;
        const registerId = lookupTargetId || targetId;

        if (_.isEmpty(defaultValue)) return null;

        const dataRegisterTransactions = await this.getDataRegisterTransactionByIds(
            _.isArray(defaultValue) ? defaultValue : [defaultValue],
        );

        const dataRegister = dataRegisters.find((dr) => dr.id === registerId);

        if (!dataRegister) return null;

        const transactionFields = this._formatTransactionFieldService.captureDataRegisterFieldsForLookup({
            selectMode: mode,
            lookupFieldId: field.fieldId,
            dataRegisterTransactions,
            transactionId: transactionId,
            fieldValue: defaultValue,
        });

        // Handle transaction lookup field label values
        let transactionLookupField = new TransactionFieldEntity();
        transactionLookupField.transactionId = transactionId;

        if (mode === 'single') {
            transactionLookupField.fieldOptionIds = [defaultValue];
            transactionLookupField.fieldValue = defaultValue;
        } else if (mode === 'multiple') {
            transactionLookupField.fieldOptionIds = typeof defaultValue === 'string' ? defaultValue.split(',') : defaultValue;
            transactionLookupField.fieldValue = typeof defaultValue === 'string' ? defaultValue : defaultValue.join(', ');
        }

        transactionLookupField.contextType = TransactionFieldContextTypeEnum.COLLECTION;

        const { fieldValue, fieldOptionIds, displayAttributeFieldIds } = this._formatTransactionFieldService.formatLookupValue({
            formField: field,
            transactionField: transactionLookupField,
            dataRegisterVersions: dataRegister.dataRegisterVersions,
            dataRegisterTransactions: dataRegisterTransactions || [],
            transactionFields,
        });

        transactionLookupField.fieldId = field.fieldId;
        transactionLookupField.fieldValue = fieldValue;
        transactionLookupField.fieldOptionIds = fieldOptionIds;
        transactionLookupField.displayAttributeFieldIds = displayAttributeFieldIds;

        return transactionLookupField ? transactionLookupField : null;
    }

    private async syncDrVersionToFormCollection({
        collections,
        repositories,
    }: {
        collections: FormCollectionTenancyEntity[];
        repositories: {
            dataRegisterRepo: Repository<DataRegisterTenancyEntity>;
            dataRegisterVersionRepo: Repository<DataRegisterVersionTenancyEntity>;
        };
    }): Promise<FormCollectionTenancyEntity[]> {
        const dataRegisterIds = collections.map((collection) => collection.dataRegisterId);

        const dataRegisters = dataRegisterIds?.length
            ? await repositories.dataRegisterRepo.find({
                  where: {
                      id: In(dataRegisterIds),
                  },
                  select: ['id', 'activeVersionId'],
              })
            : [];

        if (!dataRegisters?.length) return [];
        const activeVersionIds = _.uniq(_.compact(dataRegisters.map((dr) => dr.activeVersionId)));
        if (!activeVersionIds?.length) return [];

        const dataRegisterVersions = await repositories.dataRegisterVersionRepo.find({
            where: { id: In(activeVersionIds) },
            select: ['id', 'dataRegisterId', 'config'],
        });

        const collectionFilters: Record<string, any> = {};
        dataRegisterVersions.forEach((version) => {
            collectionFilters[version.dataRegisterId] = version.config?.filters ?? [];
        });

        const newCollections = collections.map((collection) => {
            const dataRegister = dataRegisters.find((drv) => drv.id === collection.dataRegisterId);

            return {
                ...collection,
                dataRegisterVersionId: dataRegister?.activeVersionId ?? collection?.dataRegisterVersionId,
                displaySetting: {
                    ...(collection.displaySetting ?? {}),
                    filters: collectionFilters[collection.dataRegisterId] ?? [],
                },
            } satisfies FormCollectionTenancyEntity;
        });

        return newCollections;
    }
    //#endregion Private methods
}
