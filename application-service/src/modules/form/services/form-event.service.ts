import { Inject, Injectable } from '@nestjs/common';

import { Repository } from 'typeorm';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormManualEventTenancyEntity } from '../../../database/src/entities/tenancy/form-manual-event.tenancy.entity';
import { TransactionEventEnum } from '../../../database/src/shared/enums/automation-event.enum';
import { ManualEventSourceType } from '../../../database/src/shared/enums/form-manual-event.enum';
import { ManualEventTriggerRequestDto } from '../dtos/requests';
import { FormEventDataService } from './data/form-event.data.service';
import { AutomationVersionTenancyEntity } from '../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';

@Injectable()
export class FormEventTenancyService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,
        @Inject(PROVIDER_KEYS.FORM_MANUAL_EVENT_TENANCY_REPOSITORY)
        private readonly _formManualEventRepo: Repository<FormManualEventTenancyEntity>,

        @Inject(PROVIDER_KEYS.AUTOMATION_VERSION_TENANCY_REPOSITORY)
        private readonly _autoVersionRepo: Repository<AutomationVersionTenancyEntity>,

        private readonly _formEventDataService: FormEventDataService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _logger: LoggerService,
    ) {}

    public async handleManualEvent(request: ManualEventTriggerRequestDto): Promise<{ result: boolean; errors: string[] }> {
        const result: { result: boolean; errors: string[] } = { result: false, errors: [] };
        try {
            if (!request.validRoleIds?.length) {
                request.validRoleIds = this._claims.roles?.map((item) => item.id);
            }

            const qb = this._formManualEventRepo.createQueryBuilder('qb').where({
                formVersionId: request.formVersionId,
                sourceType: request.type as ManualEventSourceType,
            });

            if (request.eventId) {
                qb.andWhere({
                    automationId: request.eventId,
                });
            }

            if (request.sourceId) {
                qb.andWhere({
                    sourceId: request.sourceId,
                });
            }

            const eventByType = await qb.getMany();

            const validateRequest = {
                ...request,
                manualEventIds: eventByType.map((item) => item.id),
            };

            const validationResult = await this._formEventDataService.validateManualEvent(validateRequest);
            const errors = validationResult.errors;
            if (errors.length) {
                result.errors = errors;
                return result;
            }

            const manualEvents = validationResult.data.manualEvents;

            for (const manualEvent of manualEvents) {
                const automationVersion = await this._autoVersionRepo.findOneBy({
                    automationId: manualEvent.automationId,
                    contextVersionId: manualEvent.formVersionId,
                });

                if (!automationVersion) continue;

                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        ...validationResult.data.transaction,
                        automationVersionId: automationVersion.id,
                        browserTabId: request.browserTabId,
                    },
                    tenantId: RequestContextService.accountId,
                    aggregateId: request.transactionId,
                    type: TransactionEventEnum.FORM_TRANSACTION_MANUAL,
                    name: TransactionEventEnum.FORM_TRANSACTION_MANUAL,
                });
                await this._eventDrivenService.publishMessage(
                    TransactionTopicEnum.FORM_TRANSACTION_TOPIC,
                    message,
                    manualEvent.automationVersionId,
                );
            }
            result.result = true;
        } catch (error) {
            result.errors = ['internal_error'];
            this._logger.error(error);
        }
        return result;
    }
}
