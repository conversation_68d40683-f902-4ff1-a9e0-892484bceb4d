import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { JsonTree } from '@react-awesome-query-builder/core';
import { isEmpty, isNil } from 'lodash';
import { FindOptionsWhere, In, IsNull, Repository } from 'typeorm';
import { LoggerService } from '../../../common/src';
import { TRANSACTION_FIELD_ID } from '../../../common/src/constant/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-extra-config.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { RelationTransactionEntity } from '../../../database/src/entities/tenancy/relation-transaction.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-access-controls.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { AccessControlType, AccessOption } from '../../../database/src/shared/enums/access-control-type.enum';
import { AutoPopulateDataSourceTypeEnum } from '../../../database/src/shared/enums/ap-data-source-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { JsonLogicUtils } from './data/util/run-json-logic.util';
import { PopulateTransactionFieldService } from './populate-transaction-field.service';

export class ConditionalChangeStageRequest {
    targetStageIdentityId: string;
    transactionFields: TransactionFieldEntity[];
    conditionalConfigs?: StageAccessControlTenancyEntity[];
    formFields: Pick<FormFieldTenancyEntity, 'fieldId' | 'label' | 'type' | 'configuration'>[];
}

export class PopulateCondition {
    id: string;
    conditions: JsonTree;
    dataSource: string;
    dataSourceType: string;
    extraConfigurations: Array<GeneralAutoPopulateExtraConfigTenancyEntity & { toFixedValue?: string; toFixedFieldType?: string }>;
}

export class ConditionalRelationRequired {
    [formId: string]: {
        type: 'transaction' | 'fields';
        items: { transactionId: string; fields: string[] }[];
    };
}

export class ConditionalFieldsRequired {
    fieldIds: string[];
}

export class ConditionalRequired {
    relationsRequired: ConditionalRelationRequired;
    fieldsRequired: ConditionalFieldsRequired;
    canChangeStage: boolean;
}

@Injectable()
export class ConditionalRequireService {
    constructor(
        private readonly _logger: LoggerService,
        private readonly _populateService: PopulateTransactionFieldService,

        @Inject(PROVIDER_KEYS.STAGE_ACCESS_CONTROL_TENANCY_REPOSITORY)
        private readonly _sacRepo: Repository<StageAccessControlTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private readonly _formFieldRepo: Repository<FormFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly _transactionRepo: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private readonly _relationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _transactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private readonly _stageRepository: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly _registerFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity>,
    ) {}

    public async verify({
        targetStageId,
        transactionId,
        transactionFieldsParam = [],
        isTest = false,
    }: {
        transactionId: string;
        targetStageId: string;
        transactionFieldsParam?: TransactionFieldEntity[];
        isTest?: boolean;
    }): Promise<ConditionalRequired> {
        const transaction = await this._transactionRepo.findOne({
            where: {
                id: transactionId,
            },
            select: ['id', 'formVersionId', 'stageId'],
            withDeleted: !!isTest,
        });

        if (!transaction) throw new BadRequestException('transaction_is_not_existed');

        const formVersionId = transaction.formVersionId;

        const targetStage = await this._stageRepository.findOne({
            where: {
                id: targetStageId,
            },
            select: ['id', 'identityId'],
        });

        const targetStageIdentityId = targetStage.identityId;

        //for now, just need verify on transaction form fields
        const transactionFields = transactionFieldsParam.length
            ? transactionFieldsParam
            : await this._transactionFieldRepository.find({
                  where: {
                      transactionId,
                      collectionId: IsNull(),
                  },
                  select: ['id', 'fieldId', 'fieldValue', 'fieldOptionIds', 'dependFieldId'],
              });

        const stageACLConfigs = await this._sacRepo.find({
            where: {
                formVersionId,
                stageId: transaction.stageId,
                type: In([AccessControlType.RELATION, AccessControlType.FIELD]),
            },
            select: ['id', 'config', 'targetId', 'type'],
        });

        const relationConfigs = stageACLConfigs.filter((item) => item.type === AccessControlType.RELATION);
        const fieldConfigs = stageACLConfigs.filter((item) => item.type === AccessControlType.FIELD);

        const formFields: Pick<FormFieldTenancyEntity, 'fieldId' | 'label' | 'type' | 'configuration'>[] = await this._formFieldRepo.find({
            where: {
                formVersionId,
            },
            select: ['fieldId', 'label', 'type', 'configuration', 'lookupTargetId'],
        });

        const fieldsRequired = await this.verifyFields({
            targetStageIdentityId,
            transactionFields,
            fieldConfigs,
            formFields,
        });

        const relationsRequired = await this.verifyRelations({
            targetStageIdentityId,
            transactionFields,
            transactionId,
            relationConfigs,
            formFields,
        });

        return {
            canChangeStage: isEmpty(relationsRequired) && isEmpty(fieldsRequired.fieldIds),
            relationsRequired,
            fieldsRequired,
        };
    }

    public async verifyRelations({
        transactionId,
        targetStageIdentityId,
        transactionFields,
        relationConfigs,
        formFields,
    }: {
        transactionId: string;
        targetStageIdentityId: string;
        transactionFields: TransactionFieldEntity[];
        relationConfigs: StageAccessControlTenancyEntity[];
        formFields: Pick<FormFieldTenancyEntity, 'fieldId' | 'label' | 'type' | 'configuration'>[];
    }): Promise<ConditionalRelationRequired> {
        // just conditional on editable relation config
        const editableRelationConfigs = relationConfigs.filter((item) => item.config?.access === AccessOption.Editable);

        //validate conditional relations
        const { conditionsRequired = [] } = await this.run({
            transactionFields,
            targetStageIdentityId,
            conditionalConfigs: editableRelationConfigs,
            formFields,
        });

        let required: ConditionalRelationRequired = {};

        if (conditionsRequired.length) {
            required = await this.check({
                requiredRelations: conditionsRequired,
                transactionId,
            });
        }

        return required;
    }

    public async verifyFields({
        transactionFields,
        fieldConfigs,
        targetStageIdentityId,
        formFields,
    }: {
        targetStageIdentityId: string;
        transactionFields: TransactionFieldEntity[];
        fieldConfigs: StageAccessControlTenancyEntity[];
        formFields: Pick<FormFieldTenancyEntity, 'fieldId' | 'label' | 'type' | 'configuration'>[];
    }): Promise<ConditionalFieldsRequired> {
        let required: ConditionalFieldsRequired = {
            fieldIds: [],
        };

        // just conditional on editable relation config
        const editableFieldConfigs = fieldConfigs.filter((item) => item.config?.access === AccessOption.Editable);

        //validate conditional relations
        const { conditionsRequired = [] } = await this.run({
            transactionFields,
            targetStageIdentityId,
            conditionalConfigs: editableFieldConfigs,
            formFields,
        });

        const transactionFormFields = transactionFields.filter((tf) => !tf.collectionId);

        const requiredFieldIds = conditionsRequired.map((item) => item.requiredTargetId).filter(Boolean);
        if (requiredFieldIds.length) {
            required.fieldIds = requiredFieldIds.filter((id) => {
                const tranField = transactionFormFields.find((f) => f.fieldId === id);
                return this._isEmpty(tranField?.fieldValue);
            });
        }

        return required;
    }

    public async run(request: ConditionalChangeStageRequest): Promise<{
        conditionsRequired: Array<{
            requiredTargetId: string;
            requiredTargetFieldIds: string[];
        }>;
    }> {
        try {
            const { targetStageIdentityId, conditionalConfigs = [], transactionFields = [], formFields = [] } = request;
            const transactionFormFields = transactionFields.filter((tf) => !tf.collectionId);

            const availableFields: Array<{ fieldId: string; value: string }> = [];
            const formValues = transactionFormFields.reduce((prev, curr) => {
                const formField = formFields.find((f) => f.fieldId === curr.fieldId);
                let fieldValue = curr.fieldValue;
                if (formField?.type === FormFieldTypeEnum.Lookup) {
                    fieldValue = curr.fieldOptionIds?.length ? (curr.fieldOptionIds || []).join(',') : fieldValue;
                }

                if (curr.dependFieldId) {
                    prev[`${curr.dependFieldId}--${curr.fieldId}`] = curr.fieldValue;
                } else {
                    prev[curr.fieldId] = fieldValue;
                }

                if (formField) {
                    availableFields.push({
                        fieldId: curr.fieldId,
                        value: fieldValue,
                    });
                }

                return prev;
            }, {});

            const conditionConfigsRun = conditionalConfigs
                .filter((rc) => rc.config)
                .map((rc) => {
                    return { ...rc.config, targetId: rc.targetId } as Record<string, any>;
                })
                .flatMap((config) =>
                    (config.requiredConditions || []).map((crc) => {
                        crc.targetId = config.targetId; //target maybe form id or field id
                        return crc;
                    }),
                )
                .filter((rc) => ((rc?.targetStages as string[]) || []).includes(targetStageIdentityId));

            if (!conditionConfigsRun.length)
                return {
                    conditionsRequired: [],
                };

            let isRequired = true;
            const conditionsRequired: Array<{
                requiredTargetId: string;
                requiredTargetFieldIds: string[];
            }> = [];

            for (const config of conditionConfigsRun) {
                if (!config) continue;

                const conditions: Array<
                    { condition: JsonTree; conditionType: 'normal' } | { condition: PopulateCondition; conditionType: 'populate' }
                > = [];

                const formCondition = (config.conditions as JsonTree)?.children1?.length ? (config.conditions as JsonTree) : undefined;
                if (formCondition) {
                    conditions.push({
                        condition: formCondition,
                        conditionType: 'normal',
                    });
                }

                const populateConditions = (config.populateConditions as PopulateCondition[]) || [];
                if (populateConditions.length) {
                    populateConditions.forEach((pc) => {
                        if (pc.conditions?.children1?.length) {
                            conditions.push({
                                condition: pc,
                                conditionType: 'populate',
                            });
                        }
                    });
                }

                const isRequiredIfTrue = !!config.requireCondition;
                let conditionRelationResult: boolean = true;

                for (const item of conditions) {
                    let conditionResult = true;
                    switch (item.conditionType) {
                        case 'normal':
                            conditionResult = await this._runNormal({ condition: item.condition, formValues });
                            break;
                        case 'populate':
                            conditionResult = await this._runPopulate({
                                condition: item.condition,
                                formFields,
                                availableFields,
                            });
                            break;
                    }

                    conditionRelationResult &&= conditionResult;
                    if (conditionRelationResult === false) break;
                }

                isRequired = isRequiredIfTrue === conditionRelationResult;

                if (isRequired) {
                    conditionsRequired.push({
                        requiredTargetId: config.targetId as string,
                        requiredTargetFieldIds: config.requiredFields,
                    });
                }
            }

            return {
                conditionsRequired,
            };
        } catch (err) {
            this._logger.error(err, 'ConditionalChangeStageService');
            throw err;
        }
    }

    public async check({
        requiredRelations,
        transactionId,
    }: {
        requiredRelations: Array<{
            requiredTargetId: string;
            requiredTargetFieldIds: string[];
        }>;
        transactionId: string;
    }): Promise<ConditionalRelationRequired> {
        const targetFormIds = requiredRelations.map((item) => item.requiredTargetId);
        const requiredFieldsByForm = requiredRelations.reduce(
            (prev, item) => {
                prev[item.requiredTargetId] = item.requiredTargetFieldIds || [];
                return prev;
            },
            {} as Record<string, string[]>,
        );

        const where: FindOptionsWhere<RelationTransactionEntity>[] = [];

        let relatedTransactions: TransactionEntity[] = [];
        let relatedTransactionFields: TransactionFieldEntity[] = [];

        targetFormIds.forEach((formId) => {
            where.push({
                originTransactionId: transactionId,
                targetFormId: formId,
            });
        });

        //get transactions and transaction fields for checking required
        if (where.length) {
            // get alls relations transaction of current transactions
            const relationTransactions = await this._relationTransactionRepository.find({
                where,
                select: ['id', 'targetTransactionId', 'targetFormId'],
            });

            const targetTransactionIds = relationTransactions.map((rt) => rt.targetTransactionId);

            relatedTransactions = relationTransactions.length
                ? await this._transactionRepo.find({
                      where: {
                          id: In(targetTransactionIds),
                      },
                      select: ['id', 'formId', 'formVersionId'],
                  })
                : [];

            const relatedTransactionIds = relatedTransactions.map((item) => item.id);

            if (targetTransactionIds.length) {
                const queryFieldsWhere: FindOptionsWhere<TransactionFieldEntity>[] = [];
                relationTransactions.forEach((rt) => {
                    if (!relatedTransactionIds.includes(rt.targetTransactionId)) return;

                    const fieldIds = requiredFieldsByForm[rt.targetFormId] || [];
                    fieldIds.push(TRANSACTION_FIELD_ID);

                    if (fieldIds.length) {
                        queryFieldsWhere.push({
                            transactionId: rt.targetTransactionId,
                            fieldId: In(fieldIds),
                            collectionId: IsNull(),
                        });
                    }
                });

                relatedTransactionFields = queryFieldsWhere?.length
                    ? await this._transactionFieldRepository.find({
                          where: queryFieldsWhere,
                          select: ['id', 'fieldId', 'fieldValue', 'transactionId'],
                      })
                    : [];
            }
        }

        const required: ConditionalRelationRequired = {};
        for (const requiredRelation of requiredRelations) {
            const formId = requiredRelation.requiredTargetId;
            const fieldIds = requiredRelation.requiredTargetFieldIds || [];
            const type = fieldIds?.length ? 'fields' : 'transaction';

            const transactions = relatedTransactions.filter((item) => item.formId === formId);
            const transactionIdSet = new Set();
            transactions.forEach((item) => item.id && transactionIdSet.add(item.id));

            const transactionFields = relatedTransactionFields.filter((item) => transactionIdSet.has(item.transactionId));
            const transactionFieldsMap = new Map<string, Record<string, TransactionFieldEntity>>();

            transactionFields.forEach((tf) => {
                if (!transactionFieldsMap.has(tf.transactionId)) transactionFieldsMap.set(tf.transactionId, {});

                transactionFieldsMap.get(tf.transactionId)[tf.fieldId] = tf;
            });

            switch (type) {
                case 'transaction': {
                    if (!transactionIdSet.size) {
                        required[formId] = {
                            type: 'transaction',
                            items: [],
                        };
                    }
                    break;
                }

                case 'fields': {
                    if (!transactions?.length) {
                        required[formId] = {
                            type: 'transaction',
                            items: [],
                        };
                        break;
                    }

                    const formVersionFieldsMap = new Map<string, FormFieldTenancyEntity[]>();

                    for (const transaction of transactions) {
                        const transactionFieldDict = transactionFieldsMap.get(transaction.id) || {};
                        const requiredFieldIds = fieldIds.filter((fieldId) => this._isEmpty(transactionFieldDict[fieldId]?.fieldValue));
                        const transactionIdField = transactionFieldDict[TRANSACTION_FIELD_ID];

                        if (requiredFieldIds.length) {
                            const formVersionId = transaction.formVersionId;
                            const formFields = formVersionFieldsMap.has(formVersionId)
                                ? formVersionFieldsMap.get(formVersionId)
                                : await this._formFieldRepo.find({
                                      where: {
                                          formVersionId,
                                      },
                                      select: {
                                          fieldId: true,
                                          label: true,
                                      },
                                  });

                            if (!formVersionFieldsMap.has(formVersionId)) {
                                formVersionFieldsMap.set(formVersionId, formFields);
                            }

                            if (!required[formId]) {
                                required[formId] = {
                                    type: 'fields',
                                    items: [],
                                };
                            }

                            const requiredFieldLabels: string[] = requiredFieldIds.map(
                                (fId) => formFields.find((ff) => ff.fieldId === fId)?.label ?? '',
                            );

                            required[formId].items.push({
                                transactionId: transactionIdField.fieldValue,
                                fields: requiredFieldLabels,
                            });
                        }
                    }
                    break;
                }
            }
        }

        return required;
    }

    private async _runPopulate({
        condition,
        availableFields,
        formFields,
    }: {
        condition: PopulateCondition;
        availableFields: Array<{ fieldId: string; value: string }>;
        formFields: FormFieldTenancyEntity[];
    }): Promise<boolean> {
        const { dataSource: dataSourceId, dataSourceType, extraConfigurations, conditions: dataSourceConditions } = condition;
        const fieldSetting: GeneralAutoPopulateSettingTenancyEntity = new GeneralAutoPopulateSettingTenancyEntity();
        fieldSetting.dataSourceId = dataSourceId;
        fieldSetting.dataSourceType = dataSourceType as AutoPopulateDataSourceTypeEnum;
        fieldSetting.extraConfigurations = extraConfigurations?.map((ec) => {
            const toFieldId = ec.toFieldId;
            const formField = formFields.find((ff) => ff.fieldId === toFieldId);
            if (formField && formField.type === FormFieldTypeEnum.Lookup) {
                const registerId = formField.lookupTargetId || formField.configuration?.targetId;
                ec.toFieldRegisterId = registerId ? registerId : undefined;
            }

            return {
                ...ec,
                fixedValue: ec.toFixedValue,
                formFieldType: ec.toFixedFieldType,
            };
        });

        const data = await this._populateService.execute({
            fieldSetting,
            availableFields,
            formFields,
        });

        if (!data) {
            return false;
        }

        const registerFields = await this._registerFieldRepository.find({
            where: {
                dataRegisterTransactionId: data.id,
            },
            select: ['fieldId', 'fieldValue', 'fieldOptionIds'],
        });

        const registerFieldValues = registerFields.reduce((registerFieldValue, field) => {
            registerFieldValue[field.fieldId] = field.fieldOptionIds?.length ? field.fieldOptionIds.join(',') : field.fieldValue;
            return registerFieldValue;
        }, {});

        const result = JsonLogicUtils.runJsonLogic(dataSourceConditions, registerFieldValues);
        return result;
    }

    private async _runNormal({ condition, formValues }: { condition: JsonTree; formValues: Record<string, any> }): Promise<boolean> {
        const result = JsonLogicUtils.runJsonLogic(condition, formValues);
        return result;
    }

    private _isEmpty(value?: string) {
        return value === '' || isNil(value);
    }
}
