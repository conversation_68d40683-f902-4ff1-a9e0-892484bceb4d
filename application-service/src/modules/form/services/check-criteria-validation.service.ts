import { Inject, Injectable } from '@nestjs/common';
import { JsonTree } from '@react-awesome-query-builder/core';
import { DataSource, In, Not, Repository } from 'typeorm';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';

import { LogicEngine } from 'json-logic-engine';
import { compact, isEmpty, orderBy, uniq } from 'lodash';
import { LoggerService, UtilsService } from '../../../common/src';
import { DurationFormatEnum } from '../../../common/src/modules/shared/enums/duration-format-type.enum';
import { Duration } from '../../../common/src/modules/shared/types/duration';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { DataRegisterTypeEnum } from '../../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';

import * as dayjs from 'dayjs';
import * as _ from 'lodash';
import { validate as isUUID } from 'uuid';
import { YYYYMMDD } from '../../../constant/date';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldOverrideEntity } from '../../../database/src/entities/tenancy/transaction-field-override.tenancy.entity';
import { TransactionFieldStyleEntity } from '../../../database/src/entities/tenancy/transaction-field-style.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { OverrideStatusEnum } from '../../../database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from '../../../database/src/shared/enums/override-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { CaptureActiveFormVersionType } from '../../../database/src/shared/providers/capture-active-form-version.provider';
import { executeConditions, formatLookupCondition, getOverrideRecord } from '../../../utils';
import { CheckValidationResponseType } from '../../validation/types';
import { CheckCriteriaValidationRequest } from '../dtos/requests/check-criteria-validation.reqest';
import { JsonLogicUtils } from './data/util/run-json-logic.util';
import { FormCollectionDataService } from './form-collection.data.service';

@Injectable()
export class CheckCriteriaValidationService {
    private readonly _engine = new LogicEngine();

    constructor(
        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemTenancyRepo: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterFieldTenancyRepo: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly _dataRegisterTenancyRepo: Repository<DataRegisterTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_TENANCY_REPOSITORY)
        private readonly _dataRegisterVersionTenancyRepo: Repository<DataRegisterVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _formCollectionAdditionalFieldTenancyRepo: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _formTransactionFieldRepo: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterAdditionalFieldTenancyRepo: Repository<DataRegisterAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _tenancyRepo: DataSource,

        private readonly _loggerService: LoggerService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly _transactionFieldOverrideRepo: Repository<TransactionFieldOverrideEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly _registerFieldTransactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private readonly _formFieldsRepo: Repository<FormFieldTenancyEntity>,

        private readonly _collectionDataService: FormCollectionDataService,
    ) {}

    public async checkCriteriaValidation(params: CheckCriteriaValidationRequest): Promise<Array<CheckValidationResponseType>> {
        const { formValues, formVersionId, collectionType, transactionId, triggerFieldId, cachedFormVersion, isTest } = params;

        let triggerField = triggerFieldId ? UtilsService.extractCollectionKeys(triggerFieldId) : null;

        let transactionFields = params?.transactionFields || [];
        if (!transactionFields?.length) {
            transactionFields = await this._formTransactionFieldRepo.find({
                where: {
                    transactionId,
                    contextType: TransactionFieldContextTypeEnum.COLLECTION,
                    rowKey: triggerField?.collectionItemKey,
                },
                relations: {
                    transactionFieldStyle: true,
                },
            });
        } else {
            if (triggerField?.collectionItemKey) {
                transactionFields = transactionFields.filter((tf) => tf.rowKey === triggerField?.collectionItemKey);
            }
        }

        if (!transactionFields?.length) {
            return [];
        }

        const fields: {
            [fieldId: string]: {
                formName: string;
                value: any;
            };
        } = {};

        const fieldIds: string[] = [];
        let formCollectionIdentityIds: string[] = [];
        const formatFormValues: Record<string, string> = {};
        const mappingFormValuesObj: Record<string, Record<string, string>> = {};

        for (const [key, value] of Object.entries(formValues ?? {})) {
            const { collectionItemKey, fieldIdentityId, collectionIdentityId, collectionItemIdentityId } =
                UtilsService.extractCollectionKeys(key);

            const fieldId = fieldIdentityId ?? collectionItemKey;
            if (fieldId?.includes('--')) {
                const [_, subField] = fieldId.split('--');
                formatFormValues[subField] = value;
                continue;
            }

            formatFormValues[key] = value;
            mappingFormValuesObj[`${collectionItemKey}_${collectionItemIdentityId}_${fieldIdentityId}`] = {
                collectionItemKey,
                collectionItemIdentityId,
                fieldIdentityId,
            };

            collectionIdentityId && !collectionIdentityId.includes('--') && formCollectionIdentityIds.push(collectionIdentityId);

            let objectKey = fieldId;

            if (collectionItemIdentityId) {
                objectKey = `${objectKey}_${collectionItemIdentityId}_${collectionItemKey}`;
            }

            fields[objectKey] = {
                formName: key,
                value: value,
            };
        }

        transactionFields.forEach((tf) => {
            if (tf.collectionId) {
                formCollectionIdentityIds.push(tf.collectionId);
                fieldIds.push(tf.fieldId);
                const formatKey = `${tf.rowKey}_${tf.collectionItemId}_${tf.fieldId}`;
                const isExistFormValue = mappingFormValuesObj[formatKey];

                if (!isExistFormValue) {
                    const combineFieldValue = tf.fieldOptionIds?.length ? tf.fieldOptionIds : tf.fieldValue;
                    const combineSimpleKey = `${tf.fieldId}_${tf.collectionItemId}_${tf.rowKey}`;
                    formatFormValues[combineSimpleKey] = combineFieldValue as string;

                    const combineFullKey = `${tf.rowKey}___${tf.fieldId}_${tf.collectionItemId}_${tf.collectionId}`;
                    formatFormValues[combineFullKey] = combineFieldValue as string;
                }
            }
        });

        const uniqFieldIds = _.uniq(fieldIds);

        formCollectionIdentityIds = _.uniq(formCollectionIdentityIds);

        if (!formCollectionIdentityIds?.length) {
            return [];
        }

        try {
            const { transactionIds, formCollectionItems } = await this._getFormCollectionItems(
                formCollectionIdentityIds,
                formVersionId,
                collectionType,
                isTest,
                cachedFormVersion,
            );

            if (!transactionIds?.length) {
                return [];
            }

            const formFields: FormFieldTenancyEntity[] = cachedFormVersion?.fields?.length
                ? cachedFormVersion?.fields
                : await this._formFieldsRepo.findBy({ formVersionId });

            return await this._checkCollectionValidation({
                fieldIds: uniqFieldIds,
                fields,
                transactionIds,
                transactionFields,
                formFields,
                formVersionId,
                triggerField,
                formCollectionItems,
                cachedFormVersion,
                isTest,
            });
        } catch (err) {
            this._loggerService.error(err);
            throw err;
        }
    }

    private async _checkCollectionValidation({
        fieldIds,
        fields,
        formCollectionItems,
        transactionFields,
        triggerField,
        formFields,
        transactionIds,
        formVersionId,
        cachedFormVersion,
        isTest,
    }: {
        fieldIds: string[];
        fields: {
            [fieldId: string]: {
                formName: string;
                value: any;
            };
        };
        formCollectionItems: FormCollectionItemTenancyEntity[];
        transactionFields: TransactionFieldEntity[];
        triggerField?: Partial<{
            collectionItemKey: string;
            fieldIdentityId: string;
            collectionItemIdentityId: string;
            collectionIdentityId: string;
        }>;
        formFields: FormFieldTenancyEntity[];
        transactionIds: string[];
        formVersionId: string;
        cachedFormVersion?: CaptureActiveFormVersionType;
        isTest?: boolean;
    }) {
        const dataRegisterVersionIds: string[] = [];
        formCollectionItems.forEach((item) => {
            item.formCollection.dataRegisterVersionId && dataRegisterVersionIds.push(item.formCollection.dataRegisterVersionId);
        });

        const collectionAdditionalFields = isTest
            ? await this._collectionDataService.getTestAdditionalFields({
                  formVersionId,
                  formCollectionItems,
              })
            : cachedFormVersion?.collectionAdditionalFields || [];

        const { normalFields, additionalFields } = await this._getDataRegisterFields({
            fieldIds,
            dataRegisterVersionIds,
            formVersionId,
            transactionIds: transactionIds || [],
            additionalFields: collectionAdditionalFields,
        });

        if (!normalFields?.length && !additionalFields?.length) {
            return [];
        }

        let _formValues = this._transformValues(fields, normalFields, additionalFields as FormCollectionAdditionalFieldTenancyEntity[]);

        const data = await this._executeCollectionQuery({
            fieldValidations: [...normalFields, ...additionalFields],
            formValues: _formValues,
            transactionFields,
            triggerField,
            formFields,
        });

        return data;
    }

    private async _checkCriteriaValidation({
        fieldIds,
        transactionIds,
        formVersionId,
        fields,
        transactionFields,
        triggerField,
        ignoreSave,
        formFields,
        formValues,
    }: {
        fieldIds: string[];
        transactionIds: string[];
        formVersionId: string;
        fields: {
            [fieldId: string]: {
                formName: string;
                value: any;
            };
        };
        transactionFields: TransactionFieldEntity[];
        triggerField?: Partial<{
            collectionItemKey: string;
            fieldIdentityId: string;
            collectionItemIdentityId: string;
            collectionIdentityId: string;
        }>;
        ignoreSave?: boolean;
        formFields: FormFieldTenancyEntity[];
        formValues?: Record<string, string>;
    }) {
        const { normalFields, additionalFields } = await this._getCriteriaFields(fieldIds, transactionIds, formVersionId);

        if (!additionalFields?.length) {
            return [];
        }
        const _formValues = this._transformValues(fields, normalFields, additionalFields);

        const data = await this._executeCriteriaQuery({
            additionalFields,
            formValues: _formValues,
            transactionFields,
            ignoreSave,
            triggerField,
            originalFormValues: formValues,
            formFields,
        });

        return data;
    }

    private async _executeCriteriaQuery({
        additionalFields,
        formValues,
        transactionFields,
        ignoreSave,
        triggerField,
        originalFormValues,
        formFields,
    }: {
        additionalFields: FormCollectionAdditionalFieldTenancyEntity[];
        formValues: Record<
            string,
            {
                rowKey?: string;
                value: any;
            }
        >;
        transactionFields: TransactionFieldEntity[];
        ignoreSave?: boolean;
        triggerField?: Partial<{
            collectionItemKey: string;
            fieldIdentityId: string;
            collectionItemIdentityId: string;
            collectionIdentityId: string;
        }>;
        originalFormValues?: Record<string, any>;
        formFields: FormFieldTenancyEntity[];
    }) {
        const _result: Array<CheckValidationResponseType> = [];

        const cloneAdditional = _.cloneDeep(additionalFields) as any;

        const transactionFieldStyles: TransactionFieldStyleEntity[] = [];

        let cloneTransactionFields = _.cloneDeep(transactionFields);

        if (triggerField?.collectionItemKey) {
            cloneTransactionFields = cloneTransactionFields.filter((field) => field.rowKey === triggerField.collectionItemKey);
        }

        const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum }> = {};

        cloneTransactionFields.forEach((transactionField) => {
            originTransactionFieldOverrides[transactionField.id] = {
                type: OverrideTypeEnum.System,
                value: transactionField.validationValue,
            };

            const field = cloneAdditional.find(
                (field) =>
                    field.fieldId === transactionField.fieldId &&
                    field?.formCollectionItemIdentityId === transactionField?.collectionItemId,
            );

            if (!field) {
                return;
            }

            const dataValidationConditions = field.configuration?.ruleConfigs?.dataValidationConditions;

            if (!dataValidationConditions?.length) {
                return;
            }

            const orderedDataValidationConditions = orderBy(dataValidationConditions, ['icon.name']);
            const _formValues: Record<string, any> = {};

            for (const { icon, condition } of orderedDataValidationConditions) {
                const childFields: string[] = [];

                for (const [key, data] of Object.entries(formValues)) {
                    const [fieldId, formCollectionItemIdentityId] = key?.split('_');
                    //is form field
                    if (!formCollectionItemIdentityId && formFields.some((f) => f.fieldId === fieldId)) {
                        _formValues[fieldId] = data?.value;
                    }

                    //is collection field
                    if (field.formCollectionItemIdentityId === formCollectionItemIdentityId) {
                        _formValues[fieldId] = data?.value;
                    }
                }

                (condition.children1 ?? []).forEach((children) => {
                    const dependencyFieldId = children?.properties?.value?.[0];
                    const isUuid = isUUID(dependencyFieldId);

                    if (isUuid) {
                        childFields.push(_formValues?.[dependencyFieldId]);
                    }
                });

                const evalResult = this.runJsonLogic(condition as JsonTree, _formValues);

                if (evalResult) {
                    const _value: CheckValidationResponseType = {
                        rowKey: transactionField.rowKey,
                        collectionId: transactionField.collectionId,
                        collectionItemId: field.formCollectionItemIdentityId,
                        fieldId: field.fieldId,
                        validationValue: icon.name,
                        label: icon.label,
                        transactionFieldStyleId: transactionField.id,
                        configuration: {
                            icon: icon?.name,
                            label: icon?.label,
                        },
                    };
                    this._setData(_result, _value);

                    const _transactionStyle = {
                        transactionId: transactionFields?.[0]?.transactionId,
                        fieldId: transactionField.fieldId,
                        id: transactionField.id,
                        configuration: {
                            icon: icon?.name,
                            label: icon?.label,
                        },
                    };

                    this._setTransactionFieldStyles(transactionFieldStyles, _transactionStyle);

                    break;
                } else {
                    const _value: CheckValidationResponseType = {
                        rowKey: transactionField.rowKey,
                        collectionId: transactionField.collectionId,
                        collectionItemId: field.formCollectionItemIdentityId,
                        fieldId: field.fieldId,
                        validationValue: null,
                        transactionFieldStyleId: transactionField.id,
                        configuration: null,
                    };
                    this._setData(_result, _value);

                    const _transactionStyle = {
                        transactionId: transactionFields?.[0]?.transactionId,
                        fieldId: transactionField.fieldId,
                        id: transactionField.id,
                        configuration: null,
                    };

                    this._setTransactionFieldStyles(transactionFieldStyles, _transactionStyle);
                }
            }
        });

        // const activeOverrides = await this.combineValidationValue({
        //     _result,
        //     cloneTransactionFields,
        //     transactionFieldStyles,
        //     formValues: originalFormValues,
        // });

        // if (!ignoreSave) {
        //     updateActiveOverrideRecords({ activeOverrides, transactionFieldOverrideRepo: this._transactionFieldOverrideRepo });
        //     addSystemOverrideRecords({
        //         originTransactionFieldOverrides,
        //         updateFields: transactionFieldStyles?.map((style) => ({
        //             id: style.id,
        //             validationValue: style.configuration?.icon,
        //         })),
        //         transactionFieldOverrideRepo: this._transactionFieldOverrideRepo,
        //     });
        // }

        return _result;
    }
    private async _executeCollectionQuery({
        fieldValidations,
        formValues,
        transactionFields,
        triggerField,
        formFields,
    }: {
        fieldValidations: Array<
            DataRegisterFieldTenancyEntity | DataRegisterAdditionalFieldTenancyEntity | FormCollectionAdditionalFieldTenancyEntity
        >;
        formValues: Record<
            string,
            {
                rowKey?: string;
                value: any;
            }
        >;
        transactionFields: TransactionFieldEntity[];
        triggerField?: Partial<{
            collectionItemKey: string;
            fieldIdentityId: string;
            collectionItemIdentityId: string;
            collectionIdentityId: string;
        }>;
        formFields: FormFieldTenancyEntity[];
    }) {
        const _result: Array<CheckValidationResponseType> = [];

        const transactionFieldStyles: TransactionFieldStyleEntity[] = [];

        let cloneTransactionFields = _.cloneDeep(transactionFields);

        if (triggerField?.collectionItemKey) {
            cloneTransactionFields = cloneTransactionFields.filter((field) => field.rowKey === triggerField.collectionItemKey);
        }
        const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum; border?: number }> = {};
        const cloneTransactionFieldMap = {};

        let registerTransactionFields: DataRegisterTransactionFieldTenancyEntity[] = [];
        const optionDict: Map<string, string> = new Map();
        const fieldOptionsDict: Map<string, string[]> = new Map();
        const lookupFieldValidations = (fieldValidations || []).filter(
            (fv) => (fv as DataRegisterFieldTenancyEntity).lookupTargetId || fv.configuration?.targetId,
        );

        if (lookupFieldValidations.length) {
            const validationFieldDict = lookupFieldValidations.reduce(
                (prev, validation) => {
                    prev[validation.fieldId] = 1;
                    return prev;
                },
                {} as Record<string, number>,
            );

            const formValueDict = Object.keys(formValues ?? {}).reduce(
                (prev, key) => {
                    const [fieldId] = key.split('_');
                    const { value, rowKey } = formValues?.[key];
                    prev[`${fieldId}_${rowKey}`] = {
                        value,
                    };

                    return prev;
                },
                {} as Record<string, Record<string, string>>,
            );

            for (const field of cloneTransactionFields) {
                if (!validationFieldDict[field.fieldId]) continue;

                const key = `${field.fieldId}_${field.rowKey}`;
                const value = formValueDict[key]?.value as string | string[];
                const formatValue = value ? value : [];
                const requestOptionIds: string[] = (Array.isArray(formatValue) ? _.compact(formatValue) : [formatValue]).filter((op) =>
                    isUUID(op),
                );

                if (requestOptionIds.length) {
                    requestOptionIds.forEach((optionId) => {
                        optionDict.set(optionId, optionId);
                    });

                    fieldOptionsDict.set(key, requestOptionIds);
                }
            }
        }

        if (optionDict.size) {
            const registerIds = Array.from(optionDict.values());
            registerTransactionFields = await this._registerFieldTransactionFieldRepo.findBy({
                dataRegisterTransactionId: In(registerIds),
            });
        }

        const formFieldDict = formFields.reduce(
            (prev, field) => {
                prev[field.fieldId] = field;
                return prev;
            },
            {} as Record<string, FormFieldTenancyEntity>,
        );

        const { formFieldValueDict, collectionFieldValueDict } = Object.keys(formValues ?? {}).reduce(
            (prev, key) => {
                const value = formValues[key]?.value;
                const [fieldId, formCollectionItemIdentityId, collectionItemKey] = key?.split('_');

                if (!formCollectionItemIdentityId && formFieldDict[fieldId]) {
                    prev.formFieldValueDict[fieldId] = value;
                } else {
                    const key = `${formCollectionItemIdentityId}_${collectionItemKey}`;
                    const exist = prev.collectionFieldValueDict[key];
                    prev.collectionFieldValueDict[key] = exist ? { ...exist, [`${fieldId}`]: value } : { [`${fieldId}`]: value };
                }

                return prev;
            },
            {
                formFieldValueDict: {} as Record<string, string>,
                collectionFieldValueDict: {} as Record<string, Record<string, string>>,
            },
        );

        const tasks = cloneTransactionFields.map(async (field, idx) => {
            cloneTransactionFieldMap[`${field.fieldId}${field.collectionItemId ? `_${triggerField?.collectionItemKey}` : ''}`] = field;
            originTransactionFieldOverrides[field.id] = {
                value: field.validationValue,
                type: OverrideTypeEnum.System,
                border: field.transactionFieldStyle?.configuration?.border,
            };
            const fieldValidation = fieldValidations.find((validation) => {
                if ((validation as any).formCollectionItemIdentityId) {
                    return (
                        validation.fieldId === field.fieldId &&
                        (validation as FormCollectionAdditionalFieldTenancyEntity)?.formCollectionItemIdentityId === field.collectionItemId
                    );
                }
                return validation.fieldId === field.fieldId;
            });

            if (!fieldValidation) {
                return;
            }

            let _formValues: Record<string, any> = {};

            if ((fieldValidation as DataRegisterFieldTenancyEntity).lookupTargetId || fieldValidation.configuration?.targetId) {
                const requestOptionIds = fieldOptionsDict.get(`${field.fieldId}_${field.rowKey}`);

                if (requestOptionIds?.length) {
                    const _registerTransactionFields = registerTransactionFields.filter((rtf) =>
                        requestOptionIds.includes(rtf.dataRegisterTransactionId),
                    );
                    for (const tranField of _registerTransactionFields) {
                        _formValues[`${fieldValidation.fieldId}--${tranField.fieldId}`] = tranField.fieldValue;
                    }
                }
            }

            const dataValidationConditions = fieldValidation.configuration?.ruleConfigs?.dataValidationConditions;
            const orderedDataValidationConditions = orderBy(dataValidationConditions, ['icon.name']);

            if (!orderedDataValidationConditions?.length) {
                return;
            }

            _formValues = _.merge(_formValues, formFieldValueDict);
            const collectionFieldKey = `${field.collectionItemId}_${field.rowKey}`;
            const collectionFieldValue = collectionFieldValueDict[`${collectionFieldKey}`];
            _formValues = _.merge(_formValues, collectionFieldValue);

            for (const item of orderedDataValidationConditions) {
                const { icon, condition } = item;

                formatLookupCondition(condition);

                (condition.children1 ?? []).forEach((children) => {
                    const field = children?.properties?.field;
                    const valueType = children?.properties?.valueType?.[0];
                    if (valueType === 'boolean') {
                        _formValues[field] = _formValues[field]?.toString() === 'true';
                    }
                });

                const evalResult = this.runJsonLogic(condition as JsonTree, _formValues);
                if (evalResult) {
                    const _value: CheckValidationResponseType = {
                        rowKey: field.rowKey,
                        collectionId: field.collectionId,
                        collectionItemId: field.collectionItemId,
                        fieldId: field.fieldId,
                        validationValue: icon.name,
                        label: icon.label,
                        transactionFieldStyleId: field.id,
                        configuration: {
                            icon: icon?.name,
                            label: icon?.label,
                        },
                    };
                    this._setData(_result, _value);
                    const _transactionStyle = {
                        transactionId: transactionFields?.[0]?.transactionId,
                        fieldId: field.fieldId,
                        id: field.id,
                        configuration: {
                            icon: icon?.name,
                            label: icon?.label,
                        },
                    };
                    this._setTransactionFieldStyles(transactionFieldStyles, _transactionStyle);
                    break;
                } else {
                    const _value: CheckValidationResponseType = {
                        rowKey: field.rowKey,
                        collectionId: field.collectionId,
                        collectionItemId: field.collectionItemId,
                        fieldId: field.fieldId,
                        validationValue: null,
                        transactionFieldStyleId: field.id,
                        configuration: null,
                    };
                    this._setData(_result, _value);
                    const _transactionStyle = {
                        transactionId: transactionFields?.[0]?.transactionId,
                        fieldId: field.fieldId,
                        id: field.id,
                        configuration: null,
                    };
                    this._setTransactionFieldStyles(transactionFieldStyles, _transactionStyle);
                }
            }
        });

        await Promise.all(tasks);

        Object.entries(formValues ?? {}).forEach(([key, value]) => {
            const { collectionItemKey, fieldIdentityId } = UtilsService.extractCollectionKeys(key);

            if (!fieldIdentityId) {
                return;
            }

            const field = cloneTransactionFieldMap[`${fieldIdentityId}${collectionItemKey ? `_${collectionItemKey}` : ''}`];

            if (!field) {
                return;
            }

            if (field.rowKey === collectionItemKey && field.fieldId === fieldIdentityId) {
                field.fieldValue = value.value;
            }
        });

        // const activeOverrides = await this.combineValidationValue({
        //     _result,
        //     cloneTransactionFields: Object.values(cloneTransactionFieldMap),
        //     transactionFieldStyles,
        // });

        // if (!ignoreSave) {
        //     updateActiveOverrideRecords({ activeOverrides, transactionFieldOverrideRepo: this._transactionFieldOverrideRepo });
        //     addSystemOverrideRecords({
        //         originTransactionFieldOverrides,
        //         updateFields: transactionFieldStyles?.map((style) => ({
        //             id: style.id,
        //             validationValue: style.configuration?.icon,
        //         })),
        //         transactionFieldOverrideRepo: this._transactionFieldOverrideRepo,
        //     });
        // }

        return _result;
    }

    //This function do nothing
    private async _updateTransactionFieldStyles(transactionFieldStyles: TransactionFieldStyleEntity[]) {
        try {
            await this._tenancyRepo.transaction(async (manager) => {
                const tasks = [];
                const transactionFieldRepo = manager.getRepository(TransactionFieldEntity);
                const transactionFieldStyleRepo = manager.getRepository(TransactionFieldStyleEntity);

                const transactionStyleEntities = await transactionFieldStyleRepo.findBy({
                    fieldId: In(transactionFieldStyles?.map((style) => style.fieldId)),
                    transactionId: In(transactionFieldStyles?.map((style) => style.transactionId)),
                    id: In(transactionFieldStyles?.map((style) => style.id)),
                });

                transactionFieldStyles.forEach((style) => {
                    tasks.push(
                        transactionFieldRepo.update(
                            {
                                id: style.id,
                            },
                            {
                                validationValue: style.configuration?.icon,
                            },
                        ),
                    );

                    const transactionStyleEntity = transactionStyleEntities.find(
                        (entity) =>
                            entity?.fieldId === style.fieldId && entity?.transactionId === style.transactionId && entity?.id === style.id,
                    );

                    if (!transactionStyleEntity) {
                        tasks.push(transactionFieldStyleRepo.save(style));
                    } else {
                        tasks.push(
                            transactionFieldStyleRepo.update(
                                {
                                    id: style.id,
                                },
                                {
                                    configuration: style.configuration,
                                },
                            ),
                        );
                    }
                });
            });
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    private _setData(result: CheckValidationResponseType[], newValue: CheckValidationResponseType) {
        const _exists = result.find(
            (res) =>
                res.rowKey === newValue.rowKey && res.collectionItemId === newValue.collectionItemId && res.fieldId === newValue.fieldId,
        );

        if (_exists) {
            _exists.validationValue = newValue.validationValue;
            _exists.label = newValue.label;
            _exists.border = newValue.border;
            _exists.configuration = newValue.configuration;
        } else {
            result.push(newValue);
        }
    }

    private _setTransactionFieldStyles(
        transactionFieldStyles: TransactionFieldStyleEntity[],
        newValue: {
            transactionId: string;
            fieldId: string;
            configuration: any;
            id: string;
        },
    ) {
        const _exists = transactionFieldStyles.find((res) => res.transactionId === newValue.transactionId && res.id === newValue.id);

        if (_exists) {
            _exists.configuration = newValue.configuration;
        } else {
            transactionFieldStyles.push(newValue);
        }
    }

    public runJsonLogic(visibilityConditions: JsonTree, formValues: Record<string, string>) {
        if (!visibilityConditions || !formValues) {
            return false;
        }
        const fieldIds = [];
        const values = [];
        Object.keys(formValues).forEach((fieldId) => {
            if (fieldId) {
                fieldIds.push(fieldId);
                values.push(formValues[fieldId]);
            }
        });
        const evalResult = executeConditions(visibilityConditions, fieldIds, values);
        return evalResult ?? true;
    }

    public convertVariableValue = (type: FormFieldTypeEnum | undefined, value: any) => {
        if (!type || isEmpty(value?.toString())) {
            return null;
        }

        let formattedValue: string | number | null | Duration | Date | boolean | string[] = '';

        switch (type) {
            case FormFieldTypeEnum.DatePicker:
                // Convert to query builder format: YYYYMMDD
                formattedValue = value ? dayjs(value).format(YYYYMMDD) : null;
                break;
            case FormFieldTypeEnum.TimePicker:
                formattedValue = JsonLogicUtils.convertMinutesToHHmm(value as string);
                break;

            case FormFieldTypeEnum.Duration:
                formattedValue = UtilsService.convertStringToDuration(
                    UtilsService.convertMinutesToDuration({ value: value, format: DurationFormatEnum.DHM }),
                );
                break;

            case FormFieldTypeEnum.Number:
                formattedValue = Number(value?.toString());
                break;

            case FormFieldTypeEnum.Checkbox:
                formattedValue = value === 'true' || value === true;
                break;

            case FormFieldTypeEnum.Select:
            case FormFieldTypeEnum.Lookup:
                formattedValue = _.isArray(value) ? value : [value];
                break;

            default:
                formattedValue = value;
                break;
        }
        return formattedValue;
    };

    private async _getFormCollectionItems(
        formCollectionIds: string[],
        formVersionId: string,
        collectionType: DataRegisterTypeEnum,
        isTest: boolean,
        cachedFormVersion?: CaptureActiveFormVersionType,
    ): Promise<{
        formCollectionItems: FormCollectionItemTenancyEntity[];
        transactionIds: string[];
    }> {
        const cachedCollectionItems = (cachedFormVersion?.formCollections || [])
            .filter((f) => formCollectionIds.includes(f.identityId))
            .flatMap((collection) => collection.formCollectionItems || []);

        if (cachedCollectionItems?.length) {
            const transactionIds = [];
            cachedCollectionItems.forEach((item) => {
                transactionIds.push(item.dataRegisterTransactionId);
                const formCollection = cachedFormVersion?.formCollections?.find((c) => c.id === item.formCollectionId);
                if (formCollection) {
                    item.formCollection = formCollection;
                }
            });
            return {
                formCollectionItems: cachedCollectionItems,
                transactionIds: uniq(compact(transactionIds)),
            };
        } else {
            const formCollectionItems = await this._formCollectionItemTenancyRepo
                .createQueryBuilder('formCollectionItem')
                .leftJoinAndSelect('formCollectionItem.formCollection', 'formCollection')
                .where(`formCollection.identityId In(:...identityIds)`, {
                    identityIds: formCollectionIds,
                })
                .andWhere(`formCollection.formVersionId =:formVersionId`, {
                    formVersionId: formVersionId,
                })
                .andWhere(`formCollection.type =:type`, {
                    type: collectionType,
                })
                .getMany();

            if (!formCollectionItems?.length) {
                return {
                    transactionIds: [],
                    formCollectionItems: [],
                };
            }

            const transactionIds = formCollectionItems.map((item) => item.dataRegisterTransactionId);

            if (!isTest) {
                return {
                    transactionIds,
                    formCollectionItems,
                };
            }

            // update formCollection with active dataRegisterVersionId when in test mode
            const collectionMap = new Map<string, FormCollectionTenancyEntity>();

            formCollectionItems.forEach((item) => {
                const collection = item.formCollection;
                if (collection && !collectionMap.has(collection.id)) {
                    collectionMap.set(collection.id, collection);
                }
            });

            const collections = Array.from(collectionMap.values());

            const dataRegisterIds = collections.map((collection) => collection.dataRegisterId);

            const dataRegisters = dataRegisterIds?.length
                ? await this._dataRegisterTenancyRepo.find({
                      where: {
                          id: In(dataRegisterIds),
                      },
                      select: ['id', 'activeVersionId'],
                  })
                : [];

            if (!dataRegisters?.length) {
                return {
                    transactionIds,
                    formCollectionItems,
                };
            }

            const activeVersionIds = _.uniq(_.compact(dataRegisters.map((dr) => dr.activeVersionId)));

            if (!activeVersionIds?.length) {
                return {
                    transactionIds,
                    formCollectionItems,
                };
            }

            collections.forEach((collection) => {
                const dataRegister = dataRegisters.find((drv) => drv.id === collection.dataRegisterId);
                collection.dataRegisterVersionId = dataRegister?.activeVersionId ?? collection?.dataRegisterVersionId;

                collectionMap.set(collection.id, collection);
            });

            formCollectionItems.forEach((item) => {
                const collection = collectionMap.get(item.formCollectionId);
                if (collection) {
                    item.formCollection = collection;
                }
            });

            return { transactionIds, formCollectionItems };
        }
    }

    private async _getCriteriaFields(fieldIds: string[], transactionIds: string[], formVersionId: string) {
        const [normalFields, additionalFields] = await Promise.all([
            this._dataRegisterFieldTenancyRepo.find({
                where: {
                    fieldId: In(fieldIds),
                },
            }),
            // this._getAdditionalFields({ fieldIds, transactionIds, formVersionId }),
            this._formCollectionAdditionalFieldTenancyRepo
                .createQueryBuilder('dataRegisterAdditionalField')
                .where({
                    fieldId: In(fieldIds),
                    transactionId: In(transactionIds),
                    formVersionId: formVersionId,
                })
                .andWhere(
                    `jsonb_array_length(dataRegisterAdditionalField.configuration -> 'ruleConfigs' -> 'dataValidationConditions') >= 1`,
                )
                .getMany(),
        ]);

        return {
            normalFields,
            additionalFields,
        };
    }

    private async _getAdditionalFields({
        fieldIds,
        formVersionId,
        transactionIds,
    }: {
        fieldIds: string[];
        transactionIds: string[];
        formVersionId: string;
    }) {
        const qb = this._formCollectionAdditionalFieldTenancyRepo.createQueryBuilder('dataRegisterAdditionalField').where({
            formVersionId: formVersionId,
        });
        // .andWhere(`jsonb_array_length(dataRegisterAdditionalField.configuration -> 'ruleConfigs' -> 'dataValidationConditions') >= 1`);
        if (fieldIds?.length) {
            qb.andWhere({ fieldId: In(fieldIds) });
        }

        if (transactionIds?.length) {
            qb.andWhere({ transactionId: In(transactionIds) });
        }

        return qb.getMany();
    }

    // ONLY FOR CHECK COLLECTION VALIDATION
    public async _getDataRegisterFields({
        fieldIds,
        dataRegisterVersionIds,
        formVersionId,
        transactionIds,
        additionalFields,
    }: {
        fieldIds: string[];
        dataRegisterVersionIds: string[];
        formVersionId: string;
        transactionIds?: string[];
        additionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
    }) {
        try {
            // DR fields
            const fields = await this._dataRegisterFieldTenancyRepo
                .createQueryBuilder('dataRegisterField')
                .where({
                    fieldId: In(fieldIds),
                    dataRegisterVersionId: In(_.uniq(dataRegisterVersionIds)),
                    type: Not(FormFieldTypeEnum.Definable),
                })
                // .andWhere(`jsonb_array_length(dataRegisterField.configuration -> 'ruleConfigs' -> 'dataValidationConditions') >= 1`)
                .getMany();

            const additionalBuilder = this._dataRegisterAdditionalFieldTenancyRepo.createQueryBuilder('dataRegisterAdditionalField').where({
                fieldId: In(fieldIds),
            });

            if (transactionIds?.length) {
                additionalBuilder.andWhere({ transactionId: In(transactionIds) });
            }

            // Additional fields and captured additional fields
            const [additionalBuilderFields, formAdditionalFields] = await Promise.all([
                additionalBuilder.getMany(),
                additionalFields?.length
                    ? Promise.resolve(
                          transactionIds?.length
                              ? additionalFields?.filter((field) => transactionIds.includes(field.transactionId))
                              : additionalFields,
                      )
                    : this._getAdditionalFields({ fieldIds: [], transactionIds, formVersionId }),
            ]);

            const validationFields = [...fields, ...additionalBuilderFields, ...formAdditionalFields];

            const validation = validationFields.some((field) => field.configuration?.ruleConfigs?.dataValidationConditions?.length > 0);
            if (validation) {
                const additionalFieldIds = formAdditionalFields.map((field) => field.fieldId);

                const filteredAdditionalBuilderFields = additionalBuilderFields.filter(
                    (i) => i.fieldId && !additionalFieldIds.includes(i.fieldId),
                );

                const additionalFieldsHasValidation = [...filteredAdditionalBuilderFields, ...formAdditionalFields];

                return {
                    normalFields: [...fields],
                    additionalFields: additionalFieldsHasValidation,
                };
            }

            return {
                normalFields: [],
                additionalFields: [],
            };
        } catch (err) {
            this._loggerService.error(err);
            return {
                normalFields: [],
                additionalFields: [],
            };
        }
    }

    private _transformValues(
        values: Record<string, { formName: string; value: any }>,
        fields: DataRegisterFieldTenancyEntity[],
        additionalFields?: FormCollectionAdditionalFieldTenancyEntity[],
    ) {
        const cloneValues = _.clone(values);

        const _values: Record<
            string,
            {
                rowKey?: string;
                value: any;
            }
        > = {};

        Object.entries(cloneValues).forEach(([key, data]) => {
            const [fieldId, collectionItemIdentityId, collectionItemKey] = key?.split('_');
            let _field =
                additionalFields?.find(
                    (field) =>
                        field.fieldId === fieldId &&
                        (!collectionItemIdentityId || field.formCollectionItemIdentityId === collectionItemIdentityId),
                ) || fields.find((field) => field.fieldId === fieldId);

            if (_field) {
                const _fieldValue = this.convertVariableValue(_field.type, data.value);
                _values[key] = {
                    rowKey: collectionItemKey,
                    value: _fieldValue,
                };
            }
        });

        return _values;
    }

    private combineValidationValue = async (params: {
        cloneTransactionFields: TransactionFieldEntity[];
        transactionFieldStyles: TransactionFieldStyleEntity[];
        _result: Array<CheckValidationResponseType>;
        formValues?: Record<string, any>;
    }) => {
        const activeOverrides: TransactionFieldOverrideEntity[] = [];
        const { cloneTransactionFields, transactionFieldStyles, _result, formValues } = params;

        const fieldIds = cloneTransactionFields.map((field) => field.id);
        const overrideValues = await this._transactionFieldOverrideRepo.find({
            where: {
                transactionFieldId: In(fieldIds),
                type: OverrideTypeEnum.User,
                status: Not(OverrideStatusEnum.UnOverride),
            },
        });

        cloneTransactionFields.forEach((transactionField) => {
            const fieldStyle = transactionFieldStyles.find((fs) => fs.id === transactionField.id);
            if (!fieldStyle) return;

            const overrides = overrideValues.filter((ov) => ov.transactionFieldId === transactionField.id);
            const overrideRecord = getOverrideRecord(cloneTransactionFields, overrides, formValues);
            if (!overrideRecord) return;
            activeOverrides.push(overrideRecord as TransactionFieldOverrideEntity);

            fieldStyle.configuration = {
                icon: overrideRecord.validationValue,
                label: overrideRecord.comment,
                border: fieldStyle.configuration.icon,
            };

            const responseItem = _result.find((i) => i.rowKey === transactionField.rowKey && i.fieldId === transactionField.fieldId);
            if (responseItem) {
                responseItem.validationValue = overrideRecord.validationValue;
                responseItem.label = overrideRecord.comment;
                responseItem.border = fieldStyle.configuration.border;
            }
        });

        return activeOverrides;
    };
}
