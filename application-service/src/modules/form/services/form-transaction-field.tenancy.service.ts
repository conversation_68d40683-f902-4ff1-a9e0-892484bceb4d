import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormTransactionFieldDto } from '../dtos';
import { EditFormTransactionFieldRequest } from '../dtos/requests/create-form-transaction.request';
import { FilterCollectionTransactionRequestDto } from '../dtos/requests/FilterCollectionTransaction.request.dto';
import {
    CreateTransactionFieldRequest,
    DeleteCollectionRowRequest,
    OverrideTransactionFieldRequest,
    UnOverrideValidationFieldRequest,
} from '../dtos/requests/transaction-field.request';
import { FormTransactionFieldDataService } from './data/form-transaction-field.data.service';

@Injectable()
export class FormTransactionFieldTenancyService {
    public get formTransactionFieldRepository(): Repository<TransactionFieldEntity> {
        return this._formTransactionFieldRepository;
    }
    constructor(
        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _formTransactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly _formTransactionRepository: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _tenantConnection: DataSource,

        private readonly _dataService: FormTransactionFieldDataService,
    ) {}

    public async getListCollectionTransField(
        query: FilterCollectionTransactionRequestDto,
        transactionId: string,
        formCollectionIdentityId: string,
        isTest?: boolean,
    ): Promise<FormTransactionFieldDto[]> {
        return this._dataService.getListCollectionTransField(query, transactionId, formCollectionIdentityId, isTest);
    }

    public async getTransactionFieldDetail(transactionFieldId: string): Promise<FormTransactionFieldDto> {
        const result = await this._dataService.getTransactionFieldDetail(transactionFieldId);

        return result;
    }

    public async createTransactionFields(request: CreateTransactionFieldRequest): Promise<FormTransactionFieldDto[]> {
        const result = await this._dataService.create({
            request,
            isTest: request.isTest,
        });
        return result;
    }

    public async updateTransactionField(request: EditFormTransactionFieldRequest): Promise<boolean> {
        const result = await this._dataService.updateField({
            request,
            formTransactionRepository: this._formTransactionRepository,
            formTransactionFieldRepository: this.formTransactionFieldRepository,
        });
        return result;
    }

    public async deleteCollectionRow(request: DeleteCollectionRowRequest): Promise<boolean> {
        const result = await this._dataService.deleteCollectionRow({
            request,
            formTransactionFieldRepository: this.formTransactionFieldRepository,
            isTest: request.isTest,
        });

        return result;
    }

    public async overrideTransactionField(request: OverrideTransactionFieldRequest): Promise<FormTransactionFieldDto> {
        const result = await this._dataService.overrideTransactionField({
            request,
            repository: this._tenantConnection,
        });

        return result;
    }

    public async unOverrideValidationValue(request: UnOverrideValidationFieldRequest): Promise<FormTransactionFieldDto> {
        const result = await this._dataService.unOverrideValidationValue({
            request,
            dataSource: this._tenantConnection,
        });

        return result;
    }
}
