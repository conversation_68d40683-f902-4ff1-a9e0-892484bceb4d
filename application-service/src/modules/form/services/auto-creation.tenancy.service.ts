import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { uuid4 } from '@sentry/utils';
import { Dictionary, groupBy } from 'lodash';
import { SELECTABLE_FIELD_TYPES } from 'src/database/src/constants/field';
import { Brackets, In, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { LoggerService, MqttService, UtilsService } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import {
    TransactionMQTTTopicEnum,
    TransactionTopicEnum,
} from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { DATA_PASSED_CODE, DEFAULT_STAGE_KPI_FIELD_ID, DEFAULT_TRANSACTION_FIELD_ID } from '../../../constant';
import { EVENT } from '../../../constant/event';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { StageEntity } from '../../../database/src/entities/public/stage.public.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormRelatedTenancyEntity } from '../../../database/src/entities/tenancy/form-related.tenancy.entity';
import { RelationTransactionEntity } from '../../../database/src/entities/tenancy/relation-transaction.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { StatusEnum } from '../../../database/src/enums/transaction-status.enum';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { AutoCreationEventDto, CreationRelatedTransactionEventDto } from '../dtos/requests/auto-creation.request';
import { EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';
import { AutoPopulateDataService } from './data/auto-populate.data.service';
import { FormTransactionDataService } from './data/form-transaction.data.service';
import { FormTransactionStatusTenancyService } from './form-transaction-status.tenancy.service';
import { FormTransactionTenancyService } from './form-transaction.tenancy.service';
import { PopulateTransactionFieldService } from './populate-transaction-field.service';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';

@Injectable()
export class AutoCreationTransactionService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_RELATED_TENANCY_REPOSITORY)
        private formRelatedRepository: Repository<FormRelatedTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_RELATION_TRANSACTION_REPOSITORY)
        private relationTransactionRepository: Repository<RelationTransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TENANCY_REPOSITORY)
        private formRepository: Repository<FormTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private transactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private autoPopulateRepository: Repository<FormAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.STAGE_TENANCY_REPOSITORY)
        private stageRepository: Repository<StageTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY)
        private formFieldRepository: Repository<FormFieldTenancyEntity>,

        private readonly formTransactionTenancyService: FormTransactionTenancyService,
        private readonly autoPopulateDataService: AutoPopulateDataService,
        private readonly eventEmitter: EventEmitter2,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _loggerService: LoggerService,
        private readonly _transactionStatus: FormTransactionStatusTenancyService,
        private readonly _formTransactionDataService: FormTransactionDataService,
        private readonly _mqttService: MqttService,
        private readonly _dataSourceService: DataSourceService,
    ) {}

    public async handleCreation(request: AutoCreationEventDto) {
        try {
            const { autoCreationStatus } = await this._transactionStatus.getTransactionStatus(request.transactionId);
            if (autoCreationStatus === StatusEnum.Processing) {
                return;
            }

            await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Processing);

            if (!request) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            const fieldIds = request.tranFields.map((item) => item.fieldId);
            if (!fieldIds?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            const formRelations = await this.getRelatedForms(request.formId);
            if (!formRelations?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            const _versionIds = formRelations.flatMap((item) => [
                item.firstForm?.activeVersionId ?? '',
                item.secondForm?.activeVersionId ?? '',
            ]);

            const versionIds = _versionIds.filter((versionId) => versionId !== request.formVersionId);

            const relatedFormFields = await this.getRelatedFormFields(versionIds);
            if (!relatedFormFields?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            // check config
            const autoCreationForms = this.getAutoCreationFormConfigs(formRelations, request.formId, request.tranFields, relatedFormFields);
            if (!autoCreationForms?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            // get auto populate fields
            const relatedFormIds = autoCreationForms.map((item) => item.formId);
            const formVersionIds = autoCreationForms.map((item) => item.formVersionId);
            const autoPopulatedFields = await this.autoPopulateDataService.getAutoPopulateFields(
                relatedFormIds,
                formVersionIds,
                this.autoPopulateRepository,
            );
            if (!autoPopulatedFields?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }
            const groupByForms = groupBy(autoPopulatedFields, 'originFormId');

            // get relation transaction created
            const relationTransactions = await this.getRelationTransaction(request.transactionId, relatedFormIds);
            const relatedTranIds = relationTransactions?.map((item) => item.targetTransactionId);
            const relatedTransactionFields = await this.transactionFieldRepository.find({
                where: {
                    transactionId: In(relatedTranIds),
                },
            });

            const newTransactions = await this.getNewTransactions(
                request.formId,
                autoCreationForms,
                groupByForms,
                relationTransactions ?? [],
                request.stageIdentityId,
                relatedTransactionFields,
                request.tranFields,
                relatedFormFields,
            );

            if (!newTransactions?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Processing);
            this._mqttService
                .publish(
                    `${RequestContextService.accountId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${request.transactionId}`,
                    {
                        id: v4(),
                        aggregateId: v4(),
                        metadata: {
                            transactionId: request.transactionId,
                            status: StatusEnum.Processing,
                            correlationId: RequestContextService.correlationId,
                            type: TransactionMQTTTopicEnum.FORM_RELATION_TRANSACTION_CREATED_STATUS,
                            relatedFormIds: relatedFormIds,
                        },
                    },
                    { qos: 1 },
                )
                .catch((error) => {
                    console.error(`Failed to publish MQTT message: ${error}`);
                });

            try {
                // create transaction fields for related forms new created transactions
                await this.createTransactions(newTransactions, request);
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
            } catch (err) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Failed);
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        ...request,
                        id: request.transactionId,
                    },
                    aggregateId: request.transactionId,
                    tenantId: request.accountId,
                    type: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
                    name: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
                });
                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
                this._loggerService.warn(err);
            }

            // await this.eventEmitter.emitAsync(EVENT.RELATED_TRANSACTION_COLLECTION_CREATE, {
            //     accountId: request.accountId,
            //     transactions: newTransactions,
            // });
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...request,
                    id: request.transactionId,
                },
                aggregateId: request.transactionId,
                tenantId: request.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_COMPLETED,
                name: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_COMPLETED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);

            return newTransactions;
        } catch (error) {
            await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Failed);
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...request,
                    id: request.transactionId,
                },
                aggregateId: request.transactionId,
                tenantId: request.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
                name: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
            this._loggerService.warn(error);
        }
    }

    public async handleRelatedCreationAction(request: CreationRelatedTransactionEventDto) {
        try {
            // const { autoCreationStatus } = await this._transactionStatus.getTransactionStatus(request.transactionId);
            // if (autoCreationStatus === StatusEnum.Processing) {
            //     return;
            // }

            await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Processing);

            if (!request) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            const fieldIds = request.tranFields.map((item) => item.fieldId);
            if (!fieldIds?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            // const formRelations = await this.getRelatedForms(request.formId);
            // if (!formRelations?.length) {
            //     await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
            //     return;
            // }

            // const _versionIds = formRelations.flatMap((item) => [
            //     item.firstForm?.activeVersionId ?? '',
            //     item.secondForm?.activeVersionId ?? '',
            // ]);

            // const versionIds = _versionIds.filter((versionId) => versionId !== request.formVersionId);

            const formRelation = await this.formRepository.findOne({
                where: {
                    id: request.relatedFormId,
                },
            });

            const versionIds = [formRelation.activeVersionId];

            const relatedFormFields = await this.getRelatedFormFields(versionIds);
            if (!relatedFormFields?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            // check config
            const autoCreationForms = this.getCreationFormConfigs(
                formRelation,
                request.formId,
                request.tranFields,
                relatedFormFields,
                request.configs,
            );
            if (!autoCreationForms?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            // get auto populate fields
            const relatedFormIds = autoCreationForms.map((item) => item.formId);
            const formVersionIds = autoCreationForms.map((item) => item.formVersionId);
            const autoPopulatedFields = await this.autoPopulateDataService.getAutoPopulateFields(
                relatedFormIds,
                formVersionIds,
                this.autoPopulateRepository,
            );
            if (!autoPopulatedFields?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }
            const groupByForms = groupBy(autoPopulatedFields, 'originFormId');

            const _fieldIds = autoPopulatedFields.reduce((prev, item) => {
                prev.push(item.originFieldId);
                prev.push(item.targetFieldId);
                return prev;
            }, []);

            // get relation transaction created
            const relationTransactions = await this.getRelationTransaction(request.transactionId, relatedFormIds);
            const relatedTranIds = relationTransactions?.map((item) => item.targetTransactionId);
            const relatedTransactionFields = await this.transactionFieldRepository.find({
                where: {
                    transactionId: In(relatedTranIds),
                    fieldId: In(_fieldIds),
                },
            });

            const newTransactions = await this.getNewTransactions(
                request.formId,
                autoCreationForms,
                groupByForms,
                relationTransactions ?? [],
                request.stageIdentityId,
                relatedTransactionFields,
                request.tranFields,
                relatedFormFields,
            );

            if (!newTransactions?.length) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
                return;
            }

            await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Processing);
            this._mqttService
                .publish(
                    `${RequestContextService.accountId}/${TransactionMQTTTopicEnum.FORM_TRANSACTION_UPDATED}/${request.transactionId}`,
                    {
                        id: v4(),
                        aggregateId: v4(),
                        metadata: {
                            transactionId: request.transactionId,
                            status: StatusEnum.Processing,
                            correlationId: RequestContextService.correlationId,
                            type: TransactionMQTTTopicEnum.FORM_RELATION_TRANSACTION_CREATED_STATUS,
                            relatedFormIds: relatedFormIds,
                        },
                    },
                    { qos: 1 },
                )
                .catch((error) => {
                    console.error(`Failed to publish MQTT message: ${error}`);
                });

            try {
                // create transaction fields for related forms new created transactions
                await this.createTransactions(newTransactions, request);
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Completed);
            } catch (err) {
                await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Failed);
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        ...request,
                        id: request.transactionId,
                    },
                    aggregateId: request.transactionId,
                    tenantId: request.accountId,
                    type: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
                    name: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
                });
                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
                this._loggerService.warn(err);
            }

            // await this.eventEmitter.emitAsync(EVENT.RELATED_TRANSACTION_COLLECTION_CREATE, {
            //     accountId: request.accountId,
            //     transactions: newTransactions,
            // });
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...request,
                    id: request.transactionId,
                },
                aggregateId: request.transactionId,
                tenantId: request.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_COMPLETED,
                name: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_COMPLETED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);

            return newTransactions;
        } catch (error) {
            await this._transactionStatus.setAutoCreationStatus(request.transactionId, StatusEnum.Failed);
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...request,
                    id: request.transactionId,
                },
                aggregateId: request.transactionId,
                tenantId: request.accountId,
                type: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
                name: TransactionEventEnum.FORM_TRANSACTION_AUTO_CREATION_STATUS_FAILED,
            });
            await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
            this._loggerService.warn(error);
        }
    }

    private async getRelatedFormFields(formVersionIds: string[]): Promise<FormFieldTenancyEntity[]> {
        if (!formVersionIds?.length) return [];

        return this.formFieldRepository.findBy({
            formVersionId: In(formVersionIds.filter((item) => item)),
        });
    }

    private async getTransactionField(formId: string, formField: FormFieldTenancyEntity): Promise<TransactionFieldEntity> {
        const transactionFieldId = await this.formTransactionTenancyService.generateTransactionIdValue({
            formId,
            formField,
            repository: this.formFieldRepository as any,
        });
        let transactionField = new TransactionFieldEntity();
        transactionField.fieldId = DEFAULT_TRANSACTION_FIELD_ID;
        transactionField.fieldValue = transactionFieldId;
        return transactionField;
    }

    private async createTransactions(newTransactions: any[], request: AutoCreationEventDto) {
        const originTransactionId = request.transactionId;
        if (!newTransactions?.length) {
            return;
        }
        const service = await this._dataSourceService.resolveService<FormTransactionTenancyService>(
            request.accountId,
            FormTransactionTenancyService,
        );

        const transactionDataService = await this._dataSourceService.resolveService<FormTransactionDataService>(
            request.accountId,
            FormTransactionDataService,
        );

        const newTransactionIds: string[] = [];
        const listResult: TransactionEntity[] = [];

        try {
            for (const newTransaction of newTransactions) {
                if (request?.user) {
                    newTransaction.createdBy = UtilsService.getUserId(request.user);
                    newTransaction.createdByUser = UtilsService.getUserFullName(request.user);
                    newTransaction.updatedBy = UtilsService.getUserId(request.user);
                    newTransaction.updatedByUser = UtilsService.getUserFullName(request.user);
                }
                const emptyTran = (await service.createEmptyTransaction({
                    formId: newTransaction.formId,
                    formVersionId: newTransaction.formVersionId,
                    timezone: '',
                    isReturnTrans: true,
                    skipPopulate: true,
                    skipValidation: true,
                    skipRollup: true,
                    skipMapDependLookupField: true,
                })) as TransactionEntity;

                newTransaction.id = emptyTran.id;

                // Auto populate from transaction field ( not collection items)
                const formEntity = await this._formTransactionDataService.getFormWithActiveVersion({
                    formId: newTransaction.formId,
                    formVersionId: newTransaction.formVersionId,
                });

                const formVersion = formEntity?.formVersions[0];
                const fieldTypeMap = {};
                formVersion.fields.forEach((item) => {
                    fieldTypeMap[item.fieldId] = item.type;
                });

                const transactionFields = emptyTran.transactionFields;
                const requestTransactionFields: TransactionFieldEntity[] = [];

                transactionFields.forEach((item) => {
                    const newItem = newTransaction.transactionFields?.find((f) => f.fieldId === item.fieldId);

                    if (newItem) {
                        item.fieldValue = newItem.fieldValue;
                        item.fieldOptionIds = newItem.fieldOptionIds || [];
                    }

                    item.fieldType = formVersion?.fields?.find((f) => f.fieldId === item.fieldId)?.type;
                    if (item.contextType !== TransactionFieldContextTypeEnum.COLLECTION) {
                        item.fieldOptionIds = SELECTABLE_FIELD_TYPES.includes(item.fieldType) ? item.fieldOptionIds || [] : [];
                        item.fieldValue = SELECTABLE_FIELD_TYPES.includes(item.fieldType)
                            ? (item.fieldOptionIds || []).join(',')
                            : item.fieldValue;
                    } else {
                        item.fieldValue = item.fieldOptionIds?.length ? (item.fieldOptionIds || []).join(',') : item.fieldValue;
                    }

                    if (newItem || item.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                        requestTransactionFields.push(item);
                    }
                });

                const formValues = transactionFields.reduce((prev, field) => {
                    if (field.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                        const collectionKey = UtilsService.combineCollectionKeys({
                            collectionIdentityId: field.collectionId,
                            collectionItemIdentityId: field.collectionItemId,
                            fieldIdentityId: field.fieldId,
                            collectionItemId: '',
                            fieldId: '',
                            collectionItemKey: field.rowKey,
                        });
                        prev[collectionKey] = field.fieldValue;
                        return prev;
                    }

                    prev[field.fieldId] = field.fieldValue;
                    return prev;
                }, {});

                const populatedTransactionFields = await this.getAutoPopulateValues({
                    accountId: request.accountId,
                    transactionId: emptyTran.id,
                    formFields: formVersion.fields,
                    transactionFields: transactionFields,
                });

                (populatedTransactionFields || []).forEach((tf) => {
                    switch (tf.contextType) {
                        case TransactionFieldContextTypeEnum.FORM:
                            formValues[tf.fieldId] = tf.fieldOptionIds?.length ? tf.fieldOptionIds : tf.fieldValue;
                            break;
                    }
                });

                const updateRequest: EditFormTransactionRequest = {
                    transactionId: emptyTran.id,
                    transactionFields: requestTransactionFields,
                    formId: newTransaction.formId,
                    formValues: formValues,
                };

                await transactionDataService.update({
                    id: emptyTran.id,
                    request: updateRequest,
                    user: request.user,
                    option: { shouldRunPopulateFormFields: true },
                });

                listResult.push(emptyTran);
                newTransactionIds.push(emptyTran.id);

                const relation = {
                    originTransactionId: originTransactionId,
                    targetTransactionId: emptyTran.id,
                    originFormId: request.formId,
                    targetFormId: formEntity.id,
                    type: 'related',
                } as RelationTransactionEntity;

                await this.relationTransactionRepository.insert(relation);
            }
            if (originTransactionId) {
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        id: originTransactionId,
                        originTransactionId: originTransactionId,
                        relationTransactionIds: newTransactionIds,
                    },
                    aggregateId: originTransactionId,
                    tenantId: RequestContextService.accountId,
                    type: TransactionEventEnum.FORM_RELATION_TRANSACTION_CREATED,
                    name: TransactionEventEnum.FORM_RELATION_TRANSACTION_CREATED,
                });
                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_REALTIME_TOPIC, message);
            }
        } catch (error) {
            throw error;
        }
        return listResult;
    }

    private async getRelatedForms(formId: string) {
        // get all form relations related to this form as follower
        const formRelationsBuilder = await this.formRelatedRepository
            .createQueryBuilder('fl')
            .leftJoinAndSelect('fl.firstForm', 'firstForms')
            .leftJoinAndSelect('fl.secondForm', 'secondForms');
        formRelationsBuilder.andWhere(
            new Brackets((qb) => {
                qb.orWhere(
                    new Brackets((qb1) => {
                        qb1.andWhere(`fl.firstFormId = :formId`, { formId: formId });
                    }),
                );
                qb.orWhere(
                    new Brackets((qb1) => {
                        qb1.andWhere(`fl.secondFormId = :formId`, { formId: formId });
                    }),
                );
            }),
        );
        return await formRelationsBuilder.getMany();
    }

    private getAutoCreationFormConfigs(
        formRelations: FormRelatedTenancyEntity[],
        formId: string,
        tranFields: TransactionFieldEntity[],
        relatedFormFields: FormFieldTenancyEntity[],
    ) {
        const autoCreationForms = [];
        formRelations?.forEach((item) => {
            const configs = item.configs;
            if (!item.firstForm || !item.secondForm) {
                return;
            }
            const form = item?.firstForm?.id === formId ? item.secondForm : item.firstForm;
            if (!form) {
                return;
            }
            if (configs?.autoCreation && configs.fieldId) {
                const tranField = tranFields?.find((field) => field.fieldId === configs.fieldId);
                if (tranField) {
                    autoCreationForms.push({
                        formId: form.id,
                        formVersionId: form.activeVersionId,
                        fieldId: tranField.fieldId,
                        fieldValue: tranField.fieldValue,
                        fieldOptionsIds: tranField.fieldOptionIds, // store option ids along with labels in fieldValue ex: id1,id2
                        transactionFormField: relatedFormFields?.find(
                            (f) => f.formVersionId === form.activeVersionId && f.fieldId === DEFAULT_TRANSACTION_FIELD_ID,
                        ),
                        stageId: configs.stageId,
                    });
                }
            }
        });
        return autoCreationForms;
    }

    private getCreationFormConfigs(
        formRelation: FormTenancyEntity,
        formId: string,
        tranFields: TransactionFieldEntity[],
        relatedFormFields: FormFieldTenancyEntity[],
        configs: {
            fieldId: string;
        },
    ) {
        const autoCreationForms = [];
        if (configs.fieldId) {
            const tranField = tranFields?.find((field) => field.fieldId === configs.fieldId);
            if (tranField) {
                autoCreationForms.push({
                    formId: formRelation.id,
                    formVersionId: formRelation.activeVersionId,
                    fieldId: tranField.fieldId,
                    fieldValue: tranField.fieldValue,
                    fieldOptionsIds: tranField.fieldOptionIds, // store option ids along with labels in fieldValue ex: id1,id2
                    transactionFormField: relatedFormFields?.find(
                        (f) => f.formVersionId === formRelation.activeVersionId && f.fieldId === DEFAULT_TRANSACTION_FIELD_ID,
                    ),
                    // stageId: configs.stageId,
                });
            }
        }
        return autoCreationForms;
    }

    private async getRelationTransaction(originTransactionId: string, relatedFormIds: string[]) {
        const relationData = await this.relationTransactionRepository.find({
            where: {
                originTransactionId: originTransactionId,
                targetFormId: In(relatedFormIds),
            },
        });
        return relationData;
    }

    private async getNewTransactions(
        formId: string,
        autoCreationForms: any[],
        groupByForms: Dictionary<FormAutoPopulateSettingTenancyEntity[]>,
        relationTransactions: RelationTransactionEntity[],
        stageIdentityId: string,
        relatedTransactionFields: TransactionFieldEntity[],
        transactionFields: TransactionFieldEntity[],
        relatedFormFields: FormFieldTenancyEntity[],
    ) {
        const newTransactions = [];
        for (const autoCreationForm of autoCreationForms) {
            const fields = groupByForms[autoCreationForm.formId]?.filter((f) => f.targetFormId === formId);
            const field = fields?.find((f) => f.targetFieldId === autoCreationForm?.fieldId);

            const existingTransactions = relationTransactions?.filter((item) => item.targetFormId === field?.originFormId);

            // if (autoCreationForm.stageId !== stageIdentityId) {
            //     continue;
            // }
            // get stages
            const formStages = await this.stageRepository.findBy({
                formVersionId: autoCreationForm?.formVersionId,
            });
            if (!formStages) {
                continue;
            }

            let startStage: StageEntity | StageTenancyEntity | null = null;
            formStages.map((stage) => {
                const config = stage.config;
                if (config.type === 'START') {
                    startStage = stage;
                }
            });

            if (!autoCreationForm || !field || !startStage) {
                continue;
            }
            const optionIds = autoCreationForm.fieldOptionsIds as string[];

            if (optionIds?.length) {
                let existingTranValue = [];
                // existingTransactions?.forEach((tranItem) => {
                relatedTransactionFields
                    ?.filter((tf) => tf.fieldOptionIds?.length)
                    ?.forEach((tf) => {
                        existingTranValue = [...existingTranValue, ...tf.fieldOptionIds];
                    });
                // });
                // const values = autoCreationForm.fieldValue?.split(',')?.filter((item) => item.trim()) ?? [];
                for (let index = 0; index < optionIds.length; index++) {
                    const id = optionIds[index];
                    if (existingTranValue.includes(id)) {
                        continue;
                    }
                    const transactionIdField = await this.getTransactionField(
                        autoCreationForm.formId,
                        autoCreationForm.transactionFormField ?? {},
                    );
                    let transactionStageKpiField = new TransactionFieldEntity();
                    transactionStageKpiField.fieldId = DEFAULT_STAGE_KPI_FIELD_ID;
                    transactionStageKpiField.fieldValue = DATA_PASSED_CODE.toString();
                    transactionStageKpiField.validationValue = DATA_PASSED_CODE;
                    transactionStageKpiField.fieldType = FormFieldTypeEnum.StageKpi;

                    const originField = relatedFormFields?.find((f) => f.fieldId === field.originFieldId);

                    const tran = {
                        originFormId: formId,
                        formVersionId: autoCreationForm?.formVersionId,
                        formId: autoCreationForm.formId,
                        fromFieldId: autoCreationForm.fieldId,
                        fromFieldValue: optionIds,
                        stage: startStage,
                        transactionFields: [
                            transactionIdField,
                            transactionStageKpiField,
                            {
                                fieldId: field.originFieldId,
                                fieldOptionIds: [id],
                                fieldValue: id ?? '',
                                fieldType: originField?.type,
                            },
                        ],
                    };
                    fields
                        .filter((fl) => fl.originFieldId !== field.originFieldId)
                        .forEach((fl) => {
                            if (!tran.transactionFields.some((tf) => tf.fieldId === fl.originFieldId)) {
                                const field = relatedFormFields?.find((f) => f.fieldId === fl.originFieldId);
                                if (field?.type !== FormFieldTypeEnum.Select) {
                                    tran.transactionFields.push({
                                        fieldId: fl.originFieldId,
                                        fieldType: field?.type,
                                        fieldOptionIds: transactionFields.find((f) => f.fieldId === fl.targetFieldId)?.fieldOptionIds ?? [],
                                        fieldValue: transactionFields.find((f) => f.fieldId === fl.targetFieldId)?.fieldValue ?? '',
                                    });
                                } else {
                                    if (field?.configuration?.mode === 'multiple') {
                                        const tfv = transactionFields.find((f) => f.fieldId === fl.targetFieldId)?.fieldValue ?? '';
                                        const labels = tfv?.split(',')?.map((item) => item.trim()) ?? [];

                                        const options = (field?.configuration?.options ?? [])?.filter(
                                            (item) => !!labels.find((label) => label?.toLowerCase() === item?.label?.toLowerCase()),
                                        );
                                        const optionIds = options?.map((item) => item.value) ?? [];

                                        tran.transactionFields.push({
                                            fieldId: fl.originFieldId,
                                            fieldOptionIds: optionIds,
                                            fieldType: field?.type,
                                            fieldValue: tfv ?? '',
                                        });
                                    } else {
                                        const tfv = transactionFields.find((f) => f.fieldId === fl.targetFieldId)?.fieldValue ?? '';
                                        const option = (field?.configuration?.options ?? [])?.find(
                                            (item) => item?.label?.toLowerCase() === tfv?.toLowerCase(),
                                        );

                                        tran.transactionFields.push({
                                            fieldId: fl.originFieldId,
                                            fieldType: field?.type,
                                            fieldOptionIds: option?.value ? [option?.value] : [],
                                            fieldValue: option?.label ?? '',
                                        });
                                    }
                                }
                            }
                        });
                    newTransactions.push(tran);
                }
            } else {
                if (!autoCreationForm.fieldValue || existingTransactions?.length) {
                    continue;
                }

                const tranId = uuid4();
                const transactionIdField = await this.getTransactionField(
                    autoCreationForm.formId,
                    autoCreationForm.transactionFormField ?? {},
                );
                let transactionStageKpiField = new TransactionFieldEntity();
                transactionStageKpiField.fieldId = DEFAULT_STAGE_KPI_FIELD_ID;
                transactionStageKpiField.fieldValue = DATA_PASSED_CODE.toString();
                transactionStageKpiField.validationValue = DATA_PASSED_CODE;
                transactionStageKpiField.fieldType = FormFieldTypeEnum.StageKpi;

                const originField = relatedFormFields?.find((f) => f.fieldId === field.originFieldId);

                const tran = {
                    id: tranId,
                    originFormId: formId,
                    formVersionId: autoCreationForm?.formVersionId,
                    formId: autoCreationForm.formId,
                    fromFieldId: autoCreationForm.fieldId,
                    fromFieldValue: autoCreationForm.fieldValue,
                    stage: startStage,
                    transactionFields: [
                        transactionIdField,
                        transactionStageKpiField,
                        {
                            transactionId: tranId,
                            fieldId: field.originFieldId,
                            fieldValue: autoCreationForm.fieldValue ?? '',
                            fieldType: originField?.type,
                        } as unknown as TransactionFieldEntity,
                        ...fields
                            .filter((fl) => fl.originFieldId !== field.originFieldId)
                            .map((fl) => {
                                const mappedField = transactionFields.find((f) => f.fieldId === fl.targetFieldId);
                                return {
                                    fieldId: fl.originFieldId,
                                    fieldOptionIds: mappedField?.fieldOptionIds ?? [],
                                    fieldValue: mappedField?.fieldValue ?? '',
                                    fieldType: mappedField?.fieldType ?? null,
                                };
                            }),
                    ],
                } as unknown as TransactionEntity;
                newTransactions.push(tran);
            }
        }
        return newTransactions;
    }

    private async getAutoPopulateValues({
        accountId,
        transactionId,
        formFields,
        transactionFields,
    }: {
        accountId: string;
        transactionId: string;
        formFields: FormFieldTenancyEntity[];
        transactionFields: any;
    }) {
        const populateFieldService = await this._dataSourceService.resolveService<PopulateTransactionFieldService>(
            accountId,
            PopulateTransactionFieldService,
        );

        try {
            const populateFields = await populateFieldService.populateTransactionFields(
                {
                    formVersionFields: formFields,
                    transactionFields: (transactionFields || []).map((rf) => {
                        return {
                            id: rf.id,
                            fieldId: rf.fieldId,
                            fieldValue: rf.fieldOptionIds?.length ? rf.fieldOptionIds.join(',') : rf.fieldValue,
                            fieldOptionIds: rf.fieldOptionIds,
                            fieldType: rf.fieldType,
                            collectionId: rf.collectionId,
                            collectionItemId: rf.collectionItemId,
                            contextType: rf.contextType,
                            transactionId: transactionId,
                            rowKey: rf.rowKey,
                        } as TransactionFieldEntity;
                    }),
                },
                transactionId,
            );

            return populateFields;
        } catch (err) {
            this._loggerService.error(`Error populate transaction fields on automation for creation transaction: ${err}`);
        }
        return [];
    }
}
