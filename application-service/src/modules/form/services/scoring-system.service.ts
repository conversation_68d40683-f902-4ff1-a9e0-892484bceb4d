import { Inject, Injectable } from '@nestjs/common';
import { isNumberString } from 'class-validator';
import { cloneDeep, Dictionary, groupBy, isEmpty, merge } from 'lodash';
import { PRIORITY_FIELD_ID } from 'src/common/src/constant/field';
import { EventDrivenService } from 'src/common/src/modules/event-driven/event-driven.service';
import { TransactionTopicEnum } from 'src/common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import Formula from 'src/common/src/modules/shared/formula-calculator/formula';
import { PROVIDER_KEYS } from 'src/database/src/constants/providers';
import { DataRegisterFieldEntity } from 'src/database/src/entities/public/data-register-fields.public.entity';
import { FormCollectionTenancyEntity } from 'src/database/src/entities/tenancy/form-collection.tenancy.entity';
import { TransactionFieldEntity } from 'src/database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from 'src/database/src/entities/tenancy/transaction.tenancy.entity';
import { SourceOfChangeType } from 'src/database/src/shared/enums/change-log.type.enum';
import { DataRegisterTypeEnum } from 'src/database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from 'src/database/src/shared/enums/form-field-type.enum';
import { checkRowConditionFilters } from 'src/utils/check-row-condition-filters';
import { In, Repository } from 'typeorm';
import { v7, validate } from 'uuid';
import { LoggerService, UtilsService } from '../../../common/src';
import { RequestContextService } from '../../../common/src/application/context/AppRequestContext';
import { TransactionEventEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-event.enum';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { EditFormTransactionRequest } from '../dtos/requests/create-form-transaction.request';
import {
    CollectionScoreResult,
    IdentifierMapping,
    ScoreModelDataSourceEnum,
    ScoreOperator,
    ScoringModel,
    ScoringQuestion,
    ScoringSystemDto,
    VariableMapping,
} from '../dtos/scring-system.dto';
import { FormTransactionDataService } from './data/form-transaction.data.service';

type RowKey = string;
type Priority = string;
type QuestionId = string;

export type CalculateParams = {
    actionId: string;
    actionName: string;
    functionType: string;
    external: {
        fields: TransactionFieldEntity[];
        formId: string;
        formVersionId: string;
    };
    context: {
        type: string;
        id: string;
        transactionId: string;
        versionId: string;
    };
    configuration: {
        scoringSettings: ScoringSystemDto;
    };

    accountId?: string;
};
@Injectable()
export class ScoringSystemService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly formTransactionRepo: Repository<TransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly formTransactionFieldRepo: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly formCollectionRepo: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly dataRegisterFieldRepo: Repository<DataRegisterFieldEntity>,

        private readonly _eventDrivenService: EventDrivenService,

        private readonly _dataSourceService: DataSourceService,

        private readonly _loggerService: LoggerService,
    ) {}

    public async calculateWithFieldResult(params: CalculateParams) {
        /** OVID - Result type is form field
         *  1. Get all collection transaction fields by formCollection parameter
         *  2. Loop through scoringModels
         *  3. Determine selected rows of template
         *  4. Group collection fields by identifierFields and identifierMapping
         *  -  Output: a set of rows which are use to calculate score grouped by rowKey
         *  5. Formula:
         *      - Get variables values:
         *          - prioritySettings: group by prioritySettings
         *          - run conditions: run conditions
         *          - apply operator: apply operator
         *      - Execute formula
         *  6. Save result transaction fields
         *  7. Emit transaction field update event
         */

        const { scoringSettings } = params.configuration ?? {};
        const { formCollection, dataSource } = scoringSettings ?? {};
        const { transactionId, versionId } = params.context ?? {};

        const { identifierMapping, scoringModels, template } = scoringSettings;

        const templateQuestionsMap: Record<QuestionId, ScoringQuestion> = {};
        for (const question of template.questions) {
            templateQuestionsMap[question.id] = question;
        }

        // step 1: get all collection transaction fields by formCollection parameter

        const transaction = await this._getTransaction(transactionId);

        if (!transaction) {
            throw new Error('transaction_not_found');
        }

        const collectionTransactionFields = await this._getCollectionTransactionFields(transactionId, formCollection);

        const collectionTransactionFieldsByRowKey: Dictionary<TransactionFieldEntity[]> = groupBy(collectionTransactionFields, 'rowKey');

        // Get register fields once at the higher level
        const formCollectionEntity = await this.formCollectionRepo.findOne({
            where: {
                formVersionId: versionId,
                identityId: formCollection,
            },
        });

        if (!formCollectionEntity) {
            throw new Error('Form collection not found');
        }

        const { dataRegisterVersionId } = formCollectionEntity;
        let registerFields = await this.dataRegisterFieldRepo.find({
            where: {
                dataRegisterVersionId: dataRegisterVersionId,
            },
        });

        registerFields = registerFields.filter((field) => field.configuration?.isSupportField?.toString() === 'true');

        // step 2: loop through scoringModels
        const models = Object.values(scoringModels);

        const resultFieldEntities: TransactionFieldEntity[] = [];
        const previous: TransactionFieldEntity[] = [];
        for (const model of models) {
            const formulaResults = await this._calculateModelScore(
                dataSource,
                model,
                templateQuestionsMap,
                identifierMapping,
                collectionTransactionFieldsByRowKey,
                versionId,
                formCollection,
                registerFields,
            );

            if (!formulaResults?.length) {
                console.warn(`missing_result_field_configuration: ${model.name}`);
                continue;
            }

            const fieldIds = formulaResults.map((formulaResult) => formulaResult.formFieldId);

            const transactionFieldEntities = await this.formTransactionFieldRepo.find({
                where: {
                    transactionId: transactionId,
                    fieldId: In(fieldIds),
                },
            });

            if (!transactionFieldEntities?.length) {
                console.warn(`missing_result_field_configuration: ${model.name}`);
                continue;
            }

            previous.push(...cloneDeep(transactionFieldEntities));

            for (const formulaResult of formulaResults) {
                const resultFieldEntity = transactionFieldEntities.find(
                    (resultFieldEntity) => resultFieldEntity.fieldId === formulaResult.formFieldId,
                );
                if (!resultFieldEntity) {
                    console.warn(`missing_result_field_configuration: ${model.name}`);
                    continue;
                }

                resultFieldEntity.fieldValue = formulaResult.result.toString();
                resultFieldEntities.push(resultFieldEntity);
            }
        }

        // 6. Save result transaction fields
        const updatedFields = await this.formTransactionFieldRepo.save(resultFieldEntities);

        // 7. Emit transaction field update event
        const message = EventDrivenService.createCommonEvent({
            payload: {
                ...transaction,
                fields: updatedFields,
                sourceOfChange: SourceOfChangeType.AUTOMATION,
                previous,
            },
            tenantId: RequestContextService.accountId,
            aggregateId: transactionId,
            type: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
            name: TransactionEventEnum.FORM_TRANSACTION_FIELD_UPDATED,
        });
        await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);
    }

    public async calculateWithCollectionResult(params: CalculateParams) {
        /** OVMSA - Result type is collection
         *  1. Get all collection transaction fields by formCollection parameter
         *  2. Loop through scoringModels
         *  3. Determine selected rows of template
         *  4. Group collection fields by identifierFields and identifierMapping
         *      -  Output: a set of rows which are use to calculate score grouped by rowKey
         *  6. Get values set of row and column from configs: rowMapping, columnMapping
         *  5. Score calculation:
         *      - Nested loop:
         *          - 1st loop: loop through rows values
         *          - 2nd loop: loop through columns values
         *      - Calculate score of each cell:
         *          - Get variables values form scoreField
         *          - Apply operator: apply operator
         *  6. Map score to target collection
         *  7. Save target collection fields
         *  8. Emit collection field update event
         */

        const { scoringSettings } = params.configuration ?? {};
        const { formCollection, dataSource } = scoringSettings ?? {};
        const { transactionId, versionId } = params.context ?? {};

        const { identifierMapping, scoringModels, template } = scoringSettings;

        const templateQuestionsMap: Record<QuestionId, ScoringQuestion> = {};
        for (const question of template.questions) {
            templateQuestionsMap[question.id] = question;
        }

        // step 1: get all collection transaction fields by formCollection parameter

        const transaction = await this._getTransaction(transactionId);

        if (!transaction) {
            throw new Error('transaction_not_found');
        }

        const collectionTransactionFields = await this._getCollectionTransactionFields(transactionId, formCollection);

        const collectionTransactionFieldsByRowKey: Dictionary<TransactionFieldEntity[]> = groupBy(collectionTransactionFields, 'rowKey');

        // Get register fields once at the higher level
        const formCollectionEntity = await this.formCollectionRepo.findOne({
            where: {
                formVersionId: versionId,
                identityId: formCollection,
            },
        });

        if (!formCollectionEntity) {
            throw new Error('Form collection not found');
        }

        const { dataRegisterVersionId } = formCollectionEntity;
        let registerFields = await this.dataRegisterFieldRepo.find({
            where: {
                dataRegisterVersionId: dataRegisterVersionId,
            },
        });

        registerFields = registerFields.filter((field) => field.configuration?.isSupportField?.toString() === 'true');

        // step 2: loop through scoringModels
        const models = Object.values(scoringModels);

        const resultFieldEntities: TransactionFieldEntity[] = [];

        const resultCollectionIds: string[] = [];

        let formValues: Record<string, any> = {};

        for (const model of models) {
            const formulaResults = await this._calculateModelScoreWithCollectionResult(
                dataSource,
                model,
                templateQuestionsMap,
                identifierMapping,
                collectionTransactionFieldsByRowKey,
                versionId,
                formCollection,
                registerFields,
            );

            if (!formulaResults || isEmpty(formulaResults)) {
                console.warn(`missing_result_field_configuration: ${model.name}`);
                continue;
            }

            if (!model.resultCollection) {
                console.warn(`missing_result_collection_configuration: ${model.name}`);
                continue;
            }

            const formCollectionEntity = await this.formCollectionRepo.findOne({
                where: {
                    identityId: model.resultCollection,
                    formVersionId: versionId,
                },
                relations: {
                    formCollectionItems: true,
                },
            });

            if (!formCollectionEntity || !formCollectionEntity.formCollectionItems.length) {
                console.warn(`missing_result_field_configuration: ${model.name}`);
                continue;
            }

            const collectionFieldEntities: TransactionFieldEntity[] = [];

            const firstItem = formCollectionEntity.formCollectionItems[0];

            await this.formTransactionFieldRepo.softDelete({
                transactionId,
                collectionId: model.resultCollection,
            });

            Object.entries(formulaResults).forEach(([groupValue, row], index) => {
                const rowKey = v7();

                const groupFieldEntity = this._createTransactionFieldEntity(
                    v7(),
                    transactionId,
                    model.resultCollection,
                    model.groupByTargetField,
                    groupValue,
                    rowKey,
                    firstItem.identityId,
                    index + 1,
                );
                collectionFieldEntities.push(groupFieldEntity);

                Object.entries(row).forEach(([fieldId, fieldValue]) => {
                    const resultFieldEntity = this._createTransactionFieldEntity(
                        v7(),
                        transactionId,
                        model.resultCollection,
                        fieldId,
                        fieldValue.result.toString(),
                        rowKey,
                        firstItem.identityId,
                        index + 1,
                    );
                    collectionFieldEntities.push(resultFieldEntity);
                });
            });

            formValues = merge(
                formValues,
                collectionFieldEntities.reduce((acc, curr) => {
                    const key = UtilsService.combineCollectionKeys({
                        collectionIdentityId: curr.collectionId,
                        collectionItemKey: curr.rowKey,
                        collectionItemId: firstItem.id,
                        collectionItemIdentityId: curr.collectionItemId,
                        fieldId: curr.fieldId,
                        fieldIdentityId: curr.id,
                    });
                    acc[key] = curr.fieldValue;
                    return acc;
                }, {}),
            );
            resultCollectionIds.push(model.resultCollection);
            resultFieldEntities.push(...collectionFieldEntities);
        }

        const updateRequest: EditFormTransactionRequest = {
            transactionFields: resultFieldEntities,
            transactionId,
            formId: transaction.formId,
            formValues: formValues,
        };

        const service = await this._dataSourceService.resolveService<FormTransactionDataService>(
            params.accountId,
            FormTransactionDataService,
        );

        await service.update({
            id: transactionId,
            request: updateRequest,
            user: RequestContextService.currentUser(),
            option: {
                shouldRunPopulateFormFields: false,
                forceRunAutoPopulate: false,
                ignoreAutoPopulateCollection: true, //!if set this option true, please make sure not forceRunAutoPopulate
            },
        });

        for (const collectionId of resultCollectionIds) {
            if (validate(collectionId)) {
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        id: transaction.id,
                        collectionId,
                        sourceOfChangeType: SourceOfChangeType.AUTOMATION,
                    },
                    tenantId: RequestContextService.accountId,
                    aggregateId: transaction.id,
                    type: TransactionEventEnum.FORM_TRANSACTION_COLLECTION_AUTO_POPULATED,
                    name: TransactionEventEnum.FORM_TRANSACTION_COLLECTION_AUTO_POPULATED,
                });
                this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_COLLECTION_TOPIC, message);
            }
        }
    }

    private _createTransactionFieldEntity(
        id: string,
        transactionId: string,
        collectionId: string,
        fieldId: string,
        fieldValue: string,
        rowKey: string,
        collectionItemId: string,
        order: number,
    ): TransactionFieldEntity {
        const resultFieldEntity = new TransactionFieldEntity();
        resultFieldEntity.transactionId = transactionId;
        resultFieldEntity.collectionId = collectionId;
        resultFieldEntity.fieldId = fieldId;
        resultFieldEntity.fieldValue = fieldValue?.toString();
        resultFieldEntity.rowKey = rowKey;
        resultFieldEntity.collectionItemId = collectionItemId;
        resultFieldEntity.contextType = TransactionFieldContextTypeEnum.COLLECTION;
        resultFieldEntity.id = id;
        resultFieldEntity.data = {
            scoringOrder: order,
        };
        return resultFieldEntity;
    }

    private async _getTransaction(transactionId: string): Promise<TransactionEntity> {
        const transaction = await this.formTransactionRepo.findOne({
            where: {
                id: transactionId,
            },
            withDeleted: true, // Purpose for Testing Step on Form Builder
        });

        // Purpose for Testing Step on Form Builder
        if (!transaction || (!transaction.isTest && transaction.deletedAt)) {
            throw new Error('transaction_not_found');
        }
        return transaction;
    }

    private async _getCollectionTransactionFields(transactionId: string, formCollection: string): Promise<TransactionFieldEntity[]> {
        return this.formTransactionFieldRepo.find({
            where: {
                transactionId: transactionId,
                collectionId: formCollection,
            },
        });
    }

    private async _calculateModelScore(
        dataSource: ScoreModelDataSourceEnum,
        model: ScoringModel,
        templateQuestionsMap: Record<QuestionId, ScoringQuestion>,
        identifierMapping: IdentifierMapping[],
        collectionTransactionFieldsByRowKey: Record<RowKey, TransactionFieldEntity[]>,
        versionId: string,
        formCollection: string,
        registerFields: any[],
    ): Promise<{ formFieldId: string; result: number }[]> {
        let { questions, scoreFormulas } = model;

        // step 3: Determine selected rows of template
        questions = questions.filter((question) => question.selected);

        // step 4: Group collection fields by identifierFields and identifierMapping
        const { rowPriorityMap } = this._getRowPriorityMap(
            dataSource,
            model,
            templateQuestionsMap,
            identifierMapping,
            collectionTransactionFieldsByRowKey,
        );

        // step 5: Formula:
        // 5.1 & 5.2 Get variables values (run conditions, apply operator)

        const formulaResults: { formFieldId: string; result: number }[] = [];

        const formulaVariables: Record<string, any> = {};

        for (const scoreFormula of scoreFormulas) {
            const priorityResultMap = await this._evaluateConditionsAndGetPriorityResultMap(
                scoreFormula.variableMappings,
                rowPriorityMap,
                collectionTransactionFieldsByRowKey,
                versionId,
                formCollection,
                registerFields,
            );

            // 5.3 Execute formula
            const variableValues: Record<string, number> = { ...priorityResultMap };

            for (const variableMapping of scoreFormula.variableMappings) {
                variableValues[variableMapping.variable] = priorityResultMap[variableMapping.variable] ?? 0;
                if (variableMapping.variableType !== 'expression') continue;

                switch (dataSource) {
                    case ScoreModelDataSourceEnum.FIXED: {
                        variableValues[variableMapping.variable] = formulaVariables[variableMapping.formulaId] ?? 0;
                        break;
                    }
                    case ScoreModelDataSourceEnum.OVIQ: {
                        variableValues[variableMapping.variable] = formulaVariables[variableMapping.formulaId] ?? 0;
                        break;
                    }
                    case ScoreModelDataSourceEnum.OVMSA: {
                        variableValues[variableMapping.variable] = formulaVariables[variableMapping.formulaId] ?? 0;
                        break;
                    }
                    default:
                        break;
                }
            }

            try {
                const calc = new Formula(scoreFormula.expression);
                const value = calc.calc(variableValues, FormFieldTypeEnum.Number) as number;

                const formulaVariable = scoreFormula.id;
                formulaVariables[formulaVariable] = value;

                formulaResults.push({
                    formFieldId: scoreFormula.resultField,
                    result: value,
                });
            } catch (error) {
                this._loggerService.error(error);
            }
        }

        return formulaResults;
    }

    private async _calculateModelScoreWithCollectionResult(
        dataSource: ScoreModelDataSourceEnum,
        model: ScoringModel,
        templateQuestionsMap: Record<QuestionId, ScoringQuestion>,
        identifierMapping: IdentifierMapping[],
        collectionTransactionFieldsByRowKey: Record<RowKey, TransactionFieldEntity[]>,
        versionId: string,
        formCollection: string,
        registerFields: any[],
    ): Promise<CollectionScoreResult> {
        const { questions, scoreFormulas } = model;

        // step 3: Determine selected rows of template
        // questions = questions.filter((question) => question.selected);

        // step 4: Group collection fields by identifierFields and identifierMapping
        const { rowPriorityMap, groupRowsByFieldIdMap } = this._getRowPriorityMap(
            dataSource,
            model,
            templateQuestionsMap,
            identifierMapping,
            collectionTransactionFieldsByRowKey,
        );

        // step 5: Formula:
        // 5.1 & 5.2 Get variables values (run conditions, apply operator)

        // const formulaResults: { formFieldId: string; result: number }[] = [];

        const formulaResults: CollectionScoreResult = {};

        const formulaVariables: Record<string, any> = {};

        for (const scoreFormula of scoreFormulas) {
            const groupedValueIds = Object.keys(groupRowsByFieldIdMap);

            for (const groupedValue of groupedValueIds) {
                const { rowKeys } = groupRowsByFieldIdMap[groupedValue];

                const shouldCalculateRows: Record<RowKey, TransactionFieldEntity[]> = {};
                rowKeys.forEach((rowKey) => {
                    shouldCalculateRows[rowKey] = collectionTransactionFieldsByRowKey[rowKey];
                });

                const priorityResultMap = await this._evaluateConditionsAndGetPriorityResultMap(
                    scoreFormula.variableMappings,
                    rowPriorityMap,
                    shouldCalculateRows,
                    versionId,
                    formCollection,
                    registerFields,
                );

                // 5.3 Execute formula
                const variableValues: Record<string, number> = { ...priorityResultMap };

                for (const variableMapping of scoreFormula.variableMappings) {
                    variableValues[variableMapping.variable] = priorityResultMap[variableMapping.variable] ?? 0;
                    if (variableMapping.variableType !== 'expression') continue;

                    switch (dataSource) {
                        case ScoreModelDataSourceEnum.FIXED: {
                            variableValues[variableMapping.variable] = formulaVariables[variableMapping.formulaId] ?? 0;
                            break;
                        }
                        case ScoreModelDataSourceEnum.OVIQ: {
                            variableValues[variableMapping.variable] = formulaVariables[variableMapping.formulaId] ?? 0;
                            break;
                        }
                        case ScoreModelDataSourceEnum.OVMSA: {
                            variableValues[variableMapping.variable] = formulaVariables[variableMapping.formulaId] ?? 0;
                            break;
                        }
                        default:
                            break;
                    }
                }

                try {
                    const calc = new Formula(scoreFormula.expression);
                    const value = calc.calc(variableValues, FormFieldTypeEnum.Number) as number;

                    const formulaVariable = scoreFormula.id;
                    formulaVariables[formulaVariable] = value;

                    if (!formulaResults[groupedValue]) {
                        formulaResults[groupedValue] = {
                            [scoreFormula.resultField]: {
                                result: value,
                            },
                        };

                        if (!formulaResults[groupedValue][scoreFormula.resultField]) {
                            formulaResults[groupedValue][scoreFormula.resultField] = {
                                result: value,
                            };
                        }
                    }

                    formulaResults[groupedValue][scoreFormula.resultField] = { result: value };
                } catch (error) {
                    this._loggerService.error(error);
                }
            }
        }

        return formulaResults;
    }

    private _getRowPriorityMap(
        dataSource: ScoreModelDataSourceEnum,
        model: ScoringModel,
        templateQuestionsMap: Record<QuestionId, ScoringQuestion>,
        identifierMapping: IdentifierMapping[],
        collectionTransactionFieldsByRowKey: Record<RowKey, TransactionFieldEntity[]>,
    ): {
        rowPriorityMap: Record<RowKey, Priority>;
        groupRowsByFieldIdMap?: Record<
            string,
            {
                rowKeys: RowKey[];
                groupByFieldId: string;
                targetFieldId: string;
            }
        >;
    } {
        switch (dataSource) {
            case ScoreModelDataSourceEnum.FIXED: {
                return {
                    rowPriorityMap: this._getFixedRowPriorityMap(templateQuestionsMap, collectionTransactionFieldsByRowKey),
                };
            }
            case ScoreModelDataSourceEnum.OVIQ:
            case ScoreModelDataSourceEnum.OVMSA: {
                return this._getDynamicRowPriorityMap(model, templateQuestionsMap, identifierMapping, collectionTransactionFieldsByRowKey);
            }
        }
    }

    private _getFixedRowPriorityMap(
        templateQuestionsMap: Record<QuestionId, ScoringQuestion>,
        collectionTransactionFieldsByRowKey: Record<RowKey, TransactionFieldEntity[]>,
    ): Record<RowKey, Priority> {
        const rowPriorityMap: Record<RowKey, Priority> = {};

        const rows = Object.entries(collectionTransactionFieldsByRowKey);
        for (const [rowKey, row] of rows) {
            if (!row.length) {
                rowPriorityMap[rowKey] = null;
                continue;
            }

            const collectionItemIdentityId = row[0].collectionItemId;
            const question = templateQuestionsMap[collectionItemIdentityId];
            if (!question) {
                rowPriorityMap[rowKey] = null;
                continue;
            }

            rowPriorityMap[rowKey] = question.priority;
        }

        return rowPriorityMap;
    }

    private _getDynamicRowPriorityMap(
        model: ScoringModel,
        templateQuestionsMap: Record<QuestionId, ScoringQuestion>,
        identifierMapping: IdentifierMapping[],
        collectionTransactionFieldsByRowKey: Record<RowKey, TransactionFieldEntity[]>,
    ): {
        rowPriorityMap: Record<RowKey, Priority>;
        groupRowsByFieldIdMap: Record<
            string,
            {
                rowKeys: RowKey[];
                groupByFieldId: string;
                targetFieldId: string;
            }
        >;
    } {
        const identifierTemplateFieldIds = identifierMapping.map((mapping) => mapping.templateField);
        const identifierCollectionFieldIds = identifierMapping.map((mapping) => mapping.collectionField);
        const rowPriorityMap: Record<RowKey, Priority> = {};

        const { groupByField, groupByTargetField } = model;

        const groupRowsByFieldIdMap: Record<
            string,
            {
                rowKeys: RowKey[];
                groupByFieldId: string;
                targetFieldId: string;
            }
        > = {};

        for (const question of model.questions) {
            const templateQuestion = templateQuestionsMap[question.id];
            question.priority = templateQuestion.priority;
            const identifierTemplateValues = identifierTemplateFieldIds.map((templateFieldId) => templateQuestion[templateFieldId]);

            for (const rowKey in collectionTransactionFieldsByRowKey) {
                const collectionRowData = collectionTransactionFieldsByRowKey[rowKey];

                // Optimize lookup for fields within the current collectionRow
                const collectionRowFieldsMap = new Map<string, string>();
                for (const field of collectionRowData) {
                    collectionRowFieldsMap.set(field.fieldId, field.fieldValue);
                }

                const identifierCollectionValues: (string | undefined)[] = [];
                for (const collectionFieldId of identifierCollectionFieldIds) {
                    identifierCollectionValues.push(collectionRowFieldsMap.get(collectionFieldId));
                }

                // Ensure all identifiers match
                let allIdentifiersMatch = true;
                if (identifierTemplateValues.length !== identifierCollectionValues.length) {
                    // This case should ideally not happen if identifierMappings are consistent
                    allIdentifiersMatch = false;
                } else {
                    for (let i = 0; i < identifierTemplateValues.length; i++) {
                        // A collection field is considered not matching if it's missing (undefined) or its value differs.
                        if (identifierCollectionValues[i] === undefined || identifierTemplateValues[i] !== identifierCollectionValues[i]) {
                            allIdentifiersMatch = false;
                            break;
                        }
                    }
                }

                if (allIdentifiersMatch) {
                    if (!groupRowsByFieldIdMap[question[groupByField]]) {
                        groupRowsByFieldIdMap[question[groupByField]] = {
                            rowKeys: [],
                            groupByFieldId: groupByField,
                            targetFieldId: groupByTargetField,
                        };
                    }

                    groupRowsByFieldIdMap[question[groupByField]].rowKeys.push(rowKey);

                    rowPriorityMap[rowKey] = question.priority;
                }
            }
        }
        return {
            rowPriorityMap,
            groupRowsByFieldIdMap,
        };
    }

    private async _evaluateConditionsAndGetPriorityResultMap(
        scoreFormulaVariableMappings: VariableMapping[],
        rowPriorityMap: Record<RowKey, Priority>,
        collectionTransactionFieldsByRowKey: Record<RowKey, TransactionFieldEntity[]>,
        versionId: string,
        formCollection: string,
        registerFields: any[],
    ): Promise<Record<Priority, number>> {
        const priorityResultMap: Record<Priority, number> = {};

        for (const variableMapping of scoreFormulaVariableMappings) {
            const { field, operator, variable, conditions, variableType } = variableMapping;

            if (variableType === 'expression') continue;

            const matchedRows: Record<RowKey, TransactionFieldEntity[]> = {};

            const rowsMap = Object.entries(collectionTransactionFieldsByRowKey);
            for (const [rowKey, row] of rowsMap) {
                const result = checkRowConditionFilters({
                    collectionFilters: [conditions],
                    fieldsOfRow: [
                        ...row,
                        {
                            fieldId: PRIORITY_FIELD_ID,
                            fieldValue: rowPriorityMap[rowKey],
                            fieldOptionIds: [rowPriorityMap[rowKey]],
                            transactionId: '',
                        },
                    ],
                    drFields: [
                        ...registerFields,
                        {
                            fieldId: PRIORITY_FIELD_ID,
                            type: FormFieldTypeEnum.Select,
                            label: '',
                            configuration: {
                                mode: 'single',
                                options: [],
                            },
                            registerType: DataRegisterTypeEnum.Standard,
                        },
                    ],
                });

                if (result) {
                    matchedRows[rowKey] = row;
                }
            }

            const getFieldValue = (row: TransactionFieldEntity[], fieldId: string): number => {
                const transactionField = row.find((r) => r.fieldId === fieldId);
                switch (transactionField?.fieldType) {
                    case FormFieldTypeEnum.Select:
                        if (!transactionField?.fieldOptionIds?.length) return 0;
                        if (isNumberString(transactionField.fieldOptionIds[0])) return Number(transactionField.fieldOptionIds[0]) || 0;
                        return 0;
                    default:
                        return Number(transactionField?.fieldValue ?? '0') || 0;
                }
            };

            switch (operator) {
                case ScoreOperator.COUNT: {
                    priorityResultMap[variable] = Object.keys(matchedRows).length;
                    break;
                }

                case ScoreOperator.SUM: {
                    priorityResultMap[variable] = Object.values(matchedRows).reduce((acc, row) => {
                        return acc + getFieldValue(row, field.id);
                    }, 0);
                    break;
                }

                case ScoreOperator.MAX: {
                    priorityResultMap[variable] = Math.max(...Object.values(matchedRows).map((row) => getFieldValue(row, field.id))) ?? 0;
                    break;
                }

                case ScoreOperator.MIN: {
                    priorityResultMap[variable] = Math.min(...Object.values(matchedRows).map((row) => getFieldValue(row, field.id))) ?? 0;
                    break;
                }

                case ScoreOperator.AVERAGE: {
                    if (!Object.keys(matchedRows)?.length) {
                        priorityResultMap[variable] = 0;
                        break;
                    }

                    const result =
                        Object.values(matchedRows).reduce((acc, row) => {
                            return acc + getFieldValue(row, field.id);
                        }, 0) / Object.keys(matchedRows).length;
                    priorityResultMap[variable] = result;
                    break;
                }
            }
        }
        return priorityResultMap;
    }
}
