import { Inject, Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { CollectionTransactionEntity } from '../../../database/src/entities/public/collection-transaction.public.entity';
import { DataRegisterAdditionalFieldEntity } from '../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { FormCollectionItemEntity } from '../../../database/src/entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { CollectionTransactionTenancyEntity } from '../../../database/src/entities/tenancy/collection-transaction.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormCollectionItemTransactionFieldDto } from '../dtos';
import { FormCollectionTransactionFieldRequest } from '../dtos/requests/transaction-field.request';
import { FormCollectionTransactionFieldDataService } from './data/form-collection-transaction-field.service';

@Injectable()
export class FormCollectionTransactionFieldService {
    constructor(
        @Inject(PROVIDER_KEYS.COLLECTION_TRANSACTION_REPOSITORY)
        private readonly _formCollectionItemTransactionFieldsRepo: Repository<CollectionTransactionEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_REPOSITORY)
        private readonly _formCollectionRepo: Repository<FormCollectionEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_REPOSITORY)
        private readonly _formCollectionItemRepo: Repository<FormCollectionItemEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_REPOSITORY)
        private readonly _dataRegisterAdditionalFieldTenancyRepo: Repository<DataRegisterAdditionalFieldEntity>,

        private readonly _dataService: FormCollectionTransactionFieldDataService,
    ) {}

    public async getFormCollectionItemTransactionFields(
        query: FormCollectionTransactionFieldRequest,
    ): Promise<FormCollectionItemTransactionFieldDto[]> {
        const result = await this._dataService.getFormCollectionTransactionFields({
            query,
            formCollectionRepo: this._formCollectionRepo as Repository<FormCollectionEntity | FormCollectionTenancyEntity>,
            formCollectionItemRepo: this._formCollectionItemRepo,
            formCollectionItemTransactionFieldsRepo: this._formCollectionItemTransactionFieldsRepo as Repository<
                CollectionTransactionEntity | CollectionTransactionTenancyEntity
            >,
            dataRegisterAdditionalFieldRepo: this._dataRegisterAdditionalFieldTenancyRepo,
        });

        return result;
    }
}
