import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';

import { EventDrivenModule, SharedModule } from 'src/common/src';
import { DataSourceService } from 'src/database/src/services/connection-util.service';
import { AppConfigService } from 'src/shared/services';

import { ScheduleWfsKpiStatusService } from './services';
import { BULL_TX_KPI_STATUS_SCHEDULER_QUEUE_NAME } from './constants';
import { RedisOptions } from 'bullmq';

const queueRegisters = [
    BullModule.registerQueueAsync({
        name: BULL_TX_KPI_STATUS_SCHEDULER_QUEUE_NAME,
    }),
];
@Module({
    imports: [
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
        BullModule.forRootAsync({
            imports: [SharedModule],
            useFactory: async (configService: AppConfigService) => ({
                connection: configService.bull.redis as RedisOptions,
                defaultJobOptionsL: configService.bull.defaultJobOptions,
            }),
            inject: [AppConfigService],
        }),
        ...queueRegisters,
    ],
    providers: [ScheduleWfsKpiStatusService],
    exports: [ScheduleWfsKpiStatusService, ...queueRegisters],
})
export class BullMQModule {}
