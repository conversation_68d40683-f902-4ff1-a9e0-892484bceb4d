import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { InjectQueue, WorkerHost, Processor } from '@nestjs/bullmq';
import { v4 } from 'uuid';

import { EventDrivenService } from 'src/common/src/modules/event-driven/event-driven.service';
import { TransactionTopicEnum } from 'src/common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { TransactionEventEnum } from 'src/database/src/shared/enums/automation-event.enum';

import { BULL_TX_KPI_STATUS_SCHEDULER_QUEUE_NAME } from '../constants';

@Injectable()
@Processor(BULL_TX_KPI_STATUS_SCHEDULER_QUEUE_NAME)
export class ScheduleWfsKpiStatusService extends WorkerHost {
    private readonly logger = new Logger(ScheduleWfsKpiStatusService.name);

    constructor(
        @InjectQueue(BULL_TX_KPI_STATUS_SCHEDULER_QUEUE_NAME)
        private readonly txWfsStatusQueue: Queue,
        private readonly eventDrivenService: EventDrivenService,
    ) {
        super();
    }

    private async removeJobsByPrefix(prefix: string): Promise<void> {
        try {
            await this.txWfsStatusQueue.pause();
            const jobs = await this.txWfsStatusQueue.getJobs();
            const jobsToRemove = jobs.filter((job) => job.name.startsWith(prefix));
            for (const job of jobsToRemove) {
                await job.remove();
            }
        } catch (error) {
            console.log(error);
        } finally {
            await this.txWfsStatusQueue.resume();
        }
    }

    private getJobName(transactionId: string, kpiStatusId: string): string {
        return `${transactionId}:${kpiStatusId}`;
    }

    async createJobs(
        params: {
            tenantId: string;
            source: string;
            transactionId: string;
            formId: string;
            formVersionId: string;
        },
        delays: { kpiStatusId: string; delay: number; kpiStatus?: number }[],
    ): Promise<void> {
        await this.removeJobsByPrefix(params.transactionId);

        for (const item of delays) {
            this.txWfsStatusQueue.add(
                this.getJobName(params.transactionId, item.kpiStatusId),
                {
                    message: EventDrivenService.createCommonEvent({
                        payload: {
                            transactionId: params.transactionId,
                            kpiStatusId: item.kpiStatusId,
                            formId: params.formId,
                            formVersionId: params.formVersionId,
                            kpiStatus: item.kpiStatus,
                        },
                        correlationId: v4(),
                        causationId: v4(),
                        userId: null,
                        tenantId: params.tenantId,
                        source: params.source,
                        aggregateId: params.transactionId,
                        type: TransactionEventEnum.FORM_STAGE_KPI_STATUS_CHANGED,
                        name: TransactionEventEnum.FORM_STAGE_KPI_STATUS_CHANGED,
                    }),
                    topic: TransactionTopicEnum.FORM_TRANSACTION_TOPIC,
                },
                {
                    delay: item.delay * 1000,
                    removeOnComplete: true,
                    removeOnFail: true,
                    jobId: this.getJobName(params.transactionId, item.kpiStatusId),
                },
            );
        }
    }

    async process(job: Job<any, any, string>): Promise<any> {
        await this.eventDrivenService.publishMessage(job.data.topic, job.data.message);
        this.logger.log(`Processing job for uuid: ${job.data.message.payload.transactionId}, jobName: ${job.name}`);
        return {};
    }
}
