import { Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { isEmpty } from 'lodash';
import { ClaimService } from '../../../common/src';
import { USER_CLAIMS } from '../../../common/src/constant/config';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { ApiMappingConfiguration } from '../dtos/api-mapping-configuration';
import { CreateApiRequestDto } from '../dtos/requests/create-api-request';
import { UpdateApiRequestDto } from '../dtos/requests/update-api-request';
import { ApiDataService } from '../services';
import { ApiCollectionService } from '../services/collection.service';
import { TransactionDataService } from '../services/data-services/transaction.data.service';
import { FormatRequestService } from '../services/format-request.service';

@Injectable()
export class FormatTransactionBodyMiddleware implements NestMiddleware {
    constructor(
        private readonly _apiDataService: ApiDataService,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _formatRequestService: FormatRequestService,
        private readonly _formatCollectionService: ApiCollectionService,
        private readonly _transactionDataService: TransactionDataService,
    ) {}

    async use(req: Request, res: Response, next: NextFunction) {
        const body = req.body;
        const formId: string = req.params?.formId ?? (req.query?.formId as string) ?? '';
        const transactionId = req.params?.id;

        let formFieldIdMapping: Map<string, string> = new Map();
        let collectionFieldIdMapping: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        > = new Map();

        let mappings: ApiMappingConfiguration[] = [];

        if (formId) {
            const apiVersion = await this._apiDataService.getApiVersionByContext(
                formId,
                ApiContextType.FormTransaction,
                this._claims.accountId,
            );

            mappings = apiVersion?.configuration?.fieldMapping;
        }

        if (body && !isEmpty(body) && formId) {
            const formattedBody: CreateApiRequestDto | UpdateApiRequestDto = { ...body, transactionFields: [], formId };

            if (mappings?.length) {
                const transactionFields: EditFormTransactionFieldRequest[] = [];
                if (body.formFields && !isEmpty(body.formFields)) {
                    const { transactionFields: tf, formFieldIdMapping: fim } = await this._formatRequestService.formatFormFieldRequest({
                        dto: formattedBody,
                        fieldMappings: mappings,
                        transactionId: (formattedBody as UpdateApiRequestDto)?.transactionId,
                    });
                    transactionFields.push(...tf);
                    formFieldIdMapping = fim;
                }

                formattedBody.transactionFields = transactionFields ?? [];

                if (body.collections && transactionId) {
                    const collectionTransactionFields = await this._formatCollectionService.formatCollectionRequest({
                        dto: body.collections,
                        transactionId: transactionId,
                        fieldMappings: mappings,
                    });

                    (formattedBody as UpdateApiRequestDto).collectionFields = {
                        addCollectionRows: collectionTransactionFields.addCollectionTransactionFields,
                        updateCollectionFields: collectionTransactionFields.updateCollectionTransactionFields,
                        deleteCollectionsRows: collectionTransactionFields.deleteRowKeys,
                    };

                    if (collectionTransactionFields.addCollectionTransactionFields?.length) {
                        formattedBody.transactionFields.push(...collectionTransactionFields.addCollectionTransactionFields);
                    }

                    if (collectionTransactionFields.updateCollectionTransactionFields?.length) {
                        formattedBody.transactionFields.push(...collectionTransactionFields.updateCollectionTransactionFields);
                    }

                    if (collectionTransactionFields.collectionFieldIdMapping) {
                        collectionFieldIdMapping = collectionTransactionFields.collectionFieldIdMapping;
                    }
                }
            }

            req.body = formattedBody;
        }

        if (transactionId) {
            const transaction = await this._transactionDataService.getTransactionById(transactionId);
            req.body.transactionId = transaction.id;
            req.body.formVersionId = transaction.formVersionId;
            req.body.stageId = transaction.stageId;
            req.body.activeStageId = transaction.stageId;
        }

        req.body.formId = formId;
        req.body.formFieldIdMapping = formFieldIdMapping;
        req.body.collectionFieldIdMapping = collectionFieldIdMapping;

        //Convert fieldKey to fieldId
        const fieldKey = req.body.fieldKey || req.query.fieldKey;
        if (fieldKey) {
            const fieldId = mappings.find((m) => m.key === fieldKey)?.fieldId;

            req.body.fieldId = fieldId || '';
            req.query.fieldId = fieldId || '';
        }

        next();
    }
}
