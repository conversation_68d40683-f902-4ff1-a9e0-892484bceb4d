import { BadRequestException, CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { ApiCollectionRequestDto } from '../dtos/requests/api-collection-request';
import { PayloadValidationService } from '../services/payload-validation.service';

type BodyBase = {
    roleId: string;
    stageId: string;
    formVersionId: string;
    transactionFields: EditFormTransactionFieldRequest[];
    transactionId: string;
    formId: string;
    originalTransactionId?: string;
    activeStageId: string;
    stageIdentityId?: string;
    activeStageIdentityId: string;
    formValues?: Record<string, any>;
    fieldChangeIds?: string[];
    formFieldIdMapping?: Map<string, string>;
    collectionFieldIdMapping?: Map<
        string,
        {
            collectionKey: string;
            collectionFieldKey: string;
        }
    >;
    collections?: ApiCollectionRequestDto;
};

@Injectable()
export class ApiUpdateTransactionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,

        private readonly _payloadValidationService: PayloadValidationService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: BodyBase; user: { roles: string[] } };

        const { formVersionId, transactionFields = [], activeStageId, formFieldIdMapping, collectionFieldIdMapping, collections } = body;

        const roles = user.roles || [];
        if (!roles.length) {
            throw new ForbiddenException('user_has_no_roles');
        }

        const roleIds = roles;

        if (!activeStageId) {
            throw new ForbiddenException('active_stage_not_found');
        }

        const acls = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: formVersionId,
                stageRoles: roles.map((r) => {
                    return {
                        roleId: r,
                        stageId: activeStageId,
                    };
                }),
                includeFieldConfig: true,
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
            ignoreCache: true,
        });

        if (!acls?.length) {
            throw new ForbiddenException('user_has_no_permission_on_this_stage');
        }

        const stageRoles = await this._dataSource.getRepository(StageRoleTenancyEntity).find({
            where: {
                formVersionId: formVersionId,
                stageId: activeStageId,
            },
        });
        const stageRoleACLs = (acls || []).filter((item) => roleIds.includes(item.roleId) && item.stageId === activeStageId);

        if (collections?.addCollectionRows) {
            if (!stageRoleACLs.length) {
                throw new ForbiddenException('stage_role_not_in_form_version');
            }

            const { valid, errors } = await this._payloadValidationService.validationAddCollectionRow(
                collections?.addCollectionRows,
                stageRoleACLs,
            );

            if (!valid) {
                throw new ForbiddenException(errors);
            }
        }

        if (collections?.deleteCollectionsRows) {
            if (!stageRoleACLs.length) {
                throw new ForbiddenException('stage_role_not_in_form_version');
            }

            const { valid, errors } = await this._payloadValidationService.validationDeleteCollectionRow(
                collections?.deleteCollectionsRows,
                stageRoleACLs,
            );

            if (!valid) {
                throw new ForbiddenException(errors);
            }
        }

        if (transactionFields?.length) {
            if (!stageRoleACLs.length) {
                throw new ForbiddenException('stage_role_not_in_form_version');
            }

            const { isValid, hasFieldAclError, hasFieldDataTypeError, fieldDataTypeErrorMap, fieldAclErrorMap } =
                await this._payloadValidationService.validateTransactionFieldPayload({
                    stageRoleACLs,
                    transactionFields,
                    dataSource: this._dataSource,
                    availableRoles: stageRoles.map((r) => r.roleId),
                    formVersionId,
                    formFieldIdMapping,
                    collectionFieldIdMapping,
                });

            if (!isValid) {
                if (hasFieldAclError) {
                    const messages = Object.values(fieldAclErrorMap).flatMap((item) => item.data?.map((d) => d.message));
                    throw new ForbiddenException(messages);
                }

                if (hasFieldDataTypeError) {
                    const messages = Object.values(fieldDataTypeErrorMap).flatMap((item) => item.data?.map((d) => d.message));
                    throw new BadRequestException(messages);
                }
            }
        }

        return true;
    }
}
