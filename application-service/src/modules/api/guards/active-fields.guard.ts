import { BadRequestException, CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common';
import { isArray, isEmpty, uniq } from 'lodash';
import { Repository } from 'typeorm';
import { CacheService, ClaimService, USER_CLAIMS, UtilsService } from '../../../common/src';
import { SELECTABLE_FIELD_TYPES } from '../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { CaptureActiveFormVersionType } from '../../../database/src/shared/providers/capture-active-form-version.provider';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';

@Injectable()
export class ActiveFieldsGuard implements CanActivate {
    constructor(
        private readonly _cacheService: CacheService,
        @Inject(PROVIDER_KEYS.FORM_TENANCY_REPOSITORY) private readonly _formRepository: Repository<FormTenancyEntity>,
        @Inject(PROVIDER_KEYS.FORM_FIELD_TENANCY_REPOSITORY) private readonly _fieldRepository: Repository<FormFieldTenancyEntity>,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const formId = request.params.formId as string;

        if (!formId) {
            throw new BadRequestException('form id is required');
        }

        const body = request.body as { transactionFields?: EditFormTransactionFieldRequest[] };
        const transactionFields = body?.transactionFields || [];

        if (isEmpty(transactionFields)) return true;

        const form: Pick<FormTenancyEntity, 'id' | 'activeVersionId'> = await this._formRepository.findOne({
            where: {
                id: formId,
            },
            select: {
                id: true,
                activeVersionId: true,
            },
        });

        if (!form.activeVersionId) {
            throw new BadRequestException('form active version not existed');
        }

        const cacheKey = UtilsService.getActiveFormVersionCacheKeys({
            accountId: this._claims.accountId,
            formId: request.formId,
            formVersionId: form.activeVersionId,
        });

        let cachedFormVersion: CaptureActiveFormVersionType;

        if (cacheKey) {
            cachedFormVersion = await this._cacheService.jsonGet<CaptureActiveFormVersionType>(cacheKey.formVersionKey);
        }

        let activeFormFields: Pick<FormFieldTenancyEntity, 'fieldId' | 'label' | 'type'>[] = [];
        if (cachedFormVersion?.fields?.length) {
            activeFormFields = cachedFormVersion.fields.map((f) => ({
                fieldId: f.fieldId,
                label: f.label,
                id: f.id,
                type: f.type,
            }));
        } else {
            activeFormFields = await this._fieldRepository.find({
                where: { formVersionId: form.activeVersionId },
                select: {
                    id: true,
                    fieldId: true,
                    label: true,
                    type: true,
                },
            });
        }

        activeFormFields = activeFormFields.filter((f) => f.type !== FormFieldTypeEnum.Rollup);

        const activeFormFieldsMap = new Map(activeFormFields.map((f) => [f.fieldId, f]));

        const activeFormFieldsSet = new Set(activeFormFields.map((f) => f.fieldId));

        const separatedField = transactionFields.reduce(
            (map, field) => {
                if (field.collectionId) {
                    map.collectionFields.push(field);
                } else {
                    map.formFields.push(field);
                }

                return map;
            },
            { formFields: [], collectionFields: [] } as {
                formFields: EditFormTransactionFieldRequest[];
                collectionFields: EditFormTransactionFieldRequest[];
            },
        );

        const invalidFormFields = separatedField.formFields.filter((f) => !activeFormFieldsSet.has(f.fieldId));
        if (invalidFormFields.length) {
            throw new BadRequestException('active_form_fields_invalid');
        }

        // Attach field type and option ids to transaction fields
        transactionFields.forEach((f) => {
            if (activeFormFieldsMap.has(f.fieldId)) {
                f.fieldType = activeFormFieldsMap.get(f.fieldId)?.type;
                if (SELECTABLE_FIELD_TYPES.includes(f.fieldType) && f.fieldValue) {
                    f.fieldOptionIds = isArray(f.fieldValue) ? f.fieldValue : uniq(f.fieldValue.split(','));
                }
            }
        });

        //TODO: check collection fields

        return true;
    }
}
