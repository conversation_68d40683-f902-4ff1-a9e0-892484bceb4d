import { BadRequestException, CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { ApiService } from '../services/api.service';
import { ApiDataService } from '../services';

@Injectable()
export class ApiGuard implements CanActivate {
    constructor(private readonly _apiService: ApiService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        // const clientId = request.headers['x-client-id'];
        // const clientSecret = request.headers['x-client-secret'];
        // if (!clientId || !clientSecret) {
        //     throw new UnauthorizedException('Client ID and secret are required');
        // }
        const endpoint = request.route.path;
        const method = request.method;
        const formId = request.params.formId;

        const api = await this._apiService.getEndpoint({ endpoint, method, formId });

        if (!api || !api.configuration?.isEnabled) {
            throw new BadRequestException('invalid_endpoint');
        }

        return true;
    }
}
