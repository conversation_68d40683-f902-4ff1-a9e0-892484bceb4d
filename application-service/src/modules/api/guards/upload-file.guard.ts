import { BadRequestException, CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { UpdateFileRequest } from '../dtos/requests/update-file-request';

@Injectable()
export class UploadFileGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: UpdateFileRequest; user: { roles: string[] } };

        const { formId, transactionId, fieldId, fieldKey } = body;

        if (!fieldId) throw new BadRequestException(`${fieldKey} is not a valid field`);

        const roles = user.roles || [];
        if (!roles.length) {
            throw new ForbiddenException('user_has_no_roles');
        }

        const transaction = await this._dataSource.getRepository(TransactionEntity).findOne({
            where: {
                id: transactionId,
            },
        });

        if (!transaction) throw new BadRequestException('transaction_not_found');

        const activeStageId = transaction?.stageId;
        const formVersionId = transaction?.formVersionId;

        const roleIds = roles;

        const acls = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: formVersionId,
                stageRoles: roles.map((r) => {
                    return {
                        roleId: r,
                        stageId: activeStageId,
                    };
                }),
                includeFieldConfig: true,
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
            ignoreCache: true,
        });

        if (!acls?.length) {
            throw new ForbiddenException('user_has_no_permission_on_this_stage');
        }

        const stageRoleACLs = (acls || []).filter((item) => roleIds.includes(item.roleId) && item.stageId === activeStageId);

        let canUpload = false;

        for (const acl of stageRoleACLs) {
            const fieldAcl = acl?.field ? acl.field[fieldId] : {};

            if (fieldAcl?.editable?.toString()?.toLowerCase() === 'true') {
                canUpload = true;
                break;
            }
        }

        if (!canUpload) {
            throw new ForbiddenException('user_has_no_permission_to_upload_file');
        }

        return true;
    }
}
