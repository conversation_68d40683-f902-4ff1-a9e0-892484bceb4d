import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';

@Injectable()
export class ApiRelatedGuard implements CanActivate {
    constructor() {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const clientId = request.headers['x-client-id'];
        const clientSecret = request.headers['x-client-secret'];
        if (!clientId || !clientSecret) {
            throw new UnauthorizedException('Client ID and secret are required');
        }

        return true;
    }
}
