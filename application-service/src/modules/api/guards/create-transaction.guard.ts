import { BadRequestException, CanActivate, ExecutionContext, ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormTenancyEntity } from '../../../database/src/entities/tenancy/form.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { StageTenancyEntity } from '../../../database/src/entities/tenancy/stage.tenancy.entity';
import { CreateApiRequestDto } from '../dtos/requests/create-api-request';
import { PayloadValidationService } from '../services/payload-validation.service';

@Injectable()
export class ApiCreateTransactionGuard implements CanActivate {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,

        private readonly _payloadValidationService: PayloadValidationService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { body, user } = request as { body: CreateApiRequestDto; user: { roles: string[] } };

        const { formId, transactionFields, formFieldIdMapping } = body;

        const roles = user.roles || [];
        if (!roles.length) {
            throw new ForbiddenException('user_has_no_roles');
        }

        const roleIds = roles;

        const stageEntities = await this._stageRoleACL.getFormStages<StageTenancyEntity>({
            formId,
            formRepo: this._dataSource.getRepository(FormTenancyEntity),
            stageRepo: this._dataSource.getRepository(StageTenancyEntity),
            select: ['id', 'config', 'formVersionId'],
        });

        const startStage = (stageEntities || []).find((stage) => stage.config.type === 'START');
        if (!startStage) throw new ForbiddenException('start_stage_not_found');

        const acls = await this._stageRoleACL.getRoleStageACLs({
            request: {
                formVersionId: startStage.formVersionId,
                stageRoles: roles.map((r) => {
                    return {
                        roleId: r,
                        stageId: startStage.id,
                    };
                }),
                includeFieldConfig: true,
            },
            stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
            stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
            fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
        });

        if (!acls?.length) {
            throw new ForbiddenException('user_has_no_permission_on_start_stage');
        }

        const canCreate = acls.some((item) => item.canCreate?.toString()?.toLowerCase() === 'true');
        if (!canCreate) {
            throw new ForbiddenException('user_has_no_permission_to_create_transaction');
        }

        if (transactionFields?.length) {
            const stageRoles = await this._dataSource.getRepository(StageRoleTenancyEntity).find({
                where: {
                    formVersionId: startStage.formVersionId,
                    stageId: startStage.id,
                },
            });
            const stageRoleACLs = (acls || []).filter((item) => roleIds.includes(item.roleId) && item.stageId === startStage.id);
            if (!stageRoleACLs.length) {
                throw new ForbiddenException('stage_role_not_in_form_version');
            }

            const { isValid, hasFieldAclError, hasFieldDataTypeError, fieldDataTypeErrorMap, fieldAclErrorMap } =
                await this._payloadValidationService.validateTransactionFieldPayload({
                    stageRoleACLs,
                    transactionFields,
                    dataSource: this._dataSource,
                    availableRoles: stageRoles.map((r) => r.roleId),
                    formFieldIdMapping,
                });

            if (!isValid) {
                if (hasFieldAclError) {
                    const messages = Object.values(fieldAclErrorMap).flatMap((item) => item.data?.map((d) => d.message));
                    throw new ForbiddenException(messages);
                }

                if (hasFieldDataTypeError) {
                    const messages = Object.values(fieldDataTypeErrorMap).flatMap((item) => item.data?.map((d) => d.message));

                    throw new BadRequestException(messages);
                }
            }
        }

        return true;
    }
}
