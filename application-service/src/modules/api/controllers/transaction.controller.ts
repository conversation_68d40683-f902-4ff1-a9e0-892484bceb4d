import {
    Body,
    Controller,
    Delete,
    FileTypeValidator,
    Get,
    HttpCode,
    HttpStatus,
    MaxFileSizeValidator,
    Param,
    ParseFilePipe,
    Post,
    Put,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    Version,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { TransactionFileService } from '../../file/services/transaction-file.service';
import { FormTransactionResponseDto } from '../dtos';
import { CreateApiRequestDto } from '../dtos/requests/create-api-request';
import { ApiGetListFormTransactionRequestDto } from '../dtos/requests/get-list-form-transaction.request';
import { UpdateApiRequestDto } from '../dtos/requests/update-api-request';
import { UpdateFileRequest } from '../dtos/requests/update-file-request';
import { ApiResponseDto } from '../dtos/responses/api-response.dto';
import { ErrorCodeDto } from '../dtos/responses/error-code.dto';
import { FormTransactionDetailResponseDto } from '../dtos/responses/form-transaction-detail.response.dto';
import { ActiveFieldsGuard } from '../guards/active-fields.guard';
import { ApiRelatedGuard } from '../guards/api-related.gurad';
import { ApiGuard } from '../guards/api.guard';
import { ApiCreateTransactionGuard } from '../guards/create-transaction.guard';
import { ApiUpdateTransactionGuard } from '../guards/update-transaction.guard';
import { UploadFileGuard } from '../guards/upload-file.guard';
import { FormatTransactionListQueryInterceptor } from '../interceptors/format-transaction-list-query.interceptor';
import { ApiCollectionService } from '../services/collection.service';
import { ApiTransactionService } from '../services/transaction.service';
import { BaseController } from './base.controller';
import { FormTransactionDetailInterceptor } from '../interceptors/form-transactions-detail.interceptor';
import { ApiFormTransactionListInterceptor } from '../interceptors/form-transaction-list.interceptor';
import { TestModeInterceptor } from 'src/interceptors';
import { FileInterceptor } from '@nestjs/platform-express';
import { DeleteTransactionGuard } from '../../form/guards/delete-transaction.guard';

@ApiTags('Api Module - Form Transaction')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
@Controller('third-party/transaction')
export class TransactionController extends BaseController {
    constructor(
        private readonly _service: ApiTransactionService,
        private readonly _apiCollectionService: ApiCollectionService,
        private readonly _transactionFileService: TransactionFileService,
    ) {
        super();
    }

    //#region CREATE TRANSACTION
    @ApiOperation({ summary: 'Create transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('create/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseGuards(ApiGuard, ActiveFieldsGuard, ApiCreateTransactionGuard)
    @UseInterceptors(TestModeInterceptor)
    public async createTransaction(@Param('formId') formId: string, @Body() dto: CreateApiRequestDto) {
        const data = await this._service.createTransaction(formId, dto);
        return this.success<{ id: string; status: string }>({
            id: data,
            status: 'Processing',
        });
    }
    //#endregion

    //#region UPDATE TRANSACTION
    @ApiOperation({ summary: 'Update transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Put('update/:formId/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseGuards(ApiGuard, ActiveFieldsGuard, ApiUpdateTransactionGuard)
    @UseInterceptors(TestModeInterceptor)
    public async updateTransaction(@Param('formId') formId: string, @Param('id') transactionId: string, @Body() dto: UpdateApiRequestDto) {
        const data = await this._service.updateTransaction(formId, { ...dto, transactionId });
        return this.success<{ id: string; status: string }>({
            id: data,
            status: 'Processing',
        });
    }

    //#endregion

    @ApiOperation({ summary: 'Get form transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('list/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseInterceptors(FormatTransactionListQueryInterceptor, ApiFormTransactionListInterceptor)
    @UseGuards(ApiGuard)
    public async getList(
        @Param('formId') formId: string,
        @Query() query: ApiGetListFormTransactionRequestDto,
    ): Promise<ApiResponseDto<PaginationResponseDto<FormTransactionResponseDto>> | ApiResponseDto<ErrorCodeDto>> {
        const data = await this._service.getList(query, formId);
        return this.success<PaginationResponseDto<FormTransactionResponseDto>>(data);
    }

    @ApiOperation({ summary: 'Get form transaction detail' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('detail/:formId/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseInterceptors(FormTransactionDetailInterceptor)
    @UseGuards(ApiGuard)
    public async getDetail(
        @Param('formId') formId: string,
        @Param('id') id: string,
        @Query('includeRelatedTransactionIds') includeRelatedTransactionIds: boolean,
    ): Promise<ApiResponseDto<FormTransactionDetailResponseDto> | ApiResponseDto<ErrorCodeDto>> {
        const data = await this._service.getDetail(formId, id, includeRelatedTransactionIds);
        return this.success<FormTransactionDetailResponseDto>(data);
    }

    @ApiOperation({ summary: 'Get collection options' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/collection/options/:formId/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseGuards(ApiRelatedGuard)
    public async getCollectionOptions(@Param('id') transactionId: string) {
        const data = await this._apiCollectionService.getFormCollectionOptions(transactionId);
        return this.success<any>(data);
    }

    @ApiOperation({ summary: 'Upload file' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('/upload/:formId/:id')
    @UseInterceptors(FileInterceptor('file'))
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseGuards(UploadFileGuard)
    public async uploadFile(
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: 100 * 1024 * 1024, message: 'The file size must be smaller than 100MB!' }),
                    new FileTypeValidator({
                        fileType:
                            /image\/(jpeg|png|gif|bmp|tiff|webp|heic|heif)|application\/(pdf|vnd\.openxmlformats-officedocument\.wordprocessingml\.document|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|octet-stream)|message\/rfc822|video\/(mp4|x-msvideo|quicktime|x-ms-wmv|x-matroska)|audio\/(mpeg|wav|aac|ogg|flac)/,
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
        @Body() request: UpdateFileRequest,
        @Param('formId') formId: string,
        @Param('id') id: string,
    ) {
        const data = await this._transactionFileService.uploadFile({
            fileName: file.originalname,
            fileData: file.buffer,
            ignoreAcl: true,
            formId: formId,
            transactionId: id,
            fieldId: request.fieldId, // fieldId is required by UploadFileProps
        });
        return this.success(data);
    }

    @ApiOperation({ summary: 'Delete transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Delete('delete/:formId/:id')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseGuards(ApiGuard, DeleteTransactionGuard)
    @UseInterceptors(TestModeInterceptor)
    public async deleteTransaction(@Param('formId') formId: string, @Param('id') id: string) {
        try {
            const data = await this._service.deleteTransaction(formId, id);
            return this.success<boolean>(data);
        } catch (error) {
            return this.error(error.code, error.message, error.details);
        }
    }
}
