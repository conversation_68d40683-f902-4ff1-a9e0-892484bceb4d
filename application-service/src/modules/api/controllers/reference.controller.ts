import { Controller, Get, HttpCode, HttpStatus, Param, Query, UseGuards, UseInterceptors, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { BaseController } from './base.controller';

@ApiTags('Api Module - Reference')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
@Controller('third-party/reference')
export class ReferenceController extends BaseController {
    constructor() {
        super();
    }
}
