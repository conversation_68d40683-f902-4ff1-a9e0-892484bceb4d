import { Controller, Get, HttpCode, HttpStatus, Param, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { ApiLookupRequestDto } from '../dtos/requests/get-lookup.request';
import { ApiGetOptionRequestDto } from '../dtos/requests/get-option.request';
import { ApiRelatedGuard } from '../guards/api-related.gurad';
import { ApiFormService } from '../services/form.service';
import { BaseController } from './base.controller';

@ApiTags('Api Module - Form')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
@Controller('third-party/form')
export class FormController extends BaseController {
    constructor(private readonly _service: ApiFormService) {
        super();
    }

    @ApiOperation({ summary: 'Get data register lookup transactions' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('lookup/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseGuards(ApiRelatedGuard)
    public async getLookupTransactions(@Param('formId') formId: string, @Query() query: ApiLookupRequestDto) {
        query.formId = formId;
        const data = await this._service.lookUpOptions(query);

        return this.success(data);
    }

    @ApiOperation({ summary: 'Get data register options transactions' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('options/:formId')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    @UseGuards(ApiRelatedGuard)
    public async getOptions(@Param('formId') formId: string, @Query() query: ApiGetOptionRequestDto) {
        query.formId = formId;
        const data = await this._service.getOptions(query);
        return this.success(data);
    }
}
