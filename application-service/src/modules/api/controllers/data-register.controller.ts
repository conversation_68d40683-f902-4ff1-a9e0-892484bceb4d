import { Controller, UseGuards } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>A<PERSON> } from '@nestjs/swagger';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { ApiDataRegisterService } from '../services/data-register.service';
import { BaseController } from './base.controller';

@Controller('third-party/data-register')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class DataRegisterController extends BaseController {
    constructor(private readonly _service: ApiDataRegisterService) {
        super();
    }

    // @ApiOperation({ summary: 'Get data register lookup transactions' })
    // @Version('1')
    // @HttpCode(HttpStatus.OK)
    // @Get('lookup/:formId')
    // @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    // @UseGuards(ApiRelatedGuard)
    // public async getLookupTransactions(@Param('formId') formId: string, @Query() query: ApiLookupRequestDto) {
    //     query.formId = formId;
    //     const data = await this._service.lookUpOptions(query);

    //     return this.success(data);
    // }

    // @ApiOperation({ summary: 'Get data register options transactions' })
    // @Version('1')
    // @HttpCode(HttpStatus.OK)
    // @Get('options/:formId')
    // @UserTypes(UserType.ACCOUNT_ADMIN, UserType.API)
    // @UseGuards(ApiRelatedGuard)
    // public async getOptions(@Param('formId') formId: string, @Query() query: ApiGetOptionRequestDto) {
    //     query.formId = formId;
    //     const data = await this._service.getOptions(query);
    //     return this.success(data);
    // }
}
