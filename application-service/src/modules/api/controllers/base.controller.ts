import { ApiResponseDto } from '../dtos/responses/api-response.dto';
import { ErrorCodeDto } from '../dtos/responses/error-code.dto';

export class BaseController {
    protected success<T>(data: T, message?: string): ApiResponseDto<T> {
        return {
            success: true,
            data,
            message,
        };
    }

    protected error(code: string, message: string, details?: string): ApiResponseDto<ErrorCodeDto> {
        return {
            success: false,
            error: {
                code,
                message,
                details,
            },
        };
    }
}
