import { Body, Controller, HttpCode, HttpStatus, Param, Post, Version } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthRequestDto } from '../dtos/requests/token.request.dto';
import { TokenResponseDto } from '../dtos/responses/token.response.dto';
import { AuthService } from '../services/auth.service';
import { BaseController } from './base.controller';
import { ApiResponseDto } from '../dtos';

@ApiTags('Api Module - Auth')
@Controller('third-party/auth')
export class AuthController extends BaseController {
    constructor(private readonly _service: AuthService) {
        super();
    }

    @ApiOperation({ summary: 'Authenticate client and return JWT token' })
    @HttpCode(HttpStatus.OK)
    @Post(':accountId/token')
    @Version('1')
    async login(@Param('accountId') accountId: string, @Body() body: AuthRequestDto): Promise<ApiResponseDto<TokenResponseDto>> {
        const { clientId, clientSecret } = body;
        const data = await this._service.validateClient(clientId, clientSecret, accountId);
        return this.success(data);
    }
}
