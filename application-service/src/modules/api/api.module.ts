import { forwardRef, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { EventDrivenModule } from '../../common/src';
import { FormCollectionCacheService } from '../../common/src/modules/shared/services';
import { DataSourceService } from '../../database/src/services/connection-util.service';
import { AppConfigService } from '../../shared/services/app-config.service';
import { SharedModule } from '../../shared/shared.module';
import { DataRegisterModule } from '../data-register/data-register.module';
import { FileModule } from '../file/file.module';
import { FormModule } from '../form/form.module';
import { FormCollectionTransactionFieldDataService } from '../form/services/data/form-collection-transaction-field.service';
import { FormatTransactionFieldService } from '../form/services/data/format/transaction-field.service';
import { DefaultTransactionFieldService } from '../form/services/default-transaction-field.service';
import { FormCollectionTransactionFieldService } from '../form/services/form-collection-transaction-field.service';
import { ValidationFormFieldModule } from '../validation/validation.module';
import { AuthController, DataRegisterController, FormController, TransactionController } from './controllers';
import { ApiRelatedGuard } from './guards/api-related.gurad';
import { ApiGuard } from './guards/api.guard';
import { ApiCreateTransactionGuard } from './guards/create-transaction.guard';
import { ApiUpdateTransactionGuard } from './guards/update-transaction.guard';
import { UploadFileGuard } from './guards/upload-file.guard';
import { ApiFormTransactionListInterceptor } from './interceptors/form-transaction-list.interceptor';
import { FormatTransactionBodyMiddleware } from './middleware/format-transaction-body.middleware';
import { CollectionMappingProfile } from './profiles/collection.profile';
import { FormTransactionFieldMappingProfile } from './profiles/form-transaction-field.profile';
import { FormTransactionMappingProfile } from './profiles/form-transaction.profile';
import { ApiCollectionDataService, ApiDataService, ApiFormService, ApiTransactionService, AuthService, FormDataService } from './services';
import { ApiService } from './services/api.service';
import { ApiCollectionService } from './services/collection.service';
import { ApiDataRegisterService } from './services/data-register.service';
import { DataRegisterDataService } from './services/data-services/data-register.data.service';
import { TransactionDataService } from './services/data-services/transaction.data.service';
import { FormatRequestService } from './services/format-request.service';
import { PayloadValidationService } from './services/payload-validation.service';
import { ValidateFieldMappingService } from './utils/validate-field-mapping.service';
import { ValidateTransactionFieldACLService } from './utils/validate-transaction-field-acl.service';

const providers = [
    AuthService,
    ApiTransactionService,
    ApiService,
    ApiGuard,
    ApiDataService,
    ApiFormService,
    FormDataService,
    ApiCollectionDataService,
    FormCollectionCacheService,
    ApiDataRegisterService,
    FormatRequestService,
    ApiCollectionService,
    FormCollectionTransactionFieldService,
    DefaultTransactionFieldService,
    FormCollectionTransactionFieldDataService,
    FormatTransactionFieldService,
    FormatTransactionBodyMiddleware,
    ApiCreateTransactionGuard,
    PayloadValidationService,
    ApiUpdateTransactionGuard,
    ApiRelatedGuard,
    TransactionDataService,
    ApiFormTransactionListInterceptor,
    ValidateFieldMappingService,
    ValidateTransactionFieldACLService,
    UploadFileGuard,
    DataRegisterDataService,
];
const mappingProfiles = [FormTransactionMappingProfile, FormTransactionFieldMappingProfile, CollectionMappingProfile];

@Module({
    imports: [
        // ConfigModule.forRoot({
        //     envFilePath: process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env',
        //     isGlobal: true,
        // }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [AppConfigService],
            useFactory: async (configService: AppConfigService) => ({
                secret: configService.jwtConfig.appSecret,
                signOptions: { expiresIn: configService.jwtConfig.expire },
            }),
        }),
        SharedModule,
        forwardRef(() => FormModule),
        forwardRef(() => DataRegisterModule),
        ValidationFormFieldModule,
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
        forwardRef(() => FileModule),
    ],
    controllers: [DataRegisterController, TransactionController, AuthController, FormController],
    providers: [...providers, ...mappingProfiles],
    exports: [...providers],
})
export class ApiModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(FormatTransactionBodyMiddleware).forRoutes(TransactionController, DataRegisterController, FormController);
        // .forRoutes(
        //     { path: API_ENDPOINTS.TRANSACTION_CREATE, method: RequestMethod.POST },
        //     { path: API_ENDPOINTS.TRANSACTION_UPDATE, method: RequestMethod.PUT },
        // );
    }
}
