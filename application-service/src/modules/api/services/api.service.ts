import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import {
    API_ENDPOINTS,
    API_ENDPOINT_CONTEXT_TYPE,
    API_ENDPOINT_MATCHERS,
    API_ENDPOINT_METHODS,
} from '../../../database/src/constants/api-endpoints';
import { AppConfigService } from '../../../shared/services';
import { CacheService, ClaimService, USER_CLAIMS } from '../../../common/src';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { ApiDataService } from './data-services/api.data.service';
import { ApiVersionEndpointTenancyEntity } from '../../../database/src/entities/tenancy/api-version-endpoint.tenancy.entity';

@Injectable()
export class ApiService {
    constructor(
        private readonly _configService: AppConfigService,
        private readonly _cacheService: CacheService,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _apiDataService: ApiDataService,
    ) {}

    public async getEndpoint({
        endpoint,
        formId,
        method,
    }: {
        endpoint: string;
        method: string;
        formId: string;
    }): Promise<ApiVersionEndpointTenancyEntity> {
        const contextType = this._checkEndpointType(endpoint);
        if (!contextType) {
            throw new BadRequestException('invalid_endpoint');
        }

        const version = await this._apiDataService.getApiVersionByContext(formId, contextType, this._claims.accountId);
        if (!version?.isEnable) {
            return null;
        }

        const apiEndpoint = await this._apiDataService.getEndpoint(
            {
                endpoint: endpoint as API_ENDPOINTS,
                method: method as API_ENDPOINT_METHODS,
                contextType,
                contextId: formId,
            },
            this._claims.accountId,
        );
        return apiEndpoint;
    }

    private _checkEndpointType(endpoint: string): ApiContextType | null {
        if (!endpoint) {
            return null;
        }
        return Object.keys(API_ENDPOINT_CONTEXT_TYPE).find((key) => API_ENDPOINT_CONTEXT_TYPE[key].includes(endpoint)) as ApiContextType;
    }
}
