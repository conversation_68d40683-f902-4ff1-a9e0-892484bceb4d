import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { CacheService, ClaimService, LoggerService, USER_CLAIMS, UtilsService } from '../../../common/src';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { OperatorType } from '../../../common/src/modules/shared/enums/operator.enum';
import { API_SYSTEM_FIELD_MAPPING } from '../../../constant/field';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { FormTransactionFieldDto } from '../../form/dtos';
import { FormTransactionDto } from '../../form/dtos/form-transaction.dto';
import { EditFormTransactionFieldRequest, EditFormTransactionRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { FilterFormTransactionRequestDto } from '../../form/dtos/requests/filter-form-transaction.request';
import { FormTransactionTenancyService } from '../../form/services/form-transaction.tenancy.service';
import { TransactionFieldResponseDto } from '../dtos';
import { ApiGetListFormTransactionRequestDto } from '../dtos/requests/get-list-form-transaction.request';
import { FormTransactionResponseDto } from '../dtos/responses/form-transaction.response.dto';
import { ApiDataService } from './data-services/api.data.service';

import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { EventDrivenService } from '../../../common/src/modules/event-driven/event-driven.service';
import { TransactionTopicEnum } from '../../../common/src/modules/shared/enums/event-driven/transaction-topic.enum';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { TransactionEventEnum } from '../../../database/src/shared/enums/automation-event.enum';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { FormTransactionFieldTenancyService } from '../../form/services/form-transaction-field.tenancy.service';
import { CreateApiRequestDto } from '../dtos/requests/create-api-request';
import { UpdateApiRequestDto } from '../dtos/requests/update-api-request';
import { FormTransactionDetailResponseDto } from '../dtos/responses/form-transaction-detail.response.dto';
import { TransactionDetailFieldResponseDto } from '../dtos/responses/transaction-detail-field.response.dto';
import { ApiCollectionDataService } from './data-services/api-collection.data.service';
import { FormDataService } from './data-services/form.data.service';
import { FormTransactionDataService } from '../../form/services/data/form-transaction.data.service';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class ApiTransactionService {
    constructor(
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        private _loggerService: LoggerService,
        private readonly _cacheService: CacheService,
        private readonly _transactionTenancyService: FormTransactionTenancyService,
        private readonly _apiDataService: ApiDataService,
        private readonly _formDataService: FormDataService,
        private readonly _formTransactionFieldTenancyService: FormTransactionFieldTenancyService,
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _apiCollectionDataService: ApiCollectionDataService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _dataSourceService: FormTransactionDataService,
    ) {}

    //#region GET
    public async getList(
        query: ApiGetListFormTransactionRequestDto,
        formId: string,
    ): Promise<PaginationResponseDto<FormTransactionResponseDto>> {
        try {
            const view = await this._formDataService.getFormViewsByRoles(formId, this._claims.roles);

            if (!view?.length) {
                return {
                    data: [],
                    total: 0,
                };
            }

            const fields = view?.[0]?.config?.map((item) => item.fieldId) ?? [];
            query.fieldIds = fields;

            const form = await this._formDataService.getActiveForm(formId);
            const formFields = form?.fields ?? [];

            query.filters = [
                ...(query.filters?.filter((item) => {
                    const field = formFields.find(
                        (field) => field.fieldId === item.field || field.fieldId === item.field?.replace('data_register_field_', ''),
                    );
                    if (!field) {
                        return false;
                    }
                    // if (field.type === FormFieldTypeEnum.Lookup) {
                    //     item.queryToOptionIds = true;
                    // }
                    return true;
                }) ?? []),
                ...[{ field: 'formId', operator: OperatorType.equals, value: formId }],
            ];

            const dataRequest: FilterFormTransactionRequestDto = {
                ...query,
                orphanedTransaction: true,
                limitField: true,
                pageIndex: query.pageIndex ?? 10,
                pageSize: query.pageSize ?? 1,
                take: query.take,
                skip: query.skip,
            };
            const transactions = await this._transactionTenancyService.getList(dataRequest);
            const result = this._mapper.mapArray(transactions.data, FormTransactionDto, FormTransactionResponseDto);

            const formFieldsMap = new Map(formFields.map((field) => [field.fieldId, field]));

            result?.forEach((transaction) => {
                transaction?.transactionFields.forEach((field) => {
                    field.fieldLabel = formFieldsMap.get(field.fieldId)?.label ?? field.fieldLabel;
                });
            });
            return {
                data: result,
                total: transactions.total,
            };
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }

    public async getDetail(formId: string, id: string, includeRelatedTransactionIds: boolean): Promise<FormTransactionDetailResponseDto> {
        try {
            const data = await this._transactionTenancyService.get(id, {
                includeRelatedTransactionIds: includeRelatedTransactionIds,
                includeOriginTransactionIds: true,
                includeFieldContextTypes: [TransactionFieldContextTypeEnum.FORM],
            });

            const formVersionCollections = await this._formDataService.getFormVersionCollections(data.formVersionId);

            const transactionDto: FormTransactionDetailResponseDto = this._mapper.map(
                data,
                FormTransactionDto,
                FormTransactionDetailResponseDto,
            );
            // Filter out capture fields
            transactionDto.transactionFields = this._mapper.mapArray(
                (data?.transactionFields ?? []).filter((field) => !field.dependFieldId),
                FormTransactionFieldDto,
                TransactionDetailFieldResponseDto,
            );

            const collectionIdentityIds = formVersionCollections?.map((item) => item.identityId) ?? [];
            transactionDto.collections = await this._getCollectionFieldData(collectionIdentityIds, data.id);

            return transactionDto;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }

    public async deleteTransaction(formId: string, transactionId: string): Promise<boolean> {
        try {
            const transaction = await this._transactionTenancyService.get(transactionId);
            if (transaction.formId !== formId) {
                throw new BadRequestException('Transaction not found');
            }

            await this._transactionTenancyService.delete(transactionId);

            return true;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }

    public getTransactionFieldMapping(
        transactionField: TransactionFieldResponseDto | FormTransactionFieldDto,
        mappings: Record<string, any>[],
    ) {
        if (!transactionField) {
            return transactionField;
        }

        if (API_SYSTEM_FIELD_MAPPING[transactionField.fieldId]) {
            transactionField.fieldId = API_SYSTEM_FIELD_MAPPING[transactionField.fieldId];
        } else {
            transactionField.fieldId =
                mappings?.find((mapping) => mapping.fieldId === transactionField.fieldId)?.key ?? transactionField.fieldId;
        }
    }

    public getCollectionsMapping(collectionIdentityId: string, mappings: Record<string, any>[]): string {
        return (
            mappings?.find((mapping) => mapping.type === 'collection' && mapping.collectionIdentityId === collectionIdentityId)?.key ??
            collectionIdentityId
        );
    }

    private async _getCollectionFieldData(
        collectionIdentityIds: string[],
        transactionId: string,
    ): Promise<Record<string, { transactionFields: TransactionDetailFieldResponseDto[] }>> {
        const tasks = [];

        collectionIdentityIds.forEach((collectionIdentityId) => {
            tasks.push(
                this._formTransactionFieldTenancyService.getListCollectionTransField(
                    {
                        take: 0,
                        skip: 0,
                    },
                    transactionId,
                    collectionIdentityId,
                ),
            );
        });

        const collectionFieldTasks = await Promise.all(tasks);

        const result: Record<string, { transactionFields: TransactionDetailFieldResponseDto[] }> = {};

        collectionIdentityIds.map((collectionIdentityId, index) => {
            // onst collectionKey = this._getCollectionsMapping(collectionIdentityId, mappings);

            const transactionFields = this._mapper.mapArray(
                collectionFieldTasks[index],
                FormTransactionFieldDto,
                TransactionDetailFieldResponseDto,
            );

            // transactionFields.forEach((field) => {
            //     this.getTransactionFieldMapping(field, mappings);
            // });

            result[collectionIdentityId] = {
                transactionFields: transactionFields,
            };
        });

        return result;
    }

    //#region CREATE TRANSACTION
    public async createTransaction(formId: string, dto: CreateApiRequestDto): Promise<string> {
        try {
            // Create transaction
            const hasUpdateFields = !!dto.transactionFields?.length;
            const createResult = await this._transactionTenancyService.create(
                {
                    formId,
                    timezone: dayjs.tz.guess(),
                },
                {
                    ignoreFirstUpdate: hasUpdateFields,
                    returnTransaction: true,
                },
            );

            const transactionId = typeof createResult === 'string' ? createResult : createResult.id;

            if (!hasUpdateFields) {
                return transactionId;
            }

            const dtoFieldMap = new Map(dto.transactionFields.map((field) => [field.fieldId, field]));

            const transactionFields = (createResult as TransactionEntity)?.transactionFields ?? [];

            transactionFields?.forEach((field) => {
                const dtoField = dtoFieldMap.get(field.fieldId);
                if (dtoField) {
                    field.fieldValue = dtoField.fieldValue;
                    if (dtoField.fieldOptionIds?.length) {
                        field.fieldOptionIds = dtoField.fieldOptionIds;
                    }
                }
            });

            const formValues = transactionFields?.reduce((prev, field) => {
                if (field.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                    const collectionKey = UtilsService.combineCollectionKeys({
                        collectionIdentityId: field.collectionId,
                        collectionItemIdentityId: field.collectionItemId,
                        fieldIdentityId: field.fieldId,
                        collectionItemId: '',
                        fieldId: '',
                        collectionItemKey: field.rowKey,
                    });

                    prev[collectionKey] = field.fieldValue;
                    return prev;
                }
                prev[field.fieldId] = field.fieldValue;
                return prev;
            }, {});

            //update transaction with payload
            await this._transactionTenancyService.update(
                transactionId,
                {
                    formValues,
                    formId,
                    transactionId,
                    transactionFields: dto.transactionFields,
                } satisfies EditFormTransactionRequest,
                {
                    shouldRunPopulateFormFields: true,
                    forceRunAutoPopulate: true,
                },
            );

            // this._dataSourceService.update({
            //     id: transactionId,
            //     request: {
            //         formValues,
            //         formId,
            //         transactionId,
            //         transactionFields: dto.transactionFields,
            //     },
            //     option: {
            //         shouldRunPopulateFormFields: true,
            //         forceRunAutoPopulate: true,
            //     },
            // });

            return transactionId;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }
    //#endregion

    //#region UPDATE TRANSACTION
    public async updateTransaction(formId: string, dto: UpdateApiRequestDto): Promise<string> {
        try {
            const { transactionId, collectionFields, transactionFields } = dto;

            const _transactionFields: EditFormTransactionFieldRequest[] = [];

            if (transactionFields?.length) {
                _transactionFields.push(...transactionFields.map((field) => ({ ...field, transactionId })));
            }

            if (collectionFields?.addCollectionRows?.length) {
                const message = EventDrivenService.createCommonEvent({
                    payload: {
                        transactionId: transactionId,
                        fields: collectionFields?.addCollectionRows,
                        sourceOfChange: SourceOfChangeType.MANUAL,
                    },
                    aggregateId: transactionId,
                    type: TransactionEventEnum.FORM_TRANSACTION_FIELD_CREATED,
                    name: TransactionEventEnum.FORM_TRANSACTION_FIELD_CREATED,
                });
                await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);
            }

            if (collectionFields?.deleteCollectionsRows?.length) {
                const deleteResult = await this._apiCollectionDataService.deleteFormCollectionItems({
                    transactionId,
                    rowKeys: collectionFields.deleteCollectionsRows,
                });

                if (deleteResult) {
                    const message = EventDrivenService.createCommonEvent({
                        payload: {
                            transactionId: transactionId,
                            fields: deleteResult?.result ?? [],
                            sourceOfChange: SourceOfChangeType.MANUAL,
                            previous: deleteResult?.previous ?? [],
                        },
                        aggregateId: transactionId,
                        type: TransactionEventEnum.FORM_TRANSACTION_FIELD_DELETED,
                        name: TransactionEventEnum.FORM_TRANSACTION_FIELD_DELETED,
                    });
                    await this._eventDrivenService.publishMessage(TransactionTopicEnum.FORM_TRANSACTION_FIELD_TOPIC, message);
                }
            }

            if (!_transactionFields?.length) {
                return transactionId;
            }

            const formValues = _transactionFields.reduce((prev, field) => {
                if (field.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                    const collectionKey = UtilsService.combineCollectionKeys({
                        collectionIdentityId: field.collectionId,
                        collectionItemIdentityId: field.collectionItemId,
                        fieldIdentityId: field.fieldId,
                        collectionItemId: '',
                        fieldId: '',
                        collectionItemKey: field.rowKey,
                    });

                    prev[collectionKey] = field.fieldValue;
                    return prev;
                }
                prev[field.fieldId] = field.fieldValue;
                return prev;
            }, {});

            await this._transactionTenancyService.update(
                transactionId,
                {
                    formValues,
                    formId,
                    transactionId,
                    transactionFields: _transactionFields,
                } satisfies EditFormTransactionRequest,
                {
                    shouldRunPopulateFormFields: true,
                },
            );

            // this._dataSourceService.update({
            //     id: transactionId,
            //     request: {
            //         formValues,
            //         formId,
            //         transactionId,
            //         transactionFields: _transactionFields,
            //     },
            //     option: {
            //         shouldRunPopulateFormFields: true,
            //     },
            // });

            return transactionId;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }
    //#endregion
}
