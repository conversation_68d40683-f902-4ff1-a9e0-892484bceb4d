import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';
import { ClaimService, LoggerService } from '../../../common/src';
import { OperatorType } from '../../../common/src/modules/shared/enums/operator.enum';
import { USER_CLAIMS } from '../../../constant';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { RoleTenancyEntity } from '../../../database/src/entities/tenancy/role.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { DataRegisterTransactionOptionDto } from '../../data-register/dtos/respsonses/data-register-transaction-option.dto';
import { DataRegisterTransactionTenancyService } from '../../data-register/services/data-register-transaction.tenancy.service';
import {
    ApiGetLookupRequestDto,
    ApiLookupRequestDto,
    ApiRoleLookupRequestDto,
    ApiUserLookupRequestDto,
} from '../dtos/requests/get-lookup.request';
import { ApiGetOptionRequestDto } from '../dtos/requests/get-option.request';
import { FormFieldDto } from '../dtos/responses/api-form.dto';
import { ApiLookupResponseDto } from '../dtos/responses/api-lookup.dto';
import { ViewFieldResponseDto } from '../dtos/responses/form-field.response.dto';
import { FormViewResponseDto } from '../dtos/responses/form-view-response.dto';
import { DataRegisterDataService } from './data-services/data-register.data.service';
import { FormDataService } from './data-services/form.data.service';

@Injectable()
export class ApiFormService {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
        @Inject(USER_CLAIMS)
        private readonly _userClaims: ClaimService,
        private readonly _formDataService: FormDataService,
        private readonly _loggerService: LoggerService,
        private readonly _dataRegisterDataService: DataRegisterDataService,
        @InjectMapper()
        private readonly _mapper: Mapper,
        private readonly _service: DataRegisterTransactionTenancyService,
    ) {}

    async getFormView(formId: string): Promise<FormViewResponseDto | null> {
        const roleIds = (this._userClaims.roles as string[]) ?? [];
        const formViews = await this._formDataService.getFormViewsByRoles(formId, roleIds);
        if (!formViews?.length) {
            return null;
        }
        const formView = formViews[0];

        const fields = formView.config?.map((config) => {
            return {
                fieldId: config.fieldId,
                label: config.label,
                type: config.type,
            } as ViewFieldResponseDto;
        });
        return {
            id: formView.id,
            name: formView.name,
            fields: fields ?? [],
        };
    }

    async getFormFieldById(formId: string, fieldId: string): Promise<FormFieldDto | null> {
        const formField = await this._formDataService.getFormFieldById(formId, fieldId);
        if (!formField) {
            return null;
        }
        return formField;
    }

    public async lookUpOptions(query: ApiLookupRequestDto) {
        try {
            const { fieldId, formId, pageIndex, pageSize, fieldKey } = query;

            if (!fieldId) {
                throw new BadRequestException(`${fieldKey} is not valid`);
            }

            const field = await this.getFormFieldById(formId, fieldId);
            if (!field) {
                throw new NotFoundException(`${fieldKey} is not found`);
            }

            let result = {
                data: [],
                total: 0,
            };

            switch (field.type) {
                case FormFieldTypeEnum.Lookup:
                    result = await this._getLookup(field, pageIndex, pageSize);
                    break;

                case FormFieldTypeEnum.UserLookup:
                    result = await this._getUserLookup(query);
                    break;

                case FormFieldTypeEnum.RoleLookup:
                    result = await this._getRoleLookup(query);
                    break;

                default:
                    throw new BadRequestException(`${fieldKey} is not a valid field`);
            }

            return result;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }

    public async getOptions(query: ApiGetOptionRequestDto) {
        const { fieldId, formId, fieldKey } = query;

        if (!fieldId) {
            throw new BadRequestException(`${fieldKey} is not valid`);
        }

        const field = await this.getFormFieldById(formId, fieldId);
        if (!field) {
            throw new NotFoundException(`${fieldKey} is not found`);
        }

        if (field?.type !== FormFieldTypeEnum.Select) {
            throw new BadRequestException(`${fieldKey} is not a select field`);
        }

        const options = field?.configuration?.options ?? [];

        if (!options?.length) return [];

        return options.map((option) => ({
            id: option.value,
            name: option.label,
        }));
    }

    private async _getUserLookup(query: ApiLookupRequestDto) {
        const request: ApiUserLookupRequestDto = {
            select: ['firstName', 'secondName', 'email', 'id'],
            onlyActive: true,
            filterType: 'or',
            take: query.pageSize,
            skip: query.pageIndex,
        };

        const data = await this._dataRegisterDataService.getUserLookup(request);

        return {
            data: this._mapper.mapArray(data?.data ?? [], UserTenancyEntity, ApiLookupResponseDto),
            total: data.total,
        };
    }

    private async _getRoleLookup(query: ApiRoleLookupRequestDto) {
        const request: ApiRoleLookupRequestDto = {
            select: ['id', 'name'],
            operatorType: 'or',
            take: query.pageSize,
            skip: query.pageIndex,
        };

        const data = await this._dataRegisterDataService.getRoleLookup(request);

        return {
            data: this._mapper.mapArray(data?.data ?? [], RoleTenancyEntity, ApiLookupResponseDto),
            total: data.total,
        };
    }

    private async _getLookup(field: FormFieldDto, pageIndex: number, pageSize: number) {
        const fieldCondition = field?.configuration?.fieldCondition ?? null;
        const lookupDataSet = field.lookupDataSet ?? field?.configuration?.dataset ?? null;
        const lookupDataRegisterId = field.lookupTargetId ?? field?.configuration?.targetId ?? null;

        const queryOption: ApiGetLookupRequestDto = {
            take: pageSize,
            skip: pageIndex,
            filters: [],
        };

        if (fieldCondition) {
            queryOption.filters.push({
                field: 'filterCondition',
                operator: OperatorType.equals,
                value: fieldCondition,
            });
        }

        if (lookupDataSet) {
            queryOption.filters.push({
                field: 'dataset',
                operator: OperatorType.equals,
                value: lookupDataSet,
            });
        }

        if (lookupDataRegisterId) {
            queryOption.filters.push({
                field: 'dataRegisterId',
                operator: OperatorType.equals,
                value: lookupDataRegisterId,
            });
        }

        const data = await this._service.lookUpOptions(v4(), queryOption);
        return {
            data: this._mapper.mapArray(data?.data ?? [], DataRegisterTransactionOptionDto, ApiLookupResponseDto),
            total: data.total,
        };
    }
}
