import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ApiCollectionDataService } from '.';
import { TRANSACTION_FIELD_ID } from '../../../common/src/constant/field';
import { FormRoleStageACLDto } from '../../../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { AddOrDeleteCollectionRow } from '../dtos/requests/api-collection-request';
import { ValidateFieldMappingService } from '../utils/validate-field-mapping.service';
import { ValidateTransactionFieldACLService } from '../utils/validate-transaction-field-acl.service';

@Injectable()
export class PayloadValidationService {
    constructor(
        private readonly validateFieldMappingService: ValidateFieldMappingService,
        private readonly apiCollectionDataService: ApiCollectionDataService,
        private readonly validateTransactionFieldACLService: ValidateTransactionFieldACLService,
    ) {}

    public async validateTransactionFieldPayload({
        stageRoleACLs,
        transactionFields,
        dataSource,
        availableRoles,
        transactionId,
        formFieldIdMapping,
        formVersionId,
        collectionFieldIdMapping,
    }: {
        stageRoleACLs: FormRoleStageACLDto[];
        transactionFields: EditFormTransactionFieldRequest[];
        dataSource: DataSource;
        availableRoles: string[];
        transactionId?: string;
        formFieldIdMapping?: Map<string, string>;
        formVersionId?: string;
        collectionFieldIdMapping?: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        >;
    }) {
        const formFields = stageRoleACLs.flatMap((item) => item.fieldsConfigurations || []) as FormFieldTenancyEntity[];

        const { transactionFormFields, transactionCollectionFields } = (transactionFields || []).reduce(
            (
                acc: {
                    transactionFormFields: EditFormTransactionFieldRequest[];
                    transactionCollectionFields: EditFormTransactionFieldRequest[];
                },
                field: EditFormTransactionFieldRequest,
            ) => {
                if (field.fieldId === TRANSACTION_FIELD_ID) {
                    return acc;
                }

                if (field.contextType === TransactionFieldContextTypeEnum.FORM) {
                    acc.transactionFormFields.push(field);
                } else if (field.contextType === TransactionFieldContextTypeEnum.COLLECTION) {
                    acc.transactionCollectionFields.push(field);
                }

                return acc;
            },
            { transactionFormFields: [], transactionCollectionFields: [] },
        );

        const fieldAclErrorMap: Record<string, any> = {};
        const fieldDataTypeErrorMap: Record<string, any> = {};

        let collectionFieldBuilders = [];
        let collectionAdditionalFields = [];
        if (transactionCollectionFields?.length) {
            const { collectionFieldBuilders: builder, collectionAdditionalFields: additionalFields } =
                await this.apiCollectionDataService.getCollectionFieldsConfigurationByTransactionCollectionFields(
                    formVersionId,
                    transactionCollectionFields,
                );

            collectionFieldBuilders = builder;
            collectionAdditionalFields = additionalFields;
        }

        for (const stageRoleACL of stageRoleACLs) {
            const fieldErrorValidation = this.validateTransactionFieldACLService.validateTransactionFieldsACL({
                stageRoleACL: stageRoleACL,
                transactionFields: transactionFields,
                fieldsConfigurations: formFields,
                formFieldIdMapping,
                collectionFieldIdMapping,
            });

            if (fieldErrorValidation) {
                fieldAclErrorMap[stageRoleACL.roleId] = fieldErrorValidation;
            } else {
                fieldAclErrorMap[stageRoleACL.roleId] = null;
            }

            const fieldDataTypeValidation = await this.validateFieldMappingService.apiValidateTransactionFieldsDataType({
                fields: transactionFields,
                fieldsConfigurations: formFields,
                dataRegisterTransactionRepository: dataSource.getRepository(DataRegisterTransactionTenancyEntity),
                formTransactionFieldRepository: dataSource.getRepository(TransactionFieldEntity),
                userRepository: dataSource.getRepository(UserTenancyEntity),
                transactionId: transactionId || '',
                availableRoles,
                formFieldIdMapping,
                collectionFieldBuilders,
                collectionAdditionalFields,
                formVersionId,
                collectionFieldIdMapping,
            });

            if (fieldDataTypeValidation) {
                fieldDataTypeErrorMap[stageRoleACL.roleId] = fieldDataTypeValidation;
            } else {
                fieldDataTypeErrorMap[stageRoleACL.roleId] = null;
            }
        }

        const hasFieldAclError = Object.values(fieldAclErrorMap).every((item) => item !== null);

        const hasFieldDataTypeError = Object.values(fieldDataTypeErrorMap).every((item) => item !== null);

        const isValid = !hasFieldAclError && !hasFieldDataTypeError;

        return {
            isValid,
            hasFieldAclError,
            hasFieldDataTypeError,
            fieldAclErrorMap,
            fieldDataTypeErrorMap,
        };
    }

    public async validationAddCollectionRow(collection: AddOrDeleteCollectionRow, stageRoleACLs: FormRoleStageACLDto[]) {
        const result = {
            valid: true,
            errors: [],
        };

        for (const [collectionKey, rowIds] of Object.entries(collection)) {
            for (const rowId of rowIds) {
                const hasPermission = stageRoleACLs.some((acl) => acl.createDeleteCollection[rowId]?.canCreate);

                if (!hasPermission) {
                    result.valid = false;
                    result.errors.push(`No permission to add this ${rowId} row to the collection ${collectionKey}`);
                }
            }
        }

        return result;
    }

    public async validationDeleteCollectionRow(collection: AddOrDeleteCollectionRow, stageRoleACLs: FormRoleStageACLDto[]) {
        const result = {
            valid: true,
            errors: [],
        };

        for (const [collectionKey, rowIds] of Object.entries(collection)) {
            for (const rowId of rowIds) {
                const hasPermission = stageRoleACLs.some((acl) => acl.createDeleteCollection[rowId]?.canDelete);

                if (!hasPermission) {
                    result.valid = false;
                    result.errors.push(`No permission to delete this ${rowId} row to the collection ${collectionKey}`);
                }
            }
        }

        return result;
    }
}
