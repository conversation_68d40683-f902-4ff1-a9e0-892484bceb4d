import { BadRequestException, Injectable } from '@nestjs/common';

import { isEmpty } from 'lodash';
import { FilterOptionDto } from '../../../common/src/modules/shared/dtos/filter-option.dto';
import { OrderOptionDto } from '../../../common/src/modules/shared/dtos/order-option.dto';
import { PREFIX } from '../../../common/src/modules/shared/enums/prefix.enum';
import { API_SYSTEM_FIELD_MAPPING } from '../../../constant/field';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { ApiMappingConfiguration } from '../dtos/api-mapping-configuration';
import { CreateApiRequestDto } from '../dtos/requests/create-api-request';
import { ApiGetListFormTransactionRequestDto } from '../dtos/requests/get-list-form-transaction.request';
import { UpdateApiRequestDto } from '../dtos/requests/update-api-request';
import { ValidateFieldMappingService } from '../utils/validate-field-mapping.service';
import { TransactionDataService } from './data-services/transaction.data.service';

@Injectable()
export class FormatRequestService {
    constructor(
        private readonly _validateFieldMappingService: ValidateFieldMappingService,
        private readonly _transactionDataService: TransactionDataService,
    ) {}

    public async formatFormFieldRequest({
        dto,
        fieldMappings,
        transactionId,
    }: {
        dto: CreateApiRequestDto | UpdateApiRequestDto;
        transactionId?: string;
        fieldMappings?: ApiMappingConfiguration[];
    }) {
        const { formFields } = dto;

        const fieldMappingConfigurations = (fieldMappings ?? []).filter((mapping) => mapping.type === 'field');

        let transactionFields: EditFormTransactionFieldRequest[] = [];
        let formFieldIdMapping: Map<string, string> = new Map();

        if (formFields && !isEmpty(formFields)) {
            const validationResult = this._validateFieldMappingService.validateFormFields(formFields, fieldMappingConfigurations);

            if (!validationResult.isValid) {
                throw new BadRequestException(validationResult.errors);
            }

            const { transactionFields: tf, formFieldIdMapping: fim } = await this._mappingFormFields({
                formFields,
                fieldMappings: fieldMappingConfigurations,
                transactionId,
            });

            transactionFields = tf;
            formFieldIdMapping = fim;
        }

        return { transactionFields, formFieldIdMapping };
    }

    public async formatFormTransactionListQuery({
        query,
        fieldMappings,
    }: {
        query: ApiGetListFormTransactionRequestDto;
        fieldMappings: ApiMappingConfiguration[];
    }) {
        const { filters, sorters } = query;
        const fieldMappingConfigurations = (fieldMappings ?? []).filter((mapping) => mapping.type === 'field');
        if (filters && !isEmpty(filters)) {
            const formattedFilters = filters.map((filter) => {
                const key = Object.keys(API_SYSTEM_FIELD_MAPPING).find((key) => API_SYSTEM_FIELD_MAPPING[key] === filter.field);
                if (key) {
                    return {
                        ...filter,
                        field: filter.field === 'transaction_code' ? `${PREFIX.DATA_REGISTER_FIELD}_${key}` : key,
                    } as FilterOptionDto;
                }
                const mappingField = fieldMappingConfigurations.find((item) => filter.field === item.key);
                return {
                    ...filter,
                    field: mappingField?.key ? `${PREFIX.DATA_REGISTER_FIELD}_${mappingField.fieldId}` : key,
                } as FilterOptionDto;
            });
            query.filters = formattedFilters;
        }

        if (sorters && !isEmpty(sorters)) {
            const formattedSorters = sorters.map((sorter) => {
                const key = Object.keys(API_SYSTEM_FIELD_MAPPING).find((key) => API_SYSTEM_FIELD_MAPPING[key] === sorter.field);
                if (key) {
                    return {
                        ...sorter,
                        field: sorter.field === 'transaction_code' ? `${PREFIX.DATA_REGISTER_FIELD}_${key}` : key,
                    } as OrderOptionDto;
                }
                const mappingField = fieldMappingConfigurations.find((item) => sorter.field === item.key);
                return {
                    ...sorter,
                    field: mappingField?.key ? `${PREFIX.DATA_REGISTER_FIELD}_${mappingField.fieldId}` : sorter.field,
                } as OrderOptionDto;
            });
            query.sorters = formattedSorters;
        }
    }

    private async _mappingFormFields({
        formFields,
        fieldMappings,
        transactionId,
    }: {
        formFields: Record<string, string | string[] | null>;
        fieldMappings: ApiMappingConfiguration[];
        transactionId?: string;
    }) {
        const transactionFields: EditFormTransactionFieldRequest[] = [];

        const formFieldIdMapping = new Map<string, string>();

        for (const [fieldKey, fieldValue] of Object.entries(formFields)) {
            const fieldMapping = fieldMappings.find((mapping) => mapping.key === fieldKey);

            if (!fieldMapping) {
                continue;
            }

            const transactionField = new EditFormTransactionFieldRequest();

            if (Array.isArray(fieldValue)) {
                transactionField.fieldOptionIds = fieldValue;
                transactionField.fieldValue = fieldValue.join(',');
            } else {
                transactionField.fieldValue = fieldValue;
            }

            transactionField.fieldId = fieldMapping.fieldId;
            transactionField.contextType = TransactionFieldContextTypeEnum.FORM;
            if (transactionId) {
                transactionField.transactionId = transactionId;
            }

            if (fieldMapping.fieldType === FormFieldTypeEnum.Document) {
                const file = await this._transactionDataService.getFileAutoGeneratedByTransactionId(
                    transactionId,
                    transactionField.fieldValue,
                );

                transactionField.fieldValue = file?.fileName;
                transactionField.data = {
                    filePath: file?.dataPath,
                    docFieldType: 'upload',
                } as any;
            }

            formFieldIdMapping.set(fieldMapping.fieldId, fieldKey);

            transactionFields.push(transactionField);
        }

        return { transactionFields, formFieldIdMapping };
    }
}
