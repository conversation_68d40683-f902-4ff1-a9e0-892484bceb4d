import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { cloneDeep, merge, partition, uniq } from 'lodash';
import { Brackets, In, Repository } from 'typeorm';
import { v7 } from 'uuid';
import { FormCollectionCacheService, LoggerService } from '../../../../common/src/modules/shared/services';
import { SELECTABLE_FIELD_TYPES } from '../../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { DataRegisterFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { TransactionFieldEntity } from '../../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { FormCollectionItemTypeEnum } from '../../../../database/src/shared/enums/form-collection-item-type.enum';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';
import { FormCollectionItemTransactionFieldDto } from '../../../form/dtos';
import { EditFormTransactionFieldRequest } from '../../../form/dtos/requests/create-form-transaction.request';
import { DefaultTransactionFieldService } from '../../../form/services/default-transaction-field.service';
import { FormCollectionTransactionFieldService } from '../../../form/services/form-collection-transaction-field.service';
import { ApiGetAdditionalFields } from '../../dtos/requests/get-additional-fields.request';
import { FormDataService } from './form.data.service';
import { TransactionDataService } from './transaction.data.service';

export type CollectionFieldBuilder = DataRegisterFieldTenancyEntity & {
    formCollectionIdentityId: string;
    formVersionId: string;
};

@Injectable()
export class ApiCollectionDataService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTenancyRepository: Repository<FormCollectionTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly _formCollectionAdditionalFieldTenancyRepository: Repository<FormCollectionAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_ITEM_TENANCY_REPOSITORY)
        private readonly _formCollectionItemTenancyRepository: Repository<FormCollectionItemTenancyEntity>,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly _transactionFieldTenancyRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_TENANCY_REPOSITORY)
        private readonly _dataRegisterVersionTenancyRepository: Repository<DataRegisterVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly _dataRegisterFieldTenancyRepository: Repository<DataRegisterFieldTenancyEntity>,

        private readonly _loggerService: LoggerService,

        private readonly _formDataService: FormDataService,
        private readonly _transactionDataService: TransactionDataService,
        private readonly _formCollectionCacheService: FormCollectionCacheService,
    ) {}

    public async getFormCollectionOptions(transactionId: string, collectionItemIdentityIds?: string[]) {
        const transaction = await this._transactionDataService.getTransactionById(transactionId);

        if (!transaction) {
            return [];
        }

        const formCollections = await this._formCollectionTenancyRepository.find({
            where: {
                formVersionId: transaction.formVersionId,
            },
        });

        if (!formCollections?.length) {
            return [];
        }

        const formCollectionIds = formCollections.map((collection) => collection.id);

        const collectionItemBuilder = this._formCollectionItemTenancyRepository
            .createQueryBuilder('formCollectionItem')
            .where('formCollectionItem.formCollectionId IN (:...formCollectionIds)', { formCollectionIds })
            .andWhere(
                new Brackets((qb) => {
                    qb.where('formCollectionItem.type = :optionType', { optionType: FormCollectionItemTypeEnum.OPTION }).orWhere(
                        new Brackets((_qb) => {
                            _qb.where('formCollectionItem.type = :defaultType', {
                                defaultType: FormCollectionItemTypeEnum.DEFAULT,
                            }).andWhere(`"formCollectionItem"."setting"::jsonb->>'isMultipleTime' = :isMultipleTime`, {
                                isMultipleTime: true,
                            });
                        }),
                    );
                }),
            );

        if (collectionItemIdentityIds?.length) {
            collectionItemBuilder.andWhere('formCollectionItem.identityId IN (:...collectionItemIdentityIds)', {
                collectionItemIdentityIds,
            });
        }

        const formCollectionItems = await collectionItemBuilder.getMany();

        let [collectionItemsMultipleTime, collectionItemsSingleTime] = partition(
            formCollectionItems ?? [],
            (item) => item?.setting?.isMultipleTime,
        );

        const collectionItemsSingleTimeIds = collectionItemsSingleTime.map((item) => item.identityId);

        if (collectionItemsSingleTimeIds?.length) {
            const transactionFields = await this._transactionFieldTenancyRepository.find({
                where: {
                    collectionItemId: In(collectionItemsSingleTime.map((item) => item.identityId)),
                    transactionId,
                },
            });

            collectionItemsSingleTime = collectionItemsSingleTime.filter(
                (item) => !transactionFields.some((field) => field.collectionItemId === item.identityId),
            );
        }

        const collectionItems = merge(collectionItemsMultipleTime, collectionItemsSingleTime);

        formCollections.forEach((collection) => {
            const _collectionItems = collectionItems.filter((item) => item.formCollectionId === collection.id);

            collection.formCollectionItems = _collectionItems;
        });

        return formCollections;
    }

    public async deleteFormCollectionItems({ transactionId, rowKeys }: { transactionId: string; rowKeys: string[] }) {
        try {
            if (!rowKeys?.length) {
                return false;
            }

            const entitiesToDelete = await this._transactionFieldTenancyRepository.find({
                where: {
                    transactionId,
                    rowKey: In(rowKeys),
                },
            });

            const previous = cloneDeep(entitiesToDelete);
            const result = await this._transactionFieldTenancyRepository.softRemove(entitiesToDelete);

            return {
                previous,
                result,
            };
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            return false;
        }
    }

    public async formatAddFormCollectionItems({
        transactionId,
        collectionItemIdentityIds,
        stageEntities,
        formCollectionTransactionFieldService,
        defaultTransactionFieldService,
        formCollectionCacheService,
        collectionFieldIdMapping,
    }: {
        transactionId: string;
        collectionItemIdentityIds: string[];
        stageEntities?: StageTenancyEntity[];
        formCollectionTransactionFieldService: FormCollectionTransactionFieldService;
        defaultTransactionFieldService: DefaultTransactionFieldService;
        formCollectionCacheService: FormCollectionCacheService;
        collectionFieldIdMapping?: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        >;
    }) {
        const transaction = await this._transactionDataService.getTransactionById(transactionId);

        const _stageEntities = stageEntities ?? (await this._formDataService.getStageEntities(transaction.formVersionId));

        if (!transaction) {
            throw new BadRequestException('Transaction not found');
        }

        const { formCollectionItems, formCollections } = await this._getFormCollections({
            formVersionId: transaction.formVersionId,
            collectionItemIdentityIds: collectionItemIdentityIds,
        });

        if (!formCollectionItems?.length || !formCollections?.length) {
            throw new BadRequestException('Form collection item not found');
        }

        const transactionFields: TransactionFieldEntity[] = [];

        for (const formCollection of formCollections) {
            const _formCollectionItems = formCollectionItems.filter((item) => item.formCollectionId === formCollection.id);

            if (!_formCollectionItems?.length) {
                continue;
            }

            const { formCollectionItemIds, formCollectionItemIdentityIds } = _formCollectionItems?.reduce(
                (acc, item) => {
                    acc.formCollectionItemIds.push(item.id);
                    acc.formCollectionItemIdentityIds.push(item.identityId);
                    return acc;
                },
                { formCollectionItemIds: [], formCollectionItemIdentityIds: [] },
            );

            const [formCollectionFields, formCollectionTransactionFields, formCollectionDefaultValues, formCollectionAdditionalFields] =
                await Promise.all([
                    this._getFormCollectionFieldsData(formCollection.dataRegisterVersionId),
                    formCollectionTransactionFieldService.getFormCollectionItemTransactionFields({
                        formCollectionIdentityId: formCollection.identityId,
                        formVersionId: transaction.formVersionId,
                    }),
                    defaultTransactionFieldService.getDefaultCollectionFields({
                        formVersionId: transaction.formVersionId,
                        transaction: transaction,
                        stages: _stageEntities,
                        option: {
                            collectionIdentityIds: [formCollection.identityId],
                            collectionItemIdentityIds: formCollectionItemIdentityIds,
                            collectionTypes: [FormCollectionItemTypeEnum.DEFAULT, FormCollectionItemTypeEnum.OPTION],
                        },
                    }),

                    this._getFormCollectionAdditionalFields({
                        dto: {
                            transactionId: transactionId,
                        },
                        collectionItemIds: formCollectionItemIds,
                        formCollectionCacheService: formCollectionCacheService,
                    }),
                ]);

            for (const formCollectionItem of _formCollectionItems) {
                const formCollectionTransactionFieldEntities = this._transformCollectionFieldsToTransactionFields(
                    formCollectionFields,
                    formCollectionTransactionFields,
                    formCollectionDefaultValues?.collectionTransactionFields ?? [],
                    formCollectionAdditionalFields,
                    formCollection.identityId,
                    formCollectionItem.identityId,
                    transactionId,
                    collectionFieldIdMapping,
                );

                transactionFields.push(...formCollectionTransactionFieldEntities);
            }
        }

        return transactionFields;
    }

    public async formatUpdateFormCollectionItems({
        transactionId,
        updateCollections,
        collectionFieldIdMapping,
        collectionIdMapping,
    }: {
        transactionId: string;
        updateCollections: TransactionFieldEntity[];
        collectionFieldIdMapping?: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        >;
        collectionIdMapping?: Map<string, string>;
    }) {
        if (!updateCollections?.length) {
            return [];
        }

        const errors: string[] = [];

        const { rowKeys, fieldIds } = updateCollections.reduce(
            (acc, updateCollection) => {
                acc.rowKeys.push(updateCollection.rowKey);
                acc.fieldIds.push(updateCollection.fieldId);

                acc.rowKeys = uniq(acc.rowKeys);
                acc.fieldIds = uniq(acc.fieldIds);
                return acc;
            },
            { rowKeys: [], fieldIds: [], collectionItemIds: [] },
        );

        const transactionFieldEntities = await this._transactionFieldTenancyRepository.find({
            where: {
                transactionId: transactionId,
                rowKey: In(rowKeys),
                // fieldId: In(uniq(fieldIds)),
            },
            select: ['id', 'rowKey', 'fieldId', 'collectionItemId', 'fieldType'],
        });

        if (!transactionFieldEntities?.length) {
            for (const [rowKey, collectionKey] of collectionIdMapping) {
                errors.push(`Row key ${rowKey} of collection ${collectionKey} not found in transaction`);
            }

            if (errors?.length) {
                throw new BadRequestException(errors);
            }

            return [];
        } else {
            for (const [rowKey, collectionKey] of collectionIdMapping) {
                const transactionFields = transactionFieldEntities.filter((tf) => tf.rowKey === rowKey && fieldIds.includes(tf.fieldId));

                if (!transactionFields?.length) {
                    errors.push(`Row key ${rowKey} of collection ${collectionKey} not found in transaction`);
                }
            }

            if (errors?.length) {
                throw new BadRequestException(errors);
            }
        }

        const transactionFields = transactionFieldEntities.filter((tf) => fieldIds.includes(tf.fieldId));

        updateCollections.forEach((updateCollection) => {
            const transactionField = transactionFields.find(
                (field) => field.rowKey === updateCollection.rowKey && field.fieldId === updateCollection.fieldId,
            );

            if (transactionField) {
                updateCollection.collectionItemId = transactionField.collectionItemId;
                updateCollection.fieldType = transactionField.fieldType;
            }
        });

        return updateCollections;
    }

    public async getCollectionFieldsConfigurationByTransactionCollectionFields(
        formVersionId: string,
        transactionFields: EditFormTransactionFieldRequest[],
    ): Promise<{
        collectionFieldBuilders: CollectionFieldBuilder[];
        collectionAdditionalFields: FormCollectionAdditionalFieldTenancyEntity[];
    }> {
        const result = {
            collectionFieldBuilders: [],
            collectionAdditionalFields: [],
        };

        if (!transactionFields?.length) {
            return result;
        }

        const transactionId = transactionFields?.[0]?.transactionId;

        const { collectionIds, collectionItemIds } = transactionFields.reduce(
            (acc, field) => {
                acc.collectionIds.push(field.collectionId);
                acc.collectionItemIds.push(field.collectionItemId);

                acc.collectionIds = uniq(acc.collectionIds);
                acc.collectionItemIds = uniq(acc.collectionItemIds);
                return acc;
            },
            { collectionIds: [], collectionItemIds: [] },
        );

        if (!collectionIds?.length) {
            return result;
        }

        const formCollections = await this._formCollectionTenancyRepository.find({
            where: {
                identityId: In(collectionIds),
                formVersionId: formVersionId,
            },
            select: ['id', 'identityId', 'dataRegisterVersionId'],
        });

        const dataRegisterVersionIds = uniq(
            formCollections?.length ? formCollections.map((collection) => collection.dataRegisterVersionId) : [],
        );

        const [collectionFieldsBuilder, collectionAdditionalFields] = await Promise.all([
            this._getFormCollectionFieldsBuilder(dataRegisterVersionIds),
            this._getFormCollectionAdditionalFields({
                dto: {
                    transactionId: transactionId,
                },
                formCollectionCacheService: this._formCollectionCacheService,
                collectionItemIds: collectionItemIds,
            }),
        ]);

        const collectionFieldBuilders = collectionFieldsBuilder.map((field) => {
            const _collectionFieldsBuilder: CollectionFieldBuilder = {
                ...field,
                formCollectionIdentityId: '',
                formVersionId: '',
            };

            const collection = formCollections.find((collection) => collection.dataRegisterVersionId === field.dataRegisterVersionId);
            if (collection) {
                _collectionFieldsBuilder.formCollectionIdentityId = collection.identityId;
                _collectionFieldsBuilder.formVersionId = formVersionId;
            }

            return _collectionFieldsBuilder;
        });

        return {
            collectionFieldBuilders,
            collectionAdditionalFields,
        };
    }

    public async getCollectionByRowKeys(rowKeys: string[], transactionId: string) {
        if (!rowKeys?.length) {
            return [];
        }

        const collections = await this._transactionFieldTenancyRepository.find({
            where: {
                rowKey: In(rowKeys),
                transactionId: transactionId,
            },
        });

        return collections;
    }

    private async _getFormCollections({
        formVersionId,
        collectionItemIdentityIds,
        collectionItemIds,
    }: {
        formVersionId: string;
        collectionItemIds?: string[];
        collectionItemIdentityIds?: string[];
    }) {
        let formCollections = await this._formCollectionTenancyRepository.find({
            where: {
                formVersionId: formVersionId,
            },
            select: ['id', 'identityId', 'dataRegisterVersionId'],
        });

        if (!formCollections?.length) {
            return {
                formCollectionItems: [],
                formCollections: [],
            };
        }

        const formCollectionIds = formCollections.map((collection) => collection.id);

        const formCollectionItemBuilder = this._formCollectionItemTenancyRepository
            .createQueryBuilder('formCollectionItem')
            .where('formCollectionItem.formCollectionId In(:...formCollectionIds)', { formCollectionIds });

        if (collectionItemIdentityIds?.length) {
            formCollectionItemBuilder.andWhere('formCollectionItem.identityId In(:...collectionItemIdentityIds)', {
                collectionItemIdentityIds,
            });
        }

        if (collectionItemIds?.length) {
            formCollectionItemBuilder.andWhere('formCollectionItem.id In(:...collectionItemIds)', {
                collectionItemIds,
            });
        }

        const formCollectionItems = await formCollectionItemBuilder.getMany();
        formCollections = formCollections.filter((collection) =>
            formCollectionItems.some((item) => item.formCollectionId === collection.id),
        );

        return { formCollectionItems, formCollections };
    }

    private async _getFormCollectionFieldsData(formVersionId: string) {
        const drv = await this._dataRegisterVersionTenancyRepository.findOne({
            where: {
                id: formVersionId,
            },
            relations: ['fields'],
        });

        if (!drv) {
            return [];
        }

        return drv?.fields ?? [];
    }

    private async _getFormCollectionFieldsBuilder(formVersionIds: string[]) {
        if (!formVersionIds?.length) {
            return [];
        }

        const fields = await this._dataRegisterFieldTenancyRepository.find({
            where: {
                dataRegisterVersionId: In(formVersionIds),
            },
        });

        return fields ?? [];
    }

    private _transformCollectionFieldsToTransactionFields(
        formCollectionFields: DataRegisterFieldTenancyEntity[],
        formCollectionTransactionFields: FormCollectionItemTransactionFieldDto[],
        formCollectionDefaultValues: TransactionFieldEntity[],
        formCollectionAdditionalFields: FormCollectionAdditionalFieldTenancyEntity[],
        formCollectionIdentityId: string,
        formCollectionItemIdentityId: string,
        transactionId: string,
        collectionFieldIdMapping?: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        >,
    ) {
        const formCollectionTransactionFieldEntities: TransactionFieldEntity[] = [];
        const rowKey = v7();

        formCollectionFields.forEach((field) => {
            const transactionField = new TransactionFieldEntity();

            transactionField.fieldId = field.fieldId;
            transactionField.collectionItemId = formCollectionItemIdentityId;
            transactionField.collectionId = formCollectionIdentityId;
            transactionField.transactionId = transactionId;
            transactionField.rowKey = rowKey;
            transactionField.contextType = TransactionFieldContextTypeEnum.COLLECTION;

            const additionalField = formCollectionAdditionalFields.find(
                (f) => f.fieldId === field.fieldId && f.formCollectionItemIdentityId === formCollectionIdentityId,
            );

            if (additionalField) {
                transactionField.fieldType = additionalField.type;
            } else {
                transactionField.fieldType = field.type;
            }

            const fieldValue = formCollectionTransactionFields.find(
                (f) => f.fieldId === field.fieldId && f.formCollectionItemIdentityId === formCollectionItemIdentityId,
            );

            if (fieldValue) {
                transactionField.fieldValue = fieldValue.fieldValue;
                transactionField.fieldOptionIds = fieldValue.fieldOptionIds;
            } else if (formCollectionDefaultValues?.length) {
                const defaultField = formCollectionDefaultValues.find(
                    (f) => f.fieldId === field.fieldId && f.collectionItemId === formCollectionItemIdentityId,
                );

                if (defaultField) {
                    transactionField.fieldValue = defaultField.fieldValue;
                    transactionField.fieldOptionIds = defaultField.fieldOptionIds;
                }
            }

            if (SELECTABLE_FIELD_TYPES.includes(transactionField.fieldType)) {
                transactionField.fieldValue = transactionField.fieldOptionIds?.length ? transactionField.fieldOptionIds[0] : null;
            }

            formCollectionTransactionFieldEntities.push(transactionField);

            collectionFieldIdMapping?.set(`${rowKey}_${field.fieldId}`, {
                collectionKey: formCollectionIdentityId,
                collectionFieldKey: field.fieldId,
            });
        });

        return formCollectionTransactionFieldEntities;
    }

    private async _getFormCollectionAdditionalFields({
        dto,
        formCollectionCacheService,
        collectionItemIds,
    }: {
        dto: ApiGetAdditionalFields;
        formCollectionCacheService: FormCollectionCacheService;
        collectionItemIds?: string[];
    }) {
        const { transactionId } = dto;

        const formVersionId = (await this._transactionDataService.getTransactionById(transactionId))?.formVersionId;

        if (!formVersionId) {
            return [];
        }

        const { formCollectionItems } = await this._getFormCollections({ formVersionId, collectionItemIdentityIds: collectionItemIds });
        const formCollectionItemIdentityIds = formCollectionItems.map((item) => item.identityId);

        const cachedData = await formCollectionCacheService.getCollectionAdditionalFields<FormCollectionAdditionalFieldTenancyEntity>(
            formVersionId,
            formCollectionItemIdentityIds,
        );

        if (cachedData?.length) {
            return cachedData;
        }

        const where: Record<string, any> = {
            formVersionId: formVersionId,
        };

        if (formCollectionItemIdentityIds?.length) {
            where.formCollectionItemIdentityId = In(formCollectionItemIdentityIds);
        }

        const data = await this._formCollectionAdditionalFieldTenancyRepository.find({
            where,
        });

        return data;
    }
}
