import { <PERSON><PERSON> } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { CacheService } from '../../../../common/src/modules/shared/services/cache.service';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { FormCollectionTenancyEntity } from '../../../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormVersionTenancyEntity } from '../../../../database/src/entities/tenancy/form-version.tenancy.entity';
import { FormViewTenancyEntity } from '../../../../database/src/entities/tenancy/form-view.tenancy.entity';
import { FormTenancyEntity } from '../../../../database/src/entities/tenancy/form.tenancy.entity';
import { StageTenancyEntity } from '../../../../database/src/entities/tenancy/stage.tenancy.entity';
import { FormFieldDto } from '../../dtos/responses/api-form.dto';

@Injectable()
export class FormDataService {
    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
        private readonly _cacheService: CacheService,
        @InjectMapper()
        private readonly _mapper: Mapper,
    ) {}

    public async getFormViewsByRoles(formId: string, roleIds: string[]): Promise<FormViewTenancyEntity[]> {
        const formRepository = this._dataSource.getRepository(FormTenancyEntity);
        const form = await formRepository.findOne({
            where: {
                id: formId,
            },
        });

        const activeVersionId = form?.activeVersionId;
        if (!activeVersionId) {
            throw new NotFoundException('form_not_found');
        }
        if (!roleIds.length) {
            throw new BadRequestException('role_ids_required');
        }

        const formViewRepository = this._dataSource.getRepository(FormViewTenancyEntity);
        const formViews = await formViewRepository
            .createQueryBuilder('formView')
            .leftJoinAndSelect('formView.roles', 'roles')
            .where('formView.formVersionId = :formVersionId', { formVersionId: activeVersionId })
            .andWhere('roles.roleId IN (:...roleIds)', { roleIds })
            .getMany();

        return formViews;
    }

    public async getFormVersionCollections(formVersionId: string): Promise<FormCollectionTenancyEntity[]> {
        const formVersionRepository = this._dataSource.getRepository(FormVersionTenancyEntity);
        const formVersion = await formVersionRepository.findOne({
            where: {
                id: formVersionId,
            },
            relations: {
                formCollections: true,
            },
        });

        return formVersion?.formCollections ?? [];
    }

    public async getStageEntities(formVersionId: string) {
        const stageRepository = this._dataSource.getRepository(StageTenancyEntity);
        const stageEntities = await stageRepository.find({
            where: {
                formVersionId: formVersionId,
            },
        });

        return stageEntities;
    }

    public async getActiveForm(formId: string): Promise<FormVersionTenancyEntity> {
        const formRepository = this._dataSource.getRepository(FormTenancyEntity);
        const form = await formRepository.findOne({
            where: {
                id: formId,
            },
        });
        if (!form) {
            return null;
        }
        const formVersionRepository = this._dataSource.getRepository(FormVersionTenancyEntity);
        const formVersion = await formVersionRepository.findOne({
            where: {
                id: form.activeVersionId,
            },
            relations: {
                fields: true,
            },
        });
        return formVersion;
    }

    public async getFormFieldById(formId: string, fieldId: string): Promise<FormFieldDto | null> {
        const { formRepository, formFieldRepository } = {
            formRepository: this._dataSource.getRepository(FormTenancyEntity),
            formFieldRepository: this._dataSource.getRepository(FormFieldTenancyEntity),
        };

        const form = await formRepository.findOne({
            where: {
                id: formId,
            },
        });

        if (!form) {
            throw new NotFoundException('form_not_found');
        }

        const activeVersionId = form.activeVersionId;

        const formField = await formFieldRepository.findOne({
            where: {
                fieldId: fieldId,
                formVersionId: activeVersionId,
            },
        });

        return this._mapper.map(formField, FormFieldTenancyEntity, FormFieldDto);
    }
}
