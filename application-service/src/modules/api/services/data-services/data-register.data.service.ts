import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { ClaimService, LoggerService } from '../../../../common/src';
import { USER_CLAIMS } from '../../../../constant';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { RoleTenancyEntity } from '../../../../database/src/entities/tenancy/role.tenancy.entity';
import { UserRoleTenancyEntity } from '../../../../database/src/entities/tenancy/user-role.tenancy.entity';
import { UserTenancyEntity } from '../../../../database/src/entities/tenancy/user.tenancy.entity';
import { UserStatus } from '../../../../database/src/shared/enums/user-status.enum';
import { UserType } from '../../../../database/src/shared/enums/user-type.enum';
import { ApiRoleLookupRequestDto, ApiUserLookupRequestDto } from '../../dtos/requests/get-lookup.request';

interface LookupResponse<T> {
    data: T[];
    total: number;
}

@Injectable()
export class DataRegisterDataService {
    constructor(
        @Inject(PROVIDER_KEYS.USER_REPOSITORY)
        private readonly _userRepository: Repository<UserTenancyEntity>,
        @Inject(USER_CLAIMS)
        private readonly _userClaims: ClaimService,
        private readonly _logger: LoggerService,
        @Inject(PROVIDER_KEYS.ROLE_TENANCY_REPOSITORY)
        private _roleRepo: Repository<RoleTenancyEntity>,
    ) {}

    public async getUserLookup(query: ApiUserLookupRequestDto): Promise<LookupResponse<UserTenancyEntity>> {
        const accountId = this._userClaims.accountId;

        const builder = this._userRepository.createQueryBuilder('users');

        this._configureUserQueryBuilder(builder, query, accountId);

        return this._executeQuery(builder, 'Failed to fetch user lookup');
    }

    public async getRoleLookup(query: ApiRoleLookupRequestDto): Promise<LookupResponse<RoleTenancyEntity>> {
        const builder = this._roleRepo.createQueryBuilder('role').leftJoinAndSelect('role.roleSubscriptions', 'roleSubscription');

        this._configureRoleQueryBuilder(builder, query);

        return this._executeQuery(builder, 'Failed to fetch role lookup');
    }

    private _configureUserQueryBuilder(
        builder: SelectQueryBuilder<UserTenancyEntity>,
        query: ApiUserLookupRequestDto,
        accountId: string,
    ): void {
        if (accountId) {
            this._includeRelations(builder, query.includeRelations);
            this._byAccount(builder, accountId);
        }

        if (query.onlyActive) {
            builder.andWhere('users.status = :status', { status: UserStatus.Active });
        }

        builder.andWhere('users.type != :type', { type: UserType.GUEST });

        this._applyPagination(builder, query);
        this._applySelection(builder, query.select, 'users');
    }

    private _configureRoleQueryBuilder(builder: SelectQueryBuilder<RoleTenancyEntity>, query: ApiRoleLookupRequestDto): void {
        this._applyPagination(builder, query);
        this._applySelection(builder, query.select, 'role');
    }

    private _applyPagination(builder: SelectQueryBuilder<any>, query: { skip?: number; take?: number }): void {
        if (query.skip !== undefined) {
            builder.skip(query.skip);
        }
        if (query.take !== undefined) {
            builder.take(query.take);
        }
    }

    private _applySelection(builder: SelectQueryBuilder<any>, select: string[] | undefined, alias: string): void {
        if (select?.length) {
            builder.select(select.map((item) => `${alias}.${item}`));
        }
    }

    private async _executeQuery<T>(builder: SelectQueryBuilder<T>, errorMessage: string): Promise<LookupResponse<T>> {
        try {
            const [data, total] = await builder.getManyAndCount();

            return {
                data: data || [],
                total,
            };
        } catch (err) {
            this._logger.error(errorMessage, err);
            throw new BadRequestException(errorMessage);
        }
    }

    private _includeRelations(builder: SelectQueryBuilder<UserTenancyEntity>, includes?: string[]): void {
        if (!includes?.length) return;

        includes.forEach((include) => {
            switch (include) {
                case 'roles':
                    this._includeUserRoles(builder);
                    break;
                // Add other relation cases as needed
                default:
                    this._logger.warn(`Unknown include relation: ${include}`);
            }
        });
    }

    private _includeUserRoles(builder: SelectQueryBuilder<UserTenancyEntity>): void {
        builder
            .leftJoinAndMapMany('users.roles', UserRoleTenancyEntity, 'userRoles', 'users.id = userRoles.userId')
            .leftJoinAndMapOne('userRoles.role', RoleTenancyEntity, 'roles', 'userRoles.roleId = roles.id');
    }

    private _byAccount(builder: SelectQueryBuilder<UserTenancyEntity>, accountId: string): void {
        if (accountId) {
            builder.where('users.accountId = :accountId', { accountId });
        }
    }
}
