import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { CacheService, LoggerService, UtilsService } from '../../../../common/src';
import { API_ENDPOINT_METHODS, API_ENDPOINTS } from '../../../../database/src/constants/api-endpoints';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { ApiVersionEndpointTenancyEntity } from '../../../../database/src/entities/tenancy/api-version-endpoint.tenancy.entity';
import { ApiVersionTenancyEntity } from '../../../../database/src/entities/tenancy/api-version.tenancy.entity';
import { ApiTenancyEntity } from '../../../../database/src/entities/tenancy/api.tenancy.entity';
import { ApiContextType } from '../../../../database/src/shared/enums/automation.enum';
import { CaptureActiveApiVersionType } from '../../../../database/src/shared/providers/capture-active-form-version.provider';

@Injectable()
export class ApiDataService {
    constructor(
        private readonly _cacheService: CacheService,
        @Inject(PROVIDER_KEYS.API_TENANCY_REPOSITORY)
        private readonly _apiRepository: Repository<ApiTenancyEntity>,
        @Inject(PROVIDER_KEYS.API_VERSION_ENDPOINT_TENANCY_REPOSITORY)
        private readonly _apiVersionEndpointRepository: Repository<ApiVersionEndpointTenancyEntity>,
        @Inject(PROVIDER_KEYS.API_VERSION_TENANCY_REPOSITORY)
        private readonly _apiVersionRepository: Repository<ApiVersionTenancyEntity>,
        private readonly _logger: LoggerService,
    ) {}

    public async getEndpoint(
        {
            endpoint,
            method,
            contextType,
            contextId,
        }: {
            endpoint: API_ENDPOINTS;
            method: API_ENDPOINT_METHODS;
            contextType: ApiContextType;
            contextId: string;
        },
        accountId: string,
    ): Promise<ApiVersionEndpointTenancyEntity> {
        const cachedData = await this.getCachedApiVersionByContext(contextId, accountId);
        if (cachedData) {
            return cachedData?.endpoints?.find((item) => item.endpoint === endpoint && item.method === method);
        }

        const apiTenancy = await this._apiRepository.findOne({
            where: {
                contextType,
                contextId,
            },
        });
        const apiVersionId = apiTenancy?.activeVersionId;

        if (!apiTenancy || !apiVersionId) {
            throw new NotFoundException('api_not_found');
        }

        const apiVersionEndpoint = await this._apiVersionEndpointRepository.findOne({
            where: {
                apiVersionId,
                endpoint,
                method,
            },
        });

        if (!apiVersionEndpoint) {
            throw new NotFoundException('api_not_found');
        }

        return apiVersionEndpoint;
    }

    public async getApiEndpointsByContext(
        contextId: string,
        contextType: ApiContextType,
        accountId: string,
    ): Promise<ApiVersionEndpointTenancyEntity[]> {
        const cachedData = await this.getCachedApiVersionByContext(contextId, accountId);
        if (cachedData) {
            return cachedData?.endpoints;
        }

        const api = await this._apiRepository.findOne({
            where: {
                contextId,
                contextType,
            },
        });
        if (!api) {
            return null;
        }

        const activeVersionId = api.activeVersionId;
        const apiVersionEndpoints = await this._apiVersionEndpointRepository.find({
            where: {
                apiVersionId: activeVersionId,
            },
        });

        return apiVersionEndpoints;
    }

    public async getApiVersionByContext(
        contextId: string,
        contextType: ApiContextType,
        accountId: string,
    ): Promise<ApiVersionTenancyEntity> {
        const cachedData = await this.getCachedApiVersionByContext(contextId, accountId);
        if (cachedData) {
            return cachedData;
        }

        try {
            const api = await this._apiRepository.findOne({
                where: {
                    contextId,
                    contextType,
                },
            });
            if (!api) {
                return null;
            }

            const activeVersionId = api.activeVersionId;

            const apiVersion = await this._apiVersionRepository.findOne({
                where: {
                    id: activeVersionId,
                },
            });

            return apiVersion;
        } catch (error) {
            this._logger.error(error);
            return null;
        }
    }

    public async getCachedApiVersionByContext(contextId: string, accountId: string): Promise<ApiVersionTenancyEntity> {
        const cacheKey = UtilsService.getActiveApiVersionCacheKeys({
            accountId: accountId,
            formId: contextId,
        });
        const cachedData = await this._cacheService.jsonGet<CaptureActiveApiVersionType>(cacheKey?.apiVersionKey);
        return cachedData;
    }
}
