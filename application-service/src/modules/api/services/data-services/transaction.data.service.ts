import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { FileAutoGeneratedEntity } from '../../../../database/src/entities/public/file-auto-generated.public.entity';
import { TransactionEntity } from '../../../../database/src/entities/tenancy/transaction.tenancy.entity';

@Injectable()
export class TransactionDataService {
    constructor(
        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_REPOSITORY)
        private readonly _transactionTenancyRepository: Repository<TransactionEntity>,
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
    ) {}

    public async getTransactionById(transactionId: string) {
        const transaction = await this._transactionTenancyRepository.findOne({
            where: {
                id: transactionId,
            },
        });

        return transaction;
    }

    public async getFileAutoGeneratedByTransactionId(transactionId: string, path: string) {
        const fileAutoGeneratedRepository = this._dataSource.getRepository(FileAutoGeneratedEntity);
        const fileAutoGenerated = await fileAutoGeneratedRepository.findOne({
            where: {
                transactionId,
                dataPath: path,
            },
        });

        return fileAutoGenerated;
    }
}
