import { BadRequestException, Inject, Injectable } from '@nestjs/common';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { isUUID } from 'class-validator';
import { isArray } from 'lodash';
import { ApiDataService } from '.';
import { LoggerService } from '../../../common/src';
import { FormCollectionCacheService } from '../../../common/src/modules/shared/services';
import { USER_CLAIMS } from '../../../constant';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { TransactionFieldContextTypeEnum } from '../../../database/src/shared/enums/transaction-field.enum';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { DefaultTransactionFieldService } from '../../form/services/default-transaction-field.service';
import { FormCollectionTransactionFieldService } from '../../form/services/form-collection-transaction-field.service';
import { ApiMappingConfiguration } from '../dtos/api-mapping-configuration';
import { AddOrDeleteCollectionRow, ApiCollectionRequestDto, CollectionUpdateRequest } from '../dtos/requests/api-collection-request';
import { UserClaims } from '../interfaces/user-claims.interface';
import { ValidateFieldMappingService } from '../utils/validate-field-mapping.service';
import { ApiCollectionDataService } from './data-services/api-collection.data.service';
import { TransactionDataService } from './data-services/transaction.data.service';

@Injectable()
export class ApiCollectionService {
    constructor(
        private readonly _apiCollectionDataService: ApiCollectionDataService,
        @Inject(USER_CLAIMS)
        private readonly _userClaims: UserClaims,
        private readonly _apiDataService: ApiDataService,
        private readonly _loggerService: LoggerService,

        private readonly _formCollectionCacheService: FormCollectionCacheService,
        private readonly _formCollectionTransactionFieldService: FormCollectionTransactionFieldService,
        private readonly _defaultTransactionFieldService: DefaultTransactionFieldService,
        private readonly _transactionDataService: TransactionDataService,
        @InjectMapper()
        private readonly _mapper: Mapper,
        private readonly _validateFieldMappingService: ValidateFieldMappingService,
    ) {}

    public async getFormCollectionOptions(transactionId: string) {
        try {
            const result = {
                collections: {},
            };

            const [fieldMappingConfigurations, formCollections] = await Promise.all([
                this._getFieldMappingsByTransactionId(transactionId),
                this._apiCollectionDataService.getFormCollectionOptions(transactionId),
            ]);

            const collectionMappings = (fieldMappingConfigurations ?? []).filter((mapping) => mapping.type === 'collection') ?? [];

            if (!collectionMappings?.length || !formCollections?.length) {
                return result;
            }

            collectionMappings.forEach((mapping) => {
                const collection = formCollections.find((collection) => collection.identityId === mapping.collectionIdentityId);

                if (!collection) {
                    result.collections[mapping.key] = [];
                    return;
                }

                const collectionItemIdentityIds = collection.formCollectionItems.map((item) => item.identityId);
                result.collections[mapping.key] = collectionItemIdentityIds;
            });

            return result.collections;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }

    public async formatCollectionRequest({
        dto,
        fieldMappings,
        transactionId,
    }: {
        dto: ApiCollectionRequestDto;
        transactionId: string;
        fieldMappings?: ApiMappingConfiguration[];
    }) {
        const { updateCollectionFields: update, addCollectionRows: add, deleteCollectionsRows: deleteData } = dto;

        let fieldMappingConfigurations = (fieldMappings ?? []).filter((mapping) =>
            ['collectionField', 'collection'].includes(mapping.type),
        );

        if (!fieldMappingConfigurations?.length) {
            fieldMappingConfigurations = await this._getFieldMappingsByTransactionId(transactionId);
        }

        let addCollectionTransactionFields: TransactionFieldEntity[] = [];
        let updateCollectionTransactionFields: TransactionFieldEntity[] = [];
        let deleteRowKeys: string[] = [];

        let collectionFieldIdMapping: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        > = new Map();

        if (update) {
            const validationResult = this._validateFieldMappingService.validateCollectionFields(update, fieldMappingConfigurations);

            if (!validationResult.isValid) {
                throw new BadRequestException(validationResult.errors);
            }

            const {
                updateCollectionFields,
                collectionFieldIdMapping: mapping,
                collectionIdMapping,
            } = this._mappingUpdateCollectionFields({
                updateCollections: update,
                fieldMappings: fieldMappingConfigurations,
            });

            collectionFieldIdMapping = mapping;

            updateCollectionTransactionFields = await this._apiCollectionDataService.formatUpdateFormCollectionItems({
                transactionId,
                updateCollections: updateCollectionFields,
                collectionFieldIdMapping: collectionFieldIdMapping,
                collectionIdMapping,
            });
        }

        if (add) {
            const { errors: addCollectionErrors } = await this._checkAddCollections(add, transactionId);

            if (addCollectionErrors?.length) {
                throw new BadRequestException(addCollectionErrors);
            }

            const collectionIdentityIds = Object.values(add).flatMap((item) => item.map((id) => id));

            addCollectionTransactionFields = await this._apiCollectionDataService.formatAddFormCollectionItems({
                transactionId,
                collectionItemIdentityIds: collectionIdentityIds,
                formCollectionTransactionFieldService: this._formCollectionTransactionFieldService,
                defaultTransactionFieldService: this._defaultTransactionFieldService,
                formCollectionCacheService: this._formCollectionCacheService,
                collectionFieldIdMapping: collectionFieldIdMapping,
            });
        }

        if (deleteData) {
            deleteRowKeys = Object.values(deleteData).flatMap((item) => item.map((id) => id));
            const validDeleteCollectionRow = await this._checkValidDeleteCollectionRow(deleteData, transactionId);

            if (!validDeleteCollectionRow?.valid) {
                throw new BadRequestException(validDeleteCollectionRow?.errors);
            }
        }

        return {
            addCollectionTransactionFields: this._mapper.mapArray(
                addCollectionTransactionFields,
                TransactionFieldEntity,
                EditFormTransactionFieldRequest,
            ),
            updateCollectionTransactionFields: this._mapper.mapArray(
                updateCollectionTransactionFields,
                TransactionFieldEntity,
                EditFormTransactionFieldRequest,
            ),
            deleteRowKeys,
            collectionFieldIdMapping,
        };
    }

    private async _checkAddCollections(collectionItems: AddOrDeleteCollectionRow, transactionId: string) {
        const result: {
            valid: boolean;
            errors: string[];
        } = {
            valid: true,
            errors: [],
        };

        // Early return if no data to validate
        if (!collectionItems || Object.keys(collectionItems).length === 0) {
            return result;
        }

        // Validate UUIDs and collect all valid IDs in a single pass
        const allCollectionItemIds: string[] = [];
        const invalidUuidErrors: string[] = [];

        for (const [collectionKey, ids] of Object.entries(collectionItems)) {
            if (!ids?.length) continue;

            const invalidUuids = ids.filter((id) => !isUUID(id));

            if (invalidUuids.length > 0) {
                invalidUuidErrors.push(`Collection row ${invalidUuids.join(',')} of Collection ${collectionKey} is not a valid UUID`);
            } else {
                allCollectionItemIds.push(...ids);
            }
        }

        // If there are UUID validation errors, return early
        if (invalidUuidErrors.length > 0) {
            result.valid = false;
            result.errors = invalidUuidErrors;
            return result;
        }

        // If no valid collection item IDs to check, return success
        if (allCollectionItemIds.length === 0) {
            return result;
        }

        // Fetch existing collections in a single query
        const existingCollections = await this._apiCollectionDataService.getFormCollectionOptions(transactionId, allCollectionItemIds);

        // Create a Set for O(1) lookup performance
        const existingCollectionItemIds = new Set(
            existingCollections.flatMap((collection) => collection.formCollectionItems.map((item) => item.identityId)),
        );

        // Validate collection item existence
        for (const [collectionKey, collectionItemIds] of Object.entries(collectionItems)) {
            if (!collectionItemIds?.length) continue;

            const nonExistentIds = collectionItemIds.filter((id) => !existingCollectionItemIds.has(id));

            if (nonExistentIds.length > 0) {
                result.errors.push(`Collection ${nonExistentIds.join(',')} of Collection ${collectionKey} not found`);
            }
        }

        // Set valid to false if any errors were found
        if (result.errors.length > 0) {
            result.valid = false;
        }

        return result;
    }

    private async _getFieldMappingsByTransactionId(transactionId: string) {
        const transaction = await this._transactionDataService.getTransactionById(transactionId);

        const apiVersion = await this._apiDataService.getApiVersionByContext(
            transaction.formId,
            ApiContextType.FormTransaction,
            this._userClaims.id,
        );

        const fieldMappingConfigurations =
            (apiVersion?.configuration?.fieldMapping ?? []).filter((mapping) => ['collectionField', 'collection'].includes(mapping.type)) ??
            [];

        return fieldMappingConfigurations;
    }

    private _mappingUpdateCollectionFields({
        updateCollections,
        fieldMappings,
    }: {
        updateCollections: CollectionUpdateRequest;
        fieldMappings: ApiMappingConfiguration[];
    }) {
        const updateCollectionFields: TransactionFieldEntity[] = [];
        const collectionFieldIdMapping: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        > = new Map();

        const collectionIdMapping: Map<string, string> = new Map();

        for (const [collectionKey, collectionUpdates] of Object.entries(updateCollections)) {
            const collectionIdentityId = fieldMappings.find(
                (mapping) => mapping.key === collectionKey && mapping.type === 'collection',
            )?.collectionIdentityId;

            if (!collectionIdentityId) {
                continue;
            }

            const collectionFieldMappings = fieldMappings?.filter(
                (mapping) => mapping?.collectionIdentityId === collectionIdentityId && mapping?.type === 'collectionField',
            );

            if (!collectionFieldMappings?.length) {
                continue;
            }

            collectionUpdates.forEach((row) => {
                collectionFieldMappings.forEach((mappingField) => {
                    const updateCollectionField = new TransactionFieldEntity();

                    updateCollectionField.rowKey = row.rowKey;
                    updateCollectionField.fieldId = mappingField.fieldId;
                    updateCollectionField.contextType = TransactionFieldContextTypeEnum.COLLECTION;
                    updateCollectionField.collectionId = collectionIdentityId;

                    if (!isArray(row?.[mappingField.key])) {
                        updateCollectionField.fieldValue = row?.[mappingField.key] as string;
                    } else {
                        updateCollectionField.fieldOptionIds = row?.[mappingField.key] as string[];

                        if (updateCollectionField?.fieldOptionIds?.length > 1) {
                            updateCollectionField.fieldValue = updateCollectionField?.fieldOptionIds.join(',');
                        } else {
                            updateCollectionField.fieldValue = updateCollectionField?.fieldOptionIds[0];
                        }
                    }

                    updateCollectionFields.push(updateCollectionField);
                    collectionFieldIdMapping.set(`${updateCollectionField.rowKey}_${updateCollectionField.fieldId}`, {
                        collectionKey,
                        collectionFieldKey: mappingField.key,
                    });
                    collectionIdMapping.set(updateCollectionField.rowKey, collectionKey);
                });
            });
        }

        return {
            updateCollectionFields,
            collectionFieldIdMapping,
            collectionIdMapping,
        };
    }

    private async _checkValidDeleteCollectionRow(deleteData: AddOrDeleteCollectionRow, transactionId: string) {
        const result: {
            valid: boolean;
            errors: string[];
        } = {
            valid: true,
            errors: [],
        };

        // Early return if no data to validate
        if (!deleteData || Object.keys(deleteData).length === 0) {
            return result;
        }

        // Validate UUIDs and collect all row keys in a single pass
        const allRowKeys: string[] = [];
        const invalidUuidErrors: string[] = [];

        for (const [collectionKey, rowKeys] of Object.entries(deleteData)) {
            if (!rowKeys?.length) continue;

            const invalidUuids = rowKeys.filter((rowKey) => !isUUID(rowKey));

            if (invalidUuids.length > 0) {
                invalidUuidErrors.push(`Row ${invalidUuids.join(',')} of Collection ${collectionKey} is not a valid UUID`);
            } else {
                allRowKeys.push(...rowKeys);
            }
        }

        // If there are UUID validation errors, return early
        if (invalidUuidErrors.length > 0) {
            result.valid = false;
            result.errors = invalidUuidErrors;
            return result;
        }

        // Fetch existing collections in a single query
        const existingCollections = await this._apiCollectionDataService.getCollectionByRowKeys(allRowKeys, transactionId);
        const existingRowKeys = new Set(existingCollections.map((collection) => collection.rowKey));

        // Validate row existence
        for (const [collectionKey, rowKeys] of Object.entries(deleteData)) {
            if (!rowKeys?.length) continue;

            const nonExistentRowKeys = rowKeys.filter((rowKey) => !existingRowKeys.has(rowKey));

            if (nonExistentRowKeys.length > 0) {
                result.errors.push(`Row ${nonExistentRowKeys.join(',')} of Collection ${collectionKey} not found`);
            }
        }

        // Set valid to false if any errors were found
        if (result.errors.length > 0) {
            result.valid = false;
        }

        return result;
    }
}
