import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { UserStatus } from '../../../database/src/shared/enums/user-status.enum';
import * as dayjs from 'dayjs';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { TokenResponseDto } from '../dtos/responses/token.response.dto';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { Repository } from 'typeorm';
import { AccountEntity } from '../../../database/src/entities/public/account.public.entity';
import { AccountStatusEnum } from '../../../database/src/shared/enums/account.enum';

@Injectable()
export class AuthService {
    constructor(
        private readonly _datasourceService: DataSourceService,
        private readonly _jwtService: JwtService,
        @Inject(PROVIDER_KEYS.ACCOUNT_REPOSITORY)
        private readonly _accountRepository: Repository<AccountEntity>,
    ) {}

    async validateClient(clientId: string, clientSecret: string, accountId: string): Promise<TokenResponseDto> {
        const account = await this._accountRepository.findOne({
            where: {
                id: accountId,
            },
        });
        if (!account || account.status !== AccountStatusEnum.ACTIVE) {
            throw new UnauthorizedException('invalid_account');
        }
        try {
            const datasource = await this._datasourceService.createAccountDataSource(account.id);
            const userRepository = datasource.getRepository(UserTenancyEntity);
            const user = await userRepository.findOne({
                where: {
                    id: clientId,
                    identityId: clientSecret,
                    type: UserType.API,
                },
                relations: ['roles'],
            });
            if (!user) {
                throw new UnauthorizedException('invalid_client');
            }

            if (user.status === UserStatus.InActive) {
                throw new UnauthorizedException('user_inactive');
            } else {
                await userRepository.update({ id: user.id }, { lastLogin: dayjs() });
            }
            const payload = {
                id: uuidv4(),
                firstName: user.firstName,
                secondName: user.secondName,
                roles: user.roles.map((role) => role.roleId),
                userType: user.type,
                accountId: account.id,
            };
            return {
                access_token: this._jwtService.sign(payload),
            };
        } catch (error) {
            throw new UnauthorizedException('invalid_client');
        }
    }
}
