import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { v4 } from 'uuid';
import { ApiFormService } from '.';
import { LoggerService } from '../../../common/src';
import { OperatorType } from '../../../common/src/modules/shared/enums/operator.enum';
import { RoleTenancyEntity } from '../../../database/src/entities/tenancy/role.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { DataRegisterTransactionOptionDto } from '../../data-register/dtos/respsonses/data-register-transaction-option.dto';
import { DataRegisterTransactionTenancyService } from '../../data-register/services/data-register-transaction.tenancy.service';
import {
    ApiGetLookupRequestDto,
    ApiLookupRequestDto,
    ApiRoleLookupRequestDto,
    ApiUserLookupRequestDto,
} from '../dtos/requests/get-lookup.request';
import { ApiGetOptionRequestDto } from '../dtos/requests/get-option.request';
import { FormFieldDto } from '../dtos/responses/api-form.dto';
import { ApiLookupResponseDto } from '../dtos/responses/api-lookup.dto';
import { DataRegisterDataService } from './data-services/data-register.data.service';

@Injectable()
export class ApiDataRegisterService {
    constructor(
        private readonly _service: DataRegisterTransactionTenancyService,
        private readonly _loggerService: LoggerService,
        private readonly _formService: ApiFormService,
        @InjectMapper()
        private readonly _mapper: Mapper,
        private readonly _dataRegisterDataService: DataRegisterDataService,
    ) {}

    public async lookUpOptions(query: ApiLookupRequestDto) {
        try {
            const { fieldId, formId, pageIndex, pageSize, fieldKey } = query;

            if (!fieldId) {
                throw new BadRequestException(`${fieldKey} is not valid`);
            }

            const field = await this._formService.getFormFieldById(formId, fieldId);
            if (!field) {
                throw new NotFoundException(`${fieldKey} is not found`);
            }

            let result = {
                data: [],
                total: 0,
            };

            switch (field.type) {
                case FormFieldTypeEnum.Lookup:
                    result = await this._getLookup(field, pageIndex, pageSize);
                    break;

                case FormFieldTypeEnum.UserLookup:
                    result = await this._getUserLookup(query);
                    break;

                case FormFieldTypeEnum.RoleLookup:
                    result = await this._getRoleLookup(query);
                    break;

                default:
                    throw new BadRequestException(`${fieldKey} is not a valid field`);
            }

            return result;
        } catch (error) {
            this._loggerService.error(`Api Module Error: ${error.message}`);
            throw new BadRequestException(error.message);
        }
    }

    public async getOptions(query: ApiGetOptionRequestDto) {
        const { fieldId, formId, fieldKey } = query;

        if (!fieldId) {
            throw new BadRequestException(`${fieldKey} is not valid`);
        }

        const field = await this._formService.getFormFieldById(formId, fieldId);
        if (!field) {
            throw new NotFoundException(`${fieldKey} is not found`);
        }

        if (field?.type !== FormFieldTypeEnum.Select) {
            throw new BadRequestException(`${fieldKey} is not a select field`);
        }

        const options = field?.configuration?.options ?? [];

        if (!options?.length) return [];

        return options.map((option) => ({
            id: option.value,
            name: option.label,
        }));
    }

    private async _getUserLookup(query: ApiLookupRequestDto) {
        const request: ApiUserLookupRequestDto = {
            select: ['firstName', 'secondName', 'email', 'id'],
            onlyActive: true,
            filterType: 'or',
            take: query.pageSize,
            skip: query.pageIndex,
        };

        const data = await this._dataRegisterDataService.getUserLookup(request);

        return {
            data: this._mapper.mapArray(data?.data ?? [], UserTenancyEntity, ApiLookupResponseDto),
            total: data.total,
        };
    }

    private async _getRoleLookup(query: ApiRoleLookupRequestDto) {
        const request: ApiRoleLookupRequestDto = {
            select: ['id', 'name'],
            operatorType: 'or',
            take: query.pageSize,
            skip: query.pageIndex,
        };

        const data = await this._dataRegisterDataService.getRoleLookup(request);

        return {
            data: this._mapper.mapArray(data?.data ?? [], RoleTenancyEntity, ApiLookupResponseDto),
            total: data.total,
        };
    }

    private async _getLookup(field: FormFieldDto, pageIndex: number, pageSize: number) {
        const fieldCondition = field?.configuration?.fieldCondition ?? null;
        const lookupDataSet = field.lookupDataSet ?? field?.configuration?.dataset ?? null;
        const lookupDataRegisterId = field.lookupTargetId ?? field?.configuration?.targetId ?? null;

        const queryOption: ApiGetLookupRequestDto = {
            take: pageSize,
            skip: pageIndex,
            filters: [],
        };

        if (fieldCondition) {
            queryOption.filters.push({
                field: 'filterCondition',
                operator: OperatorType.equals,
                value: fieldCondition,
            });
        }

        if (lookupDataSet) {
            queryOption.filters.push({
                field: 'dataset',
                operator: OperatorType.equals,
                value: lookupDataSet,
            });
        }

        if (lookupDataRegisterId) {
            queryOption.filters.push({
                field: 'dataRegisterId',
                operator: OperatorType.equals,
                value: lookupDataRegisterId,
            });
        }

        const data = await this._service.lookUpOptions(v4(), queryOption);
        return {
            data: this._mapper.mapArray(data?.data ?? [], DataRegisterTransactionOptionDto, ApiLookupResponseDto),
            total: data.total,
        };
    }
}
