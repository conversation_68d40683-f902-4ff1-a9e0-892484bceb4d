import { Injectable } from '@nestjs/common';
import { isArray, isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { TransactionValidationError } from '../../validation/enums/transaction-validation-errors.enum';
import { FieldDataTypeError, TransactionValidationErrorResponse } from '../../validation/types/transaction-validation-error';
import { ApiMappingConfiguration } from '../dtos/api-mapping-configuration';
import { CollectionUpdateRequest } from '../dtos/requests/api-collection-request';
import { CollectionFieldBuilder } from '../services';
import { LookupValue, validateFieldByType, ValidationContext } from './field-validators';
import {
    LookupValidationContext,
    RoleLookupValidationContext,
    UserLookupValidationContext,
    validateLookupValues,
    validateRoleLookupValues,
    validateUserLookupValues,
} from './lookup-validators';

@Injectable()
export class ValidateFieldMappingService {
    public validateFormFields = (
        formFields: Record<string, string | string[] | null>,
        fieldMappings: ApiMappingConfiguration[],
    ): {
        isValid: boolean;
        errors?: string[];
    } => {
        const mappingErrors = this._validateFormFieldsInMapping(formFields, fieldMappings);

        const mappingErrorsMessage = mappingErrors?.map((error) => error.error) ?? [];

        return {
            isValid: !mappingErrors,
            errors: mappingErrorsMessage,
        };
    };

    public validateCollectionFields = (
        collectionFields: CollectionUpdateRequest,
        fieldMappings: ApiMappingConfiguration[],
    ): {
        isValid: boolean;
        errors?: string[];
    } => {
        const mappingErrors = this._validateCollectionFieldsInMapping(collectionFields, fieldMappings);
        const mappingErrorsMessage = mappingErrors?.map((error) => error.error) ?? [];

        return {
            isValid: !mappingErrors?.length,
            errors: mappingErrorsMessage,
        };
    };

    public apiValidateTransactionFieldsDataType = async ({
        fields,
        fieldsConfigurations,
        dataRegisterTransactionRepository,
        formTransactionFieldRepository,
        userRepository,
        transactionId,
        formVersionId,
        availableRoles,
        formFieldIdMapping,
        collectionFieldBuilders,
        collectionAdditionalFields,
        collectionFieldIdMapping,
    }: {
        fieldsConfigurations: FormFieldTenancyEntity[];
        fields: EditFormTransactionFieldRequest[];
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>;
        formTransactionFieldRepository: Repository<TransactionFieldEntity>;
        userRepository: Repository<UserTenancyEntity>;
        transactionId: string;
        availableRoles: string[];
        formFieldIdMapping?: Map<string, string>;
        collectionFieldBuilders?: CollectionFieldBuilder[];
        collectionAdditionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
        formVersionId: string;
        collectionFieldIdMapping?: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        >;
    }): Promise<TransactionValidationErrorResponse<FieldDataTypeError[]>> => {
        const errorFieldDataTypes: FieldDataTypeError[] = [];
        const lookupValues: LookupValue[] = [];
        const roleLookupValues: LookupValue[] = [];
        const userLookupValues: LookupValue[] = [];

        // Validate each field using the modular validators
        for (const field of fields) {
            if (field.fieldValue === null || field.fieldValue === undefined) continue;

            let formField = (fieldsConfigurations || []).find((fc) => fc.fieldId === field.fieldId);
            if (!formField) {
                const collectionFieldBuilder = collectionFieldBuilders?.find(
                    (cf) => cf.fieldId === field.fieldId && field.collectionId === cf.formCollectionIdentityId,
                );

                if (collectionFieldBuilder?.type !== FormFieldTypeEnum.Definable) {
                    formField = collectionFieldBuilder;
                } else {
                    const additionalField = collectionAdditionalFields?.find(
                        (af) =>
                            af.fieldId === field.fieldId &&
                            af.formCollectionItemIdentityId === field.collectionItemId &&
                            af.formVersionId === formVersionId,
                    );

                    if (additionalField) {
                        formField.type = additionalField.type;
                    }
                }
            }

            const validationContext: ValidationContext = {
                fieldId: field.fieldId,
                fieldValue: field.fieldValue,
                formField,
                formFieldIdMapping,
                collectionFieldIdMapping,
                rowKey: field.rowKey,
            };

            const validationResult = validateFieldByType(validationContext);

            // Add validation error if any
            if (validationResult.error) {
                errorFieldDataTypes.push(validationResult.error);
            }

            // Collect lookup values for later validation
            if (validationResult.lookupValue) {
                switch (validationResult.lookupValue.type) {
                    case FormFieldTypeEnum.Lookup:
                        lookupValues.push(validationResult.lookupValue);
                        break;
                    case FormFieldTypeEnum.RoleLookup:
                        roleLookupValues.push(validationResult.lookupValue);
                        break;
                    case FormFieldTypeEnum.UserLookup:
                        userLookupValues.push(validationResult.lookupValue);
                        break;
                }
            }
        }

        // Validate lookup values against database (only if no lookup validation errors exist)
        if (lookupValues.length && !errorFieldDataTypes?.find((item) => item.type === FormFieldTypeEnum.Lookup)) {
            const lookupValidationContext: LookupValidationContext = {
                lookupValues,
                dataRegisterTransactionRepository,
                formTransactionFieldRepository,
                transactionId,
                formFieldIdMapping,
            };

            const lookupErrors = await validateLookupValues(lookupValidationContext);
            errorFieldDataTypes.push(...lookupErrors);
        }

        // Validate role lookup values
        if (roleLookupValues.length && availableRoles.length) {
            const roleLookupValidationContext: RoleLookupValidationContext = {
                roleLookupValues,
                availableRoles,
                formFieldIdMapping,
            };

            const roleLookupErrors = validateRoleLookupValues(roleLookupValidationContext);
            errorFieldDataTypes.push(...roleLookupErrors);
        }

        // Validate user lookup values
        if (userLookupValues.length) {
            const userLookupValidationContext: UserLookupValidationContext = {
                userLookupValues,
                userRepository,
                formFieldIdMapping,
            };

            const userLookupErrors = await validateUserLookupValues(userLookupValidationContext);
            errorFieldDataTypes.push(...userLookupErrors);
        }

        if (errorFieldDataTypes.length) {
            return {
                message: TransactionValidationError.FieldDataTypeError,
                data: errorFieldDataTypes,
            };
        }

        return null;
    };

    private _validateFormFieldsInMapping = (
        formFields: Record<string, string | string[] | null>,
        fieldMappings: ApiMappingConfiguration[],
    ): { fieldKey: string; error: string }[] | null => {
        if (!formFields || isEmpty(formFields)) {
            return null;
        }

        const fieldMappingKeys = fieldMappings.map((mapping) => mapping.key);
        const errors: { fieldKey: string; error: string }[] = [];

        for (const fieldKey of Object.keys(formFields)) {
            if (!fieldMappingKeys.includes(fieldKey)) {
                errors.push({
                    fieldKey,
                    error: `Form field '${fieldKey}' is not mapped in the API configuration`,
                });
            }
        }

        return errors.length > 0 ? errors : null;
    };

    private _validateCollectionFieldsInMapping = (
        collectionFields: CollectionUpdateRequest,
        fieldMappings: ApiMappingConfiguration[],
    ): { fieldKey: string; error: string }[] => {
        const errors: { fieldKey: string; error: string }[] = [];

        if (!collectionFields || isEmpty(collectionFields)) {
            return null;
        }

        const fieldMappingKeys = fieldMappings.map((mapping) => mapping.key) ?? [];
        fieldMappingKeys.push('rowKey');

        for (const [fieldKey, rows] of Object.entries(collectionFields)) {
            if (!fieldMappingKeys.includes(fieldKey)) {
                errors.push({
                    fieldKey,
                    error: `collection ${fieldKey} is not mapped in the API configuration`,
                });
                continue;
            }

            if (!isArray(rows)) {
                errors.push({
                    fieldKey,
                    error: `collection ${fieldKey} is not a valid array`,
                });
            } else {
                for (const row of rows) {
                    for (const rowKey in row) {
                        if (!fieldMappingKeys.includes(rowKey)) {
                            errors.push({
                                fieldKey,
                                error: `Field ${rowKey} of Collection ${fieldKey} is not mapped in the API configuration`,
                            });
                        }
                    }
                }
            }
        }

        return errors;
    };
}
