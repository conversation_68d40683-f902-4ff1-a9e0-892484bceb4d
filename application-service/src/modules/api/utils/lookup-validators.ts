import { every, includes, uniq } from 'lodash';
import { In, Repository } from 'typeorm';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { UserStatus } from '../../../database/src/shared/enums/user-status.enum';
import { FieldDataTypeError } from '../../validation/types/transaction-validation-error';
import { LookupValue } from './field-validators';

// Base error creator for lookup validation
const createLookupError = (
    fieldId: string,
    type: string,
    label: string,
    message: string,
    formFieldIdMapping?: Map<string, string>,
): FieldDataTypeError => ({
    fieldId,
    type,
    label,
    message: `${formFieldIdMapping?.get(fieldId)} ${message}`,
});

// Lookup validation context
export interface LookupValidationContext {
    lookupValues: LookupValue[];
    dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>;
    formTransactionFieldRepository: Repository<TransactionFieldEntity>;
    transactionId?: string;
    formFieldIdMapping?: Map<string, string>;
}

// Role lookup validation context
export interface RoleLookupValidationContext {
    roleLookupValues: LookupValue[];
    availableRoles: string[];
    formFieldIdMapping?: Map<string, string>;
}

// User lookup validation context
export interface UserLookupValidationContext {
    userLookupValues: LookupValue[];
    userRepository: Repository<UserTenancyEntity>;
    formFieldIdMapping?: Map<string, string>;
}

// Validate lookup values against database
export const validateLookupValues = async (context: LookupValidationContext): Promise<FieldDataTypeError[]> => {
    const { lookupValues, dataRegisterTransactionRepository, formTransactionFieldRepository, transactionId, formFieldIdMapping } = context;

    if (!lookupValues.length) {
        return [];
    }

    const errors: FieldDataTypeError[] = [];
    const lookupValueIds = lookupValues.flatMap((v) => v.value);

    const existedLookupValues = await dataRegisterTransactionRepository.findBy({
        id: In(uniq(lookupValueIds)),
    });

    const unExistedValues = lookupValues.filter((item) =>
        (item.value || []).some((value) => !existedLookupValues.some((e) => e.id === value)),
    );

    if (unExistedValues.length) {
        for (const item of unExistedValues) {
            try {
                let formFieldData: TransactionFieldEntity | undefined;

                if (transactionId) {
                    formFieldData = await formTransactionFieldRepository.findOne({
                        where: {
                            fieldId: item.fieldId,
                            transactionId: transactionId,
                        },
                    });
                }

                const existedLookupValuesIds = existedLookupValues.map((el) => el.id);
                const unExistedValueIds = item.value.filter((id) => !existedLookupValuesIds.includes(id));

                if (!formFieldData && unExistedValueIds.length) {
                    errors.push(
                        createLookupError(item.fieldId, item.type, item.label, 'value must be a valid lookup value', formFieldIdMapping),
                    );
                } else if (formFieldData && every(unExistedValueIds, (item) => includes(formFieldData.fieldOptionIds, item))) {
                    continue;
                } else {
                    errors.push(
                        createLookupError(item.fieldId, item.type, item.label, 'value must be a valid lookup value', formFieldIdMapping),
                    );
                }
            } catch (error) {
                console.error(`Error fetching field data for fieldId ${item.fieldId}:`, error);
                errors.push(
                    createLookupError(item.fieldId, item.type, item.label, 'value must be a valid lookup value', formFieldIdMapping),
                );
            }
        }
    }

    return errors;
};

// Validate role lookup values
export const validateRoleLookupValues = (context: RoleLookupValidationContext): FieldDataTypeError[] => {
    const { roleLookupValues, availableRoles, formFieldIdMapping } = context;

    if (!roleLookupValues.length || !availableRoles.length) {
        return [];
    }

    const errors: FieldDataTypeError[] = [];

    roleLookupValues.forEach((item) => {
        item.value.forEach((value) => {
            if (!availableRoles.includes(value)) {
                errors.push(createLookupError(item.fieldId, item.type, item.label, 'value must be a valid role', formFieldIdMapping));
            }
        });
    });

    return errors;
};

// Validate user lookup values
export const validateUserLookupValues = async (context: UserLookupValidationContext): Promise<FieldDataTypeError[]> => {
    const { userLookupValues, userRepository, formFieldIdMapping } = context;

    if (!userLookupValues.length) {
        return [];
    }

    const errors: FieldDataTypeError[] = [];
    const existingUsers = await userRepository.findBy({
        status: UserStatus.Active,
    });

    const existingUserIds = existingUsers.map((user) => user.id);

    userLookupValues.forEach((item) => {
        item.value.forEach((value) => {
            if (!existingUserIds.includes(value)) {
                errors.push(createLookupError(item.fieldId, item.type, item.label, 'value must be a valid user', formFieldIdMapping));
            }
        });
    });

    return errors;
};
