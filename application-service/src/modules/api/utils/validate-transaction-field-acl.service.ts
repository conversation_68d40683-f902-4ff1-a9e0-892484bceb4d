import { Injectable } from '@nestjs/common';
import { FormRoleStageACLDto } from '../../../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { FieldACLError, ValidateFieldsResult } from '../../../common/src/modules/acl/dto/validate-fields.result';
import { ValidateFieldRequest, ValidateFormFieldsRequest } from '../../../common/src/modules/acl/dto/validate-form-fields.request';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { StageRoleAccessControlConfig } from '../../../database/src/shared/dto/access-control.dto';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { TransactionValidationError } from '../../validation/enums/transaction-validation-errors.enum';
import { TransactionValidationErrorResponse } from '../../validation/types/transaction-validation-error';

export type IFieldACLError = FieldACLError & {
    message?: string;
};

export type IValidateFieldRequest = ValidateFieldRequest & {
    formCollectionIdentityId?: string;
    rowKey?: string;
};

@Injectable()
export class ValidateTransactionFieldACLService {
    public validateTransactionFieldsACL({
        stageRoleACL,
        transactionFields,
        fieldsConfigurations,
        formFieldIdMapping,
        collectionFieldIdMapping,
    }: {
        transactionFields: EditFormTransactionFieldRequest[];
        stageRoleACL: FormRoleStageACLDto;
        fieldsConfigurations: FormFieldTenancyEntity[];
        formFieldIdMapping?: Map<string, string>;
        collectionFieldIdMapping?: Map<
            string,
            {
                collectionKey: string;
                collectionFieldKey: string;
            }
        >;
    }): TransactionValidationErrorResponse<FieldACLError[]> {
        if (!transactionFields?.length) return null;

        const fields: ValidateFieldRequest[] = transactionFields.map((item) => ({
            fieldId: item.fieldId,
            value: item.fieldValue,
            type: item.fieldType,
            formCollectionIdentityId: item.collectionItemId,
            rowKey: item.rowKey,
        }));

        const validateDataResult = this._validateRequiredFields({
            fields,
            fieldsAcl: stageRoleACL.field,
            fieldsConfigurations,
            formFieldIdMapping,
            collectionFieldIdMapping,
            collectionAcl: stageRoleACL.collection,
        });

        const validateACLResult = this._validateFieldsACL({
            fields,
            fieldsAcl: stageRoleACL.field,
            fieldsConfigurations,
            formFieldIdMapping,
            collectionFieldIdMapping,
            collectionAcl: stageRoleACL.collection,
        });

        const errorFields = [...validateACLResult.errors, ...validateDataResult.errors].reduce((errorFields, field) => {
            const exist = errorFields.find((item) => item.fieldId === field.fieldId);
            if (!exist) {
                errorFields.push(field);
            } else {
                exist.typeACL.push(...field.typeACL);
            }
            return errorFields;
        }, [] as FieldACLError[]);

        errorFields.forEach((field) => {
            field.label = fieldsConfigurations.find((f) => f.fieldId === field.fieldId)?.label ?? '';
        });

        if (errorFields.length) {
            return {
                message: TransactionValidationError.FieldACLError,
                data: errorFields,
            };
        }

        return null;
    }

    private _validateRequiredFields(
        payload: ValidateFormFieldsRequest & {
            formFieldIdMapping?: Map<string, string>;
            collectionFieldIdMapping?: Map<
                string,
                {
                    collectionKey: string;
                    collectionFieldKey: string;
                }
            >;
            collectionAcl?: Record<string, StageRoleAccessControlConfig>;
        },
    ): ValidateFieldsResult {
        const { fields = [], fieldsAcl, fieldsConfigurations = [], formFieldIdMapping, collectionFieldIdMapping, collectionAcl } = payload;

        const requiredFieldIds = Object.entries(fieldsAcl ?? {})
            .filter(([_, fieldACL]) => fieldACL.required)
            .map(([fieldId]) => fieldId);

        const errorFields: FieldACLError[] = [];
        requiredFieldIds.forEach((fieldId) => {
            const formField = fieldsConfigurations.find((f) => f.fieldId === fieldId);
            if (formField?.type === 'rollup') {
                return;
            }

            const error: IFieldACLError = {
                fieldId,
                typeACL: ['required'],
            };

            const requestField = fields.find((f) => f.fieldId === fieldId);
            if (!requestField) {
                error.message = `Field ${formFieldIdMapping?.get(fieldId)} is required`;
                errorFields.push(error);
                return;
            }

            const isEmptyValue = requestField.value === undefined || requestField.value === null;
            if (isEmptyValue) {
                error.message = `Field ${formFieldIdMapping?.get(fieldId)} is required`;
                errorFields.push(error);
                return;
            }
        });

        return {
            errors: errorFields,
        };
    }

    private _validateFieldsACL(
        payload: ValidateFormFieldsRequest & {
            formFieldIdMapping?: Map<string, string>;
            collectionFieldIdMapping?: Map<
                string,
                {
                    collectionKey: string;
                    collectionFieldKey: string;
                }
            >;
            collectionAcl?: Record<string, StageRoleAccessControlConfig>;
        },
    ) {
        const { fieldsAcl, fields = [], formFieldIdMapping, collectionFieldIdMapping, collectionAcl } = payload;
        const errorFields: FieldACLError[] = (fields as IValidateFieldRequest[]).reduce((errFields, field) => {
            switch (field.type) {
                case 'rollup':
                    return errFields;
            }

            const error: IFieldACLError = {
                fieldId: field.fieldId,
                typeACL: [],
            };

            let fieldACL = fieldsAcl[field.fieldId] ?? collectionAcl[`${field.fieldId}_${field.formCollectionIdentityId}`];

            let fieldLabel = formFieldIdMapping?.get(field.fieldId);
            if (!fieldLabel) {
                const collection = collectionFieldIdMapping?.get(`${field.rowKey}_${field.fieldId}`);
                if (!collection) {
                    return errFields;
                }
                fieldLabel = `${collection.collectionFieldKey} of Collection ${collection.collectionKey}`;
            }

            if (!fieldACL) {
                error.message = `Field ${fieldLabel} acl error`;
                errFields.push(error);
            } else {
                if (!fieldACL.visible) {
                    error.message = `Field ${fieldLabel} is hidden`;
                    errFields.push(error);
                } else {
                    if (!fieldACL.editable) {
                        if (!formFieldIdMapping?.get(field.fieldId)) {
                            console.log(collectionFieldIdMapping, field.fieldId);
                        }
                        error.message = `Field ${fieldLabel} is read only`;
                        errFields.push(error);
                    }
                }
            }
            return errFields;
        }, [] as FieldACLError[]);

        return {
            errors: errorFields,
        };
    }
}
