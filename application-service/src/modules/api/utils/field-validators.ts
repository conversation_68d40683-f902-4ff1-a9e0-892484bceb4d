import { isUUID } from 'class-validator';
import * as dayjs from 'dayjs';
import { isArray, isBoolean, isNumber, isString } from 'lodash';
import { DEFAULT_DATE_FORMAT, DEFAULT_DATE_TIME_FORMAT } from '../../../constant/date';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { FieldDataTypeError } from '../../validation/types/transaction-validation-error';

// Types for validation context
export interface ValidationContext {
    fieldId: string;
    fieldValue: any;
    formField: FormFieldTenancyEntity;
    formFieldIdMapping?: Map<string, string>;
    collectionFieldIdMapping?: Map<
        string,
        {
            collectionKey: string;
            collectionFieldKey: string;
        }
    >;
    rowKey?: string;
}

export interface LookupValue {
    fieldId: string;
    value: string[];
    type: string;
    label: string;
}

// Base error creator function
const createFieldError = (context: ValidationContext, message: string, type?: FormFieldTypeEnum): FieldDataTypeError => {
    let fieldKey = context.formFieldIdMapping?.get(context.fieldId);

    if (!fieldKey) {
        const collection = context.collectionFieldIdMapping?.get(`${context?.rowKey}_${context?.fieldId}`);
        fieldKey = `Field ${collection?.collectionFieldKey} in collection ${collection?.collectionKey}`;
    }

    return {
        fieldId: context.fieldId,
        type: type || context.formField.type,
        label: context.formField.label,
        message: `${fieldKey} ${message}`,
    };
};

// Number field validators
export const validateNumberField = (context: ValidationContext): FieldDataTypeError | null => {
    const { fieldValue } = context;

    if (!isNumber(fieldValue)) {
        return createFieldError(context, 'value must be a number');
    }

    return null;
};

export const validateTimePickerField = validateNumberField;
export const validateDurationField = validateNumberField;

// Select field validator
export const validateSelectField = (context: ValidationContext): FieldDataTypeError | null => {
    const { fieldValue, formField } = context;
    const fieldOptions = (formField.configuration?.options as Array<{ label: string; value: string }>) || [];
    const isMultipleMode = formField?.configuration?.mode === 'multiple';

    const isValidOption = (optionValue: string) => fieldOptions.some((option) => option.value === optionValue);

    if (isMultipleMode) {
        if (!isArray(fieldValue)) {
            return createFieldError(context, 'value must be an array of options');
        }

        const invalidOptions = fieldValue.filter((v: string) => !isValidOption(v));
        if (invalidOptions.length > 0) {
            return createFieldError(context, 'value must be a valid option');
        }
    } else {
        if (!isValidOption(fieldValue)) {
            return createFieldError(context, 'value must be a valid option');
        }
    }

    return null;
};

// Date field validators
export const validateDatePickerField = (context: ValidationContext): FieldDataTypeError | null => {
    const { fieldValue } = context;
    try {
        const isValid = dayjs(fieldValue, DEFAULT_DATE_FORMAT).isValid();

        if (!isValid) {
            return createFieldError(context, 'value must be a valid date picker - MM/DD/YYYY');
        }

        return null;
    } catch (e) {
        return createFieldError(context, 'value must be a valid date picker - MM/DD/YYYY');
    }
};

export const validateDatetimePickerField = (context: ValidationContext): FieldDataTypeError | null => {
    const { fieldValue } = context;
    try {
        const isValid = dayjs(fieldValue, DEFAULT_DATE_TIME_FORMAT).isValid();

        if (!isValid) {
            return createFieldError(context, 'value must be a valid datetime picker - MM/DD/YYYY HH:mm');
        }
    } catch (e) {
        return createFieldError(context, 'value must be a valid datetime picker - MM/DD/YYYY HH:mm');
    }
};

// Lookup field validators
export const validateLookupField = (context: ValidationContext): { error: FieldDataTypeError | null; lookupValue: LookupValue | null } => {
    const { fieldValue, fieldId, formField } = context;

    if (!fieldValue) {
        return { error: null, lookupValue: null };
    }

    const lookupValueIds = isArray(fieldValue) ? fieldValue : fieldValue.split(',') || [];

    const lookupValue: LookupValue = {
        fieldId,
        value: lookupValueIds,
        label: formField.label,
        type: FormFieldTypeEnum.Lookup,
    };

    const isValidUUID = lookupValueIds.every((id: string) => isUUID(id));

    if (!isValidUUID) {
        return {
            error: createFieldError(context, 'value must be a array of UUIDs'),
            lookupValue,
        };
    }

    return { error: null, lookupValue };
};

export const validateRoleLookupField = (
    context: ValidationContext,
): { error: FieldDataTypeError | null; lookupValue: LookupValue | null } => {
    const { fieldValue, fieldId, formField } = context;

    if (!fieldValue) {
        return { error: null, lookupValue: null };
    }

    const lookupValue: LookupValue = {
        fieldId,
        value: isArray(fieldValue) ? fieldValue : fieldValue.split(',') || [],
        label: formField.label,
        type: FormFieldTypeEnum.RoleLookup,
    };

    return { error: null, lookupValue };
};

export const validateUserLookupField = (
    context: ValidationContext,
): { error: FieldDataTypeError | null; lookupValue: LookupValue | null } => {
    const { fieldValue, fieldId, formField } = context;

    if (!fieldValue) {
        return { error: null, lookupValue: null };
    }

    const lookupValue: LookupValue = {
        fieldId,
        value: isArray(fieldValue) ? fieldValue : fieldValue.split(',') || [],
        label: formField.label,
        type: FormFieldTypeEnum.UserLookup,
    };

    return { error: null, lookupValue };
};

// Checkbox field validator
export const validateCheckboxField = (context: ValidationContext): FieldDataTypeError | null => {
    const { fieldValue } = context;

    if (!isBoolean(fieldValue)) {
        return createFieldError(context, 'value must be a boolean');
    }

    return null;
};

// String field validator (default case)
export const validateStringField = (context: ValidationContext): FieldDataTypeError | null => {
    const { fieldValue } = context;

    if (!isString(fieldValue)) {
        return createFieldError(context, 'value must be a string');
    }

    return null;
};

// Calculation field validator
export const validateCalculationField = (context: ValidationContext): FieldDataTypeError | null => {
    const { formField } = context;
    const resultType = formField.configuration?.calculationFormula?.dataType as FormFieldTypeEnum;

    switch (resultType) {
        case FormFieldTypeEnum.Number:
        case FormFieldTypeEnum.TimePicker:
        case FormFieldTypeEnum.Duration:
            return validateNumberField(context);

        case FormFieldTypeEnum.DatePicker:
            return validateDatePickerField(context);

        case FormFieldTypeEnum.DatetimePicker:
            return validateDatetimePickerField(context);

        default:
            return createFieldError(context, 'value must be a valid calculation value');
    }
};

// Main field validator function
export const validateFieldByType = (context: ValidationContext): { error: FieldDataTypeError | null; lookupValue?: LookupValue } => {
    const { formField } = context;

    switch (formField.type) {
        case FormFieldTypeEnum.Number:
        case FormFieldTypeEnum.TimePicker:
        case FormFieldTypeEnum.Duration:
            return { error: validateNumberField(context) };

        case FormFieldTypeEnum.Select:
            return { error: validateSelectField(context) };

        case FormFieldTypeEnum.DatePicker:
            return { error: validateDatePickerField(context) };

        case FormFieldTypeEnum.DatetimePicker:
            return { error: validateDatetimePickerField(context) };

        case FormFieldTypeEnum.Lookup:
            const lookupResult = validateLookupField(context);
            return {
                error: lookupResult.error,
                lookupValue: lookupResult.lookupValue,
            };

        case FormFieldTypeEnum.RoleLookup:
            const roleLookupResult = validateRoleLookupField(context);
            return {
                error: roleLookupResult.error,
                lookupValue: roleLookupResult.lookupValue,
            };

        case FormFieldTypeEnum.UserLookup:
            const userLookupResult = validateUserLookupField(context);
            return {
                error: userLookupResult.error,
                lookupValue: userLookupResult.lookupValue,
            };

        case FormFieldTypeEnum.Calculation:
            return { error: validateCalculationField(context) };

        case FormFieldTypeEnum.Checkbox:
            return { error: validateCheckboxField(context) };

        default:
            return { error: validateStringField(context) };
    }
};
