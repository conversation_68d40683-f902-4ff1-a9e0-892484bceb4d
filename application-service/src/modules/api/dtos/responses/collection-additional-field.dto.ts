import { AutoMap } from '@automapper/classes';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { AdditionalFieldType } from '../../../../database/src/shared/enums/additional-field-type.enum';
import { AutoPopulateDataSource } from '../../../../database/src/shared/enums/auto-populate-data-source.enum';

export class FormCollectionAdditionalFieldDto {
    @AutoMap()
    id?: string;

    @AutoMap()
    fieldId: string;

    @AutoMap()
    label: string;

    @AutoMap()
    type: FormFieldTypeEnum;

    @AutoMap()
    additionalType: AdditionalFieldType;

    @AutoMap()
    transactionId: string; // Data register record ID

    @AutoMap()
    formVersionId: string;

    @AutoMap()
    formCollectionItemIdentityId: string;

    @AutoMap()
    configuration: Record<string, any>;

    @AutoMap(() => String)
    autoPopulateDataSource?: AutoPopulateDataSource;

    @AutoMap()
    autoPopulateDataSourceId?: string;

    @AutoMap()
    autoPopulateTargetId?: string;
}
