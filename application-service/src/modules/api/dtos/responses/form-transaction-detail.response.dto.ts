import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { TransactionDetailFieldResponseDto } from './transaction-detail-field.response.dto';

export class FormTransactionDetailResponseDto {
    @AutoMap()
    formId: string;

    @AutoMap()
    stageId: string;

    @AutoMap()
    stageName?: string;

    @AutoMap()
    formName?: string;

    @AutoMap()
    previousStageName?: string;

    stageIdentityId?: string;

    @AutoMap()
    formVersionId: string;

    @AutoMap()
    formVersion: number;

    @AutoMap()
    previousStageId?: string;

    @AutoMap()
    @ApiProperty({
        description: 'Collections',
        type: Object,
    })
    collections: Record<string, { transactionFields: TransactionDetailFieldResponseDto[] }>;

    @AutoMap()
    @ApiProperty({
        description: 'Transaction fields',
        type: [TransactionDetailFieldResponseDto],
    })
    transactionFields: TransactionDetailFieldResponseDto[];
}
