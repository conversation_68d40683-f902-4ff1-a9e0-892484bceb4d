import { AutoMap } from '@automapper/classes';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';

export class FormFieldDto {
    @AutoMap()
    id?: string;

    @AutoMap()
    fieldId: string;

    @AutoMap()
    label: string;

    @AutoMap()
    type: FormFieldTypeEnum;

    @AutoMap()
    isDefault?: boolean;

    @AutoMap()
    configuration: Record<string, any>;

    @AutoMap()
    formVersionId?: string;

    @AutoMap()
    lookupDataSet?: string;

    @AutoMap()
    lookupTargetId?: string;
}
