import { AutoMap } from '@automapper/classes';
import { TransactionFieldResponseDto } from './transaction-field.response.dto';

export class FormTransactionResponseDto {
    @AutoMap()
    id: string;

    @AutoMap()
    formId: string;

    @AutoMap()
    stageId: string;

    @AutoMap()
    stageName?: string;

    @AutoMap()
    formName?: string;

    @AutoMap()
    previousStageName?: string;

    stageIdentityId?: string;

    @AutoMap()
    formVersionId: string;

    @AutoMap()
    formVersion: number;

    @AutoMap()
    previousStageId?: string;

    @AutoMap(() => [TransactionFieldResponseDto])
    transactionFields?: TransactionFieldResponseDto[];
}
