import { AutoMap } from '@automapper/classes';
import { FormFieldTypeEnum } from '../../../../common/src/modules/shared/enums/form-field-type.enum';
import { TransactionFieldOverrideDto } from '../../../form/dtos/transaction-field-override.dto';
import { TransactionFieldContextTypeEnum } from '../../../../database/src/shared/enums/transaction-field.enum';

export class TransactionDetailFieldResponseDto {
    @AutoMap()
    id: string;

    @AutoMap()
    fieldId: string;

    @AutoMap()
    transactionId: string;

    @AutoMap()
    fieldValue?: string;

    @AutoMap()
    collectionId?: string;

    @AutoMap()
    collectionItemId?: string;

    @AutoMap()
    contextType?: TransactionFieldContextTypeEnum;

    @AutoMap()
    rowKey?: string;

    @AutoMap()
    displayValue?: string;

    @AutoMap()
    parentId?: string;

    @AutoMap(() => [String])
    fieldOptionIds?: string[];

    @AutoMap()
    type?: FormFieldTypeEnum;

    @AutoMap()
    mode?: string;

    @AutoMap()
    format?: string;

    // @AutoMap()
    // style?: Record<string, any>;

    // @AutoMap()
    // pairId?: string;

    @AutoMap()
    validationValue?: number;

    @AutoMap()
    dependFieldId: string;

    // @AutoMap()
    // transactionFieldOverrides?: TransactionFieldOverrideDto[];

    @AutoMap()
    data?: Record<string, any>;

    @AutoMap()
    inVisible?: boolean;

    // @AutoMap()
    // fieldType?: FormFieldTypeEnum;
}
