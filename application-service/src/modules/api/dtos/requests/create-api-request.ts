import { ApiHideProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsObject, IsOptional, IsUUID } from 'class-validator';
import { EditFormTransactionFieldRequest } from '../../../form/dtos/requests/create-form-transaction.request';

export class CreateApiRequestDto {
    @ApiPropertyOptional({
        type: 'object',
        description: 'Form field data',
        example: {
            field1: 'value1',
            field2: ['value2', 'value3'],
            field3: null,
        },
    })
    @IsOptional()
    @IsObject()
    formFields?: Record<string, string | string[] | null>;

    @ApiHideProperty()
    @IsOptional()
    transactionFields?: EditFormTransactionFieldRequest[];

    @ApiHideProperty()
    @IsUUID()
    @IsOptional()
    formId?: string;

    @ApiHideProperty()
    @IsOptional()
    formFieldIdMapping?: Map<string, string>;

    @ApiHideProperty()
    @IsOptional()
    formVersionId?: string;

    @ApiHideProperty()
    @IsOptional()
    stageId?: string;

    @ApiHideProperty()
    @IsOptional()
    activeStageId?: string;

    @ApiHideProperty()
    @IsOptional()
    collectionFieldIdMapping?: Map<
        string,
        {
            collectionKey: string;
            collectionFieldKey: string;
        }
    >;
}
