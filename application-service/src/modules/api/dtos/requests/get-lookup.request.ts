import { AutoMap } from '@automapper/classes';
import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, IsUUID, Max, Min } from 'class-validator';
import { DEFAULT_PAGE_SIZE } from '../../../../constant';
import { DataRegisterTypeEnum } from '../../../../database/src/shared/enums/data-register-type.enum';
import { MultipleFilterRequestDto } from '../../../../shared/common/dto/multiple-filter-request.dto';

export class ApiGetLookupRequestDto extends MultipleFilterRequestDto {
    @AutoMap()
    @IsOptional()
    @IsEnum(DataRegisterTypeEnum)
    @ApiPropertyOptional({ enum: DataRegisterTypeEnum })
    type?: DataRegisterTypeEnum;
}

export class ApiUserLookupRequestDto extends MultipleFilterRequestDto {
    includeRelations?: string[];
    select?: string[];
    onlyActive?: boolean;
    filterType?: 'or' | 'and' | undefined;
}

export class ApiRoleLookupRequestDto extends MultipleFilterRequestDto {
    select?: string[];
    operatorType?: 'or' | 'and' | undefined;
}

export class ApiLookupRequestDto {
    @AutoMap()
    @ApiHideProperty()
    @IsUUID()
    @IsOptional()
    formId?: string;

    @AutoMap()
    @ApiProperty()
    @IsString()
    fieldKey: string;

    @ApiPropertyOptional({
        minimum: 1,
        default: 1,
    })
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    pageIndex?: number = 1;

    @ApiPropertyOptional({
        minimum: 1,
        maximum: 100,
        default: DEFAULT_PAGE_SIZE,
    })
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    @IsOptional()
    pageSize?: number = DEFAULT_PAGE_SIZE;

    @ApiHideProperty()
    @IsString()
    @IsOptional()
    fieldId?: string;

    get take(): number {
        return this.pageSize;
    }

    get skip(): number {
        return (this.pageIndex - 1) * this.pageSize;
    }
}
