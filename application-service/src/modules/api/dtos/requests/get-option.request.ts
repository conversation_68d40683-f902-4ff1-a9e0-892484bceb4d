import { AutoMap } from '@automapper/classes';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class ApiGetOptionRequestDto {
    @AutoMap()
    @ApiHideProperty()
    @IsUUID()
    @IsOptional()
    formId?: string;

    @AutoMap()
    @ApiProperty()
    @IsString()
    fieldKey: string;

    @AutoMap()
    @ApiHideProperty()
    @IsString()
    @IsOptional()
    fieldId?: string;
}
