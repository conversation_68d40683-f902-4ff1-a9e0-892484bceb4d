import { ApiHideProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsObject, IsOptional, IsUUID } from 'class-validator';
import { EditFormTransactionFieldRequest } from '../../../form/dtos/requests/create-form-transaction.request';
import { ApiCollectionRequestDto } from './api-collection-request';
import { CreateApiRequestDto } from './create-api-request';

export class UpdateApiRequestDto extends CreateApiRequestDto {
    constructor() {
        super();
    }

    @ApiPropertyOptional({
        type: 'string',
        description: 'Transaction id',
        example: '123e4567-e89b-12d3-a456-************',
    })
    @IsUUID()
    @IsOptional()
    transactionId?: string;

    @ApiPropertyOptional({
        type: 'object',
        description: 'Collection fields',
        example: {
            addCollectionRows: {
                collectionKey: [],
            },
            updateCollectionFields: [],
            deleteCollectionsRows: [],
        },
    })
    @IsOptional()
    @IsObject()
    collections?: ApiCollectionRequestDto;

    @ApiHideProperty()
    @IsOptional()
    collectionFields?: {
        addCollectionRows: EditFormTransactionFieldRequest[];
        updateCollectionFields: EditFormTransactionFieldRequest[];
        deleteCollectionsRows: string[];
    };
}
