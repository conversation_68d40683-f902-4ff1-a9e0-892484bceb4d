import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateFileRequest {
    @ApiProperty()
    @IsString()
    fieldKey: string;

    @ApiHideProperty()
    @IsUUID()
    @IsOptional()
    formId: string;

    @ApiHideProperty()
    @IsUUID()
    @IsOptional()
    transactionId?: string;

    @ApiHideProperty()
    @IsString()
    @IsOptional()
    fieldId?: string;
}
