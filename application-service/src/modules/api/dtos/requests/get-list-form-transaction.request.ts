import { Is<PERSON><PERSON>y, IsOptional, isUUID } from 'class-validator';
import { GetPaginationRequestDto } from '../../../../shared/common/dto/pagination-request.dto';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { FilterOptionDto } from '../../../../common/src/modules/shared/dtos/filter-option.dto';
import { AutoMap } from '@automapper/classes';
import { UtilsService } from '../../../../common/src/modules/shared/services/utils.service';
import { OrderType } from '../../../../common/src/modules/shared/enums/order.enum';
import { OrderOptionDto } from '../../../../common/src/modules/shared/dtos/order-option.dto';

export class ApiGetListFormTransactionRequestDto extends GetPaginationRequestDto {
    @AutoMap()
    @ApiPropertyOptional({ name: 'filters', description: 'Represent the filters to be applied to the query' })
    @IsOptional()
    @Transform((value) => {
        const result = value?.value?.map((item) => {
            return {
                field: item.field,
                operator: UtilsService.transformOperator(item.operator),
                value: item.value,
                queryToOptionIds: isUUID(item.value) && !['dataRegisterId', 'transactionId'].includes(item.field),
                queryToDataFilterOptions: item.queryToDataFilterOptions?.trim()?.toLowerCase() === 'true',
            } as FilterOptionDto;
        });
        return result;
    })
    filters?: FilterOptionDto[];

    @AutoMap()
    @ApiProperty({ name: 'sorters' })
    @IsOptional()
    @Transform((value) => {
        const result = value?.value?.map((item) => {
            return {
                field: item.field,
                order: item.order ? item.order.toUpperCase() : OrderType.ASC,
            } as OrderOptionDto;
        });
        return result;
    })
    sorters?: OrderOptionDto[];

    fieldIds?: string[];
}
