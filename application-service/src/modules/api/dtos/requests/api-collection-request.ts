import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsObject, IsOptional, IsString } from 'class-validator';

export class CollectionUpdate {
    [fieldId: string]: string | string[];
}

export class CollectionRow extends CollectionUpdate {
    rowKey: string;
}

export class CollectionUpdateRequest {
    [collectionKey: string]: CollectionRow[];
}

export class AddOrDeleteCollectionRow {
    [collectionKey: string]: string[];
}

export class ApiCollectionRequestDto {
    @ApiProperty({
        type: 'object',
        description: 'Collection update data',
        example: {
            collection1: {
                row1: 'string',
                field1: 'value1',
            },
        },
    })
    @IsObject()
    @IsOptional()
    updateCollectionFields?: CollectionUpdateRequest;

    @ApiProperty({
        type: 'array',
        description: 'Collection add data',
        example: ['collectionItem1', 'collectionItem2'],
    })
    @IsOptional()
    @IsObject()
    addCollectionRows?: AddOrDeleteCollectionRow;

    @ApiProperty({
        type: 'array',
        description: 'Collection delete data',
        example: ['row1', 'row2'],
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    deleteCollectionsRows?: AddOrDeleteCollectionRow;

    @ApiProperty({
        type: 'boolean',
        description: 'Check if API configuration is enabled',
        example: true,
        default: true,
    })
    @IsBoolean()
    @IsOptional()
    checkEnabled?: boolean = true;
}
