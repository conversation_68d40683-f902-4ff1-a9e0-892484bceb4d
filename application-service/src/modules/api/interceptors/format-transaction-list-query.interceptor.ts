import { Bad<PERSON>equestEx<PERSON>, CallHandler, ExecutionContext, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { Observable } from 'rxjs';
import { ClaimService, LoggerService } from '../../../common/src';
import { USER_CLAIMS } from '../../../common/src/constant/config';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { ApiMappingConfiguration } from '../dtos/api-mapping-configuration';
import { ApiDataService } from '../services';
import { FormatRequestService } from '../services/format-request.service';
import { ApiGetListFormTransactionRequestDto } from '../dtos/requests/get-list-form-transaction.request';

@Injectable()
export class FormatTransactionListQueryInterceptor implements NestInterceptor {
    constructor(
        private readonly _apiDataService: ApiDataService,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _logger: LoggerService,
        private readonly _formatRequestService: FormatRequestService,
    ) {}

    async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
        const request = context.switchToHttp().getRequest();
        const formId = request.params?.formId;
        const query = request.query;

        try {
            if (!query || isEmpty(query) || !formId) {
                return next.handle();
            }
            const formattedQuery: ApiGetListFormTransactionRequestDto = { ...(query ?? {}) };
            const apiVersion = await this._apiDataService.getApiVersionByContext(
                formId,
                ApiContextType.FormTransaction,
                this._claims.accountId,
            );
            const mappings: ApiMappingConfiguration[] = apiVersion?.configuration?.fieldMapping;
            if (isEmpty(mappings)) {
                return next.handle();
            }
            if (mappings?.length) {
                if (formattedQuery && (!isEmpty(formattedQuery.filters) || !isEmpty(formattedQuery.sorters))) {
                    await this._formatRequestService.formatFormTransactionListQuery({
                        query: formattedQuery,
                        fieldMappings: mappings,
                    });
                }
                request.query = formattedQuery;
            }
            return next.handle();
        } catch (error) {
            this._logger.error('Api Module - FormatTransactionListQueryInterceptor', error);
            const message = 'Invalid request format or parameters.';
            throw new BadRequestException({
                code: 'invalid_request',
                message,
            });
        }
    }
}
