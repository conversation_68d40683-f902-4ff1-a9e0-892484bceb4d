import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import * as _ from 'lodash';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { DataSource } from 'typeorm';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { ClaimService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { FormRoleStageACLDto } from '../../../common/src/modules/acl/dto/form-role-stage-acl.dto';
import { StageRoleAccessControlConfig } from '../../../database/src/shared/dto/access-control.dto';
import { DEFAULT_TRANSACTION_FIELD_ID } from '../../../constant';
import { ApiResponseDto, FormTransactionResponseDto } from '../dtos';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { ApiContextType } from '../../../database/src/shared/enums/automation.enum';
import { ApiDataService } from '../services/data-services/api.data.service';
import { ApiTransactionService } from '../services/transaction.service';

@Injectable()
export class ApiFormTransactionListInterceptor implements NestInterceptor<any, any> {
    constructor(
        @Inject(IStageRoleACL)
        private readonly _stageRoleACL: IStageRoleACLProvider,
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
        private readonly _apiDataService: ApiDataService,
        private readonly _transactionService: ApiTransactionService,
    ) {}

    intercept(
        context: ExecutionContext,
        next: CallHandler<ApiResponseDto<PaginationResponseDto<FormTransactionResponseDto>>>,
    ): Observable<any> {
        return next.handle().pipe(
            switchMap(async (pagedResult) => {
                const request = context.switchToHttp().getRequest();
                const formId = request.params?.formId;

                const { data: transactions } = pagedResult?.data;

                if (!transactions?.length) {
                    return pagedResult;
                }

                const roleIds: string[] = this._claims.roles.map((role) => role);

                if (!roleIds.length)
                    return {
                        data: [],
                        total: 0,
                    };

                const aclResults = await this._groupACLs({
                    roleIds,
                    transactions,
                });

                const apiVersion = await this._apiDataService.getApiVersionByContext(
                    formId,
                    ApiContextType.FormTransaction,
                    this._claims.accountId,
                );

                const mappings = apiVersion?.configuration?.fieldMapping;

                for (const transaction of transactions) {
                    const aclResult: FormRoleStageACLDto[] =
                        (aclResults || []).find((acl) => acl.transactionId === transaction.id)?.aclResult || [];
                    const transactionFields = transaction.transactionFields || [];
                    transactionFields.forEach((transactionField) => {
                        if (transactionField.fieldId !== DEFAULT_TRANSACTION_FIELD_ID) {
                            const fieldAcls: StageRoleAccessControlConfig[] = _.compact(
                                aclResult.map((acl) => acl.field[transactionField.fieldId]),
                            );
                            const canRead = fieldAcls.some((fieldAcl) => fieldAcl.visible?.toString() === 'true');
                            if (!canRead) {
                                transactionField.fieldValue = '******';
                            }
                        }
                        this._transactionService.getTransactionFieldMapping(transactionField, mappings);
                    });
                }
                return pagedResult;
            }),
        );
    }

    async _groupACLs({ roleIds, transactions }: { roleIds: string[]; transactions: FormTransactionResponseDto[] }): Promise<
        {
            aclResult: FormRoleStageACLDto[];
            transactionId: string;
        }[]
    > {
        // Create a map to group transactions by formVersionId and stageId
        const groupedTransactions = new Map<
            string,
            {
                formVersionId: string;
                stageId: string;
                transactions: FormTransactionResponseDto[];
            }
        >();

        // Group transactions
        transactions.forEach((transaction) => {
            const key = `${transaction.formVersionId}_${transaction.stageId}`;
            if (!groupedTransactions.has(key)) {
                groupedTransactions.set(key, {
                    formVersionId: transaction.formVersionId,
                    stageId: transaction.stageId,
                    transactions: [],
                });
            }
            groupedTransactions.get(key).transactions.push(transaction);
        });

        // Get ACLs for each group
        const groupResults = await Promise.all(
            Array.from(groupedTransactions.values()).map(async (group) => {
                const stageRoles = roleIds.map((roleId) => ({ roleId, stageId: group.stageId }));
                const aclResult = await this._stageRoleACL.getRoleStageACLs({
                    request: {
                        formVersionId: group.formVersionId,
                        stageRoles,
                    },
                    stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
                    stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
                });
                return { aclResult, transactions: group.transactions };
            }),
        );

        // Apply ACLs to each transaction
        return transactions.map((transaction) => {
            const group = groupResults.find((g) => g.transactions.some((t) => t.id === transaction.id));
            return {
                aclResult: group.aclResult,
                transactionId: transaction.id,
            };
        });
    }
}
