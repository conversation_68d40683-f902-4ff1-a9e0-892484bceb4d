import { Mapper, MappingProfile, createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormTransactionResponseDto } from '../dtos/responses/form-transaction.response.dto';
import { FormTransactionDto } from '../../form/dtos/form-transaction.dto';
import { FormTransactionDetailResponseDto } from '../dtos/responses/form-transaction-detail.response.dto';
import { FormTransactionFieldDto } from '../../form/dtos/form-transaction-field.dto';
import { TransactionFieldResponseDto } from '../dtos/responses/transaction-field.response.dto';

@Injectable()
export class FormTransactionMappingProfile extends AutomapperProfile {
    constructor(@InjectMapper() mapper: Mapper) {
        super(mapper);
    }

    get profile(): MappingProfile {
        return (mapper: Mapper) => {
            createMap(
                mapper,
                TransactionEntity,
                FormTransactionResponseDto,
                forMember(
                    (dest) => dest.formVersion,
                    mapFrom((source) => {
                        return source.formVersion?.version;
                    }),
                ),
            );
            createMap(mapper, FormTransactionDto, FormTransactionResponseDto);
            createMap(mapper, FormTransactionFieldDto, TransactionFieldResponseDto);
            createMap(mapper, FormTransactionDto, FormTransactionDetailResponseDto);
        };
    }
}
