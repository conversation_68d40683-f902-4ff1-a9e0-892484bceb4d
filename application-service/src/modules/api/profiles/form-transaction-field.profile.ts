import { <PERSON><PERSON>, MappingProfile, createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { RoleTenancyEntity } from '../../../database/src/entities/tenancy/role.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { UserTenancyEntity } from '../../../database/src/entities/tenancy/user.tenancy.entity';
import { DataRegisterTransactionOptionDto } from '../../data-register/dtos/respsonses/data-register-transaction-option.dto';
import { FormTransactionFieldDto } from '../../form/dtos';
import { EditFormTransactionFieldRequest } from '../../form/dtos/requests/create-form-transaction.request';
import { TransactionFieldResponseDto } from '../dtos';
import { FormFieldDto } from '../dtos/responses/api-form.dto';
import { ApiLookupResponseDto } from '../dtos/responses/api-lookup.dto';
import { TransactionDetailFieldResponseDto } from '../dtos/responses/transaction-detail-field.response.dto';

@Injectable()
export class FormTransactionFieldMappingProfile extends AutomapperProfile {
    constructor(@InjectMapper() mapper: Mapper) {
        super(mapper);
    }

    get profile(): MappingProfile {
        return (mapper: Mapper) => {
            createMap(
                mapper,
                TransactionFieldEntity,
                FormTransactionFieldDto,
                forMember(
                    (dest) => dest.fieldOptionIds,
                    mapFrom((source) => source.fieldOptionIds ?? []),
                ),
                forMember(
                    (d) => d.data,
                    mapFrom((s) => s.data ?? {}),
                ),
            );
            createMap(
                mapper,
                FormTransactionFieldDto,
                TransactionFieldEntity,
                forMember(
                    (dest) => dest.fieldOptionIds,
                    mapFrom((source) => source.fieldOptionIds ?? []),
                ),
            );

            createMap(mapper, FormTransactionFieldDto, TransactionFieldResponseDto);
            createMap(
                mapper,
                FormTransactionFieldDto,
                TransactionDetailFieldResponseDto,
                forMember(
                    (dest) => dest.fieldOptionIds,
                    mapFrom((source) => source.fieldOptionIds ?? []),
                ),
                // forMember(
                //     (dest) => dest.style,
                //     mapFrom((source) => source.style ?? {}),
                // ),
                // forMember(
                //     (dest) => dest.transactionFieldOverrides,
                //     mapFrom((source) => (source.transactionFieldOverrides ?? []) as any[]),
                // ),
                forMember(
                    (d) => d.data,
                    mapFrom((s) => s.data ?? {}),
                ),
            );
            createMap(mapper, TransactionFieldEntity, EditFormTransactionFieldRequest);
            createMap(
                mapper,
                FormFieldTenancyEntity,
                FormFieldDto,
                forMember(
                    (d) => d.configuration,
                    mapFrom((s) => s.configuration ?? {}),
                ),
            );
            createMap(
                mapper,
                DataRegisterTransactionOptionDto,
                ApiLookupResponseDto,
                forMember(
                    (d) => d.name,
                    mapFrom((s) => s.label),
                ),
            );
            createMap(
                mapper,
                UserTenancyEntity,
                ApiLookupResponseDto,
                forMember(
                    (d) => d.name,
                    mapFrom((s) => `${s.firstName} ${s.secondName}`),
                ),
            );
            createMap(mapper, RoleTenancyEntity, ApiLookupResponseDto);
        };
    }
}
