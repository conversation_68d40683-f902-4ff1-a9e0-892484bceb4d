import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { compact, groupBy, isEqual, uniq } from 'lodash';
import { DataSource, In, Repository } from 'typeorm';
import { LoggerService } from '../../../common/src';
import { DEFAULT_DATE_TIME_FORMAT } from '../../../constant/date';
import { DataRegisterFieldEntity } from '../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { DocumentFieldEntity } from '../../../database/src/entities/public/document-field.public.entity';
import { FormCollectionEntity } from '../../../database/src/entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../../database/src/entities/public/form-field.public.entity';
import { FormEntity } from '../../../database/src/entities/public/form.public.entity';
import { StageEntity } from '../../../database/src/entities/public/stage.public.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { DocumentDatasourceTypeEnum } from '../../../database/src/shared/enums/document-datasource-type.enum';
import { DocumentFieldTypeEnum } from '../../../database/src/shared/enums/document-field-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { DataRegisterWithActiveVersionService } from '../../../shared/services/data-register-with-active-version.service';
import { FormWithVersionService } from '../../../shared/services/form-with-version.service';
import { DocumentContentService } from '../content/document.content.service';
import { DatasourceData, DocFieldValue, DocMetadata, DocumentRepositories } from '../type';
import { FormCollectionDataService } from '../../form/services/form-collection.data.service';
import { DataSourceService } from '../../../database/src/services/connection-util.service';
import { FormCollectionAdditionalFieldEntity } from '../../../database/src/entities/public/form-collection-additional-fields.public.entity';

@Injectable()
export class DocumentDatasourceDataService {
    constructor(
        private readonly _logger: LoggerService,
        private readonly _content: DocumentContentService,
        private readonly _formWithVersionService: FormWithVersionService,
        private readonly _dataRegisterWithActiveVersionService: DataRegisterWithActiveVersionService,
        private readonly _dataSourceService: DataSourceService,
    ) {}

    public getDataFromDatasource = async (params: {
        dataId: string;
        datasourceType: DocumentDatasourceTypeEnum;
        dataSource: DataSource;
        isAccount: boolean;
        documentFields: DocumentFieldEntity[];
        accountId?: string;
    }): Promise<DatasourceData> => {
        const { dataId, dataSource, datasourceType, isAccount, documentFields, accountId } = params;

        switch (datasourceType) {
            case DocumentDatasourceTypeEnum.Form: {
                return await this._getDataFromForm({
                    dataId,
                    dataSource,
                    documentFields,
                    isAccount,
                    accountId,
                });
            }

            case DocumentDatasourceTypeEnum.DataRegister: {
                return await this._getDataFromRegister({
                    dataId,
                    dataSource,
                    documentFields,
                    isAccount,
                });
            }
        }
    };

    public getSchemaFromDatasource = async (params: {
        datasourceId: string;
        datasourceType: DocumentDatasourceTypeEnum;
        dataSource: DataSource;
        isAccount: boolean;
        documentFields: DocumentFieldEntity[];
    }): Promise<{
        formCollections?: FormCollectionEntity[];
        registerVersions?: DataRegisterVersionEntity[];
        relationFormNames?: { id: string; name: string; formFields: FormFieldEntity[] }[];
        formFields;
    }> => {
        const { datasourceId, dataSource, datasourceType, isAccount, documentFields } = params;

        switch (datasourceType) {
            case DocumentDatasourceTypeEnum.Form: {
                return await this._getSchemaFromForm({
                    dataSource,
                    documentFields,
                    isAccount,
                    datasourceId,
                });
            }

            case DocumentDatasourceTypeEnum.DataRegister: {
                return await this._getSchemaFromRegister({
                    dataSource,
                    isAccount,
                    registerId: datasourceId,
                });
            }
        }
    };

    private _getSchemaFromForm = async (params: {
        datasourceId: string;
        dataSource: DataSource;
        isAccount: boolean;
        documentFields: DocumentFieldEntity[];
    }) => {
        const { datasourceId, dataSource, isAccount, documentFields } = params;
        const repos = this._content.getRepositories({
            dataSource,
            isAccount,
        });

        const { formFields, formVersion } = await this._getForm({
            formId: datasourceId,
            formRepo: repos.formRepo,
        });

        const formCollections = await this._getFormCollections({
            formCollectionRepo: repos.formCollectionRepo,
            formVersionId: formVersion.id,
            documentFields,
        });

        // #region lookup fields
        const lookupRegisterVersions = await this._getLookupRegisterVersions({
            formFields,
            registerRepo: repos.registerRepo,
            registerVersionRepo: repos.registerVersionRepo,
            registerFieldRepo: repos.dataRegisterFieldRepo,
        });
        // #endregion lookup fields

        // #region collection
        const collectionRegisterVersions = await this._getCollectionRegisterVersions({
            formCollections,
            registerVersionRepo: repos.registerVersionRepo,
        });
        // #endregion collection

        // #region relation forms
        const relationFormDocFields = (documentFields || []).filter(
            (documentField) => documentField.type === DocumentFieldTypeEnum.RelatedForm,
        );
        const relationFormIds = uniq(relationFormDocFields.map((documentField) => documentField.sourceId));

        const relationFormNames = await this._getRelationForms({
            formIds: relationFormIds,
            repos,
        });

        // #endregion relation forms

        return {
            formFields,
            formCollections,
            registerVersions: [lookupRegisterVersions, collectionRegisterVersions].flat(),
            relationFormNames,
        };
    };

    private _getDataFromRegister = async (params: {
        dataId: string;
        dataSource: DataSource;
        isAccount: boolean;
        documentFields: DocumentFieldEntity[];
    }): Promise<DatasourceData> => {
        const { dataId, dataSource, isAccount, documentFields } = params;
        const {
            registerFieldRepo,
            registerTransactionRepo,
            registerRepo,
            transactionFieldRepo,
            registerVersionRepo,
            dataRegisterFieldRepo,
        } = this._content.getRepositories({
            dataSource,
            isAccount,
        });

        const docFieldValues: DocFieldValue[] = [];

        const registerTransaction = await registerTransactionRepo.findOne({
            where: {
                id: dataId,
            },
        });
        if (!registerTransaction) throw new Error('register_transaction_not_found');

        const registers = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
            dataRegisterIds: [registerTransaction.dataRegisterId as string],
            dataRegisterRepository: registerRepo,
            dataRegisterVersionRepository: registerVersionRepo,
            dataRegisterFieldRepository: dataRegisterFieldRepo,
        });

        if (!registers?.length || !registers[0].dataRegisterVersions?.length) throw new Error('data_register_not_found');

        const registerVersion = registers[0].dataRegisterVersions[0];

        const formFields = registerVersion.fields;

        const dataFields = await registerFieldRepo.find({
            where: {
                dataRegisterTransactionId: dataId,
            },
        });

        const drFields = documentFields.filter((field) => field.type === DocumentFieldTypeEnum.Register);
        let drFieldData: Record<string, DataRegisterTransactionFieldEntity[]> = {};
        if (drFields?.length) {
            drFieldData = await this._getDataRegisterData({
                dataSourceId: dataId,
                dataSourceType: DocumentDatasourceTypeEnum.Form,
                drFields: drFields,
                drRepository: registerTransactionRepo,
                drFieldRepository: registerFieldRepo,
                formTranFieldRepository: transactionFieldRepo,
            });
        }

        for (const field of documentFields) {
            if (field.type !== DocumentFieldTypeEnum.Register) {
                const values = dataFields.filter((fld) => fld.fieldId === field.sourceId);
                docFieldValues.push({ type: field.type, values, identityId: field.identityId });
            } else {
                const dataSources = drFieldData[field.identityId];
                const dataSourceDefinition = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                    dataRegisterIds: [field.sourceId],
                    dataRegisterRepository: registerRepo,
                    dataRegisterVersionRepository: registerVersionRepo,
                    dataRegisterFieldRepository: dataRegisterFieldRepo,
                });
                const lookupRegisterVersions = dataSourceDefinition.map((r) => r.dataRegisterVersions[0]);

                docFieldValues.push({
                    type: field.type,
                    configuration: field.configuration,
                    dataSources: dataSources,
                    dataSourceDefinition: lookupRegisterVersions?.[0],
                    identityId: field.identityId,
                    values: [],
                });
            }
        }

        const metadata: DocMetadata = this._getMetadata({
            transaction: registerTransaction,
        });

        return {
            docFieldValues,
            formFields,
            dataFields,
            metadata,
        };
    };

    private _getSchemaFromRegister = async (params: { registerId: string; dataSource: DataSource; isAccount: boolean }) => {
        const { registerId, dataSource, isAccount } = params;
        const { registerRepo, registerVersionRepo, dataRegisterFieldRepo } = this._content.getRepositories({
            dataSource,
            isAccount,
        });

        const registers = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
            dataRegisterIds: [registerId as string],
            dataRegisterRepository: registerRepo,
            dataRegisterVersionRepository: registerVersionRepo,
            dataRegisterFieldRepository: dataRegisterFieldRepo,
        });

        if (!registers?.length || !registers[0].dataRegisterVersions?.length) throw new Error('data_register_not_found');

        const registerVersion = registers[0].dataRegisterVersions[0];

        const formFields = registerVersion.fields;

        return {
            formFields,
        };
    };

    private _getDataFromForm = async (params: {
        dataId?: string;
        dataSource: DataSource;
        isAccount: boolean;
        documentFields: DocumentFieldEntity[];
        accountId?: string;
    }): Promise<DatasourceData> => {
        const { dataId, dataSource, isAccount, documentFields, accountId } = params;
        const repos = this._content.getRepositories({
            dataSource,
            isAccount,
        });

        const docFieldValues: DocFieldValue[] = [];

        const transaction = await repos.transactionRepo.findOne({
            where: {
                id: dataId,
            },
            withDeleted: true,
        });

        if (!transaction.isTest && transaction.deletedAt) {
            throw new Error('transaction_not_found');
        }

        const { formFields, formVersion, formStages } = await this._getForm({
            formId: transaction.formId,
            formVersionId: transaction.formVersionId,
            formRepo: repos.formRepo,
        });

        const formCollections = await this._getFormCollections({
            formCollectionRepo: repos.formCollectionRepo,
            formVersionId: formVersion.id,
            documentFields,
        });

        // #region lookup fields
        const lookupRegisterVersions = await this._getLookupRegisterVersions({
            formFields,
            registerRepo: repos.registerRepo,
            registerVersionRepo: repos.registerVersionRepo,
            registerFieldRepo: repos.dataRegisterFieldRepo,
        });
        // #endregion lookup fields

        // #region collection
        const collectionRegisterVersions = await this._getCollectionRegisterVersions({
            formCollections,
            registerVersionRepo: repos.registerVersionRepo,
        });

        let additionalFields: FormCollectionAdditionalFieldEntity[] = [];

        const collectionWithAdditionalFields =
            documentFields.filter((f) => f.configuration.includeAdditionalFields?.toString() === 'true')?.map((f) => f.sourceId) ?? [];

        if (transaction.isTest) {
            const formCollectionService = await this._dataSourceService.resolveService<FormCollectionDataService>(
                accountId,
                FormCollectionDataService,
            );

            const collectionItems = formCollections
                ?.filter((c) => collectionWithAdditionalFields.includes(c.identityId))
                ?.flatMap((c) => c.formCollectionItems);

            additionalFields = await formCollectionService.getTestAdditionalFields({
                formVersionId: transaction.formVersionId,
                formCollectionItems: collectionItems,
            });
        } else {
            additionalFields = await repos.collectionAdditionalFieldsRepo.find({
                where: {
                    formVersionId: formVersion.id,
                },
            });
        }

        additionalFields = additionalFields?.filter((f) => f.configuration.isSupportField?.toString() === 'false') ?? [];

        const collectionItemIdentityIds =
            formCollections
                ?.filter((c) => collectionWithAdditionalFields.includes(c.identityId))
                ?.flatMap((c) => c.formCollectionItems)
                ?.map((i) => i.identityId) ?? [];

        additionalFields = additionalFields?.filter((f) => collectionItemIdentityIds.includes(f.formCollectionItemIdentityId));

        const collectionAdditionalFields = groupBy(additionalFields, 'formCollectionItemIdentityId');

        // #endregion collection

        const metadata: DocMetadata = this._getMetadata({
            transaction,
            formStages,
        });

        // #region relation transactions
        const relationFormDocFields = (documentFields || []).filter(
            (documentField) => documentField.type === DocumentFieldTypeEnum.RelatedForm,
        );
        const relationFormIds = uniq(relationFormDocFields.map((documentField) => documentField.sourceId));
        const relationTransactions = await this._getRelationTransactions({
            transactionId: dataId,
            relationFormIds,
            repos,
        });

        const relationFormNames = await this._getRelationForms({
            formIds: relationFormIds,
            repos,
        });

        // #endregion relation transactions

        const dataFields = await repos.transactionFieldRepo.find({
            where: {
                transactionId: dataId,
                inVisible: false,
            },
        });

        const drFields = documentFields.filter((field) => field.type === DocumentFieldTypeEnum.Register);
        let drFieldData: Record<string, DataRegisterTransactionFieldEntity[]> = {};
        if (drFields?.length) {
            drFieldData = await this._getDataRegisterData({
                dataSourceId: dataId,
                dataSourceType: DocumentDatasourceTypeEnum.Form,
                drFields: drFields,
                drRepository: repos.registerTransactionRepo,
                drFieldRepository: repos.registerFieldRepo,
                formTranFieldRepository: repos.transactionFieldRepo,
            });
        }

        for (const field of documentFields) {
            if (field.type !== DocumentFieldTypeEnum.Register) {
                const values = dataFields.filter((fld) => fld.fieldId === field.sourceId);
                docFieldValues.push({ type: field.type, values, identityId: field.identityId });
            } else {
                const dataSourceDefinition = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                    dataRegisterIds: [field.sourceId],
                    dataRegisterRepository: repos.registerRepo,
                    dataRegisterVersionRepository: repos.registerVersionRepo,
                    dataRegisterFieldRepository: repos.dataRegisterFieldRepo,
                });
                const lookupRegisterVersions = dataSourceDefinition.map((r) => r.dataRegisterVersions[0]);
                const dataSources = drFieldData[field.identityId];
                docFieldValues.push({
                    type: field.type,
                    configuration: field.configuration,
                    dataSources: dataSources,
                    dataSourceDefinition: lookupRegisterVersions?.[0],
                    identityId: field.identityId,
                    values: [],
                });
            }
        }

        return {
            docFieldValues,
            metadata,
            formFields,
            formCollections,
            dataFields,
            registerVersions: [lookupRegisterVersions, collectionRegisterVersions].flat(),
            relationTransactions,
            relationFormNames,
            collectionAdditionalFields,
        };
    };

    private _getMetadata = (params: {
        transaction: {
            stageId?: string;
            previousStageId?: string;
            createdAt?: Date;
            createdByUser?: string;
            updatedAt?: Date;
            updatedByUser?: string;
        };
        formStages?: StageEntity[];
    }) => {
        const { formStages, transaction } = params;
        const currentStage = formStages?.find((stage) => stage.id === transaction.stageId);
        const previousStage = formStages?.find((stage) => stage.id === transaction.previousStageId);

        const formatDisplayDateTimeValue = ({ format, value }: { format: string; value?: string | Date }) => {
            return value ? dayjs(value).format(format) : '';
        };

        const metadata: DocMetadata = {
            createdAt: formatDisplayDateTimeValue({
                format: DEFAULT_DATE_TIME_FORMAT,
                value: transaction.createdAt,
            }),
            createdBy: transaction.createdByUser,
            lastUpdatedAt: formatDisplayDateTimeValue({
                format: DEFAULT_DATE_TIME_FORMAT,
                value: transaction.updatedAt,
            }),
            updatedBy: transaction.updatedByUser,
            workflowStage: {
                current: currentStage?.name || '',
                previous: previousStage?.name || '',
            },
        };
        return metadata;
    };

    private async _getDataRegisterData({
        dataSourceId,
        dataSourceType,
        drFields,
        drRepository,
        drFieldRepository,
        formTranFieldRepository,
    }: {
        dataSourceId: string;
        dataSourceType: DocumentDatasourceTypeEnum;
        drFields: DocumentFieldEntity[];
        drRepository: Repository<DataRegisterTransactionEntity>;
        drFieldRepository: Repository<DataRegisterTransactionFieldEntity>;
        formTranFieldRepository: Repository<TransactionFieldEntity>;
    }): Promise<Record<string, DataRegisterTransactionFieldEntity[]>> {
        const result: Record<string, DataRegisterTransactionFieldEntity[]> = {};
        for (const field of drFields) {
            const { sourceId, configuration } = field ?? {};
            if (!configuration) {
                continue;
            }

            const values = await this._queryDrData({
                dataSourceId,
                dataSourceType,
                sourceId,
                configuration,
                drRepository,
                drFieldRepository,
                formTranFieldRepository,
            });
            result[field.identityId] = values;
        }

        return result;
    }

    private async _queryDrData({
        dataSourceId,
        dataSourceType,
        sourceId,
        configuration,
        drRepository,
        drFieldRepository,
        formTranFieldRepository,
    }: {
        dataSourceId: string;
        dataSourceType: DocumentDatasourceTypeEnum;
        sourceId: string;
        configuration: Record<string, any>;
        drRepository: Repository<DataRegisterTransactionEntity>;
        drFieldRepository: Repository<DataRegisterTransactionFieldEntity>;
        formTranFieldRepository: Repository<TransactionFieldEntity>;
    }) {
        if (!configuration) {
            return;
        }
        const { extraConfigurations, selectedRegisterFieldIds } = configuration;
        const transactions = await drRepository.findBy({ dataRegisterId: sourceId });
        const tranIds = transactions.map((tran) => tran.id);

        const queryBuilder = drFieldRepository.createQueryBuilder().where({ dataRegisterTransactionId: In(tranIds) });

        let selectedFieldIds: string[] = selectedRegisterFieldIds ?? [];
        let dataSourceFields: Record<string, { fieldValue: string; fieldOptionIds: string[] }>[] = [];
        if (extraConfigurations?.length) {
            const dataSourceFieldIds = extraConfigurations.map((config) => config.dataSourceFieldId) ?? [];
            if (dataSourceFieldIds) {
                const tranRepository = dataSourceType === DocumentDatasourceTypeEnum.Form ? formTranFieldRepository : drFieldRepository;
                dataSourceFields = await this._getTransactionFields(dataSourceId, dataSourceFieldIds, tranRepository);
            }

            extraConfigurations.forEach((config) => {
                const registerFieldId = config.registerFieldId;
                selectedFieldIds.push(registerFieldId);
            });
        }
        if (selectedFieldIds?.length) {
            queryBuilder.andWhere({ fieldId: In(selectedRegisterFieldIds) });
        }
        const data = await queryBuilder.getMany();
        if (!extraConfigurations?.length) {
            return data;
        }
        const groupData = groupBy(data, 'dataRegisterTransactionId');
        let result: DataRegisterTransactionFieldEntity[] = [];
        Object.keys(groupData).forEach((tranId) => {
            const tranFields = groupData[tranId];
            let satisfied: boolean = false;

            tranFields?.forEach((field) => {
                const extraConfig = extraConfigurations?.find((c) => field.fieldId === c.registerFieldId);
                if (!extraConfig) {
                    return;
                }
                const sourceValue = dataSourceFields.find((f) => f.id === extraConfig.dataSourceFieldId);

                if (sourceValue?.value?.fieldOptionIds?.length) {
                    satisfied = isEqual(field.fieldOptionIds, sourceValue?.value?.fieldOptionIds);
                } else {
                    satisfied = sourceValue?.value?.fieldValue === field.fieldValue;
                }
                if (satisfied) {
                    return;
                }
            });
            if (satisfied) {
                result = [...result, ...tranFields];
            }
        });
        return result;
    }

    private async _getTransactionFields(
        transactionId: string,
        transactionFieldIds: string[],
        tranFieldRepository: Repository<TransactionFieldEntity> | Repository<DataRegisterTransactionFieldEntity>,
    ): Promise<Record<string, any>[]> {
        let result: Record<string, any>[] = [];
        const data = await tranFieldRepository.findBy({ transactionId: transactionId, fieldId: In(transactionFieldIds) });
        if (!data?.length) {
            return result;
        }
        for (const field of data) {
            const record = { id: field.fieldId, value: { fieldValue: field.fieldValue, fieldOptionIds: field.fieldOptionIds } };
            result.push(record);
        }
        return result;
    }

    private async _getRelationTransactions({
        relationFormIds,
        transactionId,
        repos,
    }: {
        transactionId: string;
        relationFormIds: string[];
        repos: DocumentRepositories;
    }): Promise<TransactionEntity[]> {
        try {
            const uniqFormIds = uniq(relationFormIds || []);
            const relationTransactions = await repos.relationTransactionRepo.find({
                where: [
                    {
                        targetTransactionId: transactionId,
                        originFormId: In(uniqFormIds),
                    },
                    {
                        originTransactionId: transactionId,
                        targetFormId: In(uniqFormIds),
                    },
                ],
            });

            const originTransactionIds = relationTransactions.map((item) => item.originTransactionId);
            const targetTransactionIds = relationTransactions.map((item) => item.targetTransactionId);

            const transactionIds = uniq([...compact(originTransactionIds || []), ...compact(targetTransactionIds || [])]);
            if (!transactionIds?.length) return [];
            const transactions = await repos.transactionRepo.findBy({ id: In(transactionIds) });
            const transactionFields = await repos.transactionFieldRepo.findBy({ transactionId: In(transactionIds) });
            return transactions.map((item) => {
                const _transactionFields = transactionFields.filter((field) => field.transactionId === item.id);
                item.transactionFields = _transactionFields;
                return item;
            });
        } catch (err) {
            this._logger.error(err);
            return [];
        }
    }

    private async _getRelationForms({
        formIds,
        repos,
    }: {
        formIds: string[];
        repos: DocumentRepositories;
    }): Promise<{ id: string; name: string; formFields: FormFieldEntity[] }[]> {
        try {
            if (!formIds?.length) return [];
            const forms = await repos.formRepo.find({
                where: {
                    id: In(formIds),
                },
                select: {
                    id: true,
                    name: true,
                    activeVersionId: true,
                },
            });

            const activeVersionIds = forms.map((f) => f.activeVersionId);
            if (!activeVersionIds?.length) return [];

            const formActiveVersions = await repos.formVersionRepo.find({
                where: {
                    id: In(activeVersionIds),
                },
                relations: {
                    fields: true,
                },
            });

            return forms.map((f) => {
                const formFields = formActiveVersions.find((v) => v.id === f.activeVersionId)?.fields || [];
                return { id: f.id, name: f.name, formFields };
            });
        } catch (err) {
            this._logger.error(err);
            return [];
        }
    }

    private async _getFormCollections(params: {
        documentFields?: DocumentFieldEntity[];
        formVersionId: string;
        formCollectionRepo: Repository<FormCollectionEntity>;
    }) {
        const { formVersionId, formCollectionRepo, documentFields } = params;
        //just get form collections include in document
        const collectionIds = (documentFields || [])
            .filter((field) => field.type === DocumentFieldTypeEnum.Collection)
            .map((field) => field.sourceId);
        const queryCollectionIdentityIds = uniq(compact(collectionIds));

        //get collections that will be used in document
        let formCollections: FormCollectionEntity[] = [];
        if (queryCollectionIdentityIds.length) {
            formCollections = await formCollectionRepo.find({
                where: {
                    identityId: In(queryCollectionIdentityIds),
                    formVersionId: formVersionId,
                },
                relations: {
                    formCollectionItems: true,
                },
            });
        }
        return formCollections;
    }

    private async _getForm(params: { formId: string; formVersionId?: string; formRepo: Repository<FormEntity> }) {
        const { formId, formRepo, formVersionId } = params;
        const form = await this._formWithVersionService.getFormWithVersion({
            formId: formId,
            formVersionId: formVersionId,
            formRepository: formRepo,
            includeStages: true,
            includeCollection: false, //should not include collections
        });

        if (!form?.formVersions?.length) throw new Error('form_version_not_found');
        const formVersion = form.formVersions[0];
        const formFields = formVersion.fields;
        const formStages = formVersion.stages;

        return { formVersion, formFields, formStages };
    }

    private async _getLookupRegisterVersions(params: {
        registerRepo: Repository<DataRegisterEntity>;
        formFields: FormFieldEntity[];
        registerVersionRepo: Repository<DataRegisterVersionEntity>;
        registerFieldRepo: Repository<DataRegisterFieldEntity>;
    }) {
        const { formFields, registerRepo, registerVersionRepo, registerFieldRepo } = params;
        const lookupRegisterIds = compact(
            uniq(formFields.filter((f) => f.type === FormFieldTypeEnum.Lookup).map((f) => f.configuration.targetId)),
        );

        let lookupRegisterVersions: DataRegisterVersionEntity[] = [];
        if (lookupRegisterIds?.length) {
            const lookupRegisters = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                dataRegisterIds: lookupRegisterIds,
                dataRegisterRepository: registerRepo,
                dataRegisterVersionRepository: registerVersionRepo,
                dataRegisterFieldRepository: registerFieldRepo,
            });

            lookupRegisterVersions = lookupRegisters.map((r) => r.dataRegisterVersions[0]);
        }

        return lookupRegisterVersions;
    }

    private async _getCollectionRegisterVersions(params: {
        formCollections: FormCollectionEntity[];
        registerVersionRepo: Repository<DataRegisterVersionEntity>;
    }) {
        const { formCollections, registerVersionRepo } = params;

        const collectionRegisterVersionIds = uniq(formCollections?.map((c) => c.dataRegisterVersionId) ?? []);
        let collectionRegisterVersions: DataRegisterVersionEntity[] = [];

        if (collectionRegisterVersionIds?.length)
            collectionRegisterVersions = await registerVersionRepo.find({
                where: {
                    id: In(collectionRegisterVersionIds),
                },
                relations: {
                    fields: true,
                },
            });

        return collectionRegisterVersions;
    }
}
