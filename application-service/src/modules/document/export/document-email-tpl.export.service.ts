import { Injectable } from '@nestjs/common';
import { JsonTree } from '@react-awesome-query-builder/core';
import { ChildNode, Text } from 'domhandler';
import { compact, entries, groupBy, pick, sortBy, uniq } from 'lodash';
import {
    COMPANY_FIELDS,
    COMPANY_FIELD_ID,
    DATA_REGISTER_NAME_FIELD_ID,
    LOCATION_BERTH_FIELDS,
    LOCATION_COUNTRY_FIELDS,
    LOCATION_PORT_FIELDS,
    LOCATION_TERMINAL_FIELDS,
    TRANSACTION_FIELD_ID,
    VESSEL_FIELDS,
    VESSEL_FIELD_ID,
} from 'src/common/src/constant/field';
import { DataSource } from 'typeorm';
import { BaseHttpService, LoggerService, UtilsService } from '../../../common/src';
import { CONDITION_OVERRIDE_FIELD, CONDITION_VALIDATION_FIELD, DAT<PERSON>_REGISTER_CODE_FIELD_ID } from '../../../constant';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { TransactionFieldEntity } from '../../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { DocumentFieldTypeEnum } from '../../../database/src/shared/enums/document-field-type.enum';
import { DocumentLayoutZoneType } from '../../../database/src/shared/enums/document-layout-zone-type.enum';
import { FormFieldTypeEnum } from '../../../database/src/shared/enums/form-field-type.enum';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { HtmlParser } from '../../../shared/html-parser/html-parser';
import { HtmlTableParser } from '../../../shared/html-parser/html-table-parser';
import { AppConfigService } from '../../../shared/services';
import { getFieldFormat } from '../../../utils';
import { JsonLogicUtils } from '../../form/services/data/util/run-json-logic.util';
import { EXPORT_DOCUMENT_DEFAULT_STYLES, PRE_DEFINE_DOCUMENT_CODE } from '../constant';
import { DocumentContentService } from '../content/document.content.service';
import { DocumentDatasourceDataService } from '../data/document-datasource.data.service';
import { DocumentContentType, DocumentMetadataType, WorkflowStageValueType } from '../type';
import { mockDocumentMetadataType } from '../utils';
import { FormatCollectionTransactionTpl } from './format-collection-tpl.service';
import { FormatDocumentFieldTpl } from './format-document-field-tpl.service';
import { FormatRegisterTpl } from './format-register-tpl.service';
import { FormatRelationTransactionTpl } from './format-relation-transaction-tpl.service';
import { FormatValidationIconTpl } from './format-validation-icon.service';

@Injectable()
export class DocumentEmailTplExportService {
    constructor(
        private readonly logger: LoggerService,
        private readonly _content: DocumentContentService,
        private readonly _htmlParser: HtmlParser,
        private readonly _htmlTableParser: HtmlTableParser,
        private readonly _appConfigService: AppConfigService,
        private readonly _httpService: BaseHttpService,
        private readonly _documentDatasourceDataService: DocumentDatasourceDataService,
        private readonly _fmtDocField: FormatDocumentFieldTpl,
        private readonly _fmtRelationTransaction: FormatRelationTransactionTpl,
        private readonly _fmtCollection: FormatCollectionTransactionTpl,
        private readonly _validationIcon: FormatValidationIconTpl,
        private readonly _formatRegisterTpl: FormatRegisterTpl,
    ) {}

    async toEmailTpl(
        id: string,
        options: {
            accountId: string;
            dataSource: DataSource;
            dataId: string;
            type: 'latest' | 'active' | 'specific';

            documentId?: string;
        },
    ): Promise<{ subject: string; content: string }> {
        const { subject, contents } = await this.parseHtmlTpl(id, options);
        return {
            subject,
            content: this.combineZoneContents(contents),
        };
    }

    public async toQ88PDF({ htmlStr, filename }: { htmlStr: string; filename?: string }): Promise<Buffer | null> {
        try {
            const printerUrl = `${this.printerUrl}/v1/printers/html2pdf`;
            const response = await this._httpService.post<ResponseDto<string>>(printerUrl, {
                html: htmlStr,
                filename,
            });

            const fileBase64String = response.data.data;
            // convert to Buffer
            const buffer = Buffer.from(fileBase64String, 'base64');
            return buffer;
        } catch (error) {
            this.logger.error(`Error generating PDF: ${error}`);
            return null;
        }
    }

    public async exportPDF({
        subject,
        contents,
        configurations = [],
        generatedFileMetaData,
    }: {
        subject: string;
        contents: DocumentContentType;
        configurations?: Array<{ type: DocumentLayoutZoneType; configuration: Record<string, any> }>;
        generatedFileMetaData?: any;
    }) {
        if (!contents.body) return { subject, data: null };

        const headerShowType = configurations.find((z) => z.type === DocumentLayoutZoneType.Header)?.configuration?.showType;
        const footerShowType = configurations.find((z) => z.type === DocumentLayoutZoneType.Footer)?.configuration?.showType;
        const bodyConfigs = configurations.find((z) => z.type === DocumentLayoutZoneType.Body)?.configuration;

        const { body, title, ...rest } = contents;
        const payload = {
            ...rest,
            html: body,
            filename: title,
            headerShowType,
            footerShowType,
            isLandscape: bodyConfigs?.isLandscape === 'true',
        };
        const printerUrl = `${this.printerUrl}/v1/printers/html2pdf`;
        const response = await this._httpService.post<ResponseDto<Buffer>>(printerUrl, payload);
        return { subject, data: response.data.data, generatedFileMetaData };
    }

    public async parseHtmlTpl(
        id: string,
        options: {
            accountId: string;
            dataSource: DataSource;
            dataId: string;
            type: 'latest' | 'active' | 'specific';
            documentId?: string;
            isPrint?: boolean;
        },
        params?: {
            link: string;
        },
    ) {
        const information = await this._getDocumentInformation({
            accountId: options.accountId,
            dataSource: options.dataSource,
            id,
            type: options.type,
            documentId: options.documentId,
        });

        if (!information) {
            this.logger.warn(`document or version is not found: ${id}`);
            return null;
        }

        const { zones, docFields, document: doc } = information;

        const zoneParsers = zones.map((zone) => {
            const parser = this._htmlParser.parse(zone.template);
            const identityIds = this._htmlParser.extractAttributes(parser, {
                attr: PRE_DEFINE_DOCUMENT_CODE.IDENTITY_ID,
                class: PRE_DEFINE_DOCUMENT_CODE.CLASS,
            });

            return { tpl: zone.template, identityIds, parser, type: zone.type };
        });

        const fieldIdentityIds = uniq(compact(zoneParsers.flatMap((r) => r.identityIds)));
        const filteredDocFields = docFields.filter((f) => fieldIdentityIds.includes(f.identityId));

        //get datasource value and schema
        const {
            docFieldValues,
            formFields,
            metadata,
            dataFields,
            formCollections,
            registerVersions,
            relationTransactions,
            relationFormNames,
            collectionAdditionalFields,
        } = await this._documentDatasourceDataService.getDataFromDatasource({
            dataId: options.dataId,
            dataSource: options.dataSource,
            isAccount: options.accountId ? true : false,
            documentFields: filteredDocFields,
            datasourceType: doc.datasourceType,
            accountId: options.accountId,
        });

        //replace value to zone template
        const sectionsInnerHtml = {} as DocumentContentType;
        let subject: string = '';
        for (const zoneParser of zoneParsers) {
            ///handler
            const { identityIds, parser, type } = zoneParser;
            const fieldValues = docFieldValues.filter((val) => (identityIds || []).includes(val.identityId));
            const docFieldElements = this._htmlParser.getTagElements(parser, {
                class: PRE_DEFINE_DOCUMENT_CODE.CLASS,
            });

            const replaces: { from: ChildNode; to: ChildNode }[] = [];

            for (const element of docFieldElements) {
                const identityId = this._htmlParser.getAttributeValue(element, PRE_DEFINE_DOCUMENT_CODE.IDENTITY_ID);
                if (!identityId) continue;

                const fieldValue = fieldValues.find((v) => v.identityId === identityId);

                if (!fieldValue) continue;

                switch (fieldValue.type) {
                    case DocumentFieldTypeEnum.Field: {
                        const field = fieldValue?.values?.[0];
                        let value = field?.fieldValue ?? '';
                        const fieldId = field?.fieldId ?? '';

                        const dataField = dataFields.find((df) => df.fieldId === fieldId);
                        const formField = formFields.find((f) => f.fieldId === fieldId);
                        const fieldConfiguration = formField?.configuration;

                        if (
                            formField?.type === FormFieldTypeEnum.Rollup &&
                            fieldConfiguration?.rollup?.dataType === FormFieldTypeEnum.ValidationResult
                        ) {
                            const newValues = (value ?? '').split(':');
                            newValues.pop();
                            value = newValues.join(':');
                        }
                        const formattedValue = getFieldFormat({
                            fieldType: formField?.type,
                            value: value,
                            pickerType: fieldConfiguration?.pickerType,
                            ...(fieldConfiguration ?? {}),
                        });

                        //#region config label/value
                        const docField = docFields.find((docField) => docField.identityId === identityId);
                        const showLabel = docField?.configuration?.showLabel;
                        const showValue = docField?.configuration?.showValue;
                        const separator = docField?.configuration?.separator;

                        const children: ChildNode[] = [];
                        const styles: string[] = [];
                        if (dataField?.validationValue) {
                            const icon = this._validationIcon.format({
                                code: dataField.validationValue,
                            });
                            children.push(icon);
                            styles.push(...[EXPORT_DOCUMENT_DEFAULT_STYLES.INLINE_BLOCK]);
                        }

                        const fieldValueNode = this._fmtDocField.format({
                            label: formField?.label ?? '',
                            value: formattedValue,
                            showLabel,
                            showValue,
                            separator,
                        });
                        children.push(...(fieldValueNode || []));
                        //#endregion config label/value

                        const newElement = this._htmlParser.createElement({
                            tagName: element.tagName,
                            attribs: {
                                id: element.attribs?.id,
                            },
                            children,
                            styles,
                        });

                        replaces.push({
                            from: element,
                            to: newElement,
                        });

                        break;
                    }
                    case DocumentFieldTypeEnum.Collection: {
                        const documentField = filteredDocFields.find((f) => f.identityId === identityId);
                        if (!documentField) continue;

                        const collectionIdentityId = documentField.sourceId;
                        const collection = formCollections.find((c) => c.identityId === collectionIdentityId);
                        if (!collection) continue;

                        const registerVersion = registerVersions.find((v) => v.id === collection.dataRegisterVersionId);

                        const { collectionDataFields, totalRows, rowToShow } = await this._fmtCollection.handleFilter({
                            documentField,
                            registerVersion,
                            dataFields: dataFields as TransactionFieldEntity[],
                            isAccount: !!options.accountId,
                            dataSource: options.dataSource,
                        });

                        const newElement = this._fmtCollection.format({
                            transactionFields: collectionDataFields,
                            collectionItems: collection.formCollectionItems,
                            collectionIdentityId,
                            documentField,
                            registerVersion,
                            collectionAdditionalFields,
                            totalRows,
                            rowToShow,
                            isPrint: options.isPrint,
                        });

                        if (newElement) {
                            replaces.push({
                                from: element,
                                to: newElement,
                            });
                        } else {
                            const comment = this._htmlParser.createCommentNode(`Collection has empty data to show`);
                            replaces.push({
                                from: element,
                                to: comment,
                            });
                        }

                        break;
                    }
                    case DocumentFieldTypeEnum.RelatedForm: {
                        const documentField = filteredDocFields.find((f) => f.identityId === identityId);
                        if (!documentField) continue;

                        const sourceId = documentField.sourceId;
                        const sourceTransactions = (relationTransactions || []).filter((transaction) => transaction.formId === sourceId);

                        const newElement = this._fmtRelationTransaction.format({
                            documentField,
                            transactions: sourceTransactions,
                            sourceId,
                            forms: relationFormNames,
                        });
                        replaces.push({
                            from: element,
                            to: newElement,
                        });
                        break;
                    }

                    case DocumentFieldTypeEnum.Register: {
                        const newElement = await this._formatRegisterTpl.format({
                            element,
                            documentField: fieldValue,
                            dataSource: options.dataSource,
                            isAccount: options.accountId ? true : false,
                        });
                        replaces.push({
                            from: element,
                            to: newElement,
                        });
                        break;
                    }

                    case DocumentFieldTypeEnum.Metadata: {
                        this._handleMetadataFields({
                            element,
                            filteredDocFields,
                            identityId,
                            metadata,
                            replaces,
                        });
                        break;
                    }
                    case DocumentFieldTypeEnum.NoRegisteredUserTransactionLink: {
                        const documentField = filteredDocFields.find((f) => f.identityId === identityId);
                        this._handleTransactionLink({
                            element,
                            replaces,
                            link: params?.link,
                            documentField,
                        });
                        break;
                    }
                }
            }

            for (const replace of replaces) {
                this._htmlParser.replace(replace.from, replace.to);
            }

            const sectionInnerHtml = this._htmlParser.buildSectionString(parser);

            if (type === DocumentLayoutZoneType.Title) {
                subject = sectionInnerHtml;
            } else {
                sectionsInnerHtml[type] = sectionInnerHtml;
            }
        }

        // TODO: Retrieve metadata of vessel, company, and locations from the template

        // Define field sets for different entities
        const fieldSets = {
            vessel: new Set(Object.values(VESSEL_FIELDS)),
            company: new Set(Object.values(COMPANY_FIELDS)),
            berth: new Set(Object.values(LOCATION_BERTH_FIELDS)),
            port: new Set(Object.values(LOCATION_PORT_FIELDS)),
            terminal: new Set(Object.values(LOCATION_TERMINAL_FIELDS)),
            country: new Set(Object.values(LOCATION_COUNTRY_FIELDS)),
            standard: new Set(
                Object.values({
                    NAME: DATA_REGISTER_NAME_FIELD_ID,
                    CODE: DATA_REGISTER_CODE_FIELD_ID,
                }),
            ),
        };

        // Categorize metadata field IDs based on their lookupFieldId
        const metaDataFieldIds = {
            vessel: [] as string[],
            company: [] as string[],
            berth: [] as string[],
            port: [] as string[],
            terminal: [] as string[],
            country: [] as string[],
            standard: [] as string[],
        };

        // Categorize fields based on lookupFieldId
        docFields.forEach((field) => {
            const lookupId = field?.configuration?.lookupFieldId;
            if (!lookupId) return;

            Object.entries(fieldSets).forEach(([key, fieldSet]) => {
                if (fieldSet.has(lookupId)) metaDataFieldIds[key as keyof typeof metaDataFieldIds].push(field.sourceId);
            });
        });

        // Extract metadata values based on specific field ID
        const extractMetaDataValues = (metaFieldIds: string[], referenceFieldId: string) =>
            metaFieldIds
                .flatMap((id) => {
                    const fieldValues = dataFields.filter((field) => field.fieldId === id);
                    if (!fieldValues.length) return [];

                    return fieldValues.map(
                        (f) =>
                            dataFields.find(
                                (field) =>
                                    field.fieldId === referenceFieldId &&
                                    JSON.stringify(field.fieldOptionIds) === JSON.stringify(f.fieldOptionIds),
                            )?.fieldOptionIds?.[0],
                    );
                })
                .filter(Boolean); // Remove undefined/null values

        // Extract metadata register IDs from fieldOptionIds
        const extractMetaDataRegisterIds = (metaFieldIds: string[]) =>
            metaFieldIds.map((id) => dataFields.find((field) => field.fieldId === id)?.fieldOptionIds?.[0]).filter(Boolean); // Remove undefined/null values

        // Collect metadata from data fields based on fieldOptionIds
        const collectMetaData = (ids: string[]) =>
            ids.reduce(
                (acc, id) => {
                    acc[id] = dataFields
                        .filter((f) => f.fieldOptionIds?.[0] === id)
                        .map((f) => pick(f, 'id', 'fieldId', 'fieldType', 'fieldOptionIds', 'fieldValue'));
                    return acc;
                },
                {} as Record<string, any[]>,
            );

        // Collect metadata where dependFieldId or fieldId matches
        const collectDataRegisterMetaData = (ids: string[]) => {
            return ids.reduce(
                (acc, id) => {
                    acc[id] = dataFields
                        .filter((f: any) => f.dependFieldId === id || f.fieldId === id)
                        .map((f) => pick(f, 'id', 'fieldId', 'fieldType', 'fieldOptionIds', 'fieldValue'));
                    return acc;
                },
                {} as Record<string, any[]>,
            );
        };

        // Retrieve metadata values and context IDs
        const companyIds = extractMetaDataValues(metaDataFieldIds.company, COMPANY_FIELD_ID) || [];
        const vesselIds = extractMetaDataValues(metaDataFieldIds.vessel, VESSEL_FIELD_ID) || [];
        const portContextIds = extractMetaDataRegisterIds(metaDataFieldIds.port) || [];
        const berthContextIds = extractMetaDataRegisterIds(metaDataFieldIds.port) || [];
        const terminalContextIds = extractMetaDataRegisterIds(metaDataFieldIds.terminal) || [];
        const countryContextIds = extractMetaDataRegisterIds(metaDataFieldIds.country) || [];
        const standardContextIds = extractMetaDataRegisterIds(metaDataFieldIds.standard) || [];
        const transactionNumber = dataFields.find((f) => f.fieldId === TRANSACTION_FIELD_ID)?.fieldValue || null;

        // Gather metadata for different entities
        const companyMetaData = collectMetaData(companyIds);
        const vesselMetaData = collectMetaData(vesselIds);
        const portMetaData = collectDataRegisterMetaData(metaDataFieldIds.port);
        const berthMetaData = collectDataRegisterMetaData(metaDataFieldIds.berth);
        const terminalMetaData = collectDataRegisterMetaData(metaDataFieldIds.terminal);
        const countryMetaData = collectDataRegisterMetaData(metaDataFieldIds.country);
        const standardMetaData = collectDataRegisterMetaData(metaDataFieldIds.standard);

        return {
            subject: subject,
            contents: sectionsInnerHtml,
            configurations: (zones || []).map((zone) => ({
                type: zone.type,
                configuration: zone.configuration,
            })),
            generatedFileMetaData: {
                contextIds: {
                    vesselIds,
                    companyIds,
                    berthContextIds,
                    terminalContextIds,
                    portContextIds,
                    countryContextIds,
                    standardContextIds,
                },
                contextMetaData: {
                    vesselMetaData,
                    companyMetaData,
                    portMetaData,
                    transactionNumber,
                    berthMetaData,
                    terminalMetaData,
                    countryMetaData,
                    standardMetaData,
                },
            },
        };
    }

    async mockTemplate(
        id: string,
        options: {
            accountId: string;
            dataSource: DataSource;
            type: 'latest' | 'active' | 'specific';

            documentId?: string;
        },
    ): Promise<{
        subject: string;
        contents: DocumentContentType;
        configurations: Array<{ type: DocumentLayoutZoneType; configuration: Record<string, any> }>;
    }> {
        const information = await this._getDocumentInformation({
            accountId: options.accountId,
            dataSource: options.dataSource,
            id,
            type: options.type,
            documentId: options.documentId,
        });

        if (!information) {
            this.logger.warn(`document or version is not found: ${id}`);
            return null;
        }

        const { zones, docFields, document: doc } = information;

        const zoneParsers = zones.map((zone) => {
            const parser = this._htmlParser.parse(zone.template);
            const identityIds = this._htmlParser.extractAttributes(parser, {
                attr: PRE_DEFINE_DOCUMENT_CODE.IDENTITY_ID,
                class: PRE_DEFINE_DOCUMENT_CODE.CLASS,
            });

            return { tpl: zone.template, identityIds, parser, type: zone.type };
        });

        const fieldIdentityIds = uniq(compact(zoneParsers.flatMap((r) => r.identityIds)));
        const filteredDocFields = docFields.filter((f) => fieldIdentityIds.includes(f.identityId));

        //get datasource value and schema
        const { formFields, formCollections, registerVersions, relationFormNames } =
            await this._documentDatasourceDataService.getSchemaFromDatasource({
                dataSource: options.dataSource,
                isAccount: options.accountId ? true : false,
                documentFields: filteredDocFields,
                datasourceType: doc.datasourceType,
                datasourceId: doc.datasourceId,
            });

        //replace value to zone template
        const sectionsInnerHtml = {} as DocumentContentType;
        let subject: string = '';
        for (const zoneParser of zoneParsers) {
            ///handler
            const { identityIds, parser, type } = zoneParser;
            const docFieldElements = this._htmlParser.getTagElements(parser, {
                class: PRE_DEFINE_DOCUMENT_CODE.CLASS,
            });

            const replaces: { from: ChildNode; to: ChildNode }[] = [];

            for (const element of docFieldElements) {
                const identityId = this._htmlParser.getAttributeValue(element, PRE_DEFINE_DOCUMENT_CODE.IDENTITY_ID);
                if (!identityId) continue;
                const docField = docFields.find((f) => f.identityId === identityId);
                switch (docField.type) {
                    case DocumentFieldTypeEnum.Field: {
                        const formField = formFields.find((f) => f.fieldId === docField.sourceId);

                        //#region config label/value
                        const showLabel = docField?.configuration?.showLabel;
                        const showValue = docField?.configuration?.showValue;
                        const separator = docField?.configuration?.separator;

                        const children: ChildNode[] = [];
                        const styles: string[] = [];
                        let tagName = element.tagName;

                        const fieldValueNode = this._fmtDocField.mock({
                            label: formField?.label ?? '',
                            showLabel,
                            showValue,
                            separator,
                            type: formField?.type,
                        });
                        children.push(...(fieldValueNode || []));
                        //#endregion config label/value

                        const newElement = this._htmlParser.createElement({
                            tagName,
                            attribs: {
                                id: element.attribs?.id,
                            },
                            children,
                            styles,
                        });

                        replaces.push({
                            from: element,
                            to: newElement,
                        });

                        break;
                    }
                    case DocumentFieldTypeEnum.Collection: {
                        const documentField = filteredDocFields.find((f) => f.identityId === identityId);
                        if (!documentField) continue;

                        const collectionIdentityId = documentField.sourceId;
                        const collection = formCollections.find((c) => c.identityId === collectionIdentityId);
                        if (!collection) continue;
                        const registerVersion = registerVersions.find((v) => v.id === collection.dataRegisterVersionId);

                        const newElement = this._fmtCollection.mock({
                            collectionItems: collection.formCollectionItems || [],
                            collectionIdentityId,
                            documentField,
                            registerVersion,
                        });

                        if (newElement) {
                            replaces.push({
                                from: element,
                                to: newElement,
                            });
                        } else {
                            const comment = this._htmlParser.createCommentNode(`Collection has empty data to show`);
                            replaces.push({
                                from: element,
                                to: comment,
                            });
                        }

                        break;
                    }
                    case DocumentFieldTypeEnum.RelatedForm: {
                        const documentField = filteredDocFields.find((f) => f.identityId === identityId);
                        if (!documentField) continue;

                        const sourceId = documentField.sourceId;
                        const newElement = this._fmtRelationTransaction.mock({
                            documentField,
                            sourceId,
                            forms: relationFormNames,
                        });
                        replaces.push({
                            from: element,
                            to: newElement,
                        });
                        break;
                    }

                    case DocumentFieldTypeEnum.Register: {
                        const newElement = await this._formatRegisterTpl.mock({
                            element,
                            documentField: {
                                identityId: docField.identityId,
                                type: docField.type,
                                values: [],
                                configuration: docField.configuration,
                                sourceId: docField.sourceId,
                            },
                            dataSource: options.dataSource,
                            isAccount: options.accountId ? true : false,
                        });
                        replaces.push({
                            from: element,
                            to: newElement,
                        });
                        break;
                    }

                    case DocumentFieldTypeEnum.Metadata: {
                        const metadata = mockDocumentMetadataType();
                        this._handleMetadataFields({
                            element,
                            filteredDocFields,
                            identityId,
                            metadata,
                            replaces,
                        });
                        break;
                    }
                }
            }

            for (const replace of replaces) {
                this._htmlParser.replace(replace.from, replace.to);
            }

            try {
                const sectionInnerHtml = this._htmlParser.buildSectionString(parser);
                if (type === DocumentLayoutZoneType.Title) {
                    subject = sectionInnerHtml;
                } else {
                    sectionsInnerHtml[type] = sectionInnerHtml;
                }
            } catch (e) {
                console.error(e);
            }
        }

        return {
            subject: subject,
            contents: sectionsInnerHtml,
            configurations: (zones || []).map((zone) => ({
                type: zone.type,
                configuration: zone.configuration,
            })),
        };
    }

    private _handleMetadataFields = (params: { identityId; filteredDocFields; metadata; element; replaces }) => {
        const { element, filteredDocFields, identityId, metadata, replaces } = params;
        const documentField = filteredDocFields.find((f) => f.identityId === identityId);
        if (!documentField) return;

        const selectedMetadata = documentField.configuration.selectedMetadataField as DocumentMetadataType;
        let textNode: Text;
        let textValue: string = '';

        if (selectedMetadata === 'workflowStage') {
            const valueType = documentField.configuration.valueType ?? ('current' as WorkflowStageValueType);
            textValue = valueType === 'current' ? metadata['workflowStage']?.current : metadata['workflowStage']?.previous;
        } else {
            textValue = metadata[selectedMetadata] ?? '';
        }

        textNode = this._htmlParser.createTextNode(textValue);

        const newElement = this._htmlParser.createElement({
            tagName: element.tagName,
            attribs: {
                id: element.attribs?.id,
            },
            children: [textNode],
        });

        replaces.push({
            from: element,
            to: newElement,
        });
    };

    private _handleTransactionLink = ({ element, replaces, link, documentField }) => {
        const linkText = documentField?.configuration?.label || 'Transaction Link';
        const linkHref = link;

        // Create an anchor (<a>) element
        const anchorElement = this._htmlParser.createElement({
            tagName: 'a',
            attribs: {
                id: element.attribs?.id, // Retain the existing ID if any
                href: linkHref, // Set the href for the link
                target: '_blank', // Open link in a new tab (optional)
            },
            children: [
                this._htmlParser.createTextNode(linkText), // Set the link's text content
            ],
        });

        // Replace the original element with the newly created anchor element
        replaces.push({
            from: element,
            to: anchorElement,
        });
    };

    private async _getDocumentInformation(options: {
        id: string;
        accountId: string;
        dataSource: DataSource;
        type: 'latest' | 'active' | 'specific';
        documentId?: string;
    }) {
        const { accountId, dataSource, id, type } = options;
        const isAccount = !!accountId;

        const { documentRepo, documentVersionRepo, documentLayoutZoneRepo, documentFieldRepo } = this._content.getRepositories({
            dataSource,
            isAccount,
        });

        const doc = await documentRepo.findOneBy({ id });
        if (!doc) {
            this.logger.warn(`document not found: ${id}`);
            return null;
        }

        let documentVersionId = doc.latestVersionId;
        switch (options.type) {
            case 'active':
                documentVersionId = doc.activeVersionId;
                break;
            case 'specific':
                documentVersionId = options.documentId;
                break;
            case 'latest':
            default:
                documentVersionId = doc.latestVersionId;
        }

        const version = await documentVersionRepo.findOneBy({ id: documentVersionId });
        if (!version) {
            this.logger.warn(`document version not found: ${documentVersionId}`);
            return null;
        }

        const [zones, docFields] = await Promise.all([
            documentLayoutZoneRepo.findBy({ documentVersionId }),
            documentFieldRepo.findBy({ documentVersionId }),
        ]);

        const sortedZones = sortBy(zones, (zone) =>
            [
                DocumentLayoutZoneType.Title,
                DocumentLayoutZoneType.Cc,
                DocumentLayoutZoneType.Header,
                DocumentLayoutZoneType.Body,
                DocumentLayoutZoneType.Footer,
            ].findIndex((type) => zone.type === type),
        );

        return { zones: sortedZones, docFields, document: doc, documentVersion: version };
    }

    public combineZoneContents(zones: DocumentContentType) {
        const sortedZones = sortBy(entries(zones), (zone) =>
            [
                DocumentLayoutZoneType.Title,
                DocumentLayoutZoneType.Cc,
                DocumentLayoutZoneType.Header,
                DocumentLayoutZoneType.Body,
                DocumentLayoutZoneType.Footer,
            ].findIndex((type) => zone[0] === type),
        );
        return sortedZones.map((sortedZone) => sortedZone[1]).join('\n');
    }

    get printerUrl() {
        return this._appConfigService.get('PRINTER_API');
    }

    async toEmailTplWithTransactionLink(
        id: string,
        options: {
            accountId: string;
            dataSource: DataSource;
            dataId: string;
            type: 'latest' | 'active' | 'specific';
            documentId?: string;
        },
        params: {
            link: string;
        },
    ): Promise<{ subject: string; content: string }> {
        const { subject, contents } = await this.parseHtmlTpl(id, options, params);
        return {
            subject,
            content: this.combineZoneContents(contents),
        };
    }
}
