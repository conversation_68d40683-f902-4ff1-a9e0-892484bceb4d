import { BadRequestException, ForbiddenException, Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import { pick } from 'lodash';
import { WidgetMQTTTopicEnum } from 'src/common/src/modules/shared/enums/event-driven/widget-event-topic.enum';
import { FileAutoGeneratedEntity } from 'src/database/src/entities/tenancy/file-auto-generated.tenancy.entity';
import { DataSource } from 'typeorm';
import { ClaimService, MqttService, USER_CLAIMS } from '../../../common/src';
import {
    COMPANY_FIELD_ID,
    COMPANY_FIELDS,
    DATA_REGISTER_CODE_FIELD_ID,
    DATA_REGISTER_NAME_FIELD_ID,
    LOCATION_BERTH_FIELDS,
    LOCATION_COUNTRY_FIELDS,
    LOCATION_PORT_FIELDS,
    LOCATION_TERMINAL_FIELDS,
    TRANSACTION_FIELD_ID,
    VESSEL_FIELD_ID,
    VESSEL_FIELDS,
} from '../../../common/src/constant/field';
import { IStageRoleACL, IStageRoleACLProvider } from '../../../common/src/modules/acl/interfaces/stage-role-acl.interface';
import { FileResponseDto } from '../../../common/src/modules/storage/dtos';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { FormFieldTenancyEntity } from '../../../database/src/entities/tenancy/form-field.tenancy.entity';
import { StageRoleAccessControlTenancyEntity } from '../../../database/src/entities/tenancy/stage-role-access-controls.tenancy.entity';
import { StageRoleTenancyEntity } from '../../../database/src/entities/tenancy/stage-role.tenancy.entity';
import { TransactionEntity } from '../../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FileService } from '../../../shared/services/file-service.service';
import { AvailableFileAction } from '../enums/available-file-action.enum';
import { BaseFileRequest, UploadFileRequest } from '../requests/base.request';
import { DownloadFileRequest } from '../requests/download-file.request';
import { DownloadFileContentResponse } from '../responses/download-file.response';
import { getFolderUploadPath } from '../utils';
import { TransactionFieldEntity } from './../../../database/src/entities/tenancy/transaction-field.tenancy.entity';

interface UploadFileProps extends UploadFileRequest {
    fileName: string;
    fileData: Buffer;
    isTest?: boolean;
    ignoreAcl?: boolean;
}

@Injectable()
export class TransactionFileService {
    constructor(
        private readonly _fileService: FileService,

        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,
        @Inject(IStageRoleACL) private readonly _stageRoleACL: IStageRoleACLProvider,
        @Inject(USER_CLAIMS) private readonly claims: ClaimService,
        private readonly _mqttService: MqttService,
    ) {}

    async uploadFile({
        fileName,
        fileData,
        formId,
        transactionId,
        fieldId,
        roleId,
        ignoreAcl,
        isTest,
    }: UploadFileProps): Promise<FileResponseDto> {
        try {
            if (!ignoreAcl) {
                const actions = await this._verify(
                    {
                        fieldId,
                        roleId,
                        transactionId,
                    },
                    isTest,
                );

                if (!this.claims.accountId || !actions?.includes(AvailableFileAction.UPLOAD)) {
                    throw new ForbiddenException('not_permission_upload_file');
                }
            }

            const folderPath = getFolderUploadPath({ formId, transactionId });
            const result = await this._fileService.uploadFile(fileName, fileData, folderPath);
            await this.createUploadFileResource({ fieldId, transactionId, fileName, filePath: result?.filePath, isTest });
            const widgetTopic = `${this.claims.accountId}/${WidgetMQTTTopicEnum.LIST_FILE_RESOURCE_UPDATE}`;
            const messagePayload = {
                filePath: result.filePath,
                fileName: fileName,
            };
            this._mqttService.publish(widgetTopic, messagePayload, { qos: 1 }).catch((error) => {
                console.error(`Failed to publish MQTT message: ${error}`);
            });

            return result;
        } catch (error) {
            throw new InternalServerErrorException('file_upload_failed');
        }
    }

    async download(query: DownloadFileRequest): Promise<DownloadFileContentResponse> {
        const actions = await this._verify(query, query.isTest);
        if (!actions?.includes(AvailableFileAction.DOWNLOAD)) {
            throw new ForbiddenException('not_permission_download_file');
        }

        const fileName = query.fileName;

        const res = await this._fileService.downloadFile(fileName);
        if (!res) return null;

        return {
            ...res,
            fieldId: query.fieldId,
            fileName,
            transactionId: query.transactionId,
        };
    }
    Ï;

    //check acl on field file
    private async _verify(request: BaseFileRequest, isTest?: boolean): Promise<AvailableFileAction[]> {
        try {
            const transaction = await this._dataSource.getRepository(TransactionEntity).findOne({
                where: {
                    id: request.transactionId,
                },
                withDeleted: !!isTest,
            });

            if (!transaction) throw new BadRequestException('file_is_not_existed');
            const stageRoleACLs = await this._stageRoleACL.getRoleStageACLs({
                request: {
                    formVersionId: transaction.formVersionId,
                    stageRoles: [
                        {
                            roleId: request.roleId,
                            stageId: transaction.stageId,
                        },
                    ],
                },
                stageRoleRepo: this._dataSource.getRepository(StageRoleTenancyEntity),
                stageRoleACLRepo: this._dataSource.getRepository(StageRoleAccessControlTenancyEntity) as any,
                fieldsRepo: this._dataSource.getRepository(FormFieldTenancyEntity) as any,
                ignoreCache: !!isTest,
            });

            const transactionAcl = (stageRoleACLs || []).find(
                (item) => item.roleId === request.roleId && item.stageId === transaction.stageId,
            );

            const fieldAcl = transactionAcl?.field ? transactionAcl.field[request.fieldId] : {};
            const availableActions: AvailableFileAction[] = [];

            if (fieldAcl?.editable?.toString()?.toLowerCase() === 'true') {
                availableActions.push(AvailableFileAction.DOWNLOAD, AvailableFileAction.UPLOAD);
            } else if (fieldAcl?.download?.toString()?.toLowerCase() === 'true') {
                availableActions.push(AvailableFileAction.DOWNLOAD);
            }

            return availableActions;
        } catch (err) {
            return [];
        }
    }

    async createUploadFileResource({
        fieldId,
        transactionId,
        fileName,
        filePath,
        isTest,
    }: {
        fieldId: string;
        transactionId: string;
        fileName: string;
        filePath: string;
        isTest: boolean;
    }) {
        try {
            const fileGenerateRepository = this._dataSource.getRepository(FileAutoGeneratedEntity);
            const transactionRepo = this._dataSource.getRepository(TransactionEntity);
            const formFieldRepo = this._dataSource.getRepository(FormFieldTenancyEntity);
            const transactionFieldRepo = this._dataSource.getRepository(TransactionFieldEntity);

            const transaction = await transactionRepo.findOne({
                where: { id: transactionId },
                withDeleted: !!isTest,
            });
            if (!transaction) throw new BadRequestException('file_is_not_existed');

            const documentFieldConfig = await formFieldRepo.findOne({
                where: { formVersionId: transaction.formVersionId, fieldId },
            });

            if (!documentFieldConfig?.configuration?.context?.source) return [];

            const dataFields = await transactionFieldRepo.find({ where: { transactionId } });
            const source = documentFieldConfig.configuration.context.source;

            const fieldSets = {
                vessel: new Set(Object.values(VESSEL_FIELDS)),
                company: new Set(Object.values(COMPANY_FIELDS)),
                berth: new Set(Object.values(LOCATION_BERTH_FIELDS)),
                port: new Set(Object.values(LOCATION_PORT_FIELDS)),
                terminal: new Set(Object.values(LOCATION_TERMINAL_FIELDS)),
                country: new Set(Object.values(LOCATION_COUNTRY_FIELDS)),
                standard: new Set([DATA_REGISTER_NAME_FIELD_ID, DATA_REGISTER_CODE_FIELD_ID]),
            };

            const metaDataFieldIds: Record<string, string[]> = Object.keys(fieldSets).reduce(
                (acc, key) => {
                    acc[key] = [];
                    return acc;
                },
                {} as Record<string, string[]>,
            );

            source.forEach((fieldId) => {
                const field = dataFields.find((f) => f.fieldId === fieldId);
                if (!field) return;

                Object.entries(fieldSets).forEach(([key, fieldSet]) => {
                    if (fieldSet.has(field.displayAttributeFieldIds?.[0] || fieldId)) {
                        metaDataFieldIds[key].push(fieldId);
                    }
                });
            });

            const extractMetaDataValues = (metaFieldIds: string[], referenceFieldId: string) =>
                metaFieldIds
                    .flatMap((id) =>
                        dataFields
                            .filter((field) => field.fieldId === id)
                            .map(
                                (f) =>
                                    dataFields.find(
                                        (field) =>
                                            field.fieldId === referenceFieldId &&
                                            JSON.stringify(field.fieldOptionIds) === JSON.stringify(f.fieldOptionIds),
                                    )?.fieldOptionIds?.[0],
                            ),
                    )
                    .filter(Boolean);

            const extractMetaDataRegisterIds = (metaFieldIds: string[]) =>
                metaFieldIds.map((id) => dataFields.find((field) => field.fieldId === id)?.fieldOptionIds?.[0]).filter(Boolean);

            const collectMetaData = (ids: string[]) =>
                ids.reduce(
                    (acc, id) => {
                        acc[id] = dataFields
                            .filter((f) => f.fieldOptionIds?.[0] === id)
                            .map((f) => pick(f, 'id', 'fieldId', 'fieldType', 'fieldOptionIds', 'fieldValue'));
                        return acc;
                    },
                    {} as Record<string, any[]>,
                );

            const collectDataRegisterMetaData = (ids: string[]) =>
                ids.reduce(
                    (acc, id) => {
                        acc[id] = dataFields
                            .filter((f) => f.dependFieldId === id || f.fieldId === id)
                            .map((f) => pick(f, 'id', 'fieldId', 'fieldType', 'fieldOptionIds', 'fieldValue'));
                        return acc;
                    },
                    {} as Record<string, any[]>,
                );

            const companyIds = extractMetaDataValues(metaDataFieldIds.company, COMPANY_FIELD_ID);
            const vesselIds = extractMetaDataValues(metaDataFieldIds.vessel, VESSEL_FIELD_ID);
            const portContextIds = extractMetaDataRegisterIds(metaDataFieldIds.port);
            const berthContextIds = extractMetaDataRegisterIds(metaDataFieldIds.berth);
            const terminalContextIds = extractMetaDataRegisterIds(metaDataFieldIds.terminal);
            const countryContextIds = extractMetaDataRegisterIds(metaDataFieldIds.country);
            const standardContextIds = extractMetaDataRegisterIds(metaDataFieldIds.standard);
            const transactionNumber = dataFields.find((f) => f.fieldId === TRANSACTION_FIELD_ID)?.fieldValue || null;

            const companyMetaData = collectMetaData(companyIds);
            const vesselMetaData = collectMetaData(vesselIds);
            const portMetaData = collectDataRegisterMetaData(metaDataFieldIds.port);
            const berthMetaData = collectDataRegisterMetaData(metaDataFieldIds.berth);
            const terminalMetaData = collectDataRegisterMetaData(metaDataFieldIds.terminal);
            const countryMetaData = collectDataRegisterMetaData(metaDataFieldIds.country);
            const standardMetaData = collectDataRegisterMetaData(metaDataFieldIds.standard);

            const contextMetaData = {
                vesselMetaData,
                companyMetaData,
                portMetaData,
                transactionNumber,
                berthMetaData,
                terminalMetaData,
                countryMetaData,
                standardMetaData,
            };

            const contextIds = {
                vesselIds,
                companyIds,
                berthContextIds,
                terminalContextIds,
                portContextIds,
                countryContextIds,
                standardContextIds,
            };

            const fileGenerateEntity = fileGenerateRepository.create({
                fileName,
                dataPath: filePath,
                metaData: { ...contextMetaData, type: 'upload' },
                accountId: '',
                transactionId: transactionId,
                actionId: '',
                ...contextIds,
            });

            await fileGenerateRepository.save(fileGenerateEntity);
        } catch (err) {
            console.error('Error in createUploadFileResource:', err);
            return [];
        }
    }
}
