import { Mo<PERSON><PERSON>, Provider } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { KafkaContext } from '@nestjs/microservices';
import { jwtDecode } from 'jwt-decode';
import { LoggerService } from '../../common/src';
import { CommonEvent } from '../../common/src/modules/shared/domain/events';
import { StorageFactory } from '../../common/src/modules/storage/services/storage-factory.service';
import { StorageModule } from '../../common/src/modules/storage/storage.module';
import { AccountStorageTenancyEntity } from '../../database/src/entities/tenancy/account-storage.tenancy.entity';
import { StorageTypeEnum } from '../../database/src/shared/enums/storage-type.enum';
import { AppConfigService, TenantConnectionService } from '../../shared/services';
import { FileService } from '../../shared/services/file-service.service';
import { AccountAvatarController } from './controllers/account-avatar.controller';
import { FileController } from './controllers/file.controller';
import { TestTransactionFileController } from './controllers/test-transaction-file.controller';
import { TransactionFileController } from './controllers/transaction-file.controller';
import { AccountAvatarService } from './services/account-avatar.service';
import { DownloadFileService } from './services/download-file.service';
import { TransactionFileService } from './services/transaction-file.service';

const sharedProviders = [FileService] as Provider[];
const controllers =
    process.env.MODULE !== 'api' ? [TransactionFileController, FileController, AccountAvatarController, TestTransactionFileController] : [];
@Module({
    imports: [
        StorageModule.register({
            inject: [REQUEST, AppConfigService, LoggerService, TenantConnectionService],
            useFactory: async (
                request,
                appConfig: AppConfigService,
                logService: LoggerService,
                connectionService: TenantConnectionService,
            ) => {
                try {
                    let accountId;
                    if (request.headers?.['x-tenant']) {
                        accountId = request.headers['x-tenant'];
                    } else if (request.headers?.authorization) {
                        try {
                            const user: any = jwtDecode(request.headers.authorization);
                            accountId = request.headers['x-tenant'] ?? user?.accountId;
                        } catch (err) {
                            console.error(err);
                        }
                    } else if (request.data?.aggregateId) {
                        const context = request.context;
                        const kafkaContext = context as KafkaContext;
                        const originalValue = kafkaContext.getMessage()?.value;
                        let parsedValue: CommonEvent;

                        if (Buffer.isBuffer(originalValue)) {
                            parsedValue = JSON.parse(originalValue.toString()) as CommonEvent;
                        } else if (typeof originalValue === 'object') {
                            parsedValue = originalValue as CommonEvent;
                        } else {
                            const value = JSON.stringify(originalValue);
                            parsedValue = JSON.parse(value) as CommonEvent;
                        }
                        accountId = parsedValue?.metadata?.tenantId;
                    }

                    if (!accountId) {
                        return await StorageFactory.createService({
                            logger: logService,
                            defaultStorage: {
                                type: StorageTypeEnum.AzureBlob,
                                connectionString: appConfig.azureBlobStorage.connectionString,
                            },
                            resolver: async (options: Record<string, any>): Promise<Record<string, any> | null> => {
                                return {
                                    type: StorageTypeEnum.AzureBlob,
                                    connectionString: appConfig.azureBlobStorage.connectionString,
                                };
                            },
                        });
                    } else {
                        const dataSource = await connectionService.getTenantConnectionById({ accountId: accountId });
                        const accountStorageRepo = dataSource.getRepository(AccountStorageTenancyEntity);
                        return await StorageFactory.createService({
                            logger: logService,
                            defaultStorage: {
                                type: StorageTypeEnum.AzureBlob,
                                connectionString: appConfig.azureBlobStorage.connectionString,
                            },
                            resolver: async (options: Record<string, any>): Promise<Record<string, any> | null> => {
                                if (!accountId) {
                                    return {
                                        type: StorageTypeEnum.AzureBlob,
                                        connectionString: appConfig.azureBlobStorage.connectionString,
                                    };
                                }

                                try {
                                    const accountStorage = await accountStorageRepo.findOne({
                                        where: {
                                            accountId: options.accountId,
                                        },
                                    });

                                    if (accountStorage) {
                                        return {
                                            type: accountStorage.type,
                                            containerName: options.accountId,
                                            ...accountStorage.configuration,
                                        };
                                    }
                                } catch (err) {
                                    console.error(err);
                                }

                                return {
                                    type: StorageTypeEnum.AzureBlob,
                                    containerName: accountId,
                                    connectionString: appConfig.azureBlobStorage.connectionString,
                                };
                            },
                        });
                    }
                } catch (error) {
                    console.error(error);
                }
            },
        }),
    ],
    controllers: [...controllers],
    providers: [...sharedProviders, TransactionFileService, DownloadFileService, AccountAvatarService],
    exports: [...sharedProviders, TransactionFileService],
})
export class FileModule {}
