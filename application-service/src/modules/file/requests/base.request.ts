import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class BaseFileRequest {
    @ApiProperty()
    @IsString()
    transactionId: string;

    @ApiProperty()
    @IsString()
    fieldId: string;

    @ApiProperty()
    @IsString()
    roleId?: string;
}

export class UploadFileRequest extends BaseFileRequest {
    @IsString()
    @ApiProperty()
    formId: string;
}
