import {
    Body,
    Controller,
    FileTypeValidator,
    <PERSON>,
    Header,
    HttpCode,
    HttpStatus,
    MaxFileSizeValidator,
    ParseFilePipe,
    Post,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    Version,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { FileResponseDto } from '../../../common/src/modules/storage/dtos';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { StreamingInterceptor } from '../../../shared/common/interceptor/streaming-file.intercepter';
import { UploadFileRequest } from '../requests/base.request';
import { DownloadFileRequest } from '../requests/download-file.request';
import { TransactionFileService } from '../services/transaction-file.service';

@Controller({
    path: 'test-transaction-file',
})
@ApiTags('Test transaction file')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class TestTransactionFileController {
    constructor(private readonly _service: TransactionFileService) {}

    //#region GET
    @ApiOperation({ summary: 'Download transaction file' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('/download')
    @Header('Content-Type', 'application/octet-stream')
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    @UseInterceptors(StreamingInterceptor)
    public async download(@Query() query: DownloadFileRequest) {
        return this._service.download({ ...query, isTest: true });
    }
    //#endregion GET

    @ApiOperation({ summary: 'Upload file' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('/upload')
    @UseInterceptors(FileInterceptor('file'))
    @UserTypes(UserType.ACCOUNT_ADMIN, UserType.ACCOUNT)
    public async uploadFile(
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: 100 * 1024 * 1024, message: 'The file size must be smaller than 100MB!' }),
                    new FileTypeValidator({
                        fileType:
                            /image\/(jpeg|png|gif|bmp|tiff|webp|heic|heif)|application\/(pdf|vnd\.openxmlformats-officedocument\.wordprocessingml\.document|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|octet-stream)|message\/rfc822|video\/(mp4|x-msvideo|quicktime|x-ms-wmv|x-matroska)|audio\/(mpeg|wav|aac|ogg|flac)/,
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
        @Body() request: UploadFileRequest,
    ): Promise<FileResponseDto> {
        const data = await this._service.uploadFile({ ...request, fileName: file.originalname, fileData: file.buffer, isTest: true });
        return data;
    }
}
