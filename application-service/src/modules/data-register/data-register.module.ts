import { Module } from '@nestjs/common';
import { EventDrivenModule } from '../../common/src';
import { FormulaService } from '../../common/src/modules/shared/services/formula.service';
import { DataSourceService } from '../../database/src/services/connection-util.service';
import { SharedModule } from '../../shared/shared.module';

import { DataRegisterTransactionController } from './controllers/data-register-transaction.controller';
import { dataRegisterProviders } from './data-register-provider';
import {
    DataRegisterTransactionChangeLogMappingProfile,
    DataRegisterTransactionFieldMappingProfile,
    DataRegisterTransactionMappingProfile,
} from './mapping-profiles';

import { ValidationFormFieldModule } from '../validation/validation.module';
import { DataRegisterTransactionChangeLogTenancyController } from './controllers/data-register-transaction-change-log-tenancy.controller';
import { DataRegisterTransactionChangeLogController } from './controllers/data-register-transaction-change-log.controller';
import { DataRegisterTransactionFieldTenancyController } from './controllers/data-register-transaction-field-tenancy.controller';
import { DataRegisterTransactionFieldController } from './controllers/data-register-transaction-field.controller';
import { DataRegisterTransactionTenancyController } from './controllers/data-register-transaction-tenancy.controller';
import { DataRegisterAdditionalFieldMappingProfile } from './mapping-profiles/data-register-additional-field.profile';
import { DataRegisterCalculateService } from './services/data-register-calculate.service';
import { DataRegisterTransactionChangeLogService } from './services/data-register-transaction-change-log.service';
import { DataRegisterTransactionChangeLogTenancyService } from './services/data-register-transaction-change-log.tenancy.service';
import { DataRegisterTransactionFieldTenancyService } from './services/data-register-transaction-field-tenancy.service';
import { DataRegisterTransactionFieldService } from './services/data-register-transaction-field.service';
import { DataRegisterTransactionService } from './services/data-register-transaction.service';
import { DataRegisterTransactionTenancyService } from './services/data-register-transaction.tenancy.service';
import { DataRegisterPopulateHandler } from './services/data/action.service';
import { CreateExtraDataRegisterService } from './services/data/create-extra-register.service';
import { CompanyDataProviderRegisterDataService } from './services/data/data-provider/company-data-provider-register.data.service';
import { DataProviderRegisterDataService } from './services/data/data-provider/data-provider-register.data.service';
import { VesselDataProviderRegisterDataService } from './services/data/data-provider/vessel-data-provider-register.data.service';
import { DataRegisterAclService } from './services/data/data-register-acl.service';
import { DataRegisterFormFieldService } from './services/data/data-register-form-field.data.service';
import { DataRegisterTransactionChangeLogDataService } from './services/data/data-register-transaction-change-log.data.service';
import { DataRegisterTransactionFieldDataService } from './services/data/data-register-transaction-field.data.service';
import { DataRegisterTransactionIdentifierDataService } from './services/data/data-register-transaction-identifier.data.service';
import { DataRegisterTransactionDataService } from './services/data/data-register-transaction.data.service';
import { DataRegisterRepositoryService } from './services/data/data-register.repository';
import { GenerateSequenceCodeService } from './services/data/generate-squence-code.service';
import { DataRegisterUserRoleService } from './services/data/user-role.data.service';

const externalDataProviders = [
    DataProviderRegisterDataService,
    VesselDataProviderRegisterDataService,
    CompanyDataProviderRegisterDataService,
];

const providers = [
    DataRegisterTransactionMappingProfile,
    DataRegisterTransactionFieldMappingProfile,
    DataRegisterAdditionalFieldMappingProfile,
    DataRegisterTransactionChangeLogMappingProfile,
    DataRegisterTransactionService,
    FormulaService,
    DataRegisterTransactionDataService,
    DataRegisterTransactionTenancyService,
    DataRegisterTransactionFieldService,
    DataRegisterTransactionFieldTenancyService,
    DataRegisterTransactionFieldDataService,
    DataRegisterRepositoryService,
    DataRegisterFormFieldService,
    DataRegisterCalculateService,
    DataRegisterPopulateHandler,
    DataRegisterUserRoleService,
    DataRegisterTransactionChangeLogDataService,
    DataRegisterTransactionChangeLogTenancyService,
    DataRegisterTransactionChangeLogService,
    GenerateSequenceCodeService,
    DataRegisterTransactionIdentifierDataService,
    DataRegisterAclService,
    CreateExtraDataRegisterService,
    ...externalDataProviders,
    ...dataRegisterProviders,
] as const;

const controllers =
    process.env.MODULE !== 'api'
        ? [
              DataRegisterTransactionController,
              DataRegisterTransactionTenancyController,
              DataRegisterTransactionFieldController,
              DataRegisterTransactionFieldTenancyController,
              DataRegisterTransactionChangeLogTenancyController,
              DataRegisterTransactionChangeLogController,
          ]
        : [];

@Module({
    imports: [
        SharedModule,
        ValidationFormFieldModule,
        EventDrivenModule.register({
            inject: [DataSourceService],
            useFactory: (dataSourceService: DataSourceService) => {
                return dataSourceService;
            },
        }),
    ],
    controllers: [...controllers],
    providers: [...providers],
    exports: [...providers],
})
export class DataRegisterModule {}
