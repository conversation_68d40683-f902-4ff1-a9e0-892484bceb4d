import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString } from 'class-validator';

export class FilterTransactionByExternalIdsRequest {
    @AutoMap()
    @ApiProperty()
    @IsArray()
    externals: { externalValue: string; dataRegisterId: string; fieldId: string }[];

    @AutoMap()
    @ApiProperty()
    @IsString()
    dataRegisterVersionId: string;
}
