import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { DataRegisterTypeEnum } from '../../../../database/src/shared/enums/data-register-type.enum';
import { DataRegisterAdditionalFieldDto } from '../data-register-additional-field.dto';

export class EditDataRegisterTransactionFieldRequest {
    @IsUUID()
    @IsOptional()
    @ApiPropertyOptional()
    @AutoMap()
    id?: string;

    @IsUUID()
    @ApiProperty()
    @AutoMap()
    fieldId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    fieldValue?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    fieldLabelValue?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    fieldFormula?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    fieldType?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    type?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @AutoMap()
    defaultValue?: string;

    dataRegisterTransactionId?: string;

    fieldOptionIds?: string[];
}

export class EditDataRegisterTransactionRequest {
    @IsString()
    @ApiProperty()
    @AutoMap()
    dataRegisterId: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional()
    @AutoMap()
    externalId?: string;

    @IsOptional()
    @IsBoolean()
    @ApiPropertyOptional()
    @AutoMap()
    isFilteredExternal?: boolean;

    @IsOptional()
    @IsEnum(DataRegisterTypeEnum)
    @ApiPropertyOptional({
        enum: DataRegisterTypeEnum,
    })
    @AutoMap()
    type?: DataRegisterTypeEnum;

    @ApiProperty({
        type: [EditDataRegisterTransactionFieldRequest],
    })
    @IsArray()
    @AutoMap(() => [EditDataRegisterTransactionFieldRequest])
    transactionFields: EditDataRegisterTransactionFieldRequest[];

    @ApiPropertyOptional()
    @IsOptional()
    @AutoMap(() => [DataRegisterAdditionalFieldDto])
    additionalFields?: DataRegisterAdditionalFieldDto[];

    // Not pass it from frontend
    styleAndOverrideFields?: StyleAndOverrideFields;
}

export type StyleAndOverrideFields = {
    [fieldKey: string]: {
        validationValue?: number;
        override?: OverrideRecordRequest;
        style?: FieldStyleRequest;
    };
};

export type OverrideRecordRequest = {
    fieldId: string;
    dependencyValues: Record<string, any>;
    overrideValue: any;
    createdBy: string;
    createdByUser: string;
    fromValue: number;
    validationValue: number;
    comment: string;
};

export type FieldStyleRequest = {
    fieldId: string;
    style: Record<string, any>;
};
