import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards, Version } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserTypes } from '../../../common/src/decorators/user-type.decorator';
import { UserTypeGuard } from '../../../common/src/guards/user-type.guard';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { UserType } from '../../../database/src/shared/enums/user-type.enum';
import { JwtAuthGuard } from '../../../guards/jwt-auth.guard';
import { BaseController } from '../../../shared/common/base.controller';
import { GetPopulatedFieldValueRequest } from '../../../shared/common/dto/get-populated-field-values.request';
import { LookupQuery } from '../../../shared/common/dto/lookup.query';
import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { ResponseDto } from '../../../shared/common/dto/response.dto';
import { GeneralAutoPopulateService } from '../../../shared/services';
import { CalculateFieldRequest } from '../../form/dtos/requests/calculate-field.request';
import { DataRegisterTransactionDto, DataRegisterTransactionFieldDto } from '../dtos';
import { DataRegisterAdditionalFieldDto } from '../dtos/data-register-additional-field.dto';
import { GetDataRegisterTransactionsQuery } from '../dtos/requests';
import { CheckExitedDataProviderRequest } from '../dtos/requests/check-existed-data-provider.request';
import { EditDataRegisterTransactionRequest } from '../dtos/requests/create-data-register-transaction.request';
import { FilterDataRegisterTransactionRequestDto } from '../dtos/requests/filter-data-register-transaction.request';
import { FilterTransactionByExternalIdsRequest } from '../dtos/requests/filter-transaction-by-external-ids.request';
import { LoadTransactionFieldConfigRequest } from '../dtos/requests/load-transaction-field-config.request';
import { GetDataRegisterLookupTransactionResponse } from '../dtos/respsonses';
import { DataRegisterTransactionOptionDto } from '../dtos/respsonses/data-register-transaction-option.dto';
import { DataRegisterFieldDto, LoadTransactionFieldConfigResponse } from '../dtos/respsonses/load-transaction-field-config.response';
import { DataRegisterTransactionService } from '../services/data-register-transaction.service';

@Controller({
    path: 'data-register-transactions',
})
@ApiTags('Data Register Transaction')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, UserTypeGuard)
export class DataRegisterTransactionController extends BaseController {
    constructor(
        private readonly service: DataRegisterTransactionService,
        private readonly generalAutoPopulateService: GeneralAutoPopulateService,
    ) {
        super();
    }

    //#region GET
    @ApiOperation({ summary: 'Get data register transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get()
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getList(@Query() query: MultipleFilterRequestDto): Promise<PaginationResponseDto<DataRegisterTransactionDto>> {
        const data = await this.service.getList(query);
        return data;
    }

    @ApiOperation({ summary: 'Filter data register transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('filter')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async filter(
        @Body() request: FilterDataRegisterTransactionRequestDto,
    ): Promise<PaginationResponseDto<DataRegisterTransactionDto>> {
        const data = await this.service.getList(request);
        return data;
    }

    //#region GET
    @ApiOperation({ summary: 'Fetch data register transaction list' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('fetch')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async fetchList(
        @Body() query: MultipleFilterRequestDto,
        @Query('includeAdditionalFields') includeAdditionalFields?: string,
    ): Promise<PaginationResponseDto<DataRegisterTransactionDto>> {
        const data = await this.service.getList(query, includeAdditionalFields?.toString() === 'true');
        return data;
    }

    @ApiOperation({
        summary: 'Get data register transactions',
    })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('get-fields')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getDataRegisterTransactions(
        @Query() query: GetDataRegisterTransactionsQuery,
    ): Promise<ResponseDto<DataRegisterTransactionFieldDto[]>> {
        const result = await this.service.getDataRegisterTransactions(query);
        return this.getResponse(true, result);
    }

    @ApiOperation({ summary: 'Get data register transaction options' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('options')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getOptions(@Query() query: MultipleFilterRequestDto): Promise<GetDataRegisterLookupTransactionResponse> {
        const data = await this.service.getOptions(query);
        return data;
    }

    @ApiOperation({ summary: 'Get data register lookup transactions' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('lookup/:id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getLookupTransactions(
        @Param('id') id: string,
        @Query() query: LookupQuery,
    ): Promise<PaginationResponseDto<DataRegisterTransactionOptionDto>> {
        const data = await this.service.lookUpOptions(id, query);
        return data;
    }

    @ApiOperation({ summary: 'Get additional fields' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('additional-fields/:transactionIds')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getAdditionalFields(@Param('transactionIds') transactionIds: string): Promise<ResponseDto<DataRegisterTransactionDto[]>> {
        const data = await this.service.getAdditionalFields(transactionIds.split(','));
        return this.getResponse<DataRegisterTransactionDto[]>(!!data, data, []);
    }

    @ApiOperation({ summary: 'Get register and additional fields' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get('configuration-fields/:id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getConfigurationFields(@Param('id') transactionId: string): Promise<
        ResponseDto<{
            additionalFields: DataRegisterAdditionalFieldDto[];
            registerFields: DataRegisterFieldDto[];
        }>
    > {
        const data = await this.service.getConfigurationFields(transactionId);
        return this.getResponse(!!data, data, []);
    }

    @ApiOperation({ summary: 'Get data register transaction detail' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Get(':id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async get(@Param('id') id: string, @Query('isOriginal') isOriginal?: boolean): Promise<ResponseDto<DataRegisterTransactionDto>> {
        const data = await this.service.get(id, isOriginal);
        return this.getResponse<DataRegisterTransactionDto>(!!data, data, []);
    }
    //#endregion GET

    //#region POST
    @ApiOperation({ summary: 'Create data register transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post()
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async create(@Body() request: EditDataRegisterTransactionRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.create(request, SourceOfChangeType.MANUAL);
        return this.getResponse<boolean>(!!result, !!result, []);
    }

    @ApiOperation({ summary: 'Check external data provider is existing in data register' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('existed-data-provider')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async isDataProviderExist(@Body() request: CheckExitedDataProviderRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.isExistedDataProvider(request);
        return this.getResponse<boolean>(!!result, !!result, []);
    }

    @ApiOperation({ summary: 'Calculate calculation field register' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('calculate')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async calculateField(@Body() request: CalculateFieldRequest) {
        const data = await this.service.calculate(request);
        return this.getResponse(true, data, null);
    }

    @ApiOperation({ summary: 'Filter transactions by external ids' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('filter-external-ids')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async filterTransactionsByExternalIds(@Body() request: FilterTransactionByExternalIdsRequest): Promise<
        {
            id: string;
            dataRegisterId: string;
            externalId?: string;
            fieldId?: string;
        }[]
    > {
        return this.service.filterTransactionsByExternalIds(request);
    }
    //#endregion POST

    @ApiOperation({ summary: 'Get data register transaction options by POST method' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('options')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async getDrOptions(@Body() query: MultipleFilterRequestDto): Promise<PaginationResponseDto<DataRegisterTransactionOptionDto>> {
        const data = await this.service.getOptions(query);
        return data;
    }

    @ApiOperation({ summary: 'General auto populate Data register field from transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('general-auto-populate')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async generalAutoPopulateField(@Body() request: GetPopulatedFieldValueRequest): Promise<ResponseDto<Record<string, any>>> {
        const data = await this.generalAutoPopulateService.getPopulatedDataRegisterFieldValues(request);
        return this.getResponse(true, data, null);
    }

    @ApiOperation({ summary: 'Get data register transaction form fields' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Post('field-configs')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async loadFormFields(
        @Body() request: LoadTransactionFieldConfigRequest,
    ): Promise<ResponseDto<LoadTransactionFieldConfigResponse[]>> {
        const data = await this.service.loadFormFields(request);
        return this.getResponse(true, data, null);
    }

    //#region PUT
    @ApiOperation({ summary: 'Update data register transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Put(':id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async update(@Param('id') id: string, @Body() request: EditDataRegisterTransactionRequest): Promise<ResponseDto<boolean>> {
        const result = await this.service.update(id, request, SourceOfChangeType.MANUAL);
        return this.getResponse<boolean>(!!result, result, []);
    }
    //#endregion PUT

    //#region Delete
    @ApiOperation({ summary: 'Delete data register transaction' })
    @Version('1')
    @HttpCode(HttpStatus.OK)
    @Delete(':id')
    @UserTypes(UserType.MDS_ADMIN, UserType.MDS_SUPPORT)
    public async delete(@Param('id') id: string): Promise<ResponseDto<boolean>> {
        const result = await this.service.delete(id);
        return this.getResponse<boolean>(!!result, result, []);
    }
    //#endregion Delete
}
