import { Inject } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';

import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { DataRegisterTransactionDto, DataRegisterTransactionFieldDto } from '../dtos';
import { EditDataRegisterTransactionRequest } from '../dtos/requests/create-data-register-transaction.request';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterAdditionalFieldEntity } from '../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { DataRegisterAutoPopulateContextEntity } from '../../../database/src/entities/public/data-register-auto-populate-contexts.public.entity';
import { DataRegisterFieldEntity } from '../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldOverrideEntity } from '../../../database/src/entities/public/data-register-transaction-field-override.public.entity';
import { DataRegisterTransactionFieldStyleEntity } from '../../../database/src/entities/public/data-register-transaction-field-style.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../../database/src/entities/public/data-registers.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { LookupQuery } from '../../../shared/common/dto/lookup.query';
import { CalculateFieldRequest } from '../../form/dtos/requests/calculate-field.request';
import { DataRegisterAdditionalFieldDto } from '../dtos/data-register-additional-field.dto';
import { GetDataRegisterTransactionsQuery } from '../dtos/requests';
import { CheckExitedDataProviderRequest } from '../dtos/requests/check-existed-data-provider.request';
import { FilterDataRegisterTransactionRequestDto } from '../dtos/requests/filter-data-register-transaction.request';
import { FilterTransactionByExternalIdsRequest } from '../dtos/requests/filter-transaction-by-external-ids.request';
import { LoadTransactionFieldConfigRequest } from '../dtos/requests/load-transaction-field-config.request';
import { GetDataRegisterLookupTransactionResponse } from '../dtos/respsonses';
import { DataRegisterTransactionOptionDto } from '../dtos/respsonses/data-register-transaction-option.dto';
import { DataRegisterFieldDto, LoadTransactionFieldConfigResponse } from '../dtos/respsonses/load-transaction-field-config.response';
import { DataRegisterCalculateService } from './data-register-calculate.service';
import { DataRegisterFormFieldService } from './data/data-register-form-field.data.service';
import { DataRegisterTransactionDataService } from './data/data-register-transaction.data.service';
export class DataRegisterTransactionService {
    constructor(
        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION)
        private readonly dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD)
        private readonly dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_REPOSITORY)
        private readonly dataRegisterFieldRepository: Repository<DataRegisterFieldEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_REPOSITORY)
        private readonly dataRegisterVersionRepository: Repository<DataRegisterVersionEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_REPOSITORY)
        private readonly dataRegisterRepository: Repository<DataRegisterEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_REPOSITORY)
        private readonly additionalFieldRepo: Repository<DataRegisterAdditionalFieldEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_AUTO_POPULATE_CONTEXT_REPOSITORY)
        private readonly autoPopulateContextRepo: Repository<DataRegisterAutoPopulateContextEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_STYLE_REPOSITORY)
        private readonly transactionFieldStyleRepo: Repository<DataRegisterTransactionFieldStyleEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_OVERRIDE_REPOSITORY)
        private readonly transactionFieldOverrideRepo: Repository<DataRegisterTransactionFieldOverrideEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTINGS_REPOSITORY)
        private readonly generalAutoPopulateSettingRepository: Repository<GeneralAutoPopulateSettingEntity>,

        @Inject(PROVIDER_KEYS.DATA_SOURCE)
        private readonly _dataSource: DataSource,

        private readonly _dataService: DataRegisterTransactionDataService,
        private readonly _formFieldService: DataRegisterFormFieldService,
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        private readonly _calcService: DataRegisterCalculateService,

        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,
    ) {}

    //#region GET
    public async getList(
        query: FilterDataRegisterTransactionRequestDto,
        includeAdditionalFields?: boolean,
    ): Promise<PaginationResponseDto<DataRegisterTransactionDto>> {
        const result = await this._dataService.getList({
            query,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepo: this.dataRegisterTransactionFieldRepository,
            includeAdditionalFields,
        });

        return result;
    }

    public async getOptions(query: MultipleFilterRequestDto): Promise<GetDataRegisterLookupTransactionResponse> {
        const result = await this._dataService.getOptions({
            query,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });

        return result;
    }

    public async loadFormFields(request: LoadTransactionFieldConfigRequest): Promise<LoadTransactionFieldConfigResponse[]> {
        const result = await this._formFieldService.loadConfigs(request, {
            accountId: this._claims.accountId,
            dataSource: this._dataSource,
        });

        return result;
    }

    public async lookUpOptions(id: string, query: LookupQuery): Promise<PaginationResponseDto<DataRegisterTransactionOptionDto>> {
        return await this._dataService.lookUpOptions({
            id,
            query,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });
    }

    public async getAdditionalFields(transactionIds: string[]): Promise<DataRegisterTransactionDto[]> {
        return await this._dataService.getAdditionalFields({
            transactionIds,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
        });
    }

    public async getConfigurationFields(id: string): Promise<{
        additionalFields: DataRegisterAdditionalFieldDto[];
        registerFields: DataRegisterFieldDto[];
    }> {
        const result = await this._dataService.getRegisterConfigurationFields({
            id,
            registerTransactionRepo: this.dataRegisterTransactionRepository,
            registerRepo: this.dataRegisterRepository,
            registerFieldRepo: this.dataRegisterFieldRepository,
            additionalFieldRepo: this.additionalFieldRepo,
        });

        return {
            additionalFields: result?.additionalFields || [],
            registerFields: result?.registerFields || [],
        };
    }

    public async get(id: string, isOriginal?: boolean): Promise<DataRegisterTransactionDto> {
        return await this._dataService.get({
            id,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterTransactionFieldOverrideRepository: this.transactionFieldOverrideRepo,
            isOriginal,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
        });
    }
    //#endregion GET

    //#region POST
    public async create(request: EditDataRegisterTransactionRequest, sourceOfChange: SourceOfChangeType): Promise<boolean | string> {
        const result = await this._dataService.create({
            request,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterAdditionalFieldRepository: this.additionalFieldRepo,
            dataRegisterAutoPopulateContextRepository: this.autoPopulateContextRepo,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
            accountId: this._claims.accountId,
            dataSource: this._dataSource,
            sourceOfChange,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            transactionFieldOverrideRepository: this.transactionFieldOverrideRepo,
            generalAutoPopulateSettingRepo: this.generalAutoPopulateSettingRepository,
        });
        return result;
    }

    public async isExistedDataProvider(request: CheckExitedDataProviderRequest): Promise<boolean> {
        const externalId = request.externalId;
        if (!externalId) {
            return false;
        }

        return await this._dataService.isExistedDataProvider({
            request,
            transactionRepo: this.dataRegisterTransactionRepository,
        });
    }

    public async filterTransactionsByExternalIds(request: FilterTransactionByExternalIdsRequest): Promise<
        {
            id: string;
            dataRegisterId: string;
            externalId?: string;
            fieldId?: string;
        }[]
    > {
        try {
            const result = await this._dataService.filterTransactionsByExternalIds({
                request,
                transactionRepo: this.dataRegisterTransactionRepository,
                transactionFieldRepo: this.dataRegisterTransactionFieldRepository,
                generalAutoPopulateRepo: this.generalAutoPopulateSettingRepository,
                dataRegisterRepo: this.dataRegisterRepository,
            });

            return result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async calculate(request: CalculateFieldRequest) {
        return this._calcService.calculateFields({
            request,
            dataSource: this._dataSource,
            accountId: this._claims.accountId,
        });
    }
    //#endregion POST

    //#region PUT
    public async update(id: string, request: EditDataRegisterTransactionRequest, sourceOfChange: SourceOfChangeType) {
        const result = await this._dataService.update({
            id,
            request,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterAdditionalFieldRepository: this.additionalFieldRepo,
            dataRegisterAutoPopulateContextRepository: this.autoPopulateContextRepo,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
            transactionFieldOverrideRepository: this.transactionFieldOverrideRepo,
            accountId: this._claims.accountId,
            dataSource: this._dataSource,
            sourceOfChange,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });

        return result;
    }
    //#endregion PUT

    //#region DELETE
    public async delete(id: string) {
        const result = await this._dataService.delete({
            id,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
        });

        return result;
    }
    //#endregion DELETE

    public async getDataRegisterTransactions(query: GetDataRegisterTransactionsQuery): Promise<DataRegisterTransactionFieldDto[]> {
        const result = await this._dataService.getDataRegisterTransactions({
            query,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
        });
        return result;
    }
}
