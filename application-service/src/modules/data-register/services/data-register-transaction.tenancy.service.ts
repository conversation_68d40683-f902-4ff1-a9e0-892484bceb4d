import { Inject, Injectable } from '@nestjs/common';
import { DataSource, In, Repository } from 'typeorm';

import { MultipleFilterRequestDto } from '../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../shared/common/dto/pagination-response.dto';
import { DataRegisterTransactionDto, DataRegisterTransactionFieldDto } from '../dtos';
import { EditDataRegisterTransactionRequest } from '../dtos/requests/create-data-register-transaction.request';

import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ClaimService, LoggerService, USER_CLAIMS } from '../../../common/src';
import { PROVIDER_KEYS } from '../../../database/src/constants/providers';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterAutoPopulateContextTenancyEntity } from '../../../database/src/entities/tenancy/data-register-auto-populate-contexts.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldOverrideTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field-override.tenancy.entity';
import { DataRegisterTransactionFieldStyleTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field-style.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { SourceOfChangeType } from '../../../database/src/shared/enums/change-log.type.enum';
import { LookupQuery } from '../../../shared/common/dto/lookup.query';
import { TenantConnectionService } from '../../../shared/services';
import { CalculateFieldRequest } from '../../form/dtos/requests/calculate-field.request';
import { DataRegisterAdditionalFieldDto } from '../dtos/data-register-additional-field.dto';
import { CheckExitedDataProviderRequest } from '../dtos/requests/check-existed-data-provider.request';
import { FilterDataRegisterTransactionRequestDto } from '../dtos/requests/filter-data-register-transaction.request';
import { FilterTransactionByExternalIdsRequest } from '../dtos/requests/filter-transaction-by-external-ids.request';
import { LoadTransactionFieldConfigRequest } from '../dtos/requests/load-transaction-field-config.request';
import { DataRegisterTransactionOptionDto } from '../dtos/respsonses/data-register-transaction-option.dto';
import { DataRegisterFieldDto, LoadTransactionFieldConfigResponse } from '../dtos/respsonses/load-transaction-field-config.response';
import { DataRegisterCalculateService } from './data-register-calculate.service';
import { DataRegisterFormFieldService } from './data/data-register-form-field.data.service';
import { DataRegisterTransactionDataService } from './data/data-register-transaction.data.service';

@Injectable()
export class DataRegisterTransactionTenancyService {
    constructor(
        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_TENANCY)
        private readonly dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_VERSION_TENANCY_REPOSITORY)
        private readonly dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_FIELD_TENANCY_REPOSITORY)
        private readonly dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TENANCY_REPOSITORY)
        private readonly dataRegisterRepository: Repository<DataRegisterTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_TENANCY)
        private readonly transactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_ADDITIONAL_FIELD_TENANCY_REPOSITORY)
        private readonly additionalFieldRepo: Repository<DataRegisterAdditionalFieldTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_AUTO_POPULATE_CONTEXT_TENANCY_REPOSITORY)
        private readonly autoPopulateContextRepo: Repository<DataRegisterAutoPopulateContextTenancyEntity>,

        @Inject(PROVIDER_KEYS.GENERAL_AUTO_POPULATE_SETTING_TENANCY_REPOSITORY)
        private readonly generalAutoPopulateSettingRepository: Repository<GeneralAutoPopulateSettingTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_STYLE_TENANCY_REPOSITORY)
        private readonly transactionFieldStyleRepo: Repository<DataRegisterTransactionFieldStyleTenancyEntity>,

        @Inject(PROVIDER_KEYS.DATA_REGISTER_TRANSACTION_FIELD_OVERRIDE_TENANCY_REPOSITORY)
        private readonly transactionFieldOverrideRepo: Repository<DataRegisterTransactionFieldOverrideTenancyEntity>,

        private readonly _dataService: DataRegisterTransactionDataService,
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _dataSource: DataSource,

        private readonly _formFieldService: DataRegisterFormFieldService,
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,

        private readonly _calcService: DataRegisterCalculateService,

        private readonly _connectionService: TenantConnectionService,

        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,
    ) {}

    //#region GET
    public async getList(
        query: FilterDataRegisterTransactionRequestDto,
        includeAdditionalFields?: boolean,
    ): Promise<PaginationResponseDto<DataRegisterTransactionDto>> {
        const result = await this._dataService.getList({
            query,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepo: this.transactionFieldRepo,
            includeAdditionalFields,
        });

        return result;
    }

    public async getWorkspaceContext(query: any): Promise<any> {
        const result = await this._dataService.getWorkspaceContext({
            query,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });

        return result;
    }

    public async getOptions(query: MultipleFilterRequestDto): Promise<PaginationResponseDto<DataRegisterTransactionOptionDto>> {
        const result = await this._dataService.getOptions({
            query,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });

        return result;
    }

    public async lookUpOptions(id: string, query: LookupQuery): Promise<PaginationResponseDto<DataRegisterTransactionOptionDto>> {
        const accountData = await this._dataService.lookUpOptions({
            id,
            query,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });

        //TODO: if register is vessel/company/location, account register < limit then add search MDS
        // if (DataProviderTypes.includes(query?.type)) {
        //     const searchTerm = (query.filters || []).find((filter) => filter.field === 'searchTerm');
        //     const text = searchTerm?.value?.toString()?.trim() || '';
        //     if (text) {
        //         const mdsVessels = await this._dataProviderService.searchMDSCatalogue({
        //             text: '',
        //             type: query.type,
        //         });
        //     }
        // }
        return accountData;
    }

    public async getAdditionalFields(transactionIds: string[]): Promise<DataRegisterTransactionDto[]> {
        return await this._dataService.getAdditionalFields({
            transactionIds,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
        });
    }

    public async getConfigurationFields(id: string): Promise<{
        additionalFields: DataRegisterAdditionalFieldDto[];
        registerFields: DataRegisterFieldDto[];
    }> {
        const result = await this._dataService.getRegisterConfigurationFields({
            id,
            registerTransactionRepo: this.dataRegisterTransactionRepository,
            registerRepo: this.dataRegisterRepository,
            registerFieldRepo: this.dataRegisterFieldRepository,
            additionalFieldRepo: this.additionalFieldRepo,
        });

        return {
            additionalFields: result?.additionalFields || [],
            registerFields: result?.registerFields || [],
        };
    }

    public async get(id: string, isOriginal?: boolean): Promise<DataRegisterTransactionDto> {
        return await this._dataService.get({
            id,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldOverrideRepository: this.transactionFieldOverrideRepo,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            isOriginal,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
        });
    }

    //#endregion GET

    //#region POST
    public async create(request: EditDataRegisterTransactionRequest, sourceOfChange: SourceOfChangeType): Promise<boolean | string> {
        const result = await this._dataService.create({
            request,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterAdditionalFieldRepository: this.additionalFieldRepo,
            dataRegisterAutoPopulateContextRepository: this.autoPopulateContextRepo,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
            accountId: this._claims.accountId,
            dataSource: this._dataSource,
            sourceOfChange,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            transactionFieldOverrideRepository: this.transactionFieldOverrideRepo,
            generalAutoPopulateSettingRepo: this.generalAutoPopulateSettingRepository,
        });
        return result;
    }

    public async isExistedDataProvider(request: CheckExitedDataProviderRequest): Promise<boolean> {
        const externalId = request.externalId;
        if (!externalId) {
            return false;
        }

        return await this._dataService.isExistedDataProvider({
            request,
            transactionRepo: this.dataRegisterTransactionRepository,
        });
    }

    public async loadFormFields(request: LoadTransactionFieldConfigRequest): Promise<LoadTransactionFieldConfigResponse[]> {
        const result = await this._formFieldService.loadConfigs(request, {
            accountId: this._claims.accountId,
            dataSource: this._dataSource,
        });

        return result;
    }

    public async calculate(request: CalculateFieldRequest) {
        return this._calcService.calculateFields({
            request,
            dataSource: this._dataSource,
            accountId: this._claims.accountId,
        });
    }

    public async filterTransactionsByExternalIds(request: FilterTransactionByExternalIdsRequest): Promise<
        {
            id: string;
            dataRegisterId: string;
            externalId?: string;
            fieldId?: string;
        }[]
    > {
        try {
            const result = await this._dataService.filterTransactionsByExternalIds({
                request,
                transactionRepo: this.dataRegisterTransactionRepository,
                transactionFieldRepo: this.dataRegisterTransactionFieldRepository,
                generalAutoPopulateRepo: this.generalAutoPopulateSettingRepository,
                dataRegisterRepo: this.dataRegisterRepository,
            });

            return result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async refreshDataRegisterTransaction(dataRegisterId: string) {
        return this._dataService.refreshDataRegisterTransaction({
            dataRegisterId,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterRepository: this.dataRegisterRepository,
            generalAutoPopulateSettingRepository: this.generalAutoPopulateSettingRepository,
        });
    }

    //#endregion POST

    //#region PUT
    public async update(id: string, request: EditDataRegisterTransactionRequest, sourceOfChange: SourceOfChangeType) {
        const result = await this._dataService.update({
            id,
            request,
            dataRegisterRepository: this.dataRegisterRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterAdditionalFieldRepository: this.additionalFieldRepo,
            dataRegisterAutoPopulateContextRepository: this.autoPopulateContextRepo,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
            transactionFieldOverrideRepository: this.transactionFieldOverrideRepo,
            accountId: this._claims.accountId,
            dataSource: this._dataSource,
            sourceOfChange,
            dataRegisterVersionRepository: this.dataRegisterVersionRepository,
            dataRegisterFieldRepository: this.dataRegisterFieldRepository,
        });

        return result;
    }

    public async updateRollupValidation(id: string, request: EditDataRegisterTransactionRequest, sourceOfChange: SourceOfChangeType) {
        const result = await this._dataService.updateRollupValidation({
            id,
            request,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldStyleRepository: this.transactionFieldStyleRepo,
        });

        return result;
    }
    //#endregion PUT

    //#region DELETE
    public async delete(id: string) {
        const result = await this._dataService.delete({
            id,
            dataRegisterTransactionRepository: this.dataRegisterTransactionRepository,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
        });

        return result;
    }
    //#endregion DELETE

    public async getDataRegisterTransactions(query: MultipleFilterRequestDto): Promise<DataRegisterTransactionFieldDto[]> {
        const result = await this._dataService.getDataRegisterTransactions({
            query,
            dataRegisterTransactionFieldRepository: this.dataRegisterTransactionFieldRepository,
        });
        return result;
    }

    public async getFieldsByTransactionIds(transactionIds: string[], accountId: string) {
        const dataSource = await this._connectionService.getTenantConnection({ schema: accountId });
        const transactionFieldRepository = dataSource.getRepository(DataRegisterTransactionFieldTenancyEntity);
        const data = await transactionFieldRepository.findBy({ dataRegisterTransactionId: In(transactionIds) });
        return data;
    }
}
