import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Inject, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { CoreConfig, JsonItem, JsonTree, Utils as QbUtils } from '@react-awesome-query-builder/core';
import { LogicEngine } from 'json-logic-engine';
import * as _ from 'lodash';
import { Brackets, DataSource, In, IsNull, Not, Repository, SelectQueryBuilder } from 'typeorm';
import {
    BaseHttpService,
    ClaimService,
    ConfigService,
    LoggerService,
    MqttService,
    QueryBuilderService,
    TENANT_CONNECTION,
    USER_CLAIMS,
    UtilsService,
} from '../../../../common/src';
import { DataFieldDto } from '../../../../common/src/modules/shared/dtos/data-field.dto';
import { FilterOptionDto } from '../../../../common/src/modules/shared/dtos/filter-option.dto';
import { FormulaDto } from '../../../../common/src/modules/shared/dtos/formula.dto';
import { OrderOptionDto } from '../../../../common/src/modules/shared/dtos/order-option.dto';
import { DurationFormatEnum } from '../../../../common/src/modules/shared/enums/duration-format-type.enum';
import { OperatorType } from '../../../../common/src/modules/shared/enums/operator.enum';
import { OrderType } from '../../../../common/src/modules/shared/enums/order.enum';
import { PREFIX } from '../../../../common/src/modules/shared/enums/prefix.enum';
import { CalculationFormula, FormulaSettingType, VariableMapping } from '../../../../common/src/modules/shared/formula-calculator/types';
import { FormulaService } from '../../../../common/src/modules/shared/services/formula.service';
import { Duration } from '../../../../common/src/modules/shared/types/duration';
import { DATA_REGISTER_CODE_FIELD_ID, PAGINATION_EMPTY_DATA } from '../../../../constant';
import { DataProviderTypes } from '../../../../database/src/constants/data-providers';
import { DataRegisterAdditionalFieldEntity } from '../../../../database/src/entities/public/data-register-additional-fields.public.entity';
import { DataRegisterAutoPopulateContextEntity } from '../../../../database/src/entities/public/data-register-auto-populate-contexts.public.entity';
import { DataRegisterFieldEntity } from '../../../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterEntity } from '../../../../database/src/entities/public/data-registers.public.entity';
import { DataRegisterAdditionalFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-additional-fields.tenancy.entity';
import { DataRegisterAutoPopulateContextTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-auto-populate-contexts.tenancy.entity';
import { DataRegisterFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTransactionFieldTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field.tenancy.entity';
import { DataRegisterTransactionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction.tenancy.entity';
import { DataRegisterVersionTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-versions.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormFieldTypeEnum } from '../../../../database/src/shared/enums/form-field-type.enum';
import { LookupQuery } from '../../../../shared/common/dto/lookup.query';
import { MultipleFilterRequestDto } from '../../../../shared/common/dto/multiple-filter-request.dto';
import { PaginationResponseDto } from '../../../../shared/common/dto/pagination-response.dto';
import { LookupFieldDataSource } from '../../../../shared/enums/lookup-field-datasource.enum';
import { TransactionQueryBuilderService } from '../../../../shared/services/transaction-query-builder.service';
import { DataRegisterTransactionDto, DataRegisterTransactionFieldDto } from '../../dtos';
import { GetDataRegisterTransactionsQuery } from '../../dtos/requests';
import {
    EditDataRegisterTransactionFieldRequest,
    EditDataRegisterTransactionRequest,
    StyleAndOverrideFields,
} from '../../dtos/requests/create-data-register-transaction.request';
import { DataRegisterTransactionOptionDto } from '../../dtos/respsonses/data-register-transaction-option.dto';
import { DataProviderRegisterDataService } from './data-provider/data-provider-register.data.service';

import { HttpService } from '@nestjs/axios';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { uniq } from 'lodash';
import { checkRowConditionFilters } from 'src/utils/check-row-condition-filters';
import { validate as isValidUUID, v4 as uuid, validate } from 'uuid';
import { RequestContextService } from '../../../../common/src/application/context/AppRequestContext';
import {
    EXTERNAL_DATA_SOURCE__SEPARATE_MARK,
    EXTERNAL_DATA_SOURCE_MAPPING,
    EXTERNAL_DATA_SOURCE_TYPE,
    EXTERNAL_DATA_SOURCE_TYPES,
} from '../../../../common/src/constant/field';
import { EventDrivenService } from '../../../../common/src/modules/event-driven/event-driven.service';
import { CommonTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/common-topic.enum';
import { DataPopulateEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/data-populate-event.enum';
import { DataRegisterEventEnum } from '../../../../common/src/modules/shared/enums/event-driven/data-register-event';
import { DataRegisterTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/data-register-topic.enum';
import { WidgetMQTTTopicEnum } from '../../../../common/src/modules/shared/enums/event-driven/widget-event-topic.enum';
import { MMDDYYYY, TRANSPORT_DATE_TIME_FORMAT } from '../../../../constant/date';
import { TENANT_HEADER } from '../../../../database/src/constants/database-option.constant';
import { OBJECT_SELECTABLE_FIELD_TYPES, SELECTABLE_FIELD_TYPES } from '../../../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../../../database/src/constants/providers';
import { AutomationRuleEntity } from '../../../../database/src/entities/public/automation-rule.public.entity';
import { AutomationVersionEntity } from '../../../../database/src/entities/public/automation-version.public.entity';
import { DataRegisterTransactionFieldOverrideEntity } from '../../../../database/src/entities/public/data-register-transaction-field-override.public.entity';
import { DataRegisterTransactionFieldStyleEntity } from '../../../../database/src/entities/public/data-register-transaction-field-style.public.entity';
import { DataRegisterVersionEntity } from '../../../../database/src/entities/public/data-register-versions.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { AutomationRuleTenancyEntity } from '../../../../database/src/entities/tenancy/automation-rule.tenancy.entity';
import { AutomationVersionTenancyEntity } from '../../../../database/src/entities/tenancy/automation-version.tenancy.entity';
import { DataRegisterTransactionFieldOverrideTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field-override.tenancy.entity';
import { DataRegisterTransactionFieldStyleTenancyEntity } from '../../../../database/src/entities/tenancy/data-register-transaction-field-style.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { AutoPopulateBuilderTypeEnum } from '../../../../database/src/shared/enums/ap-builder-type.enum';
import { AutomationVersionStatus } from '../../../../database/src/shared/enums/automation.enum';
import { SourceOfChangeType } from '../../../../database/src/shared/enums/change-log.type.enum';
import { DataRegisterTypeEnum } from '../../../../database/src/shared/enums/data-register-type.enum';
import { LookupDataset } from '../../../../database/src/shared/enums/lookup-datasource.enum';
import { OverrideStatusEnum } from '../../../../database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from '../../../../database/src/shared/enums/override-type.enum';
import { ValidationValueEnum } from '../../../../database/src/shared/enums/validation-value.enum';
import { TransactionFieldRequest } from '../../../../shared/common/dto/get-populated-field-values.request';
import { DataValidationResult } from '../../../../shared/enums/data-validation-result.enum';
import { AppConfigService, GeneralAutoPopulateService } from '../../../../shared/services';
import { DataRegisterWithActiveVersionService } from '../../../../shared/services/data-register-with-active-version.service';
import { GeneralAutoPopulateSettingConfig } from '../../../../types';
import {
    addDrSystemOverrideRecords,
    convertDateTimeValueToDate,
    convertDateTimeValueToDateTime,
    convertDateTimeValueToString,
    convertDurationToMinutes,
    convertHHmmToMinutes,
    executeConditions,
    getFieldFormat,
    getOverrideRecord,
    getRelatedFields,
    updateDrActiveOverrideRecords,
} from '../../../../utils';
import { formatCondition } from '../../../../utils/formatConditional';
import { StyleConditionType } from '../../../../utils/get-style-conditions';
import { TransactionFieldUtil } from '../../../form/services/data/util/transaction-field.util';
import { FormRuleType } from '../../../validation/types';
import { DataRegisterAdditionalFieldDto } from '../../dtos/data-register-additional-field.dto';
import { DataRegisterRecordChangeLogDto } from '../../dtos/data-register-transaction-change-log.dto';
import { CheckExitedDataProviderRequest } from '../../dtos/requests/check-existed-data-provider.request';
import { FilterDataRegisterTransactionRequestDto } from '../../dtos/requests/filter-data-register-transaction.request';
import { FilterTransactionByExternalIdsRequest } from '../../dtos/requests/filter-transaction-by-external-ids.request';
import { GetDataRegisterLookupTransactionResponse } from '../../dtos/respsonses';
import { DataRegisterTransactionIdentifierDataService } from './data-register-transaction-identifier.data.service';
import { DataRegisterRepositoryService } from './data-register.repository';
import { GenerateSequenceCodeService } from './generate-squence-code.service';
import { DataRegisterUserRoleService } from './user-role.data.service';

@Injectable()
export class DataRegisterTransactionDataService {
    constructor(
        private readonly _queryBuilder: QueryBuilderService,
        @Inject(USER_CLAIMS) private readonly _claims: ClaimService,
        @InjectMapper() readonly _mapper: Mapper,
        private readonly _logger: LoggerService,
        private readonly _transQueryBuilder: TransactionQueryBuilderService,
        private readonly _formulaService: FormulaService,
        private readonly _dataProviderService: DataProviderRegisterDataService,
        @Inject(TENANT_CONNECTION) private readonly _datasource: DataSource,
        private readonly _moduleRef: ModuleRef,

        private readonly _dataRegisterWithActiveVersionService: DataRegisterWithActiveVersionService,
        private readonly _eventDrivenService: EventDrivenService,
        private readonly _httpService: HttpService,
        private readonly _baseHttp: BaseHttpService,
        private readonly _appConfig: AppConfigService,
        private readonly _userRoleService: DataRegisterUserRoleService,
        private readonly _generateSequenceService: GenerateSequenceCodeService,
        private readonly _repoService: DataRegisterRepositoryService,
        private readonly _identifierService: DataRegisterTransactionIdentifierDataService,
        private readonly _mqttService: MqttService,
        private readonly _jwt: JwtService,
        private readonly configService: ConfigService,
        private readonly _generalAutoPopulateService: GeneralAutoPopulateService,
    ) {}

    private async _getDataSource(): Promise<DataSource> {
        if (this._claims.accountId) {
            const contextId = ContextIdFactory.create();
            this._moduleRef.registerRequestByContextId(
                {
                    headers: {
                        [TENANT_HEADER]: this._claims.accountId,
                    },
                },
                contextId,
            );
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.TENANT_CONNECTION, contextId, { strict: false });
        } else {
            const contextId = ContextIdFactory.create();
            return await this._moduleRef.resolve<DataSource>(PROVIDER_KEYS.DATA_SOURCE, contextId, { strict: false });
        }
    }

    //#region GET
    public async getList({
        dataRegisterFieldRepository,
        query,
        dataRegisterTransactionRepository,
        dataRegisterRepository,
        dataRegisterTransactionFieldRepo,
        includeAdditionalFields,
    }: {
        query: FilterDataRegisterTransactionRequestDto;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity>;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        includeAdditionalFields?: boolean;
    }): Promise<PaginationResponseDto<DataRegisterTransactionDto>> {
        const { order, filters, sort, skip, take, conditions, sorters } = query;
        let dataRegisters: (DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity)[];
        let totalIds = 0;
        let dataRegisterTransactionIds: string[] = [];

        try {
            // Fields is filter by transaction fields, non-fields is filter by data register transactions
            const [fields, nonFields] = _.partition(filters, (filter) => filter.field.includes(PREFIX.DATA_REGISTER_FIELD));

            const dataRegisterId = nonFields.find((filter) => filter.field === 'dataRegisterId')?.value;

            if (!dataRegisterId) {
                throw new BadRequestException('data_register_id_required');
            }

            const normalizedFilters: FilterOptionDto[] = fields.map((filter) => ({
                ...filter,
                field: this._transQueryBuilder.withOutPrefix(filter.field),
            }));

            const fieldIds = normalizedFilters.map((filter) => filter.field);

            const isExistedSort = (sorters || []).some((s) => s.field === sort && s.order === order);
            if (sort && !isExistedSort) {
                sorters.push({ field: sort, order: order });
            }
            const [attributeSortFields, nonAttributeSortFields] = _.partition(sorters, (filter) =>
                filter.field.includes(PREFIX.DATA_REGISTER_FIELD),
            );
            const sortField = sort && sort.includes(PREFIX.DATA_REGISTER_FIELD) ? this._transQueryBuilder.withOutPrefix(sort) : null;
            const selectFields = _.compact([...fieldIds]);

            const { data, total } = await this._transQueryBuilder.getPaging({
                selectFields,
                normalizedFilters,
                standardFilters: nonFields,
                dataRegisterId: dataRegisterId as string,
                sortField,
                order,
                skip,
                take,
                conditions,
                attributeSortFields,
                nonAttributeSortFields,
            });

            dataRegisterTransactionIds = data;
            totalIds = total;

            // If no transaction match criteria, return empty data
            if (!dataRegisterTransactionIds.length) {
                return {
                    data: [],
                    total: 0,
                };
            }

            const builder = dataRegisterTransactionRepository
                .createQueryBuilder('dataRegisterTransaction')
                .leftJoinAndSelect('dataRegisterTransaction.dataRegister', 'dataRegister');

            if (includeAdditionalFields) {
                builder.leftJoinAndSelect('dataRegisterTransaction.additionalFields', 'additionalFields');
            }

            if (dataRegisterTransactionIds.length) {
                builder.where(`dataRegisterTransaction.id IN (:...ids)`, { ids: dataRegisterTransactionIds });
                this._queryBuilder.applyQueryFilters(builder, 'dataRegisterTransaction', nonFields, []);
                builder.orderBy(`CASE dataRegisterTransaction.id ${dataRegisterTransactionIds.map((id, index) => `WHEN '${id}' THEN ${index + 1}`).join(' ')}
                END`);

                dataRegisters = await builder.getMany();
            } else {
                // No filter by fields
                this._queryBuilder.applyQueryFilters(builder, 'dataRegisterTransaction', nonFields, []);

                if (!sortField && query.sort && query.order) {
                    const sorters: OrderOptionDto[] = [
                        {
                            field: sort,
                            order: order,
                        },
                    ];
                    this._queryBuilder.applySorters(builder, 'dataRegisterTransaction', sorters);
                }

                const [data, total] = await builder.skip(skip).take(take).getManyAndCount();
                dataRegisters = data;
                totalIds = total;
            }

            if (!totalIds) {
                return {
                    data: [],
                    total: 0,
                };
            }

            const transIds = dataRegisters.map((t) => t.id);

            const transactionFields = await dataRegisterTransactionFieldRepo.find({
                where: {
                    dataRegisterTransactionId: In(transIds),
                },
                relations: ['transactionFieldStyle'],
            });

            dataRegisters.forEach((value) => {
                value.transactionFields = transactionFields?.filter((field) => field.dataRegisterTransactionId === value.id) || [];
            });

            let trans = this._mapper.mapArray(dataRegisters, DataRegisterTransactionTenancyEntity, DataRegisterTransactionDto);

            trans = await this._mapFieldConfiguration(trans, dataRegisterRepository, dataRegisterId as string, dataRegisterFieldRepository);

            return {
                data: trans,
                total: totalIds,
            };
        } catch (error) {
            console.error(error);
            throw new InternalServerErrorException(error);
        }
    }

    public async getWorkspaceContext({
        query,
        dataRegisterTransactionFieldRepository,
        dataRegisterFieldRepository,
    }: {
        query: any;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
    }): Promise<any> {
        try {
            const { paramsMapping, contextValues } = query.filters[0];

            const pramContexts = await Promise.all(
                Object.keys(paramsMapping).map(async (key) => {
                    const drField = await dataRegisterFieldRepository.findOne({
                        where: { id: paramsMapping[key] },
                    });

                    if (!drField) {
                        console.warn(`DataRegisterField not found for key: ${key}`);
                        return null; // Or handle this case as appropriate, e.g., throw an error
                    }

                    const drFieldsValue = (await dataRegisterTransactionFieldRepository.find({
                        // Changed to .find()
                        where: {
                            fieldId: drField.fieldId,
                            dataRegisterTransactionId: contextValues,
                        },
                    })) as any[]; // Cast to array if using .find()

                    const value = drFieldsValue.length > 0 ? drFieldsValue[0]?.fieldValue : null;

                    return {
                        id: drField.id, // Or drField.fieldId if that's the correct unique identifier
                        value: value,
                        paramKey: key,
                    };
                }),
            );

            return pramContexts;
        } catch (error) {
            console.error(error);
            throw new InternalServerErrorException(error);
        }
    }

    public async getOptions({
        query,
        dataRegisterRepository,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
        dataRegisterVersionRepository,
        dataRegisterFieldRepository,
    }: {
        query: MultipleFilterRequestDto;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity | DataRegisterVersionEntity>;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
    }): Promise<GetDataRegisterLookupTransactionResponse> {
        try {
            const { filters } = query;

            const type = filters.find((filter) => filter.field === 'type')?.value ?? LookupFieldDataSource.DATA_REGISTER;

            switch (type) {
                case LookupFieldDataSource.DATA_REGISTER:
                default:
                    return this._getDataRegisterOptions({
                        query,
                        dataRegisterTransactionRepository,
                        dataRegisterRepository,
                        dataRegisterTransactionFieldRepository,
                        dataRegisterVersionRepository,
                        dataRegisterFieldRepository,
                    });
            }
        } catch (error) {
            console.error(error);
            throw new InternalServerErrorException(error);
        }
    }

    public async lookUpOptions({
        id,
        query,
        dataRegisterRepository,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
        conditions,
        dataRegisterVersionRepository,
        dataRegisterFieldRepository,
    }: {
        id: string;
        query: LookupQuery;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        conditions?: Record<string, any>;
        dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity | DataRegisterVersionEntity>;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
    }): Promise<PaginationResponseDto<DataRegisterTransactionOptionDto>> {
        if (!id) return PAGINATION_EMPTY_DATA;
        try {
            const queryOptionsIds = query.filters?.find((f) => f.field === 'optionIds')?.value as string[];
            const queryFilterConditionString = query.filters?.find((f) => f.field === 'filterCondition')?.value as string;
            const targetId = query.filters?.find((f) => f.field === 'dataRegisterId')?.value as string;

            if (!targetId) {
                return PAGINATION_EMPTY_DATA;
            }

            query.filters =
                query.filters?.filter((f) => !['dataRegisterId', 'optionIds', 'filterCondition', 'dataset'].includes(f.field)) || [];

            query.filters.push({
                field: 'dataRegisterId',
                operator: OperatorType.equals,
                value: targetId,
            });

            if (queryOptionsIds?.length) {
                query.filters.push({
                    field: 'optionIds',
                    operator: OperatorType.in,
                    value: queryOptionsIds,
                });
            }

            const filterCondition = queryFilterConditionString ? JSON.parse(queryFilterConditionString) : (conditions ?? {});
            if (Object.keys(filterCondition).length) {
                UtilsService.mutateFormatFilterCondition({
                    ruleOrGroup: filterCondition,
                });
                const treeCondition = QbUtils.loadTree(filterCondition);
                const isValid = QbUtils.isValidTree(treeCondition, CoreConfig);
                if (!isValid) return PAGINATION_EMPTY_DATA;

                const filterConditionSql = QbUtils.sqlFormat(treeCondition, CoreConfig);

                if (filterConditionSql) {
                    query.filters.push({
                        field: 'filterCondition',
                        operator: OperatorType.equals,
                        value: filterConditionSql,
                    });
                }
            }

            return this._getDataRegisterOptions({
                query,
                dataRegisterTransactionRepository,
                dataRegisterRepository,
                dataRegisterTransactionFieldRepository,
                dataRegisterVersionRepository,
                dataRegisterFieldRepository,
            });
        } catch (error) {
            console.error(error);
            throw new InternalServerErrorException(error);
        }
    }

    public async get({
        id,
        dataRegisterTransactionRepository,
        dataRegisterRepository,
        dataRegisterTransactionFieldRepository,
        dataRegisterTransactionFieldOverrideRepository,
        isOriginal,
        dataRegisterVersionRepository,
        dataRegisterFieldRepository,
        dataRegisterTransactionFieldStyleRepository,
    }: {
        id: string;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>;
        dataRegisterTransactionFieldOverrideRepository: Repository<
            DataRegisterTransactionFieldOverrideEntity | DataRegisterTransactionFieldOverrideTenancyEntity
        >;
        isOriginal?: boolean;
        dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity | DataRegisterVersionEntity>;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
        dataRegisterTransactionFieldStyleRepository: Repository<
            DataRegisterTransactionFieldStyleEntity | DataRegisterTransactionFieldStyleTenancyEntity
        >;
    }): Promise<DataRegisterTransactionDto> {
        try {
            //TODO: remove join to transaction fields and transactionFieldOverrides
            const data = await dataRegisterTransactionRepository.findOne({
                where: { id },
                relations: {
                    // transactionFields: {
                    //     transactionFieldOverrides: true,
                    // },
                    additionalFields: {
                        autoPopulateContexts: true,
                    },
                },
            });
            if (data) {
                const transactionFields = await dataRegisterTransactionFieldRepository.findBy({ dataRegisterTransactionId: id });
                const transactionFieldOverrides = await dataRegisterTransactionFieldOverrideRepository.findBy({
                    dataRegisterTransactionId: id,
                });
                const transactionFieldStyles = await dataRegisterTransactionFieldStyleRepository.findBy({
                    transactionId: id,
                });

                transactionFields?.forEach((item) => {
                    item.transactionFieldOverrides = transactionFieldOverrides?.filter(
                        (override) => override.dataRegisterTransactionFieldId === item.id,
                    );
                });
                data.transactionFields = transactionFields;

                const dataRegisters = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                    dataRegisterIds: [data.dataRegisterId],
                    dataRegisterRepository,
                    dataRegisterVersionRepository,
                    dataRegisterFieldRepository,
                });

                if (!dataRegisters?.length || !dataRegisters[0]?.dataRegisterVersions?.length) {
                    throw new NotFoundException('data_register_form_not_found');
                }

                const dataRegister = dataRegisters[0];
                const formVersion = dataRegister.dataRegisterVersions[0];

                const dto = this._mapper.map(data, DataRegisterTransactionTenancyEntity, DataRegisterTransactionDto);

                //map option ids to field value for select fields
                (dto.transactionFields || []).forEach((tf) => {
                    const transactionFieldStyle = transactionFieldStyles?.find((style) => style.id === tf.id);
                    if (transactionFieldStyle) {
                        tf.style = transactionFieldStyle.configuration;
                    }
                    const formField = formVersion?.fields?.find((f) => f.fieldId === tf.fieldId);

                    if (OBJECT_SELECTABLE_FIELD_TYPES.includes(formField?.type) && !isOriginal) {
                        switch (formField?.configuration?.mode) {
                            case 'single':
                                {
                                    tf.fieldValue = tf.fieldOptionIds?.[0] ?? tf.fieldValue;
                                }
                                break;
                            case 'multiple':
                                {
                                    tf.fieldValue = tf.fieldOptionIds?.join(',') ?? tf.fieldValue;
                                }
                                break;
                        }
                    }

                    tf.type = formField?.type as string as FormFieldTypeEnum;
                    tf.mode = formField?.configuration?.mode;
                    tf.format = formField?.configuration?.format;
                });
                return dto;
            }
            return null;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getAdditionalFields({
        transactionIds,
        dataRegisterTransactionRepository,
    }: {
        transactionIds: string[];
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
    }): Promise<DataRegisterTransactionDto[]> {
        try {
            const data = await dataRegisterTransactionRepository.find({
                where: { id: In(transactionIds) },
                relations: {
                    additionalFields: true,
                },
            });
            const dto = this._mapper.mapArray(data, DataRegisterTransactionTenancyEntity, DataRegisterTransactionDto);
            return dto;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async getRegisterConfigurationFields({
        id,
        additionalFieldRepo,
        registerFieldRepo,
        registerTransactionRepo,
        registerRepo,
    }: {
        id: string;
        additionalFieldRepo: Repository<DataRegisterAdditionalFieldTenancyEntity>;
        registerFieldRepo: Repository<DataRegisterFieldTenancyEntity>;
        registerTransactionRepo: Repository<DataRegisterTransactionTenancyEntity>;
        registerRepo: Repository<DataRegisterTenancyEntity>;
    }): Promise<{
        additionalFields: DataRegisterAdditionalFieldEntity[];
        registerFields: DataRegisterFieldEntity[];
    }> {
        try {
            const additionalFields = await additionalFieldRepo.find({
                where: { transactionId: id },
                select: ['id', 'label', 'fieldId', 'configuration', 'type', 'additionalType'],
            });

            const { dataRegisterId } = await registerTransactionRepo.findOne({
                where: { id },
                select: ['dataRegisterId', 'id'],
            });

            const { activeVersionId } = await registerRepo.findOne({
                where: { id: dataRegisterId },
                select: ['id', 'activeVersionId'],
            });

            const registerFields = await registerFieldRepo.find({
                where: { dataRegisterVersionId: activeVersionId },
                select: ['id', 'fieldId', 'configuration', 'type', 'lookupTargetId', 'label'],
            });
            return {
                additionalFields,
                registerFields,
            };
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    //#endregion GET

    //#region POST
    public async create({
        request,
        dataRegisterRepository,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
        dataRegisterAdditionalFieldRepository,
        dataRegisterAutoPopulateContextRepository,
        dataRegisterTransactionFieldStyleRepository,
        dataRegisterFieldRepository,
        transactionFieldOverrideRepository,
        accountId,
        dataSource,
        sourceOfChange,
        dataRegisterVersionRepository,
        generalAutoPopulateSettingRepo,
    }: {
        request: EditDataRegisterTransactionRequest;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        dataRegisterAdditionalFieldRepository: Repository<DataRegisterAdditionalFieldEntity | DataRegisterAdditionalFieldTenancyEntity>;
        dataRegisterAutoPopulateContextRepository: Repository<
            DataRegisterAutoPopulateContextEntity | DataRegisterAutoPopulateContextTenancyEntity
        >;
        dataRegisterTransactionFieldStyleRepository: Repository<
            DataRegisterTransactionFieldStyleEntity | DataRegisterTransactionFieldStyleTenancyEntity
        >;
        dataRegisterFieldRepository: Repository<DataRegisterFieldEntity | DataRegisterFieldTenancyEntity>;
        transactionFieldOverrideRepository: Repository<
            DataRegisterTransactionFieldOverrideEntity | DataRegisterTransactionFieldOverrideTenancyEntity
        >;
        accountId: string;
        dataSource: DataSource;
        sourceOfChange: SourceOfChangeType;
        dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity | DataRegisterVersionEntity>;
        generalAutoPopulateSettingRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>;
    }): Promise<string> {
        try {
            const isFilteredExternal = !!request.isFilteredExternal;
            const forms = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                dataRegisterIds: [request.dataRegisterId],
                dataRegisterRepository,
                dataRegisterVersionRepository,
                dataRegisterFieldRepository,
            });

            if (!forms?.length || !forms[0]?.dataRegisterVersions?.length) {
                throw new BadRequestException('data_register_form_not_found');
            }

            const form = forms[0];
            const registerType = form.type;
            const formVersion = form.dataRegisterVersions[0];

            let relatedRegister: string | boolean = null;
            let relatedRegisterIds: Set<string> = new Set();

            // check register code is exist
            const codeField = (formVersion?.fields || []).find((f) => f.fieldId === DATA_REGISTER_CODE_FIELD_ID);
            if (!codeField?.configuration?.isAutoGenerateCode) {
                const { field_value_count } = await this.getFieldValueCount(
                    DATA_REGISTER_CODE_FIELD_ID,
                    request.dataRegisterId,
                    dataRegisterRepository,
                    request.transactionFields,
                );
                if (field_value_count > 0) {
                    throw new BadRequestException('data_register_code_is_exist');
                }
            }

            let authorization = RequestContextService.authorization;
            if (!authorization) {
                const token = this._jwt.sign(
                    {
                        accountId: RequestContextService.accountId,
                    },
                    {
                        secret: this.configService.get('APP_SECRET'),
                    },
                );

                authorization = `Bearer ${token}`;
            }
            if (DataProviderTypes.includes(registerType)) {
                const externalId = request.externalId;
                const existedDataProvider = await this.isExistedDataProvider({
                    request: {
                        externalId,
                        dataRegisterId: request.dataRegisterId,
                    },
                    transactionRepo: dataRegisterTransactionRepository,
                });

                if (existedDataProvider) {
                    throw new BadRequestException('data_provider_existed');
                }

                //send request to data lake to verify if need to init entity
                try {
                    const payload: Partial<{
                        vesselId: string;
                        companyId: string;
                        countryId: string;
                        portId: string;
                        terminalId: string;
                        berthId: string;
                    }> = {};

                    switch (registerType) {
                        case DataRegisterTypeEnum.Vessel:
                            payload.vesselId = externalId;
                            break;

                        case DataRegisterTypeEnum.Company:
                            payload.companyId = externalId;
                            break;

                        case DataRegisterTypeEnum.Country:
                            payload.countryId = externalId;
                            break;

                        case DataRegisterTypeEnum.Port:
                            payload.portId = externalId;
                            break;

                        case DataRegisterTypeEnum.Terminal:
                            payload.terminalId = externalId;
                            break;

                        case DataRegisterTypeEnum.Berth:
                            payload.berthId = externalId;
                            break;
                    }

                    if (!_.isEmpty(payload)) {
                        const url = `${this._appConfig.dataLake.url}/documents/v1/init-entity-documents`;
                        this._baseHttp
                            .post(url, payload, {
                                headers: {
                                    Authorization: authorization,
                                },
                            })
                            .catch((error) => {
                                this._logger.error(error);
                            });
                    }
                } catch (err) {
                    this._logger.error(err);
                }
            }

            const checkIdentifierResult = await this.checkDuplicateIdentifier({
                dataRegisterTransactionFieldRepo: dataRegisterTransactionFieldRepository,
                dataRegisterTransactionRepo: dataRegisterTransactionRepository,
                dataRegisterVersion: formVersion,
                payload: request,
            });

            // fields has default value
            let defaultValueFields = formVersion.fields || [];
            if (checkIdentifierResult?.identifier) {
                defaultValueFields = defaultValueFields.filter((f) =>
                    checkIdentifierResult?.identifier?.availableFields?.includes(f.fieldId),
                );
            }

            //format default field Values
            const extraRequestFields: EditDataRegisterTransactionFieldRequest[] = [];
            (defaultValueFields || []).forEach((rf) => {
                const transactionField = (request.transactionFields || []).find((f) => f.fieldId === rf.fieldId);
                if (!transactionField) {
                    const newTransactionField = new EditDataRegisterTransactionFieldRequest();
                    newTransactionField.fieldId = rf.fieldId;
                    newTransactionField.fieldType = rf.type;
                    newTransactionField.fieldFormula = null;
                    newTransactionField.defaultValue = rf?.configuration?.defaultValue;
                    newTransactionField.fieldValue = rf?.configuration?.defaultValue;
                    newTransactionField.fieldOptionIds = [];

                    if (SELECTABLE_FIELD_TYPES.includes(rf.type)) {
                        newTransactionField.fieldOptionIds = newTransactionField.fieldValue
                            ? Array.isArray(newTransactionField.fieldValue)
                                ? newTransactionField.fieldValue
                                : newTransactionField.fieldValue.split(',').map((item) => item.trim())
                            : [];
                    }
                    extraRequestFields.push(newTransactionField);
                }
            });

            request.transactionFields = [...(request?.transactionFields || []), ...(extraRequestFields || [])];

            const entityMapper = this._mapper.map(request, EditDataRegisterTransactionRequest, DataRegisterTransactionTenancyEntity);

            entityMapper.dataRegisterVersionId = formVersion.id;
            const entity = dataRegisterTransactionRepository.create(entityMapper);

            if (DataProviderTypes.includes(formVersion.type)) {
                const conditions: {
                    dataRegisterId: string;
                    deletedAt: null;
                    id?: string;
                    externalId?: string;
                }[] = [];

                const registerValues: {
                    registerId: string;
                    value: any;
                }[] = [];

                const mdsFilterLookupValues: {
                    registerId: string;
                    value: { fieldValue: string; fieldId: string };
                }[] = [];

                const generalAps: { fieldId: string; targetComparedFieldId?: string }[] = await generalAutoPopulateSettingRepo.find({
                    where: {
                        builderVersionId: formVersion.id,
                    },
                    select: {
                        fieldId: true,
                        targetComparedFieldId: true,
                    },
                });

                const normalToLookupGeneralApsMap = generalAps
                    .filter((setting) => setting.targetComparedFieldId)
                    .reduce((map, item) => {
                        map.set(item.fieldId, item.targetComparedFieldId);
                        return map;
                    }, new Map<string, string>());

                const mdsSearchValues: { fieldId: string; value: string; dataRegisterId: string }[] = [];

                for (const { fieldId, type, lookupTargetId, configuration } of formVersion.fields) {
                    if (type !== FormFieldTypeEnum.Lookup || !lookupTargetId || !configuration?.targetId) continue;

                    const value = entityMapper?.transactionFields?.find((item) => item?.fieldId === fieldId);
                    const targetId = lookupTargetId || configuration.targetId;

                    //When create register by manual (from Portal) the mds filters have already run via API: filter-external-ids
                    //So don't need to run filter again
                    if (!isFilteredExternal && value?.fieldValue && normalToLookupGeneralApsMap.has(fieldId)) {
                        mdsSearchValues.push({
                            fieldId,
                            value: value.fieldValue,
                            dataRegisterId: targetId,
                        });

                        mdsFilterLookupValues.push({ registerId: configuration.targetId, value });
                        continue;
                    }

                    if (!value?.fieldValue || !isValidUUID(value?.fieldValue)) continue;

                    registerValues.push({ registerId: configuration.targetId, value });
                    conditions.push({
                        dataRegisterId: configuration.targetId,
                        id: value.fieldValue,
                        deletedAt: null,
                    });

                    conditions.push({
                        dataRegisterId: configuration.targetId,
                        externalId: value.fieldValue,
                        deletedAt: null,
                    });
                }

                if (mdsSearchValues.length) {
                    const searchResultMdsEntities = await this.filterMDSProperties({
                        dataRegisterRepository,
                        filters: mdsSearchValues,
                    });

                    mdsSearchValues.forEach((item) => {
                        if (searchResultMdsEntities.resultMap.has(item.fieldId)) {
                            const value = searchResultMdsEntities.resultMap.get(item.fieldId);
                            conditions.push({
                                dataRegisterId: item.dataRegisterId,
                                id: value,
                                deletedAt: null,
                            });

                            conditions.push({
                                dataRegisterId: item.dataRegisterId,
                                externalId: value,
                                deletedAt: null,
                            });
                        }
                    });

                    mdsFilterLookupValues.forEach((item) => {
                        if (!item.value) return;
                        const fieldId = item.value.fieldId;
                        const value = searchResultMdsEntities.resultMap.get(fieldId);
                        if (value) {
                            item.value.fieldValue = value;
                            registerValues.push(item);
                        }
                    });
                }

                if (conditions.length) {
                    const registers: Partial<{ id: string; dataRegisterId: string; externalId: string }>[] =
                        await dataRegisterTransactionRepository.find({
                            where: conditions,
                            select: ['id', 'dataRegisterId', 'externalId'],
                        });

                    for (const { registerId, value } of registerValues) {
                        const register = registers.find(
                            (item) =>
                                item.dataRegisterId === registerId &&
                                (item.id === value.fieldValue || item.externalId === value.fieldValue),
                        );
                        if (!register) {
                            const newRegisterActiveVersion = await dataRegisterRepository.findOne({
                                where: {
                                    id: registerId,
                                    type: In(DataProviderTypes),
                                    deletedAt: null,
                                },
                                select: ['activeVersionId', 'type'],
                            });
                            if (!newRegisterActiveVersion) {
                                value.fieldValue = null;
                            } else {
                                const [newRegisterFields, autoPopulateValues] = await Promise.all([
                                    dataRegisterFieldRepository.find({
                                        where: {
                                            dataRegisterVersionId: newRegisterActiveVersion.activeVersionId,
                                        },
                                        select: ['fieldId', 'configuration', 'type'],
                                    }),
                                    this._baseHttp.get(
                                        `${process.env.DATA_LAKE_SERVICE_URL}/ctx-properties/v1/data-register/${registerId}/${newRegisterActiveVersion.type}/${value.fieldValue}`,
                                        {
                                            headers: {
                                                Authorization: authorization,
                                            },
                                        },
                                    ),
                                ]);

                                const externalId = value.fieldValue;
                                const newRegisterRequest: EditDataRegisterTransactionRequest = {
                                    dataRegisterId: registerId,
                                    transactionFields: newRegisterFields.map((item) => ({
                                        fieldId: item.fieldId,
                                        fieldType: item.type,
                                        fieldFormula: null,
                                        fieldValue: autoPopulateValues.data?.[item.fieldId] || null,
                                        defaultValue: item.configuration?.defaultValue,
                                    })),
                                    externalId,
                                    additionalFields: [],
                                    isFilteredExternal,
                                };

                                try {
                                    const newRegister = await this.create({
                                        request: newRegisterRequest,
                                        dataRegisterRepository,
                                        dataRegisterTransactionRepository,
                                        dataRegisterTransactionFieldRepository,
                                        dataRegisterAdditionalFieldRepository,
                                        dataRegisterAutoPopulateContextRepository,
                                        dataRegisterTransactionFieldStyleRepository,
                                        dataRegisterFieldRepository,
                                        accountId,
                                        dataSource,
                                        sourceOfChange,
                                        dataRegisterVersionRepository,
                                        transactionFieldOverrideRepository,
                                        generalAutoPopulateSettingRepo,
                                    });

                                    //push to current checking registers
                                    registers.push({
                                        dataRegisterId: registerId,
                                        id: newRegister as string,
                                        externalId,
                                    });

                                    value.fieldValue = newRegister;
                                    relatedRegister = newRegister;
                                    relatedRegisterIds.add(newRegister as string);
                                } catch (error: any) {
                                    if (error?.message !== 'data_provider_existed') {
                                        value.fieldValue = null;
                                        return;
                                    }

                                    const register = await dataRegisterTransactionRepository.findOne({
                                        where: {
                                            dataRegisterId: registerId,
                                            externalId: value.fieldValue,
                                            deletedAt: null,
                                        },
                                        select: ['id'],
                                    });
                                    if (!register) {
                                        value.fieldValue = null;
                                        return;
                                    }
                                    value.fieldValue = register.id;
                                }
                            }
                        } else if (register.id !== value.fieldValue) {
                            value.fieldValue = register.id;
                            relatedRegister = register.id;
                            relatedRegisterIds.add(register.id);
                        }

                        if (value) {
                            const mapTransactionField = entityMapper?.transactionFields?.find((item) => item?.fieldId === value?.fieldId);
                            if (mapTransactionField && mapTransactionField?.fieldValue != value.fieldValue) {
                                mapTransactionField.fieldValue = value.fieldValue;
                            }
                        }
                    }
                }
            }

            let restoreTransactionId: string | null = null;
            if (request.externalId) {
                const existedTransaction = await dataRegisterTransactionRepository.findOne({
                    where: {
                        dataRegisterId: request.dataRegisterId,
                        externalId: request.externalId,
                        deletedAt: Not(IsNull()),
                    },
                    order: {
                        deletedAt: 'DESC',
                    },
                    withDeleted: true,
                });

                if (existedTransaction) {
                    restoreTransactionId = existedTransaction.id;
                }
            }

            let transaction: DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity;

            if (restoreTransactionId) {
                entity.id = restoreTransactionId;
                entity.deletedAt = null;
                entity.deletedByUser = null;
                entity.deletedBy = null;

                await dataRegisterTransactionRepository.upsert(entity, ['id']);

                transaction = entity;
            } else {
                transaction = await dataRegisterTransactionRepository.save(entity);
            }

            //create static field id for data provider register
            if (DataProviderTypes.includes(registerType)) {
                entityMapper.transactionFields = this._dataProviderService.createExternalField({
                    externalId: request.externalId,
                    fields: entityMapper.transactionFields || [],
                    type: registerType,
                });
            }

            if (transaction && entityMapper.transactionFields?.length) {
                const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum; border?: number }> = {};

                //#region users
                const userIds: string[] = [];
                (request.transactionFields || []).forEach((field) => {
                    const formField = (formVersion.fields || []).find((f) => f.fieldId === field.fieldId);
                    if (formField?.type === FormFieldTypeEnum.UserLookup && field.fieldValue) {
                        userIds.push(field.fieldValue);
                    }
                });
                const userData = await this._userRoleService.getUsers({
                    userIds,
                    accountId,
                    dataSource,
                });
                //#endregion

                //#region roles
                const roleIds: string[] = [];
                (request.transactionFields || []).forEach((field) => {
                    const formField = (formVersion.fields || []).find((f) => f.fieldId === field.fieldId);
                    if (formField?.type === FormFieldTypeEnum.RoleLookup && field.fieldValue) {
                        roleIds.push(field.fieldValue);
                    }
                });
                const roleData = await this._userRoleService.getRoles({
                    roleIds,
                    accountId,
                    dataSource,
                });
                //#endregion

                const relatedLookupData = await this.getRelatedLookupData({
                    formVersion,
                    transactionFields: entityMapper.transactionFields,
                    dataRegisterRepository,
                    dataRegisterTransactionRepository,
                });

                this._preProcessDataFields(formVersion.fields, entityMapper?.transactionFields, relatedLookupData);

                const transactionFields = entityMapper.transactionFields
                    ?.filter((field) => field.fieldId.indexOf('--') < 0)
                    ?.map((transactionField) => {
                        // save origin validation values
                        originTransactionFieldOverrides[transactionField.id] = {
                            type: OverrideTypeEnum.System,
                            value: transactionField.validationValue,
                            border: transactionField.transactionFieldStyle?.configuration?.border,
                        };
                        transactionField.dataRegisterTransactionId = transaction.id;
                        transactionField.id = uuid();

                        const formField = (formVersion.fields || []).find((f) => f.fieldId === transactionField.fieldId);
                        switch (formField?.type) {
                            case FormFieldTypeEnum.Select: {
                                const { fieldOptionIds, fieldValue } = this._formatSelectValue({ formField, transactionField });
                                transactionField.fieldOptionIds = fieldOptionIds;
                                transactionField.fieldValue = fieldValue;
                                break;
                            }
                            case FormFieldTypeEnum.Lookup: {
                                const { fieldOptionIds, fieldValue } = this.formatLookupValue({
                                    formField,
                                    transactionField,
                                    dataRegisterVersions: (relatedLookupData.dataRegisters ?? [])
                                        .filter((dr) => dr.dataRegisterVersions?.length)
                                        .map((dr) => dr.dataRegisterVersions[0]),
                                    dataRegisterTransactions: relatedLookupData.dataRegisterTransactions,
                                });
                                transactionField.fieldOptionIds = fieldOptionIds;
                                transactionField.fieldValue = fieldValue;
                                break;
                            }
                            case FormFieldTypeEnum.Duration: {
                                if (transactionField.fieldValue && isNaN(+transactionField.fieldValue)) {
                                    transactionField.fieldValue = convertDurationToMinutes(transactionField.fieldValue)?.toString();
                                }
                                break;
                            }
                            case FormFieldTypeEnum.Calculation: {
                                if (formField.configuration?.calculationFormula?.dataType === FormFieldTypeEnum.Duration) {
                                    if (transactionField.fieldValue && isNaN(+transactionField.fieldValue)) {
                                        transactionField.fieldValue = convertDurationToMinutes(transactionField.fieldValue)?.toString();
                                    }
                                }
                                break;
                            }

                            case FormFieldTypeEnum.UserLookup:
                                {
                                    const searchValues = Array.isArray(transactionField.fieldValue)
                                        ? transactionField.fieldValue
                                        : [transactionField.fieldValue];
                                    const users = (userData || []).filter((user) => searchValues.includes(user.id));
                                    if (users?.length) {
                                        transactionField.fieldValue = users
                                            .map((user) => `${user.firstName} ${user.secondName}`)
                                            .join(', ');
                                        transactionField.fieldOptionIds = users.map((user) => user.id);
                                    }
                                }
                                break;
                            case FormFieldTypeEnum.RoleLookup:
                                {
                                    const searchValues = Array.isArray(transactionField.fieldValue)
                                        ? transactionField.fieldValue
                                        : [transactionField.fieldValue];
                                    const roles = (roleData || []).filter((r) => searchValues.includes(r.id));
                                    if (roles?.length) {
                                        transactionField.fieldValue = roles.map((r) => r.name).join(', ');
                                        transactionField.fieldOptionIds = roles.map((r) => r.id);
                                    }
                                }
                                break;

                            case FormFieldTypeEnum.DatePicker: {
                                const { defaultValue, pickerType } = formField?.configuration ?? {};

                                const value = convertDateTimeValueToDate({ pickerType, value: defaultValue });

                                if (value) transactionField.fieldValue = value.format(MMDDYYYY);
                                else transactionField.fieldValue = undefined;
                                break;
                            }

                            case FormFieldTypeEnum.DatetimePicker: {
                                const { defaultValue, pickerType } = formField?.configuration ?? {};

                                const value = convertDateTimeValueToDateTime({ pickerType, value: defaultValue });

                                if (value) transactionField.fieldValue = value.format(TRANSPORT_DATE_TIME_FORMAT);
                                else transactionField.fieldValue = undefined;
                                break;
                            }

                            case FormFieldTypeEnum.TimePicker: {
                                const { defaultValue, pickerType } = formField?.configuration ?? {};
                                const value = convertDateTimeValueToString({ pickerType, value: defaultValue });
                                if (_.isEmpty(value)) break;

                                const minutes = convertHHmmToMinutes(value.toString()).toString();
                                transactionField.fieldValue = minutes;
                                break;
                            }
                        }

                        return transactionField;
                    });

                //#region code
                const code = await this.generateSequenceCode({
                    accountId,
                    dataSource,
                    codeField,
                    registerId: form.id,
                });

                if (code) {
                    const transactionCodeField = (transactionFields || []).find((field) => field.fieldId === DATA_REGISTER_CODE_FIELD_ID);
                    if (transactionCodeField) {
                        transactionCodeField.fieldValue = code;
                    } else {
                        transactionFields.push(
                            dataRegisterTransactionFieldRepository.create({
                                id: uuid(),
                                fieldId: DATA_REGISTER_CODE_FIELD_ID,
                                dataRegisterTransactionId: transaction.id,
                                fieldValue: code,
                            }),
                        );
                    }
                }

                //#endregion

                if (transactionFields?.length) {
                    const transactionFieldStyles = this.getTransactionFieldStyles(transaction.id, formVersion.fields, transactionFields);

                    this.copyFieldStyles({
                        transactionFieldStyles: transactionFieldStyles,
                        updatedFields: transactionFields,
                        styleAndOverrideFields: request.styleAndOverrideFields,
                    });
                    transaction.transactionFields = await dataRegisterTransactionFieldRepository.save(transactionFields);
                    if (transactionFieldStyles?.length) {
                        await dataRegisterTransactionFieldStyleRepository.save(transactionFieldStyles);
                    }

                    await this.copyOverrideRecords({
                        transaction,
                        styleAndOverrideFields: request.styleAndOverrideFields,
                        transactionFieldOverrideRepository: transactionFieldOverrideRepository,
                    });
                }

                transaction.additionalFields = await this._updateAdditionalFields({
                    payload: request.additionalFields,
                    transactionId: transaction.id,
                    dataRegisterAdditionalFieldRepository,
                    dataRegisterAutoPopulateContextRepository,
                });

                this.detectAndPublishFieldChangedEvent({
                    oldFields: transaction.transactionFields,
                    newFields: transactionFields,
                    aggregateId: transaction.id,
                    updatedTransaction: transaction,
                }).catch(console.error);
            }

            // Push MQTT Register Updated event for Widget
            this.publishMQTTRegisterChangedEvent({
                accountId,
                registerId: request.dataRegisterId,
            });

            const message = EventDrivenService.createCommonEvent({
                payload: transaction,
                aggregateId: transaction.id,
                tenantId: RequestContextService.accountId,
                type: DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED,
                name: DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED,
            });
            await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC, message);

            await this.createChangeLog({
                accountId,
                sourceOfChange,
                record: transaction,
                type: DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED,
                name: DataRegisterEventEnum.DATA_REGISTER_RECORD_CREATED,
            });

            for (const relatedRegister of relatedRegisterIds) {
                if (!validate(relatedRegister)) continue;

                const relatedRegisterTransaction = await dataRegisterTransactionRepository.findOneBy({
                    id: relatedRegister as string,
                });

                const relatedRegisterFields = await dataRegisterTransactionFieldRepository.findBy({
                    dataRegisterTransactionId: relatedRegisterTransaction.id,
                });

                await this.publishDataPopulateEvent(relatedRegisterTransaction, relatedRegisterFields);
            }

            return transaction.id;
        } catch (err) {
            console.error(err);
            this._logger.error(err);
            throw err;
        }
    }

    public async filterMDSProperties({
        dataRegisterRepository,
        filters,
    }: {
        dataRegisterRepository: Repository<DataRegisterTenancyEntity>;
        filters: {
            fieldId: string;
            value: string;
            dataRegisterId: string;
        }[];
    }) {
        const builderIds = filters.map((item) => item.dataRegisterId);
        const entityRegisters = await dataRegisterRepository.find({
            where: {
                id: In(builderIds),
                type: In(DataProviderTypes),
            },
            select: {
                id: true,
                type: true,
            },
        });

        const registerMap = entityRegisters.reduce((map, item) => {
            map.set(item.id, item.type);
            return map;
        }, new Map<string, string>());

        const mdsSearchValues = filters.filter((item) => registerMap.has(item.dataRegisterId));

        const mdsFilters: {
            fieldId: string;
            text: string;
            type: string;
        }[] = mdsSearchValues.map((item) => ({
            fieldId: item.fieldId,
            text: item.value,
            type: registerMap.get(item.dataRegisterId)?.toLowerCase(),
        }));

        const searchResultMdsEntities = await this._generalAutoPopulateService.filterDataLakeEntities({
            filters: mdsFilters,
        });

        const searchResultMap = searchResultMdsEntities.reduce((map, item) => {
            map.set(item.fieldId, item.id);
            return map;
        }, new Map<string, string>());

        return { result: searchResultMdsEntities, resultMap: searchResultMap };
    }

    public async generateSequenceCode({
        codeField,
        accountId,
        dataSource,
        registerId,
    }: {
        codeField: DataRegisterFieldEntity;
        registerId: string;
        accountId: string;
        dataSource: DataSource;
    }): Promise<string> {
        if (!codeField?.configuration?.isAutoGenerateCode) {
            return null;
        }
        const { formFieldRepo } = this._repoService.get({
            accountId,
            dataSource,
        });

        const prefix = codeField.configuration?.fieldPrefix;
        const leadingZeros = codeField.configuration?.leadingZeros ?? 5;
        let attempts = 0;
        let code = await this._generateSequenceService.generateSequenceValue({
            fieldRepo: formFieldRepo,
            registerId,
            leadingZeros,
            prefix,
            schema: accountId ? accountId : 'public',
        });

        let isExisted = await this.isCodeExisted({
            accountId,
            value: code,
            dataSource,
            registerId,
        });

        while (isExisted) {
            if (attempts > 100) {
                break;
            }

            const newCode = await this._generateSequenceService.generateSequenceValue({
                fieldRepo: formFieldRepo,
                registerId,
                leadingZeros,
                prefix,
                schema: accountId ? accountId : 'public',
            });

            isExisted = await this.isCodeExisted({
                accountId,
                value: newCode,
                dataSource,
                registerId,
            });

            if (!isExisted) {
                code = newCode;
                break;
            }

            attempts++;
        }

        return code;
    }

    public async isCodeExisted({
        accountId,
        value,
        dataSource,
        registerId,
    }: {
        value: string;
        registerId: string;
        accountId: string;
        dataSource: DataSource;
    }) {
        const { dataRegisterTransactionRepo } = this._repoService.get({
            accountId,
            dataSource,
        });

        const code = await dataRegisterTransactionRepo
            .createQueryBuilder('register')
            .leftJoin('register.transactionFields', 'fields')
            .where({
                dataRegisterId: registerId,
            })
            .andWhere('fields.fieldId = :fieldId', { fieldId: DATA_REGISTER_CODE_FIELD_ID })
            .andWhere('fields.fieldValue = :fieldValue', { fieldValue: value })
            .getOne();

        return !!code;
    }

    public async isExistedDataProvider({
        request,
        transactionRepo,
    }: {
        request: CheckExitedDataProviderRequest;
        transactionRepo: Repository<DataRegisterTransactionEntity>;
    }): Promise<boolean> {
        const externalId = request.externalId;
        if (!externalId) {
            return false;
        }

        const existedEntity = await transactionRepo.findOneBy({
            externalId: externalId,
            dataRegisterId: request.dataRegisterId,
        });

        return !!existedEntity;
    }

    public async filterTransactionsByExternalIds({
        request,
        transactionRepo,
        transactionFieldRepo,
        generalAutoPopulateRepo,
        dataRegisterRepo,
    }: {
        request: FilterTransactionByExternalIdsRequest;
        dataRegisterRepo: Repository<DataRegisterTenancyEntity>;
        transactionRepo: Repository<DataRegisterTransactionTenancyEntity>;
        transactionFieldRepo: Repository<DataRegisterTransactionFieldTenancyEntity>;
        generalAutoPopulateRepo: Repository<GeneralAutoPopulateSettingTenancyEntity>;
    }): Promise<{ id: string; dataRegisterId: string; externalId?: string; fieldId?: string }[]> {
        if (!request.externals?.length) return [];

        const dataRegisterVersionId = request.dataRegisterVersionId;
        const fieldIds = request.externals.map((e) => e.fieldId);

        const generalAps = await generalAutoPopulateRepo.find({
            where: {
                fieldId: In(fieldIds),
                builderVersionId: dataRegisterVersionId,
                builderType: AutoPopulateBuilderTypeEnum.RegisterField,
            },
        });

        const toComparedFieldSettings = generalAps.filter((g) => g.targetComparedFieldId);
        const result: {
            id: string;
            dataRegisterId: string;
            externalId?: string;
            fieldId?: string;
        }[] = [];
        const { comparedMap } = toComparedFieldSettings.reduce(
            (map, field) => {
                map.comparedMap.set(field.fieldId, field.targetComparedFieldId);
                return map;
            },
            {
                comparedMap: new Map<string, string>(),
            },
        );

        //Just search on register, not need to compare register's field
        const validExternals = request.externals.filter((e) => !comparedMap.has(e.fieldId));
        if (validExternals.length) {
            const transactions = await transactionRepo.find({
                where: validExternals.map((item) => ({
                    externalId: item.externalValue,
                    dataRegisterId: item.dataRegisterId,
                })),
                select: ['id', 'externalId', 'dataRegisterId'],
            });

            result.push(
                ...transactions.map((t) => ({
                    id: t.id,
                    externalId: t.externalId,
                    dataRegisterId: t.dataRegisterId,
                })),
            );
        }

        //Need one more step to search on existed register field so that infer register
        const comparedOnRegisterFields = request.externals.filter((e) => comparedMap.has(e.fieldId));
        const absentedValueSearchFields: { fieldId: string; value: string; dataRegisterId: string; comparedFieldId: string }[] = [];

        if (comparedOnRegisterFields.length) {
            const builder = transactionFieldRepo
                .createQueryBuilder('registerField')
                .select(['registerField.fieldId', 'registerField.dataRegisterTransactionId'])
                .innerJoin('registerField.dataRegisterTransaction', 'register')
                .where(
                    new Brackets((orQb) => {
                        comparedOnRegisterFields.forEach((item, idx) => {
                            orQb.orWhere(
                                new Brackets((andQb) => {
                                    andQb
                                        .where(`"register"."data_register_id" = :dataRegisterId_${idx}`, {
                                            [`dataRegisterId_${idx}`]: item.dataRegisterId,
                                        })
                                        .andWhere(`registerField.fieldId = :fieldId_${idx}`, {
                                            [`fieldId_${idx}`]: comparedMap.get(item.fieldId),
                                        })

                                        .andWhere(`registerField.fieldValue = :fieldValue_${idx}`, {
                                            [`fieldValue_${idx}`]: item.externalValue,
                                        });

                                    return andQb;
                                }),
                            );
                        });
                        return orQb;
                    }),
                );

            const transactionFields: { fieldId: string; dataRegisterTransactionId?: string }[] = await builder.getMany();

            const comparedTranFieldMap = transactionFields.reduce((map, field) => {
                map.set(field.fieldId, field.dataRegisterTransactionId);
                return map;
            }, new Map<string, string>());

            comparedOnRegisterFields.forEach((field) => {
                const comparedFieldId = comparedMap.get(field.fieldId);
                const transactionId = comparedTranFieldMap.get(comparedFieldId);
                if (transactionId) {
                    result.push({
                        id: transactionId,
                        dataRegisterId: field.dataRegisterId,
                        fieldId: field.fieldId,
                    });
                } else {
                    absentedValueSearchFields.push({
                        dataRegisterId: field.dataRegisterId,
                        fieldId: field.fieldId,
                        value: field.externalValue,
                        comparedFieldId,
                    });
                }
            });
        }

        //Absented registered but has compared value will used to go to filter on mds level
        //Because when creating a vessel, maybe vessel's company has not been pulled from mds yet
        if (absentedValueSearchFields.length) {
            const filterMDS = await this.filterMDSProperties({
                dataRegisterRepository: dataRegisterRepo,
                filters: absentedValueSearchFields,
            });

            if (absentedValueSearchFields.length) {
                absentedValueSearchFields.forEach((item) => {
                    if (filterMDS.resultMap.has(item.fieldId)) {
                        result.push({
                            id: filterMDS.resultMap.get(item.fieldId),
                            dataRegisterId: item.dataRegisterId,
                            fieldId: item.fieldId,
                        });
                    }
                });
            }
        }

        return result;
    }

    //#endregion POST

    //#region PUT
    public async update({
        id,
        request,
        dataRegisterRepository,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
        dataRegisterAdditionalFieldRepository,
        dataRegisterAutoPopulateContextRepository,
        dataRegisterTransactionFieldStyleRepository,
        transactionFieldOverrideRepository,
        dataSource,
        accountId,
        sourceOfChange,
        dataRegisterVersionRepository,
        dataRegisterFieldRepository,
    }: {
        id: string;
        request: EditDataRegisterTransactionRequest;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        dataRegisterAdditionalFieldRepository: Repository<DataRegisterAdditionalFieldEntity | DataRegisterAdditionalFieldTenancyEntity>;
        dataRegisterAutoPopulateContextRepository: Repository<
            DataRegisterAutoPopulateContextEntity | DataRegisterAutoPopulateContextTenancyEntity
        >;
        dataRegisterTransactionFieldStyleRepository: Repository<
            DataRegisterTransactionFieldStyleEntity | DataRegisterTransactionFieldStyleTenancyEntity
        >;
        transactionFieldOverrideRepository: Repository<
            DataRegisterTransactionFieldOverrideEntity | DataRegisterTransactionFieldOverrideTenancyEntity
        >;
        dataSource: DataSource;
        accountId: string;
        sourceOfChange: SourceOfChangeType;
        dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity | DataRegisterVersionEntity>;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
    }) {
        let form: DataRegisterTenancyEntity;
        try {
            const forms = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
                dataRegisterIds: [request.dataRegisterId],
                dataRegisterRepository,
                dataRegisterVersionRepository,
                dataRegisterFieldRepository,
            });

            if (!forms?.length || !forms[0]?.dataRegisterVersions?.length) {
                throw new BadRequestException('data_register_form_not_found');
            }
            form = forms[0];
            const formVersion = form.dataRegisterVersions[0];

            // check register code is exist
            const { field_value_count } = await this.getFieldValueCountByTransId(
                DATA_REGISTER_CODE_FIELD_ID,
                request.dataRegisterId,
                dataRegisterRepository,
                request.transactionFields,
                id,
            );
            if (field_value_count) {
                throw new BadRequestException('data_register_code_is_exist');
            }

            const transaction = await dataRegisterTransactionRepository.findOne({
                where: {
                    id,
                },
                relations: {
                    transactionFieldStyles: true,
                    transactionFields: true,
                    additionalFields: true,
                },
            });

            if (!transaction) {
                throw new NotFoundException('transaction_not_found');
            }

            await this.copyOverrideRecords({
                transaction,
                styleAndOverrideFields: request.styleAndOverrideFields,
                transactionFieldOverrideRepository,
            });

            const oldTransaction = _.cloneDeep(transaction);

            await this.checkDuplicateIdentifier({
                dataRegisterTransactionFieldRepo: dataRegisterTransactionFieldRepository,
                dataRegisterVersion: formVersion,
                dataRegisterTransactionRepo: dataRegisterTransactionRepository,
                payload: request,
                transactionId: id,
            });

            const originTransactionFieldOverrides: Record<string, { value: number; type: OverrideTypeEnum }> = {};
            transaction.transactionFields.forEach((tf) => {
                originTransactionFieldOverrides[tf.id] = {
                    value: tf.validationValue,
                    type: OverrideTypeEnum.System,
                };
            });

            transaction.dataRegisterId = formVersion.dataRegisterId;
            transaction.dataRegisterVersionId = formVersion.id;

            if (request.externalId) {
                transaction.externalId = request.externalId;
            }

            const transactionFields = transaction.transactionFields || [];

            const relatedLookupData = await this.getRelatedLookupData({
                formVersion,
                transactionFields: request.transactionFields,
                dataRegisterRepository,
                dataRegisterTransactionRepository,
            });

            this._preProcessDataFields(formVersion.fields, transactionFields, relatedLookupData);

            //update static field id for data provider register
            if (DataProviderTypes.includes(form.type)) {
                request.transactionFields = this._dataProviderService.updateExternalField({
                    externalId: request.externalId,
                    type: form.type,
                    transactionId: transaction.id,
                    transactionFields,
                    requestFields: request.transactionFields,
                });
            }

            //users
            const userIds: string[] = [];
            (request.transactionFields || []).forEach((field) => {
                const formField = (formVersion.fields || []).find((f) => f.fieldId === field.fieldId);
                if (formField?.type === FormFieldTypeEnum.UserLookup && field.fieldValue) {
                    userIds.push(field.fieldValue);
                }
            });
            const userData = await this._userRoleService.getUsers({
                userIds,
                accountId,
                dataSource,
            });

            //roles
            const roleIds: string[] = [];
            (request.transactionFields || []).forEach((field) => {
                const formField = (formVersion.fields || []).find((f) => f.fieldId === field.fieldId);
                if (formField?.type === FormFieldTypeEnum.RoleLookup && field.fieldValue) {
                    roleIds.push(field.fieldValue);
                }
            });
            const roleData = await this._userRoleService.getRoles({
                roleIds,
                accountId,
                dataSource,
            });

            let updateFields = request.transactionFields.map((requestField) => {
                const mapperField = this._mapper.map(
                    requestField,
                    EditDataRegisterTransactionFieldRequest,
                    DataRegisterTransactionFieldTenancyEntity,
                );
                const existedField = (transactionFields as DataRegisterTransactionFieldEntity[]).find(
                    (field) => field.fieldId === mapperField.fieldId,
                );

                mapperField.id = existedField?.id ?? uuid();
                mapperField.fieldId = existedField?.fieldId ?? mapperField.fieldId;
                mapperField.dataRegisterTransactionId = transaction.id;

                const formField = (formVersion.fields || []).find((f) => f.fieldId === mapperField.fieldId);
                switch (formField?.type) {
                    case FormFieldTypeEnum.Select: {
                        const { fieldOptionIds, fieldValue } = this._formatSelectValue({ formField, transactionField: mapperField });
                        mapperField.fieldOptionIds = fieldOptionIds;
                        mapperField.fieldValue = fieldValue;
                        break;
                    }
                    case FormFieldTypeEnum.Lookup: {
                        const { fieldOptionIds, fieldValue } = this.formatLookupValue({
                            formField,
                            transactionField: mapperField,
                            dataRegisterVersions: (relatedLookupData.dataRegisters ?? [])
                                .filter((dr) => dr.dataRegisterVersions?.length)
                                .map((dr) => dr.dataRegisterVersions[0]),
                            dataRegisterTransactions: relatedLookupData.dataRegisterTransactions,
                        });
                        mapperField.fieldOptionIds = fieldOptionIds;
                        mapperField.fieldValue = fieldValue;
                        break;
                    }

                    case FormFieldTypeEnum.Duration: {
                        if (mapperField.fieldValue && isNaN(+mapperField.fieldValue)) {
                            mapperField.fieldValue = convertDurationToMinutes(mapperField.fieldValue)?.toString();
                        }
                        break;
                    }
                    case FormFieldTypeEnum.Calculation: {
                        if (formField.configuration?.calculationFormula?.dataType === FormFieldTypeEnum.Duration) {
                            if (mapperField.fieldValue && isNaN(+mapperField.fieldValue)) {
                                mapperField.fieldValue = convertDurationToMinutes(mapperField.fieldValue)?.toString();
                            }
                        }
                        break;
                    }
                    case FormFieldTypeEnum.UserLookup:
                        {
                            const searchValues = Array.isArray(mapperField.fieldValue) ? mapperField.fieldValue : [mapperField.fieldValue];
                            const users = (userData || []).filter((user) => searchValues.includes(user.id));
                            if (users?.length) {
                                mapperField.fieldValue = users.map((user) => `${user.firstName} ${user.secondName}`).join(', ');
                                mapperField.fieldOptionIds = users.map((user) => user.id);
                            }
                        }
                        break;
                    case FormFieldTypeEnum.RoleLookup:
                        {
                            const searchValues = Array.isArray(mapperField.fieldValue) ? mapperField.fieldValue : [mapperField.fieldValue];
                            const roles = (roleData || []).filter((r) => searchValues.includes(r.id));
                            if (roles?.length) {
                                mapperField.fieldValue = roles.map((r) => r.name).join(', ');
                                mapperField.fieldOptionIds = roles.map((r) => r.id);
                            }
                        }
                        break;
                }

                return dataRegisterTransactionFieldRepository.create(mapperField);
            });

            const transactionFieldStyles = this.getTransactionFieldStyles(transaction.id, formVersion.fields, updateFields);

            const transactionFieldIds = updateFields.map((field) => field.id);
            const overrideValues = await transactionFieldOverrideRepository.find({
                where: {
                    dataRegisterTransactionFieldId: In(transactionFieldIds),
                    type: OverrideTypeEnum.User,
                    status: Not(OverrideStatusEnum.UnOverride),
                },
            });
            // Check Override validation value
            const activeOverrides: DataRegisterTransactionFieldOverrideEntity[] = await this.combineValidationValue({
                originTransactionFieldOverrides,
                overrideValues: overrideValues,
                updatedFieldStyles: transactionFieldStyles,
                updateFields,
            });

            //need to manual change updatedAt
            const result = (await dataRegisterTransactionRepository.save({ ...transaction, updatedAt: new Date().toISOString() })) as
                | DataRegisterTransactionEntity
                | DataRegisterTransactionTenancyEntity;

            const [updatedFields, additionalFields] = await Promise.all([
                dataRegisterTransactionFieldRepository.save(updateFields),
                this._updateAdditionalFields({
                    payload: request.additionalFields,
                    transactionId: transaction.id,
                    dataRegisterAdditionalFieldRepository,
                    dataRegisterAutoPopulateContextRepository,
                }),
            ]);

            // Push MQTT Register Updated event for Widget
            this.publishMQTTRegisterChangedEvent({
                accountId,
                registerId: request.dataRegisterId,
            });

            this.copyFieldStyles({
                styleAndOverrideFields: request.styleAndOverrideFields,
                transactionFieldStyles,
                updatedFields,
            });

            if (transactionFieldStyles?.length) {
                await dataRegisterTransactionFieldStyleRepository.save(transactionFieldStyles);
            }

            await updateDrActiveOverrideRecords({ activeOverrides, transactionFieldOverrideRepo: transactionFieldOverrideRepository });
            await addDrSystemOverrideRecords({
                originTransactionFieldOverrides,
                updateFields: updatedFields,
                transactionFieldOverrideRepo: transactionFieldOverrideRepository as Repository<DataRegisterTransactionFieldOverrideEntity>,
            });

            // update fields value
            result.transactionFields = updatedFields;
            this.detectAndPublishFieldChangedEvent({
                oldFields: transaction.transactionFields,
                newFields: updatedFields,
                aggregateId: transaction.id,
                updatedTransaction: result,
            }).catch(console.error);

            const fieldChanged = this.getChangedFields(transaction.transactionFields, updatedFields);
            const additionalFieldChanged = this.getChangedAdditionalFields(oldTransaction.additionalFields, additionalFields);

            const displayAttrs: string[] = formVersion?.displayAttributes || [];
            let newDisplayValue = '';
            if (fieldChanged.length) {
                const fieldChangeIdSet = new Set(fieldChanged.map((field) => field.fieldId));
                const hasNewDisplayValue = displayAttrs.some((fieldId) => fieldChangeIdSet.has(fieldId));
                if (hasNewDisplayValue) {
                    const displayFields = updatedFields.filter((field) => displayAttrs.includes(field.fieldId));
                    const sortedDisplayFields = _.sortBy(displayFields || [], (field) => displayAttrs.indexOf(field?.fieldId));
                    const fieldValues =
                        (sortedDisplayFields || []).map((transactionField) => {
                            const field = (formVersion.fields || []).find((f) => f.fieldId === transactionField.fieldId);
                            return getFieldFormat({
                                value: transactionField.fieldValue,
                                fieldType: field?.type as FormFieldTypeEnum,
                                ...(field?.configuration ?? {}),
                            });
                        }) || [];
                    newDisplayValue = TransactionFieldUtil.displayFieldLabels(fieldValues);
                }
            }

            const message = EventDrivenService.createCommonEvent({
                payload: { ...result, fieldChanged, newDisplayValue },
                aggregateId: result.id,
                tenantId: RequestContextService.accountId,
                type: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
                name: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
            });
            await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC, message);

            if (additionalFieldChanged.length || fieldChanged.length) {
                result.additionalFields = additionalFieldChanged;
                result.transactionFields = updatedFields.filter(({ fieldId }) => {
                    return (
                        fieldChanged.find((item) => item.fieldId === fieldId) ||
                        additionalFieldChanged.find((item) => item.fieldId === fieldId)
                    );
                });
                this.createChangeLog({
                    accountId,
                    sourceOfChange,
                    record: result,
                    previousRecord: oldTransaction,
                    type: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
                    name: DataRegisterEventEnum.DATA_REGISTER_RECORD_UPDATED,
                });
            } else {
                this._logger.info(`Skip unmodified data from the registration record log: ${sourceOfChange}-${id}`);
            }

            await this.publishDataPopulateEvent(transaction, fieldChanged);

            return true;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    public async updateRollupValidation({
        id,
        request,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
        dataRegisterTransactionFieldStyleRepository,
    }: {
        id: string;
        request: EditDataRegisterTransactionRequest;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        dataRegisterTransactionFieldStyleRepository: Repository<
            DataRegisterTransactionFieldStyleEntity | DataRegisterTransactionFieldStyleTenancyEntity
        >;
    }) {
        try {
            const transaction = await dataRegisterTransactionRepository.findOne({
                where: {
                    id,
                },
                relations: {
                    transactionFieldStyles: true,
                    transactionFields: true,
                    additionalFields: true,
                },
            });

            if (!transaction) {
                throw new NotFoundException('transaction_not_found');
            }

            const { transactionFields, dataRegisterTransactionId } = request as any;

            if (!transactionFields || !transactionFields.length) {
                throw new BadRequestException('transaction_fields_required');
            }

            const updatePromises = transactionFields.map(async (field) => {
                const fieldStyle = this.getValidationFieldStyle(field.fieldValue?.value || field.fieldValue); // Corrected typo
                const validationValue = Number(field.fieldValue?.value || field.fieldValue);

                if (isNaN(validationValue)) {
                    console.warn(`Skipping update for fieldId: ${field.fieldId} due to invalid number value:`, field.fieldValue);
                    // Skips this iteration, preventing invalid data from being processed.
                    return;
                }

                return Promise.all([
                    dataRegisterTransactionFieldRepository.update(
                        {
                            fieldId: field.fieldId,
                            dataRegisterTransactionId: dataRegisterTransactionId,
                        },
                        {
                            fieldValue: field.fieldValue?.value || field.fieldValue,
                            validationValue: validationValue,
                        },
                    ),
                    dataRegisterTransactionFieldStyleRepository.update(
                        {
                            fieldId: field.fieldId,
                            transactionId: dataRegisterTransactionId,
                        },
                        {
                            configuration: fieldStyle,
                        },
                    ),
                ]);
            });

            await Promise.all(updatePromises);

            return true;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }

    private getValidationFieldStyle(value: string): any {
        if (value == '100') {
            return {
                icon: 100,
                label: 'Passed',
            };
        }
        if (value == '300') {
            return {
                icon: 300,
                label: 'Failed',
            };
        }
        if (value == '200') {
            return {
                icon: 200,
                label: 'Warning',
            };
        }
    }

    private async copyOverrideRecords(params: {
        styleAndOverrideFields?: StyleAndOverrideFields;
        transaction: DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity;
        transactionFieldOverrideRepository: Repository<
            DataRegisterTransactionFieldOverrideEntity | DataRegisterTransactionFieldOverrideTenancyEntity
        >;
    }) {
        const { styleAndOverrideFields, transaction, transactionFieldOverrideRepository } = params;
        if (styleAndOverrideFields) {
            const overrideRecords: DataRegisterTransactionFieldOverrideEntity[] = [];
            Object.entries(styleAndOverrideFields).forEach(([fieldId, { override, style }]) => {
                if (override) {
                    const transactionField = transaction.transactionFields.find((field) => field.fieldId === fieldId);
                    if (!transactionField) return;
                    overrideRecords.push(
                        transactionFieldOverrideRepository.create({
                            comment: override.comment,
                            createdBy: override.createdBy,
                            createdByUser: override.createdByUser,
                            dependencyValues: override.dependencyValues,
                            fromValue: override.fromValue,
                            validationValue: override.validationValue,
                            dataRegisterTransactionFieldId: transactionField.id,
                            dataRegisterTransactionId: transaction.id,
                            status: OverrideStatusEnum.Active,
                            type: OverrideTypeEnum.User,
                        }),
                    );
                }
            });
            await transactionFieldOverrideRepository.update(
                {
                    dataRegisterTransactionFieldId: In(overrideRecords.map((record) => record.dataRegisterTransactionFieldId)),
                    status: OverrideStatusEnum.Active,
                },
                {
                    status: OverrideStatusEnum.Inactive,
                },
            );

            await transactionFieldOverrideRepository.save(overrideRecords);
        }
    }

    private copyFieldStyles(params: {
        styleAndOverrideFields?: StyleAndOverrideFields;
        transactionFieldStyles: DataRegisterTransactionFieldStyleEntity[];
        updatedFields: DataRegisterTransactionFieldEntity[];
    }) {
        const { styleAndOverrideFields, transactionFieldStyles, updatedFields } = params;
        if (styleAndOverrideFields) {
            Object.entries(styleAndOverrideFields).forEach(([fieldId, { style, validationValue }]) => {
                const transactionField = updatedFields.find((field) => field.fieldId === fieldId);
                if (!transactionField) return;
                if (style) {
                    const transactionFieldStyle = transactionFieldStyles.find((style) => style.fieldId === fieldId);
                    if (transactionFieldStyle) {
                        transactionFieldStyle.configuration = style.style;
                    } else {
                        transactionFieldStyles.push({
                            id: transactionField?.id,
                            fieldId,
                            configuration: style.style,
                            transactionId: transactionField.dataRegisterTransactionId,
                        });
                    }
                }
                if (validationValue) {
                    if (transactionField) {
                        transactionField.validationValue = validationValue;
                    }
                }
            });
        }
    }

    public detectAndPublishFieldChangedEvent = async ({
        oldFields,
        newFields,
        aggregateId,
        updatedTransaction,
    }: {
        oldFields: (DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity)[];
        newFields: EditDataRegisterTransactionFieldRequest[];
        aggregateId: string;
        updatedTransaction: DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity;
    }) => {
        const changes: (DataRegisterTransactionTenancyEntity | (DataRegisterTransactionEntity & { prevFieldValue: string }))[] = [];

        const autoVerRepo = (await this._getDataSource()).getRepository(
            this._claims.accountId ? AutomationVersionTenancyEntity : AutomationVersionEntity,
        );
        const autoVersions = await autoVerRepo.find({
            where: {
                eventType: DataRegisterEventEnum.DATA_REGISTER_RECORD_FIELD_UPDATED as any,
                contextVersionId: updatedTransaction.dataRegisterVersionId,
                status: AutomationVersionStatus.Published,
                isEnable: true,
            },
            relations: ['rules'],
        });

        const rules = autoVersions.flatMap((v) => v.rules).filter((r: AutomationRuleEntity | AutomationRuleTenancyEntity) => r.isEnable);

        const allTriggerFieldIds = rules.reduce((acc: string[], rule: AutomationRuleTenancyEntity) => {
            const triggerFields = rule.configuration?.node?.data?.configuration?.triggerFields ?? [];
            acc = [...acc, ...triggerFields];
            return acc;
        }, [] as string[]);

        const oldFieldMap = oldFields.reduce((map, item) => {
            map.set(item.id, item);
            return map;
        }, new Map<string, DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>());

        for (const newField of newFields) {
            if (allTriggerFieldIds.includes(newField.fieldId)) {
                let oldField = oldFieldMap.get(newField.id) ?? { fieldValue: '' };
                oldField.fieldValue = oldField?.fieldValue?.toString() ?? '';
                newField.fieldValue = newField?.fieldValue?.toString() ?? '';

                if (oldField?.fieldValue !== newField.fieldValue) {
                    changes.push({ ...newField, prevFieldValue: oldField?.fieldValue ?? '' });
                }
            }
        }

        if (changes.length > 0) {
            const { transactionFields, transactionFieldStyles, additionalFields, dataRegisterVersion, ...rest } = updatedTransaction;
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    ...rest,
                    fields: changes,
                },
                aggregateId,
                tenantId: RequestContextService.accountId,
                type: DataRegisterEventEnum.DATA_REGISTER_RECORD_FIELD_UPDATED,
                name: DataRegisterEventEnum.DATA_REGISTER_RECORD_FIELD_UPDATED,
            });
            await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC, message);
        }
    };

    private async publishDataPopulateEvent(
        transaction: DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity,
        fieldChanged: DataRegisterTransactionFieldTenancyEntity[],
    ) {
        if (!fieldChanged?.length) {
            return;
        }
        const transactionData = {
            transactionFields: fieldChanged?.map((field) => {
                return {
                    fieldId: field.fieldId,
                    fieldValue: field.fieldValue,
                    fieldType: field.fieldType,
                    fieldOptionIds: field.fieldOptionIds,
                } as TransactionFieldRequest;
            }),
        };

        const dataPopulateMessage = EventDrivenService.createCommonEvent({
            payload: {
                contextId: transaction.dataRegisterId,
                contextVersionId: transaction.dataRegisterVersionId,
                contextTransactionId: transaction.id,
                transaction: transactionData,
            },
            aggregateId: transaction.id,
            correlationId: uuid(),
            tenantId: this._claims.accountId,
            type: DataPopulateEventEnum.DATA_REGISTER_FIELD_CHANGED,
            name: DataPopulateEventEnum.DATA_REGISTER_FIELD_CHANGED,
        });
        await this._eventDrivenService.publishMessage(CommonTopicEnum.DATA_POPULATE_TOPIC, dataPopulateMessage);
    }

    private publishMQTTRegisterChangedEvent({ accountId, registerId }: { accountId: string; registerId: string }) {
        const widgetRegisterDatasourceTopic = `${accountId}/${WidgetMQTTTopicEnum.REGISTER_DATASOURCE_UPDATE}`;
        const widgetRegisterDatasourceMsg = {
            dataSourceId: registerId,
        };
        this._mqttService.publish(widgetRegisterDatasourceTopic, widgetRegisterDatasourceMsg, { qos: 1 }).catch((error) => {
            console.error(`Failed to publish MQTT message: ${error}`);
        });
    }
    //#endregion PUT

    //#region DELETE
    public async delete({
        id,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
    }: {
        id: string;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
    }) {
        if (!id) return false;
        try {
            const transaction = await dataRegisterTransactionRepository.findOneBy({ id });
            if (!transaction) {
                throw new NotFoundException('transaction_not_found');
            }

            const fields = await dataRegisterTransactionFieldRepository.find({ where: { dataRegisterTransactionId: id } });

            await dataRegisterTransactionFieldRepository.softRemove(fields);
            const result = await dataRegisterTransactionRepository.softRemove(transaction);

            // Push MQTT Register Updated event for Widget
            this.publishMQTTRegisterChangedEvent({
                accountId: this?._claims?.accountId,
                registerId: transaction?.dataRegisterId,
            });

            const message = EventDrivenService.createCommonEvent({
                payload: result,
                aggregateId: result.id,
                tenantId: RequestContextService.accountId,
                type: DataRegisterEventEnum.DATA_REGISTER_RECORD_DELETED,
                name: DataRegisterEventEnum.DATA_REGISTER_RECORD_DELETED,
            });
            await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_TOPIC, message);

            return !!result;
        } catch (err) {
            this._logger.error(err);
            throw err;
        }
    }
    //#endregion DELETE

    public async getDataRegisterTransactions({
        query,
        dataRegisterTransactionFieldRepository,
    }: {
        query: GetDataRegisterTransactionsQuery;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
    }) {
        if (!query) {
            return [];
        }

        const alias = 'dataRegisterTransactionFields';

        const builder = dataRegisterTransactionFieldRepository.createQueryBuilder(alias);

        const dataRegisterFilters = [];
        const filters = [];

        query.filters.forEach((filter) => {
            if (filter.field === 'dataRegisterId') {
                dataRegisterFilters.push(filter);
            } else {
                filters.push(filter);
            }
        });

        this._queryBuilder.applyQueryFilters(builder, alias, filters, []);

        try {
            const dataRegisterTransactionFields = await builder.getMany();

            const dto = this._mapper.mapArray(
                dataRegisterTransactionFields,
                DataRegisterTransactionFieldTenancyEntity,
                DataRegisterTransactionFieldDto,
            );

            return dto;
        } catch (err) {
            this._logger.error(err);
            throw new InternalServerErrorException(err);
        }
    }

    public async refreshDataRegisterTransaction({
        dataRegisterId,
        dataRegisterTransactionRepository,
        dataRegisterRepository,
        generalAutoPopulateSettingRepository,
    }: {
        dataRegisterId: string;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        dataRegisterRepository: Repository<DataRegisterEntity | DataRegisterTenancyEntity>;
        generalAutoPopulateSettingRepository: Repository<GeneralAutoPopulateSettingEntity | GeneralAutoPopulateSettingTenancyEntity>;
    }) {
        const registerTransactions = await dataRegisterTransactionRepository.find({
            where: {
                dataRegisterId: dataRegisterId,
            },
        });

        const register = await dataRegisterRepository.findOneBy({ id: dataRegisterId });

        const populate = await generalAutoPopulateSettingRepository.find({
            where: {
                dataSourceType: 'MDS' as any,
                builderId: dataRegisterId,
                builderVersionId: register.activeVersionId,
            },
        });

        const transactionFields = populate?.reduce((obj, item) => ((obj[item.targetFieldId] = ''), obj), {});

        for (const transaction of registerTransactions) {
            const message = EventDrivenService.createCommonEvent({
                payload: {
                    contextId: transaction.externalId,
                    contextType: EXTERNAL_DATA_SOURCE_TYPE.MDS,
                    contextSource: null,
                    transaction: transactionFields,
                    dataRegisterId: dataRegisterId,
                },
                aggregateId: transaction.id,
                tenantId: RequestContextService.accountId,
                type: DataPopulateEventEnum.EXTERNAL_DATA_FIELD_CHANGED,
                name: DataPopulateEventEnum.EXTERNAL_DATA_FIELD_CHANGED,
            });

            await this._eventDrivenService.publishMessage(CommonTopicEnum.DATA_POPULATE_TOPIC, message);
        }
    }

    //#region Dump data
    public async dumpDataRegisterTransaction() {
        const queryRunner = this._datasource.createQueryRunner();

        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const dataRegisterId = 'c0c1b4db-1fb3-4d86-a819-a1e706481aa5';

            const fieldIs = [
                '618296b8-b553-479d-818b-e9423c76f1c0', // name
                'ac56d41e-9c4d-40a4-9049-8b07819c294f', // Short name
                'bef95186-1f5f-4be3-ab99-46871505fbe9', // Code
                '6fca8dfb-8d71-435e-ab9f-35e621c32c36', // Description
                '6db968ed-de22-46ce-875a-cf6aa2b27866', // Calculation
                '8542a73c-8387-48ef-a5f6-e7288494e603', // Format
                '306bb591-7344-42f8-88f8-bbf644c971b4', // Number
            ];

            for (let i = 0; i < 10000; i++) {
                const transaction = new DataRegisterTransactionTenancyEntity();
                transaction.dataRegisterId = dataRegisterId;

                const entity = await queryRunner.manager.save(transaction);

                const transFields = fieldIs.map((field) => {
                    const transField = new DataRegisterTransactionFieldTenancyEntity();
                    transField.fieldId = field;
                    transField.fieldValue = this.generateRandomValue();
                    transField.dataRegisterTransactionId = entity.id;
                    return transField;
                });
                await queryRunner.manager.save(transFields);
            }

            await queryRunner.commitTransaction();
            return true;
        } catch (err) {
            await queryRunner.rollbackTransaction();
            throw new InternalServerErrorException(err);
        } finally {
            // Release the query runner
            await queryRunner.release();
        }
    }

    private generateRandomValue(): string {
        return this.generateRandomString(Math.floor(Math.random() * 1000));
    }

    private generateRandomString(length: number): string {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            result += characters.charAt(randomIndex);
        }
        return result;
    }
    //#endregion Dump data

    //#region PRIVATE METHODS

    public async getRelatedLookupData({
        formVersion,
        transactionFields,
        dataRegisterRepository,
        dataRegisterTransactionRepository,
    }: {
        transactionFields: Array<DataRegisterTransactionFieldTenancyEntity | EditDataRegisterTransactionFieldRequest>;
        formVersion: DataRegisterVersionTenancyEntity;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity> | Repository<DataRegisterEntity>;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity> | Repository<DataRegisterTransactionEntity>;
    }) {
        const { drIds, drTransactionIds } = transactionFields.reduce(
            (prev, curr) => {
                const field = formVersion.fields?.find((f) => f.fieldId === curr.fieldId);

                if (field?.type !== FormFieldTypeEnum.Lookup) {
                    return prev;
                }

                const transactionFieldValues =
                    field?.configuration?.mode === 'multiple' ? (curr.fieldValue?.split(',') ?? []) : [curr.fieldValue ?? ''];
                switch (field?.configuration?.dataset) {
                    case LookupFieldDataSource.MASTER_DATA: {
                        //TODO
                        prev.masterIds = [];
                        prev.masterDataIds = [];
                        break;
                    }

                    case LookupFieldDataSource.DATA_REGISTER:
                    default: {
                        prev.drTransactionIds = Array.from(new Set([...prev.drTransactionIds, ...transactionFieldValues].filter(Boolean)));
                        prev.drIds = Array.from(new Set([...prev.drIds, field?.configuration?.targetId ?? ''].filter(Boolean)));

                        break;
                    }
                }
                return prev;
            },
            { drIds: [] as string[], drTransactionIds: [] as string[], masterIds: [] as string[], masterDataIds: [] as string[] },
        );

        const relatedLookupData = {
            dataRegisters: [],
            dataRegisterTransactions: [],
            //...master data
        };

        if (drIds.length && drTransactionIds.length) {
            const [dataRegisters, dataRegisterTransactions] = await Promise.all([
                dataRegisterRepository
                    .createQueryBuilder('dataRegister')
                    .leftJoinAndSelect('dataRegister.dataRegisterVersions', 'dataRegisterVersion')
                    .where({
                        id: In(drIds),
                    })
                    .andWhere('dataRegisterVersion.version = dataRegister.activeVersion')
                    .getMany(),
                dataRegisterTransactionRepository.find({
                    where: {
                        id: In(drTransactionIds),
                    },
                    relations: ['transactionFields'],
                }),
            ]);

            relatedLookupData.dataRegisters = dataRegisters.filter((d) => d.dataRegisterVersions?.length);

            relatedLookupData.dataRegisterTransactions = dataRegisterTransactions;
        }

        return relatedLookupData;
    }

    private async _getFieldConfiguration({
        dataRegisterFieldRepository,
        fieldIds,
        dataRegisterVersionId,
    }: {
        fieldIds: string[];
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
        dataRegisterVersionId: string;
    }): Promise<DataRegisterFieldTenancyEntity[]> {
        const data = await dataRegisterFieldRepository.find({
            where: {
                fieldId: In(fieldIds),
                dataRegisterVersionId,
            },
            select: ['id', 'fieldId', 'configuration', 'type'],
        });

        return data;
    }

    private _getDataRegisterOptions = async ({
        query,
        dataRegisterTransactionRepository,
        dataRegisterRepository,
        dataRegisterTransactionFieldRepository,
        dataRegisterVersionRepository,
        dataRegisterFieldRepository,
    }: {
        query: MultipleFilterRequestDto | LookupQuery;
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>;
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>;
        dataRegisterVersionRepository: Repository<DataRegisterVersionTenancyEntity | DataRegisterVersionEntity>;
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>;
    }): Promise<GetDataRegisterLookupTransactionResponse> => {
        const { filters, skip, take, order } = query;

        const filterMap = filters.reduce(
            (acc, filter) => {
                acc[filter.field] = [...(acc[filter.field] || []), filter];
                return acc;
            },
            {} as Record<string, FilterOptionDto[]>,
        );

        const dataRegisterFilter = filterMap['dataRegisterId'];

        const dataRegisterId = dataRegisterFilter?.[0]?.value as string;
        if (!dataRegisterId) {
            throw new BadRequestException('data_register_id_required');
        }

        const dataRegisters = await this._dataRegisterWithActiveVersionService.getDataRegisterWithActiveVersion({
            dataRegisterIds: [dataRegisterId],
            dataRegisterRepository,
            dataRegisterVersionRepository,
            dataRegisterFieldRepository,
        });

        if (!dataRegisters?.length || !dataRegisters[0]?.dataRegisterVersions?.length) {
            throw new NotFoundException('data_register_version_not_found');
        }
        const dataRegister = dataRegisters[0];
        const dataRegisterVersion = dataRegister.dataRegisterVersions[0];

        const defaultValueFilter = filterMap['defaultValue'];
        const defaultValuesFilter = filterMap['defaultValues'];
        let optionIds = filterMap['optionIds'];
        const displayAttributes = dataRegisterVersion.displayAttributes || [];
        const searchTerm = filterMap['searchTerm']?.[0]?.value;
        const queryFilterDataConditionString = filterMap['dataConditions']?.[0]?.value as string;

        const dataConditions = queryFilterDataConditionString ? JSON.parse(queryFilterDataConditionString) : [];

        if (dataConditions?.length) {
            if (optionIds) {
                optionIds.push({
                    field: 'optionIds',
                    operator: OperatorType.in,
                    value: dataConditions,
                });
            } else {
                optionIds = [
                    {
                        field: 'optionIds',
                        operator: OperatorType.in,
                        value: dataConditions,
                    },
                ];
            }
        }

        try {
            const filterCondition = filterMap['filterCondition']?.[0].value;

            if (!filterCondition) {
                const alias = 'dataRegisterTransaction';

                const qb = dataRegisterTransactionRepository
                    .createQueryBuilder(alias)
                    .leftJoin(`${alias}.dataRegister`, 'dataRegister')
                    .where(`${alias}.dataRegisterId = :dataRegisterId`, { dataRegisterId });

                if (searchTerm) {
                    qb.leftJoin(`${alias}.transactionFields`, 'transactionFields');
                    this._generateFilters(qb, displayAttributes, searchTerm as string, `transactionFields`);
                }

                if (optionIds?.length) {
                    const ids = _.uniq(_.compact(_.flatMap(optionIds.map((filter) => filter.value))));
                    qb.andWhere(`${alias}.id IN (:...ids)`, { ids });
                }

                this._queryBuilder.applySorters(qb, alias, [{ field: 'createdAt', order: OrderType.DESC }]);
                const [data, total] = await qb.skip(skip).take(take).getManyAndCount();

                if (!data.length) return { data: [], total: 0, type: dataRegister.type };

                const defaultValue = defaultValueFilter?.[0]?.value as string;

                const defaultValues = _.compact((defaultValuesFilter?.[0]?.value ?? []) as string[]);
                const uniqDefaultValues = _.compact(uniq([...(defaultValues || []), defaultValue]))?.filter((item) => validate(item));
                if (uniqDefaultValues.length) {
                    const defaultEntities = await dataRegisterTransactionRepository.findBy({
                        id: In(uniqDefaultValues),
                    });

                    data.push(...defaultEntities);
                }

                const _data = await this._getDataWithField({
                    displayAttributes,
                    defaultValues: uniqDefaultValues,
                    dataRegisterTransactionEntities: data,
                    dataRegisterTransactionRepository,
                    dataRegisterTransactionFieldRepository,
                    fields: dataRegisterVersion?.fields || [],
                });
                return {
                    data: _data,
                    total: total,
                    type: dataRegister.type,
                };
            } else {
                const selectFields = displayAttributes || [];
                const transIds = _.compact(_.flatMap(optionIds?.map((filter) => filter.value)));

                const normalizedFilters = [];

                const { data } = await this._transQueryBuilder.filterAndSortByFields({
                    selectFields: selectFields,
                    normalizedFilters: normalizedFilters,
                    dataRegisterId: dataRegisterId as string,
                    additionalQuery: filterCondition as string,
                    searchTerm: searchTerm as string,
                    transactionIds: transIds.length ? (transIds as string[]) : undefined,
                    order,
                    skip,
                    take,
                });

                if (!data.length) return { ...PAGINATION_EMPTY_DATA, type: dataRegister.type };

                const alias = 'dataRegisterTransaction';

                const qb = dataRegisterTransactionRepository
                    .createQueryBuilder(alias)
                    .leftJoin(`${alias}.dataRegister`, 'dataRegister')
                    .where(`${alias}.dataRegisterId = :dataRegisterId`, { dataRegisterId })
                    .andWhere(`${alias}.id IN (:...ids)`, { ids: data })
                    .leftJoin(`${alias}.transactionFields`, 'transactionFields');

                this._queryBuilder.applySorters(qb, alias, [{ field: 'createdAt', order: OrderType.DESC }]);
                const [transData, transTotal] = await qb.skip(skip).take(take).getManyAndCount();

                if (!data.length) return { ...PAGINATION_EMPTY_DATA, type: dataRegister.type };

                const defaultValue = defaultValueFilter?.[0]?.value as string;

                const defaultValues = _.compact(defaultValuesFilter?.flatMap((filter) => filter.value)) as string[];

                const _data = await this._getDataWithField({
                    dataRegisterTransactionEntities: transData,
                    dataRegisterTransactionRepository,
                    dataRegisterTransactionFieldRepository,
                    displayAttributes,
                    fields: dataRegisterVersion?.fields || [],
                    defaultValues: uniq([...defaultValues, defaultValue]),
                });
                return {
                    data: _data,
                    total: transTotal,
                    type: dataRegister.type,
                };
            }
        } catch (error) {
            console.error(error);
            throw new InternalServerErrorException(error.message);
        }
    };

    private async _getDataWithField({
        dataRegisterTransactionEntities,
        dataRegisterTransactionRepository,
        dataRegisterTransactionFieldRepository,
        displayAttributes,
        fields,
        defaultValues,
    }: {
        dataRegisterTransactionEntities: DataRegisterTransactionTenancyEntity[];
        displayAttributes: string[];
        dataRegisterTransactionRepository: Repository<DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity>;
        dataRegisterTransactionFieldRepository: Repository<DataRegisterTransactionFieldTenancyEntity | DataRegisterTransactionFieldEntity>;
        defaultValues?: string[];
        fields: DataRegisterFieldEntity[];
    }): Promise<DataRegisterTransactionOptionDto[]> {
        const ids = dataRegisterTransactionEntities.map((d) => d.id);

        if (defaultValues?.length) {
            defaultValues.forEach((defaultValue) => {
                !ids.includes(defaultValue) && ids.push(defaultValue);
            });
        }

        if (!ids.length) {
            return [];
        }
        // Due to performance issues
        // const dataWithFields = await dataRegisterTransactionRepository
        //     .createQueryBuilder('drt')
        //     .leftJoinAndSelect('drt.transactionFields', 'tf')
        //     .where('drt.id IN (:...ids)', { ids })
        //     .getMany();
        const dataWithFields = await dataRegisterTransactionFieldRepository.findBy({ dataRegisterTransactionId: In(ids) });

        return this._toOptions({
            entities: dataRegisterTransactionEntities || [],
            displayAttributes,
            fields,
            transactionFields: dataWithFields,
        });
    }

    private _toOptions({
        displayAttributes,
        entities,
        fields = [],
        transactionFields = [],
    }: {
        entities: DataRegisterTransactionTenancyEntity[];
        displayAttributes: string[];
        fields: DataRegisterFieldEntity[];
        transactionFields: (DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity)[];
    }): DataRegisterTransactionOptionDto[] {
        if (entities.length) {
            const result = entities.map((entity) => {
                entity.transactionFields = transactionFields.filter((item) => item.dataRegisterTransactionId === entity.id);
                const dto = this._mapper.map(entity, DataRegisterTransactionTenancyEntity, DataRegisterTransactionDto);

                const displayFields = dto.transactionFields.filter((field) => displayAttributes.includes(field.fieldId));
                const sortedDisplayFields = _.sortBy(displayFields || [], (field) => (displayAttributes || []).indexOf(field?.fieldId));
                const fieldValues =
                    (sortedDisplayFields || []).map((transactionField) => {
                        const field = fields.find((f) => f.fieldId === transactionField.fieldId);
                        return getFieldFormat({
                            value: transactionField.fieldValue,
                            fieldType: field?.type as FormFieldTypeEnum,
                            ...(field?.configuration ?? {}),
                        });
                    }) || [];
                const label = TransactionFieldUtil.displayFieldLabels(fieldValues);
                return {
                    ...dto,
                    label,
                } satisfies DataRegisterTransactionOptionDto;
            });

            return result;
        }

        return [];
    }

    private _generateFilters<T>(qb: SelectQueryBuilder<T>, fields: string[], searchTerm: string, alias: string): void {
        if (!fields.length) return;
        qb.andWhere(
            new Brackets((qbInner) => {
                fields.forEach((filter, index) => {
                    // TODO: replace ILIKE with full text search
                    qbInner.orWhere(`(${alias}.fieldId = :id_${index} AND ${alias}.fieldValue ILIKE :value_${index})`, {
                        [`id_${index}`]: filter,
                        [`value_${index}`]: `%${searchTerm}%`,
                    });
                });
            }),
        );
    }

    private async _preProcessDataFields(
        registerFields: DataRegisterFieldTenancyEntity[],
        transactionFields: EditDataRegisterTransactionFieldRequest[],
        relatedLookupData: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ) {
        if (!transactionFields) {
            return;
        }

        transactionFields.forEach((field) => {
            const registerField = registerFields?.find((item) => item.fieldId === field.fieldId);
            if (registerField && !field.fieldValue) {
                try {
                    const formulaObject = registerField?.configuration?.['calculationFormula'] as FormulaSettingType;
                    const variableValue = transactionFields.map((item) => {
                        return {
                            id: item.fieldId,
                            type: item.fieldType,
                            value: item.fieldValue,
                        } as DataFieldDto;
                    });
                    const mainFormula = this.getMainFormula(formulaObject?.formulas ?? [], variableValue);
                    if (!mainFormula) {
                        return;
                    }
                    const { variableMappings } = mainFormula;
                    // get related field for formula
                    const values: any = [];
                    variableMappings.forEach((item: VariableMapping) => {
                        if (!item?.field) {
                            return;
                        }
                        let value = variableValue?.find((v) => v.id === item.field?.id)?.value;
                        if (value && FormFieldTypeEnum.Lookup === item.field.type && item.field.mode != 'multiple') {
                            const lookupFieldData = this.getLookupData(
                                value,
                                item?.lookupData?.field?.id ?? '',
                                item?.lookupData?.field?.type,
                                relatedLookupData,
                            );
                            value = lookupFieldData;
                        }
                        values.push(value);
                    });

                    const value = this._formulaService.calculate(
                        formulaObject.dataType,
                        field.defaultValue,
                        mainFormula as FormulaDto,
                        values,
                    );

                    field.fieldValue = value ? value.toString() : null;
                } catch (error) {
                    console.error(error);
                }
            }
        });
    }

    private getMainFormula(formulas: CalculationFormula[], value: DataFieldDto[]) {
        const watchingConditionFields: string[] = [];
        const values: any[] = [];
        formulas?.forEach((formula: any) => {
            const rules = formula?.conditions?.children1;
            if (rules) {
                getRelatedFields((rules as JsonItem[]) || [], watchingConditionFields);
            }
        });

        watchingConditionFields.map((field) => {
            values.push(value?.find((item) => item.id === field)?.value);
        });

        return this.getSatisfiedFormula(formulas ?? [], watchingConditionFields, values);
    }

    private getSatisfiedFormula = (
        formulas: CalculationFormula[],
        watchingConditionFields: string[],
        watchedConditionFieldValues: any[],
    ) => {
        if (!formulas?.length) {
            return null;
        }
        const activeFormulas: CalculationFormula[] = [];
        formulas.forEach((formula) => {
            const condition = formula.conditions;
            if (!condition?.children1?.length) {
                activeFormulas.push(formula);
                return;
            }
            const satisfyCondition = this.executeConditions(condition, watchingConditionFields, watchedConditionFieldValues, false);
            if (satisfyCondition) {
                activeFormulas.push(formula);
            }
        });
        // if there are multiple satisfied conditions then return first formula from the list
        return activeFormulas[0];
    };

    private executeConditions = (
        visibilityConditions: JsonTree | undefined,
        watchingFields: string[],
        watchedValues: any[],
        defaultValue?: boolean,
    ): boolean => {
        if (!watchingFields?.length) {
            return defaultValue?.toString() === 'true';
        }

        try {
            const engine = new LogicEngine();
            const variables = watchingFields.reduce(
                (acc, field, index) => {
                    acc[field] = watchedValues[index];
                    return acc;
                },
                {} as Record<string, any>,
            );

            const formatConditions = formatCondition(visibilityConditions) as any;
            const jsonLogic = QbUtils.jsonLogicFormat(QbUtils.loadTree(formatConditions as JsonTree), CoreConfig);
            if (jsonLogic.logic) {
                const evalResult = engine.run(jsonLogic.logic, variables);
                return evalResult;
            }
            return true;
        } catch (error) {
            console.error('Failed to execute conditions');
        }
        return defaultValue ?? false;
    };

    private getLookupData(
        lookupRecordId: string,
        lookupFieldId: string,
        lookupFieldType: FormFieldTypeEnum,
        relatedLookupData: {
            dataRegisters: any[];
            dataRegisterTransactions: any[];
        },
    ) {
        if (!relatedLookupData?.dataRegisterTransactions) {
            return null;
        }
        const record = relatedLookupData.dataRegisterTransactions?.find((item) => item.id === lookupRecordId);
        if (!record) {
            return null;
        }
        const data = record.transactionFields?.find((item: any) => item.fieldId === lookupFieldId)?.fieldValue;
        if (!data) {
            return data;
        }
        return this.convertVariableValue(lookupFieldType, data);
    }

    private convertVariableValue = (type: FormFieldTypeEnum | undefined, value: any) => {
        if (!type) {
            return null;
        }

        let formattedValue: string | number | null | Duration | Date = '';

        switch (type) {
            case FormFieldTypeEnum.DatePicker:
                formattedValue = value ? new Date(value) : null;
                break;
            case FormFieldTypeEnum.TimePicker:
                formattedValue = UtilsService.convertTimeStringToDate(value as string);
                break;

            case FormFieldTypeEnum.Duration:
                formattedValue = UtilsService.convertStringToDuration(
                    UtilsService.convertMinutesToDuration({ value: value, format: DurationFormatEnum.DHM }),
                );
                break;

            case FormFieldTypeEnum.Number:
                formattedValue = Number(value);
                break;

            default:
                formattedValue = value;
                break;
        }
        return formattedValue;
    };

    private _formatSelectValue({
        formField,
        transactionField,
    }: {
        formField: DataRegisterFieldTenancyEntity;
        transactionField: DataRegisterTransactionFieldTenancyEntity;
    }): {
        fieldValue: string;
        fieldOptionIds: string[];
    } {
        const result = {
            fieldValue: '',
            fieldOptionIds: [],
        };
        switch (formField.configuration?.mode) {
            case 'single':
                {
                    const fieldOptionId = transactionField.fieldValue; //id value
                    const option = ((formField.configuration?.options as { label: string; value: string }[]) || []).find(
                        (o) => o.value === fieldOptionId,
                    );

                    result.fieldValue = option?.label ?? '';
                    result.fieldOptionIds = _.compact([fieldOptionId]);
                }
                break;
            case 'multiple':
                {
                    const fieldOptionIds = _.compact(transactionField.fieldValue?.split(',') as string[]) || []; //id value
                    const options =
                        ((formField.configuration?.options as { label: string; value: string }[]) || []).filter((o) =>
                            fieldOptionIds.includes(o.value),
                        ) || [];

                    result.fieldValue = options.map((option) => option.label).join(',');
                    result.fieldOptionIds = fieldOptionIds;
                }
                break;
        }
        return result;
    }

    public formatLookupValue({
        formField,
        transactionField,
        dataRegisterVersions,
        dataRegisterTransactions,
    }: {
        formField: DataRegisterFieldTenancyEntity;
        transactionField: DataRegisterTransactionFieldTenancyEntity;
        dataRegisterVersions: DataRegisterVersionTenancyEntity[];
        dataRegisterTransactions: DataRegisterTransactionTenancyEntity[];
    }): {
        fieldValue: string;
        fieldOptionIds: string[];
    } {
        const result = {
            fieldValue: '',
            fieldOptionIds: [],
        };

        if (!transactionField.fieldValue) {
            return result;
        }

        const fieldOptionIds =
            formField?.configuration?.mode === 'multiple'
                ? transactionField.fieldValue?.split(',')
                : _.compact([transactionField.fieldValue]);
        let displayLabels: string[] = [];
        switch (formField?.configuration?.dataset) {
            case LookupFieldDataSource.DATA_REGISTER: {
                displayLabels = fieldOptionIds.map((id) => {
                    const transaction = dataRegisterTransactions.find((drt) => drt.id === id);
                    const dataRegisterVersion = dataRegisterVersions.find((dr) => dr.dataRegisterId === transaction?.dataRegisterId);
                    const displayFieldIds = dataRegisterVersion?.displayAttributes?.length
                        ? dataRegisterVersion.displayAttributes
                        : dataRegisterVersion?.fields?.filter((f) => f.isDefault)?.map((f) => f.fieldId) || [];
                    const sortedDisplayFieldIdx = displayFieldIds.reduce((prev, fId, idx) => {
                        if (!prev.has(fId)) prev.set(fId, idx);
                        return prev;
                    }, new Map());
                    let displayTransactionFields = (transaction?.transactionFields || []).filter((tf) =>
                        displayFieldIds.includes(tf.fieldId),
                    );
                    displayTransactionFields.sort(
                        (prev, next) => sortedDisplayFieldIdx.get(prev.fieldId) - sortedDisplayFieldIdx.get(next.fieldId),
                    );
                    const transactionFieldValues = displayTransactionFields?.map((f) => f.fieldValue ?? '') || [];
                    return TransactionFieldUtil.displayFieldLabels(transactionFieldValues);
                });
                break;
            }

            case LookupFieldDataSource.MASTER_DATA: {
                displayLabels = [];
                break;
            }
        }

        const label = displayLabels.join(', ');
        result.fieldValue = label;
        result.fieldOptionIds = fieldOptionIds;
        return result;
    }

    private _mapFieldConfiguration = async (
        data: Array<DataRegisterTransactionDto>,
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>,
        dataRegisterId: string,
        dataRegisterFieldRepository: Repository<DataRegisterFieldTenancyEntity | DataRegisterFieldEntity>,
    ) => {
        const fieldIds = [];

        data?.forEach((value) => {
            value.transactionFields?.forEach((field) => {
                fieldIds.push(field.fieldId);
            });
        });

        if (fieldIds.length) {
            const dataRegister = await dataRegisterRepository.findOneBy({ id: dataRegisterId.toString() });
            const fields = await this._getFieldConfiguration({
                fieldIds,
                dataRegisterFieldRepository,
                dataRegisterVersionId: dataRegister.activeVersionId,
            });

            data?.forEach((value) => {
                for (let field of value.transactionFields) {
                    const _field = fields.find((item) => item.fieldId === field.fieldId);
                    if (_field) {
                        field.format = _field.configuration?.format;
                        field.type = _field?.type;
                    }
                }
            });
        }

        return data;
    };

    private async _updateAdditionalFields({
        payload,
        transactionId,
        dataRegisterAutoPopulateContextRepository,
        dataRegisterAdditionalFieldRepository,
    }: {
        payload: Array<DataRegisterAdditionalFieldDto>;
        transactionId?: string;
        dataRegisterAutoPopulateContextRepository: Repository<
            DataRegisterAutoPopulateContextEntity | DataRegisterAutoPopulateContextTenancyEntity
        >;
        dataRegisterAdditionalFieldRepository: Repository<DataRegisterAdditionalFieldEntity | DataRegisterAdditionalFieldTenancyEntity>;
    }): Promise<DataRegisterAdditionalFieldEntity[] | DataRegisterAdditionalFieldTenancyEntity[]> {
        let updateAdditionalFields = _.compact(payload || []);
        if (updateAdditionalFields?.length) {
            const existedAdditionalFields = await dataRegisterAdditionalFieldRepository.findBy({ transactionId });
            const deleteAdditionalFields = existedAdditionalFields.filter(
                (field) => !updateAdditionalFields.some((f) => f.id === field.id),
            );

            const updateContexts: Array<DataRegisterAutoPopulateContextEntity | DataRegisterAutoPopulateContextTenancyEntity> = [];
            updateAdditionalFields.forEach((item) => {
                item.transactionId = transactionId;
                item.additionalType = item.additionalType || item.configuration?.additionalType || item.type;
                if (!item.id) {
                    item.id = uuid();
                }
            });

            await dataRegisterAdditionalFieldRepository.softRemove(deleteAdditionalFields);
            const additionalFieldEntities = await dataRegisterAdditionalFieldRepository.save(updateAdditionalFields);

            const additionalFieldIds = updateAdditionalFields.map((field) => field.id);
            const allExistedContexts = await dataRegisterAutoPopulateContextRepository.find({
                where: {
                    additionalFieldId: In(additionalFieldIds),
                },
            });
            const deleteContexts = allExistedContexts.filter((item) =>
                deleteAdditionalFields.some((daf) => daf.id === item.additionalFieldId),
            );

            updateAdditionalFields.forEach((item) => {
                //dependencies contexts
                const existedContexts = allExistedContexts.filter((f) => f.additionalFieldId === item.id);
                const generalApSettingConfigs: GeneralAutoPopulateSettingConfig[] =
                    item.configuration?.setting?.autoPopulate?.general || [];

                //find context should be deleted
                const shouldDeleteContexts = existedContexts.filter(
                    (context) =>
                        !generalApSettingConfigs.some((setting) => {
                            const settingDataSource = this._getSettingDataSource(setting);
                            return settingDataSource === context.dataRegisterId && setting.targetFieldId === context.contextId;
                        }),
                );

                if (shouldDeleteContexts.length) {
                    deleteContexts.push(...shouldDeleteContexts);
                }

                //add context
                generalApSettingConfigs.map((setting) => {
                    const settingDataSource = this._getSettingDataSource(setting);
                    const isExisted = existedContexts.some(
                        (context) => settingDataSource === context.dataRegisterId && setting.targetFieldId === context.contextId,
                    );

                    if (!isExisted) {
                        updateContexts.push({
                            additionalFieldId: item.id,
                            contextId: setting.targetFieldId,
                            dataRegisterId: settingDataSource,
                            type: item.additionalType as unknown as FormFieldTypeEnum,
                        });
                    }
                });
            });

            /**
             * The context here aim to check dependencies when need to check if target field
             * of data source change, maybe setting will have duplicate con data source and target field
             * but this dependencies will only create 1 record with data source and target field
             */
            await dataRegisterAutoPopulateContextRepository.softRemove(deleteContexts);
            await dataRegisterAutoPopulateContextRepository.save(updateContexts);

            return additionalFieldEntities;
        }
    }

    private _getSettingDataSource(setting: GeneralAutoPopulateSettingConfig): string {
        if (EXTERNAL_DATA_SOURCE_TYPES.includes(setting.dataSourceType)) {
            const dataSource: string =
                EXTERNAL_DATA_SOURCE_MAPPING[
                    `${setting.dataSourceType?.toUpperCase()}${EXTERNAL_DATA_SOURCE__SEPARATE_MARK}${setting.dataSource?.toUpperCase()}`
                ];
            return dataSource ? dataSource : (setting.dataSource as string);
        }

        return setting.dataSource as string;
    }

    private validateTransactionData(
        fields: DataRegisterFieldTenancyEntity[],
        tranFields: DataRegisterTransactionFieldEntity[],
    ): {
        fieldId: string;
        code: DataValidationResult;
        label: string;
    }[] {
        if (!fields?.length || !tranFields?.length) {
            return null;
        }
        const result = [];
        for (let tranField of tranFields) {
            const field = fields.find((item) => item.fieldId === tranField.fieldId);
            if (!field) {
                continue;
            }

            const configs = field.configuration['ruleConfigs']?.[FormRuleType.DataValidation];
            if (!configs) {
                continue;
            }
            const fieldValidationResult = this.executeDataValidationConditions(configs, tranFields, fields);
            if (fieldValidationResult) {
                result.push({
                    fieldId: tranField.fieldId,
                    code: fieldValidationResult.code,
                    label: fieldValidationResult.label,
                });
            }
        }
        return result;
    }

    private getFieldStyles(
        fields: DataRegisterFieldTenancyEntity[],
        tranFields: DataRegisterTransactionFieldEntity[],
    ): {
        fieldId: string;
        style: { color: string; backgroundColor: string };
    }[] {
        if (!fields?.length || !tranFields?.length) {
            return null;
        }
        const result = [];
        for (let tranField of tranFields) {
            const field = fields.find((item) => item.fieldId === tranField.fieldId);
            if (!field) {
                continue;
            }

            const configs = field.configuration['styleConditions'] as StyleConditionType[];
            if (!configs) {
                if (field.configuration.style) {
                    result.push({ fieldId: tranField.fieldId, style: field.configuration.style });
                } else {
                    continue;
                }
            }
            const fieldStyles = this.executeStyleConditions(configs, tranFields);
            if (fieldStyles) {
                result.push({ fieldId: tranField.fieldId, style: fieldStyles });
            } else {
                if (field.configuration.style) {
                    result.push({ fieldId: tranField.fieldId, style: field.configuration.style });
                }
            }
        }
        return result;
    }

    private executeStyleConditions(conditions: any | undefined, tranFields: DataRegisterTransactionFieldEntity[]) {
        if (!conditions?.length) return null;

        let watchingConditionFields: string[] = [];
        const watchingConditionFieldValues: any[] = [];
        conditions?.forEach((formula: any) => {
            const rules = formula?.condition?.children1;
            if (rules) {
                getRelatedFields((rules as JsonItem[]) || [], watchingConditionFields);
            }
        });

        watchingConditionFields = uniq(watchingConditionFields);

        watchingConditionFields.map((field) => {
            let fieldValue: any = null;
            fieldValue = tranFields?.find((item) => item.fieldId === field)?.fieldValue;
            watchingConditionFieldValues.push(fieldValue);
        });

        for (let index = 0; index < conditions?.length; index++) {
            const { condition, style = {} } = conditions[index];
            const satisfyCondition = executeConditions(condition, watchingConditionFields, watchingConditionFieldValues, false);

            if (satisfyCondition) {
                return style;
            }
        }
        return null;
    }

    private executeDataValidationConditions(
        conditions: any | undefined,
        tranFields: DataRegisterTransactionFieldEntity[],
        fields: DataRegisterFieldEntity[],
    ) {
        if (!conditions?.length) return null;

        for (let index = 0; index < conditions?.length; index++) {
            const { condition, icon = {} } = conditions[index];
            const satisfyCondition = checkRowConditionFilters({
                collectionFilters: [condition],
                drFields: fields,
                fieldsOfRow: tranFields as any,
            });

            if (satisfyCondition) {
                return {
                    code: icon.name,
                    label: icon.label,
                };
            }
        }
        return null;
    }

    public pubGetTransactionFieldStyles(
        tranId: string,
        fields: DataRegisterFieldEntity[] | DataRegisterFieldTenancyEntity[],
        tranFields: DataRegisterTransactionFieldEntity[] | DataRegisterTransactionFieldTenancyEntity[],
    ) {
        return this.getTransactionFieldStyles(tranId, fields, tranFields);
    }

    public async getFieldStylesByDatRegisterFieldIds(
        ids: string[],
        repo: Repository<DataRegisterTransactionFieldStyleEntity | DataRegisterTransactionFieldStyleTenancyEntity>,
    ) {
        const fieldStyles = await repo.find({
            where: {
                id: In(ids),
            },
        });
        return fieldStyles;
    }

    private getTransactionFieldStyles(tranId: string, fields: DataRegisterFieldEntity[], tranFields: DataRegisterTransactionFieldEntity[]) {
        const validationResult = this.validateTransactionData(fields, tranFields);
        const fieldStyles = this.getFieldStyles(fields, tranFields);
        const updatedFieldStyles: DataRegisterTransactionFieldStyleTenancyEntity[] = [];
        if (validationResult?.length || fieldStyles?.length) {
            tranFields?.forEach((field) => {
                let configuration = {};

                const fieldValidation = validationResult?.find((item) => item.fieldId === field.fieldId);
                if (fieldValidation) {
                    configuration = {
                        ...configuration,
                        icon: fieldValidation?.code ?? '',
                        label: fieldValidation?.label ?? '',
                    };
                }

                const fieldStyle = fieldStyles?.find((item) => item.fieldId === field.fieldId);
                if (fieldStyle) {
                    configuration = {
                        ...configuration,
                        ...(fieldStyle?.style ?? {}),
                    };
                }

                if (fieldValidation || fieldStyle) {
                    updatedFieldStyles.push({
                        id: field.id,
                        fieldId: field.fieldId,
                        configuration,
                        transactionId: tranId,
                    });
                    if (fieldValidation) field.validationValue = fieldValidation?.code;
                }
            });
        }
        return updatedFieldStyles;
    }

    //#endregion PRIVATE METHODS
    private async getFieldValueCount(
        fieldId: string,
        registerId: string,
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>,
        transactionFields: any,
    ): Promise<{ id?: string; field_value?: string; field_value_count?: number }> {
        // Extract the field value from transaction fields
        const fieldValueEntry = transactionFields.find((el) => el.fieldId === fieldId);
        if (!fieldValueEntry) {
            return {}; // Early return if fieldValue is not found
        }

        const { fieldValue } = fieldValueEntry;

        // Build the query
        const fieldValueCount = await dataRegisterRepository
            .createQueryBuilder('dr')
            .select('dr.ID', 'id')
            .addSelect('drtf.field_value', 'field_value')
            .addSelect('COUNT(drtf.field_value)', 'field_value_count')
            .innerJoin('data_register_transactions', 'drt', 'dr.ID = drt.data_register_id')
            .innerJoin('data_register_transaction_fields', 'drtf', 'drt.ID = drtf.data_register_transaction_id')
            .where('drtf.field_id = :fieldId', { fieldId })
            .andWhere('dr.ID = :registerId', { registerId })
            .andWhere('drtf.field_value = :fieldValue', { fieldValue }) // Field value condition
            .groupBy('dr.ID, drtf.field_value')
            .having('COUNT(drtf.field_value) > 0')
            .getRawOne();

        return fieldValueCount ?? {}; // Use nullish coalescing for a more explicit fallback
    }

    private async getFieldValueCountByTransId(
        fieldId: string,
        registerId: string,
        dataRegisterRepository: Repository<DataRegisterTenancyEntity | DataRegisterEntity>,
        transactionFields: any,
        transactionId: string,
    ): Promise<{ id?: string; field_value?: string; field_value_count?: number }> {
        // Extract the field value from transaction fields
        const fieldValueEntry = transactionFields.find((el) => el.fieldId === fieldId);
        if (!fieldValueEntry) {
            return {}; // Early return if fieldValue is not found
        }

        const { fieldValue } = fieldValueEntry;

        // Build the query
        const fieldValueCount = await dataRegisterRepository
            .createQueryBuilder('dr')
            .select('dr.ID', 'id')
            .addSelect('drtf.field_value', 'field_value')
            .addSelect('COUNT(drtf.field_value)', 'field_value_count')
            .innerJoin('data_register_transactions', 'drt', 'dr.ID = drt.data_register_id')
            .innerJoin('data_register_transaction_fields', 'drtf', 'drt.ID = drtf.data_register_transaction_id')
            .where('drtf.field_id = :fieldId', { fieldId })
            .andWhere('dr.ID = :registerId', { registerId })
            .andWhere('drtf.field_value = :fieldValue', { fieldValue }) // Field value condition
            .andWhere('drt.ID != :transactionId', { transactionId }) // Exclude specific transaction ID
            .groupBy('dr.ID, drtf.field_value')
            .having('COUNT(drtf.field_value) > 0')
            .getRawOne();

        return fieldValueCount ?? {}; // Use nullish coalescing for a clearer fallback
    }

    private combineValidationValue = async (params: {
        updateFields: DataRegisterTransactionFieldEntity[];
        updatedFieldStyles: DataRegisterTransactionFieldStyleEntity[];
        overrideValues: DataRegisterTransactionFieldOverrideEntity[];
        originTransactionFieldOverrides: Record<string, any>;
    }) => {
        const { originTransactionFieldOverrides, overrideValues, updateFields, updatedFieldStyles } = params;
        const activeOverrides: DataRegisterTransactionFieldOverrideEntity[] = [];

        updateFields.forEach((transactionField) => {
            if (transactionField.validationValue === ValidationValueEnum.PASS) return;
            const fieldStyle = updatedFieldStyles.find((fs) => fs.id === transactionField.id);
            if (!fieldStyle) return;

            const overrides = overrideValues.filter((ov) => ov.dataRegisterTransactionFieldId === transactionField.id);
            const overrideRecord = getOverrideRecord(updateFields, overrides);
            if (!overrideRecord) return;
            activeOverrides.push(overrideRecord as DataRegisterTransactionFieldOverrideEntity);

            originTransactionFieldOverrides[transactionField.id].type = OverrideTypeEnum.User;

            transactionField.validationValue = overrideRecord.validationValue;

            fieldStyle.configuration = {
                icon: overrideRecord.validationValue,
                label: overrideRecord.comment,
                border: fieldStyle.configuration.icon,
            };
        });

        return activeOverrides;
    };

    private async checkDuplicateIdentifier({
        dataRegisterVersion,
        payload,
        dataRegisterTransactionFieldRepo,
        dataRegisterTransactionRepo,
        transactionId,
    }: {
        dataRegisterVersion: DataRegisterVersionTenancyEntity | DataRegisterVersionEntity;
        payload: EditDataRegisterTransactionRequest;
        dataRegisterTransactionFieldRepo: Repository<DataRegisterTransactionFieldEntity | DataRegisterTransactionFieldTenancyEntity>;
        dataRegisterTransactionRepo: Repository<DataRegisterTransactionEntity | DataRegisterTransactionTenancyEntity>;
        transactionId?: string;
    }): Promise<{
        result: boolean | 'duplicate_identifier' | 'missing_context_value';
        identifier: {
            activeId: string;
            contexts: string[];
            condition: string[];
            availableFields: string[];
        };
    }> {
        const checkResult = await this._identifierService.checkDuplicateIdentifier({
            dataRegisterTransactionFieldRepo: dataRegisterTransactionFieldRepo,
            dataRegisterVersion: dataRegisterVersion,
            dataRegisterTransactionRepo,
            payload: payload,
            transactionId,
        });

        if (_.isString(checkResult?.result)) {
            throw new BadRequestException(checkResult?.result);
        }

        return checkResult;
    }

    private getChangedFields(
        beforeChanged: DataRegisterTransactionFieldTenancyEntity[],
        afterChanged: DataRegisterTransactionFieldTenancyEntity[],
    ) {
        const changedFields: DataRegisterTransactionFieldTenancyEntity[] = [];
        afterChanged.forEach((field) => {
            const beforeField = beforeChanged.find((f) => f.fieldId === field.fieldId);

            if (field.fieldValue?.toString() !== beforeField?.fieldValue?.toString()) {
                changedFields.push(field);
            }
        });
        return changedFields;
    }

    private getChangedAdditionalFields = (
        beforeChanged: DataRegisterAdditionalFieldEntity[] | DataRegisterAdditionalFieldTenancyEntity[] = [],
        afterChanged: DataRegisterAdditionalFieldEntity[] | DataRegisterAdditionalFieldTenancyEntity[] = [],
    ) => {
        const changedFields = [];
        afterChanged.forEach((field) => {
            const beforeField = beforeChanged.find((f) => f.fieldId === field.fieldId);
            if (!beforeField) {
                changedFields.push(field);
                return;
            }

            const beforeConfig = this.convertToConfigObject(beforeField);
            const afterConfig = this.convertToConfigObject(field);

            const diffConfigs = _.differenceWith(Object.entries(beforeConfig), Object.entries(afterConfig), _.isEqual);
            if (diffConfigs.length > 0) {
                changedFields.push(field);
            }
        });
        return changedFields;
    };

    private convertToConfigObject(record: DataRegisterAdditionalFieldEntity | DataRegisterAdditionalFieldTenancyEntity) {
        if (_.isEmpty(record)) {
            return {};
        }
        const { configuration } = record;

        const {
            type,
            // display
            label,
            placeholder,
            labelPlacement,
            currency,
            style,
            visibility,
            visibilityConditions,
            readonly,
            format,
            styleConditions,
            uom,
            decimalPlace,
            thousandSeparator,
            decimalSeparator,
            numberType,
            //data
            mode,
            defaultValue,
            pickerType,
            dataConditions,
            options,
            optionIds,
            targetId,
            isSupportField,
            rollup,
            operator,
            defaultByCurrentUser,
            isAssignee,
            defaultRoles,
            // validation
            ruleConfigs,
            override,
            // auto populate
            setting,
            autoPopulate,
        } = configuration;

        return {
            type,
            display: {
                label,
                labelPlacement,
                placeholder,
                currency,
                style,
                visibility,
                visibilityConditions,
                readonly,
                format,
                styleConditions,
                uom,
                decimalPlace,
                thousandSeparator,
                decimalSeparator,
                numberType,
            },
            data: {
                defaultValue: defaultValue?.toString(),
                mode,
                pickerType,
                dataConditions,
                options,
                optionIds,
                targetId,
                isSupportField,
                rollup,
                operator,
                defaultByCurrentUser,
                isAssignee,
                defaultRoles,
            },
            validation: { ruleConfigs, override },
            autoPopulate: { setting, autoPopulate },
        };
    }

    private async createChangeLog({
        record,
        previousRecord,
        sourceOfChange,
        type,
        name,
        accountId,
    }: {
        record: DataRegisterTransactionTenancyEntity;
        previousRecord?: DataRegisterTransactionTenancyEntity;
        sourceOfChange: SourceOfChangeType;
        type: DataRegisterEventEnum;
        name: DataRegisterEventEnum;
        accountId: string;
    }) {
        const transformLogData = (accountId: string, record?: DataRegisterTransactionTenancyEntity | DataRegisterTransactionEntity) => {
            if (!record) {
                return null;
            }

            return accountId
                ? this._mapper.map(record, DataRegisterTransactionTenancyEntity, DataRegisterRecordChangeLogDto)
                : this._mapper.map(record, DataRegisterTransactionEntity, DataRegisterRecordChangeLogDto);
        };

        const changeLogMessage = EventDrivenService.createCommonEvent({
            payload: {
                current: transformLogData(accountId, record),
                previous: transformLogData(accountId, previousRecord),
                sourceOfChange,
            },
            aggregateId: record.id,
            tenantId: RequestContextService.accountId,
            type: type,
            name: name,
        });
        await this._eventDrivenService.publishMessage(DataRegisterTopicEnum.DATA_REGISTER_RECORD_CHANGE_LOG_TOPIC, changeLogMessage);
    }
}
