import { forwardRef, Module } from '@nestjs/common';
import { SharedModule } from '../../shared/shared.module';
import { SendGridWebhookController } from './controllers/sendGrid.controller';
import { EmailLogDataService } from './services/email-log.data.service';
import { WebhookService } from './services/webhook.service';

const controllers = process.env.MODULE !== 'api' ? [SendGridWebhookController] : [];
const providers = [WebhookService, EmailLogDataService];
@Module({
    imports: [forwardRef(() => SharedModule)],
    controllers: [...controllers],
    providers: [...providers],
    exports: [],
})
export class WebhookModule {}
