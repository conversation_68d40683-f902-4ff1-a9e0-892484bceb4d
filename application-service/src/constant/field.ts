import { DATA_REGISTER_NAME_FIELD_ID } from '../common/src/constant/field';

export const DEFAULT_TRANSACTION_FIELD_ID = 'a9e451fb-8efb-4a92-8705-eecc380f4d4a';
export const DEFAULT_STAGE_KPI_FIELD_ID = '20870815-9f89-4430-b5ab-3cfd4c5899b8';

export const DATA_PASSED_CODE = 100;
export const DATA_WARNING_CODE = 200;
export const DATA_ERROR_CODE = 300;
export const DATA_REGISTER_CODE_FIELD_ID = '491360b3-df41-4326-9bb5-5518baebd695';

export const CONDITION_VALIDATION_FIELD = '3e808b62-5062-4439-bc07-4b2073572f16';
export const CONDITION_OVERRIDE_FIELD = 'f3e1d30d-c8b3-4b1e-8315-712d4f63450a';

export const FROM_STAGE_FIELD = '1c4d9296-c761-40e3-be3a-c75d7d9a1094';
export const TO_STAGE_FIELD = '193db29f-b2b7-46cd-b7bf-c660cc0bc655';

export const STAGE_KPI_STATUS_FIELD = '679f1e4d-6984-41fb-8a20-9af3498072b7';

export const HIDDEN_VALUE = '******';

export const DATA_REGISTER_RECORD_ID = '07159963-e68a-4e96-bb88-e98973407583';

export const CREATED_FIELD_ID = '9b0f84c9-c33b-493b-9c0d-2ce5ad55a295';
export const UPDATED_FIELD_ID = '0c509685-b04a-4802-a83c-d20e04322da2';
export const PUBLISHED_FIELD_ID = 'b6e303c3-df86-4588-8e1d-936686c3e20d';
export const VERSION_FIELD_ID = 'ff37afd3-5592-4d33-acf6-7303b739e001';
export const STAGE_FIELD_ID = '7aae3930-e48e-4b81-b007-ad2a2706e6a4';

export const API_SYSTEM_FIELD_MAPPING: Record<string, string> = {
    [DEFAULT_TRANSACTION_FIELD_ID]: 'transaction_code',
    [CREATED_FIELD_ID]: 'created_at',
    [UPDATED_FIELD_ID]: 'updated_at',
    [PUBLISHED_FIELD_ID]: 'published_at',
    [VERSION_FIELD_ID]: 'version',
    [STAGE_FIELD_ID]: 'stage',
    [DATA_REGISTER_CODE_FIELD_ID]: 'code',
    [DATA_REGISTER_NAME_FIELD_ID]: 'name',
    [DATA_REGISTER_RECORD_ID]: 'record_id',
    [FROM_STAGE_FIELD]: 'from_stage',
    [TO_STAGE_FIELD]: 'to_stage',
    [CONDITION_VALIDATION_FIELD]: 'condition_validation',
    [CONDITION_OVERRIDE_FIELD]: 'condition_override',
};
