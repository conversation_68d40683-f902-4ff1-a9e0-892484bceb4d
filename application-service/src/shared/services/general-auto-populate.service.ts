import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as _ from 'lodash';
import { OverrideStatusEnum } from 'src/database/src/shared/enums/override-status.enum';
import { OverrideTypeEnum } from 'src/database/src/shared/enums/override-type.enum';
import {
    FieldStyleRequest,
    OverrideRecordRequest,
    StyleAndOverrideFields,
} from 'src/modules/data-register/dtos/requests/create-data-register-transaction.request';
import { In } from 'typeorm';
import { validate as isUUID } from 'uuid';
import { BaseHttpService, CacheService, ConfigService, LoggerService, UtilsService } from '../../common/src';
import { RequestContextService } from '../../common/src/application/context/AppRequestContext';
import {
    AP_EXTRA_CONFIG_ITSELF_FIELD_ID,
    AP_TARGET_ITSELF_FIELD_ID,
    EXTERNAL_DATA_SOURCE_TYPE,
    EXTERNAL_DATA_SOURCE_TYPES,
    MDS_ENTITY_FIELD_IDS,
} from '../../common/src/constant/field';
import { FilterOptionDto } from '../../common/src/modules/shared/dtos/filter-option.dto';
import { OperatorType } from '../../common/src/modules/shared/enums/operator.enum';
import { OrderType } from '../../common/src/modules/shared/enums/order.enum';
import { DataProviderTypes } from '../../database/src/constants/data-providers';
import { DataRegisterFieldEntity } from '../../database/src/entities/public/data-register-fields.public.entity';
import { DataRegisterTransactionFieldEntity } from '../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../database/src/entities/public/data-register-transaction.public.entity';
import { DataRegisterVersionEntity } from '../../database/src/entities/public/data-register-versions.public.entity';
import { DataRegisterEntity } from '../../database/src/entities/public/data-registers.public.entity';
import { FormFieldEntity } from '../../database/src/entities/public/form-field.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../database/src/entities/public/general-auto-populate-setting.public.entity';
import { DataRegisterFieldTenancyEntity } from '../../database/src/entities/tenancy/data-register-fields.tenancy.entity';
import { DataRegisterTenancyEntity } from '../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { FormFieldTenancyEntity } from '../../database/src/entities/tenancy/form-field.tenancy.entity';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from '../../database/src/entities/tenancy/general-auto-populate-extra-config.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../database/src/entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { TransactionFieldEntity } from '../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../database/src/entities/tenancy/transaction.tenancy.entity';
import { AutoPopulateBuilderTypeEnum } from '../../database/src/shared/enums/ap-builder-type.enum';
import { AutoPopulateDataSourceTypeEnum } from '../../database/src/shared/enums/ap-data-source-type.enum';
import { DataRegisterTypeEnum } from '../../database/src/shared/enums/data-register-type.enum';
import { FormFieldTypeEnum } from '../../database/src/shared/enums/form-field-type.enum';
import { isPopulateFieldStyle } from '../../modules/form/services/data/util/check-populate-style';
import { TransactionFieldUtil } from '../../modules/form/services/data/util/transaction-field.util';
import { getPriorityByDependentFields, PriorityType } from '../../utils';
import { AutoPopulateDataLakeRequest, AutoPopulateDataLakeResponse } from '../common/dto/autopopulate-datalake.dto';
import { BuilderInfo } from '../common/dto/general-auto-populate-setting.dto';
import { GetPopulatedFieldValueRequest, TransactionRequest } from '../common/dto/get-populated-field-values.request';
import {
    PopulatedFormFieldValuesResult,
    PopulatedValueToRegisterFieldFromRegisterResponse,
} from '../types/auto-populate-and-check-validation';
import { AppConfigService } from './app-config.service';
import { CommonService } from './common.service';
import { TransactionQueryBuilderService } from './transaction-query-builder.service';

@Injectable()
export class GeneralAutoPopulateService {
    constructor(
        private readonly _logger: LoggerService,
        private readonly _commonService: CommonService,
        private readonly _drTransactionQueryBuilder: TransactionQueryBuilderService,
        private readonly _http: BaseHttpService,
        private readonly _appConfig: AppConfigService,
        private readonly _jwt: JwtService,
        private readonly configService: ConfigService,
        private readonly _cacheService: CacheService,
    ) {}

    public async getSettings(builderInfo: BuilderInfo): Promise<GeneralAutoPopulateSettingEntity[]> {
        const settingRepo = this._commonService.getGeneralAutoPopulateSettingRepository();
        const settingEntities = await settingRepo.find({
            where: {
                ...builderInfo,
            },
            relations: ['extraConfigurations'],
        });
        return settingEntities;
    }

    public getPopulatedFormFieldValues = async (
        props: GetPopulatedFieldValueRequest,
        shouldReturnTransactionFields?: boolean,
    ): Promise<PopulatedFormFieldValuesResult> => {
        const {
            fieldChangeId,
            transaction,
            formVersionId,
            isLabelingAsValue,
            activeStageId,
            transactionId,
            fieldChangeIds: requestFieldChangeIds,
            cachedFormVersion,
            purpleTRACPopulatedData,
            payloadDocuments,
            targetFieldChangeIds,
        } = props;
        const formVersionRepo = this._commonService.getFormVersionRepository();

        const formVersion =
            cachedFormVersion ||
            (await formVersionRepo.findOne({
                where: {
                    id: formVersionId,
                },
                relations: ['fields'],
            }));

        const stageAccessControls = formVersion?.stageAccessControls
            ? formVersion?.stageAccessControls?.filter((f) => f.stageId === activeStageId) || []
            : await this._commonService.getStageAccessControlRepository().find({
                  where: {
                      formVersionId: formVersionId,
                      stageId: activeStageId,
                  },
              });

        const lockedFields = stageAccessControls
            ?.filter((stageAccessControl) => stageAccessControl?.config?.locked)
            ?.map((field) => field.targetId);

        const generalSettings: GeneralAutoPopulateSettingEntity[] =
            cachedFormVersion?.generalAutoPopulateSettings ||
            (await this.getSettings({
                builderId: formVersion.formId,
                builderVersionId: formVersion.id,
                builderType: AutoPopulateBuilderTypeEnum.FormField,
            }));

        //filter setting will be get from data-lake
        if (!generalSettings.length)
            return {
                transactionFields: [],
            };

        const dataLakeAutoPopulateValues = await this.getDataLakeAutoPopulate({
            generalSettings,
            transactionFields: transaction?.transactionFields || [],
            type: 'form',
            transactionId: transactionId,
            payloadDocuments,
        });
        //handle override response values from data lake
        if (props?.externalValues?.length) {
            props?.externalValues.forEach((value) => {
                const existed = (dataLakeAutoPopulateValues || []).find((item) => item.ctxPropertyId === value.ctxPropertyId);
                if (existed) {
                    existed.value = value.value;
                }
            });
        }

        const groupedSettings = _.groupBy(generalSettings, 'fieldId');

        const fields = formVersion?.fields;
        if (!fields?.length)
            return {
                transactionFields: [],
            };

        const drIds = _.uniq(
            generalSettings.filter((f) => f.dataSourceType === AutoPopulateDataSourceTypeEnum.Register).map((f) => f.dataSourceId) || [],
        );

        const drs = await Promise.all(
            drIds.map(async (id) => {
                return this.getDataRegister(id);
            }),
        );

        const drsMap = _.groupBy(drs, 'id');

        const results: Record<string, any> = {};
        const transactionFieldsResult: TransactionFieldEntity[] = [];

        const styleAndOverrideFields: StyleAndOverrideFields = {};

        const metadata: Array<{
            docId: string;
            dataSourceType: string;
            docFieldType: 'external' | 'upload';
            filePath?: string;
            fieldId: string;
        }> = [];

        const sortedFieldIds = this._getQueue(generalSettings);
        const sortedFields = _.sortBy<FormFieldTenancyEntity>(fields as FormFieldTenancyEntity[], (field: FormFieldEntity) =>
            (sortedFieldIds || []).findIndex((fieldId) => fieldId === field.fieldId),
        );
        const runFieldIds: string[] = [];

        const fieldIds = (fields || []).map((item: FormFieldTenancyEntity | FormFieldEntity) => item.fieldId);

        const fieldChangeIds = requestFieldChangeIds?.length ? requestFieldChangeIds : fieldChangeId ? [fieldChangeId] : fieldIds;

        const toRunFieldIds =
            fieldChangeId || requestFieldChangeIds?.length
                ? this._getFieldsShouldRunPopulate({
                      entries: fieldChangeIds,
                      settings: generalSettings,
                      sortedLimits: sortedFieldIds,
                  })
                : fieldIds;
        for (const field of sortedFields) {
            const settings = groupedSettings[field.fieldId];
            const shouldPopulate = this.shouldPopulate({
                fieldId: field.fieldId,
                toRunFieldIds,
                runFieldIds,
            });

            if (_.isEmpty(settings)) continue;
            if (!shouldPopulate) continue;

            try {
                const populatedValueResult = await this.getPopulatedValue({
                    generalAutoPopulateSettings: settings,
                    transaction: transaction as any,
                    dataLakeValues: dataLakeAutoPopulateValues,
                    purpleTRACValues: purpleTRACPopulatedData,
                    fields,
                    drsMap,
                    isLabelingAsValue,
                    targetFieldChangeIds,
                    currentFieldType: field.type,
                });
                const { transactionField, overrideRecord, overrideStyle } = populatedValueResult ?? {};

                styleAndOverrideFields[field.fieldId] = {};
                if (overrideRecord) {
                    styleAndOverrideFields[field.fieldId].override = overrideRecord;
                }

                if (overrideStyle) {
                    styleAndOverrideFields[field.fieldId].style = overrideStyle;
                }

                if (transactionField) {
                    const check = await this.checkPopulateNormalToLookup({
                        field,
                        transactionField,
                    });
                    if (check.isContinue) {
                        continue;
                    }

                    const value = {
                        fieldOptionIds: transactionField.fieldOptionIds,
                        fieldValue: transactionField.fieldValue,
                        value: transactionField.fieldOptionIds?.length
                            ? transactionField.fieldOptionIds.join(',')
                            : transactionField.fieldValue,
                    };

                    results[field.fieldId] = value;

                    if (field.type === FormFieldTypeEnum.Document) {
                        const nonExternalDataSourceTypes = [EXTERNAL_DATA_SOURCE_TYPE.PURPLETRAC, EXTERNAL_DATA_SOURCE_TYPE.Q88];
                        const isPurpleTRAC = transactionField?.dataSourceType === EXTERNAL_DATA_SOURCE_TYPE.PURPLETRAC;
                        const isQ88 = transactionField?.dataSourceType === EXTERNAL_DATA_SOURCE_TYPE.Q88;
                        const dataSourceTypes = EXTERNAL_DATA_SOURCE_TYPES.filter((type) => !nonExternalDataSourceTypes.includes(type));
                        metadata.push({
                            docId: value.value,
                            dataSourceType: transactionField?.dataSourceType,
                            docFieldType: dataSourceTypes.includes(transactionField?.dataSourceType) ? 'external' : 'upload',
                            fieldId: field.fieldId,
                            filePath: isPurpleTRAC ? transactionField?.filePath : isQ88 ? value.value : null,
                        });
                    }

                    transactionFieldsResult.push({
                        fieldId: field.fieldId,
                        fieldValue: transactionField.fieldValue,
                        fieldOptionIds: transactionField.fieldOptionIds,
                    } as any);

                    this.reAssignFieldValue({
                        transaction,
                        field,
                        transactionField,
                    });

                    runFieldIds.push(field.fieldId);
                } else if (field.fieldId !== props?.fieldChangeId) {
                    results[field.fieldId] = null;
                }
            } catch (err) {
                this._logger.error(err);
            }
        }

        if (shouldReturnTransactionFields) {
            return {
                transactionFields: transactionFieldsResult,
                styleAndOverrideFields: styleAndOverrideFields,
            };
        }

        // filter out locked fields from results
        if (lockedFields?.length) {
            Object.keys(results).forEach((fieldId) => {
                if (lockedFields.includes(fieldId)) {
                    delete results[fieldId];
                }
            });
        }

        return {
            transactionFields: { populateValues: results, metadata },
            styleAndOverrideFields: styleAndOverrideFields,
        };
    };

    public getPopulatedDataRegisterFieldValues = async (
        props: GetPopulatedFieldValueRequest,
    ): Promise<
        Record<string, any> & {
            metadataResult?: {
                populatedExternalFields: {
                    fieldId: string;
                    fieldValue: string;
                    dataSourceType: AutoPopulateDataSourceTypeEnum;
                }[];
            };
        }
    > => {
        const { fieldChangeId, fieldChangeIds, transaction, formVersionId, isLabelingAsValue, transactionId, internalUpdate } = props;

        const drVersionRepo = this._commonService.getDataRegisterVersionRepository();
        const drRepo = this._commonService.getDataRegisterRepository();
        const drVersion = await drVersionRepo.findOne({
            where: {
                id: formVersionId,
            },
            relations: ['fields'],
        });

        if (!drVersion) {
            return { populateValues: {} };
        }

        const dr = await drRepo.findOneBy({
            id: drVersion.dataRegisterId,
        });

        const generalSettings = await this.getSettings({
            builderId: drVersion.dataRegisterId,
            builderVersionId: drVersion.id,
            builderType: AutoPopulateBuilderTypeEnum.RegisterField,
        });
        if (!generalSettings.length) return [];

        let dataLakeAutoPopulateValues: AutoPopulateDataLakeResponse[] = [];
        if (!internalUpdate) {
            dataLakeAutoPopulateValues = await this.getDataLakeAutoPopulate({
                generalSettings,
                transactionFields: transaction?.transactionFields || [],
                type: dr.type,
                transactionId: null,
                mdsId: props.transaction?.mdsId,
            });
        }

        const groupedSettings = _.groupBy(generalSettings, 'fieldId');

        const fields = drVersion?.fields;
        if (!fields?.length) return [];
        const results: Record<string, any> = {};

        const sortedFieldIds = this._getQueue(generalSettings);
        const sortedFields = _.sortBy<FormFieldTenancyEntity>(fields as FormFieldTenancyEntity[], (field: FormFieldEntity) =>
            (sortedFieldIds || []).findIndex((fieldId) => fieldId === field.fieldId),
        );
        const runFieldIds: string[] = [];

        const combineFieldChangeIds = _.uniq(_.compact([fieldChangeId, ...(fieldChangeIds || [])]));
        const fieldIds = (fields || []).map((f: DataRegisterFieldTenancyEntity | DataRegisterFieldEntity) => f.fieldId);
        const _fieldChangeIds = combineFieldChangeIds?.length ? combineFieldChangeIds : fieldIds;

        const toRunFieldIds = combineFieldChangeIds?.length
            ? this._getFieldsShouldRunPopulate({
                  entries: _fieldChangeIds,
                  settings: generalSettings,
                  sortedLimits: sortedFieldIds,
              })
            : fieldIds;

        const metadataResult: {
            populatedExternalFields: {
                fieldId: string;
                fieldValue: string;
                dataSourceType: AutoPopulateDataSourceTypeEnum;
            }[];
        } = {
            populatedExternalFields: [],
        };
        for (const field of sortedFields) {
            const settings = groupedSettings[field.fieldId];
            if (_.isEmpty(settings)) continue;

            const shouldPopulate = this.shouldPopulate({
                fieldId: field.fieldId,
                runFieldIds,
                toRunFieldIds,
            });

            if (!shouldPopulate) continue;

            try {
                const populatedValueResult = await this.getPopulatedValue({
                    transactionId,
                    generalAutoPopulateSettings: settings,
                    transaction: transaction as any,
                    dataLakeValues: dataLakeAutoPopulateValues,
                    fields,
                    isLabelingAsValue,
                    //mutate and expose
                    metadataResult,
                    currentFieldType: field.type,
                });

                const { transactionField } = populatedValueResult ?? {};

                /**LS-3367 Populate normal field value to lookup field */
                if (transactionField) {
                    const check = await this.checkPopulateNormalToLookup({
                        field,
                        transactionField,
                    });
                    if (check.isContinue) {
                        continue;
                    }

                    const value = {
                        fieldOptionIds: transactionField.fieldOptionIds,
                        fieldValue: transactionField.fieldValue,
                        value: transactionField.fieldOptionIds?.length
                            ? transactionField.fieldOptionIds.join(',')
                            : transactionField.fieldValue,
                    };
                    results[field.fieldId] = value;

                    this.reAssignFieldValue({
                        transaction,
                        field,
                        transactionField,
                    });

                    runFieldIds.push(field.fieldId);
                }
            } catch (err) {
                console.error(err);
            }
        }

        return { populateValues: results, metadataResult };
    };

    private _getQueue(generalSettings: GeneralAutoPopulateSettingEntity[]) {
        const runQueue: PriorityType[] = [];
        const allSettingFieldIds = (generalSettings || []).map((setting) => setting.fieldId);
        generalSettings.forEach((setting) => {
            const dependencies: string[] = [];
            const extras = setting.extraConfigurations || [];
            extras.forEach((extra) => {
                const toFieldId = extra.toFieldId;
                if (allSettingFieldIds.includes(toFieldId)) {
                    dependencies.push(toFieldId);
                }
            });
            runQueue.push({
                fieldId: setting.fieldId,
                dependOnFieldIds: dependencies,
            });
        });

        const { sortedList } = getPriorityByDependentFields(runQueue);

        return (sortedList || []).map((item) => item.fieldId);
    }

    private shouldPopulate = ({
        fieldId,
        runFieldIds = [],
        toRunFieldIds = [],
    }: {
        fieldId: string;
        runFieldIds: string[]; // the fields has been run
        toRunFieldIds: string[]; // the fields has to be run to populate
    }) => {
        if (!fieldId) return false;
        const isToBeRun = toRunFieldIds.includes(fieldId);
        const isHasBeenRun = runFieldIds.includes(fieldId);

        return isToBeRun && !isHasBeenRun;
    };

    public async getPopulatedValue(props: {
        generalAutoPopulateSettings: GeneralAutoPopulateSettingTenancyEntity[];
        transaction: DataRegisterTransactionEntity | TransactionEntity;
        dataLakeValues: AutoPopulateDataLakeResponse[];
        fields: Array<DataRegisterFieldEntity | FormFieldEntity>;
        purpleTRACValues?: Array<Partial<AutoPopulateDataLakeResponse>>;
        transactionId?: string;
        isLabelingAsValue?: boolean; // using for field is populate from backend and not need to send back to frontend
        drsMap?: _.Dictionary<(DataRegisterTenancyEntity | DataRegisterEntity)[]>;
        metadataResult?: {
            populatedExternalFields: {
                fieldId: string;
                fieldValue: string;
                dataSourceType: AutoPopulateDataSourceTypeEnum;
            }[];
        };
        targetFieldChangeIds?: string[];
        currentFieldType: FormFieldTypeEnum;
    }): Promise<{
        transactionField: Partial<{
            fieldOptionIds: string[];
            fieldValue: string;
            dataSourceType?: string;
            filePath?: string;
            comparedFieldId?: string;
        }>;
        overrideRecord?: OverrideRecordRequest;
        overrideStyle?: FieldStyleRequest;
    }> {
        const {
            generalAutoPopulateSettings,
            transaction,
            dataLakeValues = [],
            fields = [],
            isLabelingAsValue,
            transactionId,
            drsMap,
            metadataResult,
            purpleTRACValues = [],
            targetFieldChangeIds,
            currentFieldType,
        } = props;
        const sortedSettings = _.sortBy(generalAutoPopulateSettings, 'priority');

        for (const idx in sortedSettings) {
            const setting = sortedSettings[idx];

            if (targetFieldChangeIds?.length && !targetFieldChangeIds.includes(setting.targetFieldId)) {
                continue;
            }

            /**
             * compared field id is a field of register which was setting on lookup field
             * using when need to populated a text (number/date/checkbox) value to lookup field
             */
            const comparedFieldId = setting.targetComparedFieldId;

            /** Data lake Populating */
            const dataLakeValue = (dataLakeValues || []).find(
                (x) => x.fieldId === setting?.fieldId && x.ctxPropertyId === setting?.targetFieldId,
            );

            if (!_.isEmpty(dataLakeValue?.value?.toString())) {
                // Handle for populated lookup field for a entity register field
                const dataSourceType = setting.dataSourceType;
                const isExternal = EXTERNAL_DATA_SOURCE_TYPES.includes(dataSourceType);
                if (metadataResult?.populatedExternalFields && isExternal) {
                    metadataResult.populatedExternalFields.push({
                        fieldId: setting.fieldId,
                        fieldValue: dataLakeValue.value, //mds id
                        dataSourceType: setting.dataSourceType,
                    });
                }

                return {
                    transactionField: {
                        fieldValue: dataLakeValue.value,
                        dataSourceType: setting.dataSourceType,
                        fieldOptionIds: [], //TODO: double check for select field from data-lake
                        comparedFieldId,
                    },
                };
            }

            /** Purple track Populating */
            const purpleTRACValue = (purpleTRACValues || []).find(
                (x) => x.fieldId === setting.fieldId && x.ctxPropertyId === setting.targetFieldId,
            );

            if (!_.isEmpty(purpleTRACValue?.value?.toString())) {
                return {
                    transactionField: {
                        fieldValue: purpleTRACValue.value,
                        dataSourceType: setting.dataSourceType,
                        fieldOptionIds: [], //TODO: double check for select field from data-lake
                        filePath: purpleTRACValue.metadata?.path,
                        comparedFieldId,
                    },
                };
            }

            /** Register Populating */
            const targetFieldId = setting.targetFieldId;
            const isTargetFieldItself = targetFieldId === AP_TARGET_ITSELF_FIELD_ID;
            const populatedValueResult = await this.getPopulatedValueFromSetting({
                generalAutoPopulateSetting: setting,
                transaction,
                transactionId,
                drsMap,
                currentFieldType,
            });
            const { transactionField: valueData, overrideRecord, overrideStyle } = populatedValueResult ?? {};
            if (!_.isEmpty(valueData)) {
                if (isTargetFieldItself) {
                    const formField = fields.find((x) => x.fieldId === setting.fieldId);

                    const resultData: Partial<{
                        fieldOptionIds: string[];
                        fieldValue: string;
                        dataSourceType?: string;
                        comparedFieldId?: string;
                    }> = {
                        dataSourceType: setting.dataSourceType,
                        fieldValue: null,
                        fieldOptionIds: [],
                        comparedFieldId,
                    };

                    if (formField) {
                        const { value, type, fieldOptionIds } = this._format({
                            formField,
                            value: valueData?.id,
                            targetField: {
                                fieldValue: valueData?.id,
                                fieldOptionIds: [valueData?.id],
                            },
                            isLabelingAsValue: isLabelingAsValue,
                        });

                        if ([FormFieldTypeEnum.Select, FormFieldTypeEnum.Lookup].includes(type)) {
                            resultData.fieldValue =
                                (valueData as DataRegisterTransactionEntity & { displayLabel?: string })?.displayLabel ?? '';
                        } else {
                            resultData.fieldValue = value as string;
                        }
                        resultData.fieldOptionIds = fieldOptionIds;
                    }
                    return {
                        transactionField: resultData,
                        overrideRecord,
                        overrideStyle,
                    };
                } else {
                    const typedData = valueData as Partial<{
                        fieldOptionIds: string[];
                        fieldValue: string;
                        dataSourceType?: string;
                        comparedFieldId?: string;
                    }>;

                    typedData.comparedFieldId = comparedFieldId;

                    const formField = fields.find((x) => x.fieldId === setting.fieldId);
                    if (formField) {
                        const { value, label, type, fieldOptionIds } = this._format({
                            formField,
                            value: typedData?.fieldValue,
                            targetField: {
                                fieldValue: typedData?.fieldValue,
                                fieldOptionIds: typedData?.fieldOptionIds,
                            },
                            isLabelingAsValue: isLabelingAsValue,
                        });
                        if ([FormFieldTypeEnum.Select, FormFieldTypeEnum.Lookup].includes(type)) {
                            typedData.fieldValue = label ?? '';
                        } else {
                            typedData.fieldValue = value as string;
                        }
                        typedData.fieldOptionIds = fieldOptionIds;
                    }
                    return {
                        transactionField: typedData,
                        overrideRecord,
                        overrideStyle,
                    };
                }
            }
        }

        return {
            transactionField: null,
        };
    }

    private _format({
        formField,
        value,
        targetField,
        isLabelingAsValue,
    }: {
        formField: {
            type: FormFieldTypeEnum;
            configuration: any;
        };
        value: string;
        targetField: {
            fieldValue?: string;
            fieldOptionIds?: string[];
        };
        isLabelingAsValue?: boolean; // using for field is populate from backend and not need to send back to frontend
    }): {
        value: string | string[];
        label?: string;
        type?: FormFieldTypeEnum;
        fieldOptionIds?: string[];
    } {
        if (!formField || !value) {
            return { value };
        }

        switch (formField.type) {
            case FormFieldTypeEnum.Select: {
                const sourceOptions: Array<{ label: string; value: string }> = formField.configuration?.options || [];
                const mode = formField.configuration?.mode || 'single';

                const rawValues = _.isArray(value) ? value : (value as string).split(',');
                const values = rawValues.filter((v) => !_.isNil(v)) as string[]; //is labels of options from target field

                //filter labels from options in target field that is existing in criteria field
                const finalOptions =
                    sourceOptions.filter((option) => values.some((v) => option.label?.toString()?.trim() === `${v}`.trim())) || [];

                const finalValues = finalOptions.map((fo) => fo.value);
                const finalLabels = finalOptions.map((fo) => fo.label);

                return {
                    value: mode === 'single' ? finalValues[0] : finalValues,
                    label: finalLabels.join(', '),
                    type: FormFieldTypeEnum.Select,
                    fieldOptionIds: finalValues,
                };
            }
            case FormFieldTypeEnum.Lookup: {
                const mode = formField.configuration?.mode || 'single';

                const label = targetField?.fieldValue;
                if (!targetField?.fieldOptionIds?.length) {
                    return { value, label };
                }

                const finalValue = mode === 'single' ? targetField.fieldOptionIds[0] : targetField?.fieldOptionIds;

                return {
                    value: isLabelingAsValue ? label : finalValue,
                    type: FormFieldTypeEnum.Lookup,
                    label,
                    fieldOptionIds: targetField?.fieldOptionIds,
                };
            }
            default:
                return { value };
        }
    }

    async getDataLakeAutoPopulate({
        generalSettings,
        transactionFields,
        type,
        transactionId,
        mdsId,
        payloadDocuments,
    }: {
        transactionFields: {
            fieldId: string;
            fieldValue: string;
            fieldType?: string;
            lastValue?: { fieldValue: string; fieldOptionIds?: string[] };
        }[];
        generalSettings: GeneralAutoPopulateSettingEntity[];
        type: DataRegisterTypeEnum | 'form';
        transactionId: string;
        mdsId?: string;
        payloadDocuments?: Record<string, any>;
    }) {
        try {
            const registerTransactionRepo = this._commonService.getDataRegisterTransactionRepository();

            //filter setting will be get from data-lake
            //#region auto populate data lake
            const dataLakeSettings = (generalSettings || []).filter((setting) =>
                EXTERNAL_DATA_SOURCE_TYPES.includes(setting.dataSourceType),
            );

            const toFields: { fieldId: string; toFieldId: string; fieldValue: string; toLookupFieldId: string; targetFieldId: string }[] =
                [];

            const mdsFields: Array<(typeof toFields)[number] & { mdsId: string; documentType?: string }> = [];

            dataLakeSettings.forEach((setting) => {
                if (setting.dataSourceType === EXTERNAL_DATA_SOURCE_TYPE.PDF && !setting.extraConfigurations?.length) {
                    const pdfMdsMapping: any = {
                        fieldId: setting.fieldId,
                        mdsId: transactionId,
                        targetFieldId: setting.targetFieldId,
                        documentType: setting.dataSourceCode,
                    };

                    mdsFields.push(pdfMdsMapping);
                } else if (mdsId) {
                    const externalMapping: any = {
                        fieldId: setting.fieldId,
                        mdsId: mdsId,
                        targetFieldId: setting.targetFieldId,
                    };
                    mdsFields.push(externalMapping);
                }

                if (!setting.extraConfigurations?.length) return;
                const extraConfig = setting.extraConfigurations[0];
                const toFieldId = extraConfig?.toFieldId;
                let toField = (transactionFields || []).find((field) => field.fieldId === toFieldId);

                const toFieldValue = toField?.lastValue?.fieldValue ?? toField?.fieldValue;
                if (toFieldId && toFieldValue) {
                    toFields.push({
                        fieldId: setting.fieldId,
                        toFieldId,
                        fieldValue: toFieldValue,
                        toLookupFieldId: extraConfig.toLookupFieldId,
                        targetFieldId: setting.targetFieldId,
                    });
                }
            });

            toFields.forEach((toField) => {
                if (Array.isArray(toField.fieldValue)) {
                    toField.fieldValue = toField.fieldValue[0];
                }
            });

            //get lookup field data
            if (toFields.length) {
                const transactionIds: string[] = [];
                toFields.forEach((f) => {
                    if (!f.fieldValue) return;

                    if (Array.isArray(f.fieldValue)) {
                        transactionIds.push(...f.fieldValue);
                    } else {
                        transactionIds.push(f.fieldValue);
                    }
                });

                const uniqTransactionIds = _.uniq(transactionIds);
                const registerTransactions = await registerTransactionRepo.find({
                    where: {
                        id: In(uniqTransactionIds),
                    },
                    relations: {
                        transactionFields: true,
                    },
                });

                toFields.forEach((toField) => {
                    const toLookupFieldId = toField.toLookupFieldId;
                    const transactionId = toField.fieldValue;
                    const registerTransaction = registerTransactions.find((tr) => tr.id === transactionId);
                    if (!registerTransaction) return;

                    const transactionFields = registerTransaction.transactionFields || [];
                    const mdsField = transactionFields.find((field) => field.fieldId === toLookupFieldId);
                    if (!mdsField?.fieldValue) return;

                    mdsFields.push({
                        ...toField,
                        mdsId: mdsField.fieldValue,
                    });
                });
            }

            //build payload
            const payload: AutoPopulateDataLakeRequest = {};
            mdsFields.forEach((mdsField) => {
                if (!mdsField?.targetFieldId || !mdsField?.mdsId) {
                    return;
                }

                const dataLakeFieldMappings = payload[mdsField.fieldId];
                if (dataLakeFieldMappings?.length) {
                    const isExisted = dataLakeFieldMappings.some(
                        (mapping) => mapping.ctxPropertyId === mdsField.targetFieldId && mapping.mdsId === mdsField.mdsId,
                    );
                    if (!isExisted) {
                        payload[mdsField.fieldId].push({
                            mdsId: mdsField.mdsId,
                            ctxPropertyId: mdsField.targetFieldId,
                            documentType: mdsField.documentType,
                        });
                    }
                } else {
                    payload[mdsField.fieldId] = [
                        {
                            mdsId: mdsField.mdsId,
                            ctxPropertyId: mdsField.targetFieldId,
                            documentType: mdsField.documentType,
                        },
                    ];
                }
            });

            let dataLakeAutoPopulateValues: AutoPopulateDataLakeResponse[] = [];

            if (!_.isEmpty(payload)) {
                let authorization = RequestContextService.authorization;
                if (!authorization) {
                    const token = this._jwt.sign(
                        {
                            accountId: RequestContextService.accountId,
                        },
                        {
                            secret: this.configService.get('APP_SECRET'),
                        },
                    );

                    authorization = `Bearer ${token}`;
                }
                const dataLakeUrl = this._appConfig.dataLake.url;
                // const dataLakeUrl = 'http://localhost:3000';

                const dataLakeAutoPopulateData = await this._http.post<{ data: AutoPopulateDataLakeResponse[] }>(
                    `${dataLakeUrl}/ctx-properties/v1/get-populate-value`,
                    {
                        // payloadDocuments:{
                        //     PARIS_MOU_PSC: 'ea1624ee-1ade-419f-b073-83350bdaa427'
                        // },
                        payloadDocuments,
                        payload,
                    },
                    {
                        headers: {
                            Authorization: authorization,
                        },
                    },
                );

                dataLakeAutoPopulateValues = dataLakeAutoPopulateData.data?.data || [];
            }

            //#endregion auto populate data lake

            return dataLakeAutoPopulateValues;
        } catch (error) {
            this._logger.error(error);
            return [];
        }
    }

    private async getPopulatedValueFromSetting(props: {
        generalAutoPopulateSetting: GeneralAutoPopulateSettingTenancyEntity;
        transaction: DataRegisterTransactionEntity | TransactionEntity;
        transactionId?: string;
        drsMap?: _.Dictionary<(DataRegisterTenancyEntity | DataRegisterEntity)[]>;
        currentFieldType: FormFieldTypeEnum;
    }): Promise<PopulatedValueToRegisterFieldFromRegisterResponse> {
        const { generalAutoPopulateSetting, transaction, transactionId, drsMap, currentFieldType } = props;
        const { builderType } = generalAutoPopulateSetting;
        switch (builderType) {
            case AutoPopulateBuilderTypeEnum.RegisterField: {
                const value = await this.getPopulatedValueToRegisterField({
                    transactionId,
                    generalAutoPopulateSetting,
                    transaction: transaction as DataRegisterTransactionEntity,
                    isAccurate: true,
                    currentFieldType,
                });
                return value;
            }

            case AutoPopulateBuilderTypeEnum.FormField: {
                const value = await this.getPopulatedValueToFormField({
                    generalAutoPopulateSetting,
                    transaction: transaction as TransactionEntity,
                    drsMap,
                    isAccurate: true,
                    currentFieldType,
                });
                return value;
            }

            case AutoPopulateBuilderTypeEnum.FormCollectionItem:
                break;

            default:
                break;
        }
        return undefined;
    }

    //#region "Register Field"
    private async getPopulatedValueToRegisterField(props: {
        generalAutoPopulateSetting: GeneralAutoPopulateSettingTenancyEntity;
        transaction: DataRegisterTransactionEntity | any;
        transactionId: string;
        isAccurate?: boolean;
        currentFieldType: FormFieldTypeEnum;
    }): Promise<PopulatedValueToRegisterFieldFromRegisterResponse | null> {
        const { generalAutoPopulateSetting, transaction, transactionId, isAccurate, currentFieldType } = props;
        const { extraConfigurations, targetFieldId, dataSourceType, dataSourceId } = generalAutoPopulateSetting;

        switch (dataSourceType) {
            case AutoPopulateDataSourceTypeEnum.Register: {
                const value = await this.getPopulatedValueToRegisterFieldFromRegister({
                    dataRegisterId: dataSourceId,
                    drTransaction: transaction,
                    extraConfigurations,
                    targetFieldId,
                    transactionId,
                    setting: generalAutoPopulateSetting,
                    isAccurate,
                    currentFieldType,
                });
                return value;
            }

            default:
                return null;
        }
    }

    private async getPopulatedValueToRegisterFieldFromRegister(props: {
        transactionId?: string;
        targetFieldId: string;
        dataRegisterId: string;
        drTransaction: DataRegisterTransactionEntity | any;
        extraConfigurations: GeneralAutoPopulateExtraConfigTenancyEntity[];
        drsMap?: _.Dictionary<(DataRegisterTenancyEntity | DataRegisterEntity)[]>;
        setting: GeneralAutoPopulateSettingTenancyEntity;
        isAccurate?: boolean;
        currentFieldType: FormFieldTypeEnum;
    }): Promise<PopulatedValueToRegisterFieldFromRegisterResponse> {
        const {
            dataRegisterId,
            drTransaction,
            extraConfigurations,
            targetFieldId,
            transactionId,
            drsMap,
            setting,
            isAccurate,
            currentFieldType,
        } = props;
        const dataRegister = drsMap?.[dataRegisterId]?.[0] || (await this.getDataRegister(dataRegisterId));
        if (!dataRegister) {
            return null;
        }
        const dataRegisterVersion = dataRegister.dataRegisterVersions[0];
        const transactionDataSource = await this.getDataRegisterTransactionByExtraConfigs({
            dataRegisterVersion,
            drTransaction,
            extraConfigurations,
            transactionId,
            isAccurate,
        });
        if (targetFieldId === AP_TARGET_ITSELF_FIELD_ID) {
            if (!transactionDataSource) return undefined;
            const displayAttributes = dataRegisterVersion.displayAttributes || [];
            const labelValues = displayAttributes?.map((fieldId) => {
                const transactionField = transactionDataSource?.transactionFields?.find((f) => f.fieldId === fieldId);
                return transactionField?.fieldValue;
            });
            const displayLabel = TransactionFieldUtil.displayFieldLabels(labelValues);
            return {
                transactionField: { ...transactionDataSource, displayLabel },
            };
        }
        const transactionField = transactionDataSource?.transactionFields?.find((f) => f.fieldId === targetFieldId);
        if (!transactionField) return undefined;

        const exportStyle = isPopulateFieldStyle({
            includeValidationValue: setting.includeValidationValue,
            type: currentFieldType,
        });

        if (exportStyle) {
            const { overrideRecord, overrideStyle } = await this.getOverrideRecordAndStyle({ transactionField, fieldId: setting.fieldId });
            return { transactionField, overrideRecord, overrideStyle };
        }
        return { transactionField };
    }

    private async getOverrideRecordAndStyle(props: { transactionField: DataRegisterTransactionFieldEntity; fieldId: string }): Promise<{
        overrideRecord: OverrideRecordRequest;
        overrideStyle: FieldStyleRequest;
    }> {
        const { transactionField, fieldId } = props;
        const registerTransactionFieldRepo = this._commonService.getDataRegisterTransactionFieldRepository();
        const transactionFieldEntity = await registerTransactionFieldRepo.findOne({
            where: {
                id: transactionField.id,
            },
            relations: {
                transactionFieldOverrides: true,
                transactionFieldStyle: true,
            },
        });

        const overrideRecord = transactionFieldEntity.transactionFieldOverrides.find(
            (override) => override.type === OverrideTypeEnum.User && override.status === OverrideStatusEnum.Active,
        );
        const transactionFieldStyle = transactionFieldEntity.transactionFieldStyle;

        return {
            overrideRecord: overrideRecord?.validationValue
                ? {
                      comment: overrideRecord?.comment ?? '',
                      createdBy: overrideRecord?.createdBy,
                      createdByUser: overrideRecord?.createdByUser,
                      dependencyValues: {
                          [fieldId]: transactionField.fieldOptionIds?.length
                              ? transactionField.fieldOptionIds
                              : transactionField.fieldValue,
                      },
                      fieldId: transactionField.fieldId,
                      fromValue: overrideRecord?.fromValue,
                      overrideValue: overrideRecord?.validationValue,
                      validationValue: overrideRecord?.validationValue,
                  }
                : null,
            overrideStyle: {
                fieldId: transactionField?.fieldId,
                style: transactionFieldStyle?.configuration,
            },
        };
    }

    private async getDataRegisterTransactionByExtraConfigs(props: {
        extraConfigurations: GeneralAutoPopulateExtraConfigTenancyEntity[];
        drTransaction: DataRegisterTransactionEntity;
        dataRegisterVersion: DataRegisterVersionEntity;
        transactionId?: string;
        isAccurate?: boolean;
    }): Promise<DataRegisterTransactionEntity | undefined> {
        try {
            const { dataRegisterVersion, drTransaction, extraConfigurations, transactionId, isAccurate } = props;
            const dataRegisterTransactionRepository = this._commonService.getDataRegisterTransactionRepository();
            const filters: FilterOptionDto[] = [];

            for (const { fromFieldId, toFieldId, toLookupFieldId } of extraConfigurations) {
                if (toFieldId === AP_EXTRA_CONFIG_ITSELF_FIELD_ID) {
                    if (!transactionId) return undefined;
                    const fieldValue = transactionId;

                    const filter: FilterOptionDto = {
                        field: fromFieldId,
                        operator: OperatorType.equals,
                        value: fieldValue,
                        queryToOptionIds: true,
                    };
                    filters.push(filter);
                } else {
                    const transactionField = drTransaction.transactionFields.find((tf) => tf.fieldId === toFieldId);
                    if (_.isEmpty(transactionField?.fieldValue) && _.isEmpty(transactionField?.fieldOptionIds)) return undefined;

                    let fieldValue;
                    if (toLookupFieldId && (transactionField?.fieldOptionIds?.length || transactionField?.fieldValue)) {
                        //debug and check carefully
                        const value = transactionField.fieldOptionIds?.length
                            ? transactionField.fieldOptionIds[0]
                            : transactionField.fieldValue;
                        const lookupTransactionId = value;
                        const lookupTransaction = await dataRegisterTransactionRepository.findOne({
                            where: {
                                id: lookupTransactionId,
                            },
                            relations: ['transactionFields'],
                        });
                        if (!lookupTransaction?.transactionFields) continue;
                        const lookupTransactionField = lookupTransaction.transactionFields.find((tf) => tf.fieldId === toLookupFieldId);
                        if (_.isEmpty(lookupTransactionField?.fieldValue)) continue;

                        fieldValue = lookupTransactionField?.fieldValue;
                    } else {
                        fieldValue = transactionField?.fieldValue;
                    }

                    const shouldQueryToOptionIds = () => {
                        if (MDS_ENTITY_FIELD_IDS.includes(toLookupFieldId)) return false;
                        return isUUID(fieldValue);
                        //if ((transactionField as any).fieldType === FormFieldTypeEnum.Lookup) return true;
                        // if (transactionField.fieldOptionIds?.length) return true;
                        //return false
                    };

                    const filter: FilterOptionDto = {
                        field: fromFieldId,
                        operator: OperatorType.equals,
                        value: fieldValue,
                        queryToOptionIds: shouldQueryToOptionIds(),
                    };
                    filters.push(filter);
                }
            }

            if (!filters.length) return undefined;
            const dataRegisterTransactionDataSource = await this.getDataRegisterTransaction({
                filters,
                dataRegisterVersion,
                isAccurate,
            });
            return dataRegisterTransactionDataSource;
        } catch (error) {
            this._logger.error(error);
            return undefined;
        }
    }

    private async getDataRegisterTransaction(props: {
        filters: FilterOptionDto[];
        dataRegisterVersion: DataRegisterVersionEntity;
        isAccurate?: boolean;
    }): Promise<DataRegisterTransactionEntity | undefined> {
        const { dataRegisterVersion, filters, isAccurate } = props;

        const dataRegisterTransactionRepository = this._commonService.getDataRegisterTransactionRepository();
        const { data } = await this._drTransactionQueryBuilder.getPaging({
            selectFields: dataRegisterVersion.fields?.map((field) => field.fieldId) || [],
            normalizedFilters: filters,
            dataRegisterId: dataRegisterVersion.dataRegisterId,
            order: OrderType.ASC,
            skip: 0,
            take: 1,
            standardFilters: [
                {
                    field: 'dataRegisterId',
                    operator: OperatorType.equals,
                    value: dataRegisterVersion.dataRegisterId,
                },
            ],
        });

        if (!data?.length) return undefined;

        const entity = await dataRegisterTransactionRepository.findOne({
            where: {
                id: data[0],
            },
            relations: ['transactionFields'],
        });
        return entity;
    }

    private async getDataRegister(dataRegisterId: string) {
        const dataRegister = await this._commonService.getDataRegisterRepository()?.findOne({
            where: {
                id: dataRegisterId,
            },
            select: ['id', 'activeVersionId'],
        });
        if (!dataRegister || !dataRegister.activeVersionId) return null;

        const cacheKey = UtilsService.getActiveDrVersionCacheKeys({
            accountId: this._commonService.getAccountId(),
            drId: dataRegisterId,
            drVersionId: dataRegister.activeVersionId,
        });

        let cacheActiveVersion;

        if (cacheKey) {
            const cacheValue = await this._cacheService.jsonGet(cacheKey.drVersionKey);
            if (cacheValue) {
                cacheActiveVersion = cacheValue;
            }
        }

        const activeVersion =
            cacheActiveVersion ||
            (await this._commonService.getDataRegisterVersionRepository()?.findOne({
                where: {
                    id: dataRegister.activeVersionId,
                },
                select: {
                    id: true,
                    dataRegisterId: true,
                    displayAttributes: true,
                },
            }));

        if (!activeVersion) return null;

        if (!activeVersion.fields?.length) {
            activeVersion.fields =
                (await this._commonService.getDataRegisterFieldRepository()?.find({
                    where: {
                        dataRegisterVersionId: activeVersion.id,
                    },
                    select: ['id', 'fieldId', 'type'],
                })) || [];
        }

        dataRegister.dataRegisterVersions = [activeVersion];

        return dataRegister;
    }
    //#endregion "Register Field"

    //#region "Form Field"
    private async getPopulatedValueToFormField(props: {
        generalAutoPopulateSetting: GeneralAutoPopulateSettingTenancyEntity;
        transaction: TransactionEntity | DataRegisterTransactionEntity;
        drsMap?: _.Dictionary<(DataRegisterTenancyEntity | DataRegisterEntity)[]>;
        isAccurate?: boolean;
        currentFieldType: FormFieldTypeEnum;
    }): Promise<PopulatedValueToRegisterFieldFromRegisterResponse> {
        const { generalAutoPopulateSetting, transaction, drsMap, isAccurate, currentFieldType } = props;
        const { extraConfigurations, targetFieldId, dataSourceType, dataSourceId } = generalAutoPopulateSetting;

        switch (dataSourceType) {
            case AutoPopulateDataSourceTypeEnum.Register: {
                const value = await this.getPopulatedValueToRegisterFieldFromRegister({
                    dataRegisterId: dataSourceId,
                    drTransaction: transaction as DataRegisterTransactionEntity,
                    extraConfigurations,
                    targetFieldId,
                    drsMap,
                    setting: generalAutoPopulateSetting,
                    isAccurate,
                    currentFieldType,
                });
                return value;
            }

            case AutoPopulateDataSourceTypeEnum.RelatedForm: {
                return null;
            }
        }
    }

    //#endregion "Form Field"

    private _getFieldsShouldRunPopulate({
        entries,
        sortedLimits,
        settings,
    }: {
        entries: string[];
        sortedLimits: string[];
        settings: GeneralAutoPopulateSettingTenancyEntity[];
    }): string[] {
        if (!entries?.length) return [];
        const result: string[] = [];
        const toChecks: string[] = [];

        const sortedEntries = _.sortBy<string>(entries, (item) => sortedLimits.findIndex((fieldId) => fieldId === item));
        toChecks.push(...sortedEntries);

        while (toChecks.length) {
            const itemId = toChecks.shift();
            if (result.includes(itemId)) continue;

            const relatedFieldIds = (settings || [])
                .filter((s) => (s.extraConfigurations || []).some((config) => config.toFieldId === itemId))
                .map((s) => s.fieldId);

            const uniqIds = _.uniq(_.compact(relatedFieldIds));

            uniqIds.forEach((id) => {
                if (!sortedLimits.includes(id)) return;
                if (result.includes(id)) return;

                toChecks.push(id);
            });

            result.push(itemId);
        }

        // the value of entries is existing in request so not need to run populate again
        return result.filter((id) => !entries.includes(id));
    }

    private reAssignFieldValue({
        field,
        transaction,
        transactionField,
    }: {
        transaction: TransactionRequest;
        transactionField: Partial<{
            fieldOptionIds: string[];
            fieldValue: string;
        }>;
        field: { fieldId: string; type: FormFieldTypeEnum };
    }) {
        //set to transaction fields
        const requestField = transaction?.transactionFields?.find((f) => f.fieldId === field.fieldId);
        const fieldValue = transactionField.fieldOptionIds?.length ? transactionField.fieldOptionIds[0] : transactionField.fieldValue;

        if (requestField) {
            requestField.fieldValue = fieldValue;
            requestField.fieldOptionIds = transactionField.fieldOptionIds;
        } else {
            transaction?.transactionFields?.push({
                fieldId: field.fieldId,
                fieldType: field.type,
                fieldValue,
                fieldOptionIds: transactionField.fieldOptionIds,
            });
        }
    }

    public async checkPopulateNormalToLookup({
        transactionField,
        field,
    }: {
        transactionField: Partial<{
            fieldOptionIds: string[];
            fieldValue: string;
            dataSourceType?: string;
            filePath?: string;
            comparedFieldId?: string;
        }>;
        field: FormFieldTenancyEntity;
    }) {
        const result: { isContinue: boolean } = { isContinue: false };

        /**LS-3367 Populate normal field value to lookup field */
        if (!transactionField) return result;
        const comparedFieldId = transactionField.comparedFieldId;
        if (field.type === FormFieldTypeEnum.Lookup && comparedFieldId) {
            const registerId = field.lookupTargetId || field.configuration?.targetId;
            const value = await this.findRegisterRecordByComparedField({
                value: transactionField.fieldValue,
                comparedFieldId,
                registerId,
                fieldId: field.fieldId,
            });
            if (value) {
                transactionField.fieldValue = value;
                transactionField.fieldOptionIds = value.split(',');
            } else {
                result.isContinue = true;
            }
        }

        return result;
    }

    public async findRegisterRecordByComparedField({
        registerId,
        comparedFieldId,
        value,
        fieldId,
    }: {
        registerId: string;
        comparedFieldId: string;
        value: string;
        fieldId: string; //current field - which received value
    }): Promise<string | null> {
        if (!value || !registerId || !comparedFieldId) return null;
        const dataRegisterTransactionRepository = this._commonService.getDataRegisterTransactionRepository();
        const builder = dataRegisterTransactionRepository
            .createQueryBuilder('register')
            .innerJoin('register.transactionFields', 'registerFields')
            .where('register.dataRegisterId = :registerId', { registerId })
            .andWhere('registerFields.fieldId = :fieldId', { fieldId: comparedFieldId })
            .andWhere('registerFields.fieldValue = :fieldValue', { fieldValue: value });

        const record = await builder.getOne();

        if (!record) {
            const drRepo = this._commonService.getDataRegisterRepository();
            const builder = await drRepo.findOne({ where: { id: registerId }, select: { type: true } });
            //if register is an entity type, go to search on mds level
            if (DataProviderTypes.includes(builder.type)) {
                const entities = await this.filterDataLakeEntities({
                    filters: [
                        {
                            fieldId,
                            text: value,
                            type: builder.type.toLocaleLowerCase(),
                        },
                    ],
                });

                const entity = entities.find((e) => e.fieldId === fieldId);
                if (entity?.id) {
                    return entity.id;
                }
            }
        }

        return record ? record.id : null;
    }

    public async filterDataLakeEntities({ filters }: { filters: { fieldId: string; text: string; type: string }[] }) {
        let authorization = RequestContextService.authorization;
        if (!authorization) {
            const token = this._jwt.sign(
                {
                    accountId: RequestContextService.accountId,
                },
                {
                    secret: this.configService.get('APP_SECRET'),
                },
            );

            authorization = `Bearer ${token}`;
        }
        const dataLakeUrl = this._appConfig.dataLake.url;
        const url = `${dataLakeUrl}/systems/v1/filter`;

        const filterData = await this._http.post<{ data: { id: string; fieldId: string }[] }>(
            url,
            {
                filters,
            },
            {
                headers: {
                    Authorization: authorization,
                },
            },
        );

        const entities = filterData?.data?.data || [];
        return entities;
    }
}
