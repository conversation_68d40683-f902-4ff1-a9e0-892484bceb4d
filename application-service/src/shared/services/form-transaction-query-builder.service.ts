import { Inject, Injectable, Scope } from '@nestjs/common';
import * as _ from 'lodash';
import { Brackets, DataSource, SelectQueryBuilder } from 'typeorm';

import { JsonTree } from '@react-awesome-query-builder/core';
import { LoggerService, QueryBuilderService, UtilsService } from '../../common/src';
import { ORIGIN_DATA } from '../../common/src/constant/field';
import { FilterOptionDto } from '../../common/src/modules/shared/dtos/filter-option.dto';
import { OrderType } from '../../common/src/modules/shared/enums/order.enum';
import { PREFIX } from '../../common/src/modules/shared/enums/prefix.enum';
import { DEFAULT_PAGE_SIZE } from '../../constant';
import { PROVIDER_KEYS } from '../../database/src/constants/providers';
import { TransactionEntity } from '../../database/src/entities/tenancy/transaction.tenancy.entity';
import { getRelatedFieldTypes, RelatedFieldType } from '../../utils';
import { FormTransactionFilterOptions } from '../types/form-transaction-filter-options';

type BaseQbParams = {
    qb: SelectQueryBuilder<any>;
    fields: FilterOptionDto[];
    tableFields?: FilterOptionDto[];
    transactionId?: string;
    attributeSortFields: { field: string; order: OrderType }[];
    sorts: { field: string; order: OrderType }[];
    conditions?: JsonTree;
};

type PagingQbParams = BaseQbParams & {
    skip: number;
    take: number;
};

@Injectable({ scope: Scope.REQUEST })
export class FormTransactionQueryBuilderService {
    private TRANSACTION_TABLE = 'transaction';
    private TRANSACTION_FIELD_TABLE = 'transaction_field';
    private TRANSACTION_RELATION_TABLE = 'relation_transaction';
    private FORM_TABLE = 'forms';

    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _tenantConnection: DataSource,
        private readonly _logger: LoggerService,
        private readonly _queryBuilder: QueryBuilderService,
    ) {}

    public withOutPrefix = (field: string) => field.replace(`${PREFIX.DATA_REGISTER_FIELD}_`, '');

    public genSearchOrQuery = (fields: string[], searchQuery: string): string => {
        if (!searchQuery) {
            return '';
        }

        fields = fields.map((field) => `${this.formatAlias(field)} ILIKE '%${searchQuery}%'`);
        return `(${fields.join(' OR ')})`;
    };

    public async getPaging(options: FormTransactionFilterOptions): Promise<{
        data: string[];
        total: number;
    }> {
        const { orphanedTransaction, normalizedFilters, attributeSortFields, sorts, skip, take = DEFAULT_PAGE_SIZE, conditions } = options;

        const dataQb = this._tenantConnection.createQueryBuilder();
        const countQb = this._tenantConnection.createQueryBuilder();

        const columns = this._tenantConnection.getMetadata(TransactionEntity).columns.map((column) => column.propertyName);
        const [tableFields, nonTableFields] = _.partition(options.standardFilters, (f) => columns.indexOf(f.field) > -1);
        const formFilterField = options.standardFilters.find((f) => f.field === 'formName');

        console.log(`******** nonTableFields`, nonTableFields);
        const sortTableFields = sorts?.filter((f) => columns.indexOf(f.field) > -1) ?? [];
        if (orphanedTransaction === true) {
            this.filterOrphanTransaction(countQb, dataQb);
        }

        if (formFilterField) {
            this.filterForm(countQb, dataQb, formFilterField);
        }

        try {
            const countQuery = this.generateCountQuery({
                qb: countQb,
                tableFields: tableFields,
                fields: normalizedFilters,
                attributeSortFields,
                sorts: sortTableFields,
                conditions,
            });
            console.log(countQuery.getQuery());

            const count = await countQuery.getRawMany();

            const dataQuery = this.generateDataQuery({
                qb: dataQb,
                tableFields: tableFields,
                fields: normalizedFilters,
                attributeSortFields,
                sorts: sortTableFields,
                skip,
                take,
                conditions,
            });

            console.log('${dataQuery.getQuery()}: ', dataQuery.getQuery());
            console.log('dataQuery.getParameters(): ', dataQuery.getParameters());
            const data = await dataQuery.getRawMany();

            const ids = data.map((item) => item.transaction_id);

            return { data: ids, total: count?.[0]?.count ? parseInt(count?.[0]?.count) : 0 };
        } catch (error) {
            this._logger.error(error);
            return {
                data: [],
                total: 0,
            };
        }
    }

    public getRawPaging(options: FormTransactionFilterOptions) {
        const { orphanedTransaction, normalizedFilters, attributeSortFields, sorts, skip, take = DEFAULT_PAGE_SIZE, conditions } = options;

        const dataQb = this._tenantConnection.createQueryBuilder();
        const countQb = this._tenantConnection.createQueryBuilder();

        const columns = this._tenantConnection.getMetadata(TransactionEntity).columns.map((column) => column.propertyName);
        const [tableFields, nonTableFields] = _.partition(options.standardFilters, (f) => columns.indexOf(f.field) > -1);
        const formFilterField = options.standardFilters.find((f) => f.field === 'formName');

        const sortTableFields = sorts?.filter((f) => columns.indexOf(f.field) > -1) ?? [];
        if (orphanedTransaction === true) {
            this.filterOrphanTransaction(countQb, dataQb);
        }

        if (formFilterField) {
            this.filterForm(countQb, dataQb, formFilterField);
        }

        try {
            const dataQuery = this.generateDataQuery({
                qb: dataQb,
                tableFields: tableFields,
                fields: normalizedFilters,
                attributeSortFields,
                sorts: sortTableFields,
                skip,
                take,
                conditions,
            });

            const rawQuery = dataQuery.getQuery();
            const rawParams = dataQuery.getParameters();

            return {
                rawQuery,
                rawParams,
            };
        } catch (error) {
            this._logger.error(error);
            return {
                data: [],
                total: 0,
            };
        }
    }

    filterForm(countQb: SelectQueryBuilder<any>, dataQb: SelectQueryBuilder<any>, formFilterField: FilterOptionDto): void {
        let findOperator = null;
        let findValue = null;
        switch (formFilterField.operator) {
            case 'in':
                findOperator = `IN (:...formColumn)`;
                findValue = formFilterField.value;
                break;
            case '=':
                findOperator = `= :formColumn`;
                findValue = formFilterField.value;
                break;
            default:
                findOperator = `ILIKE :formColumn`;
                findValue = `%${formFilterField.value}%`;
                break;
        }
        const formField = formFilterField.field.toLocaleLowerCase().replace('form', '');
        countQb.leftJoin(this.FORM_TABLE, 'f', 'f.id = tr.form_id');
        countQb.andWhere(`f.${formField} ${findOperator}`, {
            [`formColumn`]: findValue,
        });

        dataQb.leftJoin(this.FORM_TABLE, 'f', 'f.id = tr.form_id');
        dataQb.andWhere(`f.${formField} ${findOperator}`, {
            [`formColumn`]: findValue,
        });
    }

    filterOrphanTransaction(countQb: SelectQueryBuilder<any>, dataQb: SelectQueryBuilder<any>): void {
        countQb.leftJoin(this.TRANSACTION_RELATION_TABLE, 'ttr', 'ttr.target_transaction_id = tr.id');
        countQb.andWhere('ttr.origin_transaction_id IS NULL');

        dataQb.leftJoin(this.TRANSACTION_RELATION_TABLE, 'ttr', 'ttr.target_transaction_id = tr.id');
        dataQb.andWhere('ttr.origin_transaction_id IS NULL');
    }

    //#endregion PUBLIC METHODS
    private generateWhereClause(qb: SelectQueryBuilder<any>, filters: FilterOptionDto[], additionalQuery?: string, searchTerm?: string) {
        qb.andWhere(
            new Brackets((qb) => {
                filters.forEach((f: FilterOptionDto, index: number) => {
                    qb.andWhere(
                        new Brackets((qb) => {
                            let findOperator = null;
                            let findValue = null;
                            switch (f.operator) {
                                case 'in':
                                    findOperator = `IN (:...fieldValue${index})`;
                                    findValue = f.value;
                                    break;
                                case '=':
                                    findOperator = `= :fieldValue${index}`;
                                    findValue = f.value;
                                    break;
                                case '!=':
                                    findOperator = `!= :fieldValue${index}`;
                                    findValue = f.value;
                                    break;
                                case '>':
                                    findOperator = `> :fieldValue${index}`;
                                    findValue = f.value;
                                    break;
                                case '>=':
                                    findOperator = `>= :fieldValue${index}`;
                                    findValue = f.value;
                                    break;
                                case '<=':
                                    findOperator = `<= :fieldValue${index}`;
                                    findValue = f.value;
                                    break;
                                case '<':
                                    findOperator = `< :fieldValue${index}`;
                                    findValue = f.value;
                                    break;
                                default:
                                    findOperator = `ILIKE :fieldValue${index}`;
                                    findValue = `%${f.value}%`;
                                    break;
                            }

                            qb.where(`${this.formatAlias(f.field, 'f')}.field_id = :fieldId${index}`, {
                                [`fieldId${index}`]: f.field,
                            });
                            if (f.queryToOptionIds) {
                                qb.andWhere(`jsonb_exists(${this.formatAlias(f.field, 'f')}.field_option_ids, '${findValue}')`, {});
                            } else if (f.queryToDataFilterOptions) {
                                qb.andWhere(
                                    `${this.formatAlias(f.field, 'f')}.data IS NOT NULL AND EXISTS (
                                        SELECT 1
                                        FROM jsonb_array_elements_text(${this.formatAlias(f.field, 'f')}.data->'filter_options') AS option
                                        WHERE option = :findValue
                                    )`,
                                    { findValue },
                                );
                            } else {
                                qb.andWhere(`${this.formatAlias(f.field, 'f')}.field_value ${findOperator}`, {
                                    [`fieldValue${index}`]: findValue,
                                });
                            }
                        }),
                    );
                });
            }),
        );

        if (additionalQuery) {
            qb.andWhere(`( ${additionalQuery} )`);
        }
    }

    private generateFieldSources = (
        qb: SelectQueryBuilder<any>,
        fields: BaseQbParams['fields'],
        sorts: BaseQbParams['sorts'],
        conditionFieldIds: string[],
    ) => {
        const fieldTables = [
            ...new Set(
                [...fields.map((f) => f.field), ...sorts.map((s) => s.field), ...(conditionFieldIds ?? [])].map((field) =>
                    field.replace(`${PREFIX.DATA_REGISTER_FIELD}_`, ''),
                ),
            ),
        ];

        qb.from(this.TRANSACTION_TABLE, `tr`);

        if (fieldTables?.length) {
            fieldTables.forEach((fieldTable, index) => {
                qb.innerJoin(
                    this.TRANSACTION_FIELD_TABLE,
                    `${this.formatAlias(fieldTable, 'f')}`,
                    `${this.formatAlias(fieldTable, 'f')}.transaction_id = tr.id AND ${this.formatAlias(fieldTable, 'f')}.field_id = :fieldId${index}`,
                    {
                        [`fieldId${index}`]: fieldTable,
                    },
                );
            });
        }
    };

    private generateQuery = ({ qb, tableFields, fields, attributeSortFields, sorts, conditions }: BaseQbParams): BaseQbParams['qb'] => {
        if (tableFields.length) {
            this._queryBuilder.applyQueryFilters(qb, 'tr', tableFields, []);
        }

        const metadata = this._tenantConnection.getMetadata(TransactionEntity);
        const columns = metadata.columns;

        let fieldTypes: RelatedFieldType = {};
        let whereConditionFromConditionBuilder = undefined;
        let additionalJoinFieldType = [];
        if (conditions?.children1?.length) {
            getRelatedFieldTypes(conditions.children1 as any, fieldTypes);
            whereConditionFromConditionBuilder = UtilsService.generateWhereQqlFromConditionBuilder(conditions, fieldTypes);
            additionalJoinFieldType = (Object.keys(fieldTypes) ?? []).filter((x) => !columns.map((c) => c.propertyName).includes(x));

            columns.forEach((c) => {
                whereConditionFromConditionBuilder = whereConditionFromConditionBuilder.replaceAll(
                    `"f_${c.propertyName}"."field_value"`,
                    `"tr".${c.databaseName}`,
                );
            });
        }
        this.generateFieldSources(qb, fields, attributeSortFields, additionalJoinFieldType);

        if (fields?.length) {
            this.generateWhereClause(qb, fields);
        }

        if (whereConditionFromConditionBuilder) {
            qb.andWhere(`( ${whereConditionFromConditionBuilder} )`);
        }
        return qb;
    };

    private generateDataQuery = ({
        qb,
        tableFields,
        fields,
        attributeSortFields,
        sorts,
        skip,
        take,
        conditions,
    }: PagingQbParams): BaseQbParams['qb'] => {
        qb.select(`tr.id`, `transaction_id`);
        const query = this.generateQuery({ qb, tableFields, fields, attributeSortFields, sorts, conditions });

        attributeSortFields.forEach((sort) => {
            const fieldId = sort.field.replace(`${PREFIX.DATA_REGISTER_FIELD}_`, '');
            const order = (sort?.order?.toUpperCase() as any) ?? OrderType.ASC;
            query.addOrderBy(`${this.formatAlias(fieldId, 'f')}.field_value`, order);
        });

        sorts.forEach((sort) => {
            query.addOrderBy(`tr.${sort.field}`, (sort.order.toUpperCase() as any) ?? OrderType.ASC);
        });

        if (!take) {
            return query;
        }
        return query.limit(take).offset(skip);
    };

    private generateCountQuery = ({
        qb,
        tableFields,
        fields,
        attributeSortFields,
        sorts,
        conditions,
    }: BaseQbParams): BaseQbParams['qb'] => {
        if (tableFields.length) {
            this._queryBuilder.applyQueryFilters(qb, 'tr', tableFields, []);
        }

        const metadata = this._tenantConnection.getMetadata(TransactionEntity);
        const columns = metadata.columns;

        qb.select(`COUNT(tr.id) as count`);

        let fieldTypes: RelatedFieldType = {};
        let whereConditionFromConditionBuilder = undefined;
        let additionalJoinFieldType = [];

        if (conditions?.children1?.length) {
            getRelatedFieldTypes(conditions.children1 as any, fieldTypes);
            whereConditionFromConditionBuilder = UtilsService.generateWhereQqlFromConditionBuilder(conditions, fieldTypes);
            additionalJoinFieldType = (Object.keys(fieldTypes) ?? []).filter((x) => !columns.map((c) => c.propertyName).includes(x));

            columns.forEach((c) => {
                whereConditionFromConditionBuilder = whereConditionFromConditionBuilder.replaceAll(
                    `"f_${c.propertyName}"."field_value"`,
                    `"tr".${c.databaseName}`,
                );
            });
        }

        this.generateFieldSources(qb, fields, attributeSortFields, additionalJoinFieldType);

        if (fields.length) {
            this.generateWhereClause(qb, fields);
        }

        if (whereConditionFromConditionBuilder) {
            qb.andWhere(`( ${whereConditionFromConditionBuilder} )`);
        }
        return qb;
    };

    /**
     * Formats the given value by replacing hyphens with underscores and prepending the alias.
     *
     * @param value - The value to be formatted.
     * @param alias - The alias to be prepended to the formatted value. Defaults to PREFIX.FIELD.
     * @returns The formatted value with the alias.
     */
    private formatAlias = (value: string, alias: string = PREFIX.FIELD) => `${alias}_${value.split('-').join('_')}`;

    //#endregion PRIVATE METHODS
}
