import { Inject, Injectable } from '@nestjs/common';
import { DataSource, SelectQueryBuilder } from 'typeorm';

import { JsonTree } from '@react-awesome-query-builder/core';
import * as _ from 'lodash';
import { ClaimService, LoggerService, QueryBuilderService, USER_CLAIMS, UtilsService } from '../../common/src';
import { FilterOptionDto } from '../../common/src/modules/shared/dtos/filter-option.dto';
import { OperatorType } from '../../common/src/modules/shared/enums/operator.enum';
import { OrderType } from '../../common/src/modules/shared/enums/order.enum';
import { PREFIX } from '../../common/src/modules/shared/enums/prefix.enum';
import { DEFAULT_PAGE_SIZE } from '../../constant';
import { PROVIDER_KEYS } from '../../database/src/constants/providers';
import { DataRegisterEntity } from '../../database/src/entities/public/data-registers.public.entity';
import { DataRegisterTenancyEntity } from '../../database/src/entities/tenancy/data-registers.tenancy.entity';
import { getRelatedFieldTypes, RelatedFieldType } from '../../utils';
import { DataRegisterTransactionFilterOptions } from '../types/dr-transaction-filter-options';

type BaseQbParams = {
    qb: SelectQueryBuilder<any>;
    dataRegisterId: string;
    normalizedFilters: FilterOptionDto[];
    standardFilters?: FilterOptionDto[];
    sortField?: string;
    order?: OrderType;
    conditions?: JsonTree;
    attributeSortFields?: { field: string; order: OrderType }[];
    nonAttributeSortFields?: { field: string; order: OrderType }[];
    isAccurate?: boolean;
};

type PagingQbParams = BaseQbParams & {
    skip: number;
    take: number;
};

@Injectable()
export class TransactionQueryBuilderService {
    private TRANSACTION_TABLE = 'data_register_transactions';
    private TRANSACTION_FIELD_TABLE = 'data_register_transaction_fields';

    constructor(
        @Inject(PROVIDER_KEYS.TENANT_CONNECTION)
        private readonly _tenantConnection: DataSource,
        @Inject(PROVIDER_KEYS.DATA_SOURCE)
        private readonly _dataSource: DataSource,
        @Inject(USER_CLAIMS)
        private readonly _claims: ClaimService,
        private readonly _logger: LoggerService,
        private readonly _queryBuilder: QueryBuilderService,
    ) {}

    //#region PUBLIC METHODS
    public async filterAndSortByFields(options: DataRegisterTransactionFilterOptions): Promise<{
        data: string[];
        total: number;
    }> {
        const {
            selectFields,
            normalizedFilters,
            dataRegisterId,
            sortField,
            additionalQuery,
            searchTerm,
            transactionIds,
            order = OrderType.ASC,
            skip = 0,
            take = DEFAULT_PAGE_SIZE,
        } = options;

        // Start transaction
        try {
            let fields = _.cloneDeep(selectFields);
            if (additionalQuery?.length) {
                const additionalFields = this.extractUUIDs(additionalQuery);
                fields = _.uniq([...selectFields, ...additionalFields]);
            }

            const fieldIds = _.compact(_.uniq([...fields, ...(sortField ? [sortField] : [])]));

            const selectClause = this.generateSelectClause(fields, sortField);

            const queryBuilder = this._claims.accountId
                ? this._tenantConnection.createQueryBuilder()
                : this._dataSource.createQueryBuilder();

            // Build the filtered subquery
            const filteredSubQuery = queryBuilder
                .subQuery()
                .select([...selectClause, 'tf.data_register_transaction_id'])
                .from(`data_register_transaction_fields`, 'tf')
                .leftJoin(`data_register_transactions`, 'tr', 'tr.id = tf.data_register_transaction_id')
                .where('tr.data_register_id = :dataRegisterId', { dataRegisterId })
                .andWhere('tf.field_id IN (:...fieldIds)', { fieldIds: fieldIds });
            if (transactionIds?.length) {
                filteredSubQuery.andWhere('tr.id IN (:...transactionIds)', { transactionIds });
            }
            filteredSubQuery.groupBy('tf.data_register_transaction_id');

            // Query data
            const dataQuery = queryBuilder
                .select('f.data_register_transaction_id')
                .from(`(${filteredSubQuery.getQuery()})`, 'f')
                .setParameters(filteredSubQuery.getParameters());

            this.generateWhereClause(dataQuery, normalizedFilters, selectFields, additionalQuery, searchTerm);

            dataQuery
                .orderBy(sortField ? `${this.formatAlias(sortField)}` : null, order)
                .limit(take)
                .offset(skip);

            const result: Array<{ data_register_transaction_id: string }> = await dataQuery.getRawMany();

            const countQb = this._claims.accountId ? this._tenantConnection.createQueryBuilder() : this._dataSource.createQueryBuilder();
            // Query to count results

            const countQuery = countQb
                .select('COUNT(f.data_register_transaction_id) as count')
                .from(`(${filteredSubQuery.getQuery()})`, 'f')
                .setParameters(filteredSubQuery.getParameters());

            this.generateWhereClause(countQuery, normalizedFilters, selectFields, additionalQuery, searchTerm);

            const countResult = await countQuery.getRawOne();

            // Commit transaction

            if (!result.length) {
                return {
                    data: [],
                    total: 0,
                };
            }

            const dataRegisterTransactionIds = result.map((item) => item.data_register_transaction_id);
            return { data: dataRegisterTransactionIds, total: +countResult?.[0]?.count || 0 };
        } catch (error) {
            // Rollback transaction if any error occurs
            this._logger.error(error);
            return {
                data: [],
                total: 0,
            };
        } finally {
            // Release query runner
        }
    }

    public withOutPrefix = (field: string, prefix = PREFIX.DATA_REGISTER_FIELD) => field.replace(`${prefix}_`, '');

    public genSearchOrQuery = (fields: string[], searchQuery: string): string => {
        if (!searchQuery) {
            return '';
        }

        fields = fields.map((field) => `${this.formatAlias(field)} ILIKE '%${searchQuery}%'`);
        return `(${fields.join(' OR ')})`;
    };

    public async getPaging(options: DataRegisterTransactionFilterOptions): Promise<{
        data: string[];
        total: number;
    }> {
        const {
            normalizedFilters,
            dataRegisterId,
            standardFilters,
            sortField,
            order = OrderType.ASC,
            skip,
            take = DEFAULT_PAGE_SIZE,
            conditions,
            attributeSortFields,
            nonAttributeSortFields,
            isAccurate,
        } = options;

        const dataQb = this._claims.accountId ? this._tenantConnection.createQueryBuilder() : this._dataSource.createQueryBuilder();
        let columns: string[];

        if (this._claims.accountId) {
            columns = this._tenantConnection.getMetadata(DataRegisterTenancyEntity).columns.map((column) => column.propertyName);
        } else {
            columns = this._dataSource.getMetadata(DataRegisterEntity).columns.map((column) => column.propertyName);
        }

        const tableField = nonAttributeSortFields?.filter((f) => columns.indexOf(f.field) > -1);

        try {
            const dataQuery = this.generateDataQuery({
                qb: dataQb,
                dataRegisterId,
                normalizedFilters,
                standardFilters,
                sortField,
                order,
                skip,
                take,
                conditions,
                attributeSortFields,
                nonAttributeSortFields: tableField,
                isAccurate,
            });

            const data = await dataQuery.getRawMany();

            const countQb = this._claims.accountId ? this._tenantConnection.createQueryBuilder() : this._dataSource.createQueryBuilder();

            const countQuery = this.generateCountQuery(
                {
                    qb: countQb,
                    dataRegisterId,
                    standardFilters,
                    normalizedFilters,
                    attributeSortFields,
                    sortField,
                    order,
                    isAccurate,
                },
                conditions,
            );

            const count = await countQuery.getRawMany();

            const dataRegisterTransactionIds = data.map((item) => item.data_register_transaction_id);

            return { data: dataRegisterTransactionIds, total: count?.[0]?.count ?? 0 };
        } catch (error) {
            this._logger.error(error);
            return {
                data: [],
                total: 0,
            };
        }
    }

    //#endregion PUBLIC METHODS

    //#region PRIVATE METHODS
    private generateSelectClause(fields: string[], sortField?: string): string[] {
        if (!fields.length && !sortField) {
            return [];
        }

        const fieldClauses = fields.map(
            (field) => `MAX(CASE WHEN field_id = '${field}' THEN field_value ELSE NULL END) AS ${this.formatAlias(field)}`,
        );

        if (sortField) {
            fieldClauses.push(`MAX(CASE WHEN field_id = '${sortField}' THEN field_value ELSE NULL END) AS ${this.formatAlias(sortField)}`);
        }
        return fieldClauses;
    }

    private generateWhereClause(
        qb: SelectQueryBuilder<any>,
        filters: FilterOptionDto[],
        selectFields: string[],
        additionalQuery?: string,
        searchTerm?: string,
        isAccurate?: boolean,
    ) {
        // if (!filters.length) {
        //     return;
        // }
        const [firstFilter, ...restFilters] = filters;
        if (firstFilter) {
            if (firstFilter.queryToOptionIds) {
                qb.andWhere(`jsonb_exists("${this.formatAlias(firstFilter.field, 'f')}"."field_option_ids" , :filter${0})`, {
                    [`filter${0}`]: `${firstFilter.value}`,
                });
            } else {
                if (isAccurate) {
                    qb.andWhere(`${this.formatAlias(firstFilter.field, 'f')}."field_value" = :filter0`, {
                        filter0: `${firstFilter.value}`,
                    });
                } else {
                    qb.andWhere(`${this.formatAlias(firstFilter.field, 'f')}."field_value" ILIKE :filter0`, {
                        filter0: `%${firstFilter.value}%`,
                    });
                }
            }
        }
        if (restFilters.length) {
            restFilters.forEach((filter, index) => {
                if (filter.queryToOptionIds) {
                    qb.andWhere(`jsonb_exists("${this.formatAlias(filter.field, 'f')}"."field_option_ids" , :filter${index + 1})`, {
                        [`filter${index + 1}`]: `${filter.value}`,
                    });
                } else {
                    if (isAccurate) {
                        qb.andWhere(`${this.formatAlias(filter.field, 'f')}."field_value" = :filter${index + 1}`, {
                            [`filter${index + 1}`]: `${filter.value}`,
                        });
                    } else {
                        qb.andWhere(`${this.formatAlias(filter.field, 'f')}."field_value" ILIKE :filter${index + 1}`, {
                            [`filter${index + 1}`]: `%${filter.value}%`,
                        });
                    }
                }
            });
        }

        if (additionalQuery) {
            qb.andWhere(`( ${additionalQuery} )`);
        }

        if (searchTerm && selectFields.length) {
            const searchQueryClause = this.genSearchOrQuery(selectFields, searchTerm);
            if (searchQueryClause) {
                qb.andWhere(searchQueryClause);
            }
        }
    }

    private generateInnerJoins = (
        qb: SelectQueryBuilder<any>,
        fields: BaseQbParams['normalizedFilters'],
        conditions?: BaseQbParams['conditions'],
    ) => {
        if (!fields.length && !conditions?.children1?.length) {
            return;
        }
        const [first, ...restFields] = fields;
        const joinedColumns = [];

        let fieldTypes: RelatedFieldType = {};
        if (conditions?.children1?.length) {
            getRelatedFieldTypes(conditions.children1 as any, fieldTypes);
        }

        if (first) {
            joinedColumns.push(first.field);
        } else {
            joinedColumns.push(Object.keys(fieldTypes)[0]);
        }

        const andWheres = [];

        restFields?.forEach((field) => {
            if (joinedColumns.includes(field.field)) {
                return;
            }
            joinedColumns.push(field.field);

            let innerJoinQuery = `${this.formatAlias(field.field, 'f')}.data_register_transaction_id = ${this.formatAlias(first.field, 'f')}.data_register_transaction_id 
                    AND ${this.formatAlias(field.field, 'f')}.field_id = '${field.field}' `;

            if (field.queryToOptionIds)
                andWheres.push(`jsonb_exists(${this.formatAlias(field.field, 'f')}.field_option_ids, '${field.value}')`);
            else
                andWheres.push(
                    `${this.formatAlias(field.field, 'f')}.field_value ${this.normalizeOperator(first.operator)} '${this.normalizeValue(field.operator, field.value)}'`,
                );

            // if (field.queryToOptionIds)
            //     innerJoinQuery = `${innerJoinQuery}
            //             AND jsonb_exists(${this.formatAlias(field.field, 'f')}.field_option_ids, '${field.value}')`;
            // else
            //     innerJoinQuery = `${innerJoinQuery}
            //             AND ${this.formatAlias(field.field, 'f')}.field_value ${this.normalizeOperator(first.operator)} '${this.normalizeValue(field.operator, field.value)}'`;

            qb.innerJoin(`data_register_transaction_fields`, `${this.formatAlias(field.field, 'f')}`, innerJoinQuery);
        });

        if (!_.isEmpty(fieldTypes)) {
            Object.keys(fieldTypes)?.forEach((fieldId) => {
                if (joinedColumns.includes(fieldId)) {
                    return;
                }
                joinedColumns.push(fieldId);

                let innerJoinQuery = `${this.formatAlias(fieldId, 'f')}.data_register_transaction_id = ${this.formatAlias(joinedColumns[0], 'f')}.data_register_transaction_id 
                AND ${this.formatAlias(fieldId, 'f')}.field_id = '${fieldId}' `;

                qb.innerJoin(`data_register_transaction_fields`, `${this.formatAlias(fieldId, 'f')}`, innerJoinQuery);
            });

            const whereConditionFromConditionBuilder = UtilsService.generateWhereQqlFromConditionBuilder(conditions, fieldTypes);
            if (whereConditionFromConditionBuilder) {
                andWheres.push(whereConditionFromConditionBuilder);
            }
        }

        if (first) {
            let firstWhereClause = `${this.formatAlias(first.field, 'f')}.field_value ${this.normalizeOperator(first.operator)} :firstValue`;

            if (first.queryToOptionIds) {
                firstWhereClause = `jsonb_exists(${this.formatAlias(first.field, 'f')}.field_option_ids, '${first.value}')`;
            }
            qb.andWhere(`${this.formatAlias(first.field, 'f')}.field_id = :firstId`, { firstId: first.field }).andWhere(firstWhereClause, {
                firstValue: `${this.normalizeValue(first.operator, first.value)}`,
            });
        }

        andWheres.forEach((where) => qb.andWhere(where));
    };

    private normalizeOperator(operator: OperatorType) {
        let result = 'ILIKE';
        const standardOperators = [OperatorType.equals, OperatorType.iLike, OperatorType.iLike];
        if (standardOperators.includes(operator)) {
            result = operator;
        }
        return result;
    }

    private normalizeValue(operand: OperatorType, value: string | number | string[]) {
        let result = value;
        const likeOperators = [OperatorType.like, OperatorType.iLike];
        if (likeOperators.includes(operand)) {
            result = `%${value}%`;
        }
        return result;
    }
    private generateFieldSources = (
        qb: SelectQueryBuilder<any>,
        normalizedFilters: BaseQbParams['normalizedFilters'],
        attributeSortFields: BaseQbParams['attributeSortFields'],
        conditionFieldIds: string[],
    ) => {
        const fieldTables = [
            ...new Set(
                [...normalizedFilters.map((f) => f.field), ...attributeSortFields.map((s) => s.field), ...(conditionFieldIds ?? [])].map(
                    (field) => field.replace(`${PREFIX.DATA_REGISTER_FIELD}_`, ''),
                ),
            ),
        ];

        qb.from(this.TRANSACTION_TABLE, `tr`);

        if (fieldTables?.length) {
            fieldTables.forEach((fieldTable, index) => {
                qb.innerJoin(
                    this.TRANSACTION_FIELD_TABLE,
                    `${this.formatAlias(fieldTable, 'f')}`,
                    `${this.formatAlias(fieldTable, 'f')}.data_register_transaction_id = tr.id AND ${this.formatAlias(fieldTable, 'f')}.field_id = :fieldId${index}`,
                    {
                        [`fieldId${index}`]: fieldTable,
                    },
                );
            });
        }
    };

    private generateQuery = ({
        qb,
        dataRegisterId,
        normalizedFilters = [],
        standardFilters = [],
        conditions,
        attributeSortFields = [],
        nonAttributeSortFields = [],
        isAccurate,
    }: BaseQbParams): BaseQbParams['qb'] => {
        if (standardFilters?.length) {
            this._queryBuilder.applyQueryFilters(qb, 'tr', standardFilters, []);
        }

        let fieldTypes: RelatedFieldType = {};
        let whereConditionFromConditionBuilder = undefined;

        if (conditions?.children1?.length) {
            getRelatedFieldTypes(conditions.children1 as any, fieldTypes);
            whereConditionFromConditionBuilder = UtilsService.generateWhereQqlFromConditionBuilder(conditions, fieldTypes);
        }

        this.generateFieldSources(qb, normalizedFilters, attributeSortFields, Object.keys(fieldTypes));

        if (normalizedFilters?.length) {
            this.generateWhereClause(qb, normalizedFilters, [], undefined, undefined, isAccurate);
        }

        if (whereConditionFromConditionBuilder) {
            qb.andWhere(`( ${whereConditionFromConditionBuilder} )`);
        }

        return qb;
    };

    private generateDataQuery = ({
        qb,
        dataRegisterId,
        normalizedFilters = [],
        sortField,
        standardFilters = [],
        order,
        skip,
        take,
        conditions,
        attributeSortFields = [],
        nonAttributeSortFields = [],
        isAccurate,
    }: PagingQbParams): BaseQbParams['qb'] => {
        qb.select(`tr.id `, `data_register_transaction_id`);

        const query = this.generateQuery({
            qb,
            dataRegisterId,
            normalizedFilters,
            standardFilters,
            sortField,
            order,
            conditions,
            attributeSortFields,
            nonAttributeSortFields,
            isAccurate,
        });

        attributeSortFields.forEach((sort) => {
            const fieldId = sort.field.replace(`${PREFIX.DATA_REGISTER_FIELD}_`, '');
            const order = (sort?.order?.toUpperCase() as any) ?? OrderType.ASC;
            query.addOrderBy(`${this.formatAlias(fieldId, 'f')}.field_value`, order);
        });

        nonAttributeSortFields.forEach((sort) => {
            query.addOrderBy(`tr.${sort.field}`, (sort.order.toUpperCase() as any) ?? OrderType.ASC);
        });

        if (!take) {
            return query;
        }
        return query.limit(take).offset(skip);
    };

    private generateCountQuery = (
        { qb, attributeSortFields = [], standardFilters = [], normalizedFilters = [], sortField, isAccurate }: BaseQbParams,
        conditions?: JsonTree,
    ): BaseQbParams['qb'] => {
        if (standardFilters.length) {
            this._queryBuilder.applyQueryFilters(qb, 'tr', standardFilters, []);
        }

        qb.select(`COUNT(tr.id) as count`);

        let fieldTypes: RelatedFieldType = {};
        let whereConditionFromConditionBuilder = undefined;

        if (conditions?.children1?.length) {
            getRelatedFieldTypes(conditions.children1 as any, fieldTypes);
            whereConditionFromConditionBuilder = UtilsService.generateWhereQqlFromConditionBuilder(conditions, fieldTypes);
        }

        this.generateFieldSources(qb, normalizedFilters, attributeSortFields, Object.keys(fieldTypes));

        if (normalizedFilters.length) {
            this.generateWhereClause(qb, normalizedFilters, whereConditionFromConditionBuilder, undefined, undefined, isAccurate);
        }

        if (whereConditionFromConditionBuilder?.length) {
            qb.andWhere(whereConditionFromConditionBuilder);
        }

        return qb;
    };

    /**
     * Formats the given value by replacing hyphens with underscores and prepending the alias.
     *
     * @param value - The value to be formatted.
     * @param alias - The alias to be prepended to the formatted value. Defaults to PREFIX.FIELD.
     * @returns The formatted value with the alias.
     */
    private formatAlias = (value: string, alias: string = PREFIX.FIELD) => `${alias}_${value.split('-').join('_')}`;

    private extractUUIDs = (data: string): string[] => {
        // Regex pattern for UUID
        const pattern = `${PREFIX.FIELD}_[0-9a-fA-F]{8}_[0-9a-fA-F]{4}_[0-9a-fA-F]{4}_[0-9a-fA-F]{4}_[0-9a-fA-F]{12}`;
        const regex = new RegExp(pattern, 'g');

        // Find all matches
        const matches = _.uniq(data.match(regex));

        const uuids = matches.map((match) => match.split('_').slice(1).join('-'));

        return uuids || [];
    };

    //#endregion PRIVATE METHODS
}
