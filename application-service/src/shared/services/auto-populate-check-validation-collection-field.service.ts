import { Inject, Injectable } from '@nestjs/common';
import { isArray, uniq } from 'lodash';
import { FindOptionsWhere, Repository } from 'typeorm';
import { LoggerService, UtilsService } from '../../common/src';
import { FormFieldTypeEnum } from '../../common/src/modules/shared/enums/form-field-type.enum';
import { OBJECT_SELECTABLE_FIELD_TYPES } from '../../database/src/constants/field';
import { PROVIDER_KEYS } from '../../database/src/constants/providers';
import { FormCollectionTenancyEntity } from '../../database/src/entities/tenancy/form-collection.tenancy.entity';
import { TransactionFieldEntity } from '../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { DataRegisterTypeEnum } from '../../database/src/shared/enums/data-register-type.enum';
import { TransactionFieldContextTypeEnum } from '../../database/src/shared/enums/transaction-field.enum';
import { PopulateCollectionField } from '../../modules/form/dtos/populate-collection-field.dto';
import { CheckCriteriaValidationService } from '../../modules/form/services/check-criteria-validation.service';
import { PopulateTransactionFieldService } from '../../modules/form/services/populate-transaction-field.service';
import { AutoPopulateAndCheckValidation, CheckValidationPayload } from '../types/auto-populate-and-check-validation';
import { StyleAndOverrideFields } from 'src/modules/data-register/dtos/requests/create-data-register-transaction.request';

@Injectable()
export class AutoPopulateAndCheckValidationCollectionFieldService {
    private readonly _chunkSize = 50;

    constructor(
        private readonly _populateTransactionFieldService: PopulateTransactionFieldService,

        @Inject(PROVIDER_KEYS.FORM_TRANSACTION_FIELD_REPOSITORY)
        private readonly formTransactionFieldRepository: Repository<TransactionFieldEntity>,

        @Inject(PROVIDER_KEYS.FORM_COLLECTION_TENANCY_REPOSITORY)
        private readonly _formCollectionTenancyRepo: Repository<FormCollectionTenancyEntity>,

        private readonly _loggerService: LoggerService,

        private readonly _checkCriteriaValidationService: CheckCriteriaValidationService,
    ) {}

    public async autoPopulateCollectionFields({
        activeFormVersionId,
        transactionId,
        ignoreSave,
        formValues,
        triggerFields,
        lockedFieldIds,
        cachedFormVersion,
        purpleTRACPopulatedData,
        targetFieldChangeIds,
        targetFieldChangeFromRegisterId,
        payloadDocuments,
        isTest,
    }: AutoPopulateAndCheckValidation) {
        const {
            populateCollectionFields,
            populatedCollectionIds = [],
            runCollectionIds = [],
            styleAndOverrideFields = {},
        } = await this.autoPopulateRelatedTransactionsCollectionFields({
            activeFormVersionId: activeFormVersionId,
            transactionId: transactionId,
            ignoreSave,
            formValues,
            triggerFields,
            lockedFieldIds,
            cachedFormVersion,
            purpleTRACPopulatedData,
            targetFieldChangeIds,
            targetFieldChangeFromRegisterId,
            payloadDocuments,
            isTest,
        });

        return { populateCollectionFields, populatedCollectionIds, runCollectionIds, styleAndOverrideFields };
    }

    public async checkValidation({
        activeFormVersionId,
        transactionId,
        formValues,
        cachedFormVersion,
        transactionFields: requestTransactionFields = [],
        isTest,
    }: CheckValidationPayload) {
        const { validations, transactionFields } = await this.checkCollectionValidation({
            activeFormVersionId: activeFormVersionId,
            transactionId: transactionId,
            formValues,
            transactionFields: requestTransactionFields,
            options: {
                isPriorityTranFieldsFormValues: false,
            },
            cachedFormVersion,
            isTest,
        });

        return {
            collectionValidation: validations,
            transactionFields,
        };
    }

    public async autoPopulateRelatedTransactionsCollectionFields({
        activeFormVersionId,
        transactionId,
        ignoreSave,
        formValues,
        triggerFields,
        cachedFormVersion,
        purpleTRACPopulatedData,
        targetFieldChangeIds,
        targetFieldChangeFromRegisterId,
        payloadDocuments,
        isTest,
    }: AutoPopulateAndCheckValidation): Promise<{
        populateCollectionFields: PopulateCollectionField[];
        populatedCollectionIds: string[];
        runCollectionIds: string[];
        styleAndOverrideFields: StyleAndOverrideFields;
    }> {
        const _formValues = Object.keys(formValues ?? {})?.length
            ? formValues
            : await this.convertTransactionFieldsToFormValues({
                  transactionId,
              });

        const collectionPopulate = await this._populateTransactionFieldService.populateCollectionFieldData({
            formVersionId: activeFormVersionId,
            formValues: _formValues,
            triggerFields,
            transactionId: transactionId,
            cachedFormVersion,
            purpleTRACPopulatedData,
            targetFieldChangeIds,
            targetFieldChangeFromRegisterId,
            payloadDocuments,
            isTest,
        });

        const populateFields = collectionPopulate?.result ?? {};
        const runCollectionIds: string[] = uniq(collectionPopulate?.runCollectionIds || []);
        const populatedCollectionIds = Object.keys(populateFields);
        const mappingPopulateFields = collectionPopulate?.mappingFieldResult ?? {};
        const dynamicCollectionItemIds: Record<string, boolean> = {};

        const updateFields = [];
        const result: PopulateCollectionField[] = [];

        const updateField = ({
            collectionIdentityId,
            collectionItemCombineIdentityId,
            fieldId,
            fieldValue,
        }: {
            collectionIdentityId: string;
            collectionItemCombineIdentityId: string;
            fieldId: string;
            fieldValue: any;
        }) => {
            const [collectionItemIdentityId, collectionItemKey, order] = (collectionItemCombineIdentityId ?? '').split('_');
            const collectionItemExternalId = collectionItemKey;
            if (collectionItemExternalId) {
                dynamicCollectionItemIds[collectionItemIdentityId] = true;
            }
            const externalOrder = +order ? +order : -1;

            if (!fieldValue) {
                if (collectionItemKey) {
                    fieldValue = null;
                }
            }
            let fieldOptionIds: string[] = [];

            const existedMapping = mappingPopulateFields[`${collectionIdentityId}_${collectionItemIdentityId}_${fieldId}`];
            if (OBJECT_SELECTABLE_FIELD_TYPES.includes(existedMapping?.type as FormFieldTypeEnum)) {
                fieldOptionIds = isArray(fieldValue) ? fieldValue : [fieldValue];
                fieldValue = existedMapping.label;
            }

            const baseCondition: FindOptionsWhere<TransactionFieldEntity> = {
                collectionId: collectionIdentityId,
                collectionItemId: collectionItemIdentityId,
                fieldId: fieldId,
                transactionId: transactionId,
            };

            let saveData: Partial<TransactionFieldEntity>;
            let data;
            const externalContextId =
                mappingPopulateFields?.[`${collectionIdentityId}_${collectionItemCombineIdentityId}_${fieldId}`]?.externalContextId;
            let isSire2AnswerField = false;
            if (fieldValue && typeof fieldValue === 'object' && 'inspectionId' in fieldValue) {
                isSire2AnswerField = true;
                data = collectionItemExternalId
                    ? {
                          collectionItemExternalId,
                          externalContextId,
                          fieldValue,
                          externalOrder,
                      }
                    : undefined;
                saveData = {
                    fieldValue: 'Sire2Answer',
                    fieldOptionIds: fieldOptionIds?.length ? fieldOptionIds : undefined,
                    rowKey: collectionItemKey,
                    data,
                };
            } else {
                data = collectionItemExternalId
                    ? {
                          collectionItemExternalId,
                          externalContextId,
                          externalOrder,
                      }
                    : undefined;
                saveData = {
                    fieldValue: fieldValue as string,
                    fieldOptionIds: fieldOptionIds?.length ? fieldOptionIds : undefined,
                    rowKey: collectionItemKey,
                    data,
                };
            }

            if (!ignoreSave) {
                updateFields.push(this.formTransactionFieldRepository.update(baseCondition, saveData));
            }

            const registerRecordId = collectionPopulate.registerRecordMap?.[collectionIdentityId]?.[collectionItemIdentityId]?.[fieldId];
            result.push({
                collectionId: collectionIdentityId,
                collectionItemId: collectionItemIdentityId,
                fieldId: fieldId,
                transactionId: transactionId,
                fieldValue: isSire2AnswerField ? 'Sire2Answer' : fieldValue,
                fieldOptionIds: fieldOptionIds?.length ? fieldOptionIds : undefined,
                rowKey: collectionItemKey ? collectionItemKey : undefined,
                registerRecordId,
                data,
                fieldType: existedMapping?.type as FormFieldTypeEnum,
            });
        };

        const processFields = ({
            collectionIdentityId,
            collectionItemIdentityId,
            fields,
        }: {
            collectionIdentityId: string;
            collectionItemIdentityId: string;
            fields: Record<string, any>;
            options?: {
                isPopulatedExternalList?: boolean;
            };
        }) => {
            Object.entries(fields ?? {}).forEach(([fieldId, fieldValue]) => {
                updateField({ collectionIdentityId, collectionItemCombineIdentityId: collectionItemIdentityId, fieldId, fieldValue });
            });
        };

        const processCollectionItems = (collectionIdentityId: string, collectionItems: Record<string, any>) => {
            Object.entries(collectionItems ?? {}).forEach(([collectionItemIdentityId, fields]) => {
                processFields({
                    collectionIdentityId,
                    collectionItemIdentityId,
                    fields,
                });
            });
        };

        Object.entries(populateFields).forEach(([collectionIdentityId, collectionItems]) => {
            processCollectionItems(collectionIdentityId, collectionItems);
        });

        const populateCollectionFields = (result || []).filter((tf) => {
            if (!dynamicCollectionItemIds[tf.collectionItemId]) return true;

            if (dynamicCollectionItemIds[tf.collectionItemId] && !tf.data?.collectionItemExternalId) return false;

            return true;
        });

        if (!updateFields?.length) {
            return {
                populateCollectionFields,
                populatedCollectionIds,
                runCollectionIds,
                styleAndOverrideFields: collectionPopulate.styleAndOverrideFields,
            };
        }

        try {
            if (!ignoreSave) {
                await this._processInChunks(updateFields);
            }

            return {
                populateCollectionFields,
                populatedCollectionIds,
                runCollectionIds,
                styleAndOverrideFields: collectionPopulate.styleAndOverrideFields,
            };
        } catch (err) {
            this._loggerService.error(err);
            return {
                populateCollectionFields: [],
                populatedCollectionIds: [],
                runCollectionIds: [],
                styleAndOverrideFields: {},
            };
        }
    }

    public async checkCollectionValidation({
        activeFormVersionId,
        transactionId,
        formValues,
        triggerField,
        cachedFormVersion,
        transactionFields: transactionFieldsParams = [],
        options,
        isTest,
    }: CheckValidationPayload) {
        console.time('checkCollectionValidation_getTransactionFields');
        const transactionFields = transactionFieldsParams || [];
        const _transactionFieldValues = (transactionFields || []).reduce((prev, curr) => {
            let fieldValue = curr.fieldValue;

            if (curr.fieldOptionIds?.length) {
                fieldValue = curr.fieldOptionIds[0];
            }

            switch (curr.contextType) {
                case TransactionFieldContextTypeEnum.COLLECTION:
                    const key = UtilsService.combineCollectionKeys({
                        collectionIdentityId: curr.collectionId,
                        collectionItemId: '',
                        collectionItemIdentityId: curr.collectionItemId,
                        collectionItemKey: curr.rowKey,
                        fieldId: '',
                        fieldIdentityId: curr.fieldId,
                    });
                    prev[key] = fieldValue;
                    break;

                case TransactionFieldContextTypeEnum.FORM:
                    prev[curr.fieldId] = fieldValue;

                    break;
                default:
                    prev[curr.fieldId] = fieldValue;

                    break;
            }

            return prev;
        }, {});

        const _formValues = Object.keys(formValues ?? {})?.length
            ? formValues
            : await this.convertTransactionFieldsToFormValues({
                  transactionId,
                  transactionFieldParam: transactionFieldsParams,
              });

        const combineFromValues = this._mergeFormValues({
            isPriorityTranFieldsFormValues: options?.isPriorityTranFieldsFormValues,
            formValues: _formValues,
            transactionFieldValues: _transactionFieldValues,
        });
        console.timeEnd('checkCollectionValidation_getTransactionFields');

        const triggerFieldId = triggerField
            ? UtilsService.combineCollectionKeys({
                  collectionIdentityId: triggerField.collectionId,
                  collectionItemIdentityId: triggerField.collectionItemId,
                  fieldId: triggerField.fieldId,
                  fieldIdentityId: triggerField.fieldId,
                  collectionItemKey: triggerField.rowKey,
                  collectionItemId: '',
              })
            : null;

        try {
            const result = await this._checkCriteriaValidationService.checkCriteriaValidation({
                formVersionId: activeFormVersionId,
                transactionId,
                formValues: combineFromValues,
                collectionType: DataRegisterTypeEnum.Collection,
                transactionFields,
                triggerFieldId,
                cachedFormVersion,
                isTest,
            });

            return {
                validations: result,
                transactionFields,
            };
        } catch (err) {
            this._loggerService.error(err);
        }
    }

    public async convertTransactionFieldsToFormValues({
        transactionId,
        transactionFieldParam,
    }: {
        transactionId: string;
        transactionFieldParam?: TransactionFieldEntity[];
    }) {
        const transactionFields = transactionFieldParam?.length
            ? transactionFieldParam
            : await this.formTransactionFieldRepository.findBy({
                  transactionId: transactionId,
              });

        if (!transactionFields?.length) {
            return {};
        }

        const formValues = transactionFields.reduce((prev, curr) => {
            let fieldValue = curr.fieldValue;

            if (curr.fieldOptionIds?.length) {
                fieldValue = curr.fieldOptionIds.join(',');
            }

            switch (curr.contextType) {
                case TransactionFieldContextTypeEnum.COLLECTION:
                case TransactionFieldContextTypeEnum.COLLECTION_ITEM:
                    const key = UtilsService.combineCollectionKeys({
                        collectionIdentityId: curr.collectionId,
                        collectionItemId: '',
                        collectionItemIdentityId: curr.collectionItemId,
                        collectionItemKey: curr.rowKey,
                        fieldId: '',
                        fieldIdentityId: curr.fieldId,
                    });
                    prev[key] = fieldValue;
                    break;
                default:
                    prev[curr.fieldId] = fieldValue;
                    break;
            }

            return prev;
        }, {});

        return formValues;
    }

    private async _processInChunks(tasks) {
        for (let i = 0; i < tasks.length; i += this._chunkSize) {
            const chunk = tasks.slice(i, i + this._chunkSize);
            await Promise.all(chunk);
        }
    }

    private _mergeFormValues = ({
        isPriorityTranFieldsFormValues,
        formValues,
        transactionFieldValues,
    }: {
        formValues: Record<string, any>;
        transactionFieldValues: Record<string, any>;
        isPriorityTranFieldsFormValues: boolean;
    }) => {
        let origin = isPriorityTranFieldsFormValues ? transactionFieldValues : formValues;
        let merge = isPriorityTranFieldsFormValues ? formValues : transactionFieldValues;
        Object.entries(merge ?? {}).forEach(([key, value]) => {
            const { collectionIdentityId, collectionItemIdentityId, collectionItemKey, fieldIdentityId } =
                UtilsService.extractCollectionKeys(key);

            if (collectionItemIdentityId && collectionIdentityId) {
                const formatKey = UtilsService.combineCollectionKeys({
                    collectionIdentityId,
                    collectionItemId: '',
                    collectionItemIdentityId,
                    collectionItemKey,
                    fieldId: '',
                    fieldIdentityId,
                });

                if (!origin[formatKey]) {
                    origin[formatKey] = value;
                }
            } else {
                if (!origin[key]) {
                    origin[key] = value;
                }
            }
        });

        return origin;
    };
}
