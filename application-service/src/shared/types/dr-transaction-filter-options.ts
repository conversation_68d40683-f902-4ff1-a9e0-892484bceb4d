import { JsonTree } from '@react-awesome-query-builder/core';
import { FilterOptionDto } from '../../common/src/modules/shared/dtos/filter-option.dto';
import { OrderType } from '../../common/src/modules/shared/enums/order.enum';

export type DataRegisterTransactionFilterOptions = {
    selectFields: string[];
    normalizedFilters: FilterOptionDto[];
    standardFilters?: FilterOptionDto[];
    dataRegisterId: string;
    order: OrderType;
    skip: number;
    take: number;
    searchTerm?: string;
    transactionIds?: string[];
    additionalQuery?: string;
    sortField?: string;
    conditions?: JsonTree;
    attributeSortFields?: { field: string; order: OrderType }[];
    nonAttributeSortFields?: { field: string; order: OrderType }[];
    isAccurate?: boolean;
};
