import {
    FieldStyleRequest,
    OverrideRecordRequest,
    StyleAndOverrideFields,
} from 'src/modules/data-register/dtos/requests/create-data-register-transaction.request';
import { DataRegisterTransactionFieldEntity } from '../../database/src/entities/public/data-register-transaction-field.public.entity';
import { DataRegisterTransactionEntity } from '../../database/src/entities/public/data-register-transaction.public.entity';
import { FormVersionTenancyEntity } from '../../database/src/entities/tenancy/form-version.tenancy.entity';
import { TransactionFieldEntity } from '../../database/src/entities/tenancy/transaction-field.tenancy.entity';
import { TransactionEntity } from '../../database/src/entities/tenancy/transaction.tenancy.entity';
import { FormFieldTypeEnum } from '../../database/src/shared/enums/form-field-type.enum';
import { PopulateCollectionField } from '../../modules/form/dtos/populate-collection-field.dto';
import { EditFormTransactionFieldRequest } from '../../modules/form/dtos/requests/create-form-transaction.request';
import { AutoPopulateDataLakeResponse } from '../common/dto/autopopulate-datalake.dto';

export type AutoPopulateAndCheckValidation = {
    activeFormVersionId: string;
    transactionId: string;
    ignoreSave?: boolean;
    formValues?: Record<string, any>;
    triggerFields?: string[];
    triggerField?: TransactionFieldEntity;
    options?: {
        isPriorityTranFieldsFormValues?: boolean;
    };
    lockedFieldIds?: string[];
    cachedFormVersion?: any;
    purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>;
    targetFieldChangeIds?: string[];
    targetFieldChangeFromRegisterId?: string;
    payloadDocuments?: Record<string, string>;
    isTest: boolean;
};

export type CheckValidationPayload = AutoPopulateAndCheckValidation & {
    transactionFields: TransactionFieldEntity[];
};

export type PopulatedValueToRegisterFieldFromRegisterResponse = {
    transactionField: (DataRegisterTransactionEntity & { displayLabel?: string }) | DataRegisterTransactionFieldEntity | undefined;
    overrideRecord?: OverrideRecordRequest;
    overrideStyle?: FieldStyleRequest;
};

export type AfterPopulateCollectionResult = {
    updatedTransactionFields: TransactionFieldEntity[];
    collectionChangeFields: TransactionFieldEntity[];
    deletedCollectionFieldIds: Set<string>;
};

export type MutateExternalCollectionField = {
    [collectionId: string]: {
        changes: {
            [rowKey: string]: TransactionFieldEntity[];
        };
    };
};

export type ProcessPopulateCollectionResult = {
    transactionFields: TransactionFieldEntity[];
    populatedCollectionIds: string[];
    collectionChangeFields: TransactionFieldEntity[];
    deletedCollectionFieldIds: Set<string>;
    styleAndOverrideFields: StyleAndOverrideFields;
};

export type UpdatePopulateCollectionParam = {
    populateCollectionFields: PopulateCollectionField[];
    transactionId: string;
    runCollectionIds: string[];
    lockedFieldIds: string[];
    onlyUpdateIfRelevantRegisterRecordIds?: string[];
    transactionFields: TransactionFieldEntity[];
    // Only for update collection when external data changes ACTION
    updatingCollectionFields: Array<UpdateCollectionFieldActionParam>;
    defaultCollectionValuesOnCreation: Record<string, any>;
};

export type AutoPopulateFormFieldParam = {
    requestTransactionFields: EditFormTransactionFieldRequest[];
    cloneTransactionFields: TransactionFieldEntity[];
    transaction: TransactionEntity;
    formVersion: FormVersionTenancyEntity;
    transactionFields: TransactionFieldEntity[];
    formValues: Record<string, any>;
    updatingFormFields: Array<UpdateFieldActionParam>;
    purpleTRACPopulatedData?: Array<Partial<AutoPopulateDataLakeResponse>>;
    forceRun?: boolean;
    payloadDocuments?: Record<string, any>;
    targetFieldChangeIds?: string[];
    targetFieldChangeFromRegisterId?: string;
};

export type UpdateFieldActionParam = {
    fieldId: string;
    fieldValue: string | string[] | any;
    fieldType: FormFieldTypeEnum;
};

export type UpdateCollectionFieldActionParam = UpdateFieldActionParam & {
    collectionId: string;
    collectionItemId: string;
};

export type AutoPopulateFormFieldsResult = {
    updatedRequestTransactionFields: EditFormTransactionFieldRequest[];
    updatedTransactionFields: TransactionFieldEntity[];
    formValues: Record<string, any>;
    isChanged: boolean;
    populatedFormFieldIds?: string[];
    styleAndOverrideFields?: StyleAndOverrideFields;
};

export type PopulatedFormFieldValuesResult = {
    transactionFields: Record<string, any> | TransactionFieldEntity[];
    styleAndOverrideFields?: StyleAndOverrideFields;
};
