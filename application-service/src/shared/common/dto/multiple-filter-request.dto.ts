import { AutoMap } from '@automapper/classes';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { UtilsService } from '../../../common/src';
import { FilterOptionDto } from '../../../common/src/modules/shared/dtos/filter-option.dto';
import { OrderOptionDto } from '../../../common/src/modules/shared/dtos/order-option.dto';
import { OrderType } from '../../../common/src/modules/shared/enums/order.enum';
import { GetPaginationRequestDto } from './pagination-request.dto';

function isUUID(str) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
}

export class MultipleFilterRequestDto extends GetPaginationRequestDto {
    @AutoMap()
    @ApiPropertyOptional({ name: 'filters' })
    @IsOptional()
    @Transform((value) => {
        const result = value?.value?.map((item) => {
            return {
                field: item.field,
                operator: UtilsService.transformOperator(item.operator),
                value: item.value,
                queryToOptionIds: isUUID(item.value) && !['dataRegisterId', 'transactionId'].includes(item.field),
                queryToDataFilterOptions: item.queryToDataFilterOptions?.trim()?.toLowerCase() === 'true',
            } as FilterOptionDto;
        });
        return result;
    })
    filters?: FilterOptionDto[];

    @AutoMap()
    @ApiPropertyOptional({ name: 'sorters' })
    @IsOptional()
    @Transform((value) => {
        const result = value?.value?.map((item) => {
            return {
                field: item.field,
                order: item.order ? item.order.toUpperCase() : OrderType.ASC,
            } as OrderOptionDto;
        });
        return result;
    })
    sorters?: OrderOptionDto[];
}
