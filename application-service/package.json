{"author": "", "dependencies": {"@automapper/classes": "^8.7.7", "@automapper/core": "^8.7.7", "@automapper/nestjs": "^8.7.7", "@aws-sdk/client-s3": "^3.705.0", "@aws-sdk/s3-request-presigner": "^3.709.0", "@azure/storage-blob": "^12.26.0", "@eturino/ioredis-del-by-pattern": "^2.0.1", "@faker-js/faker": "^9.2.0", "@golevelup/nestjs-rabbitmq": "^4.1.0", "@golevelup/ts-jest": "^0.5.6", "@google/genai": "^0.13.0", "@langchain/community": "^0.3.31", "@langchain/core": "^0.3.40", "@langchain/google-genai": "^0.2.4", "@langchain/openai": "^0.4.4", "@nestjs/axios": "^3.0.1", "@nestjs/bull": "^10.0.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.3", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.8", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/terminus": "^10.2.0", "@nestjs/typeorm": "^10.0.1", "@nestjs/websockets": "^10.3.0", "@react-awesome-query-builder/core": "^6.5.2", "@sendgrid/mail": "^8.1.4", "@sentry/node": "^7.88.0", "@socket.io/redis-adapter": "^8.2.1", "@types/multer": "^1.4.12", "@types/redlock": "^4.0.7", "auth0": "^4.4.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.0", "bullmq": "^5.56.0", "cache-manager": "^5.3.1", "cache-manager-ioredis": "^2.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cross-var": "^1.1.0", "dayjs": "^1.11.10", "deep-object-diff": "^1.1.9", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.1.0", "entities": "^5.0.0", "handlebars": "^4.7.8", "htmlparser2": "^9.1.0", "inline-css": "^4.0.2", "ioredis": "^5.6.1", "json-logic-engine": "^1.3.4", "json-logic-js": "^2.0.5", "jwt-decode": "^4.0.0", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "moleculer": "^0.14.32", "morgan": "^1.10.0", "mqtt": "^5.3.3", "nestjs-request-context": "^3.0.0", "newrelic": "^12.16.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "preview-email": "^3.0.19", "redis": "^4.6.11", "redlock": "^4.2.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "slugify": "^1.6.6", "snake-camel": "^1.0.8", "socket.io": "^4.7.2", "swagger-ui-express": "^5.0.0", "twilio": "^5.4.2", "typeorm": "^0.3.17", "uuid": "^10.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xml2js": "^0.6.2"}, "description": "", "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.4", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.6", "jest": "^29.5.0", "jest-junit": "^16.0.0", "lint-staged": "^15.2.10", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "license": "UNLICENSED", "name": "application-service", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "prepare": "husky install", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch", "start:prod": "node dist/main", "test": "jest --logHeapUsage  --runInBand --detectOpenHandles --forceExit --coverage", "test:cov": "jest --collectCoverage  --runInBand --detectOpenHandles --forceExit", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config jest-e2e.json", "test:silent": "jest --silent --runInBand --detectOpenHandles --forceExit --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "version": "0.0.1"}