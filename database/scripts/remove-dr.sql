-- Delete all transactions and related data for a tenant
-- Parameters:
--   :tenant_id - The tenant ID (schema name)
--   :register_id - The register ID (register id)

DO $$
DECLARE
    v_tenant_id TEXT;
    v_tenant_param TEXT := current_setting('app.tenant_id', true);
    v_register_id TEXT;
    v_register_param TEXT := current_setting('app.register_id', true);
    v_styles_deleted BIGINT := 0;
    v_fields_deleted BIGINT := 0;
    v_transactions_deleted BIGINT := 0;
    v_count_sequences INT := 0;
    v_override_deleted BIGINT := 0;
BEGIN
    v_tenant_id := v_tenant_param;
    
    -- Validate tenant_id format
    IF v_tenant_id !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RAISE EXCEPTION 'Invalid tenant_id format. Must be a valid UUID.';
    END IF;

    v_register_id := v_register_param;

    -- Validate register_id format
    IF v_register_id !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
        RAISE EXCEPTION 'Invalid register_id format. Must be a valid UUID.';
    END IF;

    -- Check if tenant exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.schemata 
        WHERE schema_name = v_tenant_id
    ) THEN
        RAISE EXCEPTION 'Tenant schema % does not exist', v_tenant_id;
    END IF;

    RAISE NOTICE 'Starting register data deletion for tenant %', v_tenant_id;
    
     -- Delete data
    EXECUTE format('DELETE FROM %I.data_register_transaction_field_override
                    WHERE data_register_transaction_field_override.data_register_transaction_id IN
                        (SELECT id FROM %I.data_register_transactions
                                WHERE data_register_id = %L)', v_tenant_id, v_tenant_id, v_register_id);
    
    GET DIAGNOSTICS v_override_deleted = ROW_COUNT;
    

    EXECUTE format('DELETE FROM  %I.data_register_transaction_field_styles
                    WHERE data_register_transaction_field_styles.transaction_id IN
                        (SELECT id FROM %I.data_register_transactions
               WHERE data_register_id = %L)', v_tenant_id, v_tenant_id, v_register_id);
    GET DIAGNOSTICS v_styles_deleted = ROW_COUNT;

    -- Delete data
    EXECUTE format('DELETE FROM %I.data_register_transaction_fields
                    WHERE data_register_transaction_fields.data_register_transaction_id IN
                        (SELECT id FROM %I.data_register_transactions
                                WHERE data_register_id = %L)', v_tenant_id, v_tenant_id, v_register_id);

    
    GET DIAGNOSTICS v_fields_deleted = ROW_COUNT;
    
    -- Delete the transactions
    EXECUTE format('DELETE FROM  %I.data_register_transactions
                    WHERE data_register_id = %L', v_tenant_id, v_register_id);
    GET DIAGNOSTICS v_transactions_deleted = ROW_COUNT;

     -- Reset sequence if it exists
    DECLARE
        v_sequence_name TEXT := 'register_' || REPLACE(v_register_id::text, '-', '_') || '_seq';
    BEGIN
        IF EXISTS (
            SELECT 1 
            FROM information_schema.sequences 
            WHERE sequence_schema = v_tenant_id 
            AND sequence_name = v_sequence_name
        ) THEN
            EXECUTE format('
                ALTER SEQUENCE %I.%I RESTART WITH 1',
                v_tenant_id, v_sequence_name
            );
            v_count_sequences := v_count_sequences + 1;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error resetting sequence for register %: %', v_register_id, SQLERRM;
    END;
    
    RAISE NOTICE 'Completed transaction data deletion for tenant %', v_tenant_id;
    RAISE NOTICE 'Deleted % transaction field styles', v_styles_deleted;
    RAISE NOTICE 'Deleted % transaction field overrides', v_override_deleted;
    RAISE NOTICE 'Deleted % transaction fields', v_fields_deleted;
    RAISE NOTICE 'Deleted % transactions', v_transactions_deleted;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in transaction deletion: %', SQLERRM;
END $$;