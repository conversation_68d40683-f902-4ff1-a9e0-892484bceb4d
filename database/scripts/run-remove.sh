#!/bin/bash

# Load environment variables from .env file
if [ -f "$(dirname "$0")/.env" ]; then
    export $(grep -v '^#' "$(dirname "$0")/.env" | xargs)
fi

# Set default values if not defined in .env
# Validate required environment variables
if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ] || [ -z "$TENANT_ID" ]; then
    echo "Error: Missing required environment variable(s)"
    echo "Please set the following variables in your .env file:"
    echo "DB_HOST, DB_PORT, DB_NAME, DB_USER, TENANT_ID"
    exit 1
fi

# Run the scripts
SCRIPT_PATH="$(dirname "$0")/remove-dr.sql"
echo "Running script: $SCRIPT_PATH"

PGPASSWORD="$DB_PASSWORD" psql \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    -c "SET app.tenant_id = '$TENANT_ID';" \
    -c "SET app.register_id = '$1';" \
    -f "$SCRIPT_PATH"
    
if [ $? -eq 0 ]; then
    echo "✅ Script completed: $file"
else
    echo "❌ Error: Failed to execute: $file"
    exit 1
fi

echo "All scripts completed successfully"