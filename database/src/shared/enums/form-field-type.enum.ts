export enum ExtraFieldTypeEnum {
    Widget = 'widget',
}

export enum FieldTypeStaticEnum {
    TransactionId = 'transactionId',
}

export enum FormFieldTypeEnum {
    Text = 'text',
    Number = 'number',
    TextArea = 'textArea',
    Select = 'select',
    DatePicker = 'datePicker',
    TimePicker = 'timePicker',
    DatetimePicker = 'datetimePicker',
    Calculation = 'calculation',
    Lookup = 'lookup',
    Duration = 'duration',
    Rollup = 'rollup',
    Separator = 'separator',
    UiGroup = 'uiGroup',
    Widget = 'widget',
    Checkbox = 'checkbox',
    TransactionId = 'transactionId',
    MultiSelect = 'multiselect',

    UserLookup = 'userLookup',
    RoleLookup = 'roleLookup',

    //Criteria fields
    Answer = 'answer',
    Comparison = 'comparison',

    // Validation
    ValidationResult = 'validationResult',

    //System
    System = 'system',

    //Document
    Document = 'document',

    Sire2Answer = 'sire2Answer',
    Definable = 'definable',
    StageKpi = 'stageKpi',
}
