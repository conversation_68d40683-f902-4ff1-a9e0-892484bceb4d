import { DataRegisterEventEnum, TransactionEventEnum } from './automation-event.enum';
import { FormManualAction } from './form-manual-event.enum';

export enum AutomationStatus {
    Draft = 'draft',
    Editing = 'editing',
    Published = 'published',
}

export enum AutomationVersionStatus {
    Draft = 'draft',
    Editing = 'editing',
    Published = 'published',
}

export enum AutomationContextType {
    DataRegister = 'data_register',
    FormTransaction = 'form_transaction',
    External = 'external',
}

export enum AutomationActionType {
    User = 'user',
    System = 'system',
}

export enum AutomationActionFunctionType {
    EMAIL = 'ACTION.EMAIL',
    NOTIFICATION = 'ACTION.NOTIFICATION',
    CREATE_TRANSACTION = 'ACTION.CREATE_TRANSACTION',
    CHANGE_TRANSACTION_WORKFLOW_STAGE = 'ACTION.CHANGE_TRANSACTION_WORKFLOW_STAGE',
    UPDATE_TRANSACTION_FIELD = 'ACTION.UPDATE_TRANSACTION_FIELD',
    UPDATE_REGISTER_FIELD = 'ACTION.UPDATE_REGISTER_FIELD',
    EXPORT_PDF = 'ACTION.PDF',
    CREATE_TRANSACTION_BY_EXTERNAL = 'ACTION.CREATE_TRANSACTION_BY_EXTERNAL',
    POPULATE_Q88 = 'ACTION.DATA_PIPELINE_POPULATE_Q88',
    WEBHOOK = 'ACTION.WEBHOOK',
}

export type AutomationEventType = DataRegisterEventEnum | TransactionEventEnum;

export const AutomationEventTypeEnum = [...Object.values(DataRegisterEventEnum), ...Object.values(TransactionEventEnum)];

export enum AutomationActionRunModeEnum {
    PARALLEL = 'PARALLEL',
    ORDERING = 'ORDERING',
}

export const ManualActionMapping = {
    [FormManualAction.SendEmail]: AutomationActionFunctionType.EMAIL,
    [FormManualAction.ExportPDF]: AutomationActionFunctionType.EXPORT_PDF,
};
