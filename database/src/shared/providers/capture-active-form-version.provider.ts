import { Injectable } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { CollectionTransactionEntity } from '../../entities/public/collection-transaction.public.entity';
import { FormAutoPopulateSettingEntity } from '../../entities/public/form-auto-populate-setting.public.entity';
import { FormCollectionAdditionalFieldEntity } from '../../entities/public/form-collection-additional-fields.public.entity';
import { FormCollectionAutoPopulateContextEntity } from '../../entities/public/form-collection-auto-populate-contexts.public.entity';
import { FormCollectionItemEntity } from '../../entities/public/form-collection-item.public.entity';
import { FormCollectionEntity } from '../../entities/public/form-collection.public.entity';
import { FormFieldEntity } from '../../entities/public/form-field.public.entity';
import { FormLayoutEntity } from '../../entities/public/form-layout.public.entity';
import { FormVersionEntity } from '../../entities/public/form-version.public.entity';
import { GeneralAutoPopulateExtraConfigEntity } from '../../entities/public/general-auto-populate-extra-config.public.entity';
import { GeneralAutoPopulateSettingEntity } from '../../entities/public/general-auto-populate-setting.public.entity';
import { StageAccessControlEntity } from '../../entities/public/stage-access-controls.public.entity';
import { StageDecisionEntity } from '../../entities/public/stage-decision.public.entity';
import { StageRoleEntity } from '../../entities/public/stage-role.public.entity';
import { StageTransitionEntity } from '../../entities/public/stage-transition.public.entity';
import { StageEntity } from '../../entities/public/stage.public.entity';
import { CollectionTransactionTenancyEntity } from '../../entities/tenancy/collection-transaction.tenancy.entity';
import { FormAutoPopulateSettingTenancyEntity } from '../../entities/tenancy/form-auto-populate-setting.tenancy.entity';
import { FormCollectionAdditionalFieldTenancyEntity } from '../../entities/tenancy/form-collection-additional-fields.tenancy.entity';
import { FormCollectionAutoPopulateContextTenancyEntity } from '../../entities/tenancy/form-collection-auto-populate-contexts.tenancy.entity';
import { FormCollectionItemTenancyEntity } from '../../entities/tenancy/form-collection-item.tenancy.entity';
import { FormCollectionTenancyEntity } from '../../entities/tenancy/form-collection.tenancy.entity';
import { FormFieldTenancyEntity } from '../../entities/tenancy/form-field.tenancy.entity';
import { FormLayoutTenancyEntity } from '../../entities/tenancy/form-layout.tenancy.entity';
import { FormVersionTenancyEntity } from '../../entities/tenancy/form-version.tenancy.entity';
import { GeneralAutoPopulateExtraConfigTenancyEntity } from '../../entities/tenancy/general-auto-populate-extra-config.tenancy.entity';
import { GeneralAutoPopulateSettingTenancyEntity } from '../../entities/tenancy/general-auto-populate-setting.tenancy.entity';
import { StageAccessControlTenancyEntity } from '../../entities/tenancy/stage-access-controls.tenancy.entity';
import { StageDecisionTenancyEntity } from '../../entities/tenancy/stage-decision.tenancy.entity';
import { StageRoleTenancyEntity } from '../../entities/tenancy/stage-role.tenancy.entity';
import { StageTransitionTenancyEntity } from '../../entities/tenancy/stage-transition.tenancy.entity';
import { StageTenancyEntity } from '../../entities/tenancy/stage.tenancy.entity';
import { DataSourceService } from '../../services/connection-util.service';

export type CaptureActiveFormVersionType = FormVersionTenancyEntity & {
    formAutoPopulateSettings?: FormAutoPopulateSettingTenancyEntity[];
    generalAutoPopulateSettings?: GeneralAutoPopulateSettingTenancyEntity[];
    collectionAdditionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
    stageDecisions?: StageDecisionTenancyEntity[];
    collectionTransactions?: CollectionTransactionTenancyEntity[];
    // stageRoleAccessControls?: StageRoleAccessControlTenancyEntity[];
    formCollectionAdditionalFields?: FormCollectionAdditionalFieldTenancyEntity[];
};

export interface ICaptureActiveFormVersionProvider {
    capture({ accountId, formVersionId }: { formVersionId: string; accountId: string }): Promise<CaptureActiveFormVersionType>;
}

@Injectable()
export class CaptureActiveFormVersionProvider implements ICaptureActiveFormVersionProvider {
    constructor(private readonly _dataSourceService: DataSourceService) {}

    async capture({ accountId, formVersionId }: { formVersionId: string; accountId: string }): Promise<CaptureActiveFormVersionType> {
        const connection = await this._dataSourceService.createAccountDataSource(accountId);

        const r = this._getFormContentRepositories({
            isAccount: !!accountId,
            connection: connection,
        });

        const [
            fields,
            collections,
            formAutoPopulateSettings,
            generalAutoPopulateSettings,
            generalAutoPopulateExtraConfigSettings,
            collectionAdditionalFields,
            stages,
            stageTransitions,
            layouts,
            collectionTransactions,
            stageAccessControls,
            stageRoles,
            formCollectionAdditionalFields,
            formVersion,
        ] = await Promise.all([
            r.fieldRepo.find({
                where: {
                    formVersionId,
                },
            }),
            r.collectionRepo.findBy({
                formVersionId,
            }),
            r.autoPopulateSettingRepo.findBy({ originFormVersionId: formVersionId }),
            r.generalAutoPopulateRepo.findBy({ builderVersionId: formVersionId }),
            r.generalAutoPopulateExtraConfigRepo.findBy({ builderVersionId: formVersionId }),
            r.collectionAdditionalFieldRepo.findBy({
                formVersionId,
            }),
            r.stageRepo.findBy({ formVersionId }),
            r.stageTransitionRepo.findBy({ formVersionId }),
            r.formLayoutRepo.find({
                where: {
                    formVersionId,
                },
                relations: {
                    layoutZones: {
                        layoutFields: true,
                    },
                },
            }),
            r.collectionTransactionRepo.findBy({ formVersionId }),
            r.stageAccessControlRepo.findBy({ formVersionId }),
            r.stageRoleRepo.findBy({ formVersionId }),
            r.formCollectionAdditionalFieldRepo.findBy({ formVersionId }),
            r.formVersionRepo.findOneBy({ id: formVersionId }),
        ]);

        const collectionIds = collections.map((collection) => collection.id);
        const stageIds = stages.map((stage) => stage.id);
        // const stageRoleIds = stageRoles.map((sr) => sr.id);

        const [
            collectionItems,
            stageDecisions,
            // stageRoleAccessControls
        ] = await Promise.all([
            collectionIds.length
                ? r.collectionItemRepo.find({
                      where: {
                          formCollectionId: In(collectionIds),
                      },
                      order: {
                          parentId: { direction: 'ASC', nulls: 'FIRST' },
                      },
                  })
                : Promise.resolve([]),

            r.stageDecisionRepo.findBy({ stageId: In(stageIds) }),
            // r.stageRoleAccessControlRepo.findBy({ stageRoleId: In(stageRoleIds) }),
        ]);

        generalAutoPopulateSettings.forEach((gas) => {
            const extraConfigs = generalAutoPopulateExtraConfigSettings.filter((e) => e.generalAutoPopulateSettingId === gas.id);
            gas.extraConfigurations = extraConfigs;
        });

        collections.forEach((collection) => {
            collection.formCollectionItems = collectionItems.filter((item) => item.formCollectionId === collection.id);
        });

        // Prepare for cache
        const formatFields = this._format<(typeof fields)[number]>(fields);
        const formatCollections = this._format<(typeof collections)[number]>(collections);
        const formatFormAutoPopulateSettings = this._format<(typeof formAutoPopulateSettings)[number]>(formAutoPopulateSettings);
        const formatGeneralAutoPopulateSettings = this._format<(typeof generalAutoPopulateSettings)[number]>(generalAutoPopulateSettings);
        const formatCollectionAdditionalFields = this._format<(typeof collectionAdditionalFields)[number]>(collectionAdditionalFields);
        const formatFormVersion = this._format<typeof formVersion>([formVersion])[0];
        const formatStages = this._format<(typeof stages)[number]>(stages);
        const formatStageTransitions = this._format<(typeof stageTransitions)[number]>(stageTransitions);
        const formatStageDecisions = this._format<(typeof stageDecisions)[number]>(stageDecisions);
        layouts.forEach((layout) => {
            layout.layoutZones.forEach((lz) => (lz.layoutFields = this._format<(typeof lz.layoutFields)[number]>(lz.layoutFields || [])));
            layout.layoutZones = this._format<(typeof layout.layoutZones)[number]>(layout.layoutZones || []);
        });
        const formatLayouts = this._format<(typeof layouts)[number]>(layouts);
        const formatCollectionTransactions = this._format<(typeof collectionTransactions)[number]>(collectionTransactions);
        const formatStageAccessControls = this._format<(typeof stageAccessControls)[number]>(stageAccessControls);
        const formatStageRoles = this._format<(typeof stageRoles)[number]>(stageRoles);
        // const formatStageRoleAccessControls = this._format<(typeof stageRoleAccessControls)[number]>(stageRoleAccessControls);
        const formatFormCollectionAdditionalFields =
            this._format<(typeof formCollectionAdditionalFields)[number]>(formCollectionAdditionalFields);

        const activeFormVersion: CaptureActiveFormVersionType = formatFormVersion;
        activeFormVersion.fields = formatFields;
        activeFormVersion.stages = formatStages;
        activeFormVersion.stageTransitions = formatStageTransitions;
        activeFormVersion.formCollections = formatCollections;
        activeFormVersion.formLayouts = formatLayouts;
        activeFormVersion.stageAccessControls = formatStageAccessControls;
        activeFormVersion.stageRoles = formatStageRoles;

        activeFormVersion.formAutoPopulateSettings = formatFormAutoPopulateSettings;
        activeFormVersion.generalAutoPopulateSettings = formatGeneralAutoPopulateSettings;
        activeFormVersion.collectionAdditionalFields = formatCollectionAdditionalFields;
        activeFormVersion.stageDecisions = formatStageDecisions;
        activeFormVersion.collectionTransactions = formatCollectionTransactions;
        // activeFormVersion.stageRoleAccessControls = formatStageRoleAccessControls;
        activeFormVersion.formCollectionAdditionalFields = formatFormCollectionAdditionalFields;

        return activeFormVersion;
    }

    private _format<
        T extends Partial<{
            createdAt: Date;
            createdBy: string;
            updatedAt: Date;
            updatedBy: string;
            deletedAt: Date;
            deletedBy: string;
            deletedByUser: string;
            updatedByUser: string;
            createdByUser: string;
        }>,
    >(
        items: Array<T>,
    ): Array<
        Omit<
            T,
            | 'createdAt'
            | 'createdBy'
            | 'updatedAt'
            | 'updatedBy'
            | 'deletedAt'
            | 'deletedBy'
            | 'deletedByUser'
            | 'updatedByUser'
            | 'createdByUser'
        >
    > {
        return items.map((item) => {
            const {
                createdAt,
                createdBy,
                updatedAt,
                updatedBy,
                deletedAt,
                deletedBy,
                deletedByUser,
                updatedByUser,
                createdByUser,
                ...rest
            } = item;

            return rest;
        });
    }

    public _getFormContentRepositories({ connection, isAccount }: { connection: DataSource; isAccount: boolean }) {
        return {
            // formRepo: connection.getRepository<FormTenancyEntity>(isAccount ? FormTenancyEntity : FormEntity),
            formVersionRepo: connection.getRepository<FormVersionTenancyEntity>(isAccount ? FormVersionTenancyEntity : FormVersionEntity),
            fieldRepo: connection.getRepository<FormFieldTenancyEntity>(isAccount ? FormFieldTenancyEntity : FormFieldEntity),
            collectionRepo: connection.getRepository<FormCollectionTenancyEntity>(
                isAccount ? FormCollectionTenancyEntity : FormCollectionEntity,
            ),
            collectionItemRepo: connection.getRepository<FormCollectionItemTenancyEntity>(
                isAccount ? FormCollectionItemTenancyEntity : FormCollectionItemEntity,
            ),
            collectionAdditionalFieldRepo: connection.getRepository<FormCollectionAdditionalFieldTenancyEntity>(
                isAccount ? FormCollectionAdditionalFieldTenancyEntity : FormCollectionAdditionalFieldEntity,
            ),
            collectionAutoPopulateContextRepo: connection.getRepository<FormCollectionAutoPopulateContextTenancyEntity>(
                isAccount ? FormCollectionAutoPopulateContextTenancyEntity : FormCollectionAutoPopulateContextEntity,
            ),
            stageRepo: connection.getRepository<StageTenancyEntity>(isAccount ? StageTenancyEntity : StageEntity),
            stageDecisionRepo: connection.getRepository<StageDecisionTenancyEntity>(
                isAccount ? StageDecisionTenancyEntity : StageDecisionEntity,
            ),
            stageTransitionRepo: connection.getRepository<StageTransitionTenancyEntity>(
                isAccount ? StageTransitionTenancyEntity : StageTransitionEntity,
            ),
            stageRoleRepo: connection.getRepository<StageRoleTenancyEntity>(isAccount ? StageRoleTenancyEntity : StageRoleEntity),
            stageAccessControlRepo: connection.getRepository<StageAccessControlTenancyEntity>(
                isAccount ? StageAccessControlTenancyEntity : StageAccessControlEntity,
            ),
            // stageRoleAccessControlRepo: connection.getRepository<StageRoleAccessControlTenancyEntity>(
            //     isAccount ? StageRoleAccessControlTenancyEntity : StageRoleAccessControlEntity,
            // ),
            formLayoutRepo: connection.getRepository<FormLayoutTenancyEntity>(isAccount ? FormLayoutTenancyEntity : FormLayoutEntity),
            autoPopulateSettingRepo: connection.getRepository<FormAutoPopulateSettingTenancyEntity>(
                isAccount ? FormAutoPopulateSettingTenancyEntity : FormAutoPopulateSettingEntity,
            ),
            collectionTransactionRepo: connection.getRepository<CollectionTransactionTenancyEntity>(
                isAccount ? CollectionTransactionTenancyEntity : CollectionTransactionEntity,
            ),
            generalAutoPopulateRepo: connection.getRepository<GeneralAutoPopulateSettingEntity>(
                isAccount ? GeneralAutoPopulateSettingTenancyEntity : GeneralAutoPopulateSettingEntity,
            ),
            generalAutoPopulateExtraConfigRepo: connection.getRepository<GeneralAutoPopulateExtraConfigEntity>(
                isAccount ? GeneralAutoPopulateExtraConfigTenancyEntity : GeneralAutoPopulateExtraConfigEntity,
            ),
            formCollectionAdditionalFieldRepo: connection.getRepository<FormCollectionAdditionalFieldEntity>(
                isAccount ? FormCollectionAdditionalFieldTenancyEntity : FormCollectionAdditionalFieldEntity,
            ),
            formCollectionItemRepo: connection.getRepository<FormCollectionItemEntity>(
                isAccount ? FormCollectionItemTenancyEntity : FormCollectionItemEntity,
            ),
        };
    }
}
