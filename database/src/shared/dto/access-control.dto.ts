import { AutoMap } from '@automapper/classes';
import { AccessOption } from '../enums/access-control-type.enum';

export class StageAccessControlConfig {
    @AutoMap()
    visible?: boolean;

    @AutoMap()
    editable?: boolean;

    @AutoMap()
    required?: boolean;

    @AutoMap()
    access?: AccessOption;

    @AutoMap()
    override?: boolean;

    @AutoMap()
    enable?: boolean;

    @AutoMap()
    collectionItemIdentityId?: string;

    @AutoMap()
    locked?: boolean;
}

export class StageRoleAccessControlConfig extends StageAccessControlConfig {
    @AutoMap()
    enable?: boolean;

    @AutoMap()
    fieldName?: string;

    @AutoMap()
    type?: string;

    @AutoMap()
    identityId?: string;

    @AutoMap()
    collectionId?: string;

    @AutoMap()
    canCreate?: boolean;

    @AutoMap()
    canDelete?: boolean;
}
